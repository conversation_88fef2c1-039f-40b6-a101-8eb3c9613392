import logging
import requests
from retrying import retry

from common.utils import date_utils
from proto import authorizer_pb2 as authorizer_pb
from wechat_lib.api_constants import WechatApiUrls

logger = logging.getLogger(__name__)

def get_ticket_for_card_api(access_token, appid):
    """获取卡券接口的 Ticket

    Args:
        access_token: (string)
        appid: (string)

    Return:
        (authorizer_pb.AuthorizerTicket | None)
    """
    return _get_ticket(access_token=access_token,
                       appid=appid,
                       ticket_type='wx_card')

def get_ticket_for_js_api(access_token, appid):
    """获取JS接口的 Ticket

    Args:
        access_token: (string)
        appid: (string)

    Return:
        (authorizer_pb.AuthorizerTicket | None)
    """
    return _get_ticket(access_token=access_token,
                       appid=appid,
                       ticket_type='jsapi')

def retry_if_result_none(result):
    return result is None

@retry(stop_max_attempt_number=3, retry_on_result=retry_if_result_none)
def _get_ticket(access_token, appid, ticket_type):
    """调用获取 Ticket 的接口

    Args:
        access_token: (string)
        appid: (string)
        ticket_type: (string) Ticket 类型

    Return:
        (authorizer_pb.AuthorizerTicket | None)
    """
    url = '{}?access_token={}&type={}'.format(WechatApiUrls.GET_API_TICKET_URL,
                                              access_token, ticket_type)
    resp = requests.get(url)
    resp_json = resp.json()
    if resp_json['errcode'] == 0:
        ticket = authorizer_pb.AuthorizerTicket()
        ticket.appid = appid
        if ticket_type == 'wx_card':
            ticket.type = authorizer_pb.AuthorizerTicket.WX_CARD
        else:
            ticket.type = authorizer_pb.AuthorizerTicket.JS_API
        ticket.ticket = resp_json['ticket']
        ticket.expires_in = resp_json['expires_in']
        ticket.updated_time = date_utils.timestamp_second()
        return ticket

    logger.error('获取 {} Ticket 出错，错误码: {}，错误内容: {}。'.format(ticket_type, resp_json['errcode'], resp_json['errmsg']))
    return None

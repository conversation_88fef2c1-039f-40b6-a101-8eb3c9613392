import requests
import json
import sys
import os
import proto.membership_service_pb2 as membership_service_pb
import proto.membership_service_pb2 as membership_service_pb
import proto.wechat_member_card_pb2 as wechat_member_card_pb2
import proto.coupons_pb2 as coupons_pb2
import proto.wechat_coupons_pb2 as wechat_coupons_pb2

from retrying import retry

from wechat_lib.api_constants import WechatApiUrls
from wechat_lib import api_helper_util
from google.protobuf import json_format
from common.config import *
from common.constant import const



@retry(stop_max_attempt_number=3)
def get_decrypted_code(merchant_id, encrypted_code):
      base_url = 'https://api.weixin.qq.com/card/code/decrypt'
      url = api_helper_util.get_token_embedded_url(merchant_id, base_url)
      resp = requests.post(url, json={
                          'encrypt_code': encrypted_code})
      res_json = resp.json()
      if 'errcode' in res_json and res_json['errcode'] == 0:
          return res_json['code']
      else:
          return None


@retry(stop_max_attempt_number=3)
def create_coupon_class(merchant_id, data):
      """ 创建一类新的优惠券。

       HTTP请求方式:
          HTTP请求方式: POSTURL: https://api.weixin.qq.com/card/create?access_token=ACCESS_TOKEN

      请求参数：
          json	是	会员卡数据
      返回：
          POST数据	是	Json数据

      Args:
        merchant_id: (string) 商户ID
        member_card_category: CreateMemberCardCategoryRequest信息结构体。

      Returns:
        一个CreateMemberCardCategoryResponse信息结构体。
      """

      url = "https://api.weixin.qq.com/card/create"
      resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id, url),
                           data=json.dumps(data, ensure_ascii=False).encode('utf-8'))
      result = resp.json()
      return result


@retry(stop_max_attempt_number=3)
def update_coupon_class(merchant_id, data):
    """ 更新一类新的优惠券。

    HTTP请求方式:
        HTTP请求方式: POSTURL: https://api.weixin.qq.com/card/update?access_token=ACCESS_TOKEN

    请求参数：
        json	是	会员卡数据
    返回：
        POST数据	是	Json数据

    Args:
      member_card_category: CreateMemberCardCategoryRequest信息结构体。

    Returns:
      一个CreateMemberCardCategoryResponse信息结构体。
    """
    url = "https://api.weixin.qq.com/card/update"
    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id, url),
                         data=json.dumps(data, ensure_ascii=False).encode('utf-8'))
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def update_merchant_coupon_image_class(merchant_id, filename):
    """ 创建一类新的会员卡。

    HTTP请求方式:
        HTTP请求方式: POST/FROMURL:https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token=ACCESS_TOKEN

    请求参数：
        buffer	是	文件的数据流
    返回：
        返回正确的示例：{"url":"http://mmbiz.qpic.cn/mmbiz/iaL1LJM1mF9aRKPZJkmG8xXhiaHqkKSVMMWeN3hLut7X7hicFNjakmxibMLGWpXrEXB33367o7zHN0CwngnQY7zb7g/0"}返回错误的示例{"errcode":40009,"errmsg":"invalid image size"}

        参数名	描述
        errcode	错误码
        errmsg	错误信息
        url	    商户图片url，用于创建卡券接口中填入。特别注意：该链接仅用于微信相关业务，不支持引用。

    Args:
        member_card_category: CreateMemberCardCategoryRequest信息结构体。

    Returns:
        一个CreateMemberCardCategoryResponse信息结构体。
      """
    url = "https://api.weixin.qq.com/cgi-bin/media/uploadimg"
    img_path = os.path.join(const.STATIC_IMAGES_DIR, filename)
    filename_type = filename.split(".")[-1]
    files = {"buffer": (filename, open(img_path, "rb"), filename_type)}

    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id, url), files= files)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def update_merchant_coupon_news_class(merchant_id, filename):
    """ 创建一类新的会员卡。

    HTTP请求方式:
        HTTP请求方式: https://api.weixin.qq.com/cgi-bin/media/uploadnews?access_token=ACCESS_TOKEN

    请求参数：
        buffer	是	文件的数据流
    返回：
        type	媒体文件类型，分别有图片（image）、语音（voice）、视频（video）和缩略图（thumb），图文消息（news）
        media_id	媒体文件/图文消息上传后获取的唯一标识
        created_at 媒体文件上传时间

    Args:
      member_card_category: CreateMemberCardCategoryRequest信息结构体。

    Returns:
      一个CreateMemberCardCategoryResponse信息结构体。
    """

    url = "https://api.weixin.qq.com/cgi-bin/media/uploadnews"

    data = {
      "articles": [{
        "thumb_media_id": "qI6_Ze_6PtV7svjolgs-rN6stStuHIjs9_DidOHaj0Q-mwvBelOXCFZiq2OsIU-p",
        "author": "xxx",
        "title": "Happy Day",
        "content_source_url": "www.qq.com",
        "content": "content",
        "digest": "digest",
        "show_cover_pic": 1,
        "need_open_comment": 1,
        "only_fans_can_comment": 1
      },
        {
          "thumb_media_id": "qI6_Ze_6PtV7svjolgs-rN6stStuHIjs9_DidOHaj0Q-mwvBelOXCFZiq2OsIU-p",
          "author": "xxx",
          "title": "Happy Day",
          "content_source_url": "www.qq.com",
          "content": "content",
          "digest": "digest",
          "show_cover_pic": 0,
          "need_open_comment": 1,
          "only_fans_can_comment": 1
        }
      ]
    }

    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id, url), json=data)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def send_openid_all(merchant_id, data):
    """ 创建一类新的优惠券。

     HTTP请求方式:
        POST https://api.weixin.qq.com/cgi-bin/message/mass/send?access_token=ACCESS_TOKEN

    请求参数：
       touser	            是	填写图文消息的接收者，一串OpenID列表，OpenID最少2个，最多10000个
        mpnews	        是	用于设定即将发送的图文消息
        media_id	        是	用于群发的图文消息的media_id
        msgtype	        是	群发的消息类型，图文消息为mpnews，文本消息为text，语音为voice，音乐为music，图片为image，视频为video，卡券为wxcard
        title	否	        消息的标题
        description	    否	消息的描述
        thumb_media_id	是	视频缩略图的媒体ID
        send_ignore_reprint	是	图文消息被判定为转载时，是否继续群发。 1为继续群发（转载），0为停止群发。 该参数默认为0。

    返回：
        type	媒体文件类型，分别有图片（image）、语音（voice）、视频（video）和缩略图（thumb），次数为news，即图文消息
        errcode	错误码
        errmsg	错误信息
        msg_id	消息发送任务的ID
        msg_data_id	消息的数据ID，，该字段只有在群发图文消息时，才会出现。可以用于在图文分析数据接口中，获取到对应的图文消息的数据，是图文分析数据接口中的msgid字段中的前半部分，详见图文分析数据接口中的msgid字段的介绍。

    Args:
      member_card_category: CreateMemberCardCategoryRequest信息结构体。

    Returns:
      一个CreateMemberCardCategoryResponse信息结构体。
    """
    url = 'https://api.weixin.qq.com/cgi-bin/message/mass/send'

    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id, url),
                         data=json.dumps(data, ensure_ascii=False).encode('utf-8'))
    result = resp.json()
    return result

# 卡券第三方代制模式下子商户相关接口帮助类
# 用于完成在母商户（时来餐饮营销公众平台）下添加子商户，
# 帮助子商户完成制券等流程


import json
import logging
import requests
from retrying import retry

from google.protobuf import json_format
from common.config import config
from common.utils import access_token_helper
from wechat_lib.api_constants import WechatApiUrls

import proto.merchant_rules_pb2 as merchant_rules_pb

logger = logging.getLogger(__name__)

@retry(stop_max_attempt_number=3)
def get_submerchant(wechat_merchant_id):
    """拉取单个子商户信息

    Args:
        wechat_merchant_id: (number) 微信下发的子商户ID，一个母商户公众号下唯一

    Returns:
        返回包含以下信息的JSON结构体:
            merchant_id: (number) 子商户ID，对于一个母商户公众号下唯一
            brand_name: (string) 子商户名称（12个汉字内），该名称将在制券时填入并显示在卡券页面上
            logo_url: (string) 子商户logo，可通过上传logo接口获取。该logo将在制券时填入并显示在卡券页面上
            status: (string) 子商户状态，"CHECKING" 审核中, "APPROVED" , 已通过；"REJECTED"被驳回, "EXPIRED"协议已过期
            primary_category_id: (number) 子商户一级类目
            secondary_category_id: (nubmer) 子商户二级类目
            create_time: (number) 子商户信息创建时间，单位为秒
            update_time: (number) 子商户信息更新时间，单位为秒
            begin_time: (number) 创建时间（非协议开始时间）
            end_time: (number) 授权函有效期截止时间（东八区时间，单位为秒）
    """
    url = _get_token_embedded_url(WechatApiUrls.GET_SUBMERCHANT)
    request_json = {
        "merchant_id": wechat_merchant_id
    }
    resp = requests.post(url=url, json=request_json)
    resp_json = resp.json()
    if resp_json['errcode'] != 0:
        logger.error('拉取单个子商户信息出错，错误码: {}, 错误内容: {}'.format(resp_json['errcode'], resp_json['errmsg']))
        return None
    return _response_submerchant_info(resp_json)


@retry(stop_max_attempt_number=3)
def get_apply_categories():
    """卡券开放类目查询
    """
    url = _get_token_embedded_url(WechatApiUrls.GET_APPLY_CATEGORY)
    resp = requests.get(url)
    resp_json = resp.json()
    if resp_json['errcode'] == 0:
        return resp_json['category']

    logger.error('查询卡券开放类目出错，错误码: {}，错误内容: {}'.format(resp_json['errcode'], resp_json['errmsg']))
    return None


@retry(stop_max_attempt_number=3)
def create_or_update_submerchant(brand_name,
                                 logo_url,
                                 auth_letter_media_id,
                                 auth_letter_end_time,
                                 submerchant_id=None):
    """创建或更新子商户

    如果传入子商户ID，则调用更新子商口接口，否则调用创建子商户接口

    Args:
        submerchant_id: (string | None) 子商户ID
        brand_name: (string) 子商户名称（12个汉字内），该名称将在制券时填入并显示在卡券页面上
        logo_url: (string) 子商户logo，可通过 上传图片接口 获取。该logo将在制券时填入并显示在卡券页面上
        auth_letter_media_id: (string) 授权函ID，即通过 上传临时素材接口 上传授权函后获得的 media_id
        auht_letter_end_time: (string) 授权函有效期截止时间（东八区时间，单位为秒），需要与提交的扫描件一致

    Returns:
        (Merchant.ShilaiMPSubmerchantInfo | None)
    """
    apply_categories = get_apply_categories()
    if apply_categories:
        apply_category = apply_categories[0]
        primary_category_id = apply_category['primary_category_id']
        secondary_category_id = apply_category['secondary_category'][0]['secondary_category_id']

        if submerchant_id is not None:
            url = _get_token_embedded_url(WechatApiUrls.UPDATE_SUBMERCHANT)
        else:
            url = _get_token_embedded_url(WechatApiUrls.CREATE_SUBMERCHANT)

        request_json = {
            "info": {
                "brand_name": brand_name,
                "logo_url": logo_url,
                "protocol": auth_letter_media_id,
                "end_time": auth_letter_end_time,
                "primary_category_id": primary_category_id,
                "secondary_category_id": secondary_category_id
            }
        }
        if submerchant_id is not None:
            request_json['info']['merchant_id'] = submerchant_id

        resp = requests.post(url=url,
                             data=json.dumps(request_json, ensure_ascii=False).encode('utf-8'))
        resp_json = resp.json()
        if resp_json['errcode'] != 0:
            action_text = '创建' if submerchant_id is None else '更新'
            logger.error('{}单个子商户信息出错，错误码: {}, 错误内容: {}'.format(action_text,
                                                                           resp_json['errcode'],
                                                                           resp_json['errmsg']))
            return None
        return _response_submerchant_info(resp_json)
    return None


def _response_submerchant_info(response_json):
    """转化成 ShilaiMPSubmerchantInfo

    Args:
        response_json: (json) 接口返回结果

    Returns:
        (Merchant.ShilaiMPSubmerchantInfo | None)
    """
    if 'info' in response_json:
        info_json = response_json['info']
        # 接口返回的 merchant_id 为子商户ID，容易跟时来商户 Merchant 的 ID 混淆
        # 故使用 ShilaiMPSubmerchantInfo.id
        info_json['id'] = info_json['merchant_id']
        return json_format.ParseDict(info_json,
                                     merchant_rules_pb.ShilaiMPSubmerchantInfo(),
                                     ignore_unknown_fields=True)
    return None


def _get_token_embedded_url(request_url):
    """获取微信接口请求URL

    Args:
        request_url: (string) 请求URL

    Returns:
        (string) 带上 access_token 的 URL
    """
    access_token = access_token_helper.get_shilai_app_access_token(config.SHILAI_MP_APPID)
    return "{}?access_token={}".format(request_url, access_token)

import os
import requests
from retrying import retry

from common.config import config
from common.utils import access_token_helper
from wechat_lib.api_constants import WechatApiUrls



# TODO: 素材类型 type 使用 protobuf enum
@retry(stop_max_attempt_number=3)
def upload_temp_media(file_path, type="image"):
    """上传图片素材

    Args:
        file_path: (string) 上传的文件路径
        type: (string) 素材类型

    Returns:
        (string | None) 微信临时素材库 URL
    """
    url = "{}&type={}".format(_get_token_embedded_url(WechatApiUrls.UPLOAD_TEMPORARY_MEDIA_URL), type)
    if os.path.exists(file_path) and os.path.isfile(file_path):
        with open(file_path, 'rb') as buffer:
            files = { 'media': buffer }
            resp = requests.post(url, files=files)
            resp_json = resp.json()
            return resp_json['media_id']
    return None


@retry(stop_max_attempt_number=3)
def upload_img(file_path):
    """上传图片素材

    Args:
        file_path: (string) 上传的文件路径

    Returns:
        (string | None) 微信图片 CDN URL
    """
    url = _get_token_embedded_url(WechatApiUrls.UPLOAD_IMAGE_URL)
    if os.path.exists(file_path) and os.path.isfile(file_path):
        with open(file_path, 'rb') as buffer:
            files = {'buffer': buffer }
            resp = requests.post(url, files=files)
            resp_json = resp.json()
            return resp_json['url']
    return None


def _get_token_embedded_url(request_url):
    """获取微信接口请求URL

    Args:
        request_url: (string) 请求URL

    Returns:
        (string) 带上 access_token 的 URL
    """
    access_token = access_token_helper.get_shilai_app_access_token(config.SHILAI_MP_APPID)
    return "{}?access_token={}".format(request_url, access_token)


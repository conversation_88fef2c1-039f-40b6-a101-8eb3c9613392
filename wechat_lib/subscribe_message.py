# -*- coding: utf-8 -*-


"""
发送微信订阅消息
"""

from datetime import datetime
import logging
import requests

from common.config import config
from common.utils import access_token_helper
from dao.invite_share_da_helper import InviteShareDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper

logger = logging.getLogger(__name__)


class SubscribeMessage:

    def __send(self, data):
        mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
        access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
        data.update({"access_token": access_token})
        url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}'.format(access_token)
        resp = requests.post(url=url, json=data)
        resp_json = resp.json()
        return resp_json

    def invite_share_invite_success_subscribe_message(self, invitor, invitee, merchant_id):
        template_id = "rEm_GTa5LQcWDp3BKwcu0k1sOHXE4ajq-lnlbqprC5s"
        touser_openid = invitor.wechat_profile.openid
        data = {
            "touser": touser_openid,
            "template_id": template_id,
            "page": "package-activity/activity/activity?merchantId={}&userId={}".format(merchant_id, invitor.id),
            "data": {

                "name1": {
                    "value": invitee.member_profile.nickname
                },
                "thing4": {
                    "value": "邀请成功"
                },
                "thing5": {
                    "value": "好友已成功领券，快提醒她去消费吧！"
                }
            }
        }
        self.__send(data=data)

    def invite_share_reward_subscribe_message(self, coupon_id, invitee=None, merchant=None):
        invite_share_da = InviteShareDataAccessHelper()
        user_da = UserDataAccessHelper()
        invitee_share = invite_share_da.get_invitee_share(coupon_id=coupon_id)
        if not invitee_share:
            logger.info("没有找到邀请记录: {}".format(coupon_id))
            return
        invitor = user_da.get_user(invitee_share.invitor_id)
        if not invitor:
            logger.info("没有找到邀请者: {}".format(invitee_share.invitor_id))
            return
        if merchant is None:
            merchant = MerchantDataAccessHelper().get_merchant(invitee_share.merchant_id)
        if invitee is None:
            invitee = user_da.get_user(invitee_share.invitee_id)
        if not invitee:
            logger.info("没有找到被邀请者: {}".format(invitee_share.invitee_id))
            return
        self.do_invite_share_reward_subscribe_message(invitor, invitee, merchant, invitee_share.coupon_category_value)

    def create_group_purchase_subscribe_message(self, user, merchant, group_purchase):
        """开团消息提醒"""
        try:
            touser_openid = user.wechat_profile.openid
            page = f"package-payment/pingtuan-activity/pingtuan-detail?id={group_purchase.id}"
            template_id = "aCgujrasQtv9YIiL6ADLvGSPA7meTbRjOWjT7R85tX0"
            deadline = datetime.fromtimestamp(group_purchase.create_time + group_purchase.valid_period).strftime("%Y-%m-%d")
            data = {
                "touser": touser_openid,
                "template_id": template_id,
                "page": page,
                "data": {
                    "number3": {
                        "value": group_purchase.member_coupon_count
                    },
                    "time6": {
                        "value": deadline
                    },
                    "thing5": {
                        "value": "您已开启拼团，系统将自动匹配成团"
                    }
                }
            }
            logger.info("发送开团消息参数: {}".format(data))
            result = self.__send(data=data)
            logger.info("发送开团消息结果: {}".format(result))
        except Exception as ex:
            logger.info(f"开团发消息出错: {ex}")

    def finish_group_purchase_subscribe_message(self, user, merchant, group_purchase):
        """成团消息提醒"""
        try:
            touser_openid = user.wechat_profile.openid
            page = f"package-payment/pingtuan-activity/pingtuan-detail?id={group_purchase.id}"
            # template_id = "YPz0fzIv22b4ZD3GHsBhDzGu5iiL-yUDuDOcgdnvwh8"
            template_id = "0J4ROFzkelcmAvU7V6ivRG-6v0GpA4wEtN9Zg3A-PgE"
            deadline = datetime.fromtimestamp(group_purchase.create_time + group_purchase.valid_period).strftime("%Y-%m-%d")
            data = {
                "touser": touser_openid,
                "template_id": template_id,
                "page": page,
                "data": {
                    "number3": {
                        "value": group_purchase.member_coupon_count
                    },
                    "time7": {
                        "value": deadline
                    },
                    "thing10": {
                        "value": merchant.basic_info.display_name
                    },
                    "thing8": {
                        "value": "恭喜您拼团成功，点击查看拼团返现"
                    }
                }
            }
            logger.info("发送成团消息参数: {}".format(data))
            result = self.__send(data=data)
            logger.info("发送成团消息结果: {}".format(result))
        except Exception as ex:
            logger.info(f"成团消息出错: {ex}")

    def do_invite_share_reward_subscribe_message(self, touser, invitee, merchant, amount):
        """ 当被邀请者用券消费之后,给邀请者发送小程序通知
        """
        touser_openid = touser.wechat_profile.openid
        page = "package-activity/activity/activity?merchantId={}&userId={}".format(merchant.id, touser.id)
        template_id = "0fJymozxl-GCJXc2FBuRrX_zUm_An_zF8Yl830PPF_Y"
        amount = "{:.2f}元".format(amount / float(100))
        data = {
            "touser": touser_openid,
            "template_id": template_id,
            "page": page,
            "data": {
                "phrase5": {
                    "value": "分享返佣"
                },
                "amount4": {
                    "value": amount
                },
                "thing1": {
                    "value": merchant.basic_info.display_name
                },
                "thing6": {
                    "value": invitee.member_profile.nickname
                }
            }
        }
        self.__send(data=data)
        logger.info("裂变活动给邀请者发送被邀请者消费消息通知: {}".format(data))

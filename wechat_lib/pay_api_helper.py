# 微信支付相关模块
import hashlib
import logging
import os

import xmltodict
from dicttoxml import dicttoxml
from google.protobuf import json_format
from retrying import retry

from common import constants
from common.config import config
from common.utils import id_manager
from proto.wechat import pay_pb2 as pay_pb
from wechat_lib import request_helper
from wechat_lib.resp_error import WechatApiResponseError

logger = logging.getLogger(__name__)

def retry_if_return_system_error(error):
    """ 当 err_code 为 SYSTEMERROR 时，使用当前商户订单号重试，避免重复支付等资金风险
    """
    return isinstance(error, WechatApiResponseError) and error.errmsg == "SYSTEMERROR"

@retry(stop_max_attempt_number=3, retry_on_exception=retry_if_return_system_error)
def transfer_to_user_wallet(openid,
                            amount,
                            order_id,
                            check_name="FORCE_CHECK",
                            re_user_name=None):
    """ 企业付款到零钱

    使用时来饭票子商户号付款给指定用户的微信钱包(零钱)
    """
    url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers"
    request_json = {
        # 商户账号appid
        "mch_appid": config.WECHAT_MINIPROGRAM_APPID,
        # 微信支付分配的商户号
        "mchid": config.SHILAI_SETTLEMENT_ACCOUNT,
        # 随机字符串
        "nonce_str": id_manager.generate_nonce_str(16),
        # 商户订单号
        "partner_trade_no": order_id,
        # 用户openid
        "openid": openid,
        # 校验用户姓名选项
        #   NO_CHECK: 不校验真实姓名
        #   FORCE_CHECK: 强校验真实姓名
        "check_name": check_name,
        # 付款金额
        "amount": amount,
        # 备注
        "desc": "时来钱包提现",
        # IP
        "spbill_create_ip": config.get_config_value(constants.MAIN_WEB_SERVICE_ADDRESS_ENV_NAME)
    }

    if check_name == "FORCE_CHECK":
        request_json["re_user_name"] = re_user_name

    signature = generate_signature(request_json)
    request_json["sign"] = signature

    request_xml = dicttoxml(request_json, attr_type=False, custom_root="xml").decode("utf-8")
    request_headers = {"Content-Type": "text/xml"}
    request_certs = (
        os.environ.get("WECHAT_PAY_CLIENT_CERT_PATH"),
        os.environ.get("WECHAT_PAY_CLIENT_KEY_PATH")
    )

    try:
        resp = request_helper.post(url=url,
                                   data=request_xml.encode("utf-8"),
                                   headers=request_headers,
                                   verify=True,
                                   cert=request_certs,
                                   timeout=3)
        resp_xml = resp.content.decode("utf-8")
    except UnicodeEncodeError as error:
        logger.error("企业付款到零钱解码出错，{}, 订单ID: {}".format(error, order_id))
        resp_xml = resp.content.decode("latin-1")

    parsed_resp_xml = xmltodict.parse(resp_xml)
    result = json_format.ParseDict(parsed_resp_xml['xml'], pay_pb.TransferUserWalletResponse(), ignore_unknown_fields=True)
    error = None
    if result.return_code == "SUCCESS":
        if result.result_code == "SUCCESS":
            logger.info("企业付款到零钱成功，订单ID: {}".format(order_id))
        else:
            error = WechatApiResponseError(result.err_code, result.err_code_des, api_desc="企业付款到零钱接口")
    else:
        error = WechatApiResponseError(result.return_code, result.return_msg, api_desc="企业付款到零钱接口")

    if error:
        logger.error("发起付款到用户零钱出错，订单ID: {}, 错误: {}".format(order_id, error))

    return result

def generate_signature(request_json):
    """ 根据请求主体生成签名
    """
    result = ''
    for key in sorted(request_json.keys()):
        result = '{}{}={}&'.format(result, key, request_json[key])

    result = result + 'key=' + config.WECHAT_MERCHANT_KEY
    return hashlib.md5(result.encode('utf-8')).hexdigest().upper()

@retry(stop_max_attempt_number=3, retry_on_exception=retry_if_return_system_error)
def ordering_refund(openid,
                    amount,
                    order_id,
                    check_name="FORCE_CHECK",
                    re_user_name=None,
                    reason=None):
    """ 扫码点餐退款
    """
    url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers"
    request_json = {
        # 商户账号appid
        "mch_appid": config.WECHAT_MINIPROGRAM_APPID,
        # 微信支付分配的商户号
        "mchid": config.SHILAI_SETTLEMENT_ACCOUNT,
        # 随机字符串
        "nonce_str": id_manager.generate_nonce_str(16),
        # 商户订单号
        "partner_trade_no": order_id,
        # 用户openid
        "openid": openid,
        # 校验用户姓名选项
        #   NO_CHECK: 不校验真实姓名
        #   FORCE_CHECK: 强校验真实姓名
        "check_name": check_name,
        # 付款金额
        "amount": amount,
        # 备注
        "desc": "退款",
        # IP
        "spbill_create_ip": config.get_config_value(constants.MAIN_WEB_SERVICE_ADDRESS_ENV_NAME)
    }
    if reason is not None:
        request_json.update({'desc': reason})

    if check_name == "FORCE_CHECK":
        request_json["re_user_name"] = re_user_name

    signature = generate_signature(request_json)
    request_json["sign"] = signature

    request_xml = dicttoxml(request_json, attr_type=False, custom_root="xml").decode("utf-8")
    request_headers = {"Content-Type": "text/xml"}
    request_certs = (
        os.environ.get("WECHAT_PAY_CLIENT_CERT_PATH"),
        os.environ.get("WECHAT_PAY_CLIENT_KEY_PATH")
    )

    try:
        resp = request_helper.post(url=url,
                                   data=request_xml.encode("utf-8"),
                                   headers=request_headers,
                                   verify=True,
                                   cert=request_certs,
                                   timeout=3)
        resp_xml = resp.content.decode("utf-8")
    except UnicodeEncodeError as error:
        logger.error("企业付款到零钱解码出错，{}, 订单ID: {}".format(error, order_id))
        resp_xml = resp.content.decode("latin-1")

    parsed_resp_xml = xmltodict.parse(resp_xml)
    result = json_format.ParseDict(parsed_resp_xml['xml'], pay_pb.TransferUserWalletResponse(), ignore_unknown_fields=True)
    error = None
    if result.return_code == "SUCCESS":
        if result.result_code == "SUCCESS":
            logger.info("企业付款到零钱成功，订单ID: {}".format(order_id))
        else:
            error = WechatApiResponseError(result.err_code, result.err_code_des, api_desc="企业付款到零钱接口")
    else:
        error = WechatApiResponseError(result.return_code, result.return_msg, api_desc="企业付款到零钱接口")

    if error:
        logger.error("发起付款到用户零钱出错，订单ID: {}, 错误: {}".format(order_id, error))

    return result

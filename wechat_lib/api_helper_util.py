import hashlib
import requests


from dicttoxml import dicttoxml

from common.config import config
from common.utils import access_token_helper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto import merchant_rules_pb2 as merchant_rules_pb


def get_access_token(merchant_id):
    # TOKEN_SERVICE_URL = 'http://139.217.1.236:5000/get_token'
    # resp = requests.get(TOKEN_SERVICE_URL)
    # if resp.status_code == 200:
    #   return resp.json()['access_token']
    # else:
    #   return None
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if merchant.join_method == merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER:
        authorizer_info = merchant.shilai_platform_authorizer_info
        app_id = authorizer_info.authorization_info.authorization_appid
        return access_token_helper.get_authorizer_access_token(appid=app_id)
    elif merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
        app_id = config.SHILAI_MP_APPID
        return access_token_helper.get_shilai_app_access_token(appid=app_id)

    # TODO: Logger
    return None
    # return '20_g5VP9WOHqn8QCACXEwukG5QGfa5eZOuEOIAzRMrEeifUEVCqzOBEh3WLyGpMpjfPTYf1vzKmeWZxeLXtLq5zIxlESXTy8OxCG7kUZXE47di34kAtXUP1iufy5Tpv0f9Ss8ipowL2DK2vl4n9MBFgAHDYNR'


def get_token_embedded_url(merchant_id, base_url):
    return base_url + '?access_token=' + get_access_token(merchant_id)


def get_xml_request_with_sign(orig_obj):
    orig_obj['sign'] = generate_sign(orig_obj)
    return dicttoxml(orig_obj, attr_type=False).decode('utf-8')


def generate_sign(mch_key, dict_obj):
    result = ''
    for key in sorted(dict_obj.keys()):
      result = result + key + '=' + dict_obj[key] + '&'

    result = result + 'key=' + mch_key
    return hashlib.md5(result.encode('utf-8')).hexdigest().upper()


def is_request_successful(response_json):
    if 'errcode' in response_json:
      return response_json['errcode'] == 0

    # TODO: 当前默认请求成功，但需补充更多针对不同返回信息的条件检查。
    return True

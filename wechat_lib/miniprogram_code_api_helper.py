import requests
from retrying import retry

from common.utils import access_token_helper
from wechat_lib.api_constants import WechatApiUrls


@retry(stop_max_attempt_number=3)
def create_unlimited_code(appid, scene, page, width=430, is_hyaline=False, check_path=False):
    """ 创建数量暂无限制的小程序码

      参数:
         appid: 小程序appid
         scene: 小程序码API参数，最大32个可见字符，只支持数字，大小写英文以及部分特殊字符，
                注意不支持 %，中文无法使用 urlencode 处理
         page: 小程序码API参数，访问页面路径，必须是已发布的小程序存在的页面
         width: 小程序码API参数，二维码的宽度，单位 px，最小 280px，最大 1280px，默认 430px
         is_hyaline: 小程序码API参数，是否需要透明底色，默认是非透明

      返回:
         文件流内容
    """
    access_token = access_token_helper.get_shilai_app_access_token(appid)
    url = "{}?access_token={}".format(WechatApiUrls.UNLIMITED_CODE_URL, access_token)
    request_json = {
        'scene': scene,
        'page': page,
        'width': width,
        'is_hyaline': is_hyaline,
        'check_path': check_path
    }
    resp = requests.post(url=url, json=request_json)

    return resp.content

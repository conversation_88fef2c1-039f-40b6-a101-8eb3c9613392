import logging
import requests
from retrying import retry

from common.utils import access_token_helper
from wechat_lib.api_constants import WechatApiUrls

logger = logging.getLogger(__name__)

@retry(stop_max_attempt_number=3)
def send_message(appid,
                 touser,
                 template_id,
                 form_id,
                 data=None,
                 page=None,
                 emphasis_keyword=None):
    """
        发送模板消息

        :param appid: (String) 小程序appid
        :param touser: (String) 接收者的openid
        :param template_id: (String) 模板消息的template_id
        :param form_id: (String) 表单提交场景下，为 submit 事件带上的 formId；支付场景下，为本次支付的 prepay_id
        :param data: (Object) 模板内容，不填则下发空模板
        :param page: (String) 点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar），该字段不填则模板无跳转
        :param emphasis_keyword: (String) 模板需要放大的关键词，不填则默认无放大

        :return:
            :errCode 40037: template_id 不正确
            :errCode 41028: form_id 不正确或者过期
            :errCode 41029: form_id 已被使用
            :errCode 41030: page 不正确
            :errCode 45009: 接口调用超过限额(目前默认每个账号日调用限额为100万)
    """
    access_token = access_token_helper.get_shilai_app_access_token(appid)
    url = "{}?access_token={}".format(WechatApiUrls.TEMPLATE_MESSAGE_SEND_URL, access_token)
    request_json = {
        'touser': touser,
        'template_id': template_id,
        'form_id': form_id,
    }

    if data is not None:
        request_json['data'] = data

    if page is not None:
        request_json['page'] = page

    if emphasis_keyword is not None:
        request_json['emphasis_keyword'] = emphasis_keyword

    resp = requests.post(url=url, json=request_json)
    resp_json = resp.json()

    if resp_json['errcode'] == 0:
        logger.info('发送模板消息成功。')
    else:
        logger.error('发送模板消息出错，错误码: {}，错误内容: {}。'.format(resp_json['errcode'], resp_json['errmsg']))

    return resp_json

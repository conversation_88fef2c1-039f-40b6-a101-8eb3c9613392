class WechatApiUrls(object):
  #============== 会员卡相关URL ==============
  # 创建会员卡
  CREATE_MEMBER_CARD_CLASS_URL = 'https://api.weixin.qq.com/card/create'
  # 调用激活接口为用户激活会员卡
  ACTIVATE_MEMBER_CARD_URL = 'https://api.weixin.qq.com/card/membercard/activate'
  # 设置用户激活时填写的选项，完成一键开卡
  ACTIVATE_USER_FORM_URL = 'https://api.weixin.qq.com/card/membercard/activateuserform/set'
  # 根据CardID和Code查询会员信息
  GET_USER_INFO_URL = 'https://api.weixin.qq.com/card/membercard/userinfo/get'
  # 根据activate_ticket获取到用户填写的信息
  ACTIVATE_TEMP_INFO_URL = 'https://api.weixin.qq.com/card/membercard/activatetempinfo/get'
  # 更新会员信息
  UPDATE_MEMBERCARD_USER_URL = 'https://api.weixin.qq.com/card/membercard/updateuser'
  # 开卡组件链接
  ACTIVATE_MEMBER_CARD_CATEGORY_URL = 'https://api.weixin.qq.com/card/membercard/activate/geturl'

  #=============== 小程序码相关API =============
  UNLIMITED_CODE_URL = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit'

  #=============== 模板消息相关API =============
  TEMPLATE_MESSAGE_SEND_URL = 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send'

  #============== 微信订阅消息 =================
  MESSAGE_SUBSCRIBE_SEND_URL = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send'
  MESSAGE_WXOPEN_TEMPLATE_UNIFORM_SEND = 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send'

  #=============== 卡券子商户相关API ===========
  # 创建子商户
  CREATE_SUBMERCHANT = 'https://api.weixin.qq.com/card/submerchant/submit'
  # 更新子商户
  UPDATE_SUBMERCHANT = 'https://api.weixin.qq.com/card/submerchant/update'
  # 拉取单个子商户
  GET_SUBMERCHANT = 'https://api.weixin.qq.com/card/submerchant/get'
  # 卡券开放类目查询接口
  GET_APPLY_CATEGORY = 'https://api.weixin.qq.com/card/getapplyprotocol'

  #=============== 素材管理相关API ============
  UPLOAD_TEMPORARY_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/media/upload'
  UPLOAD_IMAGE_URL = 'https://api.weixin.qq.com/cgi-bin/media/uploadimg'

  #============== Ticket API =============
  GET_API_TICKET_URL = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket'

  #============== 关联小程序 API =============
  GET_LINKING_MINIPROGRAMS = 'https://api.weixin.qq.com/cgi-bin/wxopen/wxamplinkget'
  LINK_MINIPROGRAM = 'https://api.weixin.qq.com/cgi-bin/wxopen/wxamplink'
  UNLINK_MINIPROGRAM = 'https://api.weixin.qq.com/cgi-bin/wxopen/wxampunlink'


class ReturnStatusCode(object):
  SUCCESS = 0
  # TODO: 扩充具体的错误原因及代码
  ERROR = 1

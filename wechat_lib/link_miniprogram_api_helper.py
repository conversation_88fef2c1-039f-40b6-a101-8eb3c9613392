# 小程序管理权限集
# 通过 API 的方式给授权公众号发起关联时来饭票小程序的流程
# 授权公众号完成关联时来饭票小程序之后，获得以下能力:
#   1. 授权公众号的优惠券可以打开时来饭票小程序

import logging
import requests
from retrying import retry

import proto.merchant_rules_pb2 as merchant_rules_pb

from common.config import config
from common.utils import access_token_helper
from wechat_lib.api_constants import WechatApiUrls
from wechat_lib.resp_error import WechatApiResponseError

logger = logging.getLogger(__name__)


@retry(stop_max_attempt_number=3)
def get_linking_miniprograms(appid):
    """获取已关联的小程序列表

    Args:
        appid: (String) 授权公众号的appid

    Return:
        (List of merchant_rules_pb.LinkingMiniProgramInfo)
    """
    access_token = access_token_helper.get_authorizer_access_token(appid)
    url = "{}?access_token={}".format(WechatApiUrls.GET_LINKING_MINIPROGRAMS,
                                      access_token)

    resp = requests.post(url=url, json={})
    resp_json = resp.json()
    errcode = resp_json['errcode']
    errmsg = resp_json['errmsg']
    linking_miniprograms = []
    if errcode != 0:
        logger.error('获取已关联的小程序列表出错，错误码: {}，错误内容: {}。'.format(errcode, errmsg))
        raise WechatApiResponseError(errcode, errmsg, api_desc='获取已关联的小程序列表')
    elif 'items' in resp_json['wxopens']:
        items = resp_json['wxopens']['items']
        for item in items:
            linking_miniprogram_info = merchant_rules_pb.LinkingMiniProgramInfo()
            linking_miniprogram_info.origin_id = item['username']
            linking_miniprogram_info.name = item['nickname']
            linking_miniprogram_info.status = convert_linking_status_from_api(item['status'])
            linking_miniprogram_info.is_shown_in_profile = True if item['selected'] == 1 else False
            linking_miniprogram_info.is_shown_in_nearby = True if item['nearby_display_status'] == 1 else False
            linking_miniprogram_info.is_released = True if item['released'] == 1 else False
            linking_miniprogram_info.headimg_url = item['headimg_url']
            linking_miniprogram_info.email = item['email']

            for func_info_json in item['func_infos']:
                func_info = linking_miniprogram_info.func_info_list.add()
                func_info.id = func_info_json['id']
                func_info.name = func_info_json['name']
                func_info.status = True if func_info_json['status'] == 1 else False
            linking_miniprograms.append(linking_miniprogram_info)
    return linking_miniprograms


@retry(stop_max_attempt_number=3)
def link_miniprogram(appid, notify_users=False, show_profile=True):
    """发起关联时来饭票小程序流程

    Args:
        appid: (String) 授权公众号的appid
        notify_users: (Boolean) 是否发送模板消息通知公众号粉丝
        show_profile: (Boolean) 是否展示公众号主页中
    """
    access_token = access_token_helper.get_authorizer_access_token(appid)
    url = "{}?access_token={}".format(WechatApiUrls.LINK_MINIPROGRAM,
                                      access_token)
    request_json = {
        "appid": config.WECHAT_MINIPROGRAM_APPID,
    }

    if notify_users is True:
        request_json["notify_users"] = "1"

    if show_profile is True:
        request_json["show_profile"] = "1"

    resp = requests.post(url=url, json=request_json)
    resp_json = resp.json()
    errcode = resp_json['errcode']
    errmsg = resp_json['errmsg']
    if errcode != 0:
        logger.error('关联小程序出错，错误码: {}，错误内容: {}。'.format(errcode, errmsg))
        # raise WechatApiResponseError(errcode, errmsg, api_desc='关联小程序')

    return resp_json


@retry(stop_max_attempt_number=3)
def unlink_miniprogram(appid, linked_miniprogram_appid):
    """解除已关联的小程序

    Args:
        appid: (String) 授权公众号的appid
        linked_miniprogram_appid: (String) 已关联的小程序appid
    """
    access_token = access_token_helper.get_authorizer_access_token(appid)
    url = "{}?access_token={}".format(WechatApiUrls.UNLINK_MINIPROGRAM,
                                      access_token)
    request_json = {
        "appid": linked_miniprogram_appid,
    }
    resp = requests.post(url=url, json=request_json)
    resp_json = resp.json()
    errcode = resp_json['errcode']
    errmsg = resp_json['errmsg']
    if errcode != 0:
        logger.error('解除关联小程序出错，错误码: {}，错误内容: {}。'.format(errcode, errmsg))
        raise WechatApiResponseError(errcode, errmsg, api_desc='解除关联小程序')

    return resp_json


def convert_linking_status_from_api(status):
    """转换关联状态值

    Args:
        status: (Integer) 从接口返回的关联状态值
                1: 已关联
                2: 等待小程序管理员确认中
                3: 小程序管理员拒绝关联
                12: 等到公众号管理员确认中

    Return:
        (merchant_rules_pb.LinkingMiniProgramInfo.LinkingStatus)
    """
    linking_status_map = {
        1: merchant_rules_pb.LinkingMiniProgramInfo.LINKED,
        2: merchant_rules_pb.LinkingMiniProgramInfo.MINIPROGRAM_ADMIN_PENDING,
        3: merchant_rules_pb.LinkingMiniProgramInfo.MINIPROGRAM_ADMIN_REJECTED,
        12: merchant_rules_pb.LinkingMiniProgramInfo.MERCHANT_ADMIN_PENDING,
    }
    if status in linking_status_map:
        return linking_status_map[status]
    return None

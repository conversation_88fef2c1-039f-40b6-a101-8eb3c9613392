# -*- coding: utf-8 -*-

import logging
import requests
from retrying import retry

import proto.membership_service_pb2 as membership_service_pb
import proto.membership_service_pb2 as membership_service_pb
import proto.wechat_member_card_pb2 as wechat_member_card_pb2
from wechat_lib.api_constants import WechatApiUrls
from wechat_lib import api_helper_util
from google.protobuf import json_format
import json

logger = logging.getLogger(__name__)

@retry(stop_max_attempt_number=3)
def create_member_card_class(merchant_id, request):
    """ 创建一类新的会员卡。

      HTTP请求方式:
        POSTURL : https://api.weixin.qq.com/card/create'

      请求参数：

      返回：
          errcode	错误码，0为正常。
          errmsg	错误信息。
          card_id	卡券ID。

      Args:
          member_card_category: CreateMemberCardCategoryRequest信息结构体。

      Returns:
          微信官方生成的 card_id
    """
    wechat_card = request.member_card_category.wechat_card
    json_obj = json_format.MessageToDict(wechat_card, preserving_proto_field_name=True)

    json_obj["member_card"]['supply_bonus'] = False
    json_obj["member_card"]['supply_balance'] = False
    data = {
        'card': json_obj
    }
    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id,
                        WechatApiUrls.CREATE_MEMBER_CARD_CLASS_URL),
                        data=json.dumps(data, ensure_ascii=False).encode('utf-8'))
    result = json_format.ParseDict(resp.json(),
                                   membership_service_pb.CreateMemberCardCategoryResponse())
    if result.errcode != 0:
        logger.error("创建会员卡出错，错误码: {}，错误内容: {}".format(result.errcode, result.errmsg))
        return None

    return result.card_id


@retry(stop_max_attempt_number=3)
def activate_member_card_class(member_card_class):
    """ 激活会员卡。
      HTTP请求方式:
          POSTURL:https://api.weixin.qq.com/card/membercard/activate?access_token=TOKEN

      请求参数：
          membership_number	是	string(20)	会员卡编号，由开发者填入，作为序列号显示在用户的卡包里。可与Code码保持等值。
          code	是	string(20)	领取会员卡用户获得的code
          card_id	否	string（32）	卡券ID,自定义code卡券必填
          background_pic_url	否	string（128）	商家自定义会员卡背景图，须 先调用 上传图片接口 将背景图上传至CDN，否则报错， 卡面设计请遵循 微信会员卡自定义背景设计规范
          activate_begin_time	否	unsigned int	激活后的有效起始时间。若不填写默认以创建时的 data_info 为准。Unix时间戳格式。
          activate_end_time	否	unsigned int	激活后的有效截至时间。若不填写默认以创建时的 data_info 为准。Unix时间戳格式。
          init_bonus	否	int	初始积分，不填为0。
          init_bonus_record	否	string(32)	积分同步说明。
          init_balance	否	int	初始余额，不填为0。
          init_custom_field_value1	否	string（12）	创建时字段custom_field1定义类型的初始值，限制为4个汉字，12字节。
          init_custom_field_value2	否	string（12）	创建时字段custom_field2定义类型的初始值，限制为4个汉字，12字节。
          init_custom_field_value3	否	string（12）	创建时字段custom_field3定义类型的初始值，限制为4个汉字，12字节。

      返回：
          errcode	错误码，0为正常。
          errmsg	错误信息。

      Args:
          member_card_class: MemberCardClass信息结构体。
      Returns:


    """
    resp = requests.post(url=api_helper_util.get_token_embedded_url(
          WechatApiUrls.ACTIVATE_MEMBER_CARD_URL),
          data=member_card_class)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def set_membercard_activate_user_form(merchant_id, activate_user_form):
    """ 设置用户激活时填写的选项，完成一键开卡
      HTTP请求方式:
          POSTURL:https://api.weixin.qq.com/card/membercard/activateuserform/set?access_token=TOKEN

      请求参数：

      返回：

      Args:
          activate_user_form: MemberCardActivateUserForm 信息结构体。
      Returns:

    """
    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id,
                             WechatApiUrls.ACTIVATE_USER_FORM_URL),
                             json=activate_user_form)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def card_membercard_userinfo_get_class(merchant_id, card_id, code):
    """ 根据CardID和Code查询会员信息
      HTTP请求方式:
          POSTURL:https://api.weixin.qq.com/card/membercard/userinfo/get?access_token=TOKEN

      请求参数：
        {
           "card_id": "pbLatjtZ7v1BG_ZnTjbW85GYc_E8",
           "code": "916679873278"
        }
      返回：
         会员信息
      Args:
          member_card_class: MemberCardClass信息结构体。
      Returns:

    """
    data = {
          "card_id": card_id,
          "code": code
    }

    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id,
                             WechatApiUrls.GET_USER_INFO_URL),
                             data=data)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def card_membercard_activatetempinfo_get_class(merchant_id, activate_ticket):
    """ 根据activate_ticket获取到用户填写的信息
      HTTP请求方式:
        POSTURL:https://api.weixin.qq.com/card/membercard/activatetempinfo/get?access_token=TOKEN

      请求参数：
        {
            "activate_ticket" : "abcdefg"
        }
      返回：
        会员信息
      Args:v
        member_card_class: MemberCardClass信息结构体。
      Returns:

    """
    data = {
          "activate_ticket": activate_ticket
        }
    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id,
                             WechatApiUrls.ACTIVATE_TEMP_INFO_URL),
                             data=data)

    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def card_membercard_updateuser_class(member_card_class):
    """ 更新会员信息
      HTTP请求方式:
        POSTURL:https://api.weixin.qq.com/card/membercard/updateuser?access_token=TOKEN

      请求参数：
          {
              "activate_ticket" : "abcdefg"
          }
      返回：
          errcode	错误码，0为正常
          errmsg	错误信息
          result_bonus	当前用户积分总额
          result_balance	当前用户预存总金额
          openid	用户openid

      Args:
        member_card_class: MemberCardClass信息结构体。
      Returns:

    """
    resp = requests.post(url=api_helper_util.get_token_embedded_url(
            WechatApiUrls.UPDATE_MEMBERCARD_USER_URL),
            data=wechat_member_card_pb2.wechat_card_spec)
    result = resp.json()
    return result


@retry(stop_max_attempt_number=3)
def activate_member_card_category(merchant_id, card_category_id, outer_str):
    resp = requests.post(url=api_helper_util.get_token_embedded_url(merchant_id,
            WechatApiUrls.ACTIVATE_MEMBER_CARD_CATEGORY_URL),
            json={
                'card_id': card_category_id,
                'outer_str': outer_str
            })
    result = resp.json()
    return result

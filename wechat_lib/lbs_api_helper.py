from google.protobuf import json_format

from common.config import config
from proto import poi_pb2 as poi_pb
from proto.wechat import lbs_api_pb2 as lbs_api_pb
from wechat_lib import request_helper

def get_address_location(address, region=None):
    """由地址描述到所述位置坐标的转换

    参数:
        address: (String) 地址（需要包含城市名称）
        region: (String) 指定地址所属城市

    返回:
        (POI) 地图坐标POI
    """
    url = 'https://apis.map.qq.com/ws/geocoder/v1/'
    params = {
        'address': address,
        'key': config.TENCENT_LBS_API_KEY
    }
    if region:
        params['region'] = region
    response = request_helper.get(url, params=params)
    resp = json_format.ParseDict(response.json(), lbs_api_pb.GeocoderAPIResponse(),
                                 ignore_unknown_fields=True)
    if resp.status == 0:
        result = resp.result
        poi = poi_pb.POI()
        poi.title = result.title
        poi.location.longitude = result.location.lng
        poi.location.latitude = result.location.lat
        poi.ad_info.CopyFrom(result.ad_info)
        poi.address_components.CopyFrom(result.address_components)
        poi.reliability = result.reliability
        poi.level = result.level
        return poi

    # TODO:
    #  1. 抛出接口错误
    #  2. 重整 logger 使得能投入生产
    return None

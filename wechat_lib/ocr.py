# -*- coding: utf-8 -*-

import os

from common.config import config
from common import http

access_token_domain = os.environ.get("ACCESS_TOKEN_SERVICE_DOMAIN", "https://shilai.zhiyi.cn")


class WechatOCR:

    access_token_url = access_token_domain + "/wxaccess_token/{}/{}"

    # 身份证识别
    IDCARD_URL = "https://api.weixin.qq.com/cv/ocr/idcard"
    # 银行卡
    BANK_CARD_URL = "https://api.weixin.qq.com/cv/ocr/bankcard"
    # 行驶证
    DRIVING_CARD_URL = "https://api.weixin.qq.com/cv/ocr/driving"
    # 架驶证
    DRIVING_LICENSE_URL = "https://api.weixin.qq.com/cv/ocr/drivinglicense"
    # 营业执照
    BIZLICENSE_URL = "https://api.weixin.qq.com/cv/ocr/bizlicense"
    # 通用印刷体
    COMMON_URL = "https://api.weixin.qq.com/cv/ocr/comm"
    # 菜单
    # 1、要已认证的订阅号、服务号、企业号、小程序账号才可以调用
    # 2、该接口尚未接入服务平台，暂时不支持付费购买。
    MENU_URL = "https://api.weixin.qq.com/cv/ocr/menu"

    def ocr(self, file, type):
        access_token = self.__get_access_token()
        if not access_token:
            return
        base_url = self.__get_base_url(type)
        url = "{}?access_token={}".format(base_url, access_token)
        files = {"img": file}
        ret = http.post(url, files=files).json()
        return ret

    def __get_base_url(self, type):
        if type == "idcard":
            return self.IDCARD_URL
        if type == "bank_card":
            return self.BANK_CARD_URL
        if type == "driving_card":
            return self.DRIVING_CARD_URL
        if type == "driving_license":
            return self.DRIVING_LICENSE_URL
        if type == "bizlicense":
            return self.BIZLICENSE_URL
        if type == "common":
            return self.COMMON_URL
        if type == "menu":
            return self.MENU_URL

    def __get_access_token(self):
        appid = config.WECHAT_MINIPROGRAM_APPID
        token_type = "get_authorizer_token"
        url = self.access_token_url.format(token_type, appid)
        resp = http.get(url)
        resp_json = resp.json()
        if not resp_json.get("access_token"):
            return None
        access_token = resp_json['access_token']
        return access_token

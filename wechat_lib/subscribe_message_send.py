# -*- coding: utf-8 -*-

import logging
import requests
from retrying import retry
from datetime import datetime

import maya

import proto.config_pb2 as config_pb
from event_ops.pay_order_success_message import PayOrderSuccessMessage
from event_ops.activity_message_subscribe import ActivityMessageSubscribe
from common.utils import access_token_helper
from common.config import config

logger = logging.getLogger(__name__)


@retry(stop_max_attempt_number=3)
def waiting_for_pay_subscribe_message(touser, amount, transaction_id, store_name, group_dining_event_id):
    """ 约饭AA待支付微信提醒
    """
    mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
    access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
    template_id = 'U1pSYgXLV3djM6DCYlit9tV4CLjPw-dZ7chnAI7qLsM'
    url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}'.format(access_token)
    amount = '{:.2f}元'.format(float(amount) / 100)
    date = maya.when('now').datetime(to_timezone='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')

    data = {
        "access_token": access_token,
        "touser": touser,
        'template_id': template_id,
        'page': 'package-merchant/pay-detail/pay-detail?diningId={}'.format(group_dining_event_id),
        'data': {
            'amount1': {
                'value': amount
            },
            'date2': {
                'value': date
            },
            'character_string3': {
                'value': transaction_id
            },
            'thing4': {
                'value': store_name
            }
        }
    }

    resp = requests.post(url=url, json=data)
    resp_json = resp.json()

    logger.info('发送约饭待AA支付提醒: {}'.format(group_dining_event_id))

    if resp_json['errcode'] == 0:
        logger.info('发送模板消息成功。')
    else:
        logger.error('发送模板消息出错，错误码: {}，错误内容: {}。'.format(resp_json['errcode'], resp_json['errmsg']))

    return resp_json


@retry(stop_max_attempt_number=3)
def take_away_subscribe_message(touser, meal_code, store_name):
    """ 外带扫码点餐提醒
    """
    return
    try:
        template_id = 'VkrEU5lHsgc9X8wEzP3caMahK8NyaobxzKV6lGMxUGM'
        mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
        access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
        url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}'.format(access_token)
        date = maya.when('now').datetime(to_timezone='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
        data = {
            "access_token": access_token,
            "touser": touser,
            'template_id': template_id,
            'page': 'package-merchant/menu',
            'data': {
                'thing1': {
                    'value': '下单成功，请留意服务员叫号'
                },
                'character_string3': {
                    'value': meal_code
                },
                'thing9': {
                    'value': store_name
                },
                'time8': {
                    'value': date
                }
            }
        }

        resp = requests.post(url=url, json=data)
        resp_json = resp.json()

        if resp_json['errcode'] == 0:
            logger.info('发送模板消息成功。')
        else:
            logger.error('发送模板消息出错，错误码: {}，错误内容: {}。'.format(resp_json['errcode'], resp_json['errmsg']))
        return resp_json
    except Exception as ex:
        logger.info(f"外带扫码点餐提醒: {ex}")


@retry(stop_max_attempt_number=3)
def reminder_user_coupon_package_overdue(touser, store_name, goods_name, expire_time, sweet_reminder):
    try:
        template_id = "Yw0FU9rfdjBUeSSKBTPDFxvan0OmeKVoOiuN053uGkA"
        mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
        access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
        url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}".format(access_token)
        expire_date = datetime.fromtimestamp(expire_time).strftime("%Y-%m-%d %H:%M:%S")
        data = {
            "access_token": access_token,
            "touser": touser,
            "template_id": template_id,
            "page": "package-mine/coupon/coupon",
            "data": {
                "thing1": {"value": store_name},
                "thing2": {"value": goods_name},
                "time3": {"value": expire_date},
                "thing4": {"value": sweet_reminder}
            }
        }
        resp = requests.post(url, json=data)
        resp_json = resp.json()
        if resp_json["errcode"] == 0:
            logger.info("发送模板消息成功")
        else:
            logger.info("发送模板消息出错: {}, {}".format(resp_json['errcode'], resp_json['errmsg']))
        return resp_json
    except Exception as ex:
        logger.info(f"券包过期提醒出错: {ex}")


@retry(stop_max_attempt_number=3)
def order_success_subscribe_message(touser, transaction_id, store_name, amount):
    ''' 下单成功
    '''
    try:
        template_id = 'AyRlTTB8YH9ksyB5bhVzMoEKoJIT8rBjTo4GBiWdy0Q'
        mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
        access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
        url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}'.format(access_token)
        amount = '{:.2f}元'.format(float(amount) / 100)
        date = maya.when('now').datetime(to_timezone='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
        data = {
            "access_token": access_token,
            "touser": touser,
            'template_id': template_id,
            'page': 'package-merchant/menu',
            'data': {
                'character_string1': {
                    'value': transaction_id
                },
                'date2': {
                    'value': date
                },
                'thing3': {
                    'value': store_name
                },
                'amount5': {
                    'value': amount
                },
                'phrase4': {
                    'value': '等待支付'
                }
            }
        }
        resp = requests.post(url=url, json=data)
        resp_json = resp.json()
        if resp_json['errcode'] == 0:
            logger.info('发送模板消息成功。')
        else:
            logger.error('发送模板消息出错，错误码: {}，错误内容: {}。'.format(resp_json['errcode'], resp_json['errmsg']))
        return resp_json
    except Exception as ex:
        logger.info(f"下单成功消息提醒出错: {ex}")


@retry(stop_max_attempt_number=3)
def pay_success_subscribe_message(touser, amount, store_name, transaction_id, group_dining_event_id):
    template_id = '-OZ_8wHe7YOZJhIWuiwvWLAY9gdZ2Rt3wneAbISNkt0'
    mini_program_appid = config.WECHAT_MINIPROGRAM_APPID
    access_token = access_token_helper.get_shilai_app_access_token(mini_program_appid)
    url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}".format(access_token)
    amount = '{:.2f}元'.format(float(amount) / 100)
    date = maya.when('now').datetime(to_timezone='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
    data = {
        "access_token": access_token,
        "touser": touser,
        'template_id': template_id,
        'page': 'package-merchant/pay-detail/pay-detail?diningId={}'.format(group_dining_event_id),
        'data': {
            'thing2': {
                'value': store_name
            },
            'amount1': {
                'value': amount
            },
            'date3': {
                'value': date
            },
            'character_string5': {
                'value': transaction_id
            },
            'thing4': {
                'value': '点击查看AA支付详情'
            }
        }
    }

    resp = requests.post(url=url, json=data)
    resp_json = resp.json()
    logger.info('组局约饭支付成功: {}'.format(group_dining_event_id))
    if resp_json['errcode'] == 0:
        logger.info('发送模板消息成功。')
    else:
        logger.error('发送模板消息出错，错误码: {}，错误内容: {}。'.format(resp_json['errcode'], resp_json['errmsg']))
    return resp_json


@retry(stop_max_attempt_number=3)
def pay_order_success_subscribe_message(touser, amount, merchant, paid_time, remark=None):
    try:
        if remark is None:
            remark = "消费成功"
        address = merchant.stores[0].address
        appid = merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
        obj = PayOrderSuccessMessage()
        obj.send_pay_order_success_message(
            touser=touser,
            appid=appid,
            pay=amount,
            address=address,
            time=paid_time,
            remark=remark)
    except Exception as ex:
        logger.info("给用户下发支付成功消息失败: {}".format(ex))


# @retry(stop_max_attempt_number=3)
def activity_subscribe_message(merchant, touser, activity_type):
    obj = ActivityMessageSubscribe()
    if activity_type == config_pb.ActivityConfig.FANPIAO_SNAP_UP:
        return obj.send_fanpiao_snap_up_message(
            merchant=merchant,
            touser=touser)
    return False

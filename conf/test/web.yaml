# 域名配置
service_domain: "https://test.shilai.zhiyi.cn/{VERSION}"
h5_service_domain: 'http://test.shilai-h5.zhiyi.cn'
pos_service_domain: "https://test.shilai-pos.zhiyi.cn"
pos_service_domain_v2: "https://test.shilai-pos.zhiyi.cn/v1.6"
pos_service_merchant_domain: "https://test.shilai-pos.zhiyi.cn"
tools_server_domain: "http://127.0.0.1"
access_token_service_domain: 'https://test.shilai.zhiyi.cn'
staff_assist_file_domain: 'https://test.shilai.zhiyi.cn'
staff_assist_service_domain: "https://test.shilai.zhiyi.cn"  # 业务助手服务，用于创建二维码

tickets_service_domain: "http://tickets.zhiyi.cn"
service_domain_without_version: 'https://test.shilai.zhiyi.cn'

websocket_service_domain: "https://test.shilai.zhiyi.cn/ws_api"

# 登录服务
access_token_service_address: '127.0.0.1'
access_token_service_port: 8886
shilai_access_token_service_address: "127.0.0.1"
shilai_access_token_service_port: 8886

# Redis配置
redis_host: '127.0.0.1'
redis_port: 19631
redis_db: 0
redis_password: "1aa95b9f8427f47c3e7c2ad1428b5705"

redis_service:
  host: '127.0.0.1'
  port: 19631
  password: "1aa95b9f8427f47c3e7c2ad1428b5705"
  db: 0
  socket_timeout: 30
  retry_on_timeout: true
  socket_keepalive: true

# MongoDB数据库配置
mongodb_server_address: '127.0.0.1'
mongodb_port: 13691
mongodb_user_name: 'shilai'
mongodb_root_password: 'a7cfd52194d326d5465a89192809aeef'
mongodb_replica_set: 'shilai-rs'

# 微信授权换取公众号信息，几乎没有用到
# utils_mongodb:
#   host: '********'
#   port: 27017
#   username: 'root'
#   password: 'zhiyi#mongodb#go!'

log_root: "logs/shilai/{SERVICE_NAME}"
log_dir: "logs/shilai/{SERVICE_NAME}/{VERSION}"

syslog_service:
  host: 'logger.server'
  port: 514  # udp
  level: 'info'
  enable_console: false

[uwsgi]
procname = uwsgi_merchant-assist-service --ini /root/code/merchant-assist-service/v1.6/conf/test/uwsgi.ini --port 7250 --version v1.6
http = 0.0.0.0:7250

strict = true
virtualenv = /root/code/merchant-assist-service/env
chdir = /root/code/merchant-assist-service/v1.6
wsgi-file = service/main_service.py
callable = app
master = true
processes = 2
threads = 1
gevent = 1024
gevent-early-monkey-patch = true
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1

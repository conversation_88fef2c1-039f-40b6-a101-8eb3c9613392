[uwsgi]
procname = uwsgi_main-service --ini /home/<USER>/code/shilai-common/v1.7/conf/test/uwsgi.ini --port 12917 --version v1.7
http = 0.0.0.0:12917

strict = true
virtualenv = /home/<USER>/code/shilai-common/env
chdir = /home/<USER>/code/shilai-common/v1.7
wsgi-file = service/main_service.py
callable = app
master = true
processes = 1
threads = 1
gevent = 1024
gevent-early-monkey-patch = true
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1

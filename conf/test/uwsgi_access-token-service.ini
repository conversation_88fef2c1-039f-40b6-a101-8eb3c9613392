[uwsgi]
procname = uwsgi_access-token-service --ini /root/code/access-token-service/v1.6/conf/test/uwsgi.ini --port 8886 --version v1.6
http = 0.0.0.0:8886

strict = true
virtualenv = /root/code/access-token-service/env
chdir = /root/code/access-token-service/v1.6
wsgi-file = service/access_token_service.py
callable = app
master = true
processes = 2
threads = 1
gevent = 1024
gevent-early-monkey-patch = true
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1

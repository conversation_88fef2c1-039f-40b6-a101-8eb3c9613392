[uwsgi]
procname = uwsgi_main-service --ini /root/code/shilai-common/v1.7/conf/prod/uwsgi.ini --port 12917 --version v1.7
http = 0.0.0.0:12917

strict = true
virtualenv = /root/code/shilai-common/env
chdir = /root/code/shilai-common/v1.7
wsgi-file = service/main_service.py
callable = app
master = true
processes = 32
threads = 1
gevent = 4098
gevent-early-monkey-patch = true
listen = 2048
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1

# 域名配置
service_domain: "https://shilai.zhiyi.cn/{VERSION}"  # 主服务，用于支付回调
pos_service_domain: "https://shilai-pos.zhiyi.cn"  # 收银机服务，用于同步数据到收银机或数据中心
pos_service_domain_v2: "https://shilai-pos.zhiyi.cn/v1.6"
access_token_service_domain: 'https://shilai.zhiyi.cn'  # 微信小程序access_token服务，使用开发ID验证公众号二维码身份
staff_assist_file_domain: 'http://shilai-staff.zhiyi.cn'  # 飞鹅后台下载菜品excel

h5_service_domain: 'http://shilai-h5.zhiyi.cn'  # 小程序静态页面，用于二维码页面跳转
staff_assist_service_domain: "https://shilai.zhiyi.cn"  # 业务助手服务，用于创建二维码

pos_service_merchant_domain: "https://shilai-pos.merchant.zhiyi.cn"  # 时来web后台服务，用于商家助手获取营业统计数据
tools_server_domain: "http://tools.server"  # 二维码生成工具服务，使用于业务助手

tickets_service_domain: "http://tickets.zhiyi.cn"  ## 【没有被使用】, 跟access_token登录服务有关
service_domain_without_version: 'https://shilai.zhiyi.cn'  # 【没有被使用】

websocket_service_domain: "https://shilai.zhiyi.cn/ws_api"


# 开发者access_token管理服务，【没有被使用】，目前通过access_token_service_domain/wxaccess_token 域名转发
access_token_service_address: '*************'
access_token_service_port: 8886
shilai_access_token_service_address: "*************"
shilai_access_token_service_port: 8886

# Redis配置
redis_host: 'redis-server'  # *************
redis_port: 19631
redis_db: 0
redis_password: "1aa95b9f8427f47c3e7c2ad1428b5705"

redis_service:
  host: '*************'
  port: 19631
  password: "1aa95b9f8427f47c3e7c2ad1428b5705"
  db: 0
  socket_timeout: 30
  retry_on_timeout: true
  socket_keepalive: true

# MongoDB数据库配置
mongodb_server_address: 'mongodb002'  # *************
mongodb_port: 13691
mongodb_user_name: 'shilai'
mongodb_root_password: 'a7cfd52194d326d5465a89192809aeef'
mongodb_replica_set: 'shilai-rs'

# 微信授权换取公众号信息，【没有被使用】
# utils_mongodb:
#   host: '********'
#   port: 27017
#   username: 'root'
#   password: 'zhiyi#mongodb#go!'

syslog_service:
  host: 'logger.server'
  port: 514  # udp
  level: 'info'
  enable_console: false


# 外部API版本
outer_version: v1.6

# 日志
log_root: "/data/logs/shilai/{SERVICE_NAME}"
log_dir: "/data/logs/shilai/{SERVICE_NAME}/{VERSION}"

# 图片域名
shilai_img_host: "shilai.zhiyi.cn"

# 阿里云oss
aliyun_oss_shilai_domain: "https://shilai-images.oss-cn-shenzhen.aliyuncs.com/"
aliyun_oss_upload_url: "http://oss-cn-shenzhen.aliyuncs.com"
aliyun_oss_access_key: "LTAI4G1zvzqANmwFDS9zCSb7"
aliyun_oss_access_key_secret: "******************************"
aliyun_oss_dish_image_bucket: "shilai-images"
aliyun_oss_shilai_region: "cn-shenzhen"

# 微信支付
wechat_pay_client_cert_path: '/data/shilai_cert/apiclient_cert.pem'
wechat_pay_client_key_path: '/data/shilai_cert/apiclient_key.pem'
refund_wechat_pay_client_cert_path: '/data/shilai_cert/refund_apiclient_cert.pem'
refund_wechat_pay_client_key_path: '/data/shilai_cert/refund_apiclient_key.pem'

# C端小程序
wechat_app_id_env_name: 'wx9b0a448bd837194c'  # 开发版

wechat_miniprogram_old_appid: 'wxdd5cafec95f6cc46'  # v1.6，时来饭票
wechat_miniprogram_old_secret: '95b044a926269962950d6084a49abd0c'  # v1.6，时来饭票

wechat_miniprogram_appid: 'wxaa3c47ef72452be7'  # v1.7，时来时享
wechat_miniprogram_secret: '8a478e5403c3d002dec32ca31abe889a'  # v1.7，时来时享

wechat_miniprogram_origin_id: 'gh_369e154cc7ba'
wechat_miniprogram_shiyi_cat_appid: "wx4e3c563e105ae828"
wechat_miniprogram_shiyi_cat_secret: "579250969b3cd3ddacb4a05f39330a2c"

# 商户端助手
wechat_miniprogram_merchant_appid: 'wxb61d678466685a5a'
wechat_miniprogram_merchant_secret: 'e10f0cc827af5457c925da10afb325d9'

# 业务助手
wechat_miniprogram_staff_appid: 'wxb481ce8d016fb2f3'
wechat_miniprogram_staff_secret: '128ab4ce6e5a7cb7ff175c30afdc26a2'

# 时来开放平台
wechat_app_id: 'wx9b0a448bd837194c'

# 时来服务商号
wechat_merchant_id: '1510708561'
wechat_merchant_key: '34861849ce3144fd82d804a9783ef99e'

# 时来公众号appid
shilai_mp_appid: 'wx56a5980f33bee8f9'
# 时来公众号appsecret
shilai_mp_app_secret: '4f3483f03a00f5e5592e458a07c7bed3'

# 获取平台component_verify_ticket
wechat_message_verify_token: '9NO3h9CZ7v2Tba3fyuISeuhDZQoJ2pOW'
wechat_message_encoded_symmetric_key: 'CGlpqsDfvlZeW4nz2olvXwJiI91Rn4BSsqsx4qql8jO'

# 腾讯位置服务 API KEY
tencent_lbs_api_key: 'PUPBZ-W74WX-IRK4R-TODRO-XZ3OJ-F4BSI'

# 时来在微信支付服务商下子商户
shilai_settlement_account: '**********'

# 通过业务获取不同的微信appid
platform_user: "user"
platform_merchant: "merchant"
platform_shilai_staff: "shilai-staff"

# 静态文件路径
root_path: '/tmp'
# 保存商户二维码路径
merchant_id_qrcode_save_dir: '/tmp/shilai_common/data/qrcode_images'
# 保存码牌二维码路径
code_plate_qrcode_save_dir: '/tmp/shilai_common/data/code_plate_qrcode'
# 保存宣发二维码路径
publicity_qrcode_save_dir: '/tmp/shilai_common/data/publicity_qrcode'
# 保存商户下用户小程序码路径
merchant_code_dir: '/tmp/shilai_common/data/merchant_code_images'
self_dining_merchnt_code_dir: '/tmp/shilai_common/data/self_dining_merchant_code_images'
# 保存商户下饭局入口的小程序码路径
group_dining_code_dir: '/tmp/shilai_common/data/group_dining_code_images'
# 商户桌台点餐二维码图片存储入口
table_code_dir: '/tmp/shilai_common/data/merchant_table_code_images'
# 保存上传的静态图片的路径
static_images_dir: '/tmp/shilai_common/data/static_images'

# 第三方商户ID
grant_type: 'authorization_code'
# 时间相关常量
one_day_in_seconds: 86400
str_format: "%y-%m-%d %h:%m:%s"

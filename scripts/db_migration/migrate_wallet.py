# -*- coding: utf-8 -*-

import sys
sys.path.append("../../")


from scripts.services import service_common
service_common.set_environment_var("local")

from dao.wallet_da_helper import WalletDataAccessHelper
from dao import constants

class MigrateWallet(WalletDataAccessHelper):
    def migrate_wallet(self):
        match = {
            "owner_id": {
                "$exists": 1
            }
        }
        db = self.mongo_client[constants.MONGODB_WALLET_DATABASE_NAME]
        wallet = db[constants.MONGODB_WALLET_COLLECTION_NAME].find(match)
        for w in wallet:
            self.increase_balance(w.get("owner_id"), w.get("balance"))
            db[constants.MONGODB_WALLET_COLLECTION_NAME].remove(w)

if __name__ == '__main__':
    MigrateWallet().migrate_wallet()

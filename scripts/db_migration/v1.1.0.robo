// v1.1.0 迁移内容:
//  1. 数据库 business_entities_db 的数据集 merchants 增加字段 join_method 和 shilai_platform_authorizer_info，
//     数据库 auth_db 的数据集 authorizer_info 内容迁移到 merchants 中的 shilai_platform_authorizer_info
//  2. 数据库 business_entities_db 的数据集 merchants 更新字段 basic_info 的数据结构，增加 logo_url，值填入
//     shilai_platform_authorizer_info 的字段 authorizer_info.head_img
//  3. 删除数据库 auth_db 的数据集 authorizer_info (可选)
//

// ========= 登录校验 ==========
const mongo = new Mongo('');
const adminDB = mongo.getDB('admin');
adminDB.auth('root', '');

// ========= 执行迁移逻辑 ===========
const authDB = mongo.getDB('auth_db');
const businessEntitiesDB = mongo.getDB('business_entities_db');

const authInfoCollection = authDB.getCollection('authorizer_info');
const merchantsCollection = businessEntitiesDB.getCollection('merchants');

const authInfos = authInfoCollection.find({}).toArray();
const merchants = merchantsCollection.find({}).toArray();

const merchantIdToAuthInfo = authInfos.reduce(
    (result, authInfo) => {
        delete authInfo._id;
        const { merchantId } = authInfo;
        result[merchantId] = authInfo;
        return result;
    },
    {}
);

merchants.forEach((merchant) => {
    const { id } = merchant;
    const authInfo = merchantIdToAuthInfo[id];
    if (authInfo) {
        merchantsCollection.updateOne(
            { id: id },
            {
                $set: {
                    'shilaiPlatformAuthorizerInfo': authInfo,
                    'joinMethod': 'SHILAI_PLATFORM_AUTHORIZER',
                    'basicInfo.logoUrl': authInfo.authorizerInfo.headImg
                }
            },
            { upsert: false }
        )
    }
});

# -*- coding: utf-8 -*-

import sys
sys.path.append("../../")

from dao.payment_da_helper import PaymentDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto import payment_pb2 as payment_pb
from common.utils import id_manager

def migrate_payment():
    payments = PaymentDataAccessHelper().get_payments()
    for payment in payments:
        transaction = wallet_pb.Transaction()
        transaction.id = payment.id
        print(payment.id)

        # 己经存在了就不再导入了
        if TransactionDataAccessHelper().get_transaction_by_id(transaction.id):
            continue

        transaction.create_time = payment.create_time
        transaction.type = wallet_pb.Transaction.SELF_DINING_PAYMENT

        if payment.status == payment_pb.Payment.UNKNOWN:
            transaction.state = wallet_pb.Transaction.CREATED
        elif payment.status == payment_pb.Payment.PREPAY_ORDER:
            transaction.state = wallet_pb.Transaction.ORDERED
        elif payment.status == payment_pb.Payment.SUCCESS:
            transaction.state = wallet_pb.Transaction.SUCCESS
        elif payment.status == payment_pb.FAILURE:
            transaction.state = wallet_pb.Transaction.FAILURE

        transaction.payer_id = payment.user_id
        transaction.payer_store_id = ""
        transaction.payee_id = payment.merchant_id
        transaction.payee_store_id = payment.store_id
        transaction.pay_method = wallet_pb.Transaction.WECHAT_PAY
        transaction.bill_fee = payment.bill_fee
        transaction.paid_fee = payment.paid_fee
        transaction.paid_time = payment.paid_time
        transaction.wechat_pay.wechat_prepay_id = payment.wechat_prepay_id
        transaction.wechat_pay.is_subscribe = payment.is_subscribe
        transaction.wechat_pay.sub_is_subscribe = payment.sub_is_subscribe
        transaction.use_coupon_id = payment.use_coupon_id

        TransactionDataAccessHelper().add_transaction(transaction)

if __name__ == '__main__':
    migrate_payment()

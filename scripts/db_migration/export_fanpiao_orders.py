import pandas as pd
from dao import constants
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper


def main():
    order_dao = OrderingServiceDataAccessHelper()
    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
    collection = constants.MONGODB_ORDERING_SERVICE_ORDER_COLLECTION_NAME
    orders_coll = order_dao.mongo_client[db][collection]
    transaction_dao = TransactionDataAccessHelper()
    order_pipeline = [
        {'$match': {'merchantId': 'aa3e40e596f24333bade94210d594766', 'status': 'PAID',
                    'paidTime': {'$gt': '1627747200', '$lt': '1630425599'}}
         },
        {'$project': {
            "paidDate": {"$toDate": {"$multiply": [{"$toLong": "$paidTime"}, 1000]}},
            "id": 1, "serialNumber": 1, "billFee": 1, "paidInFee": 1,
            "redPacketFee": 1, "platformDiscountFee": 1, '_id': 0, "transactionId": 1
        }},
        {'$sort': {'paidDate': -1}}
    ]
    data = []
    print(order_pipeline)
    for doc in orders_coll.aggregate(order_pipeline):
        transaction = transaction_dao.get_transaction_by_id(doc['transactionId'])
        if transaction.pay_method == 5:
            data.append({
                '订单ID': doc['id'], '交易时间': doc['paidDate'], '原价': doc['billFee'],
                '序列号': doc['serialNumber'], '本单补贴': doc['platformDiscountFee'],
                '饭票支出': transaction.paid_fee, '商家实收': doc['paidInFee'],
            })
    pd.DataFrame(data).to_csv("202108_fanpiao_orders.csv", index=False, sep='\t')


if __name__ == '__main__':
    """
    python -m scripts.db_migration.export_fanpiao_orders
    """
    main()

import pandas as pd
from datetime import datetime
from dao.merchant_da_helper import MerchantDataAccessHelper


def main():
    dao = MerchantDataAccessHelper()
    collection = dao.mongo_client["business_entities_db"]["merchants"]
    cond = {'paymentInfo.settlementRate': {'$gt': 0, '$lte': 0.0025}, 'status': 'RUNNING'}
    results = list()
    projection = {"id": 1, "name": "$basicInfo.name", "FeiLv": "$paymentInfo.settlementRate", "timestamp": "$activateTimestamp"}
    pipeline = [
        {"$match": cond},
        {"$project": projection}
    ]
    print(pipeline)
    for doc in collection.aggregate(pipeline):
        print(doc)
        doc["费率"] = f"{doc.pop('FeiLv') * 100}%"
        doc['上线日期'] = datetime.fromtimestamp(int(doc.pop('timestamp'))).strftime("%Y-%m-%d")
        doc['商户名称'] = doc.pop('name')
        results.append(doc)
    pd.DataFrame(results).to_csv("0_025商户列表.csv", index=False)


if __name__ == '__main__':
    """
    scripts.db_migration.export_merchant
    """
    main()

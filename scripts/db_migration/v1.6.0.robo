// v1.6.0 迁移内容:
//   1. CouponCategory 移除 is_for_new_members 字段
//      如果 is_for_new_members 为 True 则使用 issue_scene: NEW_MEMBER 替代
//      如果 is_for_new_members 为 False 则使用 issue_scene: NORMAL 替代
//   2. Payment 历史数据补充 storeId 字段，默认使用第一个商户的门店ID
//

// ======== 登录检验 ========
var mongo = new Mongo('');
var adminDB = mongo.getDB('admin');
adminDB.auth('root', '');

// ======== 执行迁移逻辑 ========
// ==== CouponCategory迁移逻辑
var couponDB = mongo.getDB('coupon_db');
var couponCategoriesCollection = couponDB.getCollection('coupon_categories');
var couponCategories = couponCategoriesCollection.find({}).toArray();
couponCategories.forEach(couponCategory => {
  var { id, isForNewMembers } = couponCategory;
  var issueScene = isForNewMembers ? 'NEW_MEMBER': 'NORMAL';

  couponCategoriesCollection.updateOne(
    { id: id },
    {
      $set: { 'issueScene': issueScene },
      $unset: { 'isForNewMembers': '' },
    },
    { upsert: false }
  )
});


// ==== Payment迁移逻辑
var businessEntitiesDB = mongo.getDB('business_entities_db');
var paymentDB = mongo.getDB('payment_db');

var merchantsCollection = businessEntitiesDB.getCollection('merchants');
var paymentsCollection = paymentDB.getCollection('payments');

var merchants = merchantsCollection.find({}).toArray();
var payments = paymentsCollection.find({}).toArray();

var merchantIdToFirstStoreId = merchants.reduce(
  (result, merchant) => {
    var { id, stores } = merchant;
    if (stores && stores.length > 0 && stores[0].id) {
      result[id] = stores[0].id;
    }
    return result;
  },
  {}
);

payments.forEach(payment => {
  if (!payment.storeId) {
    var { id, merchantId } = payment;
    var storeId = merchantIdToFirstStoreId[merchantId];
    if (storeId) {
      paymentsCollection.updateOne(
        { 'id': id },
        {
          $set: { 'storeId': storeId }
        },
        { upsert: false }
      );
    }
  }
})

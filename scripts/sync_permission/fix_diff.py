# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/18 15:41:08
'''

from pymongo import MongoClient


# mongo_client = MongoClient(
#     host="127.0.0.1",
#     port=13692,
#     username='shilai',
#     password='a7cfd52194d326d5465a89192809aeef',
#     replicaSet= "shilai-rs",
#     readPreference='secondaryPreferred',
#     w=3
# )

mongo_client = MongoClient(
    host="*************",
    port=13691,
    username='shilai',
    password='a7cfd52194d326d5465a89192809aeef'
)


def update(old_role, new_role):
    resp = mongo_client["user_db"]["staffs"].update_many(
        {"role": old_role},
        {"$set": {"role": new_role}}
    )
    print(resp.raw_result)


def fix(roles: list):
    for old, new in roles:
        update(old, new)


def rollback(roles: list):
    for old, new in roles:
        update(new, old)


if __name__ == "__main__":
    """角色兼容"""
    fix([
        ("EMPLOYEE", "BD"),
    ])

    # rollback([
    #     ("EMPLOYEE", "BD"),
    # ])
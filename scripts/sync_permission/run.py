# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/11 16:58:01
'''
import json
import os

import requests


with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), "permission.json")) as f:
    roles = json.loads(f.read().strip())


def get_role(name, role):
    resp = requests.post(
        f"{base_api}/ui_permission/get",
        json={
            "name": name,
            "role": role
        }
    ).json()
    return resp['data']


def update_role(item: dict):
    return requests.post(
        f"{base_api}/ui_permission/update",
        json=item
    ).json()


def update_all_roles(item):
    permission_set = {k: 'DISABLE' for k, _ in item['permissionSet'].items()}
    return requests.post(
        f"{base_api}/ui_permission/update/all_role",
        json={
            "name": item["name"],
            "permission_set": permission_set
        }
    ).json()


if __name__ == "__main__":

    for base_api in [
        # "https://test.shilai.zhiyi.cn",
        "https://shilai.zhiyi.cn"
    ]:
        print(update_all_roles(roles[0]))
        for item in roles:
            try:
                role_data = get_role(item['name'], item['role'])
                if not role_data:
                    print(f"=====> 角色不存在，{item['name']}")
                    continue
                role_data['permissionSet'].update(item['permissionSet'])
                resp = update_role(role_data)
                print(resp)
            except:
                print(f"同步失败：{item}")
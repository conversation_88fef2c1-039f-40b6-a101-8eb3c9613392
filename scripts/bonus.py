import sys
sys.path.append("../")

from scripts.services import service_common
service_common.set_environment_var("test")

from dao.wallet_da_helper import WalletDataAccessHelper
from business_ops.transaction_manager import TransactionManager
from proto.finance import wallet_pb2 as wallet_pb

def bonus():
    user_ids = [
        "519b4a91-d85e-45af-8998-2af76f97d53b",
        "8ba92b89-0a85-4a49-9f5f-e2647b1265d9",
        "ad7cd36e-f1b7-4d20-83f2-5115b2267376"
    ]
    payer_id = "ad7cd36e-f1b7-4d20-83f2-5115b2267376"
    wallet_da = WalletDataAccessHelper()
    money = 1000
    pay_method = wallet_pb.Transaction.PayMethod.Value("WALLET")
    for user_id in user_ids:
        transaction = TransactionManager().handle_bonus_to_user(payer_id='', payee_id=user_id,
                                                                pay_method=pay_method, bill_fee=money,
                                                                paid_fee=money)
        TransactionManager().update_transaction(transaction)
        wallet_da.increase_balance(user_id, money)

if __name__ == '__main__':
    bonus()

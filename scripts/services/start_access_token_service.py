# -*- coding: UTF-8 -*-
import argparse
import sys
import os

parentPath = os.path.abspath("../../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common

parser = argparse.ArgumentParser(description='时来后端服务启动脚本')
parser.add_argument('--env', help='服务部署环境，有效选项为: prod | test | local')


if __name__ == "__main__":
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        exit()

    if args.env not in ('prod', 'test', 'local'):
        print('env参数(服务部署环境)仅支持三种选项: prod | test | local')
        exit()

    # 设置环境变量
    service_common.set_environment_var(args.env)
    os.chdir('../../service')
    os.system('python3 access_token_service.py')

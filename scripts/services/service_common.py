# -*- coding: utf-8 -*-

import os

from common import constants
from scripts.services import config_local
from scripts.services import config_prod
from scripts.services import config_test


def set_environment_var(deploy_env=None):
    # config = None
    # if deploy_env is None:
    #     deploy_env = os.environ.get("DEPLOYMENT_ENV")
    # if deploy_env == constants.DEPLOYMENT_ENV_PROD:
    #     config = config_prod
    # elif deploy_env == constants.DEPLOYMENT_ENV_TEST:
    #     config = config_test
    # elif deploy_env == constants.DEPLOYMENT_ENV_LOCAL:
    #     config = config_local

    # os.environ[constants.SHILAI_DEPLOYMENT_ENV_NAME] = deploy_env
    # os.environ[constants.SERVICE_DOMAIN_ENV_NAME] = str(config.SERVICE_DOMAIN)
    # os.environ[constants.SERVICE_DOMAIN_WITHOUT_VERSION_ENV_NAME] = str(config.SERVICE_DOMAIN_WITHOUT_VERSION)
    # os.environ[constants.WECHAT_APP_ID_ENV_NAME] = str(config.WECHAT_APP_ID)
    # os.environ[constants.WECHAT_APP_SECRET_ENV_NAME] = str(config.WECHAT_APP_SECRET)
    # os.environ[constants.H5_SERVICE_DOMAIN_ENV_NAME] = str(config.H5_SERVICE_DOMAIN)
    pass


set_environment_var()

#!/bin/bash

POSITIONAL=()
while [[ $# -gt 0 ]]
do
key="$1"
case $key in
    -e|--env)
    ENV="$2"
    shift # past argument
    shift # past value
    ;;
    *)    # unknown option
    POSITIONAL+=("$1") # save it in an array for later
    shift # past argument
    ;;
esac
done
set -- "${POSITIONAL[@]}" # restore positional parameters

if [[ -z $ENV ]]; then
    echo "Usage: start_service.sh --env prod|test|local"
    exit
fi


./kill_service.sh main_service
./kill_service.sh access_token_service
./kill_service.sh event_handling_service

# 生产环境下MongoDB应独立进行维护和运行
if [[ $ENV != "prod" ]]; then
    python3 restart_mongo.py --env $ENV
fi

sudo ./start_service.sh main_service --env $ENV
sudo ./start_service.sh access_token_service --env $ENV
sudo ./start_service.sh event_handling_service --env $ENV

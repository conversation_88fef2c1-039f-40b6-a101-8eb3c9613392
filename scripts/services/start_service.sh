#!/bin/bash

POSITIONAL=()
while [[ $# -gt 0 ]]
do
key="$1"

case $key in
    -e|--env)
    ENV="$2"
    shift # past argument
    shift # past value
    ;;
    *)    # unknown option
    echo "Service: $1"
    SERVICE="$1"
    POSITIONAL+=("$1") # save it in an array for later
    shift # past argument
    ;;
esac
done
set -- "${POSITIONAL[@]}" # restore positional parameters

if [[ -z $ENV || -z $SERVICE ]]; then
    echo "Usage: start_service.sh --env prod|test|local access_token_service|event_handling_service|main_service|websocket_service"
    exit
fi

nohup python3 "start_${SERVICE}.py" --env $ENV > "/var/log/shilai/${SERVICE}_log.out" 2>&1 &

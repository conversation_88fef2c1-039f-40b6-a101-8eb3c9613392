# -*- coding: utf-8 -*-

import argparse
import sys
import os

parentPath = os.path.abspath("../../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from common import constants
from scripts.services import config_local
from scripts.services import config_prod
from scripts.services import config_test

parser = argparse.ArgumentParser(description='时来后端MongoDB服务启动脚本')
parser.add_argument('--env', help='服务部署环境，有效选项为: prod | test | local')

MONGO_CONTAINER_NAME = 'test_mongo'


if __name__ == '__main__':
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        exit()

    if args.env not in ('prod', 'test', 'local'):
        print('env参数(服务部署环境)仅支持三种选项: prod | test | local')
        exit()

    print('Stoping running MongoDB service container (if any)...')
    stop_container_cmd = 'docker stop {}'.format(MONGO_CONTAINER_NAME)
    os.system(stop_container_cmd)
    print('Removing existing MongoDB service container (if any)...')
    remove_container_cmd = 'docker rm {}'.format(MONGO_CONTAINER_NAME)
    os.system(remove_container_cmd)

    mongo_username = ''
    mongo_root_pw = ''
    if args.env == constants.DEPLOYMENT_ENV_PROD:
        mongo_root_un = config_prod.MONGODB_USER_NAME
        mongo_root_pw = config_prod.MONGODB_ROOT_PASSWORD
    elif args.env == constants.DEPLOYMENT_ENV_TEST:
        mongo_root_un = config_test.MONGODB_USER_NAME
        mongo_root_pw = config_test.MONGODB_ROOT_PASSWORD
    elif args.env == constants.DEPLOYMENT_ENV_LOCAL:
        mongo_root_un = config_local.MONGODB_USER_NAME
        mongo_root_pw = config_local.MONGODB_ROOT_PASSWORD

    print('Starting a new MongoDB service container...')
    start_mongo_cmd = 'docker run -d --name test_mongo -p 8881:27017 -v /mnt/data/db:/data/db '\
                      '-e MONGO_INITDB_ROOT_USERNAME={}  -e MONGO_INITDB_ROOT_PASSWORD={} '\
                      'mongo:3.4.20 &'.format(mongo_root_un, mongo_root_pw)
    os.system(start_mongo_cmd)
    print('Done.')

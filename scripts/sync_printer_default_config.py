# -*- encoding: utf-8 -*-

'''
@Time        :   2024/07/10 18:02:20
'''
import sys
from pymongo import MongoClient


# mongo_client = MongoClient(
#     host="127.0.0.1",
#     port=13692,
#     username='shilai',
#     password='a7cfd52194d326d5465a89192809aeef',
#     replicaSet= "shilai-rs",
#     readPreference='secondaryPreferred',
#     w=3
# )

mongo_client = MongoClient(
    host="*************",
    port=13691,
    username='shilai',
    password='a7cfd52194d326d5465a89192809aeef'
)

printer_configs = mongo_client["config_db"]["printer_configs"]


def update(d: dict):
    enable_bold = d["dishesInfo"]["body"].get("enableBold")
    dish_size = d.get('dishSize', 'SAME_AS_DISH')
    if enable_bold and dish_size == 'SAME_AS_DISH':
        d['dishSize'] = "BOLD"
    attr_size = d.get('attrSize', 'SAME_AS_DISH')
    if enable_bold and attr_size == 'SAME_AS_DISH':
        d['attrSize'] = "BOLD"
    addon_size = d.get('addonSize', 'SAME_AS_DISH')
    if enable_bold and addon_size == 'SAME_AS_DISH':
        d['addonSize'] = "BOLD"
    return d


status = input('打印配置表是否已备份？Y/N: ')
if status.lower() != "y":
    sys.exit()
for config in printer_configs.find():
    checkout_format = config.get("checkoutPrintFormat")
    if checkout_format:
        checkout_format = update(checkout_format)
        packaging_box = checkout_format.get('packagingBox')
        if packaging_box:
            packaging_box.update({
                "typography": "BEGIN_AND_END",
                "enableBold": True
            })

    kitchen_format = config.get("kitchenPrintFormat")
    if kitchen_format:
        kitchen_format = update(kitchen_format)
    resp = printer_configs.update_one(
        {"_id": config["_id"]},
        {'$set': config}
    )
    print(resp.raw_result)

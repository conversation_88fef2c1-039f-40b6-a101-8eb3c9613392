#!/bin/bash

POSITIONAL=()
while [[ $# -gt 0 ]]
do
key="$1"

case $key in
    -e|--env)
    ENV="$2"
    shift # past argument
    shift # past value
    ;;
esac
done
set -- "${POSITIONAL[@]}" # restore positional parameters

if [[ -z $ENV ]]; then
    echo "Usage: start_batch_jobs.sh --env prod|test|local"
    exit
fi

# KILL process
ps -aef | grep 'start_batch_jobs.py' | awk '{print $2}' | xargs sudo kill

nohup python3 "start_batch_jobs.py" --env $ENV > "/var/log/shilai/batch_jobs_log.out" 2>&1 &

import logging

from business_ops.coupon_manager import CouponManager
from dao.coupon_da_helper import CouponDataAccessHelper

import proto.coupons_pb2 as coupons_pb

logger = logging.getLogger('update_coupon_state')

def execute():
    """如果优惠券已过期，则更新优惠券状态
    """
    logger.info('Job update_coupon_state is running.')
    coupon_manager = CouponManager()
    coupons = CouponDataAccessHelper().get_coupon_list()
    for coupon in coupons:
        logger.info('Coupon(ID:{}) state: {}.'.format(coupon.id, coupon.state))
        if coupon_manager.update_coupon_if_expired(coupon.id):
            logger.info('Coupon(ID:{}) is updated successfully.'.format(coupon.id))
    logger.info('Job update_coupon_state is done.')

import sys
import os
import time

parentPath = os.path.abspath("../../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from apscheduler.schedulers.background import BackgroundScheduler

import common.logger.batch_jobs_logger

from common.utils import date_utils
from scripts.batch_jobs import update_coupon_state_job

if __name__ == '__main__':
    sched = BackgroundScheduler()
    sched.configure(timezone=date_utils.get_timezone())

    # 每天三点开始更新优惠券状态工作
    sched.add_job(update_coupon_state_job.execute, 'cron', hour='3')

    # 输出任务队列
    print(sched.print_jobs(), flush=True)

    try:
        sched.start()
        # 保持活动状态
        while True:
            time.sleep(60 * 60 * 6)
    except (KeyboardInterrupt, SystemExit):
        sched.shutdown()

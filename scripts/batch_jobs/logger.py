import os
import logging
from logging import handlers

# TODO: 使用loggin模块文件配置的方式
LOG_ROOT = '/var/log/shilai'
BATCH_JOBS_LOG_PATH = os.path.join(LOG_ROOT, 'batch_jobs')

def create_timed_rotating_file_handler(path):
    filename = os.path.join(BATCH_JOBS_LOG_PATH, path)
    handler = handlers.TimedRotatingFileHandler(filename,
                                                when="D",
                                                interval=1,
                                                encoding="utf-8")
    handler.suffix = "%Y_%m_%d.log"
    handler.setLevel(logging.INFO)

    fmt = '%(asctime)s - %(name)s - %(lineno)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(fmt)
    handler.setFormatter(formatter)

    return handler

def init_loggers():
    # 初始化 update_coupon Logger
    update_coupn_logger = logging.getLogger('update_coupon_state')
    update_coupn_logger.addHandler(create_timed_rotating_file_handler('update_coupon_state'))

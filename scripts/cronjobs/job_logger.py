# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/26 10:28:23
'''
import os
from common.config import config

import logging
from logging.config import dictConfig


STANDARD_FORMAT = '%(asctime)s | %(levelname)s | %(name)s(%(lineno)d): %(message)s'
CUSTOM_FORMAT = '%(asctime)s | %(levelname)s | %(remote_addr)s | %(name)s(%(lineno)d) | user_id:%(user_id)s | merchant_id:%(merchant_id)s | message_uuid:%(message_uuid)s: %(message)s'
LOG_DIR = os.path.join("/data/logs/shilai/cronjobs/v1.7", os.environ.get("job_name"))

handlers = ['info']
if os.environ.get("DEPLOYMENT_ENV", "test"):
    handlers.append('console')
    
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': STANDARD_FORMAT
        },
        'custom': {
            'format': CUSTOM_FORMAT,
            '()': 'common.logger.logger_handler.RequestFormatter'
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
        'info': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'standard',
            'filename': os.path.join(LOG_DIR, 'info.log'),
            'when': 'D',
            'interval': 1,
            'backupCount': 30,
            'encoding': 'utf-8'
        }
    },
    'loggers': {
        '': {
            'handlers': handlers,
            'level': 'INFO'
        }
    }
})


dicttoxml_logger = logging.getLogger("dicttoxml")
dicttoxml_logger.setLevel(logging.ERROR)


def get_logger(name):
    return logging.getLogger(name)


if __name__ == "__main__":
    logger = get_logger('test')
    logger.info("This is a test.")

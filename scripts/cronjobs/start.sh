#!/bin/bash
CURRENT_DIR=$(dirname `readlink -f $0`)
SCRIPTS_DIR=$(dirname `readlink -f $CURRENT_DIR/`)
PROJECT_DIR=$(dirname `readlink -f $SCRIPTS_DIR/`)
PYENV_DIR=$(dirname `readlink -f $PROJECT_DIR/`)

function usage() {
    echo -e "\nUsage: $0 [--script_file <arg>] [--args <arg>] [--deployment_env <arg>] [-h|--help]"
    echo -e "\t\t--script_file 脚本文件相对路径"
    echo -e "\t\t--args 脚本参数\n"
    echo -e "\t\t--deployment_env 环境依赖\n"
    echo -e "\t例如：\n\t\t./start.sh --script_file recover_dish/recover_attr_supply_condiments_status.py --deployment_env test"
    echo -e "\n"
    exit 0
}

_script_file=
_args=
_deployment_env=prod

short_options="h"
long_options="script_file:,args:,deployment_env:,help"
OPTS=$(getopt -o $short_options --long $long_options -n 'parse-options' -- "$@")
eval set -- "$OPTS"

while true; do
    case $1 in
        --script_file)
            _script_file=$2
            shift
            shift;;
        --args)
            _args=$2
            shift
            shift;;
        --deployment_env)
            _deployment_env=$2
            shift
            shift;;
        -h | --help)
            usage
            shift;;
        *)
            break ;;
    esac
done

_script_file="$CURRENT_DIR/$_script_file"
if [ ! -f "$_script_file" ]; then
    echo "脚本文件不存在，请重新输入！$_script_file"
    exit 1
fi

export PYTHONPATH=$PROJECT_DIR:$CURRENT_DIR
export DEPLOYMENT_ENV=$_deployment_env
export VERSION=v1.7
export SERVICE_NAME="cronjobs"

_CMD="$PYENV_DIR/env/bin/python3 $_script_file $_args"

echo PYTHONPATH=$PYTHONPATH
echo $_CMD
eval $_CMD
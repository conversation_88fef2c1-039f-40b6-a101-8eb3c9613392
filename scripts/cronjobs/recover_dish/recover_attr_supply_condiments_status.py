# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/25 19:27:00
次日0点恢复商家设置为手动估清的属性、加料

	ordering_services.orderingConfig.enableAutoRecoverAttrSoldOut: false
	ordering_services.orderingConfig.enableAutoRecoverSupplyCondimentsSoldOut: false
'''
import os

from common.config import config

os.environ["job_name"] = "recover_attr_supply_condiments_status"
from job_logger import get_logger

import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.dish_attr_pb2 as dish_attr_pb
import proto.ordering.supply_condiment_pb2 as supply_condiment_pb
import proto.ordering.registration_pb2 as registration_pb
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.dao_helper import DaoORMHelper
import dao.constants as constants
from cache.dish_cache import DishCatalogCache


logger = get_logger(__name__)
ordering_da = OrderingServiceDataAccessHelper()
merchant_da = MerchantDataAccessHelper()

dish_dao = DaoORMHelper(
    db=constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME,
    collection=constants.MONGODB_ORDERING_SERVICE_DISH_COLLECTION_NAME,
    pb=dish_pb.Dish
)
dish_attr_group_dao = DaoORMHelper(
    db=constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME,
    collection=constants.MONGODB_ORDERING_ATTR_GROUP_COLLECTION_NAME,
    pb=dish_attr_pb.DishAttrGroup
)
dish_supply_condiment_dao = DaoORMHelper(
    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME,
    collection = constants.MONGODB_ORDERING_SUPPLY_CONDIMENT_COLLECTION_NAME,
    pb=supply_condiment_pb.DishSupplyCondiment
)


class RecoverHandler(object):

    def run(self):
        registration_infos = ordering_da.get_registration_infos()
        logger.info(f"注册所有商家数量：{len(registration_infos)}")
        for r in registration_infos:
            if r.pos_type != registration_pb.OrderingServiceRegistrationInfo.FEIE:
                continue
            merchant_id = r.merchant_id
            merchant = merchant_da.get_merchant_by_id(merchant_id)
            if not merchant:
                continue

            updated_dishes = dict()
            if r.ordering_config.enable_auto_recover_attr_sold_out:
                self._recover_attr(merchant, updated_dishes)

            if r.ordering_config.enable_auto_recover_supply_condiments_sold_out:
                self._recover_supply_condiments(merchant, updated_dishes)
            
            for _, dish in updated_dishes.items():
                # ordering_da.add_or_update_dish(dish=dish)
                self.update_dish(dish)
            
            if updated_dishes:
                logger.info(f"商家名：{merchant.basic_info.name}，merchant_id={merchant.id}，共处理菜品数量：{len(updated_dishes)}")

    @DishCatalogCache.update
    def update_dish(self, dish):
        return ordering_da.add_or_update_dish(dish=dish)

    @DishCatalogCache.update
    def update_dish_attr_groups(self, attr_groups, sold_out):
        for group in attr_groups:
            for attr in group.attrs:
                if attr.status == sold_out:
                    attr.status = dish_pb.Attr.Status.NORMAL
                    logger.info(f"==> 属性组：{group.name}, 属性：{attr.name}")
            dish_attr_group_dao.add_or_update(group)

    @DishCatalogCache.update
    def update_dish_supply_condiments_groups(self, supply_condiments_groups, sold_out):
        for group in supply_condiments_groups:
            if group.status == sold_out:
                group.status = dish_pb.Attr.Status.NORMAL
                logger.info(f"==> 加料：{group.name}")
            dish_supply_condiment_dao.add_or_update(group)

    def _recover_attr(self, merchant, updated_dishes):
        sold_out = dish_pb.Attr.Status.SOLD_OUT
        attr_groups = dish_attr_group_dao.query(
            matcher={
                "merchant_id": merchant.id,
                "attrs.status": dish_pb.Attr.Status.Name(sold_out),
            },
            resp_dict=False
        )
        if not attr_groups:
            return
        self.update_dish_attr_groups(attr_groups, sold_out)
        # for group in attr_groups:
        #     for attr in group.attrs:
        #         if attr.status == sold_out:
        #             attr.status = dish_pb.Attr.Status.NORMAL
        #             logger.info(f"==> 属性组：{group.name}, 属性：{attr.name}")
        #     dish_attr_group_dao.add_or_update(group)

        dishs = dish_dao.query(
            matcher={
                "merchant_id": merchant.id,
                "attrs.status": dish_pb.Attr.Status.Name(sold_out),
            },
            resp_dict=False
        )
        logger.info(f"自动恢复菜品属性中, merchant_id={merchant.id}, dish_nums={len(dishs)}")
        for dish in dishs:
            for attr in dish.attrs:
                if attr.status == sold_out:
                    attr.status = dish_pb.Attr.Status.NORMAL
                    logger.info(f"菜品名：{dish.name}")
            updated_dishes[dish.id] = dish

    def _recover_supply_condiments(self, merchant, updated_dishes):
        sold_out = dish_pb.SupplyCondiment.Status.SOLD_OUT
        supply_condiments_groups = dish_supply_condiment_dao.query(
            matcher={
                "merchant_id": merchant.id,
                "status": dish_pb.SupplyCondiment.Status.Name(sold_out),
            },
            resp_dict=False
        )
        if not supply_condiments_groups:
            return
        self.update_dish_supply_condiments_groups(supply_condiments_groups, sold_out)
        # for group in supply_condiments_groups:
        #     if group.status == sold_out:
        #         group.status = dish_pb.Attr.Status.NORMAL
        #         logger.info(f"==> 加料：{group.name}")
        #     dish_supply_condiment_dao.add_or_update(group)

        dishs = dish_dao.query(
            matcher={
                "merchant_id": merchant.id,
                "supply_condiments.status": dish_pb.SupplyCondiment.Status.Name(sold_out),
            },
            resp_dict=False
        )
        logger.info(f"自动恢复菜品加料中, merchant_id={merchant.id}, dish_nums={len(dishs)}")
        for dish in dishs:
            dish = updated_dishes.get(dish.id) or dish
            for supply in dish.supply_condiments:
                if supply.status == sold_out:
                    supply.status = dish_pb.Attr.Status.NORMAL
                    logger.info(f"菜品名：{dish.name}")
            updated_dishes[dish.id] = dish


if __name__ == '__main__':
    logger.info("------------ 开始 ------------")
    try:
        obj = RecoverHandler()
        obj.run()
    except Exception as e:
        logger.exception(f"未知异常: {e}")
    logger.info("------------ 结束 ------------")

    # 0 0 * * * /bin/bash /root/code/cron-jobs-service/v1.7/scripts/cronjobs/start.sh --script_file recover_dish/recover_attr_supply_condiments_status.py &

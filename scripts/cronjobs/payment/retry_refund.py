import os
import time

import requests
from clickhouse_driver import Client


base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")
os.makedirs(base_path, exist_ok=True)
clickhouse = Client(host='*************', port=9000, user='admin', password='fxnFtiZT', database='cloki', verify=False)


def get_data(start_timestamp=None, end_timestamp=None, api="/refund/fanpiao"):
    sql = f"""
        SELECT DISTINCT
            extract(t1.`string`, 'message_uuid:([a-f0-9]+)') AS message_uuid,
            extract(t1.`string`, 'BeforeRequest\\s=====>\\s*(.*)\\"') AS item,
            extract(t1.`string`, 'api_rule\\':\\s\\'(.*?)\\'') AS api_rule
        FROM samples_v3 AS t1
        ALL JOIN (
            SELECT DISTINCT
                extract(`string`, 'message_uuid:([a-f0-9]+)') AS error_message_uuid
            FROM samples_v3
            WHERE 
                `timestamp_ns` >= {start_timestamp}000000000
                AND `timestamp_ns` < {end_timestamp}000000000
                AND `string` LIKE '%请稍后进行查询退款状态%'
        ) AS t2
        ON message_uuid = t2.error_message_uuid
        WHERE
            t1.`timestamp_ns` >= {start_timestamp}000000000
            AND t1.`timestamp_ns` < {end_timestamp}000000000
            AND t1.`string` LIKE '%BeforeRequest%'
        HAVING
            api_rule LIKE '%{api}%'
    """
    df = clickhouse.query_dataframe(sql)

    def extract(row):
        item = row['item']
        for pattern in ('","source_type', '\\n[pid'):
            if pattern in item:
                item = item[: item.find(pattern)]
        return eval(item)

    df['item'] = df.apply(extract, axis=1)
    df['api'] = df['item'].apply(lambda x: x['api'])
    df['user_id'] = df['item'].apply(lambda x: x.get("user_id", ""))
    df['request_args'] = df['item'].apply(lambda x: x['request_args'])
    return df


def handler():
    end_time = int(time.time())
    for data in get_data(start_timestamp=end_time + 10 - 10 * 60, end_timestamp=end_time, api='/refund/fanpiao').to_dict(
        'records'
    ):
        print(data['user_id'], data['request_args'])
        # resp = requests.post(f'{base_api}{data['api']}', json=data["request_args"], headers={"userId": data.get('user_id')})
        # resp.raise_for_status()
        # print(resp.json())
        # print('-----------------')


if __name__ == "__main__":
    base_api = "https://shilai.zhiyi.cn/v1.7"
    handler()

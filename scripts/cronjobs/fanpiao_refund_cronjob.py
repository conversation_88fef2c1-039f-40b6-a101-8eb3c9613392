# -*- encoding: utf-8 -*-

'''
@Time        :   2024/07/23 13:50:43
执行业务助手录入的退饭票任务
'''
import os

from common.config import config

os.environ["job_name"] = "fanpiao_refund_cronjob"

from job_logger import get_logger
from business_ops.fanpiao_manager import FanpiaoManager
import proto.finance.fanpiao_pb2 as fanpiao_pb

logger = get_logger(__name__)


class FanpiaoRefundHandler(object):

    _STATUS = [
        fanpiao_pb.Fanpiao.ACTIVE,
        fanpiao_pb.Fanpiao.EXPIRED
    ]

    def run(self):
        manager = FanpiaoManager()
        record_dao = manager._get_refund_record_dao()
        records = record_dao.query(
            matcher={
                "status": {
                    '$in': list(map(
                        lambda x: fanpiao_pb.Fanpiao.Status.Name(x),
                        self._STATUS
                    ))
                }
            },
            resp_dict=False
        )
        for record in records:
            try:
                fanpiao = manager.get_fanpiao_by_id(record.fanpiao_id)
                result = manager.refund_cronjob(
                    fanpiao,
                    record.is_refund_the_way,
                    record.balance_refund_method
                )
                logger.info(result)
            except Exception as e:
                logger.exception(f"退款失败: merchant_id={record.merchant_id}, merchant_name={record.merchant_name}, fanpiao_id={record.fanpiao_id}, error_msg={e}")


if __name__ == "__main__":
    logger.info("------------ 开始 ------------")
    try:
        obj = FanpiaoRefundHandler()
        obj.run()
    except Exception as e:
        logger.exception(f"未知异常: {e}")
    logger.info("------------ 结束 ------------")

    # 0 0 * * 2 /bin/bash /root/code/cron-jobs-service/v1.7/scripts/cronjobs/start.sh --script_file fanpiao_refund_cronjob.py &
# 从客如云同步菜品

import os, sys

import requests

parentPath = os.path.abspath("../../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common
from common import constants
service_common.set_environment_var(constants.DEPLOYMENT_ENV_PROD)

import begin

import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering import register as ordering_register
from business_ops.ordering.dish_manager import DishManager
from common.utils import id_manager
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.base_helper import BaseHelper
from proto.ordering import dish_pb2 as dish_pb

print('使用方式: ################################################')
print('同步菜品状态: python3.5 async_dish_auto.py async_dish_status -m <商户id>')
print('##########################################################')

@begin.subcommand
def async_dish_status(merchant_id: "商户ID"=None):
    if merchant_id:
        merchant_ids = [merchant_id]
    else:
        merchants = MerchantDataAccessHelper().get_merchant_list(status=4)
        merchant_ids = [merchant.id for merchant in merchants]
    normal_status = dish_pb.Dish.Status.Name(dish_pb.Dish.NORMAL)
    guqing_status = dish_pb.Dish.Status.Name(dish_pb.Dish.GUQING)
    for merchant_id in merchant_ids:
        # TODO: 如果要执行所有同步,打开下面一行注释
        try:
            manager = DishManager(merchant_id=merchant_id)
            registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=merchant_id)
            if not registration_info:
                print('no registration info: {}, {}'.format(merchant_id, manager.merchant.basic_info.name))
                continue
        except Exception as ex:
            print('async_dishes error: {}'.format(ex))
            continue

        dish_ids = []
        dishes = OrderingServiceDataAccessHelper().get_dishes(merchant_id=merchant_id)
        for saved_dish in dishes:
            dish_id = saved_dish.id
            try:
                keruyun_dish = manager.get_dish_by_id_from_keruyun(dish_id)
            except:
                break
            oldstatus = dish_pb.Dish.Status.Name(saved_dish.status)
            if not keruyun_dish:
                print('客如云无法获取此菜品: {}, {}'.format(merchant_id, dish_id))
                # OrderingServiceDataAccessHelper().update_dish(id=str(dish_id), merchant_id=merchant_id, status=dish_pb.Dish.GUQING)
                # print(saved_dish.name, 'from {} to {}'.format(oldstatus, guqing_status))
                continue
            keruyun_dish = keruyun_dish[0]
            # print(keruyun_dish.get('name'), keruyun_dish.get('clearStatus'))
            # clearStatus	Integer	是	估清 1-在售，2-售罄
            print(keruyun_dish.get('clearStatus'))
            if keruyun_dish.get('clearStatus') == 1:
                if oldstatus == normal_status:
                    continue
                # TODO: 如果要执行修改,打开注释
                # OrderingServiceDataAccessHelper().update_dish(id=str(dish_id), merchant_id=merchant_id, status=dish_pb.Dish.NORMAL)
                print(keruyun_dish.get('name'), 'from {} to {}'.format(oldstatus, normal_status))
            elif keruyun_dish.get('clearStatus') == 2:
                if oldstatus == guqing_status:
                    continue
                # TODO: 如果要执行修改,打开注释
                # OrderingServiceDataAccessHelper().update_dish(id=str(dish_id), merchant_id=merchant_id, status=dish_pb.Dish.GUQING)
                print(keruyun_dish.get('name'), 'from {} to {}'.format(oldstatus, guqing_status))
            else:
                print('not clear')
        # TODO: 如果要执行所有商户修改,注释下面一行.
        # break

    print("菜品同步完成")

@begin.start
def main():
    pass

# 从客如云同步菜品

import os, sys
import random

import requests

parentPath = os.path.abspath("../../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common
from common import constants

service_common.set_environment_var(constants.DEPLOYMENT_ENV_TEST)

import begin

import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering import register as ordering_register
from business_ops.ordering.table_manager import TableManager
from common.utils import id_manager
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.base_helper import BaseHelper
from proto.ordering import dish_pb2 as dish_pb


def __create_table(merchant_id, name, ordering_service_table_id=None):
    table_obj = dish_pb.Table()
    table_obj.id = id_manager.generate_common_id()
    table_obj.ordering_service_table_id = ordering_service_table_id
    table_obj.name = name
    table_obj.merchant_id = merchant_id
    if name == '外带':
        table_obj.is_take_away = True
        table_obj.meal_type = dish_pb.DishOrder.TAKE_AWAY
    elif name == '自提' or '预点餐' in name:
        table_obj.meal_type = dish_pb.DishOrder.SELF_PICK_UP
    elif name == '外卖':
        table_obj.meal_type = dish_pb.DishOrder.TAKE_OUT
    elif "支付" in name or "付款" in name:
        table_obj.only_for_pay = True
    else:
        table_obj.meal_type = dish_pb.DishOrder.EAT_IN
    OrderingServiceDataAccessHelper().add_or_update_table(table_obj)
    BaseHelper().create_merchant_table_wxcode(merchant_id, table_obj.id, name)
    return table_obj


def __async_table(merchant_id):
    try:
        manager = TableManager(merchant_id=merchant_id)
    except Exception as ex:
        print(ex)
        return
    shilai_tables = OrderingServiceDataAccessHelper().get_tables(merchant_id=merchant_id)
    shilai_tables = {table.name: table for table in shilai_tables}

    tables = manager.get_tables()
    for table in tables:
        name = table.get('tableName')
        table_obj = shilai_tables.get(name)
        if not table_obj:
            print('cannot find table: {}'.format(name))
            continue
        else:
            db_table_id = table_obj.ordering_service_table_id
            keruyun_table_id = table.get('tableID')
            print('{} {}, {}, {}'.format(name, db_table_id, keruyun_table_id, db_table_id == keruyun_table_id))

        table_obj.ordering_service_table_id = table.get("tableID")
        table_obj.merchant_id = merchant_id
        area_obj = dish_pb.TableArea()
        area_obj.area_code = table.get("tableArea").get("areaCode")
        area_obj.area_name = table.get("tableArea").get("areaName")
        area_obj.floor = table.get("tableArea").get("floor")
        table_obj.table_area.CopyFrom(area_obj)
        table_obj.name = table.get("tableName")
        table_obj.id = table.get("id")

        table_obj.table_person_count = table.get("tablePersonCount")
        table_obj.can_booking = table.get("canBooking") != 0
        table_obj.can_smoke = table.get("isSmoke") == 0  # 0吸烟,1不吸烟
        table_obj.table_type = table.get("tableType")
        table_obj.table_status = table.get("tableStatus")
        table_obj.status = table.get("status")
        OrderingServiceDataAccessHelper().add_or_update_table(table_obj)


@begin.subcommand
def async_tables_with_precreate(merchant_id: "商户Id" = None):
    '''基于时来已有的桌台.从客如云同步桌台信息'''
    print("开始同步桌号")
    if merchant_id is None:
        merchants = MerchantDataAccessHelper().get_merchant_list(status=4)
        for merchant in merchants:
            __async_table(merchant.id)
    else:
        __async_table(merchant_id)
    print("桌号同步完成")


@begin.subcommand
def create_tables(merchant_id: "商户Id" = None, number: '生成的数量' = None):
    """创建空的桌台二维码"""
    names = []
    for name in names:
        ordering_service_table_id = ''.join(str(random.choice(range(10))) for _ in range(18))
        __create_table(merchant_id, name, ordering_service_table_id=ordering_service_table_id)


@begin.subcommand
def async_tables_without_precreate(merchant_id: '商户ID' = None):
    """时来未创建桌台二维码,直接从客如云同步"""
    try:
        manager = TableManager(merchant_id=merchant_id)
    except Exception as ex:
        print(ex)
        return
    tables = manager.get_tables()
    for table in tables:
        name = table.get('tableName')
        ordering_service_table_id = table.get('tableID')
        __create_table(merchant_id, name, ordering_service_table_id)


@begin.subcommand
def add_tables(merchant_id: '商户ID' = None):
    """客如云已经添加桌台,同步新增的桌台到时来"""
    try:
        manager = TableManager(merchant_id=merchant_id)
    except Exception as ex:
        print(ex)
        return
    shilai_tables = OrderingServiceDataAccessHelper().get_tables(merchant_id=merchant_id)
    shilai_tables = {table.name: table for table in shilai_tables}

    tables = manager.get_tables()
    for table in tables:
        name = table.get('tableName')
        table_obj = shilai_tables.get(name)
        if not table_obj:
            print('cannot find table: {}'.format(name))
            table_obj = __create_table(merchant_id, name, table.get("tableID"))
        else:
            db_table_id = table_obj.ordering_service_table_id
            keruyun_table_id = table.get('tableID')
            if table_obj.table_area.area_id == "":
                table_obj.table_area.area_id = str(table.get("tableArea").get("id"))
                OrderingServiceDataAccessHelper().add_or_update_table(table_obj)
            print('{} {}, {}, {}'.format(name, db_table_id, keruyun_table_id, db_table_id == keruyun_table_id))
            continue
        table_obj.merchant_id = merchant_id
        area_obj = dish_pb.TableArea()
        area_obj.area_code = table.get("tableArea").get("areaCode")
        area_obj.area_name = table.get("tableArea").get("areaName")
        area_obj.floor = table.get("tableArea").get("floor")
        area_obj.area_id = str(table.get("tableArea").get("id"))
        table_obj.table_area.CopyFrom(area_obj)
        table_obj.table_person_count = table.get("tablePersonCount")
        table_obj.can_booking = table.get("canBooking") != 0
        table_obj.can_smoke = table.get("isSmoke") == 0  # 0吸烟,1不吸烟
        table_obj.table_type = table.get("tableType")
        table_obj.table_status = table.get("tableStatus")
        table_obj.status = table.get("status")
        OrderingServiceDataAccessHelper().add_or_update_table(table_obj)


@begin.start
def main():
    pass

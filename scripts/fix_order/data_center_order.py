"""
数据中心补单
注意：如果是隔天补需要修改payTime为当前时间，payTime第二天打款给商家
"""

import os
import re
import json
import traceback
import json
import time
from datetime import datetime

import requests
import pandas as pd
from pymongo import MongoClient
from clickhouse_driver import Client


base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")
os.makedirs(base_path, exist_ok=True)
clickhouse = Client(host='*************', port=9000, user='admin', password='fxnFtiZT', database='cloki', verify=False)
mongo = MongoClient(host="*************", port=13691, username='shilai', password='a7cfd52194d326d5465a89192809aeef')


def to_timestamp(time_str):
    time_format = "%Y-%m-%d %H:%M:%S"
    time_obj = datetime.strptime(time_str, time_format)
    return str(int(time_obj.timestamp()))


def get_logs(*args, start_timestamp=None, end_timestamp=None):
    sql = f"""
        SELECT
            string
        FROM samples_v3
        WHERE timestamp_ns > {start_timestamp}000000000
            AND timestamp_ns < {end_timestamp}000000000"""
    for value in args:
        sql += f"\n\t    AND `string` LIKE '%{value}%'"
    return clickhouse.execute(sql)


def handler(
    start_timestamp: str,
    end_timestamp: str,
    update_pay_time=False,
    first_key=("ERROR", "timed out"),
    second_key=("数据中心请求信息", )
):
    start_timestamp = to_timestamp(start_timestamp)
    end_timestamp = to_timestamp(end_timestamp)
    current_time = datetime.fromtimestamp(int(time.time()))
    current_timestamp = current_time.strftime("%Y%m%d%H%M%S")
    current_day = current_time.strftime("%Y%m%d")
    save_file = open(os.path.join(base_path, f"补单{current_timestamp}.txt"), "a", encoding='utf-8')
    error_file = open(os.path.join(base_path, f"error_{current_day}.log"), "a", encoding='utf-8')
    result, count = [], 0
    first_logs = get_logs(
        *first_key,
        start_timestamp=start_timestamp,
        end_timestamp=end_timestamp
    )
    print(f'一级日志总数：{len(first_logs)}')
    for i, first_log in enumerate(first_logs):
        first_log = json.loads(first_log[0])["message"]
        message_uuid = re.search(r"message_uuid:(.*?):", first_log).group(1)
        second_logs = get_logs(
            message_uuid,
            *second_key,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp
        )
        if not second_logs:
            continue
        print(f"正在处理：{i}")
        for second_log in second_logs:
            second_log = json.loads(second_log[0])["message"]
            try:
                ret = re.search(r"json_data=(\{.*?\})(?=\s|\n|$)", second_log)
                if not ret:
                    second_log = second_log[second_log.find("json_data=")+10:]
                    if '[pid' in second_log:
                        second_log = second_log[:second_log.find("[pid")].strip()
                else:
                    second_log = ret.group(1)

                params = eval(second_log)
                order_id = params.get('id')
                if not order_id:
                    continue
                if params.get('order', {}).get('status', '') != 'PAID':
                    continue
                resp = requests.post(f"https://shilai-pos.zhiyi.cn/v1.6/data-center/order", json={"id": order_id}).json()
                if resp.get("errcode", 500) == 0:
                    order = resp['data']
                    if order.get("status", '') == 'PAID':
                        continue
                    # if int(order['payTime']) < 1732158000:
                    #     continue
                
                if update_pay_time:
                    params['order']['payTime'] = str(int(time.time()))
                resp = requests.post(
                    "https://shilai-pos.zhiyi.cn/v1.6/data-center/order/import",
                    json=params,
                    headers={'userId': params.get('order', {}).get('userInfo', {}).get('id')}
                ).json()
                merchant_id = params['order']['merchantId']
                order = mongo.ordering_service.orders.find_one({"id": order_id})
                serialNumber = order['serialNumber']
                if serialNumber > 0:
                    merchant = mongo.business_entities_db.merchants.find_one({"id": merchant_id})
                    merchant_name = merchant['basicInfo']['name']
                    record = f"{merchant_name}\t流水号：{serialNumber}\t订单号：{order_id}"
                    save_file.write(str(record )+ "\n")
                    result.append({"merchant_name": merchant_name, "serial_number": serialNumber, "order_id": order_id})
                    count += 1
                    print(count, record)
            except:
                print(second_log, traceback.format_exc())
                error_file.write(str(f"second_log={second_log}, error_msg={traceback.format_exc()}") + "\n")
    if result:
        df = pd.DataFrame(result)
        df.rename(columns={'merchant_name': "商家名称", "serial_number": "流水号", "order_id": "订单ID"}, inplace=True)
        filename = os.path.join(base_path, f"补单{current_timestamp}.xlsx")
        df.to_excel(filename, index=False, engine="openpyxl")
        print(f"补单记录文件：{filename}")


if __name__ == "__main__":
    # -- 一级搜索关键词：ERROR + nginx/1.18.0，二级搜索关键词：message_uuid + 数据中心请求信息
    # -- 一级搜索关键词：ERROR + timed out，二级搜索关键词：message_uuid + 数据中心请求信息
    # -- 一级搜索关键词：Failed to establish a new connection + 天阙回调报错，二级搜索关键词：message_uuid + 数据中心请求信息

    for first_key, second_key in [
        [("ERROR", "timed out"), ("数据中心请求信息", )],
        [("ERROR", "nginx/1.18.0"), ("数据中心请求信息", )],
        [("Failed to establish a new connection", "天阙回调报错"), ("数据中心请求信息", )],
    ]:
        handler(
            start_timestamp="2024-11-20 19:30:00",
            end_timestamp="2024-11-20 20:20:00",
            update_pay_time=False,
            first_key=first_key,
            second_key=second_key
        )

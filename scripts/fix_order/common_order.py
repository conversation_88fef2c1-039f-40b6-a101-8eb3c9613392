# -*- encoding: utf-8 -*-

'''
@Time        :   2024/12/02 17:51:12
微信、支付宝、饭票 支付回调报错补单

待做功能：流水号需从日志中寻找，手动传入，避免商家的流水号乱序
'''

import os
import time
import traceback
import glob
from datetime import timezone, datetime, timedelta

import requests
import pandas as pd
from pymongo import MongoClient
from clickhouse_driver import Client


base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")
os.makedirs(base_path, exist_ok=True)
clickhouse = Client(host='*************', port=9000, user='admin', password='fxnFtiZT', database='cloki', verify=False)
mongo = MongoClient(host="*************", port=13691, username='shilai', password='a7cfd52194d326d5465a89192809aeef')


def timestamp_to_datetime(timestamp, fmt="%Y-%m-%d %H:%M:%S") -> datetime:
    shanghai_tz = timezone(timedelta(hours=8))
    utc_time = datetime.fromtimestamp(int(timestamp), tz=timezone.utc)
    return utc_time.astimezone(shanghai_tz).strftime(fmt)


def to_timestamp(time_str):
    time_format = "%Y-%m-%d %H:%M:%S"
    time_obj = datetime.strptime(time_str, time_format)
    return str(int(time_obj.timestamp()))


def get_logs(start_timestamp=None, end_timestamp=None, api="/payment/tian_que_pay", order_ids=None):
    filename = os.path.join(base_path, f"all_log/all_log_{start_timestamp}-{end_timestamp}.csv")
    if not os.path.exists(filename):
        df = clickhouse.query_dataframe(f"""
            SELECT DISTINCT
                extract(t1.`string`, 'message_uuid:([a-f0-9]+)') AS message_uuid,
                extract(t1.`string`, 'BeforeRequest\\s=====>\\s*(.*)\\"') AS item,
                extract(t1.`string`, 'api_rule\\':\\s\\'(.*?)\\'') AS api_rule
                -- t1.`string`
            FROM samples_v3 AS t1
            LEFT JOIN (
                SELECT DISTINCT
                    extract(`string`, 'message_uuid:([a-f0-9]+)') AS error_message_uuid
                FROM samples_v3
                WHERE 
                    `timestamp_ns` >= {start_timestamp}000000000
                    AND `timestamp_ns` < {end_timestamp}000000000
                    AND `string` LIKE '%ERROR%'
            ) AS t2
            ON extract(t1.`string`, 'message_uuid:([a-f0-9]+)') = t2.error_message_uuid
            WHERE
                t1.`timestamp_ns` >= {start_timestamp}000000000
                AND t1.`timestamp_ns` < {end_timestamp}000000000
                AND t1.`string` LIKE '%BeforeRequest%'
            HAVING
                api_rule LIKE '%{api}%'
        """)
        def extract(row):
            item = row['item']
            # for pattern in ('","source_type', '\\n[pid', '\\ncard_type'):
            for pattern in ('","source_type', '\\n'):
                if pattern in item:
                    item = item[:item.find(pattern)]
            return eval(item)

        df['item'] = df.apply(extract, axis=1)
        df['api'] = df['item'].apply(lambda x: x['api'])
        df['request_args'] = df['item'].apply(lambda x: x['request_args'])
        df['order_id'] = df['request_args'].apply(lambda x: x.get("order_id", ""))
        df['transaction_id'] = df['request_args'].apply(lambda x: x.get("transaction_id", ""))
        # df = df.drop_duplicates()
        # df.to_csv(os.path.join(base_path, f"all_log/all_log_{start_timestamp}-{end_timestamp}.csv"), index=False)
    else:
        df = pd.read_csv(filename)
    if order_ids is not None:
        return df.loc[df['order_id'].isin(order_ids)]
    return df


def fix_order(order_id, api, params, start_timestamp=None, end_timestamp=None):
    """
    异步支付：微信、支付宝、饭票合并支付、券包合并支付
    同步支付：饭票支付、零钱支付、储值支付
    """
    success = False
    flag = mongo.ordering_service.orders.update_one(filter={"id": order_id}, update={"$set": {"remark": "补单勿做"}})
    if not flag.raw_result['updatedExisting']:
        print(f"[warning]: update order_db.orders error, id={order_id}")
    try:
        # 同步支付
        if 'prepay' in api:
            # 饭票支付
            if "FANPIAO_PAY" in params:
                user_id = params.get('userId')
                fanpiaos = list(mongo.fanpiao_db.fanpiaos.find(filter={'userId': user_id}))
                for fanpiao in fanpiaos:
                    need_update = False
                    for i, record in enumerate(fanpiao.get('fanpiaoPaymentRecords', [])):
                        if record.get('createTime') >= start_timestamp and record.get('createTime') < end_timestamp:
                            fanpiao.get('fanpiaoPaymentRecords').pop(i)
                            need_update = True
                    if need_update:
                        fanpiao.update({"totalUsedFee": sum([x.get('paidFee') for x in fanpiao.get('fanpiaoPaymentRecords')])})
                        print(fanpiao)
                        mongo.fanpiao_db.fanpiaos.update_one(
                            filter={'id': fanpiao.get('id')},
                            update={
                                '$set': {
                                    'totalUsedFee': fanpiao.get('totalUsedFee'),
                                    'fanpiaoPaymentRecords': fanpiao.get('fanpiaoPaymentRecords'),
                                }
                            },
                        )
                        resp = requests.post(f'{base_api}{api}', json=params, headers={"userId": params.get('userId')}).json()
                        print(order_id, resp)
                        success = True
            # 零钱支付（先恢复零钱）
            
            # 储值支付（先恢复储值）
            
        # 异步支付
        else:
            resp = requests.post(f'{base_api}{api}', json=params, headers={"userId": params.get('userId')}).json()
            print(order_id, resp)
            success = True
        return success
    except:
        print(traceback.format_exc())
        print(f'[warning]: send api error, url={api}')
        return False


def save_record(order_id, merchant_id, save_file=None):
    resp = requests.post(f"https://shilai-pos.zhiyi.cn/v1.6/data-center/order", json={"id": order_id}).json()
    order = resp['data']
    serialNumber = int(order.get('serialNumber', 0))
    if serialNumber > 0:
        merchant = mongo.business_entities_db.merchants.find_one({"id": merchant_id})
        merchant_name = merchant['basicInfo']['name']
        record = f"{merchant_name}, 流水号：{serialNumber}, 订单号：{order_id}"
        if save_file:
            save_file.write(str(record )+ "\n")
        return {"merchant_name": merchant_name, "serial_number": serialNumber, "order_id": order_id, "create_time": timestamp_to_datetime(order["createTime"])}
    print(f"补单失败，流水号为0, order_id={order_id}")


def save_order_to_excel(orders):
    df = pd.DataFrame(orders)
    df = df.iloc[:, 1:]
    df['createTime'] = df['createTime'].apply(timestamp_to_datetime)
    df['paidTime'] = df['paidTime'].apply(timestamp_to_datetime)
    merchants = list(mongo.business_entities_db.merchants.find({"id": {"$in": list(set(df['merchantId'].tolist()))}}))
    merchant_name_map = {merchant["id"]: merchant['basicInfo']['name'] for merchant in merchants}
    df['merchantName'] = df["merchantId"].map(merchant_name_map)
    df = df.sort_values(['merchantId', 'createTime'])
    df = df.loc[:, ["id", "merchantName", "createTime", "paidTime", "userId", "phone", "transactionId", "paidFee", "billFee", "totalFee", "enableDiscountFee", "marketBillFee", "maxDiscount", "status", "mealType", "settlementRate", "remark", "products"]]
    df.to_excel(os.path.join(base_path, f"订单详情{int(time.time())}.xlsx"), index=False)


def download_order_log(start_time, end_time):
    start_timestamp = to_timestamp(start_time)
    end_timestamp = to_timestamp(end_time)
    orders = list(
        mongo.ordering_service.orders.find(
            {'status': "PAYING", "createTime": {"$gte": start_timestamp, "$lte": end_timestamp}}
        )
    )
    order_ids = [o['id'] for o in orders]
    callback_df = get_logs(start_timestamp=start_timestamp, end_timestamp=end_timestamp, api="/payment/tian_que_pay")
    callback_df = callback_df.loc[callback_df['order_id'].isin(order_ids)]
    prepay_df = get_logs(start_timestamp=start_timestamp, end_timestamp=end_timestamp, api="/payment/prepay")
    prepay_df = prepay_df.loc[prepay_df['order_id'].isin(order_ids)]
    log_df = pd.concat((callback_df, prepay_df), axis=0)
    if log_df.empty:
        print('未找到相关日志')
        return
    log_df.to_csv(os.path.join(base_path, f"log/log_{start_time}-{end_time}.csv"), index=False)
    del log_df
    del prepay_df
    del callback_df


def collect_csv(path):
    csv_files = glob.glob(path)
    df = pd.DataFrame()
    for f in csv_files:
        df = pd.concat((df, pd.read_csv(f)), axis=0)
    return df.drop_duplicates()


def batch_handler(start_time="2024-5-20 15:05:00", end_time="2024-5-16 15:20:00"):
    """根据时间范围补单"""
    start_timestamp = to_timestamp(start_time)
    end_timestamp = to_timestamp(end_time)
    current_time = datetime.fromtimestamp(int(time.time()))
    current_timestamp = current_time.strftime("%Y%m%d%H%M%S")
    current_day = current_time.strftime("%Y%m%d")
    save_file = open(os.path.join(base_path, f"补单{current_timestamp}.txt"), "a", encoding='utf-8')
    error_file = open(os.path.join(base_path, f"error_{current_day}.log"), "a", encoding='utf-8')
    orders = list(
        mongo.ordering_service.orders.find(
            {'status': "PAYING", "createTime": {"$gte": start_timestamp, "$lte": end_timestamp}}
        )
    )
    order_ids = [o['id'] for o in orders]
    # save_order_to_excel(orders)
    # callback_df = get_logs(start_timestamp=start_timestamp, end_timestamp=end_timestamp, api="/payment/tian_que_pay", order_ids=order_ids)
    # prepay_df = get_logs(start_timestamp=start_timestamp, end_timestamp=end_timestamp, api="/payment/prepay", order_ids=order_ids)
    # log_df = pd.concat((callback_df, prepay_df), axis=0)
    log_df = collect_csv(os.path.join(base_path, 'log/*.csv'))

    print(f"总补单量：{len(orders)}")
    result, count = [], 0
    for order in orders:
        # if order['merchantId'] in ("98314cbe7f69418e89b03b05807ce267", ):
        #     continue
        order_id = order["id"]
        try:
            order_row = log_df.loc[log_df['order_id'] == order_id, ['order_id', 'api', 'request_args']]
            if order_row.empty:
                print(f"order_id={order_id}, 未找到支付回调日志")
                continue
            order_row = order_row.iloc[0]
            if order_row.empty:
                print(f"order_id={order_id}, 未找到支付回调日志")
                continue
            if 'prepay' in order_row["api"] and "FANPIAO_PAY" in order_row["request_args"]:
                print(f"饭票支付，order_id={order_id}")

            # success = fix_order(
            #     order_id,
            #     order_row["api"],
            #     order_row["request_args"],
            #     start_timestamp=start_timestamp,
            #     end_timestamp=end_timestamp
            # )
            # if success:
            #     record = save_record(order_id, order["merchantId"], save_file)
            #     count += 1
            #     result.append(record)
            #     print(count, record)
        except:
            print(f"补单报错：order_id={order_id}, msg={traceback.format_exc()}")
            error_file.write(str(f"order_id={order_id}, error_msg={traceback.format_exc()}") + "\n")
    if result:
        df = pd.DataFrame(result)
        df.rename(columns={'merchant_name': "商家名称", "serial_number": "流水号", "order_id": "订单ID", "create_time": "创建时间"}, inplace=True)
        filename = os.path.join(base_path, f"补单{current_timestamp}.xlsx")
        df.to_excel(filename, index=False, engine="openpyxl")
        print(f"补单记录文件：{filename}")


def one_handler(order_id, merchant_id, api, params):
    """单个订单补单"""
    status = fix_order(order_id, api, params)
    if not status:
        return
    record = save_record(order_id, merchant_id)
    if not record:
        return
    print(f"{record['merchant_name']}, 流水号：{record['serial_number']}, 订单号：{record['order_id']}, 创建时间：{record['create_time']}")
        

if __name__ == "__main__":
    base_api = "https://test.shilai.zhiyi.cn/v1.6"
    batch_handler(
        start_time="2025-03-27 11:00:00",
        end_time="2025-04-16 00:00:00"
    )

    # batch_handler(
    #     start_time="2025-03-27 11:00:00",
    #     end_time="2025-03-28 00:00:00",
    # )

    # one_handler(
    #     order_id="7cddf3be917a46999c3764cdc1d1c9e3",
    #     merchant_id="7ba3cab1fd0d49c8b608fd5217bc0d73",
    #     api="/payment/tian_que_pay/ordering/27876e2e5ccd4ff8bae9cb95d2d9156f/7cddf3be917a46999c3764cdc1d1c9e3",
    #     params={'transaction_id': '27876e2e5ccd4ff8bae9cb95d2d9156f', 'order_id': '7cddf3be917a46999c3764cdc1d1c9e3', 'payTime': '********104229', 'bizCode': '0000', 'settleAmt': '1', 'sign': 'Dhkvl+BXD0co1F9Icua/CExS6pzvgURigHA28iVfJsACnFIMvuInzmUb8XnJqaViz22ouMV4mSOJyGS7LWerHd/oIVYitvkIEm9EXwvEQBLcOHxoiF+aNvOTkpGYRuyqallvw7g4v7iRsy1zO/1ecMjb/vQgmnJ1CV3sC1tg8/xTYLEwjtEENpZ6bG8GL5fxctb5GuXGROeaNADv8stDyh5kR9f9inzer6g8i8com/DAJZ9U1kNGM7mhcxgtjJAk0ghVBd7W1ux8U3kz8kX/fiZrI1nsBCwCm/WUMV9XY4Px3WPb3Vbo8GPd8vyidqIao1DIJ2+Gxw2C375WJ8/RBg==', 'payWay': '03', 'amt': '1', 'sxfUuid': '836********397972175', 'buyerId': 'or0rK4mJDe-iPGMzRMn2wqv0S_Jo', 'payBank': 'CCB_DEBIT', 'uuid': '40a3feb6262943f5863e197539b4961f', 'discountTotalAmt': '0', 'settlementBatchNo': '00', 'ordNo': '27876e2e5ccd4ff8bae9cb95d2d9156f', 'payType': 'WECHAT', 'bizMsg': '交易成功', 'ledgerAccountEffectTime': '30', 'channelId': '*********', 'finishTime': '**************', 'pointAmount': '0', 'ledgerAccountDate': '********', 'openid': 'olKmOswYGXetXLox2NUOAw_qfiXc', 'ledgerAccountFlag': '00', 'clearDt': '********', 'transactionId': '4200002563********7660416122', 'mno': '***************', 'timeStamp': '**************', 'subMechId': '*********', 'totalOffstAmt': '1', 'drType': '1'}
    # )

    # start_date = "2025-04-13"
    # end_date = "2025-04-15"
    # # date_range = pd.date_range(start=start_date, end=end_date, freq="D")
    # date_range = pd.date_range(start=start_date, end=end_date, freq="2H")
    # for date in date_range:
    #     start_time = pd.Timestamp(date)
    #     # end_time = pd.Timestamp(date) + pd.Timedelta(days=1)      # 次日 00:00:00
    #     end_time = pd.Timestamp(date) + pd.Timedelta(hours=2)
    #     start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    #     end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    #     print(f"{start_str} - {end_str}")
    #     download_order_log(start_time=start_str, end_time=end_str)

    # df = collect_csv(os.path.join(base_path, 'log/*.csv'))
    # df = df.loc[:, ['order_id', 'transaction_id']].drop_duplicates()
    # m = df.set_index('order_id')['transaction_id'].to_dict()
    # order_df = pd.read_excel('/home/<USER>/code/补单订单11.xlsx')
    # order_df = order_df.loc[order_df['merchantName'].isin(['程江腌面（鸿翔花园店）', '壹只卤鹅', ' 捞厨好面（E城店）'])]
    # order_df['transactionId'] = order_df['id'].map(m)
    # order_df.to_excel(f"订单详情.xlsx", index=False)

    # download_order_log(
    #     start_time="2025-03-27 11:00:00",
    #     end_time="2025-03-28 00:00:00",
    # )
# -*- coding: utf-8 -*-

import os
import logging

from pymongo import MongoClient


from common.singleton import SingletonMetaThreadSafe as SingletonMetaclass
from dao import constants

logger = logging.getLogger(__name__)


class MongodbHelper(metaclass=SingletonMetaclass):

    def __init__(self):
        host = os.environ.get("MONGODB_SERVER_ADDRESS")
        port = int(os.environ.get("MONGODB_PORT"))
        username = os.environ.get("MONGODB_USER_NAME")
        password = os.environ.get("MONGODB_ROOT_PASSWORD")
        replica_set = os.environ.get("MONGODB_REPLICA_SET")
        min_pool_size = os.environ.get("MONGODB_MIN_POOL_SIZE", 32)
        max_pool_size = os.environ.get("MONGODB_MAX_POOL_SIZE", 512)
        replica_set_number = os.environ.get("MONGODB_REPLICA_SET_NUMBER", 3)
        logger.info("mongodb_config: {}, {}, {}, {}".format(host, port, username, password))
        self.mongo_client = MongoClient(
            host=host, port=port, username=username, replicaSet=replica_set,
            password=password, minPoolSize=min_pool_size, maxPoolSize=max_pool_size,
            w=replica_set_number, readPreference="secondaryPreferred")

    @property
    def _printer_config_collection(self):
        db = constants.MONGODB_CONFIG_DATABASE_NAME
        c = constants.MONGODB_PRINTER_CONFIG_COLLECTION_NAME
        return self.mongo_client[db][c]

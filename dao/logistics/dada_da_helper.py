from google.protobuf import json_format

import time
import logging

import dao.constants as constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.logistics import dada_pb2 as dada_pb

logger = logging.getLogger(__name__)

class DadaDataAccessHelper(DaoHelper):
    @property
    def _order_collection(self):
        db = constants.MONGODB_LOGISTICS_DATABASE_NAME
        collection = constants.MONGODB_DADA_ORDER_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def _order_status_collection(self):
        db = constants.MONGODB_LOGISTICS_DATABASE_NAME
        collection = constants.MONGODB_DADA_ORDER_STATUS_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_or_update_order(self, order):
        matcher = {'originId': order.origin_id}
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        self._order_collection.update(matcher, json_obj, upsert=True)

    def add_or_update_order_status(self, order_status):
        matcher = {'orderId': order_status.order_id}
        json_obj = json_format.MessageToDict(order_status, including_default_value_fields=True)
        self._order_status_collection.update(matcher, json_obj, upsert=True)

    def get_order_status(self, order_id):
        matcher = {'orderId': order_id}
        order_status = self._order_status_collection.find_one(matcher)
        if order_status:
            return json_format.ParseDict(order_status, dada_pb.OrderStatus(), ignore_unknown_fields=True)
        return None

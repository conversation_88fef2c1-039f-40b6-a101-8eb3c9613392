from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.group_dining import group_dining_pb2 as group_dining_pb

class InvitationDataAccessHelper(DaoHelper):
    """ 约饭邀请
    """
    @property
    def __collection(self):
        db = constants.MONGODB_GROUP_DINING_EVENT_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_EVENT_INVITATION_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_or_update_invitation(self, invitation):
        """ 接受邀请
        Args:
            invitation: Invitation结构体
            必有参数 dining_event_id, invitee_id

        Returns:
            None
        """
        json_obj = json_format.MessageToDict(invitation, including_default_value_fields=True)
        match = {
            "diningEventId": invitation.dining_event_id,
            "inviteeId": invitation.invitee_id
        }
        self.__collection.update(match, json_obj, upsert=True)

    def count_invitation(self, dining_id=None, invitee_id=None, state=None, monetary_state=None):
        matcher = {}
        if dining_id is not None:
            matcher.update({"diningEventId": dining_id})
        if state is not None:
            matcher.update({"state": group_dining_pb.Invitation.InvitationState.Name(state)})
        if monetary_state is not None:
            matcher.update({"monetaryState": group_dining_pb.Invitation.MonetaryState.Name(monetary_state)})
        if invitee_id is not None:
            matcher.update({"inviteeId": invitee_id})
        if matcher:
            return self.__collection.count(matcher)
        return 0


    def get_invitations(self, dining_id=None, invitee_id=None, state=None, monetary_state=None,
                        page=None, size=None, orderby=None, signin=None, accept_time=None, transaction_id=None):
        """ 查找邀请
        Args:
            dining_id: (string)饭局id
            invitee_id: (string)被邀请人id
            state: (group_dining_pb.Invitation.InvitationState)状态
            monetary_state: (group_dining_pb.Invitation.MonetaryState)钱相关的状态
            page, size: 分页用
            orderby: 排序用,格式为[(column, 1/-1)]
        Return:
            group_dining.Invitation结构数组
        """
        matcher = {}
        if dining_id is not None:
            matcher.update({"diningEventId": dining_id})
        if transaction_id is not None:
            matcher.update({'transactionId': transaction_id})
        if state is not None:
            if isinstance(state, list):
                matcher.update({
                    "state": {
                        "$in": [group_dining_pb.Invitation.InvitationState.Name(s) for s in state]
                }})
            else:
                matcher.update({"state": group_dining_pb.Invitation.InvitationState.Name(state)})
        if monetary_state is not None:
            if isinstance(monetary_state, list):
                matcher.update({
                    "monetaryState": {
                        "$in": [group_dining_pb.Invitation.MonetaryState.Name(m) for m in monetary_state]
                    }})
            else:
                matcher.update({"monetaryState": group_dining_pb.Invitation.MonetaryState.Name(monetary_state)})
        if invitee_id is not None:
            matcher.update({"inviteeId": invitee_id})
        if signin is not None:
            matcher.update({"signin": signin})
        if accept_time is not None:
            matcher.update({"acceptTime": {"$gt": str(accept_time)}})
        cursor = self.__collection.find(matcher)
        if orderby:
            cursor.sort(orderby)
        if page is not None and size is not None:
            skip = (page - 1) * size
            invitations = cursor.skip(skip).limit(size)
        else:
            invitations = cursor
        return [json_format.ParseDict(invitation, group_dining_pb.Invitation(), ignore_unknown_fields=True)
                for invitation in invitations]

    def get_invitation(self, user_id=None, dining_id=None, state=None, invitee_id=None, transaction_id=None):
        """ 查询某个用户参加的某场饭局的ID
        Args:
            user_id: (string)用户ID
            dining_id: (string)饭局ID
        Return:
            (Invitation)结构体
        """
        matcher = {}
        if user_id is not None:
            matcher.update({
                "inviteeId": user_id
            })
        if invitee_id is not None:
            matcher.update({
                "inviteeId": invitee_id
            })
        if dining_id is not None:
            matcher.update({
                "diningEventId": dining_id
            })
        if transaction_id is not None:
            matcher.update({
                'transactionId': transaction_id
            })
        if state is not None:
            if isinstance(state, list):
                matcher.update({
                    "state": {"$in": [group_dining_pb.Invitation.InvitationState.Name(s) for s in state]}
                })
            else:
                matcher.update({
                    "state": group_dining_pb.Invitation.InvitationState.Name(state)
                })
        if matcher:
            invitation = self.__collection.find_one(matcher)
            if invitation:
                return json_format.ParseDict(invitation, group_dining_pb.Invitation(), ignore_unknown_fields=True)
        return None

    def update(self, user_id=None, dining_id=None, state=None, monetary_state=None,
               transaction_id=None, accept_time=None, signin=None):
        matcher = {"diningEventId": dining_id}
        if isinstance(user_id, list):
            matcher.update({"inviteeId": {"$in": user_id}})
        elif isinstance(user_id, str):
            matcher.update({"inviteeId": user_id})

        udata = {}
        if transaction_id is not None:
            udata.update({"transactionId": transaction_id})
        if state is not None:
            udata.update({"state": group_dining_pb.Invitation.InvitationState.Name(state)})
        if monetary_state is not None:
            udata.update({"monetaryState": group_dining_pb.Invitation.MonetaryState.Name(monetary_state)})
        if accept_time is not None:
            udata.update({"acceptTime": accept_time})
        if signin is not None:
            udata.update({"signin": signin})
        if udata:
            self.__collection.update_many(matcher, {"$set": udata})

    def kick_unsignin_user(self, dining_id, director_id):
        """ 把饭局未签到的人的邀请设置为被踢掉状态
        """
        matcher = {
            "diningEventId": dining_id,
            "signin": False,
            "inviteeId": {"$ne": director_id}
        }
        udata = {
            "state": group_dining_pb.Invitation.InvitationState.Name(group_dining_pb.Invitation.KICKED)
        }
        self.__collection.update_many(matcher, {"$set": udata})

    def get_recently_accepted_group_dining(self, user_id, merchant_id, start_time=None, end_time=None,
                                           state=None, monetary_state=None):
        matcher = {'inviteeId': user_id, 'merchantId': merchant_id}
        if state is not None:
            matcher.update({'state': group_dining_pb.Invitation.InvitationState.Name(state)})
        if monetary_state is not None:
            matcher.update({'monetaryState': group_dining_pb.Invitation.MonetaryState.Name(monetary_state)})
        if start_time is not None and end_time is not None:
            matcher.update({'acceptTime': {'$gt': str(start_time), '$lt': str(end_time)}})
        invitations = self.__collection.find(matcher).sort([('acceptTime', -1)]).limit(1)
        if invitations.count() > 0:
            return json_format.ParseDict(invitations[0], group_dining_pb.Invitation(), ignore_unknown_fields=True)
        return None

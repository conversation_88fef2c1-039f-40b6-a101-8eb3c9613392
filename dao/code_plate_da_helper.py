import uuid
from google.protobuf import json_format
# from pymongo import MongoClient
from common.utils.db_utils import *

import dao.constants as constants
from dao.dao_helper import DaoHelper
import proto.code_plate_pb2 as code_plate_pb
from dao.membership_da_helper import MembershipDataAccessHelper


# Merchant Data Access Helper (商户数据访问抽象层)，提供针对商户相关的数据访问服务。
# 作用: 通过对后端数据Persistence机制的封装，实现与前端业务逻辑的解耦。
class CodePlateHelper(DaoHelper):

    @property
    def __collection(self):
        db = constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME
        collection = constants.MONGODB_CODE_PLATE_BINDING_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def get_binings(self, merchant_id=None, qrcode_id=None):
        """ 获取码牌的绑定关系
        Args:
            merchant_id: (string) 指定商家ID
            qrcode_id: (string) 指定二维码ID
        """
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchant_id": merchant_id})
        if qrcode_id is not None:
            matcher.update({"qrcode_id": qrcode_id})
        bindings = self.__collection.find(matcher)
        if bindings :
            return [json_format.ParseDict(binding, code_plate_pb.BindingRelationship(), ignore_unknown_fields=True)
                    for binding in bindings ]
        return None


    def add_binings(self, biningds):
        """添加一个码牌绑定
        """
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        db[constants.MONGODB_CODE_PLATE_BINDING_COLLECTION_NAME].insert_one(biningds)

    def update_or_create_binings(self, binings):
        """根据二维码是否已存在，来选择更新或添加。
         Args:
            binings:(dict) 绑定信息
                merchant_id: (string) 指定商家ID
                qrcode_id: (string) 指定二维码ID
        """
        qrcode_id = binings.get('qrcode_id', None)
        if qrcode_id is None:
            return
        if self.get_binings(qrcode_id=qrcode_id):
            db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
            db[constants.MONGODB_CODE_PLATE_BINDING_COLLECTION_NAME].replace_one({'qrcode_id': qrcode_id},
                binings, upsert=True)
        else:
            return

    def delete_bining(self, merchant_id=None, qrcode_id=None):
        """ 删除码牌的绑定关系
        Args:
            merchant_id: (string) 指定商家ID
            qrcode_id: (string) 指定二维码ID
        """
        if qrcode_id is None:
            return
        if merchant_id is None:
            return
        binings = {
            "merchant_id": merchant_id,
            "qrcode_id" : qrcode_id
        }
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        db[constants.MONGODB_CODE_PLATE_BINDING_COLLECTION_NAME].replace_one(binings, {'merchant_id': "","qrcode_id":qrcode_id}, upsert=True)

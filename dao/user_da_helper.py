import logging
import json
from collections import namedtuple

import pymongo
from google.protobuf import json_format

from dao.dao_helper import Da<PERSON><PERSON><PERSON><PERSON>
from cache.user_redis_helper import get_user_redis
from cache.user_redis_helper import update_user_redis
import dao.constants as constants
import proto.user_pb2 as user_pb
from common.utils import distribute_lock, id_manager
from common.protobuf_transformer import copy_obj_from_map
from service import error_codes, errors


logger = logging.getLogger(__name__)
UserItem = namedtuple('UserItem', ['idx', 'user_pb'])


# User Data Access Helper，提供针对时来平台用户管理相关的数据访问服务。
class UserDataAccessHelper(DaoHelper):
    @property
    def __user_collection(self):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        collection = db[constants.MONGODB_USER_COLLECTION_NAME]
        return collection

    @property
    def __user_platform_setting_collection(self):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        collection = db[constants.MONGODB_USER_PLATFORM_SETTING_COLLECTION_NAME]
        return collection

    @property
    def __qrcode_login_collection(self):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        collection = db[constants.MONGODB_QRLOGIN_COLLECTION_NAME]
        return collection

    @property
    def __user_config_collection(self):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        collection = db[constants.MONGODB_USER_CONFIG_NAME]
        return collection

    @get_user_redis
    def get_user(self, user_id):
        """根据ID返回指定用户信息。
        Args:
            user_id: (string) 用户ID
        Returns:
            (User) 若指定用户ID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_USER_COLLECTION_NAME].find_one({'id': user_id})
        if user:
            return json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)
        else:
            return None

    def query_user_by_ids(self, user_ids: list):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_USER_COLLECTION_NAME].find({'id': {'$in': user_ids}})
        if not users:
            return []
        return users

    def query_users(self, user_id):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_USER_COLLECTION_NAME].find({'id': user_id, 'thirdPartyUniqId': {"$exists": 1}})
        if not users:
            return
        users = users.sort([("wechatProfile.unionid", pymongo.ASCENDING), ("wechatProfile.openid", pymongo.ASCENDING)])
        return [
            UserItem(user['_id'], json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)) for user in users
        ]

    def get_user_by_condition(self, phone=None):
        matcher = {}
        if phone not in (None, ""):
            matcher.update({"memberProfile.mobilePhone": phone})
        if not matcher:
            return None
        user = self.__user_collection.find_one(matcher)
        if not user:
            return None
        user = json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)
        return user

    def get_user_by_alipay_user_id(self, alipay_user_id):
        matcher = {'alipayProfile.userId': alipay_user_id}
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_USER_COLLECTION_NAME].find_one(matcher)
        if user:
            return json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)
        return None

    def get_user_by_union_id(self, unionid):
        """根据指定的微信UnionID返回对应时来平台用户信息。

        Args:
            unionid: (string) 用户ID

        Returns:
            (User) 若指定用户UnionID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_USER_COLLECTION_NAME].find_one({"wechatProfile.unionid": unionid})
        if user:
            return json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)
        return None

    def get_user_by_openid(self, openid):
        """根据用户的openID返回时来平台用户信息。

        Args:
            openid: (string) 用户openID

        Returns:
            (User) 若指定用户openID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_USER_COLLECTION_NAME].find_one({"wechatProfile.openid": openid})
        if user:
            return json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True)
        return None

    def add_user(self, user):
        """添加一个新的时来平台用户信息。

        Args:
            user: (User) 时来平台用户信息结构体
        """
        json_obj = json_format.MessageToDict(user, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        db[constants.MONGODB_USER_COLLECTION_NAME].insert_one(json_obj)

    @update_user_redis
    def update_miniprogram_user(self, user, _id):
        json_data = json_format.MessageToDict(user, including_default_value_fields=True)
        self.__user_collection.update_one({'_id': _id}, {"$set": json_data}, upsert=True)
        return user

    # @update_user_redis
    # def update_or_create_user(self, user):
    #     """如果指定用户当前不存在，则添加一个新的时来平台用户信息。
    #        如果指定用户存在，则更新该用户信息。

    #     Args:
    #         user: (User) 时来平台用户信息结构体
    #     """
    #     matcher = {"thirdPartyUniqId": user.third_party_uniq_id}
    #     json_data = json_format.MessageToDict(user, including_default_value_fields=True)
    #     db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
    #     c = db[constants.MONGODB_USER_COLLECTION_NAME]
    #     del json_data['id']
    #     result = c.find_and_modify(matcher, {"$set": json_data, "$setOnInsert": {"id": user.id}}, upsert=True)
    #     logger.info(f"登录信息: {result}")
    #     if result:
    #         user.id = result.get("id")
    #     return user

    @update_user_redis
    def update_or_create_user(self, user):
        third_party_uniq_id = user.third_party_uniq_id
        if not third_party_uniq_id:
            logger.error(f"登录失败，该账号third_party_uniq_id={third_party_uniq_id}")
            raise errors.Error(err=error_codes.LOGIN_CODE_INVALID)

        with distribute_lock.ExpireLock(f"lock:create_user:{third_party_uniq_id}", ttl=2000) as lock:
            user_info = json_format.MessageToDict(user, including_default_value_fields=True)
            if not lock:
                user_dump = json.dumps(user_info, ensure_ascii=False)
                logger.warning(f"登录失败，该账号third_party_uniq_id={third_party_uniq_id}正在登录中, 请稍后, user_info={user_dump}")
                raise errors.Error(err=error_codes.LOGIN_REPEAT_ERROR)

            users_coll = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME][constants.MONGODB_USER_COLLECTION_NAME]
            db_user = users_coll.find_one({"thirdPartyUniqId": third_party_uniq_id})
            if db_user:
                user_info["id"] = db_user["id"]
                user_info.update({"id": db_user["id"], "joinedTime": db_user.get("joinedTime")})
                logger.info(f"登录信息（老顾客）, user_info={user_info}")
            else:
                logger.info(f"登录信息（新注册）, user_info={user_info}")
            users_coll.update_one({"id": user_info["id"]}, {"$set": user_info}, upsert=True)
            copy_obj_from_map(user, user_info, user_pb.User)
            return user

    def get_users_by_ids(self, user_ids):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_USER_COLLECTION_NAME].find({'id': {'$in': user_ids}})
        if users:
            return [json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True) for user in users]
        else:
            None

    def get_users(
        self, join_method=None, join_start_time=None, join_end_time=None, country=None, province=None, city=None, sex=None
    ):
        filter = {}
        if join_method is not None:
            filter['joinMethod'] = join_method
        if join_start_time is not None or join_end_time is not None:
            filter['joinedTime'] = {}
        if join_start_time is not None:
            filter['joinedTime']['$gt'] = str(join_start_time)
        if join_end_time is not None:
            filter['joinedTime']['$lt'] = str(join_end_time)
        if country is not None:
            filter['wechatProfile.country'] = country
        if province is not None:
            filter['wechatProfile.province'] = province
        if city is not None:
            filter['wechatProfile.city'] = city
        if sex is not None:
            filter['wechatProfile.sex'] = sex

        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_USER_COLLECTION_NAME].find(filter)
        if users:
            return [json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True) for user in users]
        else:
            return None

    @update_user_redis
    def update(
        self,
        user_id,
        head_image_url=None,
        nickname=None,
        sex=None,
        birth_year=None,
        birth_month=None,
        birth_day=None,
        personal_signature=None,
        tags=None,
        phone=None,
    ):
        matcher = {"id": user_id}
        udata = {}
        if head_image_url is not None:
            udata.update({"memberProfile.headImageUrl": head_image_url})
        if nickname is not None:
            udata.update({"memberProfile.nickname": nickname})
        if sex is not None:
            udata.update({"memberProfile.sex": sex})
        if birth_year is not None:
            udata.update({"memberProfile.birthYear": birth_year})
        if birth_month is not None:
            udata.update({"memberProfile.birthMonth": birth_month})
        if birth_day is not None:
            udata.update({"memberProfile.birthDay": birth_day})
        if personal_signature is not None:
            udata.update({"memberProfile.personalSignature": personal_signature})
        if tags is not None:
            udata.update({"memberProfile.tags": tags})
        if phone is not None:
            udata.update({"memberProfile.mobilePhone": phone})

        if udata:
            db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
            db[constants.MONGODB_USER_COLLECTION_NAME].update(matcher, {"$set": udata})

    @update_user_redis
    def remove_shipping_address(self, user_id, shipping_address_id):
        matcher = {'id': user_id}
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        db[constants.MONGODB_USER_COLLECTION_NAME].update(
            matcher, {"$pull": {'memberProfile.shippingAddress': {'id': shipping_address_id}}}
        )

    def get_users_by_condition(self, wechat_nickname=None, phone=None, alipay_nickname=None):
        matcher = {}
        if wechat_nickname is not None:
            matcher.update({"wechatProfile.nickname": {"$regex": ".*{}.*".format(wechat_nickname)}})
        # if phone is not None:
        #     matcher.update({"memberProfile.mobilePhone": phone})
        if alipay_nickname is not None:
            matcher.update({"alipayProfile.nickname": {"$regex": ".*{}.*".format(alipay_nickname)}})
        if not matcher:
            return []
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_USER_COLLECTION_NAME].find(matcher)
        if users:
            return [json_format.ParseDict(user, user_pb.User(), ignore_unknown_fields=True) for user in users]
        else:
            return []

    def add_or_update_user_platform_setting(self, setting):
        matcher = {"userId": setting.user_id}
        json_data = json_format.MessageToDict(setting, including_default_value_fields=True)
        self.__user_platform_setting_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_user_platform_setting(self, user_id=None):
        matcher = {}
        if user_id is not None:
            matcher.update({"userId": user_id})
        if not matcher:
            return None
        setting = self.__user_platform_setting_collection.find_one(matcher)
        if not setting:
            return None
        return json_format.ParseDict(setting, user_pb.UserPlatformSetting(), ignore_unknown_fields=True)

    def qrcode_login_update(self, record):
        matcher = {"id": record.id}
        json_data = json_format.MessageToDict(record, including_default_value_fields=True)
        self.__qrcode_login_collection.update_one(matcher, {'$set': json_data}, upsert=True)

    def get_qrcode_login(self, matcher):
        if not matcher:
            return
        record = self.__qrcode_login_collection.find_one(matcher)
        if not record:
            return
        return json_format.ParseDict(record, user_pb.QrcodeLoginRecord(), ignore_unknown_fields=True)

    def add_or_update_user_config(self, config):
        if isinstance(config, dict):
            config = json_format.ParseDict(config, user_pb.UserConfig(), ignore_unknown_fields=True)
        if not config.id:
            config.id = id_manager.generate_common_id()
        matcher = {"userId": config.user_id}
        json_data = json_format.MessageToDict(config, including_default_value_fields=True)
        self.__user_config_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_user_config(self, user_id):
        config = self.__user_config_collection.find_one({'userId': user_id})
        if not config:
            config = user_pb.UserConfig()
            config.user_id = user_id
        else:
            config = json_format.ParseDict(config, user_pb.UserConfig(), ignore_unknown_fields=True)
        return config

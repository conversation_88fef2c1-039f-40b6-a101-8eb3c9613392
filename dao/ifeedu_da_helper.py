# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import Da<PERSON><PERSON><PERSON><PERSON>
from proto.ifeedu import common_pb2 as ifeedu_common_pb
from proto.ifeedu import wishlist_pb2 as wishlist_pb
from proto.ifeedu import feeding_pb2 as feeding_pb

logger = logging.getLogger(__name__)

class IFeedUDataAccessHelper(DaoHelper):
    db = constants.MONGODB_IFEEDU_DATABASE_NAME
    @property
    def _config_collection(self):
        c = constants.MONGODB_IFEEDU_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _wishlist_collection(self):
        c = constants.MONGODB_IFEEDU_WISHLIST_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _feed_plan_collection(self):
        c = constants.MONGODB_IFEEDU_FEED_PLAN_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _feed_stats_collection(self):
        c = constants.MONGODB_IFEEDU_FEED_STATS_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _user_relationship_collection(self):
        c = constants.MONGODB_IFEEDU_USER_RELATIONSHIP_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _merchant_wishlist_dish_collection(self):
        c = constants.MONGODB_IFEEDU_MERCHANT_WISHLIST_DISH_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def set_config(self, config):
        matcher = {'merchantId': config.merchant_id}
        json_obj = json_format.MessageToDict(config, including_default_value_fields=True)
        self._config_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_config(self, merchant_id):
        matcher = {'merchantId': merchant_id}
        config = self._config_collection.find_one(matcher)
        if config:
            return json_format.ParseDict(config, ifeedu_common_pb.IFeedUConfig(), ignore_unknown_fields=True)
        return None

    def add_or_update_wishlist(self, wishlist):
        matcher = {
            "id": wishlist.id
        }
        json_obj = json_format.MessageToDict(wishlist, including_default_value_fields=True)
        self._wishlist_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_wishlist(self, user_id=None, merchant_id=None):
        matcher = {"userId": user_id, "merchantId": merchant_id}
        if not matcher:
            return None
        wishlists = self._wishlist_collection.find(matcher)
        if wishlists:
            return [json_format.ParseDict(wishlist, wishlist_pb.WishList(), ignore_unknown_fields=True) for wishlist in wishlists]
        return None

    def get_wishlist_by_id(self, wishlist_id=None):
        matcher = {}
        if wishlist_id is not None:
            matcher = {'id': wishlist_id}
        if matcher:
            wishlist = self._wishlist_collection.find_one(matcher)
            if wishlist:
                return json_format.ParseDict(wishlist, wishlist_pb.WishList(), ignore_unknown_fields=True)
        return None

    def add_or_update_feed_plan(self, feed_plan):
        matcher = {"id": feed_plan.id}
        json_obj = json_format.MessageToDict(feed_plan, including_default_value_fields=True)
        self._feed_plan_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_feed_plans(self, wishlist_id=None, feeder_id=None, status=None):
        matcher = {}
        if wishlist_id is not None:
            matcher.update({"wishListId": wishlist_id})
        if feeder_id is not None:
            matcher.update({'feederId': feeder_id})
        if status is not None:
            matcher.update({'status': feeding_pb.FeedPlan.Status.Name(status)})

        if not matcher:
            return []
        feed_plans = self._feed_plan_collection.find(matcher)
        if feed_plans:
            return [json_format.ParseDict(feed_plan, feeding_pb.FeedPlan(), ignore_unknown_fields=True) for feed_plan in feed_plans]
        return []

    def get_feed_plan(self, id=None, wishlist_id=None, transaction_id=None, status=None):
        matcher = {}
        if id is not None:
            matcher = {"id": id}
        if wishlist_id is not None:
            matcher = {"wishListId": wishlist_id}
        if transaction_id is not None:
            matcher = {"transactionId": transaction_id}
        if status is not None:
            matcher.update({'status': feeding_pb.FeedPlan.Status.Name(status)})

        if not matcher:
            return None
        feed_plan = self._feed_plan_collection.find_one(matcher)
        if feed_plan:
            return json_format.ParseDict(feed_plan, feeding_pb.FeedPlan(), ignore_unknown_fields=True)
        return None

    def add_or_update_feed_stats(self, feed_stats):
        matcher = {
            "wishListId": feed_stats.wish_list_id,
            "feederId": feed_stats.feeder_id
        }
        json_obj = json_format.MessageToDict(feed_stats, including_default_value_fields=True)
        self._feed_stats_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_feed_stats(self, wish_list_id=None, feeder_id=None, merchant_id=None):
        matcher = {}
        if wish_list_id is not None and feeder_id is not None:
            matcher.update({"wishListId": wish_list_id, "feederId": feeder_id})
        if not matcher:
            return None
        feed_stats = self._feed_stats_collection.find_one(matcher)
        if feed_stats:
            return json_format.ParseDict(feed_stats, feeding_pb.FeedStats(), ignore_unknown_fields=True)
        return None

    def get_feed_statses(self, wish_list_id=None, initiator_id=None, feeder_id=None):
        matcher = {}
        if wish_list_id is not None:
            matcher = {"wishListId": wish_list_id}
        if initiator_id is not None:
            matcher.update({"initiatorId": initiator_id})
        if feeder_id is not None:
            matcher.update({'feederId': feeder_id})
        if not matcher:
            return None
        feed_statses = self._feed_stats_collection.find(matcher)
        if feed_statses:
            return [json_format.ParseDict(feed_stats, feeding_pb.FeedStats(), ignore_unknown_fields=True) for feed_stats in feed_statses]
        return None

    def add_user_relationship(self, user_relationship):
        matcher = {
            'userId': user_relationship.user_id,
            'rootId': user_relationship.root_id,
            'inviterId': user_relationship.inviter_id,
            'merchantId': user_relationship.merchant_id
        }
        json_data = json_format.MessageToDict(user_relationship, including_default_value_fields=True)
        self._user_relationship_collection.update_one(matcher, {'$set': json_data}, upsert=True)

    def get_user_relationships(self, root_id=None, user_id=None, inviter_id=None, merchant_id=None):
        matcher = {}
        if root_id is not None:
            matcher.update({'rootId': root_id})
        if user_id is not None:
            matcher.update({'userId': user_id})
        if inviter_id is not None:
            matcher.update({'inviterId': inviter_id})
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if not matcher:
            return None
        relationships = self._user_relationship_collection.find(matcher)
        if relationships:
            return [json_format.ParseDict(relationship, feeding_pb.UserRelationship(), ignore_unknown_fields=True) for relationship in relationships]
        return None

    def add_or_update_merchant_wishlist_dish(self, dish_id, merchant_id, user_id, used_copies=None, copies=None):
        matcher = {
            "dishId": dish_id,
            "merchantId": merchant_id,
            "userId": user_id
        }
        udata = {}
        if copies is not None:
            udata.update({"totalCopies": copies})
        if used_copies is not None:
            udata.update({"usedCopies": used_copies})
        if udata:
            self._merchant_wishlist_dish_collection.update_one(matcher, {'$inc': udata}, upsert=True)

    def get_merchant_wishlist_dish(self, user_id=None, merchant_id=None, dish_id=None):
        matcher = {}

        if user_id is not None:
            matcher.update({"userId": user_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if dish_id is not None:
            matcher.update({"dishId": dish_id})

        if not matcher:
            return None
        merchant_wishlist_dish = self._merchant_wishlist_dish_collection.find_one(matcher)
        if merchant_wishlist_dish:
            return json_format.ParseDict(merchant_wishlist_dish, wishlist_pb.MerchantWishlistDish(), ignore_unknown_fields=True)
        return None

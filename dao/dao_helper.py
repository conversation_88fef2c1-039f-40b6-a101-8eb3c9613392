# -*- coding: utf-8 -*-

import os
import json
import logging
from typing import List, <PERSON><PERSON>

from pymongo import MongoClient
from google.protobuf import json_format

from service import errors, error_codes
from common.schema import to_camel, AutoSnake, AutoCamel


logger = logging.getLogger(__name__)


class MongoDBHelper:
    client = None

    def __new__(cls):
        if not MongoDBHelper.client:
            host = os.environ.get("MONGODB_SERVER_ADDRESS")
            port = int(os.environ.get("MONGODB_PORT"))
            username = os.environ.get("MONGODB_USER_NAME")
            password = os.environ.get("MONGODB_ROOT_PASSWORD")
            min_pool_size = os.environ.get("MONGODB_MIN_POOL_SIZE", 10)
            max_pool_size = os.environ.get("MONGODB_MAX_POOL_SIZE", 1024)

            replica_args = {}
            replica_set = os.environ.get("MONGODB_REPLICA_SET")
            if replica_set:
                replica_args.update(
                    {
                        "replicaSet": replica_set,
                        "readPreference": 'secondaryPreferred',
                        "w": os.environ.get("MONGODB_REPLICA_SET_NUMBER", 3),
                    }
                )
            MongoDBHelper.client = MongoClient(
                host=host,
                port=port,
                username=username,
                password=password,
                minPoolSize=min_pool_size,
                maxPoolSize=max_pool_size,
                connect=False,
                **replica_args
            )


MongoDBHelper()


class DaoHelper:
    def __init__(self):
        self.mongo_client = MongoDBHelper.client
        # 如果是查询多文档,但是没有限制返回的文档数,那么统一使用此限制
        self._maximum_documents = 10000

    def limit_documents(self, cursor, page=None, size=None):
        skip = None
        limit = size
        if size is None:
            limit = self._maximum_documents
        if page is not None and size is not None:
            skip = (page - 1) * size
        if skip is not None:
            return cursor.skip(skip).limit(limit)
        else:
            return cursor.limit(limit)

    def parse_documents(self, cursor, cls, page=None, size=None, return_proto=True):
        cursor = self.limit_documents(cursor, page=page, size=size)
        ret = []
        for data in cursor:
            ret.append(self.parse_document(data, cls) if return_proto else data)
        return ret

    def parse_document(self, data, cls):
        if data is None:
            return None
        return json_format.ParseDict(data, cls(), ignore_unknown_fields=True)

    def to_dict(self, data):
        if data is None:
            return None
        data = json_format.MessageToDict(data, including_default_value_fields=True)
        return data

    def query_one(self, collection, matcher, project={}, sort=[], **kwargs):
        if not matcher:
            return {}
        project.update({'_id': False})
        return collection.find_one(matcher, projection=project, sort=sort, **kwargs)

    def query_list(self, collection, matcher, project={}, page=1, page_size=0, sort=[], **kwargs):
        if not matcher:
            return []
        project.update({'_id': False})
        return collection.find(matcher, projection=project, sort=sort, **kwargs).skip(page_size * (page - 1)).limit(page_size)


class DaoORMHelper(object):
    def __init__(self, db: str, collection: str, pb=None):
        self._collection = MongoDBHelper.client[db][collection]
        self._pb_cls = pb

    @staticmethod
    def to_field_names(pb, filter_names: list = None):
        result = []
        for field in pb.DESCRIPTOR.fields:
            name = to_camel(field.name)
            if filter_names is not None and name in filter_names:
                continue
            result.append(name)
        return result

    def to_dict(self, pb, output_names: list = None, hidden_names: list = None, including_default=True, pb_cls=None):
        pb_cls = pb_cls or self._pb_cls
        if pb_cls is None:
            return pb
        data_map = json_format.MessageToDict(pb, including_default_value_fields=including_default)
        if hidden_names is not None:
            for h in hidden_names:
                if h in data_map:
                    del data_map[h]
        if output_names is None:
            return data_map
        return {name: data_map[name] for name in output_names if name in data_map}

    def to_json(self, pb):
        return json.dumps(self.to_dict(pb), indent=4, ensure_ascii=False)

    def to_pb(self, data: dict, ignore_unknown=True, pb_cls=None):
        pb_cls = pb_cls or self._pb_cls
        if pb_cls is None:
            return data
        try:
            return json_format.ParseDict(data, pb_cls(), ignore_unknown_fields=ignore_unknown)
        except json_format.Error:
            raise errors.Error(err=error_codes.DB_PARSE_DATA_ERROR, data=data)

    def to_camel(self, names: list):
        for idx in range(len(names)):
            names[idx] = to_camel(names[idx])
        return names

    def _to_resp(self, resp, resp_dict, pb_callback, ignore_unknown):
        if resp_dict:
            return AutoSnake(resp)
        pb = self.to_pb(resp, ignore_unknown=ignore_unknown)
        return pb if pb_callback is None else pb_callback(pb)

    def add_or_update(
        self,
        data: dict,
        matcher: dict = {},
        projection: dict = {},
        resp_dict=True,
        ignore_unknown=True,
        pb_cls=None,
        pb_callback=None,
        pb_check=True,
        **kwargs
    ):
        if not isinstance(data, dict):
            data = self.to_dict(data)
        id = data.get("id")
        if id is not None:
            matcher = {"id": id}
        if pb_check:
            pb = self.to_pb(data, pb_cls=pb_cls)
            data = self.to_dict(pb if pb_callback is None else pb_callback(pb), output_names=self.to_camel(list(data.keys())))
        resp = self._collection.update(AutoCamel(matcher), {"$set": AutoCamel(data)}, upsert=True)
        if not resp['n']:
            raise errors.Error(err=error_codes.DB_ADD_OR_UPDATE_ERROR)
        return self.get(
            id=id, matcher=matcher, resp_dict=resp_dict, projection=projection, ignore_unknown=ignore_unknown, **kwargs
        )

    def get(
        self,
        id: str = None,
        matcher: dict = {},
        projection: dict = {},
        resp_dict=True,
        ignore_unknown=True,
        pb_callback=None,
        **kwargs
    ):
        if id is not None:
            matcher["id"] = id
        resp = self._collection.find_one(AutoCamel(matcher), projection={**projection, "_id": 0}, **kwargs) or AutoSnake({})
        return self._to_resp(resp, resp_dict, pb_callback, ignore_unknown)

    def query(
        self,
        matcher: dict = {},
        page: int = 1,
        page_size: int = 0,
        sort: List[Tuple[str, int]] = [],
        projection: dict = {},
        resp_dict=True,
        ignore_unknown=True,
        pb_callback=None,
        **kwargs
    ):
        resp = self._collection.find(AutoCamel(matcher), projection={**projection, "_id": 0}, sort=sort, **kwargs)
        resp = resp.skip(page_size * (page - 1)).limit(page_size) or []
        return AutoSnake([self._to_resp(row, resp_dict, pb_callback, ignore_unknown) for row in resp])

    def query_generator(
        self,
        matcher: dict = {},
        batch_size=100,
        sort: List[Tuple[str, int]] = [],
        projection: dict = {},
        resp_dict=True,
        ignore_unknown=True,
        **kwargs
    ):
        cursor = self._collection.find(AutoCamel(matcher), projection={**projection, "_id": 0}, sort=sort, **kwargs).batch_size(
            batch_size
        )
        for doc in cursor:
            if not resp_dict:
                doc = self.to_pb(doc, ignore_unknown=ignore_unknown)
            yield AutoSnake(doc)

    def delete(self, matcher: dict):
        resp = self._collection.delete_many(
            AutoCamel(matcher),
        )
        return bool(resp.raw_result['ok'])

    def aggregate(
        self,
        pipeline: List[dict] = [],
        page: int = 1,
        page_size: int = 0,
    ) -> List:
        if page > 1:
            pipeline.append({'$skip': page_size * (page - 1)})
        if page_size > 0:
            pipeline.append({'$limit': page_size})
        return list(self._collection.aggregate(pipeline, allowDiskUse=True))

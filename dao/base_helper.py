import os
import uuid
from collections import namedtuple
from urllib import parse

from flask import Response
import qrcode
import time

import proto.qrcode_pb2 as qrcode_pb
from common import constants
from common.config import config
from common.constant import const
from common.utils import file_access_helper
from common.utils import id_manager
from common.utils import zip_helper
from common.utils.db_utils import DbUtils
from common.aliyun_oss_helper import AliyunOSSHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from wechat_lib import miniprogram_code_api_helper


class BaseHelper():
    """
        基础服务
    """
    def __init__(self):
        self.db_utils = DbUtils()
        # self.from_platform = from_platform

    def create_qrcode(self, merchant_id="null", staff_id=None, platform=None):
        """
            # 创建小程序二维码
              HTTP请求方式:
            POST https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN

            请求参数：
                access_token	string	是	接口调用凭证
                scene	        string	是	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）

            返回：
                如果调用成功，会直接返回图片二进制内容，如果请求失败，会返回 JSON 格式的数据。
                  errcode	错误码，0为正常。
                  errmsg	错误信息。

            :param merchant_id: 商户id
            :param staff_id: 推广员id
            :param platform: 调用的平台
            :return: 返回
         """
        # merchant_id = request_json['merchantId']
        # user_id = request_json['userId']

        # 获取 access_token
        if platform == config.PLATFORM_MERCHANT:
            appid = config.WECHAT_MINIPROGRAM_MERCHANT_APPID
        else:
            appid = config.WECHAT_MINIPROGRAM_STAFF_APPID

        qrcode_id = str(uuid.uuid4()).replace("-", "")
        qrcode = qrcode_pb.StaffQrcode()
        qrcode.id = qrcode_id
        qrcode.base_info.staff_id = staff_id
        QrcodeDataAccessHelper().add_staff_qrcode(qrcode)

        qrcode_file = os.path.join(const.MERCHANT_ID_QRCODE_SAVE_DIR, "{}.png".format(qrcode_id))

        code_content = miniprogram_code_api_helper.create_unlimited_code(
            appid, scene=qrcode_id, page='pages/staff/invitation')

        # 保存 二维码
        file_access_helper.write_file(qrcode_file, code_content)
        domain = os.environ.get("STAFF_ASSIST_SERVICE_DOMAIN", "https://shilai.zhiyi.cn")
        return f'{domain}/merchant/qrcode/{qrcode_id}'

    def create_merchant_qrcode(self, content):
        """
            # 创建二维码
              HTTP请求方式:
            POST https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN

            请求参数：
                access_token	string	是	接口调用凭证
                scene	        string	是	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）

            返回：
                如果调用成功，会直接返回图片二进制内容，如果请求失败，会返回 JSON 格式的数据。
                  errcode	错误码，0为正常。
                  errmsg	错误信息。

            :param merchant_id: 商户id
            :param staff_id: 推广员id
            :param platform: 调用的平台
            :return: 返回
         """
        qrcode_id = id_manager.generate_qrcode_id(content=content)
        qrcode_file = os.path.join(const.MERCHANT_ID_QRCODE_SAVE_DIR, "{}.png".format(qrcode_id))
        if file_access_helper.is_path_exists(qrcode_file):
            return qrcode_id

        img = qrcode.make(content)
        file_access_helper.ensure_directory_exists_for_filename(qrcode_file)
        img.save(qrcode_file)
        return qrcode_id

    def create_merchant_home_wxacode_file(self, merchant_id, index, store_id):
        """创建时来饭票商户主页小程序码

        Args:
            merchant_id: (string) 商户ID
            store_id: (string) 门店ID
            index: (int) 索引

        Return:
            (string) 文件路径
        """
        qrcode = qrcode_pb.MerchantHomeCode()
        qrcode.id = id_manager.generate_qrcode_id()
        qrcode.merchant_id = merchant_id
        if store_id:
            qrcode.store_id = store_id
        qrcode.index = index
        QrcodeDataAccessHelper().add_merchant_home_wxacode(qrcode)

        qrcode_filename = '{}/{}.png'.format(store_id, index)
        qrcode_file = os.path.join(self.get_merchant_wxacode_dirpath(merchant_id),
                                   qrcode_filename)
        page = 'pages/merchant/index'
        if merchant_id == '6e138efe714b4714befaba9c17085928':
            page = 'pages/merchant/stores'
        code_content = miniprogram_code_api_helper.create_unlimited_code(config.WECHAT_MINIPROGRAM_APPID,
                                                                         scene=qrcode.id,
                                                                         page=page,
                                                                         is_hyaline=True)
        file_access_helper.write_file(qrcode_file, code_content)

        return qrcode_file

    def create_merchant_home_wxacodes(self, merchant_id, start_index, total, store_id=None):
        """批量生成时来饭票商户主页小程序码文件

        Args:
            merchant_id: (string) 商户ID
            store_id: (string) 指定的门店ID，如果不传，则指定所有门店
            start_index: (int) 开始的文件索引
            total: (int) 生成的文件总数

        Return:
            (string) 文件夹路径
        """
        if store_id:
            store_ids = [store_id]
        else:
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
            store_ids = [store.id for store in merchant.stores]

        for store_id in store_ids:
            for index in range(start_index, start_index + total):
                self.create_merchant_home_wxacode_file(merchant_id=merchant_id,
                                                       store_id=store_id,
                                                       index=index)
        file_dirpath = self.get_merchant_wxacode_dirpath(merchant_id)
        output_path = os.path.join(file_dirpath, 'compressed.zip')
        zip_helper.compress(output_path, file_dirpath)

        return output_path

    def get_merchant_wxacode_dirpath(self, merchant_id):
        """
            获取用户端小程序码文件夹路径，根据商户ID区分
        """
        dirpath = os.path.join(const.MERCHANT_CODE_DIR, merchant_id)
        file_access_helper.ensure_directory_exists(dirpath)
        return dirpath

    def get_merchant_barcode(self, qrcode_id):
        """
            获取生成的二维码图片
            :param qrcode_id: 二维码图片的id
            :return: 返回图片流
        """
        qrcode_file = os.path.join(const.MERCHANT_ID_QRCODE_SAVE_DIR, "{}.png".format(qrcode_id))
        print("qrcode_file : {}".format(qrcode_file))
        with open(qrcode_file, 'rb') as f:
            image = f.read()

        resp = Response(image, mimetype="image/png")
        return resp

    def get_merchant_qrcode_id_info(self, qrcode_id):
        """
            获取二维码图片的id对应的
            :param qrcode_id: 二维码图片的id
            :return: {staffId, merchantId}
        """
        return self.db_utils.get_qrcode_info(qrcode_id)

    def generate_group_dining_event_wxcode_with_table(self, merchant_id, director_id, people_count,
                                                      payment_rule, table_id):
        """ 创建饭局相关的二维码
        Args:
            merchant_id: 商户ID
            table_id: 桌号id
            initiator_id: 创建者id
        """
        _qrcode = namedtuple('Qrcode', ['scene_id', 'group_dining_event_id', 'qrcode_url'])
        scene_id = id_manager.generate_common_id()
        group_dining_event_id = id_manager.generate_group_dining_id()
        code_content = None
        # 判断之前是否已经生成过该饭局二维码
        qrcode_filename = '{}.png'.format(group_dining_event_id)
        qrcode_file = os.path.join(const.GROUP_DINING_CODE_DIR, merchant_id, qrcode_filename)

        qrcode = qrcode_pb.OrderingQrcode()
        qrcode.id = scene_id
        qrcode.table_id = table_id
        qrcode.merchant_id = merchant_id

        group_dining_qrcode = qrcode_pb.OrderingGroupDiningEventQrcode()
        group_dining_qrcode.director_id = director_id
        group_dining_qrcode.group_dining_event_id = group_dining_event_id
        group_dining_qrcode.people_count = people_count
        group_dining_qrcode.payment_rule = payment_rule

        qrcode.group_dining_event.CopyFrom(group_dining_qrcode)

        QrcodeDataAccessHelper().add_ordering_wxacode(qrcode)
        code_content = miniprogram_code_api_helper.create_unlimited_code(
            config.WECHAT_MINIPROGRAM_APPID, scene=scene_id, page='package-merchant/menu', is_hyaline=True)
        file_access_helper.write_file(qrcode_file, code_content)

        domain = config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME)
        qrcode_url = "{}/{}".format(domain, 'group_dining/qrcode/{}'.format(group_dining_event_id))
        return _qrcode(scene_id=scene_id, group_dining_event_id=group_dining_event_id, qrcode_url=qrcode_url)

    def generate_group_dining_event_wxcode(self, merchant_id, store_id, group_event_id, user_id):
        """创建时来饭票饭局邀请主页小程序码

        Args:
            merchant_id: (string) 商户ID
            store_id: (string) 门店ID
            group_dining_id: (string) 饭局ID

        Return:
            (string) 文件路径
        """
        code_content = None
        # 判断之前是否已经生成过该饭局二维码
        qrcode_filename = '{}.png'.format(group_event_id)
        qrcode_file = os.path.join(const.GROUP_DINING_CODE_DIR, merchant_id, store_id, qrcode_filename)

        if file_access_helper.is_path_exists(qrcode_file):
            code_content = file_access_helper.read_file(qrcode_file)
        else:
            qrcode = qrcode_pb.GroupDiningEventQrcode()
            qrcode.id = id_manager.generate_qrcode_id()
            qrcode.inviter_id = user_id
            qrcode.group_dining_id = group_event_id
            QrcodeDataAccessHelper().add_group_dining_event_wxacode(qrcode)

            code_content = miniprogram_code_api_helper.create_unlimited_code(config.WECHAT_MINIPROGRAM_APPID,
                                                                             scene=qrcode.id,
                                                                             page='package-merchant/group-dining',
                                                                             is_hyaline=True)
            file_access_helper.write_file(qrcode_file, code_content)

        qr_code_url = parse.urljoin(config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), 'group_dining/qrcode/{}'.format(group_event_id))
        return qr_code_url

    def get_group_dining_event_wxcode(self, group_event_id):
        """
            获取生成的二维码图片
            :param qrcode_id: 二维码图片的id
            :return: 返回图片流
        """
        qrcode_filename = '{}.png'.format(group_event_id)
        group_dining_qrcode_info = QrcodeDataAccessHelper().get_ordering_wxacode(
            group_dining_event_id=group_event_id)
        merchant_id = group_dining_qrcode_info.merchant_id

        qrcode_file = os.path.join(
            const.GROUP_DINING_CODE_DIR, merchant_id, qrcode_filename)
        with open(qrcode_file, 'rb') as f:
            image = f.read()

        resp = Response(image, mimetype="image/png")
        return resp

    def create_merchant_table_wxcode(self, merchant_id, table_id, table_name):
        code_content = None
        qrcode_filename = '{}.png'.format(table_name)
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        basic_info = merchant.basic_info

        dir_name = merchant_id
        if basic_info.name:
            dir_name = basic_info.name
        elif basic_info.display_name:
            dir_name = basic_info.display_name
        elif len(merchant.stores) > 0:
            dir_name = merchant.stores[0].name

        qrcode_file = os.path.join(const.TABLE_CODE_DIR, "{}/微信".format(dir_name), qrcode_filename)
        if file_access_helper.is_path_exists(qrcode_file):
            pass
        else:
            code_content = miniprogram_code_api_helper.create_unlimited_code(
                config.WECHAT_MINIPROGRAM_APPID, scene=table_id, page='package-merchant/menu', is_hyaline=True)
            file_access_helper.write_file(qrcode_file, code_content)
        domain = config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME)
        qr_code_url = parse.urljoin(domain, 'merchant/dish_catalog/scene/{}'.format(table_id))
        return qr_code_url

    def create_merchant_assist_login_qrcode(self, merchant_id, staff_id):
        qrcode_da = QrcodeDataAccessHelper()
        now = time.time()
        scene = qrcode_da.get_merchant_assist_login_scene(merchant_id=merchant_id, staff_id=staff_id)
        if scene and scene.expire_at > now:
            return scene.image_url
        scene_id = id_manager.generate_common_id()
        scene = qrcode_pb.MerchantAssistLoginQrcode()
        scene.id = scene_id
        scene.staff_id = staff_id
        scene.merchant_id = merchant_id
        scene.expire_at = int(time.time() + 24 * 60 * 60)
        code_content = miniprogram_code_api_helper.create_unlimited_code(
            config.WECHAT_MINIPROGRAM_MERCHANT_APPID, scene=scene_id, page='pages/login/login', is_hyaline=True)
        aliyun_oss_helper = AliyunOSSHelper()
        image = aliyun_oss_helper.upload_image_binary(code_content, "{}.png".format(scene_id))
        scene.image_url = image.url
        qrcode_da.add_or_update_merchant_assist_login_scene(scene)
        return image.url

    def create_merchant_self_dining_wxcode(self, merchant_id):
        code_content = None

        qrcode_filename = '{}.png'.format(merchant_id)
        qrcode_file = os.path.join(const.SELF_DINING_MERCHNT_CODE_DIR,
                                   merchant_id, qrcode_filename)

        if file_access_helper.is_path_exists(qrcode_file):
            pass
        else:
            qrcode = qrcode_pb.SelfDiningDiscountQrcode()
            qrcode.id = id_manager.generate_qrcode_id()
            qrcode.merchant_id = merchant_id
            QrcodeDataAccessHelper().add_self_dinig_discount_qrcode(qrcode)

            code_content = miniprogram_code_api_helper.create_unlimited_code(config.WECHAT_MINIPROGRAM_APPID,
                                                                             scene=merchant_id,
                                                                             page='package-merchant/bill/bill',
                                                                             is_hyaline=True)
            file_access_helper.write_file(qrcode_file, code_content)

        qr_code_url = parse.urljoin(config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME),
                                    'merchant/self_dining_payment/scene/{}'.format(qrcode.id))
        return qr_code_url

    def create_merchant_open_stage_qrcode(self, merchant_id):
        code_content = miniprogram_code_api_helper.create_unlimited_code(
            config.WECHAT_MINIPROGRAM_APPID, scene=merchant_id, page='package-merchant/open-stage/open-stage', is_hyaline=True)
        aliyun_oss_helper = AliyunOSSHelper()
        image = aliyun_oss_helper.upload_image_binary(code_content, "merchant_open_stage_qrcode_{}.png".format(merchant_id))
        return image.url

    def create_merchant_buy_fanpiao_qrcode(self, merchant_id):
        code_content = miniprogram_code_api_helper.create_unlimited_code(
            config.WECHAT_MINIPROGRAM_APPID, scene=merchant_id, page="package-payment/buy-fanpiao/buy-fanpiao", is_hyaline=True)
        aliyun_oss_helper = AliyunOSSHelper()
        image = aliyun_oss_helper.upload_image_binary(code_content, "merchant_buy_fanpiao_qrcode_{}.png".format(merchant_id))
        return image.url

    def create_wechat_qrcode_by_page(self, page, **kargs):
        qrcode = qrcode_pb.Qrcode()
        qrcode.id = id_manager.generate_common_id()
        merchant_id = kargs.get("merchant_id", None)
        if merchant_id is not None:
            qrcode.base.merchant_id = merchant_id
        store_id = kargs.get("store_id", None)
        if store_id is not None:
            qrcode.base.store_id = store_id
        scene = kargs.get("scene", None)
        if scene is not None:
            scene = qrcode_pb.QrcodeBase.Scene.Value(scene)
            qrcode.base.scene = scene
        function = kargs.get("function", None)
        if function is not None:
            function = qrcode_pb.QrcodeBase.QrcodeFunction.Value(function)
            qrcode.base.function = function
        if page is not None:
            qrcode.page = page
        name = kargs.get("name")
        if name is not None:
            qrcode.base.name = name
        qrcode_da = QrcodeDataAccessHelper()
        qrcode_da.add_or_update_qrcode(qrcode)
        return qrcode

    def create_user_share_scene_code(self, merchant_id, user_id):
        """ 生成用户分享用的场景值
        """
        pass
    
    def create_code_plate_qrcode(self, qrcode_id, content):
        """
            # 创建二维码
              HTTP请求方式:
            POST https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN

            请求参数：
                access_token	string	是	接口调用凭证
                scene	        string	是	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）

            返回：
                如果调用成功，会直接返回图片二进制内容，如果请求失败，会返回 JSON 格式的数据。
                  errcode	错误码，0为正常。
                  errmsg	错误信息。

            :param merchant_id: 商户id
            :param staff_id: 推广员id
            :param platform: 调用的平台
            :return: 返回
         """
        qrcode_file = os.path.join(const.CODE_PLATE_QRCODE_SAVE_DIR, "{}.png".format(qrcode_id))
        if file_access_helper.is_path_exists(qrcode_file):
            return qrcode_id

        img = qrcode.make(content)
        file_access_helper.ensure_directory_exists_for_filename(qrcode_file)
        img.save(qrcode_file)
        return qrcode_id

    def create_publicity_qrcode(self, qrcode_id, content, scene):
        qrcode_file = os.path.join(const.PUBLICITY_QRCODE_SAVE_DIR+"_"+scene, "{}.png".format(qrcode_id))
        if file_access_helper.is_path_exists(qrcode_file):
            return qrcode_id
        img = qrcode.make(content)
        file_access_helper.ensure_directory_exists_for_filename(qrcode_file)
        img.save(qrcode_file)
        return qrcode_id

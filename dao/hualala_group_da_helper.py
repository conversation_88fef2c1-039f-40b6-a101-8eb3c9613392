# -*- coding: utf-8 -*-

from google.protobuf import json_format

import proto.ordering.hualala.group_pb2 as group_pb
from dao.dao_helper import <PERSON><PERSON>H<PERSON><PERSON>
from dao import constants


class HualalaGroupDataAccessHelper(DaoHelper):
    db = constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME

    @property
    def _hualala_group_collection(self):
        c = constants.MONGODB_HUALALA_GROUP_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _hualala_store_collection(self):
        c = constants.MONGODB_HUALALA_STORE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_group(self, group):
        matcher = {"groupId": group.group_id}
        json_obj = json_format.MessageToDict(group, including_default_value_fields=True)
        self._hualala_group_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_group(self, group_id=None):
        matcher = {}
        if group_id is not None:
            matcher.update({"groupId": group_id})
        if not matcher:
            return None
        group = self._hualala_group_collection.find_one(matcher)
        if not group:
            return None
        return json_format.ParseDict(group, group_pb.Group(), ignore_unknown_fields=True)

    def add_or_update_store(self, store):
        matcher = {"shopId": store.shop_id}
        json_obj = json_format.MessageToDict(store, including_default_value_fields=True)
        self._hualala_store_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_store(self, store_id=None, merchant_id=None):
        matcher = {}
        if store_id is not None:
            matcher.update({"storeId": store_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        store = self._hualala_store_collection.find_one(matcher)
        if not store:
            return None
        return json_format.ParseDict(store, group_pb.Store(), ignore_unknown_fields=True)

    def get_stores(self, group_id=None):
        matcher = {}
        if group_id is not None:
            matcher.update({"groupId": group_id})
        if not matcher:
            return None
        stores = self._hualala_store_collection.find(matcher)
        if not stores:
            return None
        return [json_format.ParseDict(store, group_pb.Store(), ignore_unknown_fields=True)
                for store in stores]

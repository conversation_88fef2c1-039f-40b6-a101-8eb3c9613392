# -*- coding: utf-8 -*-

import logging
import time

from google.protobuf import json_format

import proto.finance.fanpiao_pb2 as fanpiao_pb
from dao.dao_helper import Da<PERSON><PERSON>elper
from cache.fanpiao_redis_helper import fanpiao_count_decorator
from dao import constants

logger = logging.getLogger(__name__)


class FanpiaoDataAccessHelper(DaoHelper):
    db = constants.MONGODB_FANPIAO_DATABASE_NAME

    @property
    def _fanpiao_scan_qrcode_collection(self):
        c = constants.MONGODB_FANPIAO_SCAN_QRCODE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _fanpiao_category_collection(self):
        c = constants.MONGODB_FANPIAO_CATEGORY_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _fanpiao_risk_control_config_collection(self):
        c = constants.MONGODB_FANPIAO_RISK_CONTROL_CONFIG_NAME
        return self.mongo_client[self.db][c]

    @property
    def _fanpiao_collection(self):
        c = constants.MONGODB_FANPIAO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _fanpiao_log_collection(self):
        c = constants.MONGODB_FANPIAO_RECORD_LOGS
        return self.mongo_client[self.db][c]

    def add_or_update_fanpiao_category(self, fanpiao_category):
        matcher = {"id": fanpiao_category.id}
        json_data = json_format.MessageToDict(fanpiao_category, including_default_value_fields=True)
        self._fanpiao_category_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_fanpiao_category_by_id(self, id=None, status=None):
        matcher = {"id": id}
        if status is not None:
            matcher.update({"status": fanpiao_pb.FanpiaoCategory.Status.Name(status)})
        fanpiao_category = self._fanpiao_category_collection.find_one(matcher)
        if fanpiao_category:
            return json_format.ParseDict(fanpiao_category, fanpiao_pb.FanpiaoCategory(), ignore_unknown_fields=True)
        return None

    def get_fanpiao_categories(self, status=None, merchant_id=None, scene=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if status is not None:
            matcher.update({"status": fanpiao_pb.FanpiaoCategory.Status.Name(status)})
        if scene is not None:
            matcher.update({"scene": fanpiao_pb.FanpiaoCategory.Scene.Name(scene)})
        fanpiao_categories = self._fanpiao_category_collection.find(matcher)
        logger.info(f"返回数量 get_fanpiao_categories: {matcher} {fanpiao_categories.count()}")
        if fanpiao_categories:
            return [
                json_format.ParseDict(fanpiao_category, fanpiao_pb.FanpiaoCategory(), ignore_unknown_fields=True)
                for fanpiao_category in fanpiao_categories
            ]
        return []

    @fanpiao_count_decorator
    def count_fanpiaos(self, status=None):
        matcher = {}
        if matcher is not None:
            matcher.update({"status": fanpiao_pb.Fanpiao.Status.Name(status)})
        if not matcher:
            return 0
        logger.info("获取饭票总数: {}".format(matcher))
        return self._fanpiao_collection.count(matcher)

    def get_fanpiaos(
        self,
        user_id=None,
        merchant_id=None,
        status=None,
        transaction_id=None,
        buy_start_time=None,
        buy_end_time=None,
        transaction_ids=None,
        balance_refund_apply=None,
        record_transaction_id=None,
        orderby=None,
        limit=None,
        brand_id=None,
        return_proto=True,
        expire_start_time=None,
        expire_end_time=None,
    ):
        matcher = {}
        tmp = list(
            filter(
                lambda x: dict(filter(lambda y: y[1], x.items())),
                [{'purchaseMerchantId': merchant_id}, {'merchantId': brand_id}],
            )
        )
        if len(tmp) > 1:
            matcher.update({'$or': tmp})
        elif len(tmp) == 1:
            matcher.update(tmp.pop(0))
        if status is not None:
            if isinstance(status, list):
                matcher.update({"status": {"$in": [fanpiao_pb.Fanpiao.Status.Name(s) for s in status]}})
            else:
                matcher.update({"status": fanpiao_pb.Fanpiao.Status.Name(status)})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if transaction_ids is not None:
            matcher.update({"transactionId": {"$in": transaction_ids}})
        if buy_start_time is not None or buy_end_time is not None:
            matcher['buyTime'] = {}
        if buy_start_time is not None:
            matcher['buyTime']["$gt"] = str(buy_start_time)
        if buy_end_time is not None:
            matcher['buyTime']["$lt"] = str(buy_end_time)
        if expire_start_time is not None or expire_end_time is not None:
            matcher['expireTime'] = {}
        if expire_start_time is not None:
            matcher['expireTime']["$gt"] = str(expire_start_time)
        if expire_end_time is not None:
            matcher['expireTime']["$lt"] = str(expire_end_time)
        if record_transaction_id is not None:
            matcher.update({"fanpiaoPaymentRecords.transactionId": record_transaction_id})
        if balance_refund_apply is not None:
            matcher.update({"balanceRefundApply": balance_refund_apply})
        if not matcher:
            return []
        if matcher == {'status': 'ACTIVE'} and orderby is None and limit is None:
            return []
        fanpiaos = self._fanpiao_collection.find(matcher)
        if orderby is not None:
            fanpiaos = fanpiaos.sort(orderby)
        if limit is not None:
            fanpiaos = fanpiaos.limit(limit)
        logger.info(f"返回数量 get_fanpiaos: {matcher} {orderby} {limit} {fanpiaos.count()}")
        if fanpiaos:
            if return_proto:
                return [
                    json_format.ParseDict(fanpiao, fanpiao_pb.Fanpiao(), ignore_unknown_fields=True) for fanpiao in fanpiaos
                ]
            return list(fanpiaos)
        return []

    def get_fanpiao(self, user_id=None, id=None, transaction_id=None, merchant_id=None, status=None, brand_id=None):
        matcher = {}
        tmp = list(
            filter(
                lambda x: dict(filter(lambda y: y[1], x.items())),
                [{'purchaseMerchantId': merchant_id}, {'merchantId': brand_id}],
            )
        )
        if len(tmp) > 1:
            matcher.update({'$or': tmp})
        elif len(tmp) == 1:
            matcher.update(tmp.pop(0))
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if status is not None:
            matcher.update({"status": fanpiao_pb.Fanpiao.Status.Name(status)})
        if not matcher:
            return None
        fanpiao = self._fanpiao_collection.find_one(matcher)
        if not fanpiao:
            return None
        return json_format.ParseDict(fanpiao, fanpiao_pb.Fanpiao(), ignore_unknown_fields=True)

    def get_fanpiaos_by_short_id(self, short_id):
        matcher = {"shortId": short_id}
        fanpiaos = self._fanpiao_collection.find(matcher)
        return [json_format.ParseDict(fanpiao, fanpiao_pb.Fanpiao(), ignore_unknown_fields=True) for fanpiao in fanpiaos]

    def get_fanpiao_by_partial_id_v2(self, merchant_id, short_id):
        matcher = {"merchantId": merchant_id, "id": {"$regex": f"{short_id}.*"}}
        fanpiaos = self._fanpiao_collection.find(matcher)
        if not fanpiaos:
            return []
        return [json_format.ParseDict(fanpiao, fanpiao_pb.Fanpiao(), ignore_unknown_fields=True) for fanpiao in fanpiaos]

    def get_fanpiao_by_partial_id(self, merchant_id, partial_id):
        matcher = {
            # "merchantId": merchant_id,
            "id": {"$regex": f".*{partial_id}.*"}
        }
        fanpiaos = self._fanpiao_collection.find(matcher)
        if not fanpiaos:
            return []
        return [json_format.ParseDict(fanpiao, fanpiao_pb.Fanpiao(), ignore_unknown_fields=True) for fanpiao in fanpiaos]

    def add_or_update_fanpiao(self, fanpiao):
        matcher = {"id": fanpiao.id}
        fanpiao.update_time = int(time.time())
        json_obj = json_format.MessageToDict(fanpiao, including_default_value_fields=True)
        self._fanpiao_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def count_fanpiao_selling_quantity(self, fanpiao_category_id=None, status=None):
        matcher = {}
        if fanpiao_category_id is not None:
            matcher.update({"fanpiaoCategoryId": fanpiao_category_id})
        if status is not None:
            matcher.update({"status": fanpiao_pb.FanpiaoCategory.Status.Name(status)})
        if not matcher:
            return 0
        return self._fanpiao_collection.count(matcher)

    def add_or_update_fanpiao_scan_qrcode(self, qrcode):
        matcher = {"id": qrcode.id}
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        self._fanpiao_scan_qrcode_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_fanpiao_scan_qrcode(self, id=None, user_id=None, payment_transaction_id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if payment_transaction_id is not None:
            matcher.update({"paymentTransactionId": payment_transaction_id})
        if not matcher:
            return None
        qrcode = self._fanpiao_scan_qrcode_collection.find_one(matcher)
        if not qrcode:
            return None
        return json_format.ParseDict(qrcode, fanpiao_pb.FanpiaoScanQrcode(), ignore_unknown_fields=True)

    def get_fanpiao_risk_control_config(self):
        config = self._fanpiao_risk_control_config_collection.find_one()
        if not config:
            return None
        return json_format.ParseDict(config, fanpiao_pb.FanpiaRiskControlConfig(), ignore_unknown_fields=True)

    def add_or_update_monitor_logs(self, logs):
        matcher = {"id": logs.id}
        json_obj = json_format.MessageToDict(logs, including_default_value_fields=True)
        return self._fanpiao_log_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

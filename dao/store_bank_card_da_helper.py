# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
import dao.constants as dao_constants
import proto.merchant_rules_pb2 as merchant_rules_pb

class StoreBankCardDataAccessHelper(DaoHelper):
    @property
    def __collection(self):
        db = dao_constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME
        collection = dao_constants.MONGODB_STORE_BANK_CARDS_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def get_bank_card_by_store_id(self, store_id):
        """ 查询门店的银行卡信息
        """
        match = {
            "storeId": store_id
        }
        bank_card = self.__collection.find_one(match)
        if bank_card:
            return json_format.ParseDict(bank_card, merchant_rules_pb.StoreBankCardInfo(), ignore_unknown_fields = True)
        return None

    def save_bank_card(self, merchant_id, store_id, user_name, card_number, open_bank_code):
        bank_card = {
            "merchantId": merchant_id,
            "storeId": store_id,
            "userName": user_name,
            "cardNumber": card_number,
            "openBankCode": open_bank_code
        }
        self.__collection.insert_one(bank_card)

# -*- coding: utf-8 -*-

from google.protobuf import json_format

import proto.user_pb2 as user_pb
import dao.constants as constants
import proto.staff_pb2 as staff_pb
from dao.dao_helper import DaoHelper


# Staff Data Access Helper，提供针对时来平台业务员管理相关的数据访问服务。
class StaffDataAccessHelper(DaoHelper):
    def get_staff(self, staff_id):
        """根据ID返回指定Staff信息。

        Args:
            staff_id: (string) 该业务员对应的ID

        Returns:
            (ShilaiStaff) 若指定业务员ID存在，返回相应业务员的ShilaiStaff结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        staff = db[constants.MONGODB_STAFF_COLLECTION_NAME].find_one({'id': staff_id})
        if staff:
            return json_format.ParseDict(staff, staff_pb.ShilaiStaff(), ignore_unknown_fields=True)
        else:
            return None

    def get_staff_by_union_id(self, unionid):
        """根据指定的微信UnionID返回对应时来平台用户信息。

        Args:
            unionid: (string) 用户ID

        Returns:
            (MerchantUser) 若指定用户UnionID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_STAFF_COLLECTION_NAME].find_one({"wechatProfile.unionid": unionid})
        if user:
            return json_format.ParseDict(user, staff_pb.ShilaiStaff(),
                                         ignore_unknown_fields=True)
        return None

    def get_user_by_openid(self, openid):
        """根据用户的openID返回时来平台用户信息。

        Args:
            openid: (string) 用户openID

        Returns:
            (User) 若指定用户openID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_STAFF_COLLECTION_NAME].find_one({"wechatProfile.openid": openid})
        if user:
            return json_format.ParseDict(user, staff_pb.ShilaiStaff(),
                                         ignore_unknown_fields=True)
        return None

    def add_staff(self, staff):
        """添加一个新的业务员。

        Args:
            staff: (ShilaiStaff) 业务员信息结构体
        """
        json_obj = json_format.MessageToDict(staff, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        db[constants.MONGODB_STAFF_COLLECTION_NAME].insert_one(json_obj)

    def add_staff_if_not_exist(self, staff):
        """如果指定业务员当前不存在，则往数据库添加一个新的业务员。

        Args:
            staff: (ShilaiStaff) 业务员信息结构体
        """
        staff = self.get_staff(staff.id)
        if not staff:
            self.add_staff(staff)

    def update_or_create_staff(self, staff):
        """如果指定业务员当前不存在，则添加一个新的ShilaiStaff信息。
           如果指定业务员存在，则更新该业务员信息。

        Args:
            staff: (ShilaiStaff) 业务员ShilaiStaff信息结构体
        """
        user_id = staff.id
        if self.get_staff(user_id):
            json_obj = json_format.MessageToDict(staff, including_default_value_fields=True)
            db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
            db[constants.MONGODB_STAFF_COLLECTION_NAME].replace_one({'id': user_id}, json_obj, upsert=True)
        else:
            self.add_staff(staff)

    def get_managed_staff_list(self, staff_id):
        """根据员工ID返回该员工管理的所有下属Staff列表。

        Args:
            staff_id: (string) 员工ID

        Returns:
            (list of ShilaiStaff) 若指定员工ID存在，返回该员工管理的所有下属Staff列表，否则返回None
        """
        matcher = {'managerId': staff_id}
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        staff_list = db[constants.MONGODB_STAFF_COLLECTION_NAME].find(matcher)
        if staff_list:
            return [json_format.ParseDict(staff, staff_pb.ShilaiStaff(), ignore_unknown_fields=True) for staff in staff_list]
        else:
            return None

    def get_staff_list(self, manager_id=None):
        """ 查询员工列表
        """
        matcher = {}
        if manager_id is not None:
            matcher.update({"managerId": manager_id})
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        staff_list = db[constants.MONGODB_STAFF_COLLECTION_NAME].find(matcher)
        return [json_format.ParseDict(staff, staff_pb.ShilaiStaff(), ignore_unknown_fields=True) for staff in staff_list]

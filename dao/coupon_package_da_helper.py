# -*- coding: utf-8 -*-

from google.protobuf import json_format

import dao.constants as constants
import proto.coupons_pb2 as coupons_pb
from dao.dao_helper import DaoHelper


class CouponPackageDataAccessHelper(DaoHelper):
    @property
    def _coupon_package_collection(self):
        db = constants.MONGODB_COUPON_DATABASE_NAME
        collection = constants.MONGODB_COUPON_PACKAGE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def get_coupon_package(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if not matcher:
            return None
        coupon_package = self._coupon_package_collection.find_one(matcher)
        if not coupon_package:
            return None
        return json_format.ParseDict(coupon_package)

    def count_coupon_package(self, coupon_category_id=None, status=None):
        matcher = {}
        if coupon_category_id is not None:
            matcher.update({"couponCategoryId": coupon_category_id})
        if status is not None:
            matcher.update({"status": coupons_pb.CouponPackage.Status.Name(status)})
        if not matcher:
            return 0
        count = self._coupon_package_collection.count(matcher)
        return count

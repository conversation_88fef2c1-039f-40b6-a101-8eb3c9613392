# -*- coding: utf-8 -*-


from google.protobuf import json_format
from pymongo import UpdateOne

import proto.merchant_rules_pb2 as merchant_rules_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class AppComponentConfigDataAccessHelper(DaoHelper):

    @property
    def _app_component_config_collection(self):
        db = constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME
        c = constants.MONGODB_APP_COMPONENT_CONFIG_COLLECTION_NAME
        return self.mongo_client[db][c]

    def add_or_update_app_component_config(self, app_component_config):
        promotion_type = merchant_rules_pb.Merchant.PromotionType.Name(app_component_config.promotion_type)
        matcher = {"promotionType": app_component_config.promotion_type}
        json_data = json_format.MessageToDict(app_component_config)
        self._app_component_config_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_app_component_config(self, promotion_type):
        matcher = {}
        if promotion_type is not None:
            if not isinstance(promotion_type, str):
                promotion_type = merchant_rules_pb.Merchant.PromotionType.Name(promotion_type)
            matcher.update({"promotionType": promotion_type})
        if not matcher:
            return
        app_component_config = self._app_component_config_collection.find_one(matcher)
        if app_component_config is None:
            return
        app_component_config = json_format.ParseDict(
            app_component_config,
            merchant_rules_pb.AppComponentConfig(),
            ignore_unknown_fields=True
        )
        return app_component_config

    def get_app_component_configs(self):
        matcher = {}
        app_component_configs = self._app_component_config_collection.find(matcher)
        result = [
            json_format.ParseDict(a, merchant_rules_pb.AppComponentConfig(), ignore_unknown_fields)
            for a in app_component_configs
        ]
        return result

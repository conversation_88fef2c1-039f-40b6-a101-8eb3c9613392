from google.protobuf import json_format

from dao import constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.sns import messaging_pb2 as messaging_pb

class ApplyForGroupDiningMessageDataAccessHelper(DaoHelper):

    @property
    def __collection(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_APPLY_FOR_MESSAGE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __collection_last_viewed_time(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_APPLY_FOR_MESSAGE_VIEW_TIME_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_message(self, message):
        obj = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection.insert_one(obj)

    def add_or_update_last_view_time(self, message):
        matcher = {"userId": message.user_id}
        json_obj = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection_last_viewed_time.update(matcher, {"$set": json_obj}, upsert=True)

    def get_last_view_message(self, user_id):
        matcher = {"userId": user_id}
        message = self.__collection_last_viewed_time.find_one(matcher)
        if message:
            return json_format.ParseDict(message, messaging_pb.ApplyForLastViewedTime(),
                                         ignore_unknown_fields=True)
        return None

    def count_unread(self, user_id, last_viewed_time):
        matcher = {
            "$or": [
                {"directorId": user_id},
                {"applicantId": user_id}
            ],
            "applyTime": {"$gt": str(last_viewed_time)}
        }
        return self.__collection.count(matcher)

    def get_messages(self, applicant_id=None, director_id=None, user_id=None, page=None, size=None, orderby=None,
                     create_time=None, group_dining_event_id=None):
        matcher = {}
        if user_id is None:
            if applicant_id is not None:
                matcher.update({
                    "applicantId": applicant_id
                })
            if director_id is not None:
                matcher.update({
                    "directorId": director_id
                })
        elif user_id is not None:
            matcher.update({
                "$or": [
                    {"directorId": user_id},
                    {"applicantId": user_id}
                ]
            })
        if create_time is not None:
            matcher.update({"$lt": str(create_time)})
        if group_dining_event_id is not None:
            matcher.update({"groupDiningEventId": group_dining_event_id})
        if matcher:
            messages = self.__collection.find(matcher)
            if orderby is not None:
                messages.sort(orderby)
            if page is not None and size is not None:
                skip = (page - 1) * size
                messages = messages.skip(skip).limit(size)
            if messages:
                return [json_format.ParseDict(message, messaging_pb.ApplyForGroupDiningMessage(),
                                              ignore_unknown_fields=True)
                        for message in messages]
        return []

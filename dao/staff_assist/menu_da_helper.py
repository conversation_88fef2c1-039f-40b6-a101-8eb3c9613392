# -*- coding: utf-8 -*-

import proto.staff_pb2 as staff_pb
from dao.dao_helper import DaoHelper
from dao import constants


class MenuDataAccessHelper(DaoHelper):

    @property
    def _menu_collection(self):
        db = constants.MONGODB_USER_DATABASE_NAME
        c = constants.MONGODB_STAFF_ASSIST_MENU_COLLECTION_NAME
        return self.mongo_client[db][c]

    def add_or_update_menu(self, menu):
        matcher = {
            "id": menu.id
        }
        json_data = self.to_dict(menu)
        self._menu_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_menu(self, id=None, name=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if name is not None:
            matcher.update({"name": name})
        authority = self._menu_collection.find_one(matcher)
        return self.parse_document(authority, cls=staff_pb.StaffAssistMenu)

    def get_menus(self):
        menus = self._menu_collection.find({})
        return self.parse_documents(menus, cls=staff_pb.StaffAssistMenu)

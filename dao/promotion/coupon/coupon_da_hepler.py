# -*- coding: utf-8 -*-

"""券数据库操作模块."""

import proto.promotion.coupon.coupon_pb2 as coupon_pb
from common import protobuf_transformer
from dao.dao_helper import <PERSON>oHel<PERSON>
from dao import constants


class CouponDAHelper(DaoHelper):

    @property
    def __coupon_collection(self):
        db = constants.MONGODB_PROMOTION_DATABASE_NAME
        c = constants.MONGODB_COUPON_COLLECTION_V2_NAME
        return self.mongo_client[db][c]

    def add_or_update_coupon(self, coupon):
        matcher = {'id': coupon.id}
        json_data = protobuf_transformer.protobuf_to_dict(coupon)
        self.__coupon_collection.update_one(matcher, {'$set': json_data}, upsert=True)

    def get_coupon(self, id=None):
        matcher = {}
        self.__set_matcher_id(matcher, id)
        if not matcher:
            return None
        result = self.__coupon_collection.find_one(matcher)
        return protobuf_transformer.dict_to_protobuf(result, coupon_pb.Coupon)

    def get_coupon_list(self, ids=None, merchant_id=None, user_id=None, status=None, coupon_type=None, reduce_fee_type=None):
        matcher = {}
        self.__set_matcher_ids(matcher, ids)
        self.__set_matcher_user_id(matcher, user_id)
        self.__set_matcher_merchant_id(matcher, merchant_id)
        self.__set_matcher_status(matcher, status)
        self.__set_matcher_coupon_type(matcher, coupon_type)
        self.__set_matcher_reduce_fee_type(matcher, reduce_fee_type)
        if not matcher:
            return
        result = self.__coupon_collection.find(matcher)
        return self.parse_documents(result, coupon_pb.Coupon)

    def __set_matcher_reduce_fee_type(self, matcher, reduce_fee_type):
        if reduce_fee_type is None:
            return
        if not isinstance(reduce_fee_type, str):
            reduce_fee_type = coupon_pb.ReduceFeeType.Name(reduce_fee_type)
        matcher.update({'reduceFeeType': reduce_fee_type})

    def __set_matcher_coupon_type(self, matcher, coupon_type):
        if coupon_type is None:
            return
        if not isinstance(coupon_type, str):
            coupon_type = coupon_pb.Coupon.CouponType.Name(coupon_type)
        matcher.update({'couponType': coupon_type})

    def __set_matcher_status(self, matcher, status):
        if status is None:
            return
        if not isinstance(status, str):
            status = coupon_pb.Coupon.CouponStatus.Name(status)
        matcher.update({'status': status})

    def __set_matcher_merchant_id(self, matcher, merchant_id):
        if merchant_id is None:
            return
        matcher.update({'merchantId': merchant_id})

    def __set_matcher_user_id(self, matcher, user_id):
        if user_id is None:
            return
        matcher.update({"userId": user_id})

    def __set_matcher_id(self, matcher, id):
        if id is None:
            return
        matcher.update({"id": id})

    def __set_matcher_ids(self, matcher, ids):
        if ids is None:
            return
        matcher.update({"id": {'$in': ids}})

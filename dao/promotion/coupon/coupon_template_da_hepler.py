# -*- coding: utf-8 -*-

"""券模板数据库操作模块."""

import proto.promotion.coupon.coupon_pb2 as coupon_pb
from common import protobuf_transformer
from dao.dao_helper import <PERSON>oHel<PERSON>
from dao import constants


class CouponTemplateDAHelper(DaoHelper):

    @property
    def __coupon_template_collection(self):
        db = constants.MONGODB_PROMOTION_DATABASE_NAME
        c = constants.MONGODB_COUPON_TEMPLATE_COLLECTION_NAME
        return self.mongo_client[db][c]

    def add_or_update_coupon_template(self, coupon_template):
        matcher = {'id': coupon_template.id}
        json_data = protobuf_transformer.protobuf_to_dict(coupon_template)
        self.__coupon_template_collection.replace_one(matcher, json_data, upsert=True)

    def get_coupon_template(self, id=None):
        matcher = {}
        self.__set_matcher_id(matcher, id)
        if not matcher:
            return None
        result = self.__coupon_template_collection.find_one(matcher)
        return protobuf_transformer.dict_to_protobuf(result, coupon_pb.CouponTemplate)

    def get_coupon_template_list(self, ids=None):
        matcher = {}
        self.__set_matcher_ids(matcher, ids)
        result = self.__coupon_template_collection.find(matcher)
        return self.parse_documents(result, coupon_pb.CouponTemplate)

    def __set_matcher_id(self, matcher, id):
        if id is None:
            return
        matcher.update({"id": id})

    def __set_matcher_ids(self, matcher, ids):
        if ids is None:
            return
        matcher.update({"id": {'$in': ids}})

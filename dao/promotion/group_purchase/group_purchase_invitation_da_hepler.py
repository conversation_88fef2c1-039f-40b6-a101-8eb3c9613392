# -*- coding: utf-8 -*-

"""团购邀请数据库操作模块."""

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from dao.dao_helper import DaoHelper
from dao import constants


class GroupPurchaseInvitationDAHelper(DaoHelper):
    """团购邀请数据库操作类."""

    @property
    def __group_purchase_invitation_collection(self):
        db = constants.MONGODB_GROUP_PURCHASE_DB_NAME
        c = constants.MONGODB_GROUP_PURCHASE_INVITATION_COLL_NAME
        return self.mongo_client[db][c]

    def add_or_update_group_purchase_invitation(self, group_purchase_invitation):
        """新增或更新团购邀请."""
        matcher = {'id': group_purchase_invitation.id}
        json_data = self.to_dict(group_purchase_invitation)
        self.__group_purchase_invitation_collection.update(
            matcher, {'$set': json_data}, upsert=True)

    def get_group_purchase_invitation(self, id=None, member_id=None, join_group_id=None):
        """获取单个团购邀请."""
        matcher = {}
        self.__set_matcher_id(matcher, id)
        self.__set_matcher_join_group_id(matcher, join_group_id)
        self.__set_matcher_member_id(matcher, member_id)
        if not matcher:
            return
        result = self.__group_purchase_invitation_collection.find_one(matcher)
        return self.parse_document(result, group_purchase_pb.GroupPurchaseInvitation)

    def get_group_purchase_invitation_list(self, ids=None, join_group_id=None, member_id=None):
        """获取单个团购邀请."""
        matcher = {}
        self.__set_matcher_ids(matcher, ids)
        self.__set_matcher_join_group_id(matcher, join_group_id)
        self.__set_matcher_member_id(matcher, member_id)
        if not matcher:
            return []
        result = self.__group_purchase_invitation_collection.find(matcher)
        return self.parse_documents(result, group_purchase_pb.GroupPurchaseInvitation)

    def __set_matcher_member_id(self, matcher, member_id):
        if member_id is None:
            return
        matcher.update({"memberId": member_id})

    def __set_matcher_join_group_id(self, matcher, join_group_id):
        if join_group_id is None:
            return
        matcher.update({"joinGroupId": join_group_id})

    def __set_matcher_id(self, matcher, id):
        if id is None:
            return None
        matcher.update({'id': id})

    def __set_matcher_ids(self, matcher, ids):
        if ids is None:
            return None
        matcher.update({'id': {"$in": ids}})

    def __set_matcher_member_id(self, matcher, member_id):
        if member_id is None:
            return
        matcher.update({"memberId": member_id})

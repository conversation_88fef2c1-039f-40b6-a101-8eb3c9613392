# -*- coding: utf-8 -*-

"""团购模板数据库操作模块."""

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from dao.dao_helper import DaoHelper
from dao import constants


class GroupPurchaseTemplateDAHelper(DaoHelper):
    """团购模板数据库操作类."""

    @property
    def __group_purchase_template_collection(self):
        db = constants.MONGODB_GROUP_PURCHASE_DB_NAME
        c = constants.MONGODB_GROUP_PURCHASE_TEMPLATE_COLL_NAME
        return self.mongo_client[db][c]

    def add_or_update_group_purchase_template(self, group_purchase_template):
        """添加团购模板."""
        matcher = {
            "id": group_purchase_template.id
        }
        json_data = self.to_dict(group_purchase_template)
        self.__group_purchase_template_collection.update(
            matcher, {"$set": json_data}, upsert=True)

    def get_group_purchase_template(self, id=None):
        """获取单个团购模板."""
        matcher = dict()
        self.__set_id(matcher, id)
        if not matcher:
            return None
        template = self.__group_purchase_template_collection.find_one(matcher)
        return self.parse_document(
            template, group_purchase_pb.GroupPurchaseTemplate)

    def get_group_purchase_template_list(self, ids=None):
        """批量获取团购模板."""
        matcher = dict()
        self.__set_ids(matcher, ids)
        if not matcher:
            return []
        templates = self.__group_purchase_template_collection.find(matcher)
        return self.parse_documents(
            templates, group_purchase_pb.GroupPurchaseTemplate)

    def __set_id(self, matcher, id):
        if id is None:
            return
        matcher.update({"id": id})

    def __set_ids(self, matcher, ids):
        if ids is None:
            return
        if len(ids) == 0:
            return
        matcher.update({"id": {"$in": ids}})

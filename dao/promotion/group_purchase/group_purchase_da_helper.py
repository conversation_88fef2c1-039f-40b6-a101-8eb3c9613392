# -*- coding: utf-8 -*-

"""团购数据库操作模块."""

import logging

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from dao.dao_helper import DaoHelper
from dao import constants

logger = logging.getLogger(__name__)


class GroupPurchaseDAHelper(DaoHelper):
    """团购数据库操作类."""

    @property
    def __group_purchase_collection(self):
        db = constants.MONGODB_GROUP_PURCHASE_DB_NAME
        c = constants.MONGODB_GROUP_PURCHASE_COLL_NAME
        return self.mongo_client[db][c]

    def add_or_update_group_purchase(self, group_purchase):
        """更新团购."""
        matcher = {"id": group_purchase.id}
        json_data = self.to_dict(group_purchase)
        self.__group_purchase_collection.update(
            matcher, {"$set": json_data}, upsert=True)

    def get_group_purchase(self, id=None, transaction_id=None, coupon_id=None, closing_coupon_id=None):
        """获取单个团购."""
        matcher = {}
        self.__set_matcher_id(matcher, id)
        self.__set_matcher_transaction_id(matcher, transaction_id)
        self.__set_matcher_coupon_id(matcher, coupon_id)
        self.__set_matcher_closing_coupon_id(matcher, closing_coupon_id)
        if not matcher:
            return
        group_purchase = self.__group_purchase_collection.find_one(matcher)
        return self.parse_document(group_purchase, group_purchase_pb.GroupPurchase)

    def get_group_purchase_list(
            self, ids=None, merchant_id=None, leader_id=None, status=None, start_update_time=None,
            multi_status=None
    ):
        """获取团购列表."""
        matcher = {}
        self.__set_matcher_ids(matcher, ids)
        self.__set_matcher_merchant_id(matcher, merchant_id)
        self.__set_matcher_leader_id(matcher, leader_id)
        self.__set_matcher_status(matcher, status)
        self.__set_matcher_multi_status(matcher, multi_status)
        self.__set_matcher_start_update_time(matcher, start_update_time)
        if not matcher:
            return []
        group_purchase_list = self.__group_purchase_collection.find(matcher)
        return self.parse_documents(group_purchase_list, group_purchase_pb.GroupPurchase)

    def __set_matcher_multi_status(self, matcher, multi_status):
        if multi_status is None or len(multi_status) == 0:
            return
        status_list = []
        for status in multi_status:
            if not isinstance(status, str):
                status = group_purchase_pb.GroupPurchase.GroupPurchaseStatus.Name(status)
            status_list.append(status)
        matcher.update({"status": {"$in": status_list}})

    def __set_matcher_start_update_time(self, matcher, start_update_time):
        if start_update_time is None:
            return
        matcher.update({"updateTime": {"$gt": str(start_update_time)}})

    def __set_matcher_coupon_id(self, matcher, coupon_id):
        if coupon_id is None:
            return
        matcher.update({"groupMembers.couponId": coupon_id})

    def __set_matcher_closing_coupon_id(self, matcher, coupon_id):
        if coupon_id is None:
            return
        matcher.update({"closingCouponIds": coupon_id})

    def __set_matcher_status(self, matcher, status):
        if status is None:
            return
        if not isinstance(status, str):
            status = group_purchase_pb.GroupPurchase.GroupPurchaseStatus.Name(status)
        matcher.update({'status': status})

    def __set_matcher_transaction_id(self, matcher, transaction_id):
        if transaction_id is None:
            return
        matcher.update({"transactionId": transaction_id})

    def __set_matcher_leader_id(self, matcher, leader_id):
        if leader_id is None:
            return
        matcher.update({"leaderId": leader_id})

    def __set_matcher_merchant_id(self, matcher, merchant_id):
        if merchant_id is None:
            return
        matcher.update({"merchantId": merchant_id})

    def __set_matcher_id(self, matcher, id):
        if id is None:
            return
        matcher.update({'id': id})

    def __set_matcher_ids(self, matcher, ids):
        if ids is None:
            return
        if len(ids) == 0:
            return
        matcher.update({'id': {"$in": ids}})

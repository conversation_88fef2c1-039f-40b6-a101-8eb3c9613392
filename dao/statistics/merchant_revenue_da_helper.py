# -*- coding: utf-8 -*-

"""
Filename: merchant_revenue_da_helper.py
Date: 2020-06-11 14:26:46
Title: 商户营收统计
"""

from google.protobuf import json_format

import proto.statistics.merchant_revenue_pb2 as merchant_revenue_pb
from dao import constants
from dao.dao_helper import DaoHelper

class MerchantRevenueDataAccessHelper(DaoHelper):

    db = constants.MONGODB_BI_DATABASE_NAME

    @property
    def _day_revenue_collection(self):
        c = constants.MONGODB_DAY_REVENUE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_day_revenue(self, day_revenue):
        matcher = {
            "merchantId": day_revenue.merchant_id,
            "date": day_revenue.date
        }
        json_data = json_format.MessageToDict(day_revenue, including_default_value_fields=True)
        self._day_revenue_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_day_revenue(self, merchant_id=None, date=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if date is not None:
            matcher.update({"date": self._get_date(date)})
        if not matcher:
            return []
        day_revenues = self._day_revenue_collection.find(matcher)
        day_revenues = [json_format.ParseDict(day_revenue, merchant_revenue_pb.DayRevenueStatistic(), ignore_unknown_fields=True)
                        for day_revenue in day_revenues]
        return day_revenues

    def _get_date(self, date):
        if isinstance(date, list):
            if len(date) == 2:
                return {"$lt": date[1], "$gte": date[0]}
            else:
                return {"$in": date}
        else:
            return date

# -*- coding: utf-8 -*-

import proto.message_center.message_pb2 as message_pb

from dao.dao_helper import <PERSON>oHel<PERSON>
from dao import constants


class MessageDataAccessHelper(DaoHelper):

    @property
    def __message_collection(self):
        db = constants.MONGODB_MESSAGE_CENTER_DB_NAME
        c = constants.MONGODB_MESSAGE_COLL_NAME
        return self.mongo_client[db][c]

    def add_or_update_message(self, message):
        matcher = {"id": message.id}
        json_data = self.to_dict(message)
        self.__message_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_user_period_messages(self, user_id, create_time_periods):
        matcher = {
            'receiverId': user_id,
            'createTime': {
                '$gt': str(create_time_periods[0]),
                '$lt': str(create_time_periods[1]),
            }
        }
        results = self.__message_collection.find(matcher).sort([('createTime', -1)]).limit(50)
        return self.parse_documents(results, message_pb.Message)

    def get_message_by_relative_id(self, receiver_id, relative_id):
        matcher = {
            "receiverId": receiver_id,
            "relativeInfo.relativeId": relative_id
        }
        result = self.__message_collection.find_one(matcher)
        return self.parse_document(result, message_pb.Message)

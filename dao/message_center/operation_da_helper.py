# -*- coding: utf-8 -*-

import time

from common.utils import id_manager
from dao.dao_helper import <PERSON><PERSON>Hel<PERSON>
from dao import constants


class OperationDataAccessHelper(DaoHelper):
    @property
    def __operation_collection(self):
        db = constants.MONGODB_MESSAGE_CENTER_DB_NAME
        c = constants.MONGODB_OPERATION_NAME
        return self.mongo_client[db][c]

    def add_operation(self, user_id, service, record):
        json_data = {
            'id': id_manager.generate_common_id(),
            'userId': user_id,
            'service': service,
            'createTime': int(time.time()),
            'updateTime': int(time.time()),
            'record': record,
        }
        self.__operation_collection.insert_one(json_data)

    def get_user_opeartions(self, user_id, create_time_periods):
        matcher = {
            'userId': user_id,
            'createTime': {
                '$gt': str(create_time_periods[0]),
                '$lt': str(create_time_periods[1]),
            },
        }
        results = self.__operation_collection.find(matcher).sort([('createTime', -1)]).limit(50)
        return self.parse_documents(results, message_pb.Message)

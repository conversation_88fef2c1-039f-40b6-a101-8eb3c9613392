# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.finance import wallet_pb2 as wallet_pb

logger = logging.getLogger(__name__)


class TransactionDataAccessHelper(DaoHelper):
    db = constants.MONGODB_TRANSACTION_DATABASE_NAME

    @property
    def _inexistence_transaction_collection(self):
        c = constants.MONGODB_INEXISTENCE_TRANSACTION_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _delay_refund_transaction_collection(self):
        c = constants.MONGODB_DELAY_REFUND_TRANSACTION_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _transaction_ordering_coupon_package_union_pay_collection(self):
        c = constants.MONGODB_TRANSACTION_ORDERING_COUPON_PACKAGE_UNION_PAY_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _union_pay_transaction_collection(self):
        c = constants.MONGODB_UNION_PAY_TRANSACTION_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _merchant_day_transfer_collection(self):
        c = constants.MONGODB_TRANSACTION_MERCHANT_DAY_TRANSFER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def __collection(self):
        db = constants.MONGODB_TRANSACTION_DATABASE_NAME
        collection = constants.MONGODB_TRANSACTION_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def _transaction_stats_collection(self):
        db = constants.MONGODB_TRANSACTION_DATABASE_NAME
        collection = constants.MONGODB_TRANSACTION_STATS_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_or_update_union_pay_transaction(self, transaction: wallet_pb.UnionPayTransaction):
        matcher = {'transactionId': transaction.transaction_id}
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self._union_pay_transaction_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_union_pay_transaction(self, transaction_id: str = None, sub_transaction_id: str = None):
        matcher = {}
        if transaction_id is not None:
            matcher.update({'transactionId': transaction_id})
        if sub_transaction_id is not None:
            matcher.update({'subTransactionIds': sub_transaction_id})
        if not matcher:
            return None
        transaction = self._union_pay_transaction_collection.find_one(matcher)
        return self.parse_document(transaction, wallet_pb.UnionPayTransaction)

    def add_or_update_ordering_coupon_package_union_pay_transaction(self, transaction):
        matcher = {"transactionId": transaction.transaction_id}
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self._transaction_ordering_coupon_package_union_pay_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_ordering_coupon_package_union_pay_transaction(
        self, transaction_id=None, ordering_transaction_id=None, coupon_package_transaction_id=None
    ):
        matcher = {}
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if ordering_transaction_id is not None:
            matcher.update({"orderingTransactionId": ordering_transaction_id})
        if coupon_package_transaction_id is not None:
            matcher.update({"buyCouponPackageTransactionId": coupon_package_transaction_id})
        if not matcher:
            return None
        transaction = self._transaction_ordering_coupon_package_union_pay_collection.find_one(matcher)
        if not transaction:
            return None
        return self.parse_document(transaction, wallet_pb.OrderingCouponPackageUnionPayTransaction)

    def count_transactions(
        self, user_id=None, last_paid_time=None, type=None, orderby=None, state=None, pay_time_period=None, pay_method=None
    ):
        matcher = {}
        if user_id is not None:
            matcher.update({"payerId": user_id})
        if last_paid_time is not None:
            matcher.update({"paidTime": {"$lt": str(last_paid_time)}})
        if pay_time_period is not None:
            matcher.update({"paidTime": {"$gte": str(pay_time_period[0]), "$lt": str(pay_time_period[1])}})
        if pay_method is not None:
            matcher.update({"payMethod": pay_method})
        if type is not None:
            if isinstance(type, list):
                matcher.update({"type": {"$in": [wallet_pb.Transaction.TransactionType.Name(t) for t in type]}})
            else:
                matcher.update({"type": wallet_pb.Transaction.TransactionType.Name(type)})
        if state is not None:
            if isinstance(state, list):
                matcher.update({"state": {"$in": [wallet_pb.Transaction.TransactionState.Name(s) for s in state]}})
            else:
                matcher.update({"state": wallet_pb.Transaction.TransactionState.Name(state)})
        if not matcher:
            return 0
        return self.__collection.count(matcher)

    def get_transactions(
        self,
        payee_id=None,
        payee_store_id=None,
        payer_id=None,
        ids=None,
        state=None,
        start_time=None,
        end_time=None,
        type=None,
        last_paid_time=None,
        page=None,
        size=None,
        orderby=None,
        multi_types=None,
        pay_method=None,
        start_create_time=None,
        end_create_time=None,
        refunded_transaction_id=None,
        ret_proto=True,
    ):
        """根据指定的查询条件，获取商户下的支付记录列表
        Args:
            payee_id: (String) 商户ID
            payee_store_id: (String) 门店ID
            payer_id: (string) 用户ID
            state: (PaymentStatus) 支付记录和状态
            start_time: (int) 所需查询的支付记录的起始时间
            end_time: (int) 所需查询的支付记录的结束时间
            type: (TransactionType) 交易类型
        Returns:
            (Transaction[]) 商户下的支付记录列表
        """
        filter = {}
        if payee_id is not None:
            filter['payeeId'] = payee_id
        if payee_store_id is not None:
            filter['payeeStoreId'] = payee_store_id
        if payer_id is not None:
            filter['payerId'] = payer_id
        if ids is not None:
            filter['id'] = {'$in': ids}
        if state is not None:
            filter['state'] = wallet_pb.Transaction.TransactionState.Name(state)
        if start_create_time is not None and end_create_time is not None:
            filter["createTime"] = {"$gt": str(start_create_time), "$lt": str(end_create_time)}
        if start_time is not None or end_time is not None or last_paid_time is not None:
            filter['paidTime'] = {}
        if start_time is not None:
            filter['paidTime']["$gt"] = str(start_time)
        if end_time is not None:
            filter['paidTime']["$lt"] = str(end_time)
        if last_paid_time is not None:
            filter['paidTime']["$lt"] = str(last_paid_time)
        if type is not None:
            filter['type'] = wallet_pb.Transaction.TransactionType.Name(type)
        if refunded_transaction_id is not None:
            filter['refundedTransactionId'] = refunded_transaction_id
        if isinstance(multi_types, list) and len(multi_types) > 0:
            filter['type'] = {"$in": [wallet_pb.Transaction.TransactionType.Name(t) for t in multi_types]}
        if pay_method is not None:
            filter['payMethod'] = wallet_pb.Transaction.PayMethod.Name(pay_method)
        if not filter:
            return []
        cursor = self.__collection.find(filter)
        # logger.info(f"-----------get_transactions: {filter} {page} {size} {cursor.count()}")
        if orderby is not None:
            cursor.sort(orderby)
        if not ret_proto:
            return list(cursor)
        return self.parse_documents(cursor, wallet_pb.Transaction, page=page, size=size)

    def update_state(self, transaction_id, state):
        """修改流水状态
        Args:
            transaction_id: (string)交易id
            state: (int)要改成的状态
        Return:
            None
        """
        match = {"id": transaction_id}
        udata = {"$set": {"state": wallet_pb.Transaction.TransactionState.Name(state)}}
        self.__collection.update(match, udata)

    def add_or_update_transaction(self, transaction):
        matcher = {"id": transaction.id}
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self.__collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    # unused
    def add_transaction(self, transaction):
        """增加一条流水
        Args:
            transaction: (Transaction)结构体
        Return:
            None
        """
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)

    # unused
    def update_transaction(self, transaction):
        """更新流水的信息
        Args:
            transaction: (Transaction)结构体
        Return:
            None
        """
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self.__collection.replace_one({'id': transaction.id}, json_obj)

    def get_transaction_by_id(self, transaction_id):
        """根据交易id返回一条流水
        Args:
            transaction_id: (string)流水id
        Return:
            Transaction结构体
        """
        match = {"id": transaction_id}
        transaction = self.__collection.find_one(match)
        if not transaction:
            return None
        return self.parse_document(transaction, wallet_pb.Transaction)

    def get_transaction_by_auth_code(self, auth_code):
        matcher = {"authCode": auth_code}
        transaction = self.__collection.find_one(matcher)
        if not transaction:
            return None
        return self.parse_document(transaction, wallet_pb.Transaction)

    def get_user_recently_order(self, user_id):
        """用户最近的一笔订单
        Args:
            user_id: (strting)
        Return:
            Transaction
        """
        matcher = {"payerId": user_id}
        transactions = self.__collection.find(matcher).sort([("paidTime", -1)]).limit(1)
        for transaction in transactions:
            return self.parse_document(transaction, wallet_pb.Transaction)
        return None

    def count_paid(self, user_id):
        pipeline = [
            {"$match": {"payerId": user_id}},
            {"$group": {"_id": 1, "count": {"$sum": "$paidFee"}}},
        ]
        return list(self.__collection.aggregate(pipeline))

    def count_incoming(self, user_id):
        pipeline = [
            {"$match": {"payeeId": user_id}},
            {"$group": {"_id": 1, "count": {"$sum": "$paidFee"}}},
        ]
        return list(self.__collection.aggregate(pipeline))

    def get_user_visited_stores(self, user_id, page=None, size=None, orderby=None):
        matcher = {
            "payerId": user_id,
            "state": wallet_pb.Transaction.TransactionState.Name(wallet_pb.Transaction.SUCCESS),
            "type": {
                "$in": [
                    wallet_pb.Transaction.TransactionType.Name(wallet_pb.Transaction.SELF_DINING_PAYMENT),
                    wallet_pb.Transaction.TransactionType.Name(wallet_pb.Transaction.GROUP_DINING_PAYMENT),
                ]
            },
        }
        group = {"_id": "$payeeId"}
        pipeline = [
            {"$match": matcher},
            {"$group": group},
        ]
        return list(self.__collection.aggregate(pipeline))

    def update_merchant_day_transfer(self, udata=None, order_no=None):
        matcher = {}
        if udata is None:
            return
        if order_no is not None:
            matcher.update({"orderNo": order_no})
        if not matcher:
            return
        self._merchant_day_transfer_collection.update_many(matcher, {"$set": udata})

    def add_or_update_merchant_day_transfer(self, transfer):
        matcher = {"transactionId": transfer.transaction_id}
        json_data = json_format.MessageToDict(transfer, including_default_value_fields=True)
        self._merchant_day_transfer_collection.update(matcher, {"$set": json_data}, upsert=True)

    def add_or_update_delay_refund_transaction(self, transaction):
        matcher = {"transactionId": transaction.transaction_id}
        json_data = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self._delay_refund_transaction_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_delay_refund_transactions(self, status=None, size=20, transaction_id=None):
        matcher = {}
        if status is not None:
            matcher.update({"status": wallet_pb.DelayRefundTransaction.Status.Name(status)})
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if not matcher:
            return None
        transactions = self._delay_refund_transaction_collection.find(matcher)
        if not transactions:
            return None
        return self.parse_documents(transactions, wallet_pb.DelayRefundTransaction, size=size)

    def get_merchant_day_transfers(
        self, status=None, transaction_id=None, start_timestamp=None, end_timestamp=None, merchant_id=None
    ):
        matcher = {}
        if status is not None:
            status = wallet_pb.MerchantDayTransfer.Status.Name(status)
            matcher.update({"status": status})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if start_timestamp is not None and end_timestamp is not None:
            matcher.update({"transactionTimestamp": {"$gt": str(start_timestamp), "$lt": str(end_timestamp)}})
        if not matcher:
            return None
        transfers = self._merchant_day_transfer_collection.find(matcher)
        return self.parse_documents(transfers, wallet_pb.MerchantDayTransfer)

    def add_or_update_inexistence_transaction(self, transaction):
        matcher = {"transactionId": transaction.transaction_id}
        obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self._inexistence_transaction_collection.update(matcher, {"$set": obj}, upsert=True)

    def get_inexistence_transactions(self, merchant_id=None, status=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if status is not None:
            matcher.update({"status": wallet_pb.InexistenceTransaction.Status.Name(status)})
        if not matcher:
            return []
        transactions = self._inexistence_transaction_collection.find(matcher)
        return self.parse_documents(transactions, wallet_pb.InexistenceTransaction)

    def get_inexistence_transaction(self, transaction_id=None):
        matcher = {}
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if not matcher:
            return None
        transaction = self._inexistence_transaction_collection.find_one(matcher)
        if not transaction:
            return None
        return self.parse_document(transaction, wallet_pb.InexistenceTransaction)

    def query_list(self, matcher, project={}, page=1, page_size=0, sort=[], **kwargs):
        return super().query_list(self.__collection, matcher, project, page=page, page_size=page_size, sort=sort, **kwargs)

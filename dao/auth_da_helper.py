from google.protobuf import json_format
from pymongo import MongoClient

import dao.constants as constants
import proto.authorizer_pb2 as authorizer_pb
import proto.user_pb2 as user_pb
from dao.dao_helper import DaoHelper

# Auth Data Access Helper，提供针对时来平台用户及商户授权相关的数据访问服务。
class AuthDataAccessHelper(DaoHelper):

    def get_authorizer_token(self, appid):
        """根据APPID返回对应的商户AuthorizerToken信息。

        Args:
            appid: (string) 商户APPID

        Returns:
            (AuthorizerToken) 若指定商户APPID存在，返回对应商户的AuthorizerToken结构体，
            若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        token = db[constants.MONGODB_AUTHORIZER_ACCESS_TOKEN_COLLECTION_NAME].find_one({'appid': appid})
        if token:
          return json_format.ParseDict(token, authorizer_pb.AuthorizerToken(),
              ignore_unknown_fields=True)
        else:
          return None

    def get_all_authorizer_tokens(self):
        """返回所有商户的AuthorizerToken信息。

        Returns:
            (list of AuthorizerToken) 所有商户的AuthorizerToken结构体
        """
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        tokens = db[constants.MONGODB_AUTHORIZER_ACCESS_TOKEN_COLLECTION_NAME].find({})
        if tokens:
          return [json_format.ParseDict(token, authorizer_pb.AuthorizerToken(),
              ignore_unknown_fields=True) for token in tokens]
        else:
          return None

    def set_authorizer_token(self, token):
        """创建或更新一个新的商户AuthorizerToken信息。

        Args:
            token: (AuthorizerToken) 商户AuthorizerToken信息结构体
        """
        json_obj = json_format.MessageToDict(token, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        if self.get_authorizer_token(token.appid):
            db[constants.MONGODB_AUTHORIZER_ACCESS_TOKEN_COLLECTION_NAME].replace_one({
                'appid': token.appid
            }, json_obj, upsert=True)
        else:
            db[constants.MONGODB_AUTHORIZER_ACCESS_TOKEN_COLLECTION_NAME].insert_one(json_obj)

    def get_component_verify_ticket(self, appid):
        """根据指定商户APPID返回对应的ComponentVerifyTicket信息。

        Args:
            appid: (string) 商户APPID

        Returns:
            (ComponentVerifyTicket) 若指定商户APPID存在，返回相应的ComponentVerifyTicket结构体，
            若不存在则返回None。
        """
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        ticket = db[constants.MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME].find_one({'appid': appid})
        if ticket:
          return json_format.ParseDict(ticket, authorizer_pb.ComponentVerifyTicket(),
              ignore_unknown_fields=True)
        else:
          return None

    def set_component_verify_ticket(self, component_verify_ticket):
        """创建或更新一个新的商户ComponentVerifyTicket信息。

        Args:
            component_verify_ticket: (ComponentVerifyTicket) 商户ComponentVerifyTicket信息结构体
        """
        json_obj = json_format.MessageToDict(component_verify_ticket, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        res = db[constants.MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME].find_one({
            'appid': component_verify_ticket.appid
        })
        if res:
            db[constants.MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME].replace_one({
                    'appid': component_verify_ticket.appid
                }, json_obj, upsert=True)
        else:
            db[constants.MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME].insert_one(json_obj)

    def get_authorizer_api_ticket(self, appid, ticket_type):
        """根据商户APPID返回对应AuthorizerTicket信息。

        Args:
            appid: (string) 商户APPID
            ticket_type: (authorizer_pb.AuthorizerTicket.TicketType)

        Returns:
            (AuthorizerTicket) 若指定商户APPID存在，返回相应的AuthorizerTicket结构体，若不存在则返回None
        """
        find_options = {
            'appid': appid,
            'type': authorizer_pb.AuthorizerTicket.TicketType.Name(ticket_type)
        }

        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        ticket = db[constants.MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME].find_one(find_options)
        if ticket:
          return json_format.ParseDict(ticket, authorizer_pb.AuthorizerTicket(),
              ignore_unknown_fields=True)
        else:
          return None

    def get_all_authorizer_api_tickets(self):
        """返回所有WechatApiTicket结构体。

        Returns:
            (list of AuthorizerApiTicket) 返回所有AuthorizerApiTicket结构体列表
        """
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        tickets = db[constants.MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME].find({})
        if tickets:
          return [json_format.ParseDict(ticket, authorizer_pb.AuthorizerTicket(),
              ignore_unknown_fields=True) for ticket in tickets]
        else:
          return None

    def set_authorizer_api_ticket(self, api_ticket):
        """添加一个新的时来平台商户的AuthorizerApiTicket信息。

        Args:
            api_ticket: (AuthorizerApiTicket) 时来平台商户AuthorizerApiTicket信息结构体
        """
        filter = {
            'appid': api_ticket.appid,
            'type': authorizer_pb.AuthorizerTicket.TicketType.Name(api_ticket.type)
        }
        json_obj = json_format.MessageToDict(api_ticket, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_AUTH_DATABASE_NAME]
        res = db[constants.MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME].find_one(filter)
        if res:
            db[constants.MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME].replace_one(filter,
                json_obj, upsert=True)
        else:
            db[constants.MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME].insert_one(json_obj)

    # def get_user_by_union_id(self, unionid):
    #     """根据指定的微信UnionID返回对应时来平台用户信息。

    #     Args:
    #         unionid: (string) 用户ID

    #     Returns:
    #         (User) 若指定用户UnionID存在，返回相应用户的User结构体，若不存在则返回None
    #     """
    #     db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
    #     user = db[constants.MONGODB_USER_COLLECTION_NAME].find_one({"wechatProfile.unionid": unionid})
    #     if user:
    #         return json_format.ParseDict(user, user_pb.User(),
    #                                      ignore_unknown_fields=True)
    #     return None

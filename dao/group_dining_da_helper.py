from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.group_dining import group_dining_pb2 as group_dining_pb


class GroupDiningDataAccessHelper(DaoHelper):
    """ 约饭
    """
    @property
    def __collection(self):
        db = constants.MONGODB_GROUP_DINING_EVENT_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_EVENT_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def update(self, dining_id, event_time=None, payment_rule=None, open_invite_permission=None,
               director_id=None, transaction_id=None, use_coupon_id=None, state=None, user_cnt=None):
        """ 更新饭局信息
        """
        matcher = {"id": dining_id}
        udata = {}
        if event_time is not None:
            udata.update({"eventTime": event_time})
        if payment_rule is not None:
            udata.update({"paymentRule": payment_rule})
        if open_invite_permission is not None:
            udata.update({"openInvitePermission": open_invite_permission})
        if director_id is not None:
            udata.update({"directorId": director_id})
        if transaction_id is not None:
            udata.update({"transactionId": transaction_id})
        if use_coupon_id is not None:
            udata.update({"useCouponId": use_coupon_id})
        if state is not None:
            udata.update({"state": group_dining_pb.GroupDiningEvent.DiningState.Name(state)})
        if user_cnt is not None:
            udata.update({"userCnt": user_cnt})

        if udata:
            self.__collection.update(matcher, {"$set": udata})

    def get_dining_events(self, merchant_id=None, store_id=None, initiator_id=None,
                          create_start_time=None, create_end_time=None, event_start_time=None, event_end_time=None,
                          payment_rule=None, state=None, use_coupon_id=None, user_cnt=None, visibility=None,
                          page=None, size=None, limit=None, orderby=None):
        filter = {}
        if merchant_id is not None:
            filter['merchantId'] = merchant_id
        if store_id is not None:
            filter['storeId'] = store_id
        if initiator_id is not None:
            filter['initiatorId'] = initiator_id
        if create_start_time is not None or create_end_time is not None:
            filter['createTime'] = {}
        if create_start_time is not None:
            filter['createTime']["$gt"] = str(create_start_time)
        if create_end_time is not None:
            filter['createTime']["$lt"] = str(create_end_time)
        if event_start_time is not None or event_end_time is not None:
            filter['eventTime'] = {}
        if event_start_time is not None:
            filter['eventTime']["$gt"] = str(event_start_time)
        if event_end_time is not None:
            filter['eventTime']["$lt"] = str(event_end_time)
        if payment_rule is not None:
            filter['paymentRule'] = group_dining_pb.GroupDiningEvent.PaymentRule.Name(payment_rule)
        if state is not None:
            filter['state'] = group_dining_pb.GroupDiningEvent.DiningState.Name(state)
        if use_coupon_id is not None:
            filter['useCouponId'] = use_coupon_id
        if user_cnt is not None:
            filter['userCnt'] = user_cnt
        if visibility is not None:
            filter['visibility'] = group_dining_pb.GroupDiningEvent.Visibility.Name(visibility)

        dinings = self.__collection.find(filter)
        if orderby is not None:
            dinings.sort(orderby)
        if limit is not None:
            dinings = dinings.limit(limit)
        if dinings:
            return [json_format.ParseDict(dining, group_dining_pb.GroupDiningEvent(), ignore_unknown_fields=True) for dining in dinings]
        return []

    def count_dining_events(self, merchant_id=None, store_id=None, initiator_id=None,
                            create_start_time=None, create_end_time=None, event_start_time=None,
                            event_end_time=None, payment_rule=None, state=None, visibility=None):
        filter = {}
        if merchant_id is not None:
            filter['merchantId'] = merchant_id
        if store_id is not None:
            filter['storeId'] = store_id
        if initiator_id is not None:
            filter['initiatorId'] = initiator_id
        if create_start_time is not None or create_end_time is not None:
            filter['createTime'] = {}
        if create_start_time is not None:
            filter['createTime']["$gt"] = str(create_start_time)
        if create_end_time is not None:
            filter['createTime']["$lt"] = str(create_end_time)
        if event_start_time is not None or event_end_time is not None:
            filter['eventTime'] = {}
        if event_start_time is not None:
            filter['eventTime']["$gt"] = str(event_start_time)
        if event_end_time is not None:
            filter['eventTime']["$lt"] = str(event_end_time)
        if payment_rule is not None:
            filter['paymentRule'] = group_dining_pb.GroupDiningEvent.PaymentRule.Name(payment_rule)
        if state is not None:
            filter['state'] = group_dining_pb.GroupDiningEvent.DiningState.Name(state)
        if visibility is not None:
            filter['visibility'] = group_dining_pb.GroupDiningEvent.Visibility.Name(visibility)
        return self.__collection.count(filter)

    def add_group_dining_event(self, dining):
        """ 新增一个饭局
        Args:
            dining: GroupDiningEvent结构

        Returns:
            None
        """
        json_obj = json_format.MessageToDict(dining, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)

    def get_dining_by_id(self, dining_id):
        """ 根据饭局id查找饭局
        Args:
            dining_id: 饭局

        Returns:
            GroupDiningEvent结构
        """
        match = {
            "id": dining_id
        }
        dining = self.__collection.find_one(match)
        if dining:
            return json_format.ParseDict(dining, group_dining_pb.GroupDiningEvent(), ignore_unknown_fields=True)
        return None

    def get_dining_by_transaction_id(self, transaction_id):
        """ 根据流水id查找饭局
        Args:
            transaction_id: 流水id

        Returns:
            GroupDiningEvent结构
        """
        match = {
            "transactionId": transaction_id
        }
        dining = self.__collection.find_one(match)
        if dining:
            return json_format.ParseDict(dining, group_dining_pb.GroupDiningEvent(), ignore_unknown_fields=True)
        return None

# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao import constants
from dao.dao_helper import DaoHelper
from proto.group_dining import combo_meal_pb2 as combo_meal_pb


class ComboMealDataAccessHelper(DaoHelper):
    """ 套餐
    """

    @property
    def __collection(self):
        db = constants.MONGODB_COMBO_MEAL_DATABASE_NAME
        collection = constants.MONGODB_COMBO_MEAL_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def get_combo_meals(self, merchant_id, page=None, size=None):
        """ 获取门店的所有套餐
        Args:
            store_id: (string)门店id
        Return:
            group_dining.combo_meal.ComboMeal结构体
        """
        match = {"merchantId": merchant_id}
        cursor = self.__collection.find(match)
        if page is not None and size is not None:
            skip = (page - 1) * size
            combo_meals = cursor.skip(skip).limit(size)
        else:
            combo_meals = cursor
        if combo_meals:
            return [json_format.ParseDict(combo_meal, combo_meal_pb.ComboMeal(),
                                          ignore_unknown_fields=True) for combo_meal in combo_meals]
        return []

    def get_combo_meal_by_id(self, combo_meal_id):
        """ 根据套餐id获取套餐
        Args:
            combo_meal_id: (string)套餐id
        Returns:
            group_dining.combo_meal.ComboMeal结构体
        """
        match = {"id": combo_meal_id}
        combo_meal = self.__collection.find_one(match)
        if combo_meal:
            return json_format.ParseDict(combo_meal, combo_meal_pb.ComboMeal(), ignore_unknown_fields=True)
        return None

    def add_combo_meal(self, combo_meal):
        """添加一个新的套餐信息。

        Args:
          combo_meal: (ComboMeal) 套餐信息结构体
        """
        combo_meal_json = json_format.MessageToDict(combo_meal, including_default_value_fields=True)
        self.__collection.insert_one(combo_meal_json)

    def update_or_create_combo_meal(self, combo_meal):
        """如果指定套餐当前不存在，则添加一个新的套餐信息。
           如果指定套餐存在，则更新该套餐信息。

        Args:
            combo_meal: (ComboMeal) 时来平台套餐信息结构体
        """
        matcher = {"id": combo_meal.id}
        meal = self.__collection.find_one(matcher)
        if meal:
            json_obj = json_format.MessageToDict(combo_meal, including_default_value_fields=True)
            self.__collection.replace_one(matcher, json_obj, upsert=True)
        else:
            self.add_combo_meal(combo_meal)

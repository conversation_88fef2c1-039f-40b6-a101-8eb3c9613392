# -*- coding: utf-8 -*-

from google.protobuf import json_format

import dao.constants as constants
import proto.payment_pb2 as payment_pb
import proto.chinaums_wechat_pay_pb2 as chinaums_wechat_pay_pb
from dao.dao_helper import DaoHelper


# Payment Data Access Helper，提供针对支付相关的数据访问服务。
class PaymentDataAccessHelper(DaoHelper):

    db = constants.MONGODB_PAYMENT_DATABASE_NAME

    @property
    def _tian_que_pay_temporary_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_TEMPORARY_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_pic_id_url_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_PIC_ID_URL_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_income_result_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_INCOME_RESULT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_income_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_INCOME_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_transfer_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_TRANSFER_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_ledger_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_LEDGER_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _chinaums_wechat_pay_config_collection(self):
        c = constants.MONGODB_CHINAUMS_WECHAT_PAY_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _chinaums_wechat_pay_sub_order_collection(self):
        c = constants.MONGODB_CHINAUMS_WECHAT_PAY_SUB_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _self_dining_payment_collection(self):
        c = constants.MONGODB_SELF_DINING_PAYMENT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_real_name_commit_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_REAL_NAME_COMMIT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_prepay_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_PREPAY_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_notification_info_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_NOTIFICATION_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _tian_que_pay_transfer_amount_collection(self):
        c = constants.MONGODB_TIAN_QUE_PAY_TRANSFER_AMOUNT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _chinaums_wechat_pay_order_collection(self):
        c = constants.MONGODB_CHINAUMS_WECHAT_PAY_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def get_payment(self, payment_id):
        """根据ID返回指定的支付记录。

        Args:
            payment_id: (string) 支付记录ID

        Returns:
            (Payment) 若指定支付记录存在，返回相应用户的Payment结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_PAYMENT_DATABASE_NAME]
        order = db[constants.MONGODB_PAYMENT_COLLECTION_NAME].find_one({'id': payment_id})
        if order:
            return json_format.ParseDict(order, payment_pb.Payment(), ignore_unknown_fields=True)
        return None

    def add_payment_transaction_id(self, payment):
        json_obj = json_format.MessageToDict(payment, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_PAYMENT_DATABASE_NAME]
        db[constants.MONGODB_PAYMENT_TRANSACTION_ID_COLLECTION_NAME].insert_one(json_obj)

    def add_payment(self, payment):
        """添加一个新的支付信息。

        Args:
            payment: (Payment) Payment信息结构体
        """
        json_obj = json_format.MessageToDict(payment, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_PAYMENT_DATABASE_NAME]
        db[constants.MONGODB_PAYMENT_COLLECTION_NAME].insert_one(json_obj)

    def update_payment(self, payment):
        """更新一个新的支付信息。

        Args:
            payment: (Payment) 需要更新的Payment信息结构体
        """
        json_obj = json_format.MessageToDict(payment, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_PAYMENT_DATABASE_NAME]
        db[constants.MONGODB_PAYMENT_COLLECTION_NAME].replace_one({'id': payment.id}, json_obj, upsert=True)

    def get_payments(self, merchant_id=None, store_id=None, user_id=None, status=None,
                     start_time=None, end_time=None):
        """根据指定的查询条件，获取商户下的支付记录列表

        Args:
            merchant_id: (String) 商户ID
            store_id: (String) 门店ID
            user_id: (string) 用户ID
            status: (PaymentStatus) 支付记录和状态
            start_time: (int) 所需查询的支付记录的起始时间
            end_time: (int) 所需查询的支付记录的结束时间
        Returns:
            (Payment[]) 商户下的支付记录列表
        """
        filter = {}
        if merchant_id is not None:
            filter['merchantId'] = merchant_id
        if store_id is not None:
            filter['storeId'] = store_id
        if user_id is not None:
            filter['userId'] = user_id
        if status is not None:
            filter['status'] = payment_pb.Payment.PaymentStatus.Name(status)
        if start_time is not None or end_time is not None:
            filter['paidTime'] = {}
        if start_time is not None:
            filter['paidTime']["$gt"] = str(start_time)
        if end_time is not None:
            filter['paidTime']["$lt"] = str(end_time)

        db = self.mongo_client[constants.MONGODB_PAYMENT_DATABASE_NAME]
        payments = db[constants.MONGODB_PAYMENT_COLLECTION_NAME].find(filter)
        if payments:
            return [json_format.ParseDict(payment, payment_pb.Payment(),
                                          ignore_unknown_fields=True) for payment in payments]
        else:
            return None

    def add_or_update_tian_que_pay_temporary_info(self, info):
        matcher = {"merchantId": info.merchant_id}
        json_obj = json_format.MessageToDict(info, including_default_value_fields=True)
        self._tian_que_pay_temporary_info_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_tian_que_pay_temporary_info(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        info = self._tian_que_pay_temporary_info_collection.find_one(matcher)
        if not info:
            return None
        return json_format.ParseDict(info, payment_pb.TianQuePayTemporaryInfo(), ignore_unknown_fields=True)

    def get_tian_que_pay_temporary_infos(self, status=None):
        matcher = {"status": payment_pb.TianQuePayTemporaryInfo.Status.Name(status)}
        infos = self._tian_que_pay_temporary_info_collection.find(matcher)
        if not infos:
            return None
        return [json_format.ParseDict(info, payment_pb.TianQuePayTemporaryInfo(), ignore_unknown_fields=True)
                for info in infos]

    def add_or_update_tian_que_pay_info(self, tian_que_pay_info):
        matcher = {"matcherId": tian_que_pay_info.merchant_id}
        json_obj = json_format.MessageToDict(tian_que_pay_info, including_default_value_fields=True)
        self._tian_que_pay_info_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_tian_que_pay_info(self, merchant_id=None, mno=None, state=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if mno is not None:
            matcher.update({"mno": mno})
        if state is not None:
            matcher.update({"state": payment_pb.TianQuePayInfo.State.Name(state)})
        if not matcher:
            return None
        tian_que_pay_info = self._tian_que_pay_info_collection.find_one(matcher)
        if tian_que_pay_info:
            return json_format.ParseDict(tian_que_pay_info, payment_pb.TianQuePayInfo(), ignore_unknown_fields=True)
        return None

    def get_tian_que_pay_infos(self, merchant_id=None, mno=None, state=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if mno is not None:
            matcher.update({"mno": mno})
        if state is not None:
            matcher.update({"state": payment_pb.TianQuePayInfo.State.Name(state)})
        if not matcher:
            return None
        tian_que_pay_infos = self._tian_que_pay_info_collection.find(matcher)
        if tian_que_pay_infos:
            return [json_format.ParseDict(tian_que_pay_info, payment_pb.TianQuePayInfo(), ignore_unknown_fields=True)
                    for tian_que_pay_info in tian_que_pay_infos]
        return None

    def add_tian_que_pay_prepay_info(self, data):
        if not isinstance(data, dict):
            data = json_format.MessageToDict(data, including_default_value_fields=True)
        self._tian_que_pay_prepay_info_collection.insert(data)

    def get_tian_que_pay_prepay_info(self, transaction_id):
        matcher = {"respData.transactionId": transaction_id}
        prepay_info = self._tian_que_pay_prepay_info_collection.find_one(matcher)
        if prepay_info:
            return json_format.ParseDict(prepay_info, payment_pb.TianQuePayPrepayInfo(), ignore_unknown_fields=True)
        return None

    def add_tian_que_pay_notification_info(self, data):
        if not isinstance(data, dict):
            data = json_format.MessageToDict(data, including_default_value_fields=True)
        transaction_id = data.get("transactionId")
        matcher = {"transactionId": transaction_id}
        self._tian_que_pay_notification_info_collection.update(matcher, {"$set": data}, upsert=True)

    def get_tian_que_pay_notification_info(self, uuid=None, ord_no=None):
        if uuid is not None:
            matcher = {"uuid": uuid}
        if ord_no is not None:
            matcher = {"ordNo": ord_no}
        notification_info = self._tian_que_pay_notification_info_collection.find_one(matcher)
        if not notification_info:
            return None
        return json_format.ParseDict(notification_info, payment_pb.TianQuePayNotificationInfo(), ignore_unknown_fields=True)

    def add_or_update_real_name_commit(self, commit):
        if not isinstance(commit, dict):
            commit = json_format.MessageToDict(commit, including_default_value_fields=True)
        matcher = {"mechantId": commit.get("merchantId")}
        self._tian_que_pay_real_name_commit_collection.update(matcher, {"$set": commit}, upsert=True)

    def get_tian_que_pay_real_name_commit(self, wx_apply_id=None, merchant_id=None):
        matcher = {}
        if wx_apply_id is not None:
            matcher.update({"wxApplyId": wx_apply_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})

        if not matcher:
            return None
        commit = self._tian_que_pay_real_name_commit_collection.find_one(matcher)
        if not commit:
            return None
        return json_format.ParseDict(commit, payment_pb.TianQuePayRealNameCommit(), ignore_unknown_fields=True)

    def add_or_update_self_dining_payment(self, payment):
        if not isinstance(payment, dict):
            payment = json_format.MessageToDict(payment, including_default_value_fields=True)
        matcher = {"id": payment.get("id")}
        self._self_dining_payment_collection.update(matcher, {"$set": payment}, upsert=True)

    def get_self_dining_payment(self, id=None, user_id=None, transaction_id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if not matcher:
            return None
        payment = self._self_dining_payment_collection.find_one(matcher)
        if not payment:
            return None
        return json_format.ParseDict(payment, payment_pb.SelfDiningPayment(), ignore_unknown_fields=True)

    def get_chinaums_wechat_pay_config(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        config = self._chinaums_wechat_pay_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, chinaums_wechat_pay_pb.ChinaumsWechatPayConfig(), ignore_unknown_fields=True)

    def add_or_update_chinaums_wechat_pay_config(self, config):
        matcher = {"merchantId": config.merchant_id}
        json_config = json_format.MessageToDict(config, including_default_value_fields=True)
        self._chinaums_wechat_pay_config_collection.update(matcher, {"$set": json_config}, upsert=True)

    def get_chinaums_wechat_pay_configs(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        configs = self._chinaums_wechat_pay_config_collection.find(matcher)
        if not configs:
            return None
        return [json_format.ParseDict(config, chinaums_wechat_pay_pb.ChinaumsWechatPayConfig(), ignore_unknown_fields=True) for config in configs]

    def add_or_update_chinaums_wechat_pay_sub_order(self, sub_order):
        matcher = {"transactionId": sub_order.transaction_id}
        json_data = json_format.MessageToDict(sub_order, including_default_value_fields=True)
        self._chinaums_wechat_pay_sub_order_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_chinaums_wechat_pay_bus_orders(self, transaction_ids=None):
        matcher = {}
        if transaction_ids is not None:
            matcher.update({"transactionId": {"$in": transaction_ids}})
        if not matcher:
            return None
        sub_orders = self._chinaums_wechat_pay_sub_order_collection.find(matcher)
        return [json_format.ParseDict(sub_order, chinaums_wechat_pay_pb.ChinaumsWechatPaySubOrder(), ignore_unknown_fields=True) for sub_order in sub_orders]

    def add_or_update_chinaums_wechat_pay_order(self, order):
        matcher = {"transactionId": order.transaction_id}
        json_data = json_format.MessageToDict(order, including_default_value_fields=True)
        self._chinaums_wechat_pay_order_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_chinaums_wechat_pay_order(self, transaction_id=None, target_order_id=None, mer_order_id=None):
        matcher = {}
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if target_order_id is not None:
            matcher.update({"targetOrderId": target_order_id})
        if mer_order_id is not None:
            matcher.update({"merOrderId": mer_order_id})
        if not matcher:
            return None
        orders = self._chinaums_wechat_pay_order_collection.find(matcher)
        if not orders:
            return None
        return [json_format.ParseDict(order, chinaums_wechat_pay_pb.ChinaumsWechatPayOrder(), ignore_unknown_fields=True)
                for order in orders]

    def add_or_update_tian_que_pay_ledger_info(self, ledger):
        matcher = {"ordNo": ledger.ord_no}
        json_data = json_format.MessageToDict(ledger, including_default_value_fields=True)
        self._tian_que_pay_ledger_info_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_tian_que_pay_ledger_infos(self, transaction_id=None, status=None):
        matcher = {}
        if transaction_id is not None:
            matcher.update({"ordNo": transaction_id})
        if status is not None:
            matcher.update({"status": payment_pb.TianQuePayLedgerInfo.Status.Name(status)})
        if not matcher:
            return None
        ledgers = self._tian_que_pay_ledger_info_collection.find(matcher)
        if not ledgers:
            return None
        return [json_format.ParseDict(ledger, payment_pb.TianQuePayLedgerInfo(), ignore_unknown_fields=True)
                for ledger in ledgers]

    def add_or_update_tian_que_pay_transfer_info(self, transfer):
        matcher = {"orderNo": transfer.order_no}
        json_data = json_format.MessageToDict(transfer, including_default_value_fields=True)
        self._tian_que_pay_transfer_info_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_tian_que_pay_transfer_infos(self, order_no=None, transaction_no=None):
        matcher = {}
        if order_no is not None:
            matcher.update({"orderNo": order_no})
        if transaction_no is not None:
            matcher.update({"transactionNo": transaction_no})
        if not matcher:
            return None
        transfers = self._tian_que_pay_transfer_info_collection.find(matcher)
        if not transfers:
            return None
        return [json_format.ParseDict(transfer, payment_pb.TianQuePayTransferInfo(), ignore_unknown_fields=True)
                for transfer in transfers]

    def increase_tian_que_pay_transfer_amount(self, fee):
        matcher = {}
        udata = {"$inc": {"fee": fee}}
        self._tian_que_pay_transfer_amount_collection.update(matcher, udata, upsert=True)

    def add_or_update_tian_que_pay_income_info(self, info):
        matcher = {"merchantId": info.get("merchantId")}
        self._tian_que_pay_income_info_collection.update(matcher, info, upsert=True)

    def get_tian_que_pay_income_info(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        info = self._tian_que_pay_income_info_collection.find_one(
            matcher, {"_id": 0})
        return info

    def add_or_update_tian_que_pay_income_result(self, result):
        matcher = {'merchantId': result.get("merchantId")}
        self._tian_que_pay_income_result_collection.update(matcher, result, upsert=True)

    def get_tian_que_pay_income_result(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        result = self._tian_que_pay_income_result_collection.find_one(
            matcher, {"_id": 0})
        return result

    def update_tian_que_pic_id_url(self, merchant_id, id, url):
        self._tian_que_pay_pic_id_url_collection.insert({"id": id, "url": url})

    def get_tian_que_pic_id_url(self, id):
        matcher = {"id": id}
        id_url = self._tian_que_pay_pic_id_url_collection.find_one(matcher)
        return id_url

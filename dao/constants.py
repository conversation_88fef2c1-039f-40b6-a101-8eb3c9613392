# 与后端数据访问相关的配置变量

# TODO: 考虑将以下动态配置信息以环境变量形式传入(尤其是credentials相关信息)
MONGODB_SERVER_ADDRESS = '*************'
MONGODB_PORT = 8881
MONGODB_USER_NAME = ''
MONGODB_ROOT_PASSWORD = ''

# 时来平台授权相关数据库
MONGODB_AUTH_DATABASE_NAME = 'auth_db'
MONGODB_AUTHORIZER_ACCESS_TOKEN_COLLECTION_NAME = 'authorizer_access_token'
MONGODB_AUTHORIZER_API_TICKET_COLLECTION_NAME = 'api_ticket'
MONGODB_COMPONENT_ACCESS_TOKEN_COLLECTION_NAME = 'component_access_token'
MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME = 'component_verify_tickets'

# 时来平台用户相关数据库
MONGODB_USER_DATABASE_NAME = 'user_db'
MONGODB_USER_COLLECTION_NAME = 'users'
MONGODB_MERCHANT_USER_COLLECTION_NAME = 'merchant_users'
MONGODB_STAFF_COLLECTION_NAME = 'staffs'
MONGODB_QRLOGIN_COLLECTION_NAME = 'qrcode_login_record'
MONGODB_USER_CONFIG_NAME = 'user_config'
# 业务各个菜品模块
MONGODB_STAFF_ASSIST_MENU_COLLECTION_NAME = "staff_assist_menus"
# 用户平台设置
MONGODB_USER_PLATFORM_SETTING_COLLECTION_NAME = "user_platform_settings"

# 商户相关数据库
MONGODB_BUSINESS_ENTITIES_DATABASE_NAME = 'business_entities_db'
MONGODB_MERCHANT_COLLECTION_NAME = 'merchants'
MONGODB_STORE_BANK_CARDS_COLLECTION_NAME = 'store_bank_cards'
MONGODB_HUALALA_GROUP_COLLECTION_NAME = "hualala_groups"
MONGODB_HUALALA_STORE_COLLECTION_NAME = "hualala_stores"
MONGODB_BRAND_COLLECTION_NAME = "brands"
MONGODB_CODE_PLATE_BINDING_COLLECTION_NAME = 'code_plate_binding'
# 商家应用组件配置
MONGODB_APP_COMPONENT_CONFIG_COLLECTION_NAME = "app_component_configs"

# 会员管理相关数据库
MONGODB_MEMBERSHIP_DATABASE_NAME = 'membership_db'
MONGODB_CARD_CATEGORY_COLLECTION_NAME = 'member_card_categories'
MONGODB_MEMBER_CARD_COLLECTION_NAME = 'member_cards'
MONGODB_MEMBER_CARD_RECHARGE_CONFIG_COLLECTION_NAME = 'member_card_recharge_configs'
MONGODB_KERUYUN_MEMBER_CARD_BALANCE_COLLECTION_NAME = 'keruyun_member_card_balances'

# 优惠券管理相关数据库
MONGODB_COUPON_DATABASE_NAME = 'coupon_db'
MONGODB_COUPON_CATEGORY_COLLECTION_NAME = 'coupon_categories'
MONGODB_COUPON_COLLECTION_NAME = 'coupons'
MONGODB_COUPON_PACKAGE_COLLECTION_NAME = 'coupon_packages'

MONGODB_PROMOTION_DATABASE_NAME = 'promotion_db'
MONGODB_COUPON_TEMPLATE_COLLECTION_NAME = 'coupon_templates'
MONGODB_COUPON_COLLECTION_V2_NAME = 'coupons'

# 支付管理相关数据库
MONGODB_PAYMENT_DATABASE_NAME = 'payment_db'
MONGODB_PAYMENT_COLLECTION_NAME = 'payments'
MONGODB_PAYMENT_TRANSACTION_ID_COLLECTION_NAME = 'payment_transaction_ids'
MONGODB_TIAN_QUE_PAY_INFO_COLLECTION_NAME = "tian_que_pay_infos"
MONGODB_TIAN_QUE_PAY_TEMPORARY_INFO_COLLECTION_NAME = "tian_que_pay_temporary_infos"
MONGODB_TIAN_QUE_PAY_PREPAY_INFO_COLLECTION_NAME = "tian_que_pay_prepay_infos"
MONGODB_TIAN_QUE_PAY_NOTIFICATION_INFO_COLLECTION_NAME = "tian_que_pay_notification_infos"
MONGODB_TIAN_QUE_PAY_REAL_NAME_COMMIT_COLLECTION_NAME = "tian_que_pay_real_name_commits"
# 天阙支付分账信息
MONGODB_TIAN_QUE_PAY_LEDGER_INFO_COLLECTION_NAME = "tian_que_pay_ledger_infos"
# 天阙转账信息
MONGODB_TIAN_QUE_PAY_TRANSFER_INFO_COLLECTION_NAME = "tian_que_pay_transfer_infos"
# 通过天阙支付已转账总金额
MONGODB_TIAN_QUE_PAY_TRANSFER_AMOUNT_COLLECTION_NAME = "tian_que_pay_transfer_amount"
# 天阙商户入驻信息
MONGODB_TIAN_QUE_PAY_INCOME_INFO_COLLECTION_NAME = "tian_que_pay_income_infos"
# 天阙商户入驻返回信息
MONGODB_TIAN_QUE_PAY_INCOME_RESULT_COLLECTION_NAME = "tian_que_pay_income_results"
# 天阙商户入驻时,图片ID与图片URL的对应关系
MONGODB_TIAN_QUE_PAY_PIC_ID_URL_COLLECTION_NAME = "tian_que_pay_pic_id_urls"
MONGODB_SELF_DINING_PAYMENT_COLLECTION_NAME = "self_dining_payments"
# 银商支付相关配置
MONGODB_CHINAUMS_WECHAT_PAY_CONFIG_COLLECTION_NAME = "chinaums_wechat_pay_configs"
# 银商支付分账子订单
MONGODB_CHINAUMS_WECHAT_PAY_SUB_ORDER_COLLECTION_NAME = "chinaums_wechat_pay_sub_orders"
# 银商支付订单详情
MONGODB_CHINAUMS_WECHAT_PAY_ORDER_COLLECTION_NAME = "chinaums_wechat_pay_orders"

# 其他杂项类型数据库
MONGODB_MISC_DATABASE_NAME = 'misc_db'
MONGODB_STAFF_QRCODE_COLLECTION_NAME = 'staff_qrcodes'
MONGODB_MERCHANT_QRCODE_COLLECTION_NAME = 'merchant_qrcodes'
MONGODB_MERCHANT_TABLE_QRCODE_COLLECTION_NAME = 'merchant_table_qrcodes'
MONGODB_MERCHANT_HOME_CODE_COLLECTION_NAME = 'merchant_home_codes'
MONGODB_GROUP_DINING_EVENT_CODE_COLLECTION_NAME = 'group_dining_event_codes'
MONGODB_ORDERING_GROUP_DINING_EVENT_CODE_COLLECTION_NAME = 'ordering_group_dining_event_codes'
MONGODB_ORDERING_CODE_COLLECTION_NAME = 'ordering_qrcodes'
MONGODB_FEEDBACK_COLLECTION_NAME = 'feedbacks'
MONGODB_REPORT_COLLECTION_NAME = 'reports'
MONGODB_SELF_DINING_DISCOUNT_CODE_COLLECTION_NAME = 'self_dining_discount_codes'
MONGODB_MINI_PROGRAM_FORM = 'mini_program_form'
MONGODB_MERCHANT_ASSIST_LOGIN_QRCODES = 'merchant_assist_login_qrcodes'
# 记录前端的日志
MONGODB_FRONTEND_LOG_COLLECTION_NAME = "frontend_logs"
MONGODB_QRCODE_COLLECTION_NAME = "qrcodes"
MONGODB_DIRECT_PAY_QRCODE_COLLECTION_NAME = "direct_pay_qrcodes"

# 约饭相关数据库
MONGODB_GROUP_DINING_EVENT_DATABASE_NAME = 'group_dining_event_db'
MONGODB_GROUP_DINING_EVENT_COLLECTION_NAME = 'group_dining_event'  # 饭局
MONGODB_GROUP_DINING_EVENT_INVITATION_COLLECTION_NAME = 'invitations'  # 邀请
MONGODB_GROUP_DINING_RED_PACKET_COLLECTION_NAME = 'red_packets'
MONGODB_TEMP_RED_PACKET_VALUE_COLLECTION_NAME = 'temp_red_packet_value'  # 临时存储红包金额

# 钱包数据库
MONGODB_WALLET_DATABASE_NAME = 'wallet_db'
MONGODB_WALLET_COLLECTION_NAME = 'wallets'
MONGODB_USER_ASSET_ACCOUNT_NAME = 'user_asset_accounts'

# Transaction相关数据库
MONGODB_TRANSACTION_DATABASE_NAME = 'transaction_db'
MONGODB_TRANSACTION_COLLECTION_NAME = 'transaction'
MONGODB_TRANSACTION_TRANSFER_STORE_COLLECTION_NAME = 'transfer_store_transaction'
MONGODB_TRANSACTION_MERCHANT_TRANSFERS_COLLECTION_NAME = 'merchant_transfers'
# 通过红包支付,饭票支付,等方式支付的话需要记录在此表,然后通过转账接口转给商户
MONGODB_TRANSACTION_MERCHANT_DAY_TRANSFER_COLLECTION_NAME = 'merchant_day_transfers'
# 交易流水汇总
MONGODB_TRANSACTION_STATS_COLLECTION_NAME = 'transaction_stats'
# 订单,券包联合支付的transaction映射表
MONGODB_TRANSACTION_ORDERING_COUPON_PACKAGE_UNION_PAY_COLLECTION_NAME = "ordering_coupon_package_transactions"
# 合并支付时记录支付流水ID和子流水ID
MONGODB_UNION_PAY_TRANSACTION_COLLECTION_NAME = 'union_pay_transactions'
# 记录需要延迟退款处理的订单
MONGODB_DELAY_REFUND_TRANSACTION_COLLECTION_NAME = "delay_refund_transactions"
# 在处理回调的时候,有可能transaction还没有同步到从库,会导致回调执行失败,把这样的失败记录下来做重试
MONGODB_INEXISTENCE_TRANSACTION_COLLECTION_NAME = "inexistence_transactions"

# 套餐相关数据库
MONGODB_COMBO_MEAL_DATABASE_NAME = 'combo_meal_db'
MONGODB_COMBO_MEAL_COLLECTION_NAME = 'combo_meals'

# 消息相关数据库
MONGODB_CHAT_MESSAGE_DATABASE_NAME = 'chat_message_db'
MONGODB_GROUP_DINING_CHAT_MESSAGE_COLLECTION_NAME = 'group_dining_chat_messages'
MONGODB_GROUP_DINING_APPLY_FOR_MESSAGE_COLLECTION_NAME = 'apply_for_group_dining_messages'
MONGODB_GROUP_DINING_APPLY_FOR_MESSAGE_VIEW_TIME_COLLECTION_NAME = 'apply_for_group_dining_messages_view_time'
MONGODB_PRIVATE_MESSAGE_COLLECTION_NAME = 'private_messages'
MONGODB_PRIVATE_MESSAGE_VIEW_TIME_COLLECTION_NAME = 'private_message_view_time'
MONGODB_SYSTEM_NOTIFICATION_COLLECTION_NAME = 'system_notification'
MONGODB_SYSTEM_NOTIFICATION_VIEW_TIME_COLLECTION_NAME = 'system_notification_view_time'

# 点菜相关数据库
MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME = 'ordering_service'
MONGODB_ORDERING_SERVICE_INFO_COLLECTION_NAME = 'ordering_services'
MONGODB_ORDERING_SERVICE_DISH_CATEGORY_COLLECTION_NAME = 'dish_categories'
MONGODB_ORDERING_SERVICE_DISH_COLLECTION_NAME = 'dishes'
MONGODB_ORDERING_SERVICE_TABLE_COLLECTION_NAME = 'tables'
MONGODB_ORDERING_SERVICE_TABLE_AREA_COLLECTION_NAME = "table_areas"
MONGODB_ORDERING_SERVICE_ORDER_COLLECTION_NAME = 'orders'
MONGODB_ORDERING_SERVICE_ORDER_PARTIAL_REFUND_RECORD_COLLECTION_NAME = (
    "order_partial_refund_records"  # 部分退菜时记录下退的菜与transaction的关系
)
# 订单操作记录
MONGODB_ORDERING_SERVICE_ORDER_OPERATION_RECORD = "order_operation_records"
MONGODB_ORDERING_SERVICE_FAILED_ORDER_COLLECTION_NAME = "failed_orders"
MONGODB_ORDERING_SERVICE_KERUYUN_ORDER_COLLECTION_NAME = 'keruyun_orders'
MONGODB_ORDERING_SERVICE_KERUYUN_TAKE_OUT_ORDER_COLLECTION_NAME = 'keruyun_take_out_orders'
MONGODB_ORDERING_SERVICE_KERUYUN_FAST_FOOD_ORDER_COLLECTION_NAME = 'keruyun_fast_food_orders'
MONGODB_ORDERING_SERVICE_DISCOUNT_PLAN_COLLECTION_NAME = 'discount_plans'
MONGODB_ORDERING_SERVICE_ADDITIONAL_DISH_ORDER_COLLECTION_NAME = 'additional_dish_order'
MONGODB_ORDERING_SERVICE_LATEST_KERUYUN_ORDER_ID = 'latest_keruyun_order_id'
MONGODB_ORDERING_SERVICE_DISH_CATEGORY_PRINTER = 'dish_category_printer'
MONGODB_ORDERING_KERUYUN_TABLE_MAP = 'keruyun_table_map'
# 公众号二维码与桌台码对应关系
MONGODB_MPQRCODE_TABLE_MAP_COLLECTION_NAME = "mpqrcode_table_maps"
MONGODB_ORDERING_HUALALA_ORDER_COLLECTION_NAME = "hualala_orders"
# 菜品名与图片的映射
MONGODB_ORDERING_DISH_IMAGES_COLLECTION_NAME = "dish_images"
MONGODB_ORDERING_ATTR_GROUP_COLLECTION_NAME = "dish_attr_groups"
MONGODB_DISH_SALE_TIME_COLLECTION_NAME = "dish_sale_time"
MONGODB_ORDERING_SUPPLY_CONDIMENT_COLLECTION_NAME = "dish_supply_condiments"
# 商户手机会员表
MONGODB_ORDERING_MERCHANT_PHONE_MEMBER_COLLECTION_NAME = "merchant_phone_members"

# 物流相关数据库
MONGODB_LOGISTICS_DATABASE_NAME = 'logistics_db'
MONGODB_DADA_ORDER_COLLECTION_NAME = 'dada_orders'
MONGODB_DADA_ORDER_STATUS_COLLECTION_NAME = 'dada_order_status'

# 投喂相关数据库
MONGODB_IFEEDU_DATABASE_NAME = "ifeedu_db"
MONGODB_IFEEDU_CONFIG_COLLECTION_NAME = "configs"
MONGODB_IFEEDU_WISHLIST_COLLECTION_NAME = "wish_lists"
MONGODB_IFEEDU_FEED_PLAN_COLLECTION_NAME = "feed_plans"
MONGODB_IFEEDU_FEED_STATS_COLLECTION_NAME = "feed_statses"
MONGODB_IFEEDU_USER_RELATIONSHIP_COLLECTION_NAME = 'user_relationships'
MONGODB_IFEEDU_MERCHANT_WISHLIST_DISH_COLLECTION_NAME = 'merchant_wishlist_dish'

# 数据分析相关数据库
MONGODB_BI_DATABASE_NAME = "bi_db"
MONGODB_BI_CLICK_EVENT_COLLECTION_NAME = "click_events"
# 每日营收数据
MONGODB_DAY_REVENUE_COLLECTION_NAME = "day_revenues"

# 评论相关
MONGODB_COMMENT_DATABASE_NAME = "comment_db"
MONGODB_COMMENT_COLLECTION_NAME = "comments"

# 饭票
MONGODB_FANPIAO_DATABASE_NAME = "fanpiao_db"
MONGODB_FANPIAO_CATEGORY_COLLECTION_NAME = "fanpiao_categories"
MONGODB_FANPIAO_COLLECTION_NAME = "fanpiaos"
MONGODB_FANPIAO_SCAN_QRCODE_COLLECTION_NAME = "fanpiao_scan_qrcodes"
MONGODB_FANPIAO_RISK_CONTROL_CONFIG_NAME = "fanpiao_risk_control_configs"
MONGODB_FANPIAO_RECORD_LOGS = "fanpiao_record_logs"
MONGODB_FANPIAO_REFUND_RECORDS = "fanpiao_refund_records"

SUCCESS_CODE = 0
FAILURE_CODE = 1

# 核销码相关
MONGODB_VERIFICATION_CODE_DATABASE_NAME = 'verification_code_db'
MONGODB_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME = 'verification_code_strategies'
MONGODB_VERIFICATION_CODE_COLLECTION_NAME = 'verification_codes'
MONGODB_DISH_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME = 'dish_verification_code_strategies'
MONGODB_DISH_VERIFICATION_CODE_COLLECTION_NAME = 'dish_verification_codes'
MONGODB_BRAND_DISH_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME = 'brand_dish_verification_code_strategies'
MONGODB_BRAND_DISH_VERIFICATION_CODE_COLLECTION_NAME = 'brand_dish_verification_codes'

# 活动相关
MONGODB_ACTIVITY_DATABASE_NAME = 'activity_db'
MONGODB_INVITE_SHARE_COLLECTION_NAME = "invite_shares"
MONGODB_INVITEE_SHARE_COLLECTION_NAME = "invitee_shares"
MONGODB_ACTIVITY_SUBSCRIBE_INFO_COLLECTION_NAME = "activity_subscribe_infos"

# 配置相关
MONGODB_CONFIG_DATABASE_NAME = "config_db"
# 一些常规配置
# 现在配置主要放在merchant_rules,registration_info下面
# 后面考虑把配置规整到此表
MONGODB_COMMON_CONFIG_COLLECTION_NAME = "common_configs"
# 一些配置的最近更新时间戳
MONGODB_UPDATE_TIMESTAMP_COLLECTION_NAME = "update_timestamps"
# 邀请分享活动的配置
MONGODB_INVITE_SHARE_CONFIG_COLLECTION_NAME = "invite_share_configs"
MONGODB_FANPIAO_CONFIG_COLLECTION_NAME = "fanpiao_configs"
MONGODB_COUPON_PACKAGE_CONFIG_COLLECTION_NAME = "coupon_package_configs"
MONGODB_ORDERING_CONFIG_COLLECTION_NAME = "ordering_configs"
MONGODB_PRINTER_CONFIG_COLLECTION_NAME = "printer_configs"
MONGODB_QRCODE_PRINTER_CONFIG_COLLECTION_NAME = "qrcode_printer_configs"
MONGODB_ACTIVITY_CONFIG_COLLECTION_NAME = "activity_configs"
MONGODB_MERCHANT_ACTIVITY_CONFIG_COLLECTION_NAME = "merchant_activity_configs"
# 平台全局配置
MONGODB_PLATFORM_GLOBAL_CONFIG_COLLECTION_NAME = "platform_global_configs"
# 时享会员配置
MONGODB_MERCHANT_VIP_MEMBERSHIP_CONFIG_COLLECTION_NAME = "merchant_vip_membership_config"
# 营销配置
MONGODB_PROMOTION_CONFIG_COLLECTION_NAME = "promotion_configs"

# 团购
MONGODB_GROUP_PURCHASE_DB_NAME = 'group_purchase_db'
MONGODB_GROUP_PURCHASE_TEMPLATE_COLL_NAME = 'group_purchase_templates'
MONGODB_GROUP_PURCHASE_COLL_NAME = 'group_purchases'
MONGODB_GROUP_PURCHASE_INVITATION_COLL_NAME = 'group_purchase_invitations'

# 消息中心
MONGODB_MESSAGE_CENTER_DB_NAME = "message_center_db"
# 消息
MONGODB_MESSAGE_COLL_NAME = "messages"
# 操作记录
MONGODB_OPERATION_NAME = 'operations'

# -*- coding: utf-8 -*-


from google.protobuf import json_format

import proto.config_pb2 as config_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class ConfigDataAccessHelper(DaoHelper):

    db = constants.MONGODB_CONFIG_DATABASE_NAME

    @property
    def _promotion_config_collection(self):
        c = constants.MONGODB_PROMOTION_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _platform_global_config_collection(self):
        c = constants.MONGODB_PLATFORM_GLOBAL_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _merchant_vip_membership_config(self):
        c = constants.MONGODB_MERCHANT_VIP_MEMBERSHIP_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _activity_subscribe_info_collection(self):
        c = constants.MONGODB_ACTIVITY_SUBSCRIBE_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _merchant_activity_config_collection(self):
        c = constants.MONGODB_MERCHANT_ACTIVITY_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _activity_config_collection(self):
        c = constants.MONGODB_ACTIVITY_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _qrcode_printer_config_collection(self):
        c = constants.MONGODB_QRCODE_PRINTER_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _ordering_config_collection(self):
        c = constants.MONGODB_ORDERING_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _common_config_collection(self):
        c = constants.MONGODB_COMMON_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _update_timestamps_collection(self):
        c = constants.MONGODB_UPDATE_TIMESTAMP_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _invite_share_config_collection(self):
        c = constants.MONGODB_INVITE_SHARE_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _fanpiao_config_collection(self):
        c = constants.MONGODB_FANPIAO_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _coupon_package_config_collection(self):
        c = constants.MONGODB_COUPON_PACKAGE_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def get_platform_global_config(self):
        result = self._platform_global_config_collection.find_one()
        return json_format.ParseDict(result, config_pb.PlatformGlobalConfig(), ignore_unknown_fields=True)

    def add_or_update_vip_membership_config(self, vip_membership_config):
        matcher = {"merchantId": vip_membership_config.merchant_id}
        json_data = json_format.MessageToDict(vip_membership_config, including_default_value_fields=True)
        self._merchant_vip_membership_config.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_vip_membership_config(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        result = self._merchant_vip_membership_config.find_one(matcher)
        if not result:
            return None
        return json_format.ParseDict(result, config_pb.VipMembershipConfig(), ignore_unknown_fields=True)

    def add_or_update_common_config(self, config):
        merchant_id = config.merchant_id
        matcher = {
            "merchantId": merchant_id
        }
        config_json = json_format.MessageToDict(config, including_default_value_fields=True)
        self._common_config_collection.update(matcher, {"$set": config_json}, upsert=True)

    def get_common_config(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        config = self._common_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, config_pb.CommonConfig(), ignore_unknown_fields=True)

    from dao.merchant_da_helper import DishCatalogCache
    
    @DishCatalogCache.update
    def add_or_update_ordering_config(self, config):
        merchant_id = config.merchant_id
        matcher = {
            "merchantId": merchant_id
        }
        config_json = json_format.MessageToDict(config, including_default_value_fields=True)
        self._ordering_config_collection.update(matcher, {"$set": config_json}, upsert=True)

    def get_ordering_config(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        config = self._ordering_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, config_pb.OrderingConfig(), ignore_unknown_fields=True)

    def add_or_update_fanpiao_config(self, config):
        matcher = {
            "merchantId": config.merchant_id
        }
        config_json = json_format.MessageToDict(config, including_default_value_fields=True)
        self._fanpiao_config_collection.update(matcher, {"$set": config_json}, upsert=True)

    def get_fanpiao_config(self, merchant_id):
        matcher = {
            "merchantId": merchant_id
        }
        config = self._fanpiao_config_collection.find_one(matcher)
        if not config:
            return None
        config = json_format.ParseDict(config, config_pb.FanpiaoConfig(), ignore_unknown_fields=True)
        return config

    def add_or_update_coupon_package_config(self, config):
        matcher = {"merchantId": config.merchant_id}
        config_json = json_format.MessageToDict(config, including_default_value_fields=True)
        self._coupon_package_config_collection.update(matcher, {"$set": config_json}, upsert=True)

    def get_coupon_package_config(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        config = self._coupon_package_config_collection.find_one(matcher)
        if not config:
            return None
        config = json_format.ParseDict(config, config_pb.CouponPackageConfig(), ignore_unknown_fields=True)
        return config

    def get_invite_share_config(self, merchant_id):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        config = self._invite_share_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, config_pb.InviteShareConfig(), ignore_unknown_fields=True)

    def add_or_update_invite_share_config(self, config):
        matcher = {"merchantId": config.merchant_id}
        json_obj = json_format.MessageToDict(config, including_default_value_fields=True)
        self._invite_share_config_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def add_or_update_update_timestamp(self, config):
        matcher = {"merchantId": config.merchant_id}
        json_obj = json_format.MessageToDict(config, including_default_value_fields=True)
        self._update_timestamps_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_update_timestamp(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        config = self._update_timestamps_collection.find_one(matcher)
        if config:
            return json_format.ParseDict(config, config_pb.UpdateTimestamp(), ignore_unknown_fields=True)
        return None

    def get_qrcode_printer_configs(self):
        matcher = {}
        configs = self._qrcode_printer_config_collection.find(matcher)
        return [json_format.ParseDict(config, config_pb.QrcodePrinterConfig(), ignore_unknown_fields=True)
                for config in configs]

    def add_or_update_qrcode_printer_config(self, config):
        matcher = {"id": config.id}
        json_data = json_format.MessageToDict(config, including_default_value_fields=True)
        self._qrcode_printer_config_collection.update(matcher, {"$set": json_data}, upsert=True)

    def add_or_update_activity_config(self, config):
        matcher = {"id": config.id}
        json_data = json_format.MessageToDict(config, including_default_value_fields=True)
        self._activity_config_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_current_activity_by_type(self, now_timestamp, activity_type):
        if not isinstance(activity_type, str):
            activity_type = config_pb.ActivityConfig.ActivityType.Name(activity_type)

        matcher = {
            "startTimestamp": {"$lt": str(now_timestamp)},
            "endTimestamp": {"$gt": str(now_timestamp)},
            "activityType": activity_type
        }
        activity = self._activity_config_collection.find_one(matcher)
        if not activity:
            return None
        return json_format.ParseDict(activity, config_pb.ActivityConfig(), ignore_unknown_fields=True)

    def get_activity_config(self, activity_id=None):
        matcher = {}
        if activity_id is not None:
            matcher.update({"id": activity_id})
        if not matcher:
            return None
        config = self._activity_config_collection.find_one(matcher)
        if config:
            return json_format.ParseDict(config, config_pb.ActivityConfig(), ignore_unknown_fields=True)
        return None

    def get_activity_configs(self, activity_ids=None):
        matcher = {}
        if activity_ids is not None:
            matcher.update({
                "id": {"$in": activity_ids}
            })
        configs = self._activity_config_collection.find(matcher)
        return [json_format.ParseDict(config, config_pb.ActivityConfig(), ignore_unknown_fields=True)
                for config in configs]

    def add_or_update_merchant_activity_config(self, config):
        matcher = {"merchantId": config.merchant_id}
        json_data = json_format.MessageToDict(config, including_default_value_fields=True)
        self._merchant_activity_config_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_merchant_activity_configs(self, merchant_id=None, merchant_ids=None, activity_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if merchant_ids is not None:
            matcher.update({"merchantId": {"$in": merchant_ids}})
        if activity_id is not None:
            matcher.update({"activityIds": activity_id})
        if not matcher:
            return []
        configs = self._merchant_activity_config_collection.find(matcher)
        return [json_format.ParseDict(config, config_pb.MerchantActivityConfig(), ignore_unknown_fields=True)
                for config in configs]

    def get_merchant_activity_config(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        config = self._merchant_activity_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, config_pb.MerchantActivityConfig(), ignore_unknown_fields=True)

    def get_promotion_configs(self, merchant_id: str, type: int = None, return_proto=True):
        if not merchant_id:
            return []
        matcher = {
            "merchantId": merchant_id,
            "status": config_pb.PromotionConfig.StatusEnum.Name(config_pb.PromotionConfig.StatusEnum.ACTIVE)
        }
        if type is not None:
            matcher["type"] = config_pb.PromotionConfig.TypeEnum.Name(type)
        configs = self._promotion_config_collection.find(matcher)
        if not configs:
            return []
        if not return_proto:
            return list(configs)
        return [
            json_format.ParseDict(config, config_pb.PromotionConfig(), ignore_unknown_fields=True)
            for config in configs
        ]

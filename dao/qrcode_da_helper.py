# -*- coding: utf-8 -*-

from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import DaoHelper
import proto.qrcode_pb2 as qrcode_pb


# Qrcode Data Access Helper，提供针对时来业务员拓展业务所用的二维码相关的数据访问服务。
class QrcodeDataAccessHelper(DaoHelper):

    @property
    def _qrcode_collection(self):
        db = constants.MONGODB_MISC_DATABASE_NAME
        c = constants.MONGODB_QRCODE_COLLECTION_NAME
        return self.mongo_client[db][c]

    @property
    def _direct_pay_collection(self):
        db = constants.MONGODB_MISC_DATABASE_NAME
        c = constants.MONGODB_DIRECT_PAY_QRCODE_COLLECTION_NAME
        return self.mongo_client[db][c]

    def get_merchant_assist_login_scene(self, scene_id=None, staff_id=None, merchant_id=None):
        matcher = {}
        if scene_id is not None:
            matcher.update({"id": scene_id})
        if staff_id is not None:
            matcher.update({"staffId": staff_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        scene = db[constants.MONGODB_MERCHANT_ASSIST_LOGIN_QRCODES].find_one(matcher)
        if not scene:
            return None
        scene = json_format.ParseDict(scene, qrcode_pb.MerchantAssistLoginQrcode(), ignore_unknown_fields=True)
        return scene

    def add_or_update_merchant_assist_login_scene(self, scene):
        matcher = {"merchantId": scene.merchant_id, "staffId": scene.staff_id}
        json_data = json_format.MessageToDict(scene, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        scene = db[constants.MONGODB_MERCHANT_ASSIST_LOGIN_QRCODES].update(matcher, json_data, upsert=True)

    def get_staff_qrcode(self, qrcode_id):
        """根据ID返回指定业务员拓展二维码相关信息。

        Args:
            qrcode_id: (string) 业务员拓展二维码ID

        Returns:
            (StaffQrcode) 若指定二维码ID存在，返回相应二维码的StaffQrcode结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        qrcode = db[constants.MONGODB_STAFF_QRCODE_COLLECTION_NAME].find_one({'id': qrcode_id})
        if qrcode:
            return json_format.ParseDict(qrcode, qrcode_pb.StaffQrcode(), ignore_unknown_fields=True)
        return None

    def add_staff_qrcode(self, qrcode):
        """添加一个新的业务员拓展二维码。

        Args:
          qrcode: (StaffQrcode) 业务员拓展二维码信息结构体
        """
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_STAFF_QRCODE_COLLECTION_NAME].insert_one(json_obj)

    def get_merchant_qrcode(self, qrcode_id):
        """根据ID返回指定商户拓展二维码相关信息。

        Args:
            qrcode_id: (string) 商户拓展二维码ID

        Returns:
            (MerchantQrcode) 若指定二维码ID存在，返回相应二维码的MerchantQrcode结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        qrcode = db[constants.MONGODB_MERCHANT_QRCODE_COLLECTION_NAME].find_one({'id': qrcode_id})
        if qrcode:
          return json_format.ParseDict(qrcode, qrcode_pb.MerchantQrcode(),
                                       ignore_unknown_fields=True)
        else:
          return None

    def add_merchant_qrcode(self, qrcode):
        """添加一个新的商户拓展二维码信息。

        Args:
          qrcode: (MerchantQrcode) 商户拓展二维码信息结构体
        """
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_MERCHANT_QRCODE_COLLECTION_NAME].insert_one(json_obj)

    def get_merchant_home_wxacode(self, code_id):
        """根据ID返回指定商户首页小程序码相关信息。

        Args:
            code_id: (string) 商户首页二维码ID

        Returns:
            (MerchantHomeCode) 若指定二维码ID存在，返回相应二维码的MerchantHomeCode结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        qrcode = db[constants.MONGODB_MERCHANT_HOME_CODE_COLLECTION_NAME].find_one({'id': code_id})
        if qrcode:
          return json_format.ParseDict(qrcode, qrcode_pb.MerchantHomeCode(),
                                       ignore_unknown_fields=True)
        else:
          return None

    def add_merchant_home_wxacode(self, qrcode):
        """添加一个新的商户首页小程序码信息。

        Args:
          qrcode: (MerchantHomeCode) 商户首页二维码信息结构体
        """
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_MERCHANT_HOME_CODE_COLLECTION_NAME].insert_one(json_obj)

    def add_group_dining_event_wxacode(self, qrcode):
        """添加一个新的饭局邀请页面小程序码信息。

        Args:
          qrcode: (GroupDiningEventQrcode) 饭局邀请页面二维码信息结构体
        """
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_GROUP_DINING_EVENT_CODE_COLLECTION_NAME].insert_one(json_obj)

    def get_group_dining_event_wxacode(self, code_id):
        """根据ID返回指定饭局邀请页面小程序码相关信息。

        Args:
            code_id: (string) 饭局邀请页面二维码ID

        Returns:
            (GroupDiningEventQrcode) 若指定二维码ID存在，返回相应二维码的GroupDiningEventQrcode结构体，
            若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        qrcode = db[constants.MONGODB_GROUP_DINING_EVENT_CODE_COLLECTION_NAME].find_one({'id': code_id})
        if qrcode:
          return json_format.ParseDict(qrcode, qrcode_pb.GroupDiningEventQrcode(),
                                       ignore_unknown_fields=True)
        else:
          return None

    def add_self_dinig_discount_qrcode(self, qrcode):
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_SELF_DINING_DISCOUNT_CODE_COLLECTION_NAME].insert_one(json_obj)

    def add_ordering_wxacode(self, qrcode):
        json_obj = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        db[constants.MONGODB_ORDERING_CODE_COLLECTION_NAME].insert_one(json_obj)

    def get_ordering_wxacode(self, id=None, group_dining_event_id=None):

        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        matcher = {}
        if id is not None:
            matcher.update({'id': id})
        if group_dining_event_id is not None:
            matcher.update({'groupDiningEvent.groupDiningEventId': group_dining_event_id})
        qrcode = db[constants.MONGODB_ORDERING_CODE_COLLECTION_NAME].find_one(matcher)
        if qrcode:
          return json_format.ParseDict(qrcode, qrcode_pb.OrderingQrcode(),
                                       ignore_unknown_fields=True)
        else:
          return None

    def add_or_update_qrcode(self, qrcode):
        matcher = {
            "id": qrcode.id
        }
        json_data = json_format.MessageToDict(
            qrcode, including_default_value_fields=True)
        self._qrcode_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_qrcode(self, id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if matcher is None:
            return None
        qrcode = self._qrcode_collection.find_one(matcher)
        if not qrcode:
            return None
        return json_format.ParseDict(qrcode, qrcode_pb.Qrcode(), ignore_unknown_fields=True)

    def add_or_update_direct_pay_qrcode(self, qrcode):
        matcher = {
            "id": qrcode.id
        }
        json_data = json_format.MessageToDict(qrcode, including_default_value_fields=True)
        self._direct_pay_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_direct_pay_qrcode(self, id, name):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if name is not None:
            matcher.update({"name": name})
        if not matcher:
            return None
        result = self._direct_pay_collection.find_one(matcher)
        if not result:
            return None
        return json_format.ParseDict(result, qrcode_pb.DirectPayQrcode(), ignore_unknown_fields=True)

    def get_direct_pay_qrcodes(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return []
        result = self._direct_pay_collection.find(matcher)
        return [
            json_format.ParseDict(r, qrcode_pb.DirectPayQrcode(), ignore_unknown_fields=True)
            for r in result
        ]

    def get_bound_direct_pay_qrcodes(self):
        """获取已经绑定的二维码"""
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": {"$ne": ""}})
        if not matcher:
            return []
        result = self._direct_pay_collection.find(matcher)
        return [
            json_format.ParseDict(r, qrcode_pb.DirectPayQrcode(), ignore_unknown_fields=True)
            for r in result
        ]

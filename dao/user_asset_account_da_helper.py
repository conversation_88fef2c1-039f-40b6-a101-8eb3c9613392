# -*- coding: utf-8 -*-

import proto.finance.account_pb2 as account_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class UserAssetAccountDAHelper(DaoHelper):

    @property
    def __user_asset_account_collection(self):
        db = constants.MONGODB_WALLET_DATABASE_NAME
        c = constants.MONGODB_USER_ASSET_ACCOUNT_NAME
        return self.mongo_client[db][c]

    def add_or_update_user_asset_account(self, user_asset_account):
        matcher = {'userId': user_asset_account.user_id}
        json_data = self.to_dict(user_asset_account)
        self.__user_asset_account_collection.update_one(matcher, {'$set': json_data}, upsert=True)

    def get_user_asset_account(self, user_id=None):
        matcher = {}
        self.__set_matcher_user_id(matcher, user_id)
        if not matcher:
            return None
        result = self.__user_asset_account_collection.find_one(matcher)
        return self.parse_document(result, account_pb.UserAssetAccount)

    def __set_matcher_user_id(self, matcher, user_id):
        if user_id is None:
            return
        matcher.update({"userId": user_id})

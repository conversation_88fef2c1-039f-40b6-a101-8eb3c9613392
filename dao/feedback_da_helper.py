# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants
import proto.misc_pb2 as misc_pb

class FeedbackDataAccessHelper(DaoHelper):

    @property
    def __collection(self):
        db = constants.MONGODB_MISC_DATABASE_NAME
        collection = constants.MONGODB_FEEDBACK_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __collection_report(self):
        db = constants.MONGODB_MISC_DATABASE_NAME
        collection = constants.MONGODB_REPORT_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_feedback(self, feedback):
        """ 新增一条反馈
        Args:
            user_id: (string)
            feedback: (string)反馈描述
            date: (timestamp)反馈的时间
        """
        json_obj = json_format.MessageToDict(feedback, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)

    def add_report(self, report):
        """ 新增一条举报
        """
        json_obj = json_format.MessageToDict(report, including_default_value_fields=True)
        self.__collection_report.insert_one(json_obj)

    def save_form(self, mini_program_form):
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        json_obj = json_format.MessageToDict(mini_program_form, including_default_value_fields=True)
        db[constants.MONGODB_MINI_PROGRAM_FORM].insert_one(json_obj)

    def get_one_form(self, user_id, timestamp):
        """ 获得用户最久未使用的一个formid
        """
        matcher = {'userId': user_id, 'timestamp': {'$gt': timestamp}, 'used': False}
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        form = db[constants.MONGODB_MINI_PROGRAM_FORM].find(matcher).sort([('timestamp', -1)]).limit(1)
        if form:
            return json_format.ParseDict(form, misc_pb.MiniProgramFormid(), ignore_unknown_fields=True)
        return None

    def update_form(self, formid, user_id):
        matcher = {'userId': user_id, 'formid': formid}
        db = self.mongo_client[constants.MONGODB_MISC_DATABASE_NAME]
        form = db[constants.MONGODB_MINI_PROGRAM_FORM].update(matcher, {'$set': {'used': True}})

from google.protobuf import json_format

from common.utils import id_manager
from dao.dao_helper import DaoHelper
import dao.constants as constants
import proto.user_pb2 as user_pb


# Merchant User Data Access Helper，提供针对时来平台用户管理相关的数据访问服务。
class MerchantUserDataAccessHelper(DaoHelper):
    def get_user(self, user_id):
        """根据ID返回指定用户信息。

        Args:
            user_id: (string) 用户ID

        Returns:
            (MerchantUser) 若指定用户ID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].find_one({'id': user_id})
        if user:
            return json_format.ParseDict(user, user_pb.MerchantUser(), ignore_unknown_fields=True)
        return None

    def get_user_by_union_id(self, unionid):
        """根据指定的微信UnionID返回对应时来平台用户信息。

        Args:
            unionid: (string) 用户ID

        Returns:
            (MerchantUser) 若指定用户UnionID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].find_one({"wechatProfile.unionid": unionid})
        if user:
            return json_format.ParseDict(user, user_pb.MerchantUser(), ignore_unknown_fields=True)
        return None

    def get_user_by_openid(self, openid):
        """根据用户的openID返回时来平台用户信息。

        Args:
            openid: (string) 用户openID

        Returns:
            (User) 若指定用户openID存在，返回相应用户的User结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        user = db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].find_one({"wechatProfile.openid": openid})
        if user:
            return json_format.ParseDict(user, user_pb.MerchantUser(), ignore_unknown_fields=True)
        return None

    def add_user(self, user):
        """添加一个新的时来平台用户信息。

        Args:
            user: (MerchantUser) 时来平台用户信息结构体
        """
        json_obj = json_format.MessageToDict(user, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].insert_one(json_obj)

    def update_or_create_user(self, user):
        """如果指定用户当前不存在，则添加一个新的时来平台用户信息。
           如果指定用户存在，则更新该用户信息。

        Args:
            user: (MerchantUser) 时来平台用户信息结构体
        """
        user.id = user.id or id_manager.generate_user_id()
        json_obj = json_format.MessageToDict(user, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].update_one({'id': user.id}, {"$set": json_obj}, upsert=True)

    def get_users(self, merchant_id):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        users = db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].find({'merchants.merchantId': merchant_id})
        if users:
            return [json_format.ParseDict(user, user_pb.MerchantUser(), ignore_unknown_fields=True) for user in users]
        return []

    def get_users_by_nickname(self, nickname=None):
        db = self.mongo_client[constants.MONGODB_USER_DATABASE_NAME]
        nickname = {"$regex": '.*{}.*'.format(nickname)}
        matcher = {"wechatProfile.nickname": nickname}
        users = db[constants.MONGODB_MERCHANT_USER_COLLECTION_NAME].find(matcher)
        if users:
            return [json_format.ParseDict(user, user_pb.MerchantUser(), ignore_unknown_fields=True) for user in users]
        return None

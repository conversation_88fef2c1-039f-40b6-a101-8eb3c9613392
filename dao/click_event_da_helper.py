# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao import constants
from dao.dao_helper import DaoHelper
from proto.group_dining import combo_meal_pb2 as combo_meal_pb


class ClickEventDataAccessHelper(DaoHelper):
    """ 点击事件
    """

    @property
    def __collection(self):
        db = constants.MONGODB_BI_DATABASE_NAME
        collection = constants.MONGODB_BI_CLICK_EVENT_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def get_click_events(self, user_id=None, merchant_id=None, page=None, size=None):
        """ 获取符合条件的点击事件列表
        Args:
            user_id: (string)用户ID
            merchant_id: (string)商户id
        Return:
            ClickEvent结构体列表
        """
        match = {"merchantId": merchant_id}
        cursor = self.__collection.find(match)
        if page is not None and size is not None:
            skip = (page - 1) * size
            combo_meals = cursor.skip(skip).limit(size)
        else:
            combo_meals = cursor
        if combo_meals:
            return [json_format.ParseDict(combo_meal, combo_meal_pb.ComboMeal(),
                                          ignore_unknown_fields=True) for combo_meal in combo_meals]
        return []

    def get_click_event_by_id(self, event_id):
        """ 根据套餐id获取套餐
        Args:
            combo_meal_id: (string)套餐id
        Returns:
            group_dining.combo_meal.ComboMeal结构体
        """
        match = {"id": event_id}
        combo_meal = self.__collection.find_one(match)
        if combo_meal:
            return json_format.ParseDict(combo_meal, combo_meal_pb.ComboMeal(), ignore_unknown_fields=True)
        return None

    def add_click_event(self, click_event):
        """添加一个新的点击事件。

        Args:
          click_event: (ClickEvent) 点击事件结构体
        """
        json_obj = json_format.MessageToDict(click_event, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)
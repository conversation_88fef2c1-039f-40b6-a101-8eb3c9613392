# -*- coding: utf-8 -*-

from google.protobuf import json_format

import proto.verification_code_pb2 as verification_code_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class BrandDishVerificationCodeDataAccessHelper(DaoHelper):

    db = constants.MONGODB_VERIFICATION_CODE_DATABASE_NAME

    @property
    def _brand_dish_verification_code_strategy_collection(self):
        c = constants.MONGODB_BRAND_DISH_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _brand_dish_verification_code_collection(self):
        c = constants.MONGODB_BRAND_DISH_VERIFICATION_CODE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_strategy(self, strategy):
        matcher = {"id": strategy.id}
        json_obj = json_format.MessageToDict(strategy, including_default_value_fields=True)
        self._brand_dish_verification_code_strategy_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def add_or_update_brand_dish_verification_code(self, dish_verification_code):
        matcher = {"id": dish_verification_code.id}
        json_obj = json_format.MessageToDict(dish_verification_code, including_default_value_fields=True)
        self._brand_dish_verification_code_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_brand_dish_verification_codes(self, brand_id=None, msg=None, coupon_id=None, merchant_id=None, strategy_id=None, status=None, size=None, strategy_ids=None, user_id=None):
        if all(p is None for p in (brand_id, msg, coupon_id, strategy_ids)):
            return None
        matcher = {}
        if msg is not None:
            matcher.update({"msg": msg})
        if coupon_id is not None:
            matcher.update({"couponId": coupon_id})
        if brand_id is not None:
            matcher.update({"brandId": brand_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if strategy_id is not None:
            matcher.update({"strategyId": strategy_id})
        if status is not None:
            matcher.update({"status": verification_code_pb.BrandDishVerificationCode.Status.Name(status)})
        if strategy_ids is not None:
            matcher.update({
                "strategyId": {"$in": strategy_ids}
            })
        if user_id is not None:
            matcher.update({"userId": user_id})
        dish_coupon_codes = self._brand_dish_verification_code_collection.find(matcher)
        if size is not None:
            dish_coupon_codes.limit(size)
        ret = []
        for code in dish_coupon_codes:
            ret.append(json_format.ParseDict(code, verification_code_pb.BrandDishVerificationCode(), ignore_unknown_fields=True))
        return ret

    def get_brand_dish_verification_code_strategy(self, brand_id=None, strategy_id=None):
        if all(p is None for p in (brand_id, strategy_id)):
            return None

        matcher = {}
        if brand_id is not None:
            matcher.update({"brandId": brand_id})
        if strategy_id is not None:
            matcher.update({"id": strategy_id})
        strategy = self._brand_dish_verification_code_strategy_collection.find_one(matcher)
        if strategy:
            return json_format.ParseDict(
                strategy, verification_code_pb.BrandDishVerificationCodeStrategy(), ignore_unknown_fields=True)
        return None

    def get_brand_dish_verification_code_strategies(self, brand_id, dish_brand_id=None):
        if None in (brand_id,):
            return None

        matcher = {
            "brandId": brand_id
        }
        if dish_brand_id is not None:
            matcher.update({"dishBrandId": dish_brand_id})
        strategies = self._brand_dish_verification_code_strategy_collection.find(matcher)
        if strategies.count() == 0:
            return []
        return [
            json_format.ParseDict(
                strategy, verification_code_pb.BrandDishVerificationCodeStrategy(), ignore_unknown_fields=True)
            for strategy in strategies]

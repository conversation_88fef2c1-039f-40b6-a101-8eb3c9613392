# -*- coding: utf-8 -*-

"""
菜品配料相关的操作
"""
import logging

from google.protobuf import json_format
from pymongo import UpdateOne

import dao.constants as constants
import proto.ordering.supply_condiment_pb2 as supply_condiment_pb
from dao.dao_helper import <PERSON><PERSON>H<PERSON><PERSON>
from cache.dish_cache import DishCatalogCache

logger = logging.getLogger(__name__)


class DishSupplyCondimentDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def _ordering_supply_condiment_collection(self):
        c = constants.MONGODB_ORDERING_SUPPLY_CONDIMENT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @DishCatalogCache.update
    def add_or_update_supply_condiment(self, supply_condiment):
        matcher = {"id": supply_condiment.id, "merchantId": supply_condiment.merchant_id}
        json_data = json_format.MessageToDict(supply_condiment, including_default_value_fields=True)
        self._ordering_supply_condiment_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_supply_condiments(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        supply_condiments = self._ordering_supply_condiment_collection.find(matcher)
        return [json_format.ParseDict(s, supply_condiment_pb.DishSupplyCondiment(), ignore_unknown_fields=True)
                for s in supply_condiments]

    def get_supply_condiment(self, merchant_id, id=None, name=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if name is not None:
            matcher.update({"name": name})
        if not matcher:
            return None
        matcher.update({"merchantId": merchant_id})
        supply_condiment = self._ordering_supply_condiment_collection.find_one(matcher)
        if not supply_condiment:
            return None
        return json_format.ParseDict(supply_condiment, supply_condiment_pb.DishSupplyCondiment(), ignore_unknown_fields=True)

    def reorder_supply_condiments(self, merchant_id, supply_condiments):
        @DishCatalogCache.update
        def func(merchant_id, supply_condiments):
            bulk_data = []
            for _, supply_condiment in supply_condiments.items():
                json_data = json_format.MessageToDict(supply_condiment, including_default_value_fields=True)
                bulk_data.append(UpdateOne({
                    "merchantId": merchant_id, "id": supply_condiment.id
                }, {"$set": json_data}, upsert=True))
            self._ordering_supply_condiment_collection.bulk_write(bulk_data)
        return func(merchant_id=merchant_id, supply_condiments=supply_condiments)

import time
import logging

from google.protobuf import json_format
from pymongo import UpdateOne

import dao.constants as constants
import proto.ordering.keruyun.order_pb2 as order_pb
import proto.ordering.discount_pb2 as discount_pb
import proto.ordering.keruyun.keruyun_take_out_pb2 as keruyun_take_out_pb
import proto.ordering.order_operation_record_pb2 as order_operation_record_pb
from cache.dish_category_redis_helper import get_dish_category
from cache.dish_category_redis_helper import update_dish_category
from cache.dish_category_redis_helper import update_dish_categories
from cache.dish_redis_helper import get_dish as get_dish_redis
from cache.dish_redis_helper import update_dish as update_dish_redis
from cache.dish_redis_helper import update_dishes as update_dishes_redis
from cache.order_redis_helper import check_order_can_update
from cache.dish_redis_helper import get_all_dishes as get_all_dishes_redis
from cache.dish_cache import DishCatalogCache
from dao.dao_helper import Dao<PERSON>elper
from proto.ordering import registration_pb2 as registration_pb
from proto.ordering import dish_pb2 as dish_pb

logger = logging.getLogger(__name__)


class OrderingServiceDataAccessHelper(DaoHelper):
    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def _order_operation_record_collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_ORDER_OPERATION_RECORD
        return self.mongo_client[self.db][c]

    @property
    def _order_partial_refund_record_collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_ORDER_PARTIAL_REFUND_RECORD_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _ordering_failed_order_collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_FAILED_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _ordering_dish_images_collection(self):
        c = constants.MONGODB_ORDERING_DISH_IMAGES_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _ordering_hualala_order_collection(self):
        c = constants.MONGODB_ORDERING_HUALALA_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _ordering_service_keruyun_take_out_order_collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_KERUYUN_TAKE_OUT_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _mpqrcode_table_map_collection(self):
        c = constants.MONGODB_MPQRCODE_TABLE_MAP_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def __collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_INFO_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __category_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_DISH_CATEGORY_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __dish_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_DISH_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __table_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_TABLE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __keruyun_table_map_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_KERUYUN_TABLE_MAP
        return self.mongo_client[db][collection]

    @property
    def __order_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_ORDER_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __keruyun_order_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_KERUYUN_ORDER_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __discount_plan_collection(self):
        """该表已废弃，不再被使用，折扣逻辑转为实时计算"""
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_DISCOUNT_PLAN_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __additional_dish_order(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_ADDITIONAL_DISH_ORDER_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __latest_keruyun_order_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_LATEST_KERUYUN_ORDER_ID
        return self.mongo_client[db][collection]

    @property
    def __dish_category_printer_collection(self):
        db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME
        collection = constants.MONGODB_ORDERING_SERVICE_DISH_CATEGORY_PRINTER
        return self.mongo_client[db][collection]

    @property
    def _keruyun_fast_food_orders_collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_KERUYUN_FAST_FOOD_ORDER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_keruyun_fast_food_order(self, order):
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        matcher = {"tpOrderId": order.tp_order_id}
        self._keruyun_fast_food_orders_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_keruyun_fast_food_order(self, tp_order_id):
        matcher = {"tpOrderId": tp_order_id}
        order = self._keruyun_fast_food_orders_collection.find_one(matcher)
        if not order:
            return None
        return self.parse_document(order, order_pb.FastFoodOrder)

    @DishCatalogCache.update
    def add_or_update_registration_info(self, registration):
        json_obj = json_format.MessageToDict(registration, including_default_value_fields=True)
        merchant_id = registration.merchant_id
        self.__collection.update({"merchantId": merchant_id}, json_obj, upsert=True)

    def get_registration_infos(self, keruyun_shop_id=None, return_proto=True):
        matcher = {}
        if keruyun_shop_id is not None:
            matcher.update({"keruyunPosInfo.shopId": keruyun_shop_id})
        if return_proto:
            registration_infos = self.__collection.find(matcher)
            return self.parse_documents(registration_infos, cls=registration_pb.OrderingServiceRegistrationInfo)
        return self.__collection.find(matcher, {'_id': False})

    def get_registration_info(self, merchant_id=None, keruyun_shop_id=None, hualala_shop_id=None, hualala_group_id=None, return_proto=True):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if keruyun_shop_id is not None:
            matcher.update({"keruyunPosInfo.shopId": keruyun_shop_id})
        if hualala_shop_id is not None:
            matcher.update({"hualalaPosInfo.shopId": hualala_shop_id})
        if hualala_group_id is not None:
            matcher.update({"hualalaPosInfo.groupId": hualala_group_id})
        if not matcher:
            return None
        registration = self.__collection.find_one(matcher)
        if not registration:
            return None
        if not return_proto:
            return registration
        return self.parse_document(registration, registration_pb.OrderingServiceRegistrationInfo)

    def get_registration_info_by_matcher(self, matcher):
        registration = self.__collection.find(matcher)
        return [self.parse_document(i, registration_pb.OrderingServiceRegistrationInfo) for i in registration]

    @DishCatalogCache.update
    @update_dish_category
    def add_or_update_category(self, category=None):
        json_obj = json_format.MessageToDict(category, including_default_value_fields=True)
        matcher = {"id": category.id, 'merchantId': category.merchant_id}
        self.__category_collection.update(matcher, {"$set": json_obj}, upsert=True)

    @update_dish_categories
    def add_or_update_categories(self, categories, merchant_id, **kargs):
        @DishCatalogCache.update
        def func(categories, merchant_id, **kargs):
            bulk_data = []
            for category in categories:
                json_data = json_format.MessageToDict(category, including_default_value_fields=True)
                bulk_data.append(UpdateOne({"merchantId": merchant_id, "id": category.id}, {"$set": json_data}, upsert=True))
            if len(bulk_data) == 0:
                return
            self.__category_collection.bulk_write(bulk_data)

        return func(categories=categories, merchant_id=merchant_id, **kargs)

    @DishCatalogCache.update
    @update_dish_category
    def update_category(self, id=None, merchant_id=None, status=None):
        matcher = {'id': id, 'merchantId': merchant_id}
        udata = {}
        if status is not None:
            udata.update({"status": dish_pb.DishCategory.Status.Name(status)})
        if matcher and udata:
            self.__category_collection.update(matcher, {"$set": udata})

    @DishCatalogCache.update
    def delete_category(self, id=None, merchant_id=None):
        if None in (id, merchant_id):
            return
        matcher = {"id": id, "merchantId": merchant_id}
        self.__category_collection.remove(matcher)

    @get_dish_category
    def get_category(self, id=None, merchant_id=None, status=None, name=None, third_party_category_id=None, **kargs):
        matcher = {'merchantId': merchant_id}
        if status is not None:
            matcher.update({'status': dish_pb.DishCategory.Status.Name(status)})
        if id is not None:
            matcher.update({"id": id})
        if name is not None:
            matcher.update({"name": name})
        if third_party_category_id is not None:
            matcher.update({"thirdPartyCategoryId": third_party_category_id})
        if not matcher:
            return None
        category = self.__category_collection.find_one(matcher)
        if not category:
            return None
        return self.parse_document(category, dish_pb.DishCategory)

    def get_categories(self, merchant_id=None, orderby=None, status=None, return_proto=True, no_discount: bool = None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if no_discount is not None:
            matcher['noDiscount'] = no_discount
        if status is not None:
            if isinstance(status, list):
                status = [dish_pb.DishCategory.Status.Name(s) for s in status]
                matcher.update({'status': {'$in': status}})
            else:
                matcher.update({"status": dish_pb.DishCategory.Status.Name(status)})
        if not matcher:
            return []
        cursor = self.__category_collection.find(matcher)
        if orderby is not None:
            cursor.sort(orderby)
        return self.parse_documents(cursor, dish_pb.DishCategory, return_proto=return_proto)

    @DishCatalogCache.update
    @update_dish_redis
    def add_or_update_dish(self, dish):
        dish.update_time = int(time.time())
        json_obj = json_format.MessageToDict(dish, including_default_value_fields=True)
        matcher = {"id": dish.id, 'merchantId': dish.merchant_id}
        return self.__dish_collection.update(matcher, {"$set": json_obj}, upsert=True)

    @DishCatalogCache.update
    @update_dish_redis
    def add_or_update_dish_info(self, dish, status=None, is_auto_sold_out=None, remain_quantity=None):
        matcher = {"id": dish.id, "merchantId": dish.merchant_id}
        udata = {}
        if status is not None:
            udata.update({"status": dish_pb.Dish.Status.Name(status)})
        if is_auto_sold_out is not None:
            udata.update({"isAutoSoldOut": is_auto_sold_out})
        if remain_quantity is not None:
            udata["remainQuantity"] = remain_quantity
        if udata is None:
            return
        udata.update({"updateTime": int(time.time())})
        ret = self.__dish_collection.update(matcher, {"$set": udata})
        return ret

    @update_dishes_redis
    def add_or_update_dishes(self, dishes, merchant_id, **kargs):
        @DishCatalogCache.update
        def func(dishes, merchant_id, **kargs):
            bulk_data = []
            for dish in dishes:
                dish.update_time = int(time.time())
                json_data = json_format.MessageToDict(dish, including_default_value_fields=True)
                bulk_data.append(UpdateOne({"merchantId": merchant_id, "id": dish.id}, {"$set": json_data}, upsert=True))
            if len(bulk_data) == 0:
                return
            self.__dish_collection.bulk_write(bulk_data)

        return func(dishes=dishes, merchant_id=merchant_id, **kargs)

    @DishCatalogCache.update
    def add_or_update_dish_no_cache(self, dish):
        dish.update_time = int(time.time())
        json_obj = json_format.MessageToDict(dish, including_default_value_fields=True)
        matcher = {"id": dish.id, 'merchantId': dish.merchant_id}
        return self.__dish_collection.update(matcher, {"$set": json_obj}, upsert=True)

    @DishCatalogCache.update
    @update_dish_redis
    def update_dish(self, id=None, merchant_id=None, except_dish_ids=None, status=None, dish=None):
        matcher = {'id': id, 'merchantId': merchant_id}
        if except_dish_ids is not None:
            matcher.update({"id": {"$nin": except_dish_ids}})

        udata = {}
        if status is not None:
            udata = {"status": dish_pb.Dish.Status.Name(status), "updateTime": int(time.time())}
        if matcher and udata:
            self.__dish_collection.update_many(matcher, {"$set": udata})

    @get_all_dishes_redis
    def get_all_dishes(self, merchant_id=None, **kargs):
        matcher = {"merchantId": merchant_id}
        dishes = self.__dish_collection.find(matcher)
        ret = self.parse_documents(dishes, dish_pb.Dish)
        return ret

    def get_dishes(
        self,
        merchant_id=None,
        dish_ids=None,
        status=None,
        category_id=None,
        orderby=None,
        type=None,
        supply_condiment_id=None,
        supply_condiment_name=None,
        attr_id=None,
        attr_group_id=None,
        name=None,
        attr_name=None,
        return_proto=True,
    ):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if dish_ids is not None:
            matcher.update({"id": {"$in": dish_ids}})
        if status is not None:
            if isinstance(status, list):
                status = [dish_pb.Dish.Status.Name(s) for s in status]
                matcher.update({"status": {'$in': status}})
            else:
                matcher.update({'status': dish_pb.Dish.Status.Name(status)})
        if type is not None:
            matcher.update({"type": dish_pb.Dish.DishType.Name(type)})
        if attr_id is not None:
            matcher.update({"attrs.id": attr_id})
        if attr_group_id is not None:
            matcher.update({"attrs.groupId": attr_group_id})
        if category_id is not None:
            matcher.update({"categories": category_id})
        if supply_condiment_id is not None:
            matcher.update({"supplyCondiments.id": supply_condiment_id})
        if supply_condiment_name is not None:
            matcher.update({"supplyCondiments.name": supply_condiment_name})
        if attr_name is not None:
            matcher.update({"attrs.name": attr_name})
        if name is not None:
            matcher.update({"name": name})
        if not matcher:
            return []
        dishes = self.__dish_collection.find(matcher)
        if orderby is not None:
            dishes = dishes.sort(orderby)
        if return_proto:
            return self.parse_documents(dishes, dish_pb.Dish)
        return dishes

    @get_dish_redis
    def get_dish(
        self,
        dish_id=None,
        merchant_id=None,
        status=None,
        dish_brand_id=None,
        uuid=None,
        is_zero_dish=None,
        third_party_dish_id=None,
        **kargs,
    ):
        matcher = {"merchantId": merchant_id}
        if dish_id is not None:
            matcher.update({"id": dish_id})
        if dish_id is None and dish_brand_id is not None:
            matcher = {'merchantId': merchant_id, "dishBrandId": dish_brand_id}
        if status is not None:
            matcher.update({"status": dish_pb.Dish.Status.Name(status)})
        if dish_brand_id is not None:
            matcher.update({"dishBrandId": dish_brand_id})
        if uuid is not None:
            matcher.update({'uuid': uuid})
        if is_zero_dish is not None:
            matcher.update({"isZeroDish": is_zero_dish})
        if third_party_dish_id is not None:
            matcher.update({"thirdPartyDishId": third_party_dish_id})
        if not matcher:
            return None
        dish = self.__dish_collection.find_one(matcher)
        if not dish:
            return None
        return self.parse_document(dish, dish_pb.Dish)

    def get_dish_no_cache(self, name=None, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if name is not None:
            matcher.update({"name": name})
        if not matcher:
            return None
        dish = self.__dish_collection.find_one(matcher)
        if not dish:
            return None
        return self.parse_document(dish, dish_pb.Dish)

    def count_dish(self, merchant_id=None, status=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if status is not None:
            if isinstance(status, list):
                status = [dish_pb.Dish.Status.Name(s) for s in status]
                matcher.update({"status": {'$in': status}})
            else:
                matcher.update({'status': dish_pb.Dish.Status.Name(status)})
        if matcher:
            return self.__dish_collection.count(matcher)
        return 0

    def change_table_meal_type(self, merchant_id, from_meal_type, to_meal_type):
        @DishCatalogCache.update
        def func(merchant_id, from_meal_type, to_meal_type):
            matcher = {"merchantId": merchant_id, "mealType": dish_pb.DishOrder.MealType.Name(from_meal_type)}
            udata = {"$set": {"mealType": dish_pb.DishOrder.MealType.Name(to_meal_type)}}
            self.__table_collection.update_many(matcher, udata)

        return func(merchant_id=merchant_id, from_meal_type=from_meal_type, to_meal_type=to_meal_type)

    @DishCatalogCache.update
    def add_or_update_table(self, table):
        json_obj = json_format.MessageToDict(table, including_default_value_fields=True)
        matcher = {"id": table.id, 'merchantId': table.merchant_id}
        self.__table_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_tables(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return []
        tables = self.__table_collection.find(matcher).collation({"locale": "zh", "numericOrdering": True}).sort('sort')
        tables = self.parse_documents(tables, dish_pb.Table)
        return tables

    def get_table(self, id=None, merchant_id=None, ordering_service_table_id=None, meal_type=None, name=None):
        matcher = {}
        if id is not None:
            matcher.update({'id': id})
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if ordering_service_table_id is not None:
            matcher.update({"orderingServiceTableId": ordering_service_table_id})
        if meal_type is not None:
            matcher.update({"mealType": dish_pb.DishOrder.MealType.Name(meal_type)})
        if name is not None:
            matcher.update({"name": name})
        if not matcher:
            return None
        table = self.__table_collection.find_one(matcher)
        if table is None:
            return None
        return self.parse_document(table, dish_pb.Table)

    @check_order_can_update
    def add_or_update_order(self, order):
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        matcher = {"id": order.id}
        self.__order_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def update_order(
        self,
        id=None,
        transaction_id=None,
        status=None,
        ordering_service_order_id=None,
        bill_fee=None,
        paid_fee=None,
        paid_time=None,
        meal_code=None,
        is_get_meal=None,
    ):
        matcher = {}
        if transaction_id is not None:
            # 此处用的不是update,transaction_id能惟一确定一个订单
            matcher = {"transactionId": transaction_id}
        if id is not None:
            # 不用update
            matcher = {"id": id}

        udata = {}
        if transaction_id is not None:
            udata.update({"transactionId": transaction_id})
        if status is not None:
            udata.update({"status": dish_pb.DishOrder.OrderStatus.Name(status)})
        if ordering_service_order_id is not None:
            udata.update({"orderingServiceOrderId": ordering_service_order_id})
        if paid_fee is not None:
            udata.update({"paidFee": paid_fee})
        if bill_fee is not None:
            udata.update({"billFee": bill_fee})
        if paid_time is not None:
            udata.update({"paidTime": paid_time})
        if meal_code is not None:
            udata.update({'mealCode': meal_code})
        if is_get_meal is not None:  # 是否已取餐
            udata.update({'isGetMeal': is_get_meal})
        self.__order_collection.update(matcher, {"$set": udata})

    def count_recently_paid_order(self, merchant_id=None, user_id=None, before_time=None):
        matcher = {}
        if merchant_id is not None and merchant_id != "":
            matcher.update({"merchantId": merchant_id})
        if user_id is not None:
            matcher.update({'userId': user_id})
        if before_time is not None:
            matcher.update({"paidTime": {"$gt": str(before_time)}})
        if not matcher:
            return 0
        matcher.update({"status": "PAID"})
        orders = self.__order_collection.find(matcher).sort([("createTime", -1)]).limit(1)
        return self.parse_documents(orders, dish_pb.DishOrder)

    def get_recently_order(self, merchant_id=None, table_id=None, before_time=None, status=None):
        matcher = {}
        if merchant_id is not None and merchant_id != "":
            matcher.update({"merchantId": merchant_id})
        if table_id is not None and table_id != "":
            matcher.update({"tableId": table_id})
        if not matcher:
            return None
        if status is None:
            matcher.update(
                {
                    "status": {
                        "$in": [
                            dish_pb.DishOrder.OrderStatus.Name(dish_pb.DishOrder.APPROVED),
                            # dish_pb.DishOrder.OrderStatus.Name(dish_pb.DishOrder.ORDERED),
                            dish_pb.DishOrder.OrderStatus.Name(dish_pb.DishOrder.TO_BE_CONFIRMED),
                        ]
                    }
                }
            )
        else:
            matcher.update({"status": {"$in": [dish_pb.DishOrder.OrderStatus.Name(status) for status in status]}})
        if before_time is not None:
            matcher.update({"createTime": {"$gt": str(before_time)}})
        order = self.__order_collection.find(matcher).sort([("createTime", -1)]).limit(1)
        for o in order:
            return self.parse_document(o, dish_pb.DishOrder)
        return None

    def get_user_recently_order(self, merchant_id, user_id=None, table_id=None, order_status=None, before_time=None):
        order = None
        matcher = {"merchantId": merchant_id, "createTime": {"$gt": str(int(time.time()) - 60 * 60 * 6)}}
        if before_time is not None:
            matcher.update({"createTime": {"$gt": str(before_time)}})
        if order_status is not None:
            matcher.update({"status": {"$in": [dish_pb.DishOrder.OrderStatus.Name(status) for status in order_status]}})
        if user_id is not None:
            matcher.update({"$or": [{"userId": user_id}, {"userIds": user_id}]})
            order = self.__order_collection.find(matcher).sort([("createTime", -1)]).limit(1)
        if table_id is not None and (not order or order.count() <= 0):
            matcher.update({"tableId": str(table_id)})
            order = self.__order_collection.find(matcher).sort([("createTime", -1)]).limit(1)
        if order is None:
            return None
        for o in order:
            return self.parse_document(o, dish_pb.DishOrder)
        return None

    def query_one(self, matcher, project={}):
        return super().query_one(self.__order_collection, matcher, project)

    def get_order(
        self,
        id=None,
        ordering_service_order_id=None,
        transaction_id=None,
        status=None,
        ordering_service_trade_id=None,
        dining_id=None,
        refund_transaction_id=None,
        table_id=None,
        handheld_pos_order_no=None,
        new_coupon_id=None,
    ):
        matcher = {}
        if transaction_id is not None:
            matcher.update({"transactionId": transaction_id})
        if refund_transaction_id is not None:
            matcher.update({'refundTransactionId': refund_transaction_id})
        if id is not None:
            matcher.update({"id": id})
        if status is not None:
            matcher.update({"status": dish_pb.DishOrder.OrderStatus.Name(status)})
        if ordering_service_order_id is not None:
            matcher.update({"orderingServiceOrderId": ordering_service_order_id})
        if ordering_service_trade_id is not None:
            matcher.update({'orderingServiceTradeId': ordering_service_trade_id})
        if dining_id is not None:
            matcher.update({"groupDiningEventId": dining_id})
        if table_id is not None:
            matcher.update({"tableId": table_id})
        if handheld_pos_order_no is not None:
            matcher.update({"handheldPosOrderNo": handheld_pos_order_no})
        if new_coupon_id is not None:
            matcher.update({'couponIds': new_coupon_id})
        if not matcher:
            return None
        order = self.__order_collection.find_one(matcher)
        if not order:
            return None
        return self.parse_document(order, dish_pb.DishOrder)

    def get_orders(
        self,
        merchant_id=None,
        start_time=None,
        end_time=None,
        status=None,
        meal_type=None,
        orderby=None,
        user_id=None,
        size=None,
        create_time=None,
        last_paid_time=None,
        last_create_time=None,
        start_create_time=None,
        end_create_time=None,
        start_paid_time=None,
        end_paid_time=None,
        pos_order_ids=None,
        new_coupon_id=None,
    ):
        matcher = {}
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if start_create_time is not None and end_create_time is not None:
            matcher.update({"createTime": {'$lt': str(end_create_time), "$gt": str(start_create_time)}})
        if start_time is not None or end_time is not None:
            matcher['approveTime'] = {}
        if start_time is not None:
            matcher['approveTime']["$gt"] = str(start_time)
        if end_time is not None:
            matcher['approveTime']["$lt"] = str(end_time)
        if last_paid_time:
            matcher['paidTime'] = {}
            matcher['paidTime']["$lt"] = str(last_paid_time)
        if last_create_time:
            matcher['createTime'] = {}
            matcher['createTime']['$lt'] = str(last_create_time)
        if meal_type is not None:
            if isinstance(meal_type, list):
                matcher.update({"mealType": {"$in": [dish_pb.DishOrder.MealType.Name(m) for m in meal_type]}})
            else:
                matcher['mealType'] = dish_pb.DishOrder.MealType.Name(meal_type)
        if pos_order_ids is not None:
            matcher['orderingServiceOrderId'] = {'$in': pos_order_ids}
        if start_paid_time is not None and end_paid_time is not None:
            matcher["paidTime"] = {}
            matcher["paidTime"]["$lt"] = str(end_paid_time)
            matcher["paidTime"]["$gt"] = str(start_paid_time)
        if status is not None:
            if isinstance(status, list):
                matcher.update({'status': {'$in': [dish_pb.DishOrder.OrderStatus.Name(s) for s in status]}})
            else:
                matcher.update({'status': dish_pb.DishOrder.OrderStatus.Name(status)})
        if user_id is not None:
            matcher.update({'userId': user_id})
        if create_time is not None:
            matcher.update({'createTime': {'$lt': create_time}})
        if new_coupon_id is not None:
            matcher.update({"couponIds": new_coupon_id})
        logger.info(f"[*] get get_orders: {matcher}")
        if not matcher:
            return []
        orders = self.__order_collection.find(matcher)
        if orderby is not None:
            orders.sort(orderby)
        return self.parse_documents(orders, dish_pb.DishOrder, size=size)

    def count_orders(
        self, merchant_id=None, status=None, user_id=None, create_time=None, meal_type=None, start_time=None, end_time=None
    ):
        matcher = {}
        if merchant_id is not None:
            matcher.update({'merchantId': merchant_id})
        if status is not None:
            if isinstance(status, list):
                matcher.update({'status': {'$in': [dish_pb.DishOrder.OrderStatus.Name(s) for s in status]}})
            else:
                matcher.update({'status': dish_pb.DishOrder.OrderStatus.Name(status)})
        if start_time is not None and end_time is not None:
            matcher.update({'createTime': {'$lt': str(end_time), "$gt": str(start_time)}})
        if meal_type is not None:
            matcher.update({'mealType': dish_pb.DishOrder.MealType.Name(meal_type)})
        if user_id is not None:
            matcher.update({'userId': user_id})
        if create_time is not None:
            matcher.update({'createTime': {'$lt': create_time}})
        count = self.__order_collection.count(matcher)
        return count

    def update_keruyun_order(self, tp_order_id, user_fee):
        matcher = {'tpOrderId': tp_order_id}
        udata = {'userFee': user_fee}
        self.__keruyun_order_collection.update(matcher, {'$set': udata})

    def add_or_update_keruyun_order(self, order):
        matcher = {"tpOrderId": order.tp_order_id}
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        self.__keruyun_order_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_keruyun_order(self, tp_order_id=None):
        matcher = {}
        if tp_order_id is not None:
            matcher = {"tpOrderId": tp_order_id}
        if not matcher:
            return None
        keruyun_order = self.__keruyun_order_collection.find_one(matcher)
        if not keruyun_order:
            return None
        return self.parse_document(keruyun_order, order_pb.DinnerOrder)

    def remove_discount_plan(self, merchant_id, user_id):
        matcher = {"merchantId": merchant_id, 'userId': user_id}
        self.__discount_plan_collection.remove(matcher)

    def get_discount_plan(self, user_id, merchant_id):
        matcher = {"userId": user_id, "merchantId": merchant_id}
        plan = self.__discount_plan_collection.find_one(matcher)
        if not plan:
            return None
        return self.parse_document(plan, discount_pb.StoreDiscountPlan)

    def add_discount_plan(self, plan):
        matcher = {"userId": plan.user_id, "merchantId": plan.merchant_id}
        json_obj = json_format.MessageToDict(plan, including_default_value_fields=True)
        self.__discount_plan_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def add_keruyun_additional_dish_order(self, additional_dish_order):
        json_obj = json_format.MessageToDict(additional_dish_order, including_default_value_fields=True)
        self.__additional_dish_order.insert_one(json_obj)

    def get_keruyun_additional_dish_order(self, order_id):
        matcher = {"shilaiOrderId": order_id}
        additional_dish_orders = self.__additional_dish_order.find(matcher)
        return self.parse_documents(additional_dish_orders, order_pb.AdditionalDishOrder)

    def add_or_update_latest_keruyun_order_id(self, order):
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        matcher = {"tableId": order.table_id}
        self.__latest_keruyun_order_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_latest_keruyun_order(self, table_id):
        matcher = {"tableId": table_id, "status": "PAID"}
        order = self.__order_collection.find_one(matcher)
        if not order:
            return None
        order = self.parse_document(order, dish_pb.DishOrder)
        return order

    def get_dish_by_merchant_id_name(self, merchant_id=None, name=None):
        matcher = {"merchantId": merchant_id, 'name': name}
        dishes = self.__dish_collection.find(matcher)
        return self.parse_documents(dishes, dish_pb.Dish)

    def get_required_items(self, merchant_id):
        config = self.__collection.find_one({"merchantId": merchant_id}, {"orderingConfig": 1})
        if not config:
            return
        required_order_items = config.get("orderingConfig", {}).get("requiredOrderItems", [])
        return {item["id"]: item for item in required_order_items if item}

    def set_dish_category_printer(self, printer):
        matcher = {"merchantId": printer.merchant_id, "dishCategoryId": printer.dish_category_id}
        json_obj = json_format.MessageToDict(printer, including_default_value_fields=True)
        self.__dish_category_printer_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_dish_category_printer_configs(self, merchant_id):
        matcher = {"merchantId": merchant_id}
        printer_configs = self.__dish_category_printer_collection.find(matcher)
        if not printer_configs:
            return []
        return self.parse_documents(printer_configs, dish_pb.DishCategoryPrinter)

    def get_dish_category_printer(self, merchant_id, dish_category_id):
        matcher = {'merchantId': merchant_id, 'dishCategoryId': dish_category_id}
        printer = self.__dish_category_printer_collection.find_one(matcher)
        if not printer:
            return None
        return self.parse_document(printer, dish_pb.DishCategoryPrinter)

    def get_category_printers(self, merchant_id, printer_sn):
        matcher = {'merchantId': merchant_id, 'printerSns': printer_sn}
        printers = self.__dish_category_printer_collection.find(matcher)
        if not printers:
            return []
        return self.parse_documents(printers, dish_pb.DishCategoryPrinter)

    def delete_dish_category_printer(self, merchant_id, printer_sn):
        matcher = {
            'merchantId': merchant_id,
            'printerSns': printer_sn,
        }
        printer_configs = self.__dish_category_printer_collection.find(matcher)
        for config in printer_configs:
            config.get('printerSns').remove(printer_sn)
            if not config.get('printerSns'):
                self.__dish_category_printer_collection.delete_one({'_id': config['_id']})
            else:
                self.__dish_category_printer_collection.update_one({'_id': config['_id']}, {'$set': config}, upsert=True)

    def get_mapping_table(self, ordering_service_table_id):
        matcher = {'tableId': ordering_service_table_id}
        table_map = self.__keruyun_table_map_collection.find_one(matcher)
        if not table_map:
            return None
        return table_map.get('realTableId')

    def set_mapping_table(self, table_id, real_table_id, merchant_id):
        matcher = {"tableId": table_id, "realTableId": real_table_id, "merchantId": merchant_id}
        self.__keruyun_table_map_collection.insert(matcher)

    def add_or_update_keruyun_take_out_order(self, order):
        matcher = {"tpOrderId": order.tp_order_id}
        json_obj = json_format.MessageToDict(order, including_default_value_fields=True)
        self._ordering_service_keruyun_take_out_order_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_keruyun_take_out_order(self, tp_order_id=None):
        matcher = {}
        if tp_order_id is not None:
            matcher.update({"tpOrderId": tp_order_id})

        if not matcher:
            return None
        order = self._ordering_service_keruyun_take_out_order_collection.find_one(matcher)
        if not order:
            return None
        return self.parse_document(order, keruyun_take_out_pb.KeruyunTakeOutOrder)

    def add_or_update_hualala_order(self, order, hualala_order):
        matcher = {"thirdOrderID": order.id}
        self._ordering_hualala_order_collection.update(matcher, {"$set": hualala_order}, upsert=True)

    def get_hualala_order(self, order):
        matcher = {"thirdOrderID": order.id}
        hualala_order = self._ordering_hualala_order_collection.find_one(matcher, {"_id": 0})
        if not hualala_order:
            return
        return hualala_order

    def add_or_update_failed_order(self, order):
        matcher = {"id": order.id}
        json_data = json_format.MessageToDict(order, including_default_value_fields=True)
        self._ordering_failed_order_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_failed_order(self, errcode=None, status=None):
        matcher = {}
        if errcode is not None:
            matcher.update({"errcode": errcode})
        if status is not None:
            matcher.update({"status": dish_pb.FailedOrder.Status.Name(status)})
        failed_orders = self._ordering_failed_order_collection.find(matcher)
        return self.parse_documents(failed_orders, dish_pb.FailedOrder)

    @DishCatalogCache.update
    def delete_one_dish(self, dish):
        matcher = {"merchantId": dish.merchant_id, "id": dish.id}
        udata = {"id": "{}-deleted".format(dish.id), "merchantId": "{}-deleted".format(dish.merchant_id)}
        self.__dish_collection.update_one(matcher, {"$set": udata})

    def add_or_update_mpqrcode_table_map(self, m):
        matcher = {"merchantId": m.merchant_id, "mpqrcode": m.mpqrcode}
        json_data = json_format.MessageToDict(m, including_default_value_fields=True)
        self._mpqrcode_table_map_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_mpqrcode_table_map(self, mpqrcode):
        matcher = {"mpqrcode": mpqrcode}
        m = self._mpqrcode_table_map_collection.find_one(matcher)
        logger.info("{} {}".format(matcher, m))
        if m is None:
            return None
        return self.parse_document(m, dish_pb.MpqrcodeTableMap)

    def add_or_update_order_operation_record(self, operation_record):
        json_data = json_format.MessageToDict(operation_record, including_default_value_fields=True)
        matcher = {"id": operation_record.id}
        self._order_operation_record_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def add_or_update_order_partial_refund_record(self, record):
        json_data = json_format.MessageToDict(record, including_default_value_fields=True)
        matcher = {"id": record.id}
        self._order_partial_refund_record_collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_order_operation_record(self, id):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if not matcher:
            return None
        operation_record = self._order_operation_record_collection.find_one(matcher)
        if not operation_record:
            return None
        return self.parse_document(operation_record, order_operation_record_pb.OrderOperationRecord)

    def get_order_partial_refund_record(self, id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if not matcher:
            return None
        record = self._order_partial_refund_record_collection.find_one(matcher)
        if not record:
            return None
        return self.parse_document(record, order_operation_record_pb.OrderPartialRefundRecord)

    def clear_dish(self, merchant_id):
        @DishCatalogCache.update
        def func(merchant_id):
            if not merchant_id:
                return {'dish_count': 0, 'cate_count': 0}
            return {
                'dish_count': self.__dish_collection.delete_many({'merchantId': merchant_id}).deleted_count,
                'cate_count': self.__category_collection.delete_many({'merchantId': merchant_id}).deleted_count,
            }

        return func(merchant_id=merchant_id)

    def remove_recommend_dish(self, merchant_id, dish_id=None):
        @DishCatalogCache.update
        def func(merchant_id, dish_id):
            if dish_id is not None:
                return self.__collection.update_one(
                    {'merchantId': merchant_id, 'recommendDish.dishes.id': dish_id},
                    {'$pull': {"recommendDish.dishes": {"id": dish_id}}},
                )
            return self.__collection.update_one({'merchantId': merchant_id}, {'$set': {'recommendDish.dishes': []}})

        return func(merchant_id=merchant_id, dish_id=dish_id)

# -*- coding: utf-8 -*-

"""
菜品属性相关的操作
"""
import logging

from google.protobuf import json_format
from pymongo import UpdateOne

import dao.constants as constants
import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.dish_attr_pb2 as dish_attr_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cache.dish_cache import DishCatalogCache

logger = logging.getLogger(__name__)


class DishAttrDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def _ordering_attr_group_collection(self):
        c = constants.MONGODB_ORDERING_ATTR_GROUP_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @DishCatalogCache.update
    def add_or_update_attr_group(self, group):
        matcher = {"id": group.id, "merchantId": group.merchant_id}
        json_data = json_format.MessageToDict(group, including_default_value_fields=True)
        self._ordering_attr_group_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_attr_groups(self, merchant_id, group_type=None):
        matcher = {"merchantId": merchant_id}
        if group_type is not None:
            group_type = dish_attr_pb.DishAttrGroup.AttrGroupType.Value(group_type)
            matcher.update({
                "attrGroupType": group_type
            })
        attr_groups = self._ordering_attr_group_collection.find(matcher)
        return [json_format.ParseDict(ag, dish_attr_pb.DishAttrGroup(), ignore_unknown_fields=True)
                for ag in attr_groups]

    def get_attr_group(self, merchant_id, id=None, group_name=None, type=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if group_name is not None:
            matcher.update({"name": group_name})
        if type is not None:
            matcher.update({"attrGroupType": dish_pb.Attr.AttrType.Name(type)})
        if not matcher:
            return None
        matcher.update({"merchantId": merchant_id})
        attr_group = self._ordering_attr_group_collection.find_one(matcher)
        if not attr_group:
            return None
        return json_format.ParseDict(attr_group, dish_attr_pb.DishAttrGroup(), ignore_unknown_fields=True)

    def add_or_update_attrs(self, merchant_id, attr_groups):
        @DishCatalogCache.update
        def func(merchant_id, attr_groups):
            bulk_data = []
            for _, attr_group in attr_groups.items():
                json_data = json_format.MessageToDict(attr_group, including_default_value_fields=True)
                bulk_data.append(UpdateOne({
                    "merchantId": merchant_id, "id": attr_group.id
                }, {"$set": json_data}, upsert=True))
            self._ordering_attr_group_collection.bulk_write(bulk_data)
        return func(merchant_id=merchant_id, attr_groups=attr_groups)

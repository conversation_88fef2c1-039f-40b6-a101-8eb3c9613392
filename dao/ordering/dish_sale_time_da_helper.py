# -*- coding: utf-8 -*-
"""
菜品限时操作
"""
import logging
from google.protobuf import json_format

import dao.constants as constants
import proto.ordering.dish_pb2 as dish_pb
from dao.dao_helper import DaoHelper

logger = logging.getLogger(__name__)


class DishSaleTimeDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def _dish_sale_time_collection(self):
        c = constants.MONGODB_DISH_SALE_TIME_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_dish_sale_time(self, sale_time):
        matcher = {"id": sale_time.id}
        json_data = json_format.MessageToDict(sale_time, including_default_value_fields=True)
        self._dish_sale_time_collection.update(matcher, {"$set": json_data}, upsert=True)

    def delete_dish_sale_time(self, dish_sale_time_id):
        return self._dish_sale_time_collection.delete_one({'id': dish_sale_time_id}).deleted_count

    def get_dish_sale_time_list(self, merchant_id, status=None, dish_id=None):
        matcher = {"merchantId": merchant_id}
        if isinstance(status, int):
            matcher['status'] = dish_pb.DishSaleTime.SaleTimeStatus.Name(status)
        if isinstance(dish_id, str):
            matcher['dishIds'] = dish_id
        sale_time_list = self._dish_sale_time_collection.find(matcher)
        return [json_format.ParseDict(st, dish_pb.DishSaleTime(), ignore_unknown_fields=True)
                for st in sale_time_list]

    def get_dish_sale_time(self, id=None, name=None, merchant_id=None):
        matcher = dict()
        if id is not None:
            matcher['id'] = id
        if name is not None:
            matcher['name'] = name
        if merchant_id:
            matcher['merchantId'] = merchant_id
        if not matcher:
            return None
        sale_time = self._dish_sale_time_collection.find_one(matcher)
        if sale_time:
            return json_format.ParseDict(sale_time, dish_pb.DishSaleTime(), ignore_unknown_fields=True)

import proto.ordering.dish_pb2 as dish_pb
import dao.constants as constants
from dao.dao_helper import DaoHelper


class TableAreaDaHelper(DaoHelper):

    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def collection(self):
        c = constants.MONGODB_ORDERING_SERVICE_TABLE_AREA_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_table_area(self, area):
        matcher = {"id": area.id}
        json_data = self.to_dict(area)
        self.collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def get_table_area(self, id):
        matcher = {"id": id}
        area = self.collection.find_one(matcher)
        return self.parse_document(area, dish_pb.TableArea)

    def list_table_areas(self, merchant_id, status):
        matcher = {
            "merchantId": merchant_id,
            "status": dish_pb.TableArea.Status.Name(status)
        }
        areas = self.collection.find(matcher)
        return self.parse_documents(areas, dish_pb.TableArea)

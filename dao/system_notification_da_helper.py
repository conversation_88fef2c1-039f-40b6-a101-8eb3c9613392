from google.protobuf import json_format

from dao import constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.sns import messaging_pb2 as messaging_pb
from proto.websocket import common_pb2 as common_pb

class SystemNotificationDataAccessHelper(DaoHelper):

    @property
    def __collection(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_SYSTEM_NOTIFICATION_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __collection_last_viewed_time(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_SYSTEM_NOTIFICATION_VIEW_TIME_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_message(self, message):
        obj = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection.insert_one(obj)

    def add_or_update_last_view_time(self, message):
        matcher = {"userId": message.user_id}
        json_obj = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection_last_viewed_time.update(matcher, {"$set": json_obj}, upsert=True)

    def get_last_view_message(self, user_id):
        matcher = {"userId": user_id}
        message = self.__collection_last_viewed_time.find_one(matcher)
        if message:
            return json_format.ParseDict(message, messaging_pb.NotificationLastViewedTime(),
                                         ignore_unknown_fields=True)
        return None

    def count_unread(self, user_id, last_viewed_time):
        matcher = {
            "receiverId": user_id,
            "createTime": {"$gt": str(last_viewed_time)}
        }
        return self.__collection.count(matcher)

    def get_messages(self, id=None, user_id=None, last_viewed_time=None, create_time=None,
                     page=None, size=None, orderby=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({
                "receiverId": user_id
            })
        if create_time is not None:
            matcher.update({
                "createTime": {"$lt": str(create_time)}
            })
        if matcher:
            cursor = self.__collection.find(matcher)
            if orderby:
                cursor = cursor.sort(orderby)
            if page is not None and size is not None:
                skip = (page - 1) * size
                messages = cursor.skip(skip).limit(size)
            else:
                messages = cursor
            return [json_format.ParseDict(message, common_pb.EventMessage(),
                                          ignore_unknown_fields=True) for message in messages]
        return []

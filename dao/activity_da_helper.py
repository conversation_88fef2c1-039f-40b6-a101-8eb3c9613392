# -*- coding: utf-8 -*-


from google.protobuf import json_format
from pymongo import UpdateOne

import proto.activity.activity_pb2 as activity_pb
from dao.dao_helper import DaoHelper
from dao import constants


class ActivityDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ACTIVITY_DATABASE_NAME

    @property
    def _activity_subscribe_info_collection(self):
        c = constants.MONGODB_ACTIVITY_SUBSCRIBE_INFO_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_activity_subscribe_infos(self, infos):
        bulk_data = []
        for info in infos:
            json_data = json_format.MessageToDict(info, including_default_value_fields=True)
            bulk_data.append(UpdateOne({
                "merchantId": info.merchant_id,
                "id": info.id
            }, {"$set": json_data}, upsert=True))
        self._activity_subscribe_info_collection.bulk_write(bulk_data)

    def add_or_update_activity_subscribe_info(self, info):
        matcher = {
            "userId": info.user_id,
            "merchantId": info.merchant_id,
            "activityId": info.activity_id
        }
        json_data = json_format.MessageToDict(
            info, including_default_value_fields=True)
        self._activity_subscribe_info_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_activity_subscribe_infos(
            self, user_id=None, merchant_id=None, activity_id=None,
            status=None):
        matcher = {}
        if user_id is not None:
            matcher.update({"userId": user_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if activity_id is not None:
            matcher.update({"activityId": activity_id})
        if status is not None:
            matcher.update({"status": activity_pb.ActivitySubscribeInfo.Status.Name(status)})
        if not matcher:
            return
        infos = self._activity_subscribe_info_collection.find(matcher)
        return [json_format.ParseDict(info, activity_pb.ActivitySubscribeInfo(), ignore_unknown_fields=True)
                for info in infos]

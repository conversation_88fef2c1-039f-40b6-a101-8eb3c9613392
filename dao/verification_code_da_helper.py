# -*- coding: utf-8 -*-

"""
Filename: verification_code_da_helper.py
Date: 2020-06-08 11:29:14
Title: 核销码相关
"""

from google.protobuf import json_format

import proto.verification_code_pb2 as verification_code_pb
import dao.constants as constants
from dao.dao_helper import DaoHelper


class VerificationCodeDataAccessHelper(DaoHelper):

    db = constants.MONGODB_VERIFICATION_CODE_DATABASE_NAME

    @property
    def _verification_code_collection(self):
        c = constants.MONGODB_VERIFICATION_CODE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _verification_code_strategy_collection(self):
        c = constants.MONGODB_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_verification_code(self, verification_code):
        matcher = {"id": verification_code.id}
        json_data = json_format.MessageToDict(verification_code, including_default_value_fields=True)
        self._verification_code_collection.update(matcher, {"$set": json_data}, upsert=True)

    def add_or_update_verification_code_strategy(self, verification_code_strategy):
        matcher = {"id": verification_code_strategy.id}
        json_data = json_format.MessageToDict(verification_code_strategy, including_default_value_fields=True)
        self._verification_code_strategy_collection.update(matcher, {'$set': json_data}, upsert=True)

    def get_verification_code_strategy(self, id):
        matcher = {"id": id}
        strategy = self._verification_code_strategy_collection.find_one(matcher)
        if not strategy:
            return None
        return json_format.ParseDict(strategy, verification_code_pb.VerificationCodeStrategy(), ignore_unknown_fields=True)

    def get_verification_codes(self, id=None, msg=None, brand_id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if msg is not None:
            matcher.update({"showMessage": msg})
        if brand_id is not None:
            matcher.update({"brandId": brand_id})
        if not matcher:
            return None
        verification_codes = self._verification_code_collection.find(matcher)
        if not verification_codes or verification_codes.count() == 0:
            return None
        return [json_format.ParseDict(verification_code, verification_code_pb.VerificationCode(), ignore_unknown_fields=True)
                for verification_code in verification_codes]

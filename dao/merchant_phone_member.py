# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import proto.user_pb2 as user_pb
from dao import constants
from dao.dao_helper import DaoHelper

logger = logging.getLogger(__name__)


class MerchantPhoneMemberDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME

    @property
    def _merchant_phone_member_collection(self):
        c = constants.MONGODB_ORDERING_MERCHANT_PHONE_MEMBER_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_merchant_phone_member(self, obj):
        matcher = {
            "merchantId": obj.merchant_id,
            "phone": obj.phone
        }
        member = json_format.MessageToDict(obj, including_default_value_fields=True)
        self._merchant_phone_member_collection.update(matcher, {"$set": member}, upsert=True)

    def get_merchant_phone_member(self, merchant_id=None, phone=None, status=None, user_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if phone is not None:
            matcher.update({"phone": phone})
        if status is not None:
            matcher.update({"status": user_pb.MerchantPhoneMember.Status.Name(status)})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if not matcher:
            return None
        member = self._merchant_phone_member_collection.find_one(matcher)
        if not member:
            return None
        return json_format.ParseDict(member, user_pb.MerchantPhoneMember(), ignore_unknown_fields=True)

    def get_merchant_phone_members(self, merchant_id=None, phone=None, user_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if phone is not None:
            matcher.update({"phone": phone})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if not matcher:
            return None
        phone_members = self._merchant_phone_member_collection.find(matcher)
        logger.info(f"返回数量: {matcher} {phone_members.count()}")
        return [json_format.ParseDict(member, user_pb.MerchantPhoneMember(), ignore_unknown_fields=True)
                for member in phone_members]

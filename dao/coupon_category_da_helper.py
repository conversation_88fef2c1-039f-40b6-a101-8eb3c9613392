from google.protobuf import json_format

import dao.constants as constants
import proto.coupon_category_pb2 as coupon_category_pb

from dao.dao_helper import DaoHelper


class CouponCategoryDataAccessHelper(DaoHelper):

    def add_or_update_invite_share_coupon_category(self, coupon_category):
        matcher = {"id": coupon_category.id}
        json_obj = json_format.MessageToDict(coupon_category, including_default_value_fields=True)
        self.__collection.update(matcher, {"$set": json_obj}, upsert=True)

    def add_or_update_coupon_category(self, coupon_category):
        matcher = {"id": coupon_category.id}
        json_data = json_format.MessageToDict(coupon_category, including_default_value_fields=True)
        self.__collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_dish_coupon_categories(self, merchant_id=None, dish_id=None, dish_brand_id=None, state=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if dish_id is not None:
            matcher.update({"dishCouponSpec.dishId": dish_id})
        if dish_brand_id is not None:
            matcher.update({"dishCouponSpec.dishBrandId": dish_brand_id})
        if state is not None:
            matcher.update({"state": coupon_category_pb.CouponCategory.State.Name(state)})
        if not matcher:
            return None
        coupon_categories = self.__collection.find(matcher)
        return [json_format.ParseDict(coupon_category, coupon_category_pb.CouponCategory(),
                                      ignore_unknown_fields=True) for coupon_category in coupon_categories]

    def get_coupon_category(self, id=None, verification_code_strategy_id=None, merchant_id=None, strategy_id=None, brand_id=None, brand_dish_verification_code_strategy_id=None, issue_scene=None, state=None):
        """根据ID返回指定优惠券类别信息

        Args:
            id: (String) 优惠券类别ID

        Return:
            (coupon_category_pb.CouponCategory) 优惠券类别信息
        """
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if issue_scene is not None:
            matcher.update({"issueScene": coupon_category_pb.CouponCategory.IssueScene.Name(issue_scene)})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if verification_code_strategy_id is not None:
            matcher.update({
                "verificationCodeCouponSpec.verificationCodeStrategyId": verification_code_strategy_id
            })
        if merchant_id is not None:
            matcher.update({
                "merchantId": merchant_id
            })
        if strategy_id is not None:
            matcher.update({
                "dishVerificationCodeCouponSpec.strategyId": strategy_id
            })
        if brand_dish_verification_code_strategy_id is not None:
            matcher.update({
                "brandDishVerificationCodeCouponSpec.strategyId": brand_dish_verification_code_strategy_id
            })
        if brand_id is not None:
            matcher.update({
                "brandId": brand_id
            })
        if state is not None:
            matcher.update({
                'state': coupon_category_pb.CouponCategory.State.Name(state)
            })
        coupon_category = self.__collection.find_one(matcher)
        if coupon_category:
            return json_format.ParseDict(coupon_category, coupon_category_pb.CouponCategory(),
                                         ignore_unknown_fields=True)
        return None

    def get_coupon_categories(self, merchant_id, state=coupon_category_pb.CouponCategory.ACTIVE, issue_scene=None, brand_id=None):
        """根据查询条件获取商户的优惠券类别列表

        Args:
            merchant_id: (String) 商户ID
            issue_scene: (coupon_category_pb.CouponCategory.IssueScene) 优惠券投放场景

        Return:
            (List of coupon_category_pb.CouponCategory)
        """
        filter = {
            'state': coupon_category_pb.CouponCategory.State.Name(state)
        }
        if merchant_id is not None:
            filter.update({"merchantId": merchant_id})
        if brand_id is not None:
            filter.update({"brandId": brand_id})
        if issue_scene is not None:
            filter['issueScene'] = coupon_category_pb.CouponCategory.IssueScene.Name(issue_scene)

        categories = self.__collection.find(filter)
        if categories:
            return [json_format.ParseDict(category, coupon_category_pb.CouponCategory(),
                                          ignore_unknown_fields=True) for category in categories]
        return None

    def add_coupon_category(self, coupon_category):
        """添加一个新的优惠券种类信息。

        Args:
          coupon_category: (CouponCategory) 优惠券种类信息结构体
        """
        coupon_category_json = json_format.MessageToDict(coupon_category, including_default_value_fields=True)
        self.__collection.insert_one(coupon_category_json)

    def update_or_create_coupon_category(self, coupon_category):
        """如果指定优惠券类型当前不存在，则添加一个新的优惠券类型信息。
           如果指定优惠券类型存在，则更新该优惠券类型信息。

        Args:
            coupon_category: (CouponCategory) 时来平台优惠券类型信息结构体
        """
        coupon_category_id = coupon_category.id
        coupon_category_json = json_format.MessageToDict(coupon_category, including_default_value_fields=True)
        if self.get_coupon_category(coupon_category_id):
            self.__collection.replace_one({'id': coupon_category_id},
                                          coupon_category_json, upsert=True)
        else:
            self.__collection.insert_one(coupon_category_json)

    def update_coupon_category_state(self, state, id=None, merchant_id=None, issue_scene=None, store_id=None):
        """更新优惠券种类的状态
        """
        update = {
            '$set': {
                'state': coupon_category_pb.CouponCategory.State.Name(state)
            }
        }

        query = {}
        if id is not None:
            query['id'] = id
        if merchant_id is not None:
            query['merchantId'] = merchant_id
        if store_id is not None:
            query['storeIdList'] = {'$in': [store_id]}
        if issue_scene is not None:
            query['issueScene'] = coupon_category_pb.CouponCategory.IssueScene.Name(issue_scene)

        self.__collection.update_many(
            query,
            update,
        )

    @property
    def __collection(self):
        """返回当前 DataAccess Helper 中用到的 Collection 实例

        TODO: 原则上一个 DataAccessHelper 仅对应一个 Collection 操作，
              DaoHelper 可设计成抽象类，提供抽象方法 db_name() 和 collection_name()，
              由子类去实现对应的数据库和集合名称，可减少重复代码
        """
        db_name = constants.MONGODB_COUPON_DATABASE_NAME
        collection_name = constants.MONGODB_COUPON_CATEGORY_COLLECTION_NAME
        return self.mongo_client[db_name][collection_name]

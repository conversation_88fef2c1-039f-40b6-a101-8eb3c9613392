import logging

from google.protobuf import json_format

import dao.constants as constants
import proto.membership_pb2 as membership_pb
import proto.ordering.keruyun.keruyun_member_card_balance_pb2 as keruyun_member_card_balance_pb
from dao.dao_helper import Da<PERSON><PERSON><PERSON>per

logger = logging.getLogger(__name__)


# Membership Data Access Helper，提供针对会员管理相关的数据访问服务。
class MembershipDataAccessHelper(DaoHelper):
    db = constants.MONGODB_MEMBERSHIP_DATABASE_NAME

    @property
    def _member_card_recharge_config_collection(self):
        c = constants.MONGODB_MEMBER_CARD_RECHARGE_CONFIG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _keruyun_member_card_balance_collection(self):
        c = constants.MONGODB_KERUYUN_MEMBER_CARD_BALANCE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def set_member_card_recharge_config(self, config):
        json_obj = json_format.MessageToDict(config, including_default_value_fields=True)
        self._member_card_recharge_config_collection.insert_one(json_obj)

    def add_or_update_member_card_recharge_config(self, config):
        matcher = {'id': config.id}
        json_obj = json_format.MessageToDict(config, including_default_value_fields=True)
        self._member_card_recharge_config_collection.update_one(matcher, {"$set": json_obj}, upsert=True)

    def get_member_card_recharge_configs(self, merchant_id, status=None):
        matcher = {"merchantId": merchant_id}
        if status is not None:
            matcher.update({"status": membership_pb.MemberCardRechargeConfig.Status.Name(status)})
        configs = self._member_card_recharge_config_collection.find(matcher)
        if configs.count() > 0:
            return [
                json_format.ParseDict(config, membership_pb.MemberCardRechargeConfig(), ignore_unknown_fields=True)
                for config in configs
            ]
        return []

    def add_or_update_keruyun_member_card_balance(self, keruyun_member_card_balance):
        matcher = {"phone": keruyun_member_card_balance.phone, "merchantId": keruyun_member_card_balance.merchant_id}
        json_obj = json_format.MessageToDict(keruyun_member_card_balance, including_default_value_fields=True)
        self._keruyun_member_card_balance_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_keruyun_member_card_balance(self, phone, merchant_id=None, brand_id=None, status=None):
        matcher = {"phone": phone}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if brand_id is not None:
            matcher = {"phone": phone, "brandId": brand_id}
        if status is not None:
            matcher.update({"status": keruyun_member_card_balance_pb.KeruyunMemberCardBalance.Status.Name(status)})
        keruyun_member_card_balance = self._keruyun_member_card_balance_collection.find_one(matcher)
        if not keruyun_member_card_balance:
            return None
        return json_format.ParseDict(
            keruyun_member_card_balance, keruyun_member_card_balance_pb.KeruyunMemberCardBalance(), ignore_unknown_fields=True
        )

    def get_member_card_recharge_config(self, merchant_id, recharge_category_id):
        matcher = {"merchantId": merchant_id, "id": recharge_category_id}
        config = self._member_card_recharge_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, membership_pb.MemberCardRechargeConfig(), ignore_unknown_fields=True)

    def get_member_card_category(self, card_category_id):
        """根据ID返回指定会员卡种类信息。

        Args:
            card_category_id: (string) 会员卡种类ID

        Returns:
            若指定ID会员卡类型存在，返回该会员卡类型对应的MemberCardCategory结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        card_category = db[constants.MONGODB_CARD_CATEGORY_COLLECTION_NAME].find_one({'id': card_category_id})
        if card_category:
            return json_format.ParseDict(card_category, membership_pb.MemberCardCategory(), ignore_unknown_fields=True)
        else:
            return None

    def get_member_card_categories_for_merchant(self, merchant_id, card_type=None):
        """根据商户ID返回属于该商户的所有会员卡种类信息。

        Args:
            merchant_id: (string) 商户ID

        Returns:
            (list of MemberCardCategory) 若指定ID商户存在，返回为该商户创建的所有对应的MemberCardCategory结构体，
            若不存在则返回None
        """
        matcher = {"merchantId": merchant_id}
        if card_type is not None:
            matcher.update({"wechatCard.cardType": card_type})
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        card_categories = db[constants.MONGODB_CARD_CATEGORY_COLLECTION_NAME].find(matcher)
        if card_categories:
            return [
                json_format.ParseDict(category, membership_pb.MemberCardCategory(), ignore_unknown_fields=True)
                for category in card_categories
            ]
        else:
            return None

    def add_member_card_category(self, card_category):
        """添加一个新的会员卡种类信息。

        Args:
            card_category: (MemberCardCategory) 会员卡种类信息结构体
        """
        json_obj = json_format.MessageToDict(card_category, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        db[constants.MONGODB_CARD_CATEGORY_COLLECTION_NAME].insert_one(json_obj)

    def get_member_card_by_id(self, card_id):
        """根据ID返回指定会员卡信息。

        Args:
            card_id: (string) 会员卡ID

        Returns:
            若指定ID会员卡存在，返回该会员卡对应的MemberCard结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        card = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find_one({'id': card_id})
        if card:
            return json_format.ParseDict(card, membership_pb.MemberCard(), ignore_unknown_fields=True)
        else:
            return None

    def get_member_cards(
        self,
        merchant_id=None,
        user_id=None,
        activate_start_time=None,
        orderby=None,
        activate_end_time=None,
        page=None,
        size=None,
        last_issue_time=None,
        last_initial_activate_time=None,
    ):
        """根据指定查询条件，返回对应的会员卡列表。

        Args:
            user_id: (string) 用户ID
            merchant_id: (string) 商户ID
            activate_start_time: (int) 所需查询的激活会员卡的周期起始时间
            activate_end_time: (int) 所需查询的激活会员卡的周期结束时间

        Returns:
            (list of MemberCard) 若指定会员卡存在，返回与该会员对应的所有MemberCard结构体，
            若不存在则返回None。
        """
        filter = {}
        if merchant_id is not None:
            filter['merchantId'] = merchant_id
        if user_id is not None:
            filter['userId'] = user_id
        # 需小心处理activate_start_time和activate_end_time不同取值情况下的filter结构更新
        if activate_start_time is not None or activate_end_time is not None or last_initial_activate_time is not None:
            filter['initialActivateTime'] = {}
        if activate_start_time is not None:
            filter['initialActivateTime']["$gt"] = str(activate_start_time)
        if activate_end_time is not None:
            filter['initialActivateTime']["$lt"] = str(activate_end_time)
        if last_issue_time is not None:
            filter['issueTime'] = {"$lt": str(last_issue_time)}
        if last_initial_activate_time is not None:
            filter['initialActivateTime']['$lt'] = str(last_initial_activate_time)
        if not filter:
            return []

        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        cursor = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find(filter)
        if orderby is not None:
            cursor.sort(orderby)
        if page and size:
            skip = (page - 1) * size
            member_cards = cursor.skip(skip).limit(size)
        elif size:
            member_cards = cursor.limit(size)
        else:
            member_cards = cursor
        logger.info("获取会员卡列表: {}".format(filter))
        if member_cards:
            return [
                json_format.ParseDict(card, membership_pb.MemberCard(), ignore_unknown_fields=True) for card in member_cards
            ]
        else:
            return []

    def add_member_card(self, member_card):
        """添加一个新的会员卡信息。

        Args:
            member_card: (MemberCard) 会员卡信息结构体
        """
        json_obj = json_format.MessageToDict(member_card, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].insert_one(json_obj)

    def update_or_create_member_card(self, member_card):
        """更新或者添加一个新的会员卡信息。

        Args:
            member_card: (MemberCard) 会员卡信息结构体
        """
        card = self.get_member_card_by_id(member_card.id)
        if card:
            json_obj = json_format.MessageToDict(member_card, including_default_value_fields=True)
            db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
            db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].replace_one({'id': member_card.id}, json_obj, upsert=True)
        else:
            self.add_member_card(member_card)

    def get_member_cards_by_merchant_id(self, merchant_id, activate_start_time=None, activate_end_time=None):
        """根据商户ID返回所有注册会员信息。

        Args:
            merchant_id: (string) 商户ID
            activate_start_time: (int) 所需查询的激活会员卡的周期起始时间
            activate_end_time: (int) 所需查询的激活会员卡的周期结束时间

        Returns:
            (list of User) 若指定ID商户存在，返回该商户对应的所有注册会员的User结构体，若不存在则返回None
        """
        filter = {'merchantId': merchant_id}
        # 需小心处理activate_start_time和activate_end_time不同取值情况下的filter结构更新
        if activate_start_time is not None or activate_end_time is not None:
            filter['initialActivateTime'] = {}
        if activate_start_time is not None:
            filter['initialActivateTime']["$gt"] = str(activate_start_time)
        if activate_end_time is not None:
            filter['initialActivateTime']["$lt"] = str(activate_end_time)

        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        member_cards = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find(filter)
        if member_cards:
            return [
                json_format.ParseDict(card, membership_pb.MemberCard(), ignore_unknown_fields=True) for card in member_cards
            ]
        return None

    def get_member_cards_for_user(self, user_id):
        """根据指定用户ID返回所有属于该用户的会员卡信息。

        Args:
            user_id: (string) 用户ID

        Returns:
            (list of MemberCard) 若指定用户存在，返回该用户对应的所有领取的MemberCard结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        member_cards = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find({'userId': user_id})
        if member_cards:
            return [
                json_format.ParseDict(card, membership_pb.MemberCard(), ignore_unknown_fields=True) for card in member_cards
            ]
        else:
            return None

    def is_member(self, user_id, merchant_id):
        """返回某个用户是不是某个商户的会员
        Args:
            user_id: (string)用户id
            merchant_id: (string)商户id
        Return:
            MemberCard
        """
        matcher = {"userId": user_id, "merchantId": merchant_id}
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        member_card = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find_one(matcher)
        if member_card:
            return json_format.ParseDict(member_card, membership_pb.MemberCard(), ignore_unknown_fields=True)
        else:
            return None

    def count_member_card(self, merchant_id=None, start_time=None, end_time=None):
        matcher = {"merchantId": merchant_id}

        initial_activate_time = {}
        if start_time is not None:
            initial_activate_time.update({"$gt": str(start_time)})
        if end_time is not None:
            initial_activate_time.update({"$lt": str(end_time)})
        if initial_activate_time:
            matcher.update({"initialActivateTime": initial_activate_time})
        if matcher:
            db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
            count = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].count(matcher)
            logger.info("count_member_card: {}, {}".format(matcher, count))
            return count
        return 0

    def get_member_card(self, user_id=None, merchant_id=None, brand_id=None):
        matcher = {}
        if user_id is not None:
            matcher.update({"userId": user_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if brand_id is not None:
            matcher.update({"brandId": brand_id})

        if not matcher:
            return None
        db = self.mongo_client[constants.MONGODB_MEMBERSHIP_DATABASE_NAME]
        member_card = db[constants.MONGODB_MEMBER_CARD_COLLECTION_NAME].find_one(matcher)
        if not member_card:
            return None
        return json_format.ParseDict(member_card, membership_pb.MemberCard(), ignore_unknown_fields=True)

    def get_keruyun_member_cards(self, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        keruyun_member_card_balances = self._keruyun_member_card_balance_collection.find(matcher)
        if not keruyun_member_card_balances:
            return []
        return [
            json_format.ParseDict(
                keruyun_member_card_balance,
                keruyun_member_card_balance_pb.KeruyunMemberCardBalance(),
                ignore_unknown_fields=True,
            )
            for keruyun_member_card_balance in keruyun_member_card_balances
        ]

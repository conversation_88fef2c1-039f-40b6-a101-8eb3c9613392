# -*- coding: utf-8 -*-


from dao import constants
from dao.dao_helper import DaoHelper


class FrontendLogDataAccessHelper(DaoHelper):

    db = constants.MONGODB_MISC_DATABASE_NAME

    @property
    def _frontend_log_collection(self):
        c = constants.MONGODB_FRONTEND_LOG_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_log(self, log):
        self._frontend_log_collection.insert(log)

    def get_logs(self, user_id):
        matcher = {"userId": user_id}
        return self._frontend_log_collection.find(matcher, {"_id": 0})

# -*- coding: utf-8 -*-


from google.protobuf import json_format

import proto.comment_pb2 as comment_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class CommentDataAccessHelper(DaoHelper):
    db = constants.MONGODB_COMMENT_DATABASE_NAME
    @property
    def _comment_collection(self):
        c = constants.MONGODB_COMMENT_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_comment(self, comment):
        matcher = {"id": comment.id}
        json_data = json_format.MessageToDict(comment, including_default_value_fields=True)
        self._comment_collection.update(matcher, {"$set": json_data}, upsert=True)

    def get_comments(self, merchant_id=None, latest_create_time=None):
        matcher = {"status": comment_pb.UserComment.Status.Name(comment_pb.UserComment.NORMAL)}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if latest_create_time is not None:
            matcher.update({"createTime": {"$lt": latest_create_time}})
        comments = self._comment_collection.find(matcher).sort([("createTime", -1)]).limit(20)
        return [json_format.ParseDict(comment, comment_pb.UserComment(), ignore_unknown_fields=True) for comment in comments]

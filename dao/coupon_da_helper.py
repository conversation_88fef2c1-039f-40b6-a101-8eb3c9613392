# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import dao.constants as constants
import proto.coupons_pb2 as coupons_pb

from dao.dao_helper import DaoHelper

logger = logging.getLogger(__name__)


# Coupon Data Access Helper，提供针对优惠券管理相关的数据访问服务。
class CouponDataAccessHelper(DaoHelper):

    db = constants.MONGODB_COUPON_DATABASE_NAME

    @property
    def _coupon_collection(self):
        c = constants.MONGODB_COUPON_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _coupon_package_collection(self):
        collection = constants.MONGODB_COUPON_PACKAGE_COLLECTION_NAME
        return self.mongo_client[self.db][collection]

    def get_coupon_by_id(self, coupon_id):
        """根据ID返回指定优惠券信息。

        Args:
            coupon_id: (string) 优惠券ID

        Returns:
            (Coupon) 若指定ID优惠券存在，返回该优惠券对应的Coupon结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_COUPON_DATABASE_NAME]
        coupon = db[constants.MONGODB_COUPON_COLLECTION_NAME].find_one({'id': coupon_id})
        if coupon:
            return json_format.ParseDict(coupon, coupons_pb.Coupon(),
                                         ignore_unknown_fields=True)
        else:
            return None

    def get_user_coupon_by_category_id(self, user_id, coupon_category_id):
        """根据优惠券类型ID返回指定用户的相应优惠券信息。

        Args:
            user_id: (string) 用户ID
            coupon_category_id: (string) 优惠券类型ID

        Returns:
            (Coupon) 若指定ID优惠券存在，返回该优惠券对应的Coupon结构体，若不存在则返回None
        """
        db = self.mongo_client[constants.MONGODB_COUPON_DATABASE_NAME]
        coupon = db[constants.MONGODB_COUPON_COLLECTION_NAME].find_one({
            'userId': user_id,
            'couponCategoryId': coupon_category_id,
        })
        if coupon:
            return json_format.ParseDict(coupon, coupons_pb.Coupon(),
                                         ignore_unknown_fields=True)
        else:
            return None

    def add_coupon(self, coupon):
        """添加一个新的优惠券信息。

        Args:
          coupon: (Coupon) 优惠券信息结构体
        """
        json_obj = json_format.MessageToDict(coupon, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_COUPON_DATABASE_NAME]
        db[constants.MONGODB_COUPON_COLLECTION_NAME].insert_one(json_obj)

    def update_or_create_coupon(self, coupon):
        """如果指定优惠券当前不存在，则添加一个新的优惠券信息。
           如果指定优惠券存在，则更新该优惠券信息。

        Args:
            coupon: (Coupon) 时来平台优惠券信息结构体
        """
        coupon_id = coupon.id
        if self.get_coupon_by_id(coupon_id):
            json_obj = json_format.MessageToDict(coupon, including_default_value_fields=True)
            db = self.mongo_client[constants.MONGODB_COUPON_DATABASE_NAME]
            db[constants.MONGODB_COUPON_COLLECTION_NAME].replace_one({'id': coupon_id}, json_obj, upsert=True)
        else:
            self.add_coupon(coupon)

    def get_coupon_list(self, merchant_id=None, user_id=None, coupon_category_id=None, state=None,
                        issue_time_start=None, issue_time_end=None, accept_time_start=None, accept_time_end=None,
                        use_time_start=None, use_time_end=None, page=None, size=None, latest_reminder_timestamp=None):
        """
            根据查询条件返回匹配的优惠券列表。

            Args:
                merchant_id: (string) 商户ID
                user_id: (string) 用户ID
                coupon_category_id: (string) 优惠券类型ID
                state: (CouponState) 优惠券状态
                issue_time_start: (int) 优惠券投放时间的查询起始点
                issue_time_end: (int) 优惠券投放时间的查询结束点
                accept_time_start: (int) 优惠券领取时间的查询起始点
                accept_time_end: (int) 优惠券领取时间的查询结束点
                use_time_start: (int) 优惠券使用时间的查询起始点
                use_time_end: (int) 优惠券使用时间的查询结束点

            Returns:
                (list of Coupon) 若有符合条件则返回所有匹配优惠券列表，否则返回None
        """
        filter = {}
        if merchant_id is not None:
            filter['merchantId'] = merchant_id
        if user_id is not None:
            filter['userId'] = user_id
        if coupon_category_id is not None:
            filter['couponCategoryId'] = coupon_category_id
        if state is not None:
            filter['state'] = coupons_pb.Coupon.CouponState.Name(state)
        if state is None:
            filter['state'] = "ACCEPTED"
        if issue_time_start is not None or issue_time_end is not None:
            filter['issueTime'] = {}
        if issue_time_start is not None:
            filter['issueTime']["$gt"] = str(issue_time_start)
        if issue_time_end is not None:
            filter['issueTime']["$lt"] = str(issue_time_end)
        if accept_time_start is not None or accept_time_end is not None:
            filter['acceptTime'] = {}
        if accept_time_start is not None:
            filter['acceptTime']["$gt"] = str(accept_time_start)
        if accept_time_end is not None:
            filter['acceptTime']["$lt"] = str(accept_time_end)
        if use_time_start is not None or use_time_end is not None:
            filter['useTime'] = {}
        if use_time_start is not None:
            filter['useTime']["$gt"] = str(use_time_start)
        if use_time_end is not None:
            filter['useTime']["$lt"] = str(use_time_end)
        if latest_reminder_timestamp is not None:
            filter.update({
                "latestReminderTimestamp": {
                    "$lt": str(latest_reminder_timestamp)
                }
            })

        db = self.mongo_client[constants.MONGODB_COUPON_DATABASE_NAME]
        cursor = db[constants.MONGODB_COUPON_COLLECTION_NAME].find(filter)
        logger.info(f"返回数量get coupon list: {filter} {cursor.count()}")
        if page and size:
            skip = (page - 1) * size
            coupons = cursor.skip(skip).limit(size)
        else:
            coupons = cursor
        if coupons:
            return [json_format.ParseDict(coupon,
                                          coupons_pb.Coupon(),
                                          ignore_unknown_fields=True) for coupon in coupons]
        else:
            return None

    def add_or_update_coupon_package(self, coupon_package):
        matcher = {'transactionId': coupon_package.transaction_id}
        json_obj = {'$set': json_format.MessageToDict(coupon_package, including_default_value_fields=True)}
        self._coupon_package_collection.update(matcher, json_obj, upsert=True)

    def get_coupon_package(self, transaction_id=None):
        matcher = {}
        if transaction_id is not None:
            matcher.update({'transactionId': transaction_id})
        if not matcher:
            return None
        coupon_package = self._coupon_package_collection.find_one(matcher)
        if not coupon_package:
            return None
        return json_format.ParseDict(coupon_package, coupons_pb.CouponPackage(), ignore_unknown_fields=True)

    def get_user_coupons(self, user_id=None, coupon_category_id=None, state=None):
        matcher = {}
        if user_id is not None:
            matcher.update({"userId": user_id})
        if coupon_category_id is not None:
            matcher.update({"couponCategoryId": coupon_category_id})
        if state is not None:
            matcher.update({"state": coupons_pb.Coupon.State.Name(state)})
        if not matcher:
            return None
        coupons = self._coupon_collection.find(matcher)
        if not coupons:
            return None
        return [json_format.ParseDict(coupon, coupons_pb.Coupon(), ignore_unknown_fields=True)
                for coupon in coupons]

# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
import dao.constants as dao_constants
import proto.merchant_rules_pb2 as merchant_rules_pb

class TransferStoreTransactionDataAccessHelper(DaoHelper):
    @property
    def __collection(self):
        db = dao_constants.MONGODB_TRANSACTION_DATABASE_NAME
        collection = dao_constants.MONGODB_TRANSACTION_TRANSFER_STORE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __merchant_transfers_collection(self):
        db = dao_constants.MONGODB_TRANSACTION_DATABASE_NAME
        collection = dao_constants.MONGODB_TRANSACTION_MERCHANT_TRANSFERS_COLLECTION_NAME
        print(db, collection)
        return self.mongo_client[db][collection]

    def add_transaction(self, transaction):
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)

    def update_transaction(self, transaction):
        match = {
            "id": transaction.id
        }
        json_obj = json_format.MessageToDict(transaction, including_default_value_fields=True)
        self.__collection.replace_one(match, json_obj)

    def get_transaction_by_store_id_date(self, store_id, state, date):
        match = {
            "storeId": store_id,
            "state": merchant_rules_pb.TransferStoreTransaction.TransactionState.Name(state),
            "transferForDate": date
        }
        transaction = self.__collection.find_one(match)
        if transaction:
            return json_format.ParseDict(transaction, merchant_rules_pb.TransferStoreTransaction(), ignore_unknown_fields=True)
        return None

    def get_merchant_latest_transfer(self, merchant_id):
        matcher = {
            'merchantId': merchant_id
        }
        transfer = self.__merchant_transfers_collection.find(matcher).sort([("transferTime", -1)]).limit(1)
        if transfer.count() > 0:
            return json_format.ParseDict(transfer[0], merchant_rules_pb.MerchantTransfers(), ignore_unknown_fields=True)
        return None

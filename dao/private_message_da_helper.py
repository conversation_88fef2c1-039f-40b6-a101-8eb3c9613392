from google.protobuf import json_format

from dao import constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.sns import messaging_pb2 as messaging_pb

class PrivateMessageDataAccessHelper(DaoHelper):

    @property
    def __collection(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_PRIVATE_MESSAGE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __private_message_view_time_collection(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_PRIVATE_MESSAGE_VIEW_TIME_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_message(self, message):
        obj = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection.insert_one(obj)

    def add_or_update_last_view_time(self, user_id, id, last_viewed_time=None, deleted=None):
        matcher = {
            "userId": user_id,
            "id": id
        }
        udata = {}
        if last_viewed_time is not None:
            udata.update({"lastViewedTime": last_viewed_time})
        if deleted is not None:
            udata.update({"deleted": deleted})
        self.__private_message_view_time_collection.update(matcher, {"$set": udata}, upsert=True)

    def get_private_message_last_viewed_time(self, id, user_id):
        matcher = {
            "id": id,
            "userId": user_id
        }
        last_viewed_time = self.__private_message_view_time_collection.find_one(matcher)
        if last_viewed_time:
            return json_format.ParseDict(last_viewed_time,
                                         messaging_pb.PrivateMessageLastViewedTime(),
                                         ignore_unknown_fields=True)
        return None

    def get_private_message_notifies(self, user_id=None, last_viewed_time=None, deleted=None,
                                     page=None, size=None, orderby=None):
        matcher = {}
        if user_id is not None:
            matcher.update({"userId": user_id})
        if last_viewed_time is not None:
            matcher.update({"lastViewedTime": last_viewed_time})
        if deleted is not None:
            matcher.update({"deleted": deleted})
        cursor = self.__private_message_view_time_collection.find(matcher)
        if orderby is not None:
            cursor.sort(orderby)
        if page is not None and size is not None:
            skip = (page - 1) * size
            notifies = cursor.skip(skip).limit(size)
        else:
            notifies = cursor
        return [json_format.ParseDict(notify, messaging_pb.PrivateMessageLastViewedTime(),
                                      ignore_unknown_fields=True) for notify in notifies]

    def get_messages(self, id=None, page=None, size=None, orderby=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if matcher:
            cursor = self.__collection.find(matcher)
            if orderby is not None:
                cursor = cursor.sort(orderby)
            if page is not None and size is not None:
                skip = (page - 1) * size
                messages = cursor.skip(skip).limit(size)
            else:
                messages = cursor
            return [json_format.ParseDict(message, messaging_pb.ChatMessage(),
                                          ignore_unknown_fields=True) for message in messages]
        return None

    def count_unread(self, id, receiver_id, last_viewed_time):
        matcher = {
            "id": id,
            "receiverId": receiver_id,
            "createTime": {"$gt": str(last_viewed_time)}
        }
        return self.__collection.count(matcher)

    def delete_private_message(self, id, user_id):
        matcher = {
            "id": id,
            "userId": user_id
        }
        udata = {"deleted": True}
        self.__private_message_view_time_collection.update(matcher, {"$set": udata})

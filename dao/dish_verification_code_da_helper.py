# -*- coding: utf-8 -*-

from google.protobuf import json_format

import proto.verification_code_pb2 as verification_code_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class DishVerificationCodeDataAccessHelper(DaoHelper):

    db = constants.MONGODB_VERIFICATION_CODE_DATABASE_NAME

    @property
    def _dish_verification_code_strategies_collection(self):
        c = constants.MONGODB_DISH_VERIFICATION_CODE_STRATEGY_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _dish_verification_codes_collection(self):
        c = constants.MONGODB_DISH_VERIFICATION_CODE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_strategy(self, strategy):
        matcher = {"id": strategy.id}
        json_obj = json_format.MessageToDict(strategy, including_default_value_fields=True)
        self._dish_verification_code_strategies_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def add_or_update_dish_verification_code(self, dish_verification_code):
        matcher = {"id": dish_verification_code.id}
        json_obj = json_format.MessageToDict(dish_verification_code, including_default_value_fields=True)
        self._dish_verification_codes_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_dish_verification_codes(self, brand_id=None, msg=None):

        # 这些是必填参数
        if None in (brand_id, msg):
            return None

        matcher = {"brandId": brand_id, "msg": msg}
        dish_coupon_codes = self._dish_verification_codes_collection.find(matcher)
        return [json_format.ParseDict(dish_coupon_code, verification_code_pb.DishVerificationCode(), ignore_unknown_fields=True)
                for dish_coupon_code in dish_coupon_codes]

    def get_dish_verification_code_strategy(self, brand_id, strategy_id):
        if None in (brand_id, strategy_id):
            return None

        matcher = {
            "brandId": brand_id,
            "id": strategy_id
        }

        strategy = self._dish_verification_code_strategies_collection.find_one(matcher)
        if strategy:
            return json_format.ParseDict(strategy, verification_code_pb.DishVerificationCodeStrategy(), ignore_unknown_fields=True)
        return None

    def get_dish_verification_code_strategies(self, brand_id, dish_id=None):
        if None in (brand_id,):
            return None

        matcher = {
            "brandId": brand_id
        }
        if dish_id is not None:
            matcher.update({"dishId": dish_id})
        strategies = self._dish_verification_code_strategies_collection.find(matcher)
        if strategies.count() == 0:
            return []
        return [json_format.ParseDict(strategy, verification_code_pb.DishVerificationCodeStrategy(), ignore_unknown_fields=True)
                for strategy in strategies]

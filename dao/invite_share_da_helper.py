# -*- coding: utf-8 -*-


from google.protobuf import json_format

import proto.activity.invite_share_pb2 as invite_share_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class InviteShareDataAccessHelper(DaoHelper):

    db = constants.MONGODB_ACTIVITY_DATABASE_NAME

    @property
    def _invite_share_collection(self):
        c = constants.MONGODB_INVITE_SHARE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    @property
    def _invitee_share_collection(self):
        c = constants.MONGODB_INVITEE_SHARE_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_invite_share(self, share):
        matcher = {"id": share.id}
        json_obj = json_format.MessageToDict(share, including_default_value_fields=True)
        self._invite_share_collection.update(matcher, {"$set": json_obj}, upsert=True)

    def get_invite_share(self, id=None, user_id=None, merchant_id=None, status=None):
        matcher = {}

        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if status is not None:
            matcher.update({"status": invite_share_pb.InviteShare.Status.Name(status)})
        if not matcher:
            return None
        share = self._invite_share_collection.find_one(matcher)
        if not share:
            return None
        return json_format.ParseDict(share, invite_share_pb.InviteShare(), ignore_unknown_fields=True)

    def get_invite_shares(self, user_id=None, merchant_id=None, status=None):
        matcher = {}

        if user_id is not None:
            matcher.update({"userId": user_id})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if status is not None:
            matcher.update({"status": invite_share_pb.InviteShare.Status.Name(status)})
        if not matcher:
            return None
        shares = self._invite_share_collection.find(matcher)
        if not shares:
            return None
        return [json_format.ParseDict(share, invite_share_pb.InviteShare(), ignore_unknown_fields=True)
                for share in shares]

    def add_or_update_invitee_share(self, invitee_share):
        matcher = {"id": invitee_share.id}
        udata = json_format.MessageToDict(invitee_share, including_default_value_fields=True)
        self._invitee_share_collection.update(matcher, {"$set": udata}, upsert=True)

    def get_invitee_shares(self, id=None, user_id=None, invitor_id=None, status=None, invite_share_id=None, merchant_id=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if invitor_id is not None:
            matcher.update({"invitorId": invitor_id})
        if status is not None:
            matcher.update({"status": status})
        if invite_share_id is not None:
            matcher.update({"inviteShareId": invite_share_id})
        if not matcher:
            return None
        shares = self._invitee_share_collection.find(matcher)
        if not shares:
            return None
        return [json_format.ParseDict(share, invite_share_pb.InviteeShare(), ignore_unknown_fields=True)
                for share in shares]

    def get_invitee_share(self, id=None, user_id=None, invitor_id=None, status=None, coupon_id=None):
        matcher = {}
        if id is not None:
            matcher.update({"id": id})
        if user_id is not None:
            matcher.update({"userId": user_id})
        if invitor_id is not None:
            matcher.update({"invitorId": invitor_id})
        if status is not None:
            matcher.update({"status": status})
        if coupon_id is not None:
            matcher.update({"couponId": coupon_id})
        if not matcher:
            return None
        share = self._invitee_share_collection.find_one(matcher)
        if not share:
            return None
        return json_format.ParseDict(share, invite_share_pb.InviteeShare(), ignore_unknown_fields=True)

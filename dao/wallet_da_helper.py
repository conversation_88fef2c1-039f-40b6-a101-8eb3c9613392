# -*- coding: utf-8 -*-

import logging
import time

from google.protobuf import json_format

from dao.dao_helper import DaoHelper
from dao import constants
from dao.transaction_da_helper import TransactionDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb

logger = logging.getLogger(__name__)


class WalletDataAccessHelper(DaoHelper):
    """时来钱包"""

    def add_or_update_wallet(self, wallet):
        matcher = {"ownerId": wallet.owner_id}
        wallet.update_time = int(time.time())
        json_data = json_format.MessageToDict(wallet, including_default_value_fields=True)
        db = self.mongo_client[constants.MONGODB_WALLET_DATABASE_NAME]
        db[constants.MONGODB_WALLET_COLLECTION_NAME].update_one(matcher, {"$set": json_data}, upsert=True)

    def get_user_wallet(self, owner_id):
        """获取用户的钱包
        Args:
            owner_id

        Return:
            Wallet结构体
        """
        match = {"ownerId": owner_id}

        db = self.mongo_client[constants.MONGODB_WALLET_DATABASE_NAME]
        wallet = db[constants.MONGODB_WALLET_COLLECTION_NAME].find_one(match)
        if wallet:
            # merchantBalance为空的情况下需要进行一次红包余额迁移
            if wallet.get('merchantBalance') is None:
                transactions = TransactionDataAccessHelper().query_list(
                    matcher={'payeeId': owner_id, 'state': 'SUCCESS', 'type': 'RED_PACKET_DEPOSIT'},
                    project={'payerId': 1, 'billFee': 1},
                    sort=[('paidTime', -1)],
                )
                org_balance = int(wallet.get('balance', 0))
                org_lock_balance = wallet.get('lockBalance')
                all_red_fee = 0
                for transaction in transactions:
                    if not transaction.get('payerId'):
                        continue
                    total_fee = int(transaction.get('billFee', 0))
                    all_red_fee += total_fee
                    if org_lock_balance is not None:
                        balance = int(wallet.get('lockBalance', 0))
                        diff_fee = balance - total_fee
                        wallet['lockBalance'] = diff_fee if diff_fee > 0 else 0
                    else:
                        balance = int(wallet.get('balance', 0))
                        diff_fee = balance - total_fee
                        wallet['balance'] = diff_fee if diff_fee > 0 else 0
                    merchant_balance = wallet.get('merchantBalance', {})
                    merchant_balance.update(
                        {
                            transaction.get('payerId'): merchant_balance.get(transaction.get('payerId'), 0)
                            + (total_fee if diff_fee > 0 else balance)
                        }
                    )
                    wallet.update({'merchantBalance': merchant_balance})
                wallet = json_format.ParseDict(wallet, wallet_pb.Wallet(), ignore_unknown_fields=True)
                self.add_or_update_wallet(wallet)
                logger.info(
                    f"零钱迁移 {owner_id} {org_balance} {org_lock_balance or 0} {all_red_fee} after {wallet.balance} {wallet.lock_balance} {wallet.merchant_balance}"
                )
                return wallet
            return json_format.ParseDict(wallet, wallet_pb.Wallet(), ignore_unknown_fields=True)

        return None

    def increase_balance(self, owner_id, amount, lock_amount=0, merchant_id=None):
        """钱包金额增加
        Args:
            owner_id: 钱包拥有者id
            amount: 增加的数量
        Return:
            None
        """
        match = {"ownerId": owner_id}
        inc = {'balance': amount}
        if lock_amount and merchant_id:
            inc.update({f'merchantBalance.{merchant_id}': lock_amount})
        udata = {"$inc": inc, "$set": {"updateTime": int(time.time())}}
        db = self.mongo_client[constants.MONGODB_WALLET_DATABASE_NAME]
        db[constants.MONGODB_WALLET_COLLECTION_NAME].update_one(match, udata, upsert=True)
        tmp_max = {'balance': 0}
        if lock_amount and merchant_id:
            tmp_max.update({f'merchantBalance.{merchant_id}': 0})
        db[constants.MONGODB_WALLET_COLLECTION_NAME].update_one(match, {'$max': tmp_max})
        return True

    def decrease_balance(self, owner_id, amount, lock_amount=0, merchant_id=None):
        """钱包金额减少
        Args:
            owner_id: 钱包拥有者id
            amount: 减少的数量
        Return:
            None
        """
        return self.increase_balance(owner_id, amount * (-1), lock_amount * (-1), merchant_id)

    def increase_coin_balance(self, owner_id, amount):
        """时来币金额增加"""
        match = {"ownerId": owner_id}
        udata = {"$inc": {"coinBalance": amount}, "$set": {"updateTime": int(time.time())}}
        db = self.mongo_client[constants.MONGODB_WALLET_DATABASE_NAME]
        db[constants.MONGODB_WALLET_COLLECTION_NAME].update(match, udata, upsert=True)
        return True

    def decrease_coin_balance(self, owner_id, amount):
        return self.increase_coin_balance(owner_id, amount * (-1))

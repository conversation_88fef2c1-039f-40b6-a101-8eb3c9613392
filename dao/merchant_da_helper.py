# -*- coding: utf-8 -*-

import logging
import uuid
from google.protobuf import json_format

# from pymongo import MongoClient
from common.utils.db_utils import *

import dao.constants as constants
import proto.merchant_rules_pb2 as merchant_pb
from cache.dish_cache import DishCatalogCache
from cache.get_merchant_redis_helper import get_merchant as get_merchant_cache
from cache.get_merchant_redis_helper import update_merchant as update_merchant_cache
from common.utils import date_utils
from common.utils import id_manager
from dao.dao_helper import DaoHelper
from dao.membership_da_helper import MembershipDataAccessHelper

logger = logging.getLogger(__name__)


# Merchant Data Access Helper (商户数据访问抽象层)，提供针对商户相关的数据访问服务。
# 作用: 通过对后端数据Persistence机制的封装，实现与前端业务逻辑的解耦。
class MerchantDataAccessHelper(DaoHelper):
    @property
    def __collection(self):
        db = constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME
        collection = constants.MONGODB_MERCHANT_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def _brand_collection(self):
        c = constants.MONGODB_BRAND_COLLECTION_NAME
        return self.mongo_client[self.db][c]

    def add_or_update_brand(self, brand):
        matcher = {"id": brand.id}
        brand_json = json_format.MessageToDict(brand, including_default_value_fields=True)
        self._brand_collection.update(matcher, {"$set": brand_json}, upsert=True)

    def get_brands(self):
        matcher = {}
        brands = self._brand_collection.find(matcher)
        return [json_format.ParseDict(brand, merchant_pb.BrandInfo(), ignore_unknown_fields=True) for brand in brands]

    def get_brand(self, id):
        matcher = {"id": id}
        brand = self._brand_collection.find_one(matcher)
        if not brand:
            return None
        return json_format.ParseDict(brand, merchant_pb.BrandInfo(), ignore_unknown_fields=True)

    def get_merchant_by_id(self, merchant_id):
        matcher = {'id': merchant_id}

        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchant = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find_one(matcher)
        if merchant:
            return json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True)
        else:
            return None

    def get_merchants_by_ids(self, ids):
        matcher = {"id": {"$in": ids}}
        merchants = self.__collection.find(matcher)
        if merchants:
            return [
                json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants
            ]
        return []

    def get_merchants_by_name(self, name):
        matcher = {'basicInfo.name': {'$regex': '.*{}.*'.format(name)}}
        merchants = self.__collection.find(matcher)
        if merchants:
            return [
                json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants
            ]
        return []

    def get_merchants_for_latlng(self, enable_group_dining=None, status=None):
        """获取所有相关的商户,用来做经纬度计算"""
        matcher = {}
        if enable_group_dining is not None:
            matcher.update({"stores.0.enableGroupDining": enable_group_dining})
        if status is not None:
            matcher.update({"status": merchant_pb.MerchantStatus.Name(status)})
        col_filter = {"id": 1, "stores": 1}  # 只返回这些内容
        merchants = self.__collection.find(matcher, col_filter)
        if merchants:
            return [
                json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants
            ]
        return None

    # @get_merchant_cache
    def get_merchant(self, merchant_id=None, status=None, enable_group_dining=None, name=None, return_proto=True, **kargs):
        """根据ID返回指定商户信息。

        Args:
            merchant_id: (string) 商户ID
            state: (MerchantStats) 商户状态

        Returns:
            若指定ID商户存在，返回该商户对应的Merchant结构体，若不存在则返回None
        """
        matcher = {}
        if merchant_id is not None:
            matcher.update({"id": merchant_id})
        if status is not None:
            matcher['status'] = merchant_pb.MerchantStatus.Name(status)
        if enable_group_dining is not None:
            matcher.update({'stores': {'$elemMatch': {'enableGroupDining': enable_group_dining}}})
        if name is not None:
            matcher.update({"basicInfo.displayName": name})
        handheld_sn = kargs.get("handheld_sn")
        if handheld_sn is not None:
            matcher.update({"handheldPos.sn": handheld_sn})
        if not matcher:
            return None
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        if return_proto:
            merchant = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find_one(matcher)
            if not merchant:
                return
            return json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True)
        return db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find_one(matcher, {'_id': False})

    def get_merchant_by_appid(self, appid):
        """根据授权方appid查询商户信息

        Args:
            appid: (string) 授权方appid

        Returns:
            (Merchant) 商户信息
        """
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchant = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find_one(
            {'shilaiPlatformAuthorizerInfo.authorizationInfo.authorizationAppid': appid}
        )
        if merchant:
            return json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True)
        return None

    def get_merchant_by_submerchant_id(self, submerchant_id):
        """根据时来公众号下的子商户 ID 查询商户信息

        Args:
            submerchant_id: (string) 时来公众号下子商户的 ID

        Returns:
            (Merchant) 商户信息
        """
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchant = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find_one({'shilaiMpSubmerchantInfo.id': submerchant_id})
        if merchant:
            return json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True)
        return None

    def get_merchant_list(
        self,
        binding_staff_id=None,
        manager_id=None,
        status=None,
        create_start_time=None,
        create_end_time=None,
        city=None,
        enable_group_dining=None,
        page=None,
        size=None,
        brand_id=None,
        fuzzy_query_name=None,
        staff_ids=None,
        main_staff_id=None,
        assist_staff_id=None,
        order_by=None,
        prev_create_timestamp=None,
        is_demonstration_merchant=None,
    ):
        """根据指定查询条件返回相应商户列表。

        Args:
            status: (MerchantStatus) 商户状态
            staff_id: (string) 跟商户对接的业务员ID
            manager_id: (string) 商户管理员ID
            create_start_time: (int) 商户创建时间的查询周期起始点
            create_end_time: (int) 商户创建时间的查询周期结束点

        Returns:
            (list of Merchant) 若符合条件商户存在则返回所有匹配商户列表，否则返回None
        """
        filter = {}
        staff_id = []
        if binding_staff_id is not None:
            staff_id.append({"bindingStaffId": binding_staff_id})
        if main_staff_id is not None:
            staff_id.append({"mainStaffId": main_staff_id})
        if assist_staff_id is not None:
            staff_id.append({"assistStaffIds": assist_staff_id})
        if len(staff_id) > 0:
            filter.update({"$or": staff_id})
        if staff_ids is not None:
            filter.update(
                {
                    "$or": [
                        {"bindingStaffId": {"$in": staff_ids}},
                        {"mainStaffId": {"$in": staff_ids}},
                        {"assistStaffIds": {"$in": staff_ids}},
                    ]
                }
            )

        if fuzzy_query_name is not None:
            filter.update({"basicInfo.displayName": {"$regex": ".*{}.*".format(fuzzy_query_name)}})
        if manager_id is not None:
            filter['managerList'] = {
                "$elemMatch": {'userId': manager_id},
            }
        if status is not None:
            if isinstance(status, int):
                filter['status'] = merchant_pb.MerchantStatus.Name(status)
            else:
                filter['status'] = status
        if create_start_time is not None or create_end_time is not None:
            filter['createTimestamp'] = {}
        if create_start_time is not None:
            filter['createTimestamp']["$gt"] = str(create_start_time)
        if create_end_time is not None:
            filter['createTimestamp']["$lt"] = str(create_end_time)
        if prev_create_timestamp is not None:
            filter.update({"createTimestamp": {"$lt": prev_create_timestamp}})
        if city is not None or enable_group_dining is not None:
            filter['stores'] = {}
            filter['stores']['$elemMatch'] = {}
        if city is not None:
            filter['stores']['$elemMatch']['poi.addressComponents.city'] = city
        if enable_group_dining is not None:
            filter['stores']['$elemMatch']['enableGroupDining'] = enable_group_dining
        if brand_id is not None:
            filter.update({"brandInfo.id": brand_id})
        if is_demonstration_merchant is not None:
            filter.update({"isDemonstrationMerchant": is_demonstration_merchant})

        logger.info("查询商家: {}".format(filter))

        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        cursor = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find(filter)
        if order_by is not None:
            cursor = cursor.sort(order_by)
        if page is not None and size is not None:
            skip = (page - 1) * size
            merchants = cursor.skip(skip).limit(size)
        elif size is not None:
            merchants = cursor.limit(size)
        else:
            merchants = cursor
        if merchants:
            return [
                json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants
            ]
        else:
            return None

    def get_merchant_for_card_category(self, card_category_id):
        """根据ID返回指定商户信息。

        Args:
            merchant_id: (string) 商户ID

        Returns:
            若指定ID商户存在，返回该商户对应的Merchant结构体，若不存在则返回None
        """
        card_category = MembershipDataAccessHelper().get_member_card_category(card_category_id)
        if card_category:
            return self.get_merchant(card_category.merchant_id)
        else:
            return None

    def add_merchant(self, merchant):
        """添加一个新的商户信息。

        Args:
            merchant: (Merchant) 新商户信息结构体
        """

        @DishCatalogCache.update
        def func(merchant):
            matcher = {"id": merchant.id}
            merchant.wechat_pay_type = merchant_pb.Merchant.TIAN_QUE
            json_obj = json_format.MessageToDict(merchant, including_default_value_fields=True)
            db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
            db[constants.MONGODB_MERCHANT_COLLECTION_NAME].update(matcher, {"$set": json_obj}, upsert=True)

        return func(merchant=merchant)

    @update_merchant_cache
    def update_or_create_merchant(self, merchant):
        """根据指定商户是否已存在，来选择更新或添加该商户。

        Args:
            merchant: (Merchant) 需更新或添加的商户信息
        """

        @DishCatalogCache.update
        def func(merchant):
            merchant_id = merchant.id
            db_merchant = self.get_merchant_by_id(merchant.id)
            if db_merchant:
                # 如果已有商户存在,则以已存的微信支付方式为准.如果以后在业务助手上能修改微信支付方式了,则去掉此段代码
                logger.info("更新商户信息1: {}, {}, {}".format(merchant.id, merchant.wechat_pay_type, db_merchant.wechat_pay_type))
                merchant.wechat_pay_type = db_merchant.wechat_pay_type
                logger.info("更新商户信息2: {}, {}, {}".format(merchant.id, merchant.wechat_pay_type, db_merchant.wechat_pay_type))
            if self.get_merchant(merchant_id):
                json_obj = json_format.MessageToDict(merchant, including_default_value_fields=True)
                db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
                db[constants.MONGODB_MERCHANT_COLLECTION_NAME].replace_one({'id': merchant_id}, json_obj, upsert=True)
            else:
                merchant.wechat_pay_type = merchant_pb.Merchant.TIAN_QUE
                self.add_merchant(merchant)

        return func(merchant=merchant)

    def get_merchant_list_bound_to_staff(self, staff_id, status=None):
        """
        根据 staff_id 返回指定商户信息。
        :param staff_id:  (string) 推荐员工信息
        :return: 若指定ID商户存在，返回该商户对应的Merchant结构体，若不存在则返回None
        """
        # 获取商户与员工对应关系
        match = {'bindingStaffId': staff_id}
        if status is not None:
            match['status'] = merchant_pb.MerchantStatus.Name(status)
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchants = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find(match)
        # 从MongoClient.Cursor类型转成普通list
        return [json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants]

    def get_merchant_list_bound_to_assist_staff(self, staff_id, status=None):
        match = {'assistStaffIds': staff_id}
        if status is not None:
            match['status'] = merchant_pb.MerchantStatus.Name(status)
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchants = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find(match)
        return [json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants]

    def get_merchants_by_user(self, user_id):
        """根据指定的用户UserID, 返回所有与该用户关联的商户。

        Args:
            user_id: (string) 指定用户ID
        """
        data_filter = {
            "managerList": {
                "$elemMatch": {
                    'userId': user_id,
                }
            }
        }
        db = self.mongo_client[constants.MONGODB_BUSINESS_ENTITIES_DATABASE_NAME]
        merchants = db[constants.MONGODB_MERCHANT_COLLECTION_NAME].find(data_filter)
        # 从MongoClient.Cursor类型转成普通list
        return [json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants]

    def update_merchant_status(self, merchant_id, status):
        """更新商户状态

        Args:
            merchant_id: (string) 指定商户ID
            status: (MerchantStatus) 更新的商户状态
        """
        merchant = self.get_merchant(merchant_id)
        if merchant:
            merchant.status = status
            self.update_or_create_merchant(merchant)

        return merchant

    def activate_merchant(self, merchant_id, wechat_mch_id):
        """激活商户，更改商户状态为RUNNING
        @Deprecated 移除

        Args:
            merchant_id: (string) 指定商户ID
            wechat_mch_id: (string) 微信支付分配的子商户号
        """
        merchant = self.get_merchant(merchant_id)
        if merchant:
            merchant.wechat_mch_id = wechat_mch_id
            merchant.status = merchant_pb.RUNNING
            self.update_or_create_merchant(merchant)

        return merchant

    def get_merchant_info(self, merchant_id):
        """
        根据 merchant_id 返回指定商户信息。
        :param merchant_id:  (string) 商户ID
        :return: 若指定ID商户存在，返回该商户对应的Merchant结构体，若不存在则返回None
        """
        data = DbUtils().get_merchant(merchant_id=merchant_id)

        merchant_json_data = []
        if data:
            for merchant in data:
                json_obj = json_format.MessageToDict(merchant, including_default_value_fields=True, use_integers_for_enums=True)
                merchant_json_data.append(json_obj)
        return merchant_json_data

    def get_user_merchant(self, user_id):
        """
        根据 user_id 返回指定商户信息。
        :param merchant_id:  (string) 商户ID
        :return: 若指定ID商户存在，返回该商户对应的Merchant结构体，若不存在则返回None
        """
        data = DbUtils().get_merchant(userid=user_id)

        merchant_json_data = []
        if data:
            for merchant in data:
                json_obj = json_format.MessageToDict(merchant, including_default_value_fields=True, use_integers_for_enums=True)
                merchant_json_data.append(json_obj)
        return merchant_json_data

    def get_merchant_authorizer_status(self, merchant_id):
        """
            获取商家信息状态
        :param merchant_id:
        :return:
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if merchant:
            return True
        else:
            return False

    def save_merchant_info_basic(self, merchant_id, request):
        """
        5.	保存商家基本信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        print(request.json)
        basic_info = json_format.ParseDict(request.json, merchant_pb.MerchantBasic(), ignore_unknown_fields=True)
        merchant.basic_info.CopyFrom(basic_info)

        # 更新商家信息
        self.db_utils.set_merchant(merchant)

        return merchant.basic_info

    def get_merchant_info_basic(self, merchant_id):
        """
        5.	获取商家基本信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = json_format.MessageToDict(
                merchant.basic_info, including_default_value_fields=True, use_integers_for_enums=True
            )
            return json_obj
        else:
            return None

    def save_merchant_info_stores(self, merchant_id, request):
        """
        @depredicated

        5.	保存商家所有门店信息
        # 没有地方调用
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        # 更新商家信息
        for store_json in request.json:
            store = merchant.stores.add()
            store.id = id_manager.generate_common_id()
            store.enable_ordering_coupon_package_union_pay = True
            store.enable_ordering_service = True
            merchant_store = json_format.ParseDict(store_json, merchant_pb.Store(), ignore_unknown_fields=True)
            merchant_store.id = str(uuid.uuid4())
            store.CopyFrom(merchant_store)

        # 更新商家信息
        self.db_utils.set_merchant(merchant)

        return merchant.stores

    def get_merchant_info_stores(self, merchant_id):
        """
        5.	获取商家基本信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant_stores = []
            for store in merchant.stores:
                json_obj = json_format.MessageToDict(store, including_default_value_fields=True, use_integers_for_enums=True)
            merchant_stores.append(json_obj)
        return merchant_stores

    def save_merchant_info_store(self, merchant_id, request):
        """
        @depredicated
        5.	保存商家门店信息
        # 此函数没有被调用
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        store = json_format.ParseDict(request.json, merchant_pb.Store(), ignore_unknown_fields=True)
        # 更新商家信息
        is_exist = False
        # 查找是否存在
        for merchant_store in merchant.stores:
            if len(merchant_store.id) > 0 and len(store.id) > 0 and merchant_store.id == store.id:
                print("修改商户信息")
                merchant_store.CopyFrom(store)
                is_exist = True
                break

        # 没有就新建
        if not is_exist:
            print("新建商户信息")
            merchant_store = merchant.stores.add()
            store.enable_ordering_coupon_package_union_pay = True
            store.enable_ordering_service = True
            merchant_store.CopyFrom(store)
            merchant_store.id = str(uuid.uuid4())

        # 更新商家信息
        self.db_utils.set_merchant(merchant)
        return merchant_store

    def get_merchant_info_store(self, merchant_id, store_id):
        """
        5.	获取商家单个门店信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = None
            for store in merchant.stores:
                if store.id == store_id:
                    json_obj = json_format.MessageToDict(
                        store, including_default_value_fields=True, use_integers_for_enums=True
                    )
        return json_obj

    def save_merchant_info_payment(self, merchant_id, request):
        """
        5. 保存商家支付信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        # 更新商家支付信息
        payment_info = json_format.ParseDict(request.json, merchant_pb.PaymentInfo(), ignore_unknown_fields=True)
        merchant.payment_info.CopyFrom(payment_info)

        # 更新商家信息
        self.db_utils.set_merchant(merchant)
        return merchant.payment_info

    def get_merchant_info_payment(self, merchant_id):
        """
        5.	获取商家基本信息
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = json_format.MessageToDict(
                merchant.payment_info, including_default_value_fields=True, use_integers_for_enums=True
            )
        return json_obj

    def save_merchant_info_qualification(self, merchant_id, request):
        """
        5.	保存商家资质信息
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        # 更新商家支付信息
        merchant_qualifications = json_format.ParseDict(
            request.json, merchant_pb.MerchantQualifications(), ignore_unknown_fields=True
        )
        merchant.merchant_qualifications.CopyFrom(merchant_qualifications)
        # 更新商家信息
        self.db_utils.set_merchant(merchant)
        return merchant.merchant_qualifications

    def get_merchant_info_qualification(self, merchant_id):
        """
        5.	获取商家基本信息
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = json_format.MessageToDict(
                merchant.merchant_qualifications, including_default_value_fields=True, use_integers_for_enums=True
            )
        return json_obj

    def save_merchant_info_design_config(self, merchant_id, request):
        """
        保存会员卡颜色和背景信息等等
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        # 更新会员卡颜色和背景信息等等
        design_config = json_format.ParseDict(request.json, merchant_pb.DesignConfig(), ignore_unknown_fields=True)
        merchant.preferences.design_config.CopyFrom(design_config)
        # 更新商家信息
        self.db_utils.set_merchant(merchant)
        return merchant.preferences.design_config

    def get_merchant_info_design_config(self, merchant_id):
        """
        保存会员卡颜色和背景信息等等
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = json_format.MessageToDict(
                merchant.preferences.design_config, including_default_value_fields=True, use_integers_for_enums=True
            )
        return json_obj

    def save_merchant_info_coupon_config(self, merchant_id, request):
        """
        保存会员卡颜色和背景信息等等
        """
        # 获取商家信息
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            merchant = merchant_pb.Merchant()
            merchant.id = merchant_id
            merchant.create_timestamp = date_utils.timestamp_second()

        # 更新会员卡颜色和背景信息等等
        coupon_config = json_format.ParseDict(request.json, merchant_pb.CouponConfig(), ignore_unknown_fields=True)
        merchant.preferences.coupon_config.CopyFrom(coupon_config)
        # 更新商家信息
        self.db_utils.set_merchant(merchant)
        return merchant.preferences.coupon_config

    def get_merchant_info_coupon_config(self, merchant_id):
        """
        保存会员卡颜色和背景信息等等
        """
        merchant = self.db_utils.get_merchant(merchant_id=merchant_id)
        if not merchant:
            json_obj = json_format.MessageToDict(
                merchant.preferences.coupon_config, including_default_value_fields=True, use_integers_for_enums=True
            )
        return json_obj

    @update_merchant_cache
    def update_merchant(self, merchant_id, enable_ordering_service=None, max_discount_update_timestamp=None):
        @DishCatalogCache.update
        def func(merchant_id, enable_ordering_service=None, max_discount_update_timestamp=None):
            matcher = {"id": merchant_id}
            udata = {}
            if enable_ordering_service is not None:
                udata = {"stores.0.enableOrderingService": enable_ordering_service}
            if max_discount_update_timestamp is not None:
                udata.update({"preferences.couponConfig.maxDiscountUpdateTimestamp": max_discount_update_timestamp})
            if udata:
                self.__collection.update(matcher, {"$set": udata})

        return func(
            merchant_id=merchant_id,
            enable_ordering_service=enable_ordering_service,
            max_discount_update_timestamp=max_discount_update_timestamp,
        )

    # 工具函数
    def get_merchant_for_tools(self, name=None):
        matcher = {}
        if name is not None:
            matcher.update({'basicInfo.name': {'$regex': '.*{}.*'.format(name)}})
        merchants = self.__collection.find(matcher)
        if merchants:
            return [
                json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True) for merchant in merchants
            ]
        return []

    def change_merchant_status_for_tools(self, merchant_id, status):
        @DishCatalogCache.update
        def func(merchant_id, status):
            matcher = {'id': merchant_id}
            udata = {'status': merchant_pb.MerchantStatus.Name(status)}
            self.__collection.update(matcher, {'$set': udata})

        return func(merchant_id=merchant_id, status=status)

    def change_discount_for_tools(self, merchant_id, value):
        @DishCatalogCache.update
        def func(merchant_id, value):
            matcher = {'id': merchant_id}
            udata = {'preferences.couponConfig.maxDiscount': value}
            self.__collection.update(matcher, {'$set': udata})

        return func(merchant_id=merchant_id, value=value)

    def query_one(self, matcher, project=...):
        return super().query_one(self.__collection, matcher, project)

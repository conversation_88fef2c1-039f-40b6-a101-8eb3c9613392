# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import dao.constants as constants
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from proto.group_dining import red_packet_pb2 as red_packet_pb

logger = logging.getLogger(__name__)


class RedPacketDataAccessHelper(DaoHelper):
    """ 红包
    """
    @property
    def __collection(self):
        db = constants.MONGODB_GROUP_DINING_EVENT_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_RED_PACKET_COLLECTION_NAME
        return self.mongo_client[db][collection]

    @property
    def __temp_save_red_packet_value(self):
        db = constants.MONGODB_GROUP_DINING_EVENT_DATABASE_NAME
        collection = constants.MONGODB_TEMP_RED_PACKET_VALUE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_or_update_red_packet(self, red_packet):
        matcher = {"transactionId": red_packet.transaction_id}
        json_data = json_format.MessageToDict(red_packet, including_default_value_fields=True)
        self.__collection.update_one(matcher, {"$set": json_data}, upsert=True)

    def update_red_packet_status(self, transaction_id=None, status=None):
        matcher, udata = {}, {}
        if transaction_id is not None:
            matcher = {'transactionId': transaction_id}
        if status is not None:
            udata = {
                "status": red_packet_pb.RedPacket.Status.Name(status)
            }
        if matcher and udata:
            self.__collection.update(matcher, {"$set": udata})

    def add_red_packet(self, red_packet):
        """ 保存饭局的红包
        Args:
            red_packet: (RedPacket)结构体
        Return:
            None
        """
        json_obj = json_format.MessageToDict(red_packet, including_default_value_fields=True)
        self.__collection.insert_one(json_obj)

    def get_red_packet(self, dining_id=None, transaction_id=None, id=None, order_id=None,
                       new_transaction_id=None, status=None):
        """ 根据不同的id查找红包
        Args:
            dining_id: (string)饭局
            transaction_id: 新会员红包对应的交易ID,已废弃
            new_transaction_id: (红包对应的交易)
            id: (string)红包惟一id
        Return:
            red_packet_pb.RedPacket()
        """

        matcher = {}
        if dining_id is not None:
            matcher = {"diningEventId": dining_id}
        if transaction_id is not None:
            matcher.update({"newMemberTransactionId": transaction_id})
        if new_transaction_id is not None:
            matcher.update({'transactionId': new_transaction_id})
        if id is not None:
            matcher.update({"id": id})
        if order_id is not None:
            matcher.update({"orderId": order_id})
        if status is not None:
            matcher.update({'status': red_packet_pb.RedPacket.Status.Name(status)})
        if not matcher:
            return None
        red_packet = self.__collection.find_one(matcher)
        if not red_packet:
            return None
        return self.parse_document(red_packet, red_packet_pb.RedPacket)

    def update_red_packet_drawn_users(self, id=None, user_id=None, date=None, status=None):
        """ 更新红包drawn_users
        Args:
            id: (string)红包惟一id
            drawn_users: (数组)开红包的人
        """
        match = {
            "id": id,
        }
        udata = {
            "drawnUsers.{}".format(user_id): date
        }
        if status is not None:
            udata.update({'status': red_packet_pb.RedPacket.Status.Name(status)})
        self.__collection.update(match, {'$set': udata})

    def get_red_packets(self, user_id=None, create_start_time=None, create_end_time=None, issue_scene=None,
                        status = None, page=None, size=None, orderby=None, single_user_id=None):
        """
        user_id: 对应 valueAssignments.user_id 字段 废弃
        single_user_id: 对应red_packet.user_id字段
        """
        matcher = {}
        if create_start_time is not None or create_end_time is not None:
            matcher['createTime'] = {}
        if create_start_time is not None:
            matcher['createTime']["$gt"] = str(create_start_time)
        if create_end_time is not None:
            matcher['createTime']["$lt"] = str(create_end_time)
        if issue_scene is not None:
            matcher['issueScene'] = red_packet_pb.RedPacket.IssueScene.Name(issue_scene)
        if user_id is not None:
            logger.info("get_red_packets user_id 参数已废弃,使用single_user_id代替")
            matcher['valueAssignments.{}'.format(user_id)] = {"$exists": 1}
        if single_user_id is not None:
            matcher["userId"] = single_user_id
        if status is not None:
            matcher.update({'status': red_packet_pb.RedPacket.Status.Name(status)})

        cursor = self.__collection.find(matcher)
        logger.info(f"返回数量: {matcher} {orderby}, {page}, {size} {cursor.count()}")
        if orderby:
            cursor.sort(orderby)
        return self.parse_documents(cursor, red_packet_pb.RedPacket, page=page, size=size)

    def get_temp_red_packet_value(self, order_id):
        matcher = {"orderId": order_id}
        red_packet_value = self.__temp_save_red_packet_value.find_one(matcher)
        if not red_packet_value:
            return None
        return self.parse_document(red_packet_value, red_packet_pb.TempRedPacketValue)

# -*- coding: utf-8 -*-

from google.protobuf import json_format

import proto.ordering.registration_pb2 as registration_pb
from dao.dao_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dao import constants


class PrinterConfigDataAccessHelper(DaoHelper):

    @property
    def _printer_config_collection(self):
        db = constants.MONGODB_CONFIG_DATABASE_NAME
        c = constants.MONGODB_PRINTER_CONFIG_COLLECTION_NAME
        return self.mongo_client[db][c]

    def add_or_update_printer_config(self, config):
        matcher = {
            "merchantId": config.merchant_id,
            "printerSn": config.printer_sn
        }
        config_json = json_format.MessageToDict(config, including_default_value_fields=True)
        self._printer_config_collection.update(matcher, {"$set": config_json}, upsert=True)

    def get_printer_configs(self, merchant_id=None, type=None, printer_sn=None):
        matcher = {}
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if type is not None:
            matcher.update({"type": registration_pb.PrinterConfigByType.Type.Name(type)})
        if printer_sn is not None:
            matcher.update({"printerSn": printer_sn})
        if not matcher:
            return []
        configs = self._printer_config_collection.find(matcher)
        return [json_format.ParseDict(config, registration_pb.PrinterConfigByType(), ignore_unknown_fields=True)
                for config in configs]

    def get_printer_config(self, printer_sn=None, merchant_id=None):
        matcher = {}
        if printer_sn is not None:
            matcher.update({"printerSn": printer_sn})
        if merchant_id is not None:
            matcher.update({"merchantId": merchant_id})
        if not matcher:
            return None
        config = self._printer_config_collection.find_one(matcher)
        if not config:
            return None
        return json_format.ParseDict(config, registration_pb.PrinterConfigByType(), ignore_unknown_fields=True)

    def delete_printer_config(self, matcher):
        res = self._printer_config_collection.delete_many(matcher)

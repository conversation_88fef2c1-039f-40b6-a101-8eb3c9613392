# -*- coding: utf-8 -*-

from google.protobuf import json_format

from dao import constants
from dao.dao_helper import DaoH<PERSON>per
from proto.sns import messaging_pb2 as messaging_pb


class ChatMessageDataAccessHelper(DaoHelper):
    @property
    def __collection(self):
        db = constants.MONGODB_CHAT_MESSAGE_DATABASE_NAME
        collection = constants.MONGODB_GROUP_DINING_CHAT_MESSAGE_COLLECTION_NAME
        return self.mongo_client[db][collection]

    def add_group_dining_message(self, message):
        """添加一个新的饭局聊天信息。

        Args:
          message: (ChatMessage) 饭局聊天信息结构体
        """
        msg_json = json_format.MessageToDict(message, including_default_value_fields=True)
        self.__collection.insert_one(msg_json)

    def get_group_dining_messages(self, dining_event_id=None, sender_id=None, receiver_id=None,
                                  create_start_time=None, create_end_time=None, orderby=None,
                                  page=None, size=None):
        filter = {}
        if dining_event_id is not None:
            filter['groupDiningEventId'] = dining_event_id
        if sender_id is not None:
            filter['senderId'] = sender_id
        if receiver_id is not None:
            filter['receiverId'] = receiver_id
        if create_start_time is not None or create_end_time is not None:
            filter['createTime'] = {}
        if create_start_time is not None:
            filter['createTime']["$gt"] = str(create_start_time)
        if create_end_time is not None:
            filter['createTime']["$lt"] = str(create_end_time)

        cursor = self.__collection.find(filter)
        if orderby is not None:
            cursor.sort(orderby)
        if page is not None and size is not None:
            skip = (page - 1) * size
            messages = cursor.skip(skip).limit(size)
        else:
            messages = cursor
        if messages:
            return [json_format.ParseDict(message, messaging_pb.ChatMessage(),
                                         ignore_unknown_fields=True) for message in messages]
        else:
            return None

import logging

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from bi import membership_analyzer
from bi import payment_analyzer
from business_ops.merchant_manager import MerchantManager
from business_ops.user_manager import UserManager
from common.config import config
from common.utils import date_utils
from common.utils import requests_utils
from proto.bi import common_pb2 as bi_common_pb
from proto.finance import wallet_pb2 as wallet_pb
from service import base_responses
from service import errors

merchant_stats = Blueprint("merchant_stats", __name__, url_prefix="/merchant/<string:merchant_id>/stats")

logger = logging.getLogger(__name__)


@merchant_stats.before_request
def check_permission():
    """ 检查接口访问权限

    给商户展示的门店 BI 数据，只允许商户管理员访问
    """
    from_platform = requests_utils.get_platform(request)
    if from_platform != config.PLATFORM_MERCHANT:
        raise errors.PermissionError()

    user_manager = UserManager()
    user_id = requests_utils.get_headers_info(request, "userId")
    user = user_manager.get_platform_user_by_id(from_platform, user_id)
    # if not user_id or not user:
    #     raise errors.PermissionError()

    merchant_id = request.view_args["merchant_id"]
    merchant_manager = MerchantManager()
    merchant = merchant_manager.get_valid_merchant(merchant_id)
    if not merchant:
        raise errors.MerchantNotFoundError()

    setattr(request, "user", user)
    setattr(request, "merchant", merchant)
    del request.view_args["merchant_id"]
    return None

# TODO: 使用 Transaction
@merchant_stats.route("/payment", methods=["POST"])
def get_payment_stats():
    """ 获取商户账单数据
    """
    merchant = request.merchant
    request_json = request.json
    start_time = request_json.get("startTime", None)
    end_time = request_json.get("endTime", None)
    store_id = request_json.get("storeId")
    time_granularity = request_json.get("timeGranularity")
    time_granularity = bi_common_pb.TimeGranularity.Value(time_granularity.upper())

    start_time = int(date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).timestamp())
    end_time = int(date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).timestamp())

    payment_stats = payment_analyzer.get_merchant_payment_stats(
        merchant_id=merchant.id, store_id=store_id, start_time=start_time,
        end_time=end_time, time_granularity=time_granularity)
    json_payment_stats = json_format.MessageToDict(payment_stats, including_default_value_fields=True)
    return base_responses.make_json_response({"stats": json_payment_stats})


@merchant_stats.route("/member", methods=["POST"])
def get_member_stats():
    """ 获取会员数据
    """
    merchant = request.merchant
    request_json = request.json
    start_time = request_json.get("startTime", None)
    end_time = request_json.get("endTime", None)
    time_granularity = request_json.get("timeGranularity")
    time_granularity = bi_common_pb.TimeGranularity.Value(time_granularity.upper())

    start_time = int(date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).timestamp())
    end_time = int(date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).timestamp())

    member_stats = membership_analyzer.get_merchant_membership_stats(merchant.id, start_time=start_time, end_time=end_time, time_granularity=time_granularity)
    json_member_stats = json_format.MessageToDict(member_stats, including_default_value_fields=True)
    return base_responses.make_json_response({"stats": json_member_stats})

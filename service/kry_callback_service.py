# -*- coding: utf-8 -*-

import logging

from flask import Flask

import common.logger.main_service_logger
from scripts.services import service_common
from common import constants
from common.config import config
from service.init_blueprint import init_blueprint

# 初始化 Flask
app = Flask(__name__)

app.logger = logging.getLogger(__name__)
init_blueprint(app)

# 以Python脚本形式启动
if __name__ == "__main__":
    app.config.update(DEBUG=True)
    app.logger.info("-------main server begin start------")
    app.run(host="0.0.0.0", port=int(config.get_config_value(constants.MAIN_WEB_SERVICE_PORT_ENV_NAME)))

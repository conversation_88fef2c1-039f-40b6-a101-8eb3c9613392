# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request
from flask import jsonify

import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.handheld_pos.boshijie_manager import BoshijieManager
from business_ops.merchant_manager import MerchantManager
from business_ops.direct_pay_manager_helper import DirectPayManagerHelper
from business_ops.direct_pay_manager import DirectPayManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.transaction_manager import TransactionManager
from business_ops.tian_que_pay_manager import TianQuePayManager
from common.utils import distribute_lock
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import error_codes
from service import errors

bp_name = "handheld_pos"

_handheld_pos = Blueprint(bp_name, bp_name, url_prefix="/handheld-pos")


@_handheld_pos.route("/merchant/bind", methods=["POST"])
def bind():
    """博实结手持pos机与商户绑定"""
    sn = request.json.get("sn")
    merchant_id = request.json.get("merchantId")
    manager = BoshijieManager()
    merchant = manager.bind(sn, merchant_id)
    return jsonify(BoshijieManager.parse_merchant_result(merchant))


@_handheld_pos.route("/merchant/unbind", methods=["POST"])
def unbind():
    sn = request.json.get("sn")
    merchant_id = request.json.get("merchantId")
    manager = BoshijieManager()
    merchant = manager.unbind(sn, merchant_id)
    return jsonify(BoshijieManager.parse_merchant_result(merchant))


@_handheld_pos.route("/merchant/query", methods=["GET"])
def query():
    """查询手持Pos机的商户信息"""
    sn = request.args.get("sn")
    sign = request.args.get("sign")
    BoshijieManager.verify(
        sn=sn,
        sign=sign
    )
    manager = MerchantManager()
    merchant = manager.get_merchant_by_handheld_pos_sn(sn=sn)
    return jsonify(BoshijieManager.parse_merchant_result(merchant))


@_handheld_pos.route("/order/prepay", methods=["POST"])
def prepay():
    """支付"""
    sn = request.json.get("sn")
    sign = request.json.get("sign")
    order_amt = int(request.json.get("order_amt"))
    order_no = request.json.get("order_no")
    auth_no = request.json.get("auth_no")
    key = f"handheld_pos_prepay_{auth_no}"
    with distribute_lock.redislock(key=key, ttl=30000, retry_count=0, retry_delay=0) as lock:
        if not lock:
            result = {
                'errcode': error_codes.USER_PAYING
            }
            return jsonify(BoshijieManager.parse_prepay_result(result))
        order_manager = OrderManager()
        order = order_manager.get_order_by_handheld_pos_order_no(order_no)
        if order is not None:
            return jsonify(BoshijieManager.parse_query_order_result(order))
        BoshijieManager.verify(
            sn=sn,
            sign=sign,
            order_amt=order_amt,
            order_no=order_no,
            auth_no=auth_no
        )
        merchant_manager = MerchantManager()
        merchant = merchant_manager.get_merchant_by_handheld_pos_sn(sn=sn)
        helper = DirectPayManagerHelper(merchant)
        helper.prepay(auth_no, order_amt, order_no)
        result = BoshijieManager.parse_query_order_result(helper.order, helper.transaction)
        return jsonify(result)


@_handheld_pos.route("/order/query", methods=["GET"])
def query_order():
    sn = request.args.get("sn")
    order_no = request.args.get("order_no")
    sign = request.args.get("sign")
    BoshijieManager.verify(sign=sign, sn=sn, order_no=order_no)
    order_manager = OrderManager()
    transaction_manager = TransactionManager()
    order = order_manager.get_order_by_handheld_pos_order_no(order_no)
    if order is None:
        return BoshijieManager.parse_query_order_result(order)
    transaction = transaction_manager.get_transaction_by_id(order.transaction_id)
    if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
        return BoshijieManager.parse_query_order_result(order, transaction)
    else:
        if order.status == dish_pb.DishOrder.PAID:
            return BoshijieManager.parse_query_order_result(order, transaction)
        tian_que_pay_manager = TianQuePayManager(merchant_id=transaction.payee_id)
        result = tian_que_pay_manager.trade_query(transaction_id=transaction.id)
        flag = tian_que_pay_manager.is_pay_success(result)
        if flag == TianQuePayManager.TRADE_SUCCESS:
            order.status = dish_pb.DishOrder.PAID
        elif flag == TianQuePayManager.TRADE_FAILED:
            order.status = dish_pb.DishOrder.CANCELLED
        return BoshijieManager.parse_query_order_result(order, transaction)

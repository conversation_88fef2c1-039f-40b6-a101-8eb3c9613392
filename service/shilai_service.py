# -*- coding: utf-8 -*-

import logging
import hashlib
import uuid
import requests

from bs4 import BeautifulSoup
from urllib import parse
from flask import Flask
from flask import redirect
from flask import request
from flask import jsonify

from google.protobuf import json_format

import proto.merchant_rules_pb2 as merchant_rules_pb
from common.utils import requests_utils
from common.utils.db_utils import DbUtils
from common.utils.log_utils import LogUtils
from common.config import config
from common.utils import date_utils
from common.utils.WXBizMsgCrypt import WXBizMsgCrypt
from dao.membership_da_helper import MembershipDataAccessHelper
from proto import merchant_service_pb2 as merchant_service_pb2
from proto.ui.merchant import user_pb2 as ui_merchant_user_pb2
from proto.ui.merchant import order_pb2 as ui_merchant_order_pb2
from service.base_responses import BaseHelper
from service.auth_service_helper import UserAuthHelper
from service.merchant_analysis_service import merchant_analysis
from service.misc_service import misc
from service import base_responses

app = Flask(__name__)
app.register_blueprint(misc)
app.register_blueprint(merchant_analysis)
# app.register_blueprint(merchant, url_prefix='/merchant')

logger = logging.getLogger(__name__)


@app.route('/merchant/qrcode/<string:qrcode_id>', methods=['GET'])
def merchant_barcode_get(qrcode_id):
    """
    获取商家授权二维码
    GET /merchant/qrcode/<string:qrcode_id>
    请求参数

    响应主体
        二维码图片流
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    """
    LogUtils(app, request)
    resp = BaseHelper().get_merchant_barcode(qrcode_id)
    return resp


@app.route('/merchant/get_qrcode_info', methods=['GET'])
def merchant_get_qrcode_info():
    """
        获取商家二维码id 对应的 业务员和商家信息
        GET /merchant/get_qrcodeid_info
        请求参数
            qrcodeId
        响应主体
            {staffId, merchantId}
    """
    LOG = LogUtils(app, request)
    qrcode_id = request.values.get("qrcodeId")

    qrcode_id_info = BaseHelper().get_merchant_qrcode_id_info(qrcode_id)
    if qrcode_id_info:
        LOG.info(qrcode_id_info)
        merchant_id = qrcode_id_info['merchantId']
        staff_id = qrcode_id_info['staffId']

        ret_value = {
            "staffId": staff_id,
            "merchantId": merchant_id
        }
        return base_responses.jsonify_response(ret_value)
    else:
        return base_responses.jsonify_response()


@app.route('/staff/get_qrcode_info', methods=['GET'])
def staff_get_qrcode_info():
    """
        获取商家二维码id 对应的 业务员和商家信息
        GET /merchant/get_qrcodeid_info
        请求参数
            qrcodeId
        响应主体
            {staffId, merchantId}
    """
    LOG = LogUtils(app, request)
    qrcode_id = request.values.get("qrcodeId")

    qrcode_id_info = BaseHelper().get_merchant_qrcode_id_info(qrcode_id)
    if qrcode_id_info:
        LOG.info(qrcode_id_info)
        merchant_id = qrcode_id_info['merchantId']
        staff_id = qrcode_id_info['staffId']

        ret_value = {
            "staffId": staff_id,
            "merchantId": merchant_id
        }
        return base_responses.jsonify_response(ret_value)
    else:
        return base_responses.jsonify_response()


@app.route('/staff/qrcode/create', methods=['OPTIONS', 'POST'])
def staff_qrcode_create():
    """
    生成拓展员二维码小程序二维码
    GET /merchant/qrcode
    请求参数 {staffId}
    响应主体 {qrcodeUrl}
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    #
    """
    # LOG = LogUtils(app, request)
    staff_id = requests_utils.get_headers_info(request, "staffId")
    bar_code_url = BaseHelper().create_qrcode(staff_id=staff_id, platform=config.PLATFORM_SHILAI_STAFF)

    return base_responses.jsonify_response({"barCodeUrl": bar_code_url})


@app.route('/merchant/qrcode/create', methods=['POST'])
def merchant_qrcode_create():
    """
    生成员工小程序二维码
    GET /merchant/qrcode
    请求参数 {merchantId}
    响应主体 {qrcodeUrl}
    流程: 业务员工创建Merchant主体后, 生成一个二维码给员工扫描, 扫描后跳转到员工助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    #
    """
    request_json = request.json
    merchant_id = request_json['merchantId']
    user_id = requests_utils.get_headers_info(request, 'userId')
    bar_code_url = BaseHelper().create_qrcode(merchant_id, user_id, config.PLATFORM_MERCHANT)

    return base_responses.jsonify_response({"barCodeUrl": bar_code_url})


@app.route('/user/login', methods=['OPTIONS', 'POST'])
def user_login_api():
    """
    登录凭证校验。通过 wx.login() 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。更多使用方法详见 小程序登录。
    # POST /user/login
    # 请求主体 { code }
    # 请求头 { fromPlatform: “merchant” }
    # 流程：使用 code 调用微信接口 https://api.weixin.qq.com/sns/jscode2session 获取openid, session_key, unionid，其中 session_key 不能在客户端使用
    # 响应主体 { openid, unionid }
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api-backend/code2Session.html
    :return:
    """
    # 获取参数
    js_code = request.json['code']
    from_platform = requests_utils.get_platform(request)
    user = UserAuthHelper().user_login_code_to_session(js_code, from_platform)

    if user:
        return base_responses.jsonify_response({"userId": user.id})
    return base_responses.jsonify_response()


@app.route('/user/get_merchants', methods=['OPTIONS', 'GET'])
def user_get_merchant():
    """
        根据 user_id 返回指定商户信息。
    :return:
    """
    return base_responses.jsonify_response()


@app.route('/user/info/save', methods=['OPTIONS', 'POST'])
def user_info_save():
    """
    # POST /user/info/save
    # 请求主体 { encryptData, iv, userId}
    # 请求头 { fromPlatform: “merchant” }
    # 流程：通过session_key, iv, 小程序appid 解密 encryptData，得到用户基本信息(nickName, gender, province, city, country, avatarUrl, unionId)
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api/wx.getUserInfo.html
    """
    request_json = request.json
    from_platform = requests_utils.get_platform(request)
    iv = request_json['iv']
    user_id = request_json['userId']
    encrypted_data = request_json['encryptedData']

    UserAuthHelper().save_user_info(from_platform, iv, user_id, encrypted_data)
    return base_responses.jsonify_response()


# 3.商家授权
@app.route('/authorizer/auth', methods=['GET'])
def authorizer_auth():
    """
    # GET /authorizer/auth
    # 请求参数 { userId }
    #
    """
    staff_id = request.values.get('staffId')
    user_id = request.values.get('userId')
    merchant_id = request.values.get('merchantId')

    url = UserAuthHelper().authorizer_auth(staff_id, user_id, merchant_id)

    return redirect(url, code=302)


# 4.商家授权回调
@app.route('/authorizer/authorized', methods=['GET'])
def authorizer_authorized():
    """
    # GET /authorizer/authorized
    # 请求参数 { auth_code, expires_in, unionid }
    # 响应 执行下一个步骤的 HTML 代码
    # 流程：使用 auth_code, expires_id 可获取授权商家基本信息和接口调用凭据，初始化商家数据：初始化两张会员卡（一张用于非注册会员，一张用户注册会员）
    """
    user_id = request.values.get('userId')
    staff_id = request.values.get('staffId')
    merchant_id = request.values.get('merchantId')
    auth_code = request.values.get('auth_code')
    request.values.get('expires_in')

    UserAuthHelper().authorizer_authorized(user_id, staff_id, merchant_id, auth_code)

    response = """
<script
  type="text/javascript"
  src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"
></script>
<script>
  wx.miniProgram.reLaunch({ url: "/pages/index/index" });
</script>
"""
    return response


@app.route('/staff/add', methods=['POST'])
def staff_add():
    """
    1.	添加员工
    POST /staff/add
    请求参数 { userId }
    流程: 超级管理员可以添加地推(业务)员工, 会将该员工的User id复制到Staff表格中, 员工享有注册商家的权限
    """
    user_id = requests_utils.get_headers_info('userId')
    UserAuthHelper().staff_add(user_id)
    return base_responses.jsonify_response()


@app.route('/staff/info', methods=['GET'])
def staff_info():
    """
    POST /staff/info
    请求参数 { userId }
    流程: 超级管理员可以添加地推(业务)员工, 会将该员工的User id复制到Staff表格中, 员工享有注册商家的权限
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    json_obj = UserAuthHelper().staff_info(user_id)

    return base_responses.jsonify_response(data=json_obj)


@app.route('/staff/invitation/accept', methods=['POST'])
def staff_invitation_info():
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, "userId")
    invitation_id = request_json['invitationId']
    staff = UserAuthHelper().staff_invitation_info(user_id, invitation_id)
    # 返回结果体
    json_obj = json_format.MessageToDict(staff,
                                         including_default_value_fields=True,
                                         use_integers_for_enums=True)
    return base_responses.jsonify_response(data=json_obj)


@app.route('/staff/<string:staff_id>', methods=['POST'])
def delete_staff_info(staff_id):
    UserAuthHelper().staff_invitation_info(staff_id)
    return base_responses.jsonify_response()


@app.route('/authorizer/status', methods=['GET'])
def authorizer_status():
    """
    5.	获取授权状态
    GET /authorizer/status
    请求参数 {merchantId}
    流程：去authorizer数据集查看是否有绑定了merchantId的
    :return:
    """
    return base_responses.jsonify_response()


@app.route('/authorizer/info', methods=['GET'])
def authorizer_info():
    """ 获取授权的公众号或小程序公众号信息
    请求方式:
       GET /authorizer/info
    Args:
        merchantId
    Returns:
        authorizer_info : authorizer_pb2.Authorizer()
    """
    db_utils = DbUtils()
    merchant_id = request.values.get('merchantId')

    # 使用授权码换取公众号或小程序的接口调用凭据和授权信息
    # merchant_id = 'b617e666-5ca2-4c41-a302-b7c7a9045666'
    authorizer_info = db_utils.get_authorizer_info(merchant_id)
    json_obj = json_format.MessageToDict(authorizer_info,
                                         including_default_value_fields=True,
                                         use_integers_for_enums=True)

    return base_responses.jsonify_response({"authorizer_info": json_obj})


"""
    C端小程序


"""


@app.route('/user/card/<string:card_id>/save', methods=['POST'])
def user_card_save(card_id):
    """ 完成激活用户开卡流程
      POST /user/card/:cardId/save
      请求头 { userId }
      请求主体 { code, activate_ticket, userId}
    """
    user_id = requests_utils.get_headers_info('userId')
    request_json = request.json
    code = request_json['code']
    activate_ticket = request_json['activate_ticket']

    MembershipDataAccessHelper().activate_member_card(user_id, code, card_id, activate_ticket)

    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/member_card', methods=['GET'])
def get_member_card(merchant_id):
    """ 获取商家投放的会员卡信息
      GET /merchant/:merchantId/member_card
      请求头 { merchant_id }
      响应主体 { card }
    """
    db_utils = DbUtils()

    member_card_category_list = db_utils.get_member_card_category(merchant_id)

    json_obj_list = []
    for member_card_category in member_card_category_list:
        json_obj = json_format.MessageToDict(member_card_category,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)
        json_obj_list.append(json_obj)

    return base_responses.jsonify_response(json_obj_list)


@app.route('/user/info', methods=['GET'])
def get_user_info():
    """ 获取用户信息
        GET /user/info
        请求头 { userId }
        响应主体 { user }
    """
    db_utils = DbUtils()

    user_id = requests_utils.get_headers_info(request, 'userId')

    # 获取微信用户信息
    user_info = db_utils.get_wechat_user(id=user_id)
    json_obj = json_format.MessageToDict(user_info,
                                         including_default_value_fields=True,
                                         use_integers_for_enums=True)
    return base_responses.jsonify_response(json_obj)


@app.route('/merchant/<string:merchant_id>/card/<string:card_id>/activate_extra', methods=['GET'])
def merchant_card_activate_extra(merchant_id, card_id):
    """ 获取开卡组件参数

        通过 https://api.weixin.qq.com/card/membercard/activate/geturl 接口获取激活URL，从 URL 参数中截图 encrypt_card_id, outer_str, biz 值

        GET /merchant/:merchantId/card/:cardId/activate_extra
        响应主体 { extraData: { encrypt_card_id, outer_str, biz } }
    """
    db_utils = DbUtils()
    LOG = LogUtils(app, request)

    authorizer_info = db_utils.get_authorizer_info(merchant_id)
    app_id = authorizer_info.authorization_info.authorization_appid
    authorizer_token = db_utils.get_authorizer_access_token(appid=app_id)
    access_token = authorizer_token.access_token
    url = "https://api.weixin.qq.com/card/membercard/activate/geturl?access_token={}".format(access_token)

    json_obj = {
        "card_id": card_id,
        "outer_str": "123"
    }
    resp = requests.post(url=url, data=json_obj)
    result = resp.json()
    result_url = result['url']
    parse_result = parse.urlparse(result_url)
    param_dict = parse.parse_qs(parse_result.query)
    LOG.info(param_dict)
    encrypt_card_id = ""
    if 'encrypt_card_id' in param_dict.keys():
        encrypt_card_id = param_dict["encrypt_card_id"]
    outer_str = ""
    if 'outer_str' in param_dict.keys():
        outer_str = param_dict["outer_str"]
    biz = ""
    if 'biz' in param_dict.keys():
        biz = param_dict["biz"]

    responses_obj = {
        "extraData": {"encrypt_card_id": encrypt_card_id, "outer_str": outer_str, "biz": biz}
    }
    resp = jsonify(responses_obj)
    LOG.info_api_responses(resp)
    return resp


def sign_fun(data):
    data_key = data.keys()
    sort_key = [value for index, value in sorted(enumerate(data_key), key=lambda d: d[1])]
    string_sign = ""
    for key in sort_key:
        string_sign += "{}={}&".format(key, data[key])

    string_sign_temp = string_sign + "key=192006250b4c09247ec02edce69f6a2d"
    # MD5
    sign = hashlib.md5(string_sign_temp.encode('utf-8'))
    # sign = sign.hexdigest()
    # hmac256
    # sign = hmac.new(key,sign,digestmod=hashlib.sha256).hexdigest()
    return sign.hexdigest().upper()


@app.route('/prepay', methods=['POST'])
def prepay():
    """ 预支付
        用于小程序调用支付模块 wx.requestPayment()
        此接口需要经过两次签名：调用微信生成订单API前，返回响应主体前

        POST /prepay
        请求头 { userId }
        请求主体 { merchantId, totalFee, couponId }
        响应主体 { paySign, timeStamp, nonceStr, package, signType }}
    """
    url = 'https://api.mch.weixin.qq.com/pay/unifiedorder'

    # 小程序ID
    appid = ""
    # 商户号
    mch_id = ""
    # 随机字符串
    nonce_str = ""
    # 商品描述
    body = ""
    # 商户订单号
    out_trade_no = ""
    # 标价金额
    total_fee = ""
    # 终端IP
    spbill_create_ip = ""
    # 通知地址
    notify_url = ""
    # 交易类型
    trade_type = ""

    unified_order_data = {
        'appid' : appid,
        'mch_id' : mch_id,
        'nonce_str' : nonce_str,
        'body' : body,
        'out_trade_no' : out_trade_no,
        'total_fee' : total_fee,
        'spbill_create_ip' : spbill_create_ip,
        'notify_url' : notify_url,
        'trade_type' : trade_type
    }

    # 签名
    sign = sign_fun(unified_order_data)
    unified_order_data['sign'] = sign
    resp = requests.post(url=url, data=unified_order_data)
    result = resp.json()
    prepay_id = result['prepay_id']

    # 再次签名
    package = "prepay_id={}".format(prepay_id)
    pay_sign_data = {
        "appId" : appid,
        "timeStamp" : date_utils.timestamp_second(),
        "nonceStr" : nonce_str,
        "package" : package,
        "signType" : "MD5"
    }

    pay_sign = sign_fun(pay_sign_data)

    pay_sign_data['pay_sign'] = pay_sign

    resp = jsonify(pay_sign_data)
    return resp


@app.route('/user/member_card/list', methods=['GET'])
def user_member_card_list():
    """  用户领取的会员卡列表
        GET /user/member_card/list
        请求头 { userId }
        请求主体 { merchantId }
        merchantId: 商家ID，如设置则返回在此商家的列表，如无，则返回所有
        响应主体 { memberCards }
    """
    db_utils = DbUtils()
    LOG = LogUtils(app, request)
    user_id = requests_utils.get_headers_info(request, 'userId')

    # 获取微信用户信息
    user_info = db_utils.get_wechat_user(id=user_id)
    json_obj = json_format.MessageToDict(user_info,
                                         including_default_value_fields=True,
                                         use_integers_for_enums=True)
    resp = jsonify(json_obj)
    LOG.info_api_responses(resp)
    return resp


@app.route('/user/coupon/list', methods=['GET'])
def user_coupon_list():
    """  用户领取的优惠券列表
        GET /user/coupon/list
        请求头 { userId }
        请求主体 { merchantId }
        merchantId: 商家ID，如设置则返回在此商家的列表，如无，则返回所有
        响应主体 { coupons }
    """
    db_utils = DbUtils()
    LOG = LogUtils(app, request)
    user_id = requests_utils.get_headers_info(request, 'userId')

    # 获取微信用户信息
    user_info = db_utils.get_wechat_user(id=user_id)
    json_obj = json_format.MessageToDict(user_info,
                                         including_default_value_fields=True,
                                         use_integers_for_enums=True)
    resp = jsonify(json_obj)
    LOG.info_api_responses(resp)
    return resp


@app.route('/receiver', methods=['POST'])
def receiver():
    """  1.	授权事件接收用于接收取消授权通知、授权成功通知、授权更新通知，也用于接收ticket，ticket是验证平台方的重要凭据
        POST /receiver
        请求头 Content-Type: text/xml
        请求参数 signature, timestamp, nonce, encrypt_type, msg_signature
        请求主体（xml，需要由请求参数解密并转成 JSON）：
        AppId: 第三方平台 appid
        CreateTime: 消息创建时间
        InfoType: 事件消息类型，比如 平台component_verify_ticket, 授权authorized, 取消授权unauthorized
        AuthorizerAppid: 授权方appid
        AuthorizationCode: 授权码（可选项）
        AuthorizationCodeExpiredTime: 授权码过期时间（可选项）
        PreAuthCode: 预授权码（可选项）
        详情: https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
    """
    app.logger.info('receiver ')
    db_utils = DbUtils()

    signature = request.values.get('signature')
    app.logger.info("signature:{}".format(signature))
    timestamp = request.values.get('timestamp')
    app.logger.info("timestamp:{}".format(timestamp))
    nonce = request.values.get('nonce')
    app.logger.info("nonce:{}".format(nonce))
    encrypt_type = request.values.get('encrypt_type')
    app.logger.info("encrypt_type:{}".format(encrypt_type))
    msg_sign = request.values.get('msg_signature')
    app.logger.info("msg_sign:{}".format(msg_sign))

    soup = BeautifulSoup(request.data, 'xml')
    app.logger.info(request.data)
    app.logger.info(soup.find('xml'))
    from_xml = soup.find('xml')
    # app.logger.info(str(request.data))
    # from_xml = str(request.data).replace("\n", "")
    # from_xml = from_xml.replace("b\'", "")
    # from_xml = from_xml.replace("\'", "")
    # app.logger.info(from_xml)
    # from_xml = from_xml.replace("\n", "")
    # app.logger.info(from_xml)
    # 解密接口
    appid = config.WECHAT_APP_ID
    token = config.WECHAT_MESSAGE_VERIFY_TOKEN
    sEncodingAESKey = config.WECHAT_MESSAGE_ENCODED_SYMMETRIC_KEY
    decrypt_test = WXBizMsgCrypt(token, sEncodingAESKey, appid)

    ret, decryp_xml = decrypt_test.DecryptMsg(str(from_xml), msg_sign, timestamp, nonce)
    app.logger.info(ret)
    decryp_xml = str(decryp_xml).replace("\n", "")
    decryp_xml = decryp_xml.replace("b\'", "")
    decryp_xml = decryp_xml.replace("\'", "")

    # 解密为xml
    soup = BeautifulSoup(decryp_xml, 'xml')

    # 第三方平台 appid
    appid_soup = soup.find_all('AppId')
    if appid_soup and len(appid_soup) > 0:
        appid = appid_soup[0].text
        app.logger.info('appid = {}'.format(appid))

    # 消息创建时间
    create_time_soup = soup.find_all('CreateTime')
    if create_time_soup and len(create_time_soup) > 0:
        create_time = create_time_soup[0].text
        app.logger.info('create_time = {}'.format(create_time))

    # 事件消息类型，比如 平台component_verify_ticket, 授权authorized, 取消授权unauthorized
    info_type_soup = soup.find_all('InfoType')
    if info_type_soup and len(info_type_soup) > 0:
        info_type = info_type_soup[0].text
        app.logger.info('info_type = {}'.format(info_type))

    # 授权方appid
    authorizer_appid_soup = soup.find_all('AuthorizerAppid')
    if authorizer_appid_soup and len(authorizer_appid_soup) > 0:
        authorizer_appid = authorizer_appid_soup[0].text
        app.logger.info('authorizer_appid = {}'.format(authorizer_appid))

    # 授权码（可选项）
    authorization_code_soup = soup.find_all('AuthorizationCode')
    if authorization_code_soup and len(authorization_code_soup) > 0:
        authorization_code = authorization_code_soup[0].text
        app.logger.info('authorization_code = {}'.format(authorization_code))

    # 授权码过期时间（可选项）
    authorization_code_expired_time_soup = soup.find_all('AuthorizationCodeExpiredTime')
    if authorization_code_expired_time_soup and len(authorization_code_expired_time_soup) > 0:
        authorization_code_expired_time = authorization_code_expired_time_soup[0].text
        app.logger.info('authorization_code_expired_time = {}'.format(authorization_code_expired_time))

    # 预授权码（可选项）
    pre_auth_code_soup = soup.find_all('PreAuthCode')
    if pre_auth_code_soup and len(pre_auth_code_soup) > 0:
        pre_auth_code = pre_auth_code_soup[0].text
        app.logger.info('pre_auth_code = {}'.format(pre_auth_code))

    # Ticket内容
    component_verify_ticket_soup = soup.find_all('ComponentVerifyTicket')
    if component_verify_ticket_soup and len(component_verify_ticket_soup) > 0:
        component_verify_ticket = component_verify_ticket_soup[0].text
        app.logger.info('component_verify_ticket = {}'.format(component_verify_ticket))
        db_utils.set_component_verify_ticket(appid, component_verify_ticket)
    return 'success'


@app.route('/<string:appid>/callback', methods=['POST'])
def receiver_callback(appid):
    """  2.	消息与事件接收接收公众号或小程序消息和事件推送
        POST /:appid/callback
        请求头 Content-Type: text/xml
        请求参数 signature, timestamp, nonce, encrypt_type, msg_signature
        请求主体（xml，需要由请求参数解密并转成 JSON）：
        ToUserName: 开发者微信号
        FromUserName: 发送方账号(OpenID)
        CreateTime: 消息创建时间
        MsgType: 消息类型，比如 文本text, 图片image 等
        Content: 文本消息内容
        MsgId: 消息ID
        ...
        详情：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140453
    """
    app.logger.info('/<string:appid>/callback')

    signature = request.values.get('signature')
    app.logger.info("signature:{}".format(signature))
    timestamp = request.values.get('timestamp')
    app.logger.info("timestamp:{}".format(timestamp))
    nonce = request.values.get('nonce')
    app.logger.info("nonce:{}".format(nonce))
    encrypt_type = request.values.get('encrypt_type')
    app.logger.info("noencrypt_typence:{}".format(encrypt_type))
    msg_sign = request.values.get('msg_signature')
    app.logger.info("msg_sign:{}".format(msg_sign))

    soup = BeautifulSoup(request.data, 'xml')
    app.logger.info(request.data)
    app.logger.info(soup.find('xml'))
    from_xml = soup.find('xml')

    # 解密接口
    token = config.WECHAT_MESSAGE_VERIFY_TOKEN
    sEncodingAESKey = config.WECHAT_MESSAGE_ENCODED_SYMMETRIC_KEY
    decrypt_test = WXBizMsgCrypt(token, sEncodingAESKey, appid)
    ret, decryp_xml = decrypt_test.DecryptMsg(from_xml, msg_sign, timestamp, nonce)
    app.logger.info(ret)
    app.logger.info(decryp_xml)

    # 解密为xml
    soup = BeautifulSoup(decryp_xml, 'xml')

    # 开发者微信号
    to_user_name_soup = soup.find_all('ToUserName')
    if to_user_name_soup and len(to_user_name_soup) > 0:
        to_user_name = to_user_name_soup[0]
        app.logger.info('to_user_name = {}'.format(to_user_name))

    # 发送方账号(OpenID)
    from_user_name_soup = soup.find_all('FromUserName')
    if from_user_name_soup and len(from_user_name_soup) > 0:
        from_user_name = from_user_name_soup[0]
        app.logger.info('from_user_name = {}'.format(from_user_name))

    # 消息创建时间
    create_time_soup = soup.find_all('CreateTime')
    if create_time_soup and len(create_time_soup) > 0:
        create_time = create_time_soup[0]
        app.logger.info('create_time = {}'.format(create_time))

    # 消息类型，比如 文本text, 图片image 等
    msg_type_soup = soup.find_all('MsgType')
    if msg_type_soup and len(msg_type_soup) > 0:
        msg_type = msg_type_soup[0]
        app.logger.info('msg_type = {}'.format(msg_type))

    # 第三方平台 appid
    content_soup = soup.find_all('Content')
    if content_soup and len(content_soup) > 0:
        content = content_soup[0]
        app.logger.info('content = {}'.format(content))

    # 消息ID
    msg_id_soup = soup.find_all('MsgId')
    if msg_id_soup and len(msg_id_soup) > 0:
        msg_id = msg_id_soup[0]
        app.logger.info('msg_id = {}'.format(msg_id))

    return 'success'


@app.route('/merchant/<string:merchant_id>/info/basic/save', methods=['POST'])
def merchant_info_basic_save(merchant_id):
    """
    保存商家基本信息
    POST /merchant/info/basic/save
    请求参数 {
    id, name, displayName, foundedYear, headquaterAddress, contactName, contactPhone, contactMobile, contactEmail
    }
    """
    json_format.ParseDict(request.json, merchant_rules_pb.MerchantBasic(), ignore_unknown_fields=True)

    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/stores/save', methods=['POST'])
def merchant_info_stores_save(merchant_id):
    """
    保存商家所有门店信息
    POST /merchant/info/stores/save
    请求参数{
    stores: [{}]
    流程: 业务员输入门店信息并保存到merchant实例的stores中.
    """

    store_obj_list = []
    for store_json in request.json:
        merchant_store = json_format.ParseDict(store_json, merchant_rules_pb.Store(),
                                               ignore_unknown_fields=True)
        if not merchant_store.id or len(merchant_store.id) == 0:
            merchant_store.id = str(uuid.uuid4())
        store_obj_list.append(merchant_store)
    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/store/save', methods=['POST'])
def merchant_info_store_save(merchant_id):
    """
    6.	修改或者添加商家门店信息
    POST /merchant/info/stores/save
    请求参数{
    stores: [
            {
            id, name, address, phone, postalCode, numEmployees, capacity, posType
            }
        ]
    }
    流程: 业务员输入门店信息并保存到merchant实例的stores中.
    """
    json_format.ParseDict(request.json, merchant_rules_pb.Store(), ignore_unknown_fields=True)

    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/qualification/save', methods=['POST'])
def merchant_info_qualification_save(merchant_id):
    """
    保存商家资质信息
    POST /merchant/info/qualification/save
    请求参数: MerchantQualifications里的所有数据
    {
      business_type, catering_type, customer_service_phone, food_distribution_permit_url, catering_service_permit_url, license_photo_url, license_number, is_license_forever, license_start, license_end, license_scope, id_holder_type, id_type, id_front_photo, id_back_photo, id_number, id_name, is_id_forever, id_card_start, id_card_end, entrance_photo_url, store_photo_url, pos_photo_url, other_photo_urls
    }
    流程: 业务员输入商家资质信息并保存到merchant 实例的MerchantQualifications中
    """
    # 解析数据
    json_format.ParseDict(request.json, merchant_rules_pb.MerchantQualifications(), ignore_unknown_fields=True)
    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/payment/save', methods=['POST'])
def merchant_info_payment_save(merchant_id):
    """
    保存商家支付信息
    POST /merchant/info/payment/save
    请求参数: paymentInfo里的所有数据
    {bank_account_type, bank_name, branch_name, account_number}
    流程: 业务员输入商家支付信息并保存到Merchant实例的paymentInfo里面.
    """

    json_format.ParseDict(request.json, merchant_rules_pb.PaymentInfo(), ignore_unknown_fields=True)
    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/design_config/save', methods=['POST'])
def save_merchant_info_design_config(merchant_id):
    """ 保存会员卡和券的logo, 卡券的颜色，卡券的背景
    POST /merchant/<string:merchant_id>/info/design_config/save
    Args:
        merchant_rules_pb2.DesignConfig()
    Returns:
        merchant_rules_pb2.DesignConfig()
    """
    json_format.ParseDict(request.json, merchant_rules_pb.DesignConfig(), ignore_unknown_fields=True)
    return base_responses.jsonify_response()


@app.route('/merchant/<string:merchant_id>/info/coupon_config/save', methods=['POST'])
def save_merchant_info_coupon_config(merchant_id):
    """ 保存营销方案
    POST /merchant/<string:merchant_id>/info/coupon_config/save
    Args:
        merchant_rules_pb2.CouponConfig()
    Returns:
        merchant_rules_pb2.CouponConfig()
    """
    json_format.ParseDict(request.json, merchant_rules_pb.CouponConfig(), ignore_unknown_fields=True)
    return base_responses.jsonify_response()


@app.route('/merchant/activate', methods=['POST'])
def merchant_activate():
    """
        激活商家
        请求方式:
            GET /merchant/activate
        Args:
            {merchantId}
        Returns:
            data : merchant.Merchant()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/list', methods=['GET'])
def merchant_list():
    """
        获取业务员名下所有商家的全部信息
        请求方式:
            GET /merchant/list
        Args:
            {staffId}
         Returns:
            {basic_info}
            data : merchant.Merchant()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info/basic', methods=['GET'])
def merchant_info_basic():
    """
        获取商家基本信息
        请求方式:
            GET /merchant/info/basic
        Args:
            {merchantId}
         Returns:
            {basic_info}
            data : merchant.MerchantBasic()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info/stores', methods=['GET'])
def merchant_info_stores():
    """
        获取商家所有等门店信息
        请求方式:
            GET /merchant/info/stores
        Args:
            {merchantId}
         Returns:
            {data}
            data : list[merchant_rules_pb2.Store()]
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info/store', methods=['GET'])
def merchant_info_store():
    """
        获取商家门店信息
        请求方式:
            GET /merchant/info/store
        Args:
            {merchantId}
         Returns:
            {'id', 'store'}
            id : merchant_id
            store : merchant_rules_pb2.Store()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info/qualification', methods=['GET'])
def merchant_info_qualification():
    """
    获取商家资质信息
    请求方式:
       GET /merchant/info/qualification
    Args:
        {merchantId}
    Returns:
        {'id', 'merchant_qualifications'}
        id : merchant_id
        merchant_qualifications : merchant_rules_pb2.MerchantQualifications()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info/payment', methods=['GET'])
def merchant_info_payment():
    """
    获取商家支付信息
    请求方式:
       GET /merchant/info/payment
    Args:
        {merchantId}
    Returns:
        {'id', 'payment_info'}
        id : merchant_id
        payment_info : merchant_rules_pb2.PaymentInfo()
    """
    return base_responses.jsonify_response()


@app.route('/merchant/info', methods=['GET'])
def merchant_info():
    """
    获取商户信息
    请求方式:
       GET /merchant/info
    Args:
        merchantId
    Returns:
        merchant : merchant_rules_pb2.Merchant()
    """
    return base_responses.jsonify_response()


newMembersMetrics = [
    ["08:00", 1],
    ["09:00", 2],
    ["10:00", 2],
    ["11:00", 8],
    ["12:00", 12],
    ["13:00", 7],
    ["14:00", 3],
    ["15:00", 0],
    ["16:00", 1],
    ["17:00", 9],
    ["18:00", 18],
    ["19:00", 22],
    ["20:00", 13],
    ["21:00", 4],
    ["22:00", 2],
]

usedCouponsMetrics = [
    ["08:00", 3],
    ["09:00", 4],
    ["10:00", 0],
    ["11:00", 7],
    ["12:00", 20],
    ["13:00", 18],
    ["14:00", 1],
    ["15:00", 0],
    ["16:00", 0],
    ["17:00", 10],
    ["18:00", 32],
    ["19:00", 16],
    ["20:00", 8],
    ["21:00", 8],
    ["22:00", 2]
]

visitsMetrics = [
    ["08:00", 6],
    ["09:00", 9],
    ["10:00", 2],
    ["11:00", 11],
    ["12:00", 33],
    ["13:00", 21],
    ["14:00", 8],
    ["15:00", 1],
    ["16:00", 6],
    ["17:00", 19],
    ["18:00", 40],
    ["19:00", 55],
    ["20:00", 26],
    ["21:00", 13],
    ["22:00", 9],
]

orderAmountsMetrics = [
    ["08:00", 18],
    ["09:00", 27],
    ["10:00", 6],
    ["11:00", 33],
    ["12:00", 99],
    ["13:00", 63],
    ["14:00", 24],
    ["15:00", 3],
    ["16:00", 18],
    ["17:00", 57],
    ["18:00", 120],
    ["19:00", 165],
    ["20:00", 52],
    ["21:00", 39],
    ["22:00", 18]
]

orders = [
    {
        "userId": "1",
        "nickname": "大江东去",
        "tradeTime": date_utils.timestamp_second(),
        "tradeAmount": 120.00,
    }
]

members = [
    {
        "userId": "1",
        "nickname": "大江东去",
        "joinedTime": "1554027530",
        "name": "胡小虎",
        "mobilePhone": "18825283721",
        "birthday": "1990-01-01",
        "sex": "男",
    }
]

"""
  获取最新会员列表
    请求方式:
       GET /merchant/new_member/list
    Args:
        merchant_service_pb2.QueryMerchantNewMembersRequest
    Returns:
        merchant_service_pb2.QueryMerchantNewMembersResponse

  获取订单列表
    请求方式:
       GET /merchant/order/list
    Args:
        merchant_service_pb2.QueryMerchantOrdersRequest
    Returns:
        merchant_service_pb2.QueryMerchantOrdersResponse

  查询趋势分析请求
    请求方式:
       GET /merchant/trend_analysis
    Args:
        merchant_service_pb2.QueryMerchantTrendAnalysisRequest
    Returns:
        merchant_service_pb2.QueryMerchantTrendAnalysisResponse
"""


# 获取最新会员列表
@app.route('/merchant/<string:merchant_id>/new_member/list', methods=['OPTIONS', 'GET'])
def query_merchant_new_member_list(merchant_id):
    """ 获取最新会员列表
    请求方式:
       GET /merchant/new_member/list
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantNewMembersRequest
    Returns:
        merchant_service_pb2.QueryMerchantNewMembersResponse
    :return:
    """
    LOG = LogUtils(app, request)
    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchantNewMembersRequest(),
    #                                       ignore_unknown_fields=True)
    # merchant_id = request.values.get()

    response = merchant_service_pb2.QueryMerchantNewMembersResponse()
    response.errcode = 0
    response.errmsg = "success"

    for member in members:
        resp_member = response.members.add()
        resp_member.CopyFrom(json_format.ParseDict(member, ui_merchant_user_pb2.Member(),
                                                   ignore_unknown_fields=True))

    resp = json_format.MessageToDict(response,
                                     including_default_value_fields=True,
                                     use_integers_for_enums=True)
    resp = jsonify(resp)
    LOG.info_api_responses(resp)
    return resp


# 获取订单列表
@app.route('/merchant/<string:merchant_id>/order/list', methods=['OPTIONS', 'GET'])
def query_merchant_order_list(merchant_id):
    """ 获取订单列表
    请求方式:
       GET /merchant/order/list
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantOrdersRequest
    Returns:
        merchant_service_pb2.QueryMerchantOrdersResponse
    :return:
    """
    LOG = LogUtils(app, request)
    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchantOrdersRequest(),
    #                                       ignore_unknown_fields=True)
    # merchant_id = query_request.merchant_id
    response = merchant_service_pb2.QueryMerchantOrdersResponse()
    response.errcode = 0
    response.errmsg = "success"

    for order in orders:
        resp_order = response.orders.add()
        resp_order.CopyFrom(json_format.ParseDict(order, ui_merchant_order_pb2.Order(),
                                                  ignore_unknown_fields=True))

    resp = json_format.MessageToDict(response,
                                     including_default_value_fields=True,
                                     use_integers_for_enums=True)
    resp = jsonify(resp)
    LOG.info_api_responses(resp)
    return resp


@app.route('/merchant/<string:merchant_id>/plan/list', methods=['OPTIONS', 'GET'])
def query_merchant_new_plan_list():
    """ 获取营销计划
       请求方式:
          GET /merchant/trend_analysis
       Args:
           待定
       Returns:
           待定
        :return:
    """
    return base_responses.jsonify_response()


# 查询趋势分析请求
@app.route('/merchant/<string:merchant_id>/trend_analysis', methods=['OPTIONS', 'GET'])
def query_merchant_trend_analysis(merchant_id):
    """ 查询趋势分析请求
    请求方式:
       GET /merchant/trend_analysis
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantTrendAnalysisRequest
    Returns:
        merchant_service_pb2.QueryMerchantTrendAnalysisResponse
    :return:
    """
    LOG = LogUtils(app, request)
    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchantTrendAnalysisRequest(),
    #                                       ignore_unknown_fields=True)
    # 商户ID
    # merchant_id = query_request.merchant_id
    # 起始日期，比如
    # date_start = request.values.get('date_start')
    # # 结束日期，比如
    # date_end = request.values.get('date_end')

    response = merchant_service_pb2.QueryMerchantTrendAnalysisResponse()

    response.errcode = 0
    response.errmsg = "success"

    # 响应趋势分析内容
    # if not merchant_id == "1":
    #     newMembersMetrics = []
    #     visitsMetrics = []
    #     usedCouponsMetrics = []
    #     orderAmountsMetrics = []

    # 新会员数
    for metrics in newMembersMetrics:
        new_members_metrics = response.new_members_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 访问次数
    for metrics in visitsMetrics:
        new_members_metrics = response.visits_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 优惠券核销数
    for metrics in usedCouponsMetrics:
        new_members_metrics = response.used_coupons_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 订单金额
    for metrics in orderAmountsMetrics:
        new_members_metrics = response.order_amounts_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    resp = json_format.MessageToDict(response,
                                     including_default_value_fields=True,
                                     use_integers_for_enums=True)
    resp = jsonify(resp)
    LOG.info_api_responses(resp)
    return resp


if __name__ == "__main__":
    app.config.update(DEBUG=True)
    app.run(host="0.0.0.0", port=config.MERCHANT_ASSISTANT_SERVICE_PORT)

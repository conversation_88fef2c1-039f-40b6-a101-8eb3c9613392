import logging
import os
import sys
import json
import requests

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    print(parentPath)
    sys.path.insert(0, parentPath)

import common.logger.event_handling_service_logger
from scripts.services import service_common
from common import constants
service_common.set_environment_var(constants.DEPLOYMENT_ENV_PROD)


from flask import Flask
from flask import request
import xmltodict

from common import constants
from common.config import config
from common.utils import date_utils
from common.utils.WXBizMsgCrypt import SHA1
from common.utils.WXBizMsgCrypt import WXBizMsgCrypt
from common.utils import access_token_helper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from event_ops.auth_handler import AuthEventHandler
from event_ops.card_handler import CardEventHandler
from event_ops.scan_code_order_handler import ScanCodeOrderHandler


app = Flask(__name__)

logger = logging.getLogger(__name__)

@app.route('/receiver', methods=['POST'])
def receiver():
    """  1.	授权事件接收用于接收取消授权通知、授权成功通知、授权更新通知，也用于接收ticket，ticket是验证平台方的重要凭据
        POST /receiver
        请求头 Content-Type: text/xml
        请求参数 signature, timestamp, nonce, encrypt_type, msg_signature
        请求主体（xml，需要由请求参数解密并转成 JSON）：
        AppId: 第三方平台 appid
        CreateTime: 消息创建时间
        InfoType: 事件消息类型，比如 平台component_verify_ticket, 授权authorized, 取消授权unauthorized
        AuthorizerAppid: 授权方appid
        AuthorizationCode: 授权码（可选项）
        AuthorizationCodeExpiredTime: 授权码过期时间（可选项）
        PreAuthCode: 预授权码（可选项）
        详情: https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
    """
    decrypt_dict = _parse_encrypted_xml_to_dict(request)
    if decrypt_dict is not None:
        decrypt_data = decrypt_dict['xml']
        # 通知类型
        info_type = decrypt_data['InfoType']
        if info_type == 'component_verify_ticket':
            AuthEventHandler().handle_component_verify_ticket_info(decrypt_data)
        elif info_type == 'authorized':
            AuthEventHandler().handle_authorized_info(decrypt_data)
        elif info_type == 'unauthorized':
            AuthEventHandler().handle_unauthorized_info(decrypt_data)
        elif info_type == 'updateauthorized':
            AuthEventHandler().handle_update_authorized_info(decrypt_data)

    return 'success'


@app.route('/receiver/<string:appid>/callback', methods=['POST'])
def receiver_callback(appid):
    """  2.	消息与事件接收接收公众号或小程序消息和事件推送
        POST /:appid/callback
        请求头 Content-Type: text/xml
        请求参数 signature, timestamp, nonce, encrypt_type, msg_signature
        请求主体（xml，需要由请求参数解密并转成 JSON）：
        ToUserName: 开发者微信号
        FromUserName: 发送方账号(OpenID)
        CreateTime: 消息创建时间
        MsgType: 消息类型，比如 文本text, 图片image 等
        Content: 文本消息内容
        MsgId: 消息ID
        ...
        详情：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140453
    """

    decrypt_dict = _parse_encrypted_xml_to_dict(request)
    if decrypt_dict is not None:
        decrypt_data = decrypt_dict['xml']
        print(decrypt_data)
        # 事件类型
        event = decrypt_data['Event']
        if event == 'card_pass_check':
            # 卡券通过审核
            CardEventHandler().handle_card_pass_event(decrypt_data)
        elif event == 'card_not_pass_check':
            # 卡券未通过审核
            CardEventHandler().handle_card_not_pass_event(decrypt_data)
        elif event == 'user_get_card':
            # 用户领取卡券
            CardEventHandler().handle_user_get_card_event(decrypt_data)
        elif event == 'user_gifting_card':
            # 用户转赠卡券
            CardEventHandler().handle_user_gifting_card_event(decrypt_data)
        elif event == 'user_del_card':
            # 用户删除卡券
            CardEventHandler().handle_user_delete_card_event(decrypt_data)
        elif event == 'user_consume_card':
            # 用户核销卡券
            CardEventHandler().handle_user_consume_card_event(decrypt_data)
        elif event == 'user_pay_from_pay_cell':
            # 买单
            CardEventHandler().handle_user_pay_from_pay_cell_event(decrypt_data)
        elif event == 'user_view_card':
            # 用户点击会员卡，前提设置 need_push_on_view: true
            CardEventHandler().handle_user_view_card_event(decrypt_data)
        elif event == 'user_enter_session_from_card':
            # 用户从卡券进入公众号会话
            CardEventHandler().handle_user_enter_session_from_card(decrypt_data)
        elif event == 'update_member_card':
            # 会员卡内容更新
            CardEventHandler().handle_update_member_card_event(decrypt_data)
        elif event == 'card_sku_remind':
            # 库存警报
            # 初始库存数大于200且当前库存小于等于100时，用户尝试领券会触发发送事件给商户，事件每隔12h发送一次
            CardEventHandler().handle_card_sku_remind_event(decrypt_data)
        elif event == 'card_pay_order':
            # 券点流水详情
            # 当商户朋友的券券点发生变动时，微信服务器会推送消息给商户服务器
            CardEventHandler().handle_card_pay_order_event(decrypt_data)
        elif event == 'submit_membercard_user_info':
            # 会员卡激活
            CardEventHandler().handle_submit_membercard_user_info_event(decrypt_data)
        elif event == 'card_merchant_check_result':
            # 卡券子商户审核事件
            CardEventHandler().handle_card_submerchant_check_result_event(decrypt_data)
        elif event == 'SCAN':
            key = decrypt_data['EventKey']
            ScanCodeOrderHandler(key, request.args.get('openid')).handle_scan_code_event(decrypt_data)
        elif event == 'subscribe':
            key = decrypt_data['EventKey']
            ScanCodeOrderHandler(key, request.args.get('openid')).handle_subscribe_event(decrypt_data)
    return 'success'

@app.route('/receiver/<string:appid>/callback', methods=['GET'])
def verify_receiver(appid):
    """微信公众号用于校验开发服务器配置

    URL参数带上 signature, timestamp, nonce, echostr，
    WECHAT_MESSAGE_VERIFY_TOKEN, timestamp, nonce 使用SHA1算法生成签名，
    校验签名与 signature 是否一致，如一致则返回 echostr 表示校验通过
    """
    signature = request.values.get('signature')
    timestamp = request.values.get('timestamp')
    nonce = request.values.get('nonce')
    echostr = request.values.get('echostr')

    ret, result = SHA1().getSHA1(config.WECHAT_MESSAGE_VERIFY_TOKEN, timestamp, nonce, '')
    if ret == 0 and result == signature:
        return echostr

    return ''

def _parse_encrypted_xml_to_dict(request):
    """
        接收已授权公众号消息和事件的URL中有四个参数:
        encrypt_type: 加密类型，为 AES
        msg_signature: 消息体签名，用于验证消息体的正确性
        timestamp: 时间戳
        nonce: 随机数
        另，URL额外带着第五个参数 signature，目前微信文档暂无体现

        :param request: (Object) 请求
        :return: (Dict)
    """
    signature = request.values.get('signature')
    timestamp = request.values.get('timestamp')
    nonce = request.values.get('nonce')
    encrypt_type = request.values.get('encrypt_type')
    msg_sign = request.values.get('msg_signature')

    logger.info("消息解密URL:{}，参数:".format(request.base_url))
    logger.info(" signature:{}".format(signature))
    logger.info(" timestamp:{}".format(timestamp))
    logger.info(" nonce:{}".format(nonce))
    logger.info(" noencrypt_typence:{}".format(encrypt_type))
    logger.info(" msg_sign:{}".format(msg_sign))

    crypt_instance = WXBizMsgCrypt(config.WECHAT_MESSAGE_VERIFY_TOKEN,
                                   config.WECHAT_MESSAGE_ENCODED_SYMMETRIC_KEY,
                                   config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME))
    ret, decrypt_xml = crypt_instance.DecryptMsg(request.data, msg_sign, timestamp, nonce)

    if ret == 0:
        # 消息解密成功
        return xmltodict.parse(decrypt_xml)
    else:
        logger.error("消息解密失败, 错误码:{}, 来自URL:{}".format(ret, request.base_url))
        return None


# 以Python脚本形式启动
if __name__ == "__main__":
    app.config.update(DEBUG=True)
    app.run(host="0.0.0.0", port=9900)

# -*- coding: utf-8 -*-

import logging
import os

from flask import Blueprint
from flask import jsonify
from flask import make_response
from flask import request
from flask import send_file
from google.protobuf import json_format

import proto.bi.common_pb2 as bi_common_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.merchant_service_pb2 as merchant_service_pb
import proto.staff_pb2 as staff_pb
from bi import membership_analyzer
from bi import payment_analyzer
from business_ops import member_card_manager
from business_ops.coupon_manager import CouponManager
from business_ops.member_card_manager import MemberCardManager
from business_ops.merchant_manager import MerchantManager
from business_ops.coupon_package_manager import CouponPackageManager
from common import constants
from common.config import config
from common.utils import date_utils
from common.utils import requests_utils
from common.utils import id_manager
from dao.base_helper import BaseHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from dao.code_plate_da_helper import CodePlateHelper
from service import base_responses
from service import error_codes
from service.base_responses import create_responses_obj
from service.base_responses import error_responses
from service.base_responses import success_responses_obj
from service.base_responses import make_json_response
from service.base_responses import jsonify_response
from view_ops import coupon_view_helper
from view_ops.merchant_view_helper import MerchantViewObjectHelper
from view_ops.dining_event_view_helper import DiningEventViewObjectHelper
from wechat_lib.resp_error import WechatApiResponseError
from business_ops.staff_manager import StaffManager

merchant = Blueprint('merchant', __name__)
logger = logging.getLogger(__name__)


@merchant.route("/merchant/search", methods=["GET"])
def merchant_search():
    """ 按条件查找商户,按距离远近返回
    """
    lat = request.args.get('latitude', 22.524020182291668)
    lng = request.args.get("longitude", 113.9372493489)
    name = request.args.get("name", None)
    radius = 5000
    view_obj = MerchantViewObjectHelper()
    merchants = view_obj.get_merchant_by_name(name=name, lng=lng, lat=lat, radius=radius)
    merchants = json_format.MessageToDict(merchants, including_default_value_fields=True)
    return jsonify_response(merchants)


# TODO: 即将弃用，使用 /merchant/<string:merchant_id>/stats/payment 替代
@merchant.route('/merchant/<string:merchant_id>/payment_stats', methods=['POST'])
def get_merchant_payment_stats(merchant_id):
    """获取商户Payment数据

    Args:
        merchant_id: (string) 商户ID
    """
    request_json = request.json
    start_time = requests_utils.get_value_from_json(request_json, 'startTime')
    end_time = requests_utils.get_value_from_json(request_json, 'endTime')
    time_granularity = requests_utils.get_value_from_json(request_json, 'timeGranularity')
    store_id = requests_utils.get_value_from_json(request_json, 'storeId')
    if time_granularity == "hourly":
        time_granularity = bi_common_pb.HOURLY
    elif time_granularity == "daily":
        time_granularity = bi_common_pb.DAILY
    payment_stats = payment_analyzer.get_merchant_payment_stats(
        merchant_id=merchant_id, store_id=store_id, start_time=start_time,
        end_time=end_time, time_granularity=time_granularity)
    json_payment_stats = json_format.MessageToDict(payment_stats, including_default_value_fields=True)
    response_obj = success_responses_obj()
    response_obj['paymentStats'] = json_payment_stats
    resp = jsonify(response_obj)
    return resp


# TODO: 即将弃用，使用 /merchant/<string:merchant_id>/stats/member 替代
@merchant.route('/merchant/<string:merchant_id>/member_stats', methods=['POST'])
def get_merchant_member_stats(merchant_id):
    """获取商户会员数据

    Args:
        merchant_id: (string) 商户ID
    """
    request_json = request.json
    start_time = requests_utils.get_value_from_json(request_json, 'startTime')
    end_time = requests_utils.get_value_from_json(request_json, 'endTime')
    time_granularity = requests_utils.get_value_from_json(request_json, 'timeGranularity')
    if time_granularity == "hourly":
        time_granularity = bi_common_pb.HOURLY
    elif time_granularity == "daily":
        time_granularity = bi_common_pb.DAILY
    member_stats = membership_analyzer.get_merchant_membership_stats(
        merchant_id, start_time=start_time, end_time=end_time, time_granularity=time_granularity)
    json_member_stats = json_format.MessageToDict(member_stats, including_default_value_fields=True)
    response_obj = success_responses_obj()
    response_obj['membershipStats'] = json_member_stats
    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/save', methods=['POST'])
def update_merchant(merchant_id):
    """
    更新商家数据
    请求参数

    响应主体
    """
    merchant = json_format.ParseDict(request.json, merchant_pb.Merchant(), ignore_unknown_fields=True)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/v1/merchant/<string:merchant_id>/info', methods=['GET'])
def get_merchant_info(merchant_id):
    """
        获取商户信息
        请求方式:
           GET /merchant/info
        Args:
            merchantId
        Returns:
            merchant : ui_merchant_rules_pb.MerchantInfo()
    """
    response_obj = success_responses_obj()

    merchant_view_obj = MerchantViewObjectHelper()
    merchant_info = MerchantDataAccessHelper().get_merchant(merchant_id)
    pb_merchant_info = merchant_view_obj.convert_merchant_info_vo(merchant_info)
    if pb_merchant_info is not None:
        json_obj = json_format.MessageToDict(pb_merchant_info, including_default_value_fields=True)
        response_obj['merchant'] = json_obj

    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/qrcode/<string:qrcode_id>', methods=['GET'])
def merchant_barcode_get(qrcode_id):
    """
    获取商家授权二维码
    GET /merchant/qrcode/<string:qrcode_id>
    请求参数

    响应主体
        二维码图片流
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    """
    resp = BaseHelper().get_merchant_barcode(qrcode_id)
    return resp


@merchant.route('/merchant/get_qrcode_info', methods=['GET'])
def get_qrcode_info():
    """
        获取商家二维码id 对应的 业务员和商家信息
        GET /merchant/get_qrcodeid_info
        请求参数
            qrcodeId
        响应主体
            {staffId, merchantId}
    """
    qrcode_id = request.values.get("qrcodeId")
    qrcode_info = QrcodeDataAccessHelper().get_merchant_qrcode(qrcode_id)
    merchant_id = qrcode_info.merchant_id
    staff_id = qrcode_info.base_info.staff_id

    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    if merchant_id == 'null':
        success_responses_obj["staffId"] = staff_id
    else:
        success_responses_obj["staffId"] = staff_id
        success_responses_obj["merchantId"] = merchant_id

    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/home_wxacode_info', methods=['GET'])
def get_code_info():
    """
        获取商户首页小程序码的信息
        GET /merchant/home_wxacode_info
        请求参数
            codeId
        响应主体
            {code}
    """
    code_id = request.values.get('codeId')
    code_info = QrcodeDataAccessHelper().get_merchant_home_wxacode(code_id)

    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    if code_info:
        success_responses_obj['codeInfo'] = json_format.MessageToDict(
            code_info, including_default_value_fields=True)

    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/binding_qrcode/create', methods=['POST'])
def create_merchant_qrcode_for_binding(merchant_id):
    """生成商户绑定二维码，用于无公众号的商户绑定流程
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())

    encode_text = '{}/authorizer/binding?merchantId={}&staffId={}'.format(
        config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), merchant_id, staff.id)
    qrcode_id = BaseHelper().create_merchant_qrcode(encode_text)
    bar_code_url = '{}/merchant/qrcode/{}'.format(
        config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), qrcode_id)

    response_obj = success_responses_obj()
    response_obj['barCodeUrl'] = bar_code_url
    resp = jsonify(response_obj)
    return resp


# @Deprecated
# 使用 /merchant/<string:merchant_id>/auth_qrcode/create
@merchant.route('/merchant/qrcode/create', methods=['POST'])
def merchant_qrcode_create():
    """
    生成员工小程序二维码
    GET /merchant/qrcode
    请求参数 {merchantId}
    响应主体 {qrcodeUrl}
    流程: 业务员工创建Merchant主体后, 生成一个二维码给员工扫描, 扫描后跳转到员工助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    #
    """
    request_json = request.json
    merchant_id = request_json['merchantId']
    return create_merchant_qrcode_for_auth(merchant_id)


@merchant.route('/merchant/<string:merchant_id>/auth_qrcode/create', methods=['POST'])
def create_merchant_qrcode_for_auth(merchant_id):
    """
        生成商户授权二维码，用于有公众号的商户授权流程
        GET /merchant/<string:merchant_id>/auth_qrcode/create
        请求参数
            merchant_id
        响应主体
            {barCodeUrl}
        流程
            业务员工创建Merchant主体后, 生成一个二维码给员工扫描, 扫描后跳转到员工助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())

    staff_id = staff.id
    encode_text = '{}/authorizer/auth?merchantId={}&staffId={}'.format(
        config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME),
        merchant_id, staff_id)
    qrcode_id = BaseHelper().create_merchant_qrcode(encode_text)
    bar_code_url = '{}/merchant/qrcode/{}'.format(
        config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), qrcode_id)
    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj['barCodeUrl'] = bar_code_url

    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/wxacode/download', methods=['GET'])
def download_merchant_qrcode(merchant_id):
    """
        生成并下载商户首页入口小程序码，用于拓展用户，完成支付等流程
        GET /merchant/<string:merchant_id>/wxacode/download
        请求参数
            merchant_id
        请求主体
            start_index: 开始编号，默认为 0
            total: 生成二维码总数
        响应主体
            二维码文件压缩包
    """
    start_index = request.values.get('start_index', default=0, type=int)
    total = request.values.get('total', default=50, type=int)

    output_path = BaseHelper().create_merchant_home_wxacodes(merchant_id=merchant_id,
                                                             start_index=start_index,
                                                             total=total)

    # 输出文件
    response = make_response(send_file(output_path))
    response.headers['Content-Disposition'] = 'attachment; filename={}'.format(os.path.basename(output_path))

    return response


@merchant.route('/merchant/<string:merchant_id>/member_card_category', methods=['GET'])
def get_merchant_member_card(merchant_id):
    user_id = requests_utils.get_headers_info(request, 'userId')
    card_category = MemberCardManager().get_card_category_for_user(user_id, merchant_id)
    resp = None
    if card_category:
        card_category_json = json_format.MessageToDict(card_category, including_default_value_fields=True)
        success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
        success_responses_obj["memberCardCategory"] = card_category_json
        resp = jsonify(success_responses_obj)
    else:
        resp = jsonify(base_responses.success_responses_obj())

    return resp


@merchant.route('/merchant/<string:merchant_id>/card_category/create', methods=['POST'])
def create_member_card(merchant_id):
    member_card_manager.create_default_member_card(merchant_id, True)
    member_card_manager.create_default_member_card(merchant_id, False)
    success_responses_obj = create_responses_obj(0, 'success')
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/card/<string:card_category_id>/activate_extra', methods=['GET'])
def get_activate_extra(merchant_id, card_category_id):
    activate_extra_obj = member_card_manager.get_activate_extra(merchant_id, card_category_id)
    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['extraData'] = activate_extra_obj
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/card/<string:card_category_id>/ext', methods=['GET'])
def get_card_list(merchant_id, card_category_id):
    card_category_ext = MerchantManager().get_card_ext(merchant_id, card_category_id)
    ext_json = json_format.MessageToDict(card_category_ext, preserving_proto_field_name=True)
    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj["cardExt"] = ext_json
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/staffs', methods=['GET'])
def get_merchant_staffs(merchant_id):
    """获取商户门店的员工信息列表
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    response_obj = success_responses_obj()
    role = MerchantManager().get_merchant_staff_role(merchant_id, user_id)
    if role == merchant_pb.SUPER_ADMIN:
        merchant_staffs = MerchantViewObjectHelper().get_staffs(merchant_id)
        json_staffs = [json_format.MessageToDict(merchant_staff,
                                                 including_default_value_fields=True) for merchant_staff in merchant_staffs]
        response_obj['staffs'] = json_staffs
    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/store/<string:store_id>/manager/invitation/accept', methods=['POST'])
def accept_manager_invitation(merchant_id, store_id):
    """接受邀请成为商户门店管理员
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    inviter_id = requests_utils.get_value_from_json(request.json, 'inviterId')

    role = MerchantManager().get_merchant_staff_role(merchant_id, inviter_id)

    if role == merchant_pb.SUPER_ADMIN:
        response_obj = success_responses_obj()
        MerchantManager().add_user_to_merchant_manager_list(merchant_id=merchant_id,
                                                            store_id=store_id,
                                                            user_id=user_id,
                                                            role=merchant_pb.ADMIN)
    else:
        response_obj = create_responses_obj(error_codes.MERCHANT_STORE_INVITATION_INVITER_PERMISSION_DENIED,
                                            error_codes.MERCHANT_STORE_INVITATION_INVITER_PERMISSION_DENIED_MSG)
    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/create_new_member_coupon_categories', methods=['POST'])
def create_new_member_coupon_category(merchant_id):
    """
        为指定商户创建拉新优惠券类型

        请求方式:
            POST /merchant/<string:merchant_id>/create_new_member_coupon_categories
        Args:
            { status }
    """
    CouponManager().create_new_member_coupon_categories(merchant_id)
    resp = jsonify(base_responses.success_responses_obj())
    return resp


@merchant.route('/merchant/<string:merchant_id>/create_ads_coupon_categories', methods=['POST'])
def create_ads_coupon_categories(merchant_id):
    """
        为指定商户创建广告投放优惠券类型

        请求方式:
            POST /merchant/<string:merchant_id>/create_ads_coupon_categories
        Args:
            { status }
    """
    CouponManager().create_wechat_moments_ad_coupon_categories(merchant_id)
    resp = jsonify(base_responses.success_responses_obj())
    return resp


# @Deprecated 使用 /merchant/<string:merchant_id>/coupon_category/issue
@merchant.route('/merchant/<string:merchant_id>/issue_coupons', methods=['POST'])
def issue_coupons_to_member(merchant_id):
    """根据场景投放优惠券

    用于营销场景，支持投放拉新券和微信朋友圈广告券等，如果用户已领取，则不返回

    Args:
        merchant_id: (string) 商户ID
        issue_scene:
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    input_issue_scene = requests_utils.get_value_from_json(request.json, 'issueScene')
    if input_issue_scene == 'WECHAT_MOMENTS_AD':
        issue_scene = coupon_category_pb.CouponCategory.WECHAT_MOMENTS_AD
    elif issue_scene == 'NEW_MEMBER':
        issue_scene = coupon_category_pb.CouponCategory.NEW_MEMBER

    if issue_scene:
        coupons = CouponManager().issue_coupons_to_user(merchant_id=merchant_id,
                                                        user_id=user_id,
                                                        issue_scene=issue_scene)
        coupons = [coupon_view_helper.convert_to_ui_coupon(coupon) for coupon in coupons]
    response_obj = success_responses_obj()
    if coupons:
        response_obj['coupons'] = [json_format.MessageToDict(
            coupon, including_default_value_fields=True) for coupon in coupons]
    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/status/update', methods=['POST'])
def update_merchant_status(merchant_id):
    """
        更改商户状态

        请求方式:
            POST /merchant/<string:merchant_id>/status/update
        Args:
            { status }
    """
    user_id = requests_utils.get_headers_info(request, 'userId')

    # TODO: 增加权限校验拦截器
    staff = StaffDataAccessHelper().get_staff(user_id)
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

    if merchant.binding_staff_id == staff.id:
        status = request.json['status']
        try:
            status_pb = merchant_pb.MerchantStatus.Value(status)
            MerchantDataAccessHelper().update_merchant_status(merchant_id, status_pb)

            responses_obj = create_responses_obj(error_codes.SUCCESS,
                                                 error_codes.SUCCESS_MSG)
        except ValueError:
            responses_obj = create_responses_obj(error_codes.INVALID_REQUEST_DATA,
                                                 error_codes.INVALID_REQUEST_DATA_MSG)

    else:
        responses_obj = create_responses_obj(error_codes.MERCHANT_UPDATE_PERMISSION_DENIED,
                                             error_codes.MERCHANT_UPDATE_PERMISSION_DENIED_MSG)

    resp = jsonify(responses_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/activate', methods=['POST'])
def activate_merchant(merchant_id):
    """商家上线
    """
    user_id = requests_utils.get_headers_info(request, 'userId')

    merchant_manager = MerchantManager()
    merchant = merchant_manager.get_valid_merchant(merchant_id)
    if not merchant:
        response_obj = create_responses_obj(error_codes.MERCHANT_NOT_FOUND,
                                            error_codes.MERCHANT_NOT_FOUND_MSG)
        resp = jsonify(response_obj)
        return resp

    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    staff = StaffDataAccessHelper().get_staff(user_id)
    staff_manager = StaffManager()
    role_name = staff_pb.ShilaiStaff.Role.Name(staff.role)
    if not (staff and (
        role_name in staff_manager.get_all_merchants_roles() 
        or staff.role == staff_pb.ShilaiStaff.MANAGER 
        or merchant.binding_staff_id == staff.id)):
        response_obj = create_responses_obj(error_codes.MERCHANT_ACTIVATE_PERMISSION_DENIED,
                                            error_codes.MERCHANT_ACTIVATE_PERMISSION_DENIED_MSG)
        resp = jsonify(response_obj)
        return resp

    # 创建会员卡类
    # 目前会员卡基于微信体系，存在创卡失败的可能，如果失败，则返回错误
    auto_activate_card_catgory = member_card_manager.create_default_member_card(merchant_id, True)
    wx_activate_card_catgory = member_card_manager.create_default_member_card(merchant_id, False)
    if not (auto_activate_card_catgory and wx_activate_card_catgory):
        response_obj = create_responses_obj(error_codes.MERCHANT_ACTIVATE_FAILURE,
                                            error_codes.MERCHANT_ACTIVATE_FAILURE_MSG)
        resp = jsonify(response_obj)
        return resp

    # 创建拉新券和广告券
    coupon_manager = CouponManager()
    coupon_manager.create_new_member_coupon_categories(merchant_id)
    coupon_manager.create_wechat_moments_ad_coupon_categories(merchant_id)

    # 上线
    merchant_manager.activate_merchant(merchant_id, user_id)

    response_obj = success_responses_obj()
    resp = jsonify(response_obj)
    MerchantManager().link_shilai_miniprogram(merchant_id)
    return resp


@merchant.route('/merchant/<string:merchant_id>/link_miniprogram', methods=['POST'])
def merchant_link_miniprogram(merchant_id):
    """商户公众号关联时来饭票小程序
    """
    # TODO: 检查接口调用权限
    try:
        MerchantManager().link_shilai_miniprogram(merchant_id)
        response_obj = success_responses_obj()
    except WechatApiResponseError as error:
        response_obj = create_responses_obj(error.errcode, error.errmsg)

    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/<string:merchant_id>/link_miniprogram/status/check', methods=['POST'])
def check_linking_miniprogram_status(merchant_id):
    """检查商户公众号关联小程序的状态
    """
    # TODO: 检查接口调用权限
    try:
        response_obj = success_responses_obj()
        response_obj['isLinked'] = MerchantManager().check_linking_shilai_miniprogram_status(merchant_id)
    except WechatApiResponseError as error:
        response_obj = create_responses_obj(error.errcode, error.errmsg)

    resp = jsonify(response_obj)
    return resp


@merchant.route('/merchant/info/basic', methods=['GET'])
def merchant_info_basic():
    """
        获取商家基本信息
        请求方式:
            GET /merchant/info/basic
        Args:
            {merchantId}
         Returns:
            {basic_info}
            data : merchant.MerchantBasic()
    """
    merchant_id = request.values.get('merchantId')

    basic_info = MerchantDataAccessHelper().get_merchant_info_basic(merchant_id)

    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['data'] = {'basic_info': basic_info}
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/info/stores', methods=['GET'])
def merchant_info_stores():
    """
        获取商家所有等门店信息
        请求方式:
            GET /merchant/info/stores
        Args:
            {merchantId}
         Returns:
            {data}
            data : list[merchant_rules_pb2.Store()]
    """
    merchant_id = request.values.get('merchantId')

    merchant_stores = MerchantDataAccessHelper().get_merchant_info_stores(merchant_id)

    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['data'] = merchant_stores
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/info/store', methods=['GET'])
def merchant_info_store():
    """
        获取商家门店信息
        请求方式:
            GET /merchant/info/store
        Args:
            {merchantId}
         Returns:
            {'id', 'store'}
            id : merchant_id
            store : merchant_rules_pb2.Store()
    """

    merchant_id = request.values.get('merchantId')
    store_id = request.values.get('storeId')

    json_obj = MerchantDataAccessHelper().get_merchant_info_store(merchant_id, store_id)

    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['data'] = {'id': merchant_id, 'store': json_obj}
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/info/qualification', methods=['GET'])
def merchant_info_qualification():
    """
    获取商家资质信息
    请求方式:
       GET /merchant/info/qualification
    Args:
        {merchantId}
    Returns:
        {'id', 'merchant_qualifications'}
        id : merchant_id
        merchant_qualifications : merchant_rules_pb2.MerchantQualifications()
    """
    merchant_id = request.values.get('merchantId')

    json_obj = MerchantDataAccessHelper().get_merchant_info_qualification(merchant_id)

    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['data'] = {'id': merchant_id, 'merchant_qualifications': json_obj}
    resp = jsonify(success_responses_obj)
    return resp


@merchant.route('/merchant/info/payment', methods=['GET'])
def merchant_info_payment():
    """
    获取商家支付信息
    请求方式:
       GET /merchant/info/payment
    Args:
        {merchantId}
    Returns:
        {'id', 'payment_info'}
        id : merchant_id
        payment_info : merchant_rules_pb2.PaymentInfo()
    """
    merchant_id = request.values.get('merchantId')

    json_obj = MerchantDataAccessHelper().get_merchant_info_payment(merchant_id)

    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['data'] = {'id': merchant_id, 'payment_info': json_obj}
    resp = jsonify(success_responses_obj)
    return resp


# 获取最新会员列表
@merchant.route('/merchant/<string:merchant_id>/new_member/list', methods=['OPTIONS', 'GET'])
def query_merchant_new_member_list(merchant_id):
    """ 获取最新会员列表
    请求方式:
       GET /merchant/new_member/list
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantNewMembersRequest
    Returns:
        merchant_service_pb2.QueryMerchantNewMembersResponse
    :return:
    """
    start_time = int(request.values.get('startTime'))
    end_time = int(request.values.get('endTime'))

    start_time = int(date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).timestamp())
    end_time = int(date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).timestamp())

    response = merchant_service_pb.QueryMerchantNewMembersResponse()
    response.errcode = 0
    response.errmsg = "success"

    members = MerchantViewObjectHelper().get_members(merchant_id, start_time=start_time, end_time=end_time)
    if members:
        response.members.MergeFrom(members)

    resp = json_format.MessageToDict(response,
                                     including_default_value_fields=True,
                                     use_integers_for_enums=True)
    resp = jsonify(resp)
    return resp


@merchant.route('/merchant/<string:merchant_id>/group_dining_transaction/list', methods=['GET'])
def query_merchant_group_dining_list(merchant_id):
    start_time = int(request.values.get('startTime'))
    end_time = int(request.values.get('endTime'))
    store_id = request.values.get('storeId', None)

    start_time = int(date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).timestamp())
    end_time = int(date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).timestamp())

    transactions = MerchantViewObjectHelper().get_merchant_transactions_group_dining(
        merchant_id=merchant_id, store_id=store_id, start_time=start_time, end_time=end_time)
    resp = [json_format.MessageToDict(
        transaction, including_default_value_fields=True, use_integers_for_enums=True) for transaction in transactions]
    return make_json_response({"transactions": resp})


# 获取订单列表
@merchant.route('/merchant/<string:merchant_id>/order/list', methods=['OPTIONS', 'GET'])
def query_merchant_order_list(merchant_id):
    """ 获取订单列表
    请求方式:
       GET /merchant/order/list
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantOrdersRequest
    Returns:
        merchant_service_pb2.QueryMerchantOrdersResponse
    :return:
    """
    start_time = int(request.values.get('startTime'))
    end_time = int(request.values.get('endTime'))
    store_id = request.values.get('storeId', None)

    start_time = int(date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).timestamp())
    end_time = int(date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).timestamp())

    response = merchant_service_pb.QueryMerchantOrdersResponse()
    response.errcode = 0
    response.errmsg = "success"

    orders = MerchantViewObjectHelper().get_orders(merchant_id, store_id=store_id, start_time=start_time, end_time=end_time)
    if orders:
        response.orders.MergeFrom(orders)

    resp = json_format.MessageToDict(response, including_default_value_fields=True, use_integers_for_enums=True)
    resp = jsonify(resp)
    return resp


@merchant.route('/merchant/<string:merchant_id>/plan/list', methods=['OPTIONS', 'GET'])
def query_merchant_new_plan_list():
    """ 获取营销计划
       请求方式:
          GET /merchant/trend_analysis
       Args:
           待定
       Returns:
           待定
        :return:
    """

    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchant(),
    #                                       ignore_unknown_fields=True)
    #
    # merchant_id = query_request.merchant_id
    #
    # response
    # resp = json_format.MessageToDict(response,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    success_responses_obj = create_responses_obj(0, 'success')
    resp = jsonify(success_responses_obj)
    return resp


# TODO: ====== 以下为假数据，尽快删除！========
newMembersMetrics = [
    ["08:00", 1],
    ["09:00", 2],
    ["10:00", 2],
    ["11:00", 8],
    ["12:00", 12],
    ["13:00", 7],
    ["14:00", 3],
    ["15:00", 0],
    ["16:00", 1],
    ["17:00", 9],
    ["18:00", 18],
    ["19:00", 22],
    ["20:00", 13],
    ["21:00", 4],
    ["22:00", 2],
]

usedCouponsMetrics = [
    ["08:00", 3],
    ["09:00", 4],
    ["10:00", 0],
    ["11:00", 7],
    ["12:00", 20],
    ["13:00", 18],
    ["14:00", 1],
    ["15:00", 0],
    ["16:00", 0],
    ["17:00", 10],
    ["18:00", 32],
    ["19:00", 16],
    ["20:00", 8],
    ["21:00", 8],
    ["22:00", 2]
]

visitsMetrics = [
    ["08:00", 6],
    ["09:00", 9],
    ["10:00", 2],
    ["11:00", 11],
    ["12:00", 33],
    ["13:00", 21],
    ["14:00", 8],
    ["15:00", 1],
    ["16:00", 6],
    ["17:00", 19],
    ["18:00", 40],
    ["19:00", 55],
    ["20:00", 26],
    ["21:00", 13],
    ["22:00", 9],
]

orderAmountsMetrics = [
    ["08:00", 18],
    ["09:00", 27],
    ["10:00", 6],
    ["11:00", 33],
    ["12:00", 99],
    ["13:00", 63],
    ["14:00", 24],
    ["15:00", 3],
    ["16:00", 18],
    ["17:00", 57],
    ["18:00", 120],
    ["19:00", 165],
    ["20:00", 52],
    ["21:00", 39],
    ["22:00", 18]
]

orders = [
    {
        "userId": "1",
        "nickname": "大江东去",
        "tradeTime": date_utils.timestamp_second(),
        "tradeAmount": 120.00,
    }
]

members = [
    {
        "userId": "1",
        "nickname": "大江东去",
        "joinedTime": "1554027530",
        "name": "胡小虎",
        "mobilePhone": "18825283721",
        "birthday": "1990-01-01",
        "sex": "男",
    }
]

"""
  获取最新会员列表
    请求方式:
       GET /merchant/new_member/list
    Args:
        merchant_service_pb2.QueryMerchantNewMembersRequest
    Returns:
        merchant_service_pb2.QueryMerchantNewMembersResponse

  获取订单列表
    请求方式:
       GET /merchant/order/list
    Args:
        merchant_service_pb2.QueryMerchantOrdersRequest
    Returns:
        merchant_service_pb2.QueryMerchantOrdersResponse

  查询趋势分析请求
    请求方式:
       GET /merchant/trend_analysis
    Args:
        merchant_service_pb2.QueryMerchantTrendAnalysisRequest
    Returns:
        merchant_service_pb2.QueryMerchantTrendAnalysisResponse
"""


@merchant.route('/merchant/<string:merchant_id>/trend_analysis', methods=['OPTIONS', 'GET'])
def query_merchant_trend_analysis(merchant_id):
    """ 查询趋势分析请求
    请求方式:
       GET /merchant/trend_analysis
    Args:
        merchant_id
        # merchant_service_pb2.QueryMerchantTrendAnalysisRequest
    Returns:
        merchant_service_pb2.QueryMerchantTrendAnalysisResponse
    :return:
    """
    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchantTrendAnalysisRequest(),
    #                                       ignore_unknown_fields=True)
    # 商户ID
    # merchant_id = query_request.merchant_id
    # 起始日期，比如
    # date_start = request.values.get('date_start')
    # # 结束日期，比如
    # date_end = request.values.get('date_end')

    response = merchant_service_pb.QueryMerchantTrendAnalysisResponse()

    response.errcode = 0
    response.errmsg = "success"

    # 响应趋势分析内容
    # if not merchant_id == "1":
    #     newMembersMetrics = []
    #     visitsMetrics = []
    #     usedCouponsMetrics = []
    #     orderAmountsMetrics = []

    # 新会员数
    for metrics in newMembersMetrics:
        new_members_metrics = response.new_members_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 访问次数
    for metrics in visitsMetrics:
        new_members_metrics = response.visits_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 优惠券核销数
    for metrics in usedCouponsMetrics:
        new_members_metrics = response.used_coupons_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    # 订单金额
    for metrics in orderAmountsMetrics:
        new_members_metrics = response.order_amounts_metrics.add()
        new_members_metrics.label = metrics[0]
        new_members_metrics.value = metrics[1]

    resp = json_format.MessageToDict(response,
                                     including_default_value_fields=True,
                                     use_integers_for_enums=True)
    resp = jsonify(resp)
    return resp


@merchant.route("/merchant/<string:merchant_id>", methods=["GET"])
def merchant_info(merchant_id):
    """ 获取商户信息
    """
    logger.info(f"获取商户信息的接口: {merchant_id}")
    # 这里做个V2的兼容
    ids = merchant_id.split("_")
    if len(ids) == 2 and ids[1] == "V2":
        #去查绑定的映射关系,V2传的是二维码的值
        bindings = CodePlateHelper().get_binings(qrcode_id=ids[0])
        if bindings is None:
            return jsonify(error_responses())
        if bindings and len(bindings) > 0 and bindings[0].merchant_id != '':
            merchant_id = bindings[0].merchant_id
        else:
            return jsonify(create_responses_obj(error_codes.FAIL ,"二维码错误或未绑定商家"))
    user_id = requests_utils.get_headers_info(request, "userId")
    store = DiningEventViewObjectHelper().get_store(merchant_id, user_id)
    if not store:
        return jsonify_response()
    logger.info(f"获取商户信息: {user_id} {merchant_id} {store.merchant_id}")
    store = json_format.MessageToDict(store, including_default_value_fields=True)
    # 如果用户没有门店的会员卡,就为它创建一张
    brand_id = MemberCardManager().issue_brand_dish_coupon_if_not_brand_member(
        user_id, merchant_id)
    MemberCardManager().create_member_card_for_user_if_not_member(
        user_id, merchant_id, brand_id=brand_id)
    return jsonify_response(data=store)


@merchant.route("/merchant/coupon_package_v2/<string:merchant_id>", methods=["GET"])
def merchant_coupon_package_v2(merchant_id):
    coupon_package_manager = CouponPackageManager(merchant_id=merchant_id)
    merchant_coupon_package_vo = coupon_package_manager.get_merchant_coupon_packages()
    if not merchant_coupon_package_vo:
        return jsonify_response()
    merchant_coupon_package = json_format.MessageToDict(
        merchant_coupon_package_vo, including_default_value_fields=True)
    return jsonify_response(data=merchant_coupon_package)


@merchant.route('/merchant/coupon_package/<string:merchant_id>', methods=['GET'])
def merchant_coupon_package(merchant_id):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    coupon_packages = merchant.preferences.coupon_config.coupon_packages
    ret = {}
    bill_fee_ladder = []
    if len(coupon_packages) > 0:
        for package in coupon_packages:
            if package.state != coupon_category_pb.CouponCategory.ACTIVE:
                continue
            number = package.coupon_package_spec.coupon_count
            coupons = [{'reduceCost': package.coupon_package_spec.reduce_cost}] * number
            total_price = package.coupon_package_spec.reduce_cost * number
            data = {
                'id': package.id,
                'name': package.name,  # package.name,
                'originalPrice': package.coupon_package_spec.total_value,
                'price': package.coupon_package_spec.sell_price,
                'coupons': coupons,
                'number': number,
                'totalPrice': total_price,
                'validDays': package.coupon_package_spec.coupon_category_spec.date_info.fixed_term
            }
            ret.update({
                package.coupon_package_spec.least_cost: data
            })
            bill_fee_ladder.append(package.coupon_package_spec.least_cost)
        bill_fee_ladder.sort()
        return jsonify_response({'couponPackages': ret, 'billFeeLadder': bill_fee_ladder})
    else:
        data = {
            "billFeeLadder": [1500, 3000, 5000],
            "couponPackages": {},
        }
        return jsonify_response(data)
    return jsonify_response({})


@merchant.route("/brands", methods=["GET"])
def get_brands(self):
    """ 返回所有品牌信息
    """
    merchant_da = MerchantDataAccessHelper()
    brands = merchant_da.get_brands()
    brands = [json_format.MessageToDict(brand, including_default_value_fields=True)
              for brand in brands]
    return jsonify_response(brands)


@merchant.route("/brand", methods=["POST"])
def add_brand(self):
    """ 创建品牌
    """
    merchant_da = MerchantDataAccessHelper()
    name = request.json.get("name", None)
    id = request.json.get("id", None)
    brand = merchant_pb.BrandInfo()
    if id is None:
        brand.id = id_manager.generate_common_id()
    else:
        brand.id = id
    brand.name = name
    merchant_da.add_or_update_brand(brand)
    return jsonify_response()

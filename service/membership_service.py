from flask import Blueprint
from flask import request
from google.protobuf import json_format

from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
import proto.membership_pb2 as membership_pb
import proto.ordering.keruyun.keruyun_member_card_balance_pb2 as keruyun_member_card_balance_pb
from business_ops.member_card_manager import MemberCardManager
from business_ops.shilai_pos_member_manager import ShilaiPosMemberManager
from common.utils import requests_utils
from common.utils import id_manager
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service.base_responses import jsonify_response
from view_ops import membership_view_helper

membership = Blueprint("membership", __name__, url_prefix="/membership")


# @membership.before_request
# def check_permission():
#     """检查访问权限

#     此模块提供用户自己领取的会员卡服务，只允许登录的有效用户请求
#     """
#     user_id = requests_utils.get_headers_info(request, "userId")
#     if user_id:
#         user = UserDataAccessHelper().get_user(user_id)
#         setattr(request, "user", user)
#         return None
#     raise errors.PermissionError()


@membership.route("/list", methods=["GET"])
def get_list():
    """获取用户会员卡列表"""
    user = request.user
    page = request.args.get("page", None)
    size = request.args.get("size", None)
    member_cards = membership_view_helper.get_member_cards(user, page=page, size=size)
    member_cards = [json_format.MessageToDict(member_card, including_default_value_fields=True) for member_card in member_cards]
    return jsonify_response(member_cards)


@membership.route("/member_card_deposit_config/<string:merchant_id>", methods=["GET"])
@membership.route("/recharge_config/<string:merchant_id>", methods=["GET"])
def get_recharge_config(merchant_id):
    """获取会员充值列表"""
    manager = ShilaiPosMemberManager(merchant_id=merchant_id)
    ret = manager.get_merchant_member_recharge_config_list()
    if not ret.flag:
        membership_da = MembershipDataAccessHelper()
        status = membership_pb.MemberCardRechargeConfig.ACTIVE
        configs = membership_da.get_member_card_recharge_configs(merchant_id, status=status)
    else:
        configs = ret.data
    configs.sort(key=lambda config: config.sell_price)
    configs = [json_format.MessageToDict(config, including_default_value_fields=True) for config in configs]
    return jsonify_response(configs)


@membership.route("/member_card_deposit_config/<string:merchant_id>", methods=["POST"])
@membership.route("/recharge_config/<string:merchant_id>", methods=["POST"])
def set_recharge_config(merchant_id):
    """设置会员卡充值配置"""
    giving = request.json.get("giving")
    sell_price = request.json.get("sellPrice")
    unit = request.json.get("unit", "元")
    if unit == "元":
        giving = giving * 100
        sell_price = sell_price * 100
    amount = giving + sell_price
    config = membership_pb.MemberCardRechargeConfig()
    config.merchant_id = merchant_id
    config.id = id_manager.generate_common_id()
    config.amount = amount
    config.sell_price = sell_price
    MembershipDataAccessHelper().set_member_card_recharge_config(config)
    return jsonify_response()


@membership.route("/user_member_card_deposit/<string:merchant_id>", methods=["GET"])
@membership.route("/get_user_member_card_deposit/<string:merchant_id>", methods=["GET"])
def get_user_member_card_deposit(merchant_id):
    """用户会员卡余额
    1. 如果商户是时来pos机商户,并且用户已授权手机号,那么会从pos端获取到储值余额
    2. 如果1)不成立,则从小程序端数据库获取余额
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if user_id is None:
        return jsonify_response({"balance": 0})
    manager = ShilaiPosMemberManager(merchant_id=merchant_id)
    user_da = UserDataAccessHelper()
    user = user_da.get_user(user_id)
    ret = manager.get_member_card_balance(user)
    if ret.flag:
        balance = ret.data.get("balance")
        return jsonify_response({"balance": balance})
    balance = MemberCardManager().get_user_member_card_balance(user_id, merchant_id)
    return jsonify_response({"balance": balance})


@membership.route("/balance/<string:phone>", methods=["GET"])
def get_membership_balance_by_phone(phone):
    user_da = UserDataAccessHelper()
    membership_da = MembershipDataAccessHelper()
    merchant_da = MerchantDataAccessHelper()
    user = user_da.get_user_by_condition(phone=phone)
    import_balance = membership_da.get_keruyun_member_card_balance(phone)
    result = {}
    ret = []
    if user:
        result.update({"是否授权": True})
    else:
        result.update({"是否授权": False})
    if user:
        member_cards = membership_da.get_member_cards_for_user(user.id)
        for member_card in member_cards:
            merchant_id = member_card.merchant_id
            merchant = merchant_da.get_merchant_by_id(merchant_id)
            if not merchant:
                continue
            balance = 0
            try:
                manager = ShilaiPosMemberManager(merchant_id=merchant.id)
                api_result = manager.get_member_card_balance_by_phone(phone)
                balance = api_result.data.get("balance") or 0
            except:
                pass
            if member_card.balance + balance == 0:
                continue
            ret.append({"商户": merchant.basic_info.name, "余额(元)": float((member_card.balance + balance) / 100.0)})
    if import_balance and import_balance.balance > 0:
        merchant = merchant_da.get_merchant_by_id(import_balance.merchant_id)
        synced = import_balance.status == keruyun_member_card_balance_pb.KeruyunMemberCardBalance.MERGED
        ret.append({"商户": merchant.basic_info.name, "导入金额(元)": float(import_balance.balance / 100.0), "已同步": synced})
    result.update({"balance": ret})
    return jsonify_response(result)


@membership.route("/cards/<string:phone>", methods=["GET"])
def get_membership_cards_by_phone(phone):
    user_id = requests_utils.get_headers_info(request, "userId")
    membership_da = MembershipDataAccessHelper()
    merchant_da = MerchantDataAccessHelper()
    ordering_da = OrderingServiceDataAccessHelper()
    ret = []
    if user_id:
        member_cards = membership_da.get_member_cards_for_user(user_id)
        for member_card in member_cards:
            merchant_id = member_card.merchant_id
            registration_info = ordering_da.get_registration_info(merchant_id)
            if not registration_info:
                continue
            if not registration_info.ordering_config.enable_show_membership:
                continue
            merchant = merchant_da.get_merchant_by_id(merchant_id)
            if not merchant:
                continue
            balance = 0
            try:
                manager = ShilaiPosMemberManager(merchant_id=merchant.id)
                api_result = manager.get_member_card_balance_by_phone(phone)
                balance = api_result.data.get("balance") or 0
            except:
                pass
            if member_card.balance + balance == 0:
                continue
            ret.append(
                {
                    "merchantId": merchant_id,
                    "merchanName": merchant.basic_info.name,
                    "balance": member_card.balance + balance,
                    "logoUrl": merchant.basic_info.logo_url,
                }
            )
    return jsonify_response(ret)


@membership.route("/sync-merchant-member-card-balance-to-pos", methods=["POST"])
def sync_merchant_member_card_balance_to_pos():
    merchant_id = request.json.get("merchantId", None)
    user_id = request.json.get("userId", None)
    manager = MemberCardManager()
    manager.sync_merchant_member_card_balance(merchant_id, user_id)
    return jsonify_response()


@membership.route("/sync-user-member-card-balance-to-pos", methods=["POST"])
def sync_user_member_card_balance_to_pos():
    user_id = request.json.get("userId", None)
    manager = MemberCardManager()
    manager.sync_user_member_card_balance(user_id)
    return jsonify_response()


@membership.route("/member_card_pay_order_fee/<string:merchant_id>", methods=["POST"])
@membership.route("/get_member_card_pay_order_fee/<string:merchant_id>", methods=["POST"])
def get_member_card_pay_order_fee(merchant_id):
    """如果用户用会员卡支付应该要支付的金额"""
    fee = request.json.get("fee", 0)
    user_id = requests_utils.get_headers_info(request, "userId")
    if user_id is None:
        return jsonify_response({"fee": 0})
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    membership_da = MembershipDataAccessHelper()
    member_card = membership_da.get_member_card(user_id=user_id, brand_id=merchant.brand_info.id)
    if not member_card:
        member_card = MembershipDataAccessHelper().get_member_card(user_id=user_id, merchant_id=merchant_id)
    if member_card:
        fee = int(fee * (100 - member_card.discount) / (float(100)) + 0.5)
    return jsonify_response({"fee": fee})

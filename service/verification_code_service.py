# -*- coding: utf-8 -*-

"""
Filename: verification_code_service.py
Date: 2020-06-09 13:23:32
Title: 核销码相关
"""

from google.protobuf import json_format

from flask import Blueprint
from flask import request
from business_ops.verification_code_manager import VerificationCodeManager
from business_ops.dish_verification_code_manager import DishVerificationCodeManager
from business_ops.brand_dish_verification_code_manager import BrandDishVerificationCodeManager
from common.utils import requests_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from service.base_responses import jsonify_response

_verification_code = Blueprint(__name__, __name__, url_prefix="/verification_code")


@_verification_code.route("/generate", methods=["POST"])
def generate_verification_code():
    verification_code_strategy_id = request.json.get("verificationCodeStrategyId")
    brand_id = request.json.get("brandId")
    nums = request.json.get("nums")
    VerificationCodeManager(brand_id=brand_id).generate_verification_code(verification_code_strategy_id, nums=nums)
    return "success"


@_verification_code.route("/verify_verification_code/<string:merchant_id>", methods=["POST"])
def verify_verification_code(merchant_id):
    msg = request.json.get("msg")
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = VerificationCodeManager(merchant_id=merchant_id, user_id=user_id)
    manager.verify_verification_code(msg)
    return jsonify_response()


@_verification_code.route("/verify", methods=["POST"])
@_verification_code.route("/verify/<string:merchant_id>", methods=["POST"])
def verify_dish_verification_code(merchant_id=None):
    msg = request.json.get("msg").strip()
    user_id = requests_utils.get_headers_info(request, "userId")
    type = request.json.get("type", "BRAND_DISH_VERIFICATION_CODE")
    if type == "DISH_VERIFICATION_CODE":
        manager = DishVerificationCodeManager(merchant_id=merchant_id, user_id=user_id)
        coupon_info, dish_info  = manager.verify(msg)
        coupon_info = json_format.MessageToDict(coupon_info, including_default_value_fields=True)
        dish_info = json_format.MessageToDict(dish_info, including_default_value_fields=True)
        return jsonify_response({"couponInfo": coupon_info, "dishInfo": dish_info})
    elif type == "BRAND_DISH_VERIFICATION_CODE":
        manager = BrandDishVerificationCodeManager(user_id=user_id, merchant_id=merchant_id)
        coupon_info, dish_info  = manager.verify(msg)
        coupon_info = json_format.MessageToDict(coupon_info, including_default_value_fields=True)
        if dish_info:
            dish_info = json_format.MessageToDict(dish_info, including_default_value_fields=True)
        return jsonify_response({"couponInfo": coupon_info, "dishInfo": dish_info})
    return jsonify_response()


@_verification_code.route("/get_user_new_member_brand_dish_coupon/<string:merchant_id>", methods=["GET"])
def get_user_brand_dish_coupon(merchant_id):
    """ 获取用户品牌菜品券
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    info = BrandDishVerificationCodeManager(user_id=user_id, merchant_id=merchant_id).get_user_new_member_brand_dish_coupon(user_id, merchant_id)
    if info:
        dish_vo = json_format.MessageToDict(info.dish_vo, including_default_value_fields=True)
        coupon_info = json_format.MessageToDict(info.coupon_info, including_default_value_fields=True)
        return jsonify_response({"dish": dish_vo, "couponInfo": coupon_info})
    return jsonify_response()

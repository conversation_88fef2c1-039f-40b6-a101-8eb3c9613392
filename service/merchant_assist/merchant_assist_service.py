# -*- coding: utf-8 -*-

import logging
import time
from io import BytesIO
from collections import defaultdict

from flask import Blueprint
from flask import request

from google.protobuf import json_format
from openpyxl import load_workbook

from business_ops.user_manager import UserManager
from dao.base_helper import BaseHelper
import proto.staff_pb2 as staff_pb
from business_ops.merchant_manager import MerchantManager
from business_ops.ordering.attr_manager import AttrManager
from business_ops.ordering.supply_condiment_manager import SupplyCondimentManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.dish_category_manager import DishCategoryManager
from business_ops.import_dish_manager import ImportDishManager
from business_ops.merchant_assist_manager import MerchantAssistManager
from business_ops.ordering.table_manager import TableManager
from common.utils import date_utils as date_utils
from common.utils import requests_utils
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from service.base_responses import create_responses_obj
from service import error_codes
from service.auth_service_helper import User<PERSON>uthHelper
from view_ops.merchant_assist.stats_view_info import StatsViewInfo
from view_ops.merchant_assist.transaction_view_info import TransactionViewObj
from view_ops.merchant_assist.merchant_info import MerchantInfo
from business_ops.ordering.dish_sale_time_manager import DishSaleTimeManager
from service.base_responses import jsonify_response
from view_ops.merchant_assist.membership import Membership
from service import errors
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from business_ops.staff_manager import StaffManager
from common.request import check_body
from dao.dao_helper import DaoORMHelper
from dao import constants
from proto.finance import wallet_pb2 as wallet_pb
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common.protobuf_transformer import protobuf_to_dict
from dao.merchant_da_helper import MerchantDataAccessHelper
from service.auth_service_helper import UserAuthHelper
from common.config import config


logger = logging.getLogger(__name__)
merchant_assist = Blueprint("merchant_assist", __name__, url_prefix="/merchant_assist")


def user_authorize(merchant_id, user_id):
    """商家助手用户权限检查
    1. 要么是商家授权用户
    2. 要么是业务助手的管理员
    """
    if config.deployment_env != 'prod':
        return True
    if user_id is None:
        raise errors.ShowError("请先登陆")
    mda = MerchantUserDataAccessHelper()
    user = mda.get_user(user_id=user_id)
    if not user:
        raise errors.ShowError("请先登陆")
    if merchant_id is not None:
        for m in user.merchants:
            if m.merchant_id == merchant_id:
                return True
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff_by_union_id(user.wechat_profile.unionid)
    if not staff:
        raise errors.ShowError("非时来员工")
    if not staff.is_certified:
        raise errors.ShowError("非授权用户")
    staff_manager = StaffManager()
    role_name = staff_pb.ShilaiStaff.Role.Name(staff.role)
    if role_name not in staff_manager.get_all_merchants_roles():
        raise errors.ShowError("非管理员")


@merchant_assist.route("/user/login", methods=["POST"])
def login():
    username = request.json.get("username")
    password = request.json.get("password")
    test_user_id = "ad7cd36e-f1b7-4d20-83f2-5115b2267376"
    merchant_id = "30730d2c7fd84bd7a42695209033eb49"

    # 线上环境打开注释用于微信小程序审核
    # test_user_id = "6090f212-e5ff-4a54-a499-2165ed0e9fd2"
    # merchant_id = "********************************"
    # user = {"id": test_user_id, 'authorized': True, "role": "SUPER_ADMIN", "merchantId": merchant_id, "merchantList": [merchant_id]}

    if username == "test" and password == "abcdefgtest":
        user = {
            "id": test_user_id,
            'authorized': True,
            "role": "SUPPER_ADMIN",
            "merchantId": merchant_id,
            "merchantList": [merchant_id],
        }
        return jsonify_response(user)
    if username == "malong" and password == "malong":
        merchant_id = "8fef9fc9336f4a17b660b2a1e4488641"
        user_id = "6090f212-e5ff-4a54-a499-1335ed0e9133"
        user = {"id": user_id, 'authorized': True, "role": "ADMIN", "merchantId": merchant_id, "merchantList": [merchant_id]}
        return jsonify_response(user)
    if username == "13980124567" and password == "6dfDE6#":
        merchant_id = "98f047913f2f4cfab7d1e0e9748854a9"
        user_id = "6090f212-e5ff-4a54-aaaa-1335ed0e9133"
        user = {"id": user_id, 'authorized': True, "role": "ADMIN", "merchantId": merchant_id, "merchantList": [merchant_id]}
        return jsonify_response(user)
    if username == "15159102821" and password == "666888":
        merchant_id = "00c794c29e3d4ad2bae5407719597a6b"
        user_id = "7090f212-e5ff-4a54-a499-1335ed0e9133"
        user = {"id": user_id, 'authorized': True, "role": "ADMIN", "merchantId": merchant_id, "merchantList": [merchant_id]}
        return jsonify_response(user)
    user = {"id": test_user_id, 'authorized': False, "role": "None"}
    return jsonify_response(user)


@merchant_assist.route('/user/sign_up', methods=['OPTIONS', 'POST'])
def user_sign_up():
    """
    微信授权登录流程
    请求主体 { code, encryptedData, iv }
    请求头 { fromPlatform }
    流程: 使用 code 获取 session_key, 用 session_key, iv, 小程序appid 解密 encryptData，获得用户基本信息和 unionid
    """
    from_platform = requests_utils.get_platform(request)
    request_json = request.json
    scene_id = request.json.get("sceneId", None)
    code = requests_utils.get_value_from_json(request_json, 'code')
    encrypted_data = requests_utils.get_value_from_json(request_json, 'encryptedData')
    iv = requests_utils.get_value_from_json(request_json, 'iv')
    join_method = requests_utils.get_value_from_json(request_json, 'sceneCode')

    inviter_id = request.json.get("inviterId")
    merchant_id = request.json.get("merchantId")
    logger.info("{}, {}, {}, {}, {}".format(from_platform, code, encrypted_data, iv, join_method))

    _wx_user_auth_helper = UserAuthHelper()
    user = _wx_user_auth_helper.user_auth_v2(from_platform, code, encrypted_data, iv, join_method)

    # 当前登陆的用户
    # user = UserAuthHelper().user_auth(from_platform, code, encrypted_data, iv, join_method)
    if user:
        merchant_manager = MerchantManager()
        if inviter_id:
            merchant_manager.inviter_user_as_manager(inviter_id, user.id, merchant_id)
        merchant_manager.scan_code_become_manager(scene_id, user.id, merchant_id)
        user = MerchantUserDataAccessHelper().get_user(user.id)
        ret = {'id': user.id, "authorized": False, "role": "None"}
        ret = merchant_manager.get_merchant_list_for_merchant_user(user, ret)
        logger.info("merchant assist sign up: {}".format(ret))
        return jsonify_response(ret)
    else:
        error_response = create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
        return jsonify_response(status_response=error_response)


@merchant_assist.route("/transaction_info/<string:merchant_id>", methods=["GET"])
def transaction_info(merchant_id):
    """
    废弃
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    start_date = request.args.get("startDate", None)
    end_date = request.args.get("endDate", None)
    type = request.args.get("type", "TODAY")
    view_obj = TransactionViewObj(merchant_id, user_id)
    transaction_vo = view_obj.get_transactions(start_date, end_date, type)
    if view_obj.has_pos_service:
        transaction_vo = view_obj.wrap_transaction_info(transaction_vo, start_date, end_date, type)
    return jsonify_response(transaction_vo)


# 旧的接口暂时先保留，如果新的接口有问题，可快速切换
@merchant_assist.route("/v1/sale_summary/<string:merchant_id>", methods=["GET"])
def sale_summary_v1(merchant_id):
    """
    latest
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    start_date = request.args.get("startDate", None)
    end_date = request.args.get("endDate", None)
    start_time = request.args.get("startTime", None)
    end_time = request.args.get("endTime", None)
    date_type = request.args.get("type") or "SELF_DEFINE"
    export_email = request.args.get("exportTo")  # email
    view_obj = TransactionViewObj(merchant_id, user_id)
    transaction_vo = view_obj.get_summary_by_date_range(start_date, end_date, date_type, start_time, end_time)
    if view_obj.has_pos_service:
        transaction_vo = view_obj.merge_pos_transaction_amount(
            transaction_vo, start_date, end_date, date_type, start_time, end_time
        )
    else:
        # convert pay method
        method2fee = defaultdict(int)
        method2num = defaultdict(int)
        for item in transaction_vo['transactionsByPayMethod']:
            item['payMethod'] = 'SHILAI_COMBINE' if item['payMethod'] == 'SHILAI_FANPIAO' else item['payMethod']
            method2fee[item['payMethod']] += item['totalPaidFeeAmount']
            method2num[item['payMethod']] += item['totalPayment']
        transaction_vo["transactionsByPayMethod"] = view_obj._group_transaction_by_pay_method_v2(method2fee, method2num)
    transaction_vo = view_obj.convert_discount_to_minus(transaction_vo)
    if export_email:
        view_obj.export_sale_summary_and_email(start_time, end_time, transaction_vo, export_email)
        transaction_vo['exportTo'] = export_email
    return jsonify_response(transaction_vo)


@merchant_assist.route("/sale_summary/<string:merchant_id>", methods=["GET", "POST"])
def sale_summary(merchant_id):
    """
    latest
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if request.method == 'GET':
        start_time = request.args.get("startTime", None)
        end_time = request.args.get("endTime", None)
        export_email = request.args.get("exportTo")  # email
        merchantIds = request.args.get('merchantIds', merchant_id).split(',')
    else:
        start_time = request.json.get("startTime", None)
        end_time = request.json.get("endTime", None)
        export_email = request.json.get("exportTo")  # email
        merchantIds = request.json.get('merchantIds', merchant_id).split(',')

    merchant_id = merchantIds[0]
    view_obj = TransactionViewObj(merchant_id, user_id)
    manager = MerchantAssistManager(merchant_id=merchant_id, start_timestamp=start_time, end_timestamp=end_time)
    transaction_vo = manager.sale_summary(merchantIds)
    if export_email:
        view_obj.export_sale_summary_and_email(start_time, end_time, transaction_vo, export_email)
        transaction_vo['exportTo'] = export_email
        return jsonify_response(transaction_vo)
    return jsonify_response(transaction_vo)


@merchant_assist.route("/stats_info", methods=["POST"])
def get_stats():
    body = check_body(
        {
            "user_id": {"type": str, "required": True},
            "merchant_id": {"type": str, "required": True},
            "start_time": {"type": int, "required": True},
            "end_time": {"type": int, "required": True},
            "type": {"type": str, "required": True, "in": ["marketing", "membership"]},
        }
    )
    _type = body.type
    if _type == "marketing":
        marketing_stats = StatsViewInfo(merchant_id=body.merchant_id, user_id=body.user_id)
        marketing_transaction_vo = marketing_stats.get_merchant_marketing_stats(
            None, None, body.start_time, body.end_time, 'SELF_DEFINE'
        )
        return marketing_stats.to_json(marketing_transaction_vo)
    if _type == "membership":
        member_stats = Membership(merchant_id=body.merchant_id, user_id=body.user_id)
        membership_list_vo = member_stats.get_membership_list(
            None, None, None, body.start_time, body.end_time, type="SELF_DEFINE", size=None
        )
        return member_stats.to_json(membership_list_vo)


@merchant_assist.route("/order_info/<string:merchant_id>", methods=["GET"])
def order_info(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    start_date = request.args.get("startDate", None)
    end_date = request.args.get("endDate", None)
    type = request.args.get("type", "TODAY")
    meal_type = request.args.get("mealType", "EAT_IN")
    order_vo = TransactionViewObj(merchant_id, user_id).get_orders(start_date, end_date, type, meal_type)
    order_vo = json_format.MessageToDict(order_vo, including_default_value_fields=True)
    return jsonify_response(order_vo)


@merchant_assist.route("/transaction_list_by_type/<string:merchant_id>", methods=["GET"])
def transaction_list_by_type(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    tpe = request.args.get("type", None)
    if tpe is None:
        tpe = "SELF_DINING_PAYMENT"
    last_paid_time = request.args.get('lastPaidTime', None)
    if last_paid_time is None or last_paid_time == "":
        last_paid_time = date_utils.timestamp_second()

    transaction_list_vo = TransactionViewObj(merchant_id, user_id).get_transaction_list_by_type(
        last_paid_time=last_paid_time, type=tpe
    )
    transaction_list_vo = json_format.MessageToDict(transaction_list_vo, including_default_value_fields=True)

    return jsonify_response(transaction_list_vo)


@merchant_assist.route("/transaction_list/<string:merchant_id>", methods=["GET"])
def get_transaction_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    last_paid_time = request.args.get('lastPaidTime', None)
    if last_paid_time is None or last_paid_time == "":
        last_paid_time = date_utils.timestamp_second()
    view_obj = TransactionViewObj(merchant_id, user_id)
    if view_obj.has_pos_service:
        transaction_list_vo = view_obj.get_whole_order_list(last_paid_time=last_paid_time)
    else:
        transaction_list_vo = view_obj.get_transaction_list(last_paid_time=last_paid_time)
    transaction_list_vo = json_format.MessageToDict(transaction_list_vo, including_default_value_fields=True)
    return jsonify_response(transaction_list_vo.get("transactions"))


@merchant_assist.route("/get_order_list/<string:merchant_id>", methods=["GET"])
def get_order_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    meal_type = request.args.get('mealType', None)
    last_paid_time = request.args.get("lastPaidTime", None)
    if last_paid_time is None:
        last_paid_time = date_utils.timestamp_second()
    order_list = TransactionViewObj(merchant_id, user_id).get_order_list(meal_type=meal_type, last_paid_time=last_paid_time)
    order_list = [json_format.MessageToDict(order, including_default_value_fields=True) for order in order_list]
    return jsonify_response(order_list)


@merchant_assist.route("/get_recharge_list/<string:merchant_id>", methods=["GET"])
def get_recharge_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    last_paid_time = request.args.get("lastPaidTime", None)
    if last_paid_time is None:
        last_paid_time = int(time.time())
    recharge_list = TransactionViewObj(merchant_id, user_id).get_member_card_recharge_list(last_paid_time=last_paid_time)
    recharge_list = [
        json_format.MessageToDict(recharge, including_default_value_fields=True) for recharge in recharge_list.transactions
    ]
    return jsonify_response(recharge_list)


@merchant_assist.route("/export_order_data/<string:merchant_id>", methods=["GET"])
def export_order_data(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    start_paid_time = request.args.get("startPaidTime", None)
    end_paid_time = request.args.get("endPaidTime", None)
    if end_paid_time is None:
        end_paid_time = date_utils.timestamp_second()

    result = MerchantInfo(merchant_id=merchant_id, user_id=user_id).export_order_data(start_paid_time, end_paid_time)
    return jsonify_response(result)


@merchant_assist.route("/get_order/<string:order_id>", methods=["GET"])
def get_order(order_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    order = TransactionViewObj(None, user_id).get_order(order_id)
    order = json_format.MessageToDict(order, including_default_value_fields=True)
    return jsonify_response(order)


@merchant_assist.route("/merchant_stats/<string:merchant_id>", methods=["GET"])
def get_merchant_stats(merchant_id):
    """
    商务助手获取 营销数字
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    start_date = request.args.get('startDate', None)
    end_date = request.args.get("endDate", None)
    start_time = request.args.get('startTime', None)
    end_time = request.args.get("endTime", None)
    type = request.args.get("type", "TODAY")
    stats = StatsViewInfo(merchant_id, user_id).get_merchant_marketing_stats(
        start_date=start_date, end_date=end_date, start_time=start_time, end_time=end_time, type=type
    )
    result = json_format.MessageToDict(stats, including_default_value_fields=True)
    return jsonify_response(result)


@merchant_assist.route("/merchant_dish_stats/<string:merchant_id>", methods=["GET"])
def get_merchant_dish_stats(merchant_id):
    """
    获取 菜品统计报表
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    start_time = request.args.get('startTime', None)
    end_time = request.args.get("endTime", None)
    result = StatsViewInfo(merchant_id, user_id).get_merchant_dish_stats(start_time=start_time, end_time=end_time)
    return jsonify_response(result)


@merchant_assist.route("/merchant_strategy_stats/<string:merchant_id>", methods=["GET"])
def get_merchant_strategy_stats(merchant_id):
    """
    获取 菜品统计报表
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    start_time = request.args.get('startTime', None)
    end_time = request.args.get("endTime", None)
    result = StatsViewInfo(merchant_id, user_id).get_merchant_strategy_stats(start_time=start_time, end_time=end_time)
    return jsonify_response(result)


@merchant_assist.route("/get_membership_list/<string:merchant_id>", methods=["GET"])
def get_membership_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    last_initial_activate_time = request.args.get('lastInitialActivateTime', None)
    start_date = request.args.get('startDate', None)
    end_date = request.args.get("endDate", None)
    start_time = request.args.get('startTime', None)
    end_time = request.args.get("endTime", None)
    size = int(request.args.get("size", 10))
    type = request.args.get('type', "SELF_DEFINE")
    obj = Membership(merchant_id, user_id)
    membership_list = obj.get_membership_list(
        last_initial_activate_time=last_initial_activate_time,
        start_date=start_date,
        end_date=end_date,
        start_time=start_time,
        end_time=end_time,
        type=type,
        size=size,
    )
    membership_list = json_format.MessageToDict(membership_list, including_default_value_fields=True)
    return jsonify_response(membership_list)


@merchant_assist.route("/get_membership_info/<string:merchant_id>", methods=["GET"])
def get_membership_info(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    start_date = request.args.get('startDate', None)
    end_date = request.args.get("endDate", None)
    start_time = request.args.get('startTime', None)
    end_time = request.args.get("endTime", None)
    type = request.args.get("type", "SELF_DEFINE")
    membership_info = Membership(merchant_id, user_id).get_membership_info(start_date, end_date, start_time, end_time, type)
    membership_info = json_format.MessageToDict(membership_info, including_default_value_fields=True)
    return jsonify_response(membership_info)


@merchant_assist.route("/get_merchant_info/<string:merchant_id>", methods=["GET"])
@merchant_assist.route("/<string:merchant_id>", methods=["GET"])
def get_merchant_info(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_info = MerchantInfo(merchant_id, user_id).get_merchant_info()
    merchant_info = json_format.MessageToDict(merchant_info, including_default_value_fields=True)
    return jsonify_response(merchant_info)


@merchant_assist.route("/dish_list/<string:merchant_id>", methods=["GET"])
@merchant_assist.route("/get_dish_list/<string:merchant_id>", methods=["GET"])
def get_dish_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    status = request.args.get('status', None)
    dish_list = MerchantInfo(merchant_id=merchant_id, user_id=user_id).get_dishes(status)
    dish_list = json_format.MessageToDict(dish_list, including_default_value_fields=True)
    if dish_list.get("categoryAttr") is None:
        dish_list["categoryAttr"] = {}
    return jsonify_response(dish_list)


@merchant_assist.route("/dish_category/<string:merchant_id>", methods=["POST"])
def update_dish_category(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    category_id = request.json.get("categoryId", None)
    status = request.json.get("status", None)
    no_discount = request.json.get("noDiscount", None)
    manager = MerchantInfo(merchant_id=merchant_id, user_id=user_id)
    manager.update_dish_category(category_id=category_id, status=status, no_discount=no_discount)
    return jsonify_response()


@merchant_assist.route("/change_status/dish/<string:merchant_id>", methods=["POST"])
def change_dish_status(merchant_id):
    """菜品沽清"""
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    dish_id = request.json.get("dishId", None)
    if not dish_id:
        return jsonify_response()
    status = request.json.get("status", None)
    if status is None:
        return jsonify_response()
    MerchantInfo(merchant_id=merchant_id, user_id=user_id).change_dish_status(dish_id, status)
    return jsonify_response()


@merchant_assist.route("/attr/<string:merchant_id>", methods=["POST"])
def update_attrs(merchant_id):
    """批量修改菜品属性"""
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    attrs = request.json.get("attrs", None)
    manager = MerchantInfo(merchant_id=merchant_id, user_id=user_id)
    for attr in attrs:
        attr_id = attr.get("attrId")
        group_id = attr.get("groupId")
        status = attr.get("status")
        multi_select = attr.get("multiSelect")
        manager.update_attr(attr_id=attr_id, status=status, group_id=group_id, multi_select=multi_select)
    return jsonify_response()


@merchant_assist.route("/change_status/attr/<string:merchant_id>", methods=["POST"])
def change_attr_status(merchant_id):
    """修改菜品属性状态"""
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    attr_id = request.json.get("attrId", None)
    status = request.json.get("status", None)
    MerchantInfo(merchant_id=merchant_id, user_id=user_id).change_attr_status(attr_id, status)
    return jsonify_response()


# @merchant_assist.route("/supply_condiment/<string:merchant_id>", methods=["POST"])
# def update_supply_condiments(merchant_id):
#     """ 批量修改加料
#     [{
#     "supplyCondimentId": "11111111",
#     "status": "NORMAL",
#     "supplyCondimentUplimit": 2,
#     "dishId": "123"
#     }]
#     """
#     user_id = requests_utils.get_headers_info(request, "userId")
#     supply_condiments = request.json.get("supplyCondiments", None)
#     manager = MerchantInfo(merchant_id=merchant_id, user_id=user_id)
#     for supply_condiment in supply_condiments:
#         dish_id = supply_condiment.get("dishId")
#         supply_condiment_id = supply_condiment.get("supplyCondimentId")
#         supply_condiment_uplimit = supply_condiment.get("supplyCondimentUplimit")
#         status = supply_condiment.get("status")
#         manager.update_supply_condiment(
#             dish_id=dish_id, status=status, supply_condiment_uplimit=supply_condiment_uplimit,
#             supply_condiment_id=supply_condiment_id)
#     return jsonify_response()


@merchant_assist.route("/change_status/supply_condiment/<string:merchant_id>", methods=["POST"])
def change_supply_condiment_status(merchant_id):
    """修改菜品加料状态"""
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    supply_condiment_id = request.json.get("supplyCondimentId", None)
    status = request.json.get("status", None)
    MerchantInfo(merchant_id=merchant_id, user_id=user_id).change_supply_condiment_status(supply_condiment_id, status)
    return jsonify_response()


@merchant_assist.route("/get_employee_list/<string:merchant_id>", methods=["GET"])
def get_employee_list(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = MerchantInfo(merchant_id, user_id)
    user_list = manager.get_merchant_user_list()
    user_list = [json_format.MessageToDict(user, including_default_value_fields=True) for user in user_list]
    return jsonify_response(user_list)


@merchant_assist.route("/remove_employee/<string:merchant_id>", methods=["POST"])
def remove_employee(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    employee_id = request.json.get("employeeId")
    MerchantInfo(merchant_id, user_id).remove_employee(employee_id)
    return jsonify_response()


@merchant_assist.route("/edit_employee/<string:merchant_id>", methods=["POST"])
def edit_employee(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    user_merchant = request.json
    MerchantInfo(merchant_id, user_id).edit_employee(user_merchant)
    return jsonify_response()


@merchant_assist.route("/refund/<string:merchant_id>", methods=["POST"])
def refund(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    platform = requests_utils.get_headers_info(request, "platform")
    if platform != 'APP':
        user_authorize(merchant_id, user_id)
    check_user_id = request.json.get("checkUserId")
    if check_user_id is None:
        check_user_id = user_id
    transaction_id = request.json.get("transactionId")
    refund_print = bool(request.json.get("refundPrint", False))
    back_comment = request.json.get("backComment", "")
    if not refund_print:
        print_helper = PrinterConfigDataAccessHelper()
        print_configs = print_helper.get_printer_configs(merchant_id=merchant_id)
        refund_print = any(
            [config.kitchen_print_format.refund_print for config in print_configs if config.enable_kitchen_print]
        )

    manager = MerchantInfo(merchant_id=merchant_id, user_id=check_user_id)
    if manager._merchant.stores[0].disable_order_refund:
        raise errors.ShowError("该商家暂时不能发起退款,请联系客服")
    check_user = True
    if check_user_id in ["989713c8-8fcc-4bfa-9757-af53cdc603b7", "ad7cd36e-f1b7-4d20-83f2-5115b2267376"] or platform == 'APP':
        check_user = False
    manager.refund(transaction_id, check_user, refund_print=refund_print, back_comment=back_comment)
    return jsonify_response()


@merchant_assist.route("/refund/coupon_package/<string:transaction_id>", methods=["POST"])
def coupon_package_refund(transaction_id):
    # user_id = requests_utils.get_headers_info(request, "userId")
    # check_user_id = request.json.get("checkUserId")
    # if check_user_id is None:
    #     check_user_id = user_id
    # check_user = True
    # if check_user_id in ["989713c8-8fcc-4bfa-9757-af53cdc603b7", "ad7cd36e-f1b7-4d20-83f2-5115b2267376"]:
    #     check_user = False
    # manager = MerchantInfo(user_id=check_user_id)
    # manager.coupon_package_refund(transaction_id, check_user)
    return jsonify_response()


@merchant_assist.route("/refund/fanpiao/<string:transaction_id>", methods=["POST"])
def fanpiao_refund(transaction_id):
    # user_id = requests_utils.get_headers_info(request, "userId")
    # check_user_id = request.json.get("checkUserId")
    # if check_user_id is None:
    #     check_user_id = user_id
    # check_user = True
    # if check_user_id in ["989713c8-8fcc-4bfa-9757-af53cdc603b7", "ad7cd36e-f1b7-4d20-83f2-5115b2267376"]:
    #     check_user = False
    # manager = MerchantInfo(user_id=check_user_id)
    # manager.fanpiao_refund(transaction_id, check_user)
    return jsonify_response()


@merchant_assist.route("/refund/member_card_recharge/<string:transaction_id>", methods=["POST"])
def member_card_recharge_refund(transaction_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(None, user_id)
    check_user_id = request.json.get("checkUserId")
    if check_user_id is None:
        check_user_id = user_id
    check_user = True
    if check_user_id in ["989713c8-8fcc-4bfa-9757-af53cdc603b7", "ad7cd36e-f1b7-4d20-83f2-5115b2267376"]:
        check_user = False
    manager = MerchantInfo(user_id=check_user_id)
    manager.member_card_recharge_refund(transaction_id, check_user)
    return jsonify_response()


# 商户助手接口 v2 ------------------------------------------------


@merchant_assist.route("/dish-category/list/<string:merchant_id>", methods=["GET"])
def get_dish_category_list(merchant_id):
    """菜品分类列表"""
    manager = DishCategoryManager(merchant_id=merchant_id)
    categories = manager.get_dish_category_list()
    categories = json_format.MessageToDict(categories, including_default_value_fields=True)
    return jsonify_response(categories)


@merchant_assist.route("/dish-category/<string:merchant_id>", methods=["POST"])
def update_dish_category_v2(merchant_id):
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    id = request.json.get("id", None)
    name = request.json.get("name", None)
    sort = request.json.get("sort", None)
    no_discount = request.json.get("noDiscount", None)
    status = request.json.get("status", None)
    sns = request.json.get("sns", None)
    required = request.json.get('required', None)
    manager = DishCategoryManager(merchant_id=merchant_id, staff_id=user_id)
    category = manager.update_dish_category(
        id=id, name=name, sort=sort, no_discount=no_discount, status=status, sns=sns, required=required
    )
    category = json_format.MessageToDict(category, including_default_value_fields=True)
    return jsonify_response(category)


@merchant_assist.route("/batch-dish-category/<string:merchant_id>", methods=["POST"])
def batch_update_dish_category_v2(merchant_id):
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    manager = DishCategoryManager(merchant_id=merchant_id, staff_id=user_id)
    for category in request.json.get('categorys', []):
        id = category.get("id", None)
        name = category.get("name", None)
        sort = category.get("sort", None)
        no_discount = category.get("noDiscount", None)
        status = category.get("status", None)
        sns = category.get("sns", None)
        required = category.get('required', None)
        manager.update_dish_category(
            id=id, name=name, sort=sort, no_discount=no_discount, status=status, sns=sns, required=required
        )
    return jsonify_response()


@merchant_assist.route("/dish-category/<string:merchant_id>", methods=["DELETE"])
def delete_dish_category(merchant_id):
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    if user_id not in ["ad7cd36e-f1b7-4d20-83f2-5115b2267376"]:
        return jsonify_response()
    category_id = request.json.get("categoryId", None)
    manager = DishCategoryManager(merchant_id=merchant_id, staff_id=user_id)
    manager.remove_dish_category(category_id)
    return jsonify_response()


@merchant_assist.route("/dish-category/reorder/<string:merchant_id>", methods=["POST"])
def reorder_dish_category(merchant_id):
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    manager = DishCategoryManager(merchant_id=merchant_id, staff_id=user_id)
    category_ids = request.json.get("categoryIds", None)
    manager.reorder_dish_category(category_ids)
    return jsonify_response()


@merchant_assist.route("/dish-category/printer/<string:merchant_id>", methods=["POST"])
def update_dish_category_printer(merchant_id):
    """分类批量绑定打印机"""
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    sns = request.json.get("sns", [])
    manager = DishCategoryManager(merchant_id=merchant_id, staff_id=user_id)
    manager.update_printer_category(sns)
    return jsonify_response()


@merchant_assist.route("/dish-category/printer/<string:merchant_id>", methods=["GET"])
def get_dish_category_printer(merchant_id):
    manager = MerchantAssistManager(merchant_id=merchant_id)
    sns = manager.get_printer_categorys()
    return jsonify_response(sns)


@merchant_assist.route("/dish/list/<string:merchant_id>", methods=["GET"])
def get_dish_list_v2(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    dish_list_vo = manager.merchant_assist_get_dish_list()
    dish_list = json_format.MessageToDict(dish_list_vo, including_default_value_fields=True)
    return jsonify_response(dish_list)


@merchant_assist.route("/for/check-user", methods=["POST"])
def check_user():
    user_id = request.headers.get("userId")
    user = MerchantUserDataAccessHelper().get_user(user_id=user_id)
    if not user:
        raise errors.ShowError("用户不存在")
    # if not user.is_certified:
    #     raise errors.ShowError("用户不存在")
    # user = json_format.MessageToDict(user, including_default_value_fields=True)
    return jsonify_response()


@merchant_assist.route("/for/user-merchants", methods=["POST"])
def user_merchants():
    user_id = request.headers.get("userId")
    user = MerchantUserDataAccessHelper().get_user(user_id=user_id)
    if not user:
        raise errors.ShowError("用户不存在")
    merchant_manager = MerchantManager()
    ret = {'id': user.id, "authorized": False, "role": "None"}
    ret = merchant_manager.get_merchant_list_for_merchant_user(user, ret)
    return jsonify_response(ret)


@merchant_assist.route("/dish/reorder/<string:merchant_id>", methods=["POST"])
def reorder_dishes(merchant_id):
    user_id = request.headers.get("userId")
    user_authorize(merchant_id, user_id)
    manager = DishManager(merchant_id=merchant_id, staff_id=user_id)
    dish_ids = request.json.get("dishIds", None)
    category_id = request.json.get("categoryId", None)
    manager.merchant_assist_reorder_dish(dish_ids=dish_ids, category_id=category_id)
    return jsonify_response()


@merchant_assist.route("/dish/<string:merchant_id>", methods=["GET"])
def get_dish(merchant_id):
    dish_id = request.args.get("dishId")
    user_id = request.headers.get("userId")
    manager = DishManager(merchant_id=merchant_id, staff_id=user_id)
    dish = manager.merchant_assist_get_dish_detail(dish_id=dish_id)
    if dish:
        dish = json_format.MessageToDict(dish, including_default_value_fields=True)
        return jsonify_response(dish)
    return jsonify_response()


@merchant_assist.route("/dish/<string:merchant_id>", methods=["POST"])
def update_dish(merchant_id):
    """
    更新菜品接口
    自动填充sort 字段
    套餐:
    "childDishGroups.sort": {$gt: 0}
    POST /merchant_assist/dish/<merchantId>
    shilaiDishImage: 套餐图片
    name: 套餐名字
    dishId: 空表示新增, 有表示更新
    type: SINGLE or COMBO_MEAL
    categoryId: 分类
    price: 价格
    childDishGroups: list, 子菜分组
        - groupName: 分组名称
        - childDishes: list, 子菜
            - id:
            - price: dish 的 price
            - name: dish 的 name
            - marketPrice: dish 的 marketPrice
            - isMust: 是否必选
            - quantityIncrement: 数量
        - orderMin: 可选最小数量
        - orderMax: 可选最大数量
        - allowDuplicate: 可选重复菜品
        - isFixed: 是否固定分组
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    dish_id = request.json.get("dishId", None)
    status = request.json.get("status", None)
    supply_condiment_uplimit = request.json.get("supplyCondimentUplimit", None)
    shilai_dish_image = request.json.get("shilaiDishImage", None)
    name = request.json.get("name", None)
    dish_type = request.json.get("type", None)
    child_dish_groups = request.json.get("childDishGroups", None)
    category_id = request.json.get("categoryId", None)
    attr_group_ids = request.json.get("attrGroupIds", None)
    attr_ids = request.json.get("attrIds", None)
    supply_condiment_ids = request.json.get("supplyCondimentIds", None)
    selection_type = request.json.get("selectionType", None)
    lower_limit = request.json.get("lowerLimit", None)
    upper_limit = request.json.get("upperLimit", None)
    sale_times = request.json.get("saleTimes", None)
    box_qty = request.json.get("boxQty", None)
    min_order_num = request.json.get("minOrderNum", None)
    max_order_num = request.json.get("maxOrderNum", None)
    sale_time_dish_category = request.json.get("saleTimeDishCategory", None)
    is_discount_dish = request.json.get("isDiscountDish", None)
    price = request.json.get("price", None)
    available_quantity = request.json.get("availableQuantity", None)
    remain_quantity = request.json.get("remainQuantity", None)
    enable_quantity_setting = request.json.get("enableQuantitySetting", None)
    dish_ids = request.json.get("dishIds", None)
    tag = request.json.get("tag", None)
    desc = request.json.get("desc", None)
    supply_condiment_group_name = request.json.get("supplyCondimentGroupName", None)

    if user_id is None:
        raise errors.ShowError("Cannot Do This")

    manager = DishManager(merchant_id=merchant_id, user_id=user_id)
    if dish_ids is None:
        dish_ids = [dish_id]
    for dish_id in dish_ids:
        dish = manager.merchant_assist_create_or_update_dish(
            dish_id=dish_id,
            status=status,
            type=dish_type,
            child_dish_groups=child_dish_groups,
            supply_condiment_uplimit=supply_condiment_uplimit,
            shilai_dish_image=shilai_dish_image,
            category_id=category_id,
            name=name,
            is_discount_dish=is_discount_dish,
            box_qty=box_qty,
            min_order_num=min_order_num,
            max_order_num=max_order_num,
            price=price,
            selection_type=selection_type,
            lower_limit=lower_limit,
            upper_limit=upper_limit,
            sale_times=sale_times,
            sale_time_dish_category=sale_time_dish_category,
            attr_group_ids=attr_group_ids,
            attr_ids=attr_ids,
            supply_condiment_ids=supply_condiment_ids,
            available_quantity=available_quantity,
            remain_quantity=remain_quantity,
            tag=tag,
            enable_quantity_setting=enable_quantity_setting,
            desc=desc,
            supply_condiment_group_name=supply_condiment_group_name,
        )
        manager.remove_recommend_dish(dish)
        manager.bind_dish_printer(dish)
    return jsonify_response()


@merchant_assist.route("/dish/batch_update/<string:merchant_id>", methods=["POST"])
def batch_update_dish(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user_authorize(merchant_id, user_id)
    manager = DishManager(merchant_id=merchant_id, user_id=user_id)

    for dish in request.json.get('dishes'):
        dish_id = dish.get("dishId", None)
        status = dish.get("status", None)
        supply_condiment_uplimit = dish.get("supplyCondimentUplimit", None)
        shilai_dish_image = dish.get("shilaiDishImage", None)
        name = dish.get("name", None)
        dish_type = dish.get("type", None)
        child_dish_groups = dish.get("childDishGroups", None)
        category_id = dish.get("categoryId", None)
        attr_group_ids = dish.get("attrGroupIds", None)
        attr_ids = dish.get("attrIds", None)
        supply_condiment_ids = dish.get("supplyCondimentIds", None)
        selection_type = dish.get("selectionType", None)
        lower_limit = dish.get("lowerLimit", None)
        upper_limit = dish.get("upperLimit", None)
        sale_times = dish.get("saleTimes", None)
        box_qty = dish.get("boxQty", None)
        min_order_num = dish.get("minOrderNum", None)
        max_order_num = dish.get("maxOrderNum", None)
        sale_time_dish_category = dish.get("saleTimeDishCategory", None)
        is_discount_dish = dish.get("isDiscountDish", None)
        price = dish.get("price", None)
        available_quantity = dish.get("availableQuantity", None)
        remain_quantity = dish.get("remainQuantity", None)
        enable_quantity_setting = dish.get("enableQuantitySetting", None)
        tag = dish.get("tag", None)
        attr_type = dish.get('attrType', None)
        manager.merchant_assist_create_or_update_dish(
            dish_id=dish_id,
            status=status,
            type=dish_type,
            child_dish_groups=child_dish_groups,
            supply_condiment_uplimit=supply_condiment_uplimit,
            shilai_dish_image=shilai_dish_image,
            category_id=category_id,
            name=name,
            is_discount_dish=is_discount_dish,
            box_qty=box_qty,
            min_order_num=min_order_num,
            max_order_num=max_order_num,
            price=price,
            selection_type=selection_type,
            lower_limit=lower_limit,
            upper_limit=upper_limit,
            sale_times=sale_times,
            sale_time_dish_category=sale_time_dish_category,
            attr_group_ids=attr_group_ids,
            attr_ids=attr_ids,
            supply_condiment_ids=supply_condiment_ids,
            available_quantity=available_quantity,
            remain_quantity=remain_quantity,
            tag=tag,
            enable_quantity_setting=enable_quantity_setting,
            attr_type=attr_type,
        )
    return jsonify_response()


@merchant_assist.route("/dish/batch/<string:merchant_id>", methods=["POST"])
def batch_import_dish(merchant_id):
    """通过文件批量导入菜品"""
    f = request.files.get("file")
    wb = load_workbook(filename=BytesIO(f.read()))
    obj = ImportDishManager(merchant_id=merchant_id)
    obj.parse_excel(wb)
    return jsonify_response()


@merchant_assist.route("/dish/export/<string:merchant_id>", methods=["GET"])
def export_dish(merchant_id):
    manager = ImportDishManager(merchant_id=merchant_id)
    url = manager.export_dish_xlsx()
    return jsonify_response({"url": url})


@merchant_assist.route("/attr_group/generate/<string:merchant_id>", methods=["POST"])
def generate_attr_groups(merchant_id):
    """把某个商户的所有菜品的属性转换成属性组"""
    manager = AttrManager(merchant_id=merchant_id)
    manager.generate_dishes_attr_groups()
    return jsonify_response()


@merchant_assist.route("/supply_condiment/generate/<string:merchant_id>", methods=["POST"])
def generate_supply_condiments(merchant_id):
    """把某个商户的所有菜品的加料设置到加料数据库"""
    manager = SupplyCondimentManager(merchant_id=merchant_id)
    manager.generate_dishes_supply_condiment()
    return jsonify_response()


@merchant_assist.route("/attr_group/template", methods=["POST"])
@merchant_assist.route("/attr_group/template/<string:merchant_id>", methods=["POST"])
@merchant_assist.route("/attr_group/<string:merchant_id>", methods=["POST"])
def add_attr_group(merchant_id=None):
    if merchant_id is None:
        merchant_id = request.json.get("merchantId", None)
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    group_id = request.json.get("groupId", None)
    group_name = request.json.get("groupName", None)
    attrs = request.json.get("attrs", None)
    attr_group_type = request.json.get("attrGroupType", None)
    is_multi_select = request.json.get("isMultiSelect", None)
    selection_type = request.json.get("selectionType", None)
    attr_status = request.json.get("attrStatus", None)
    manager = AttrManager(merchant_id=merchant_id, staff_id=user_id)
    manager.add_or_update_attr_group(
        group_id=group_id,
        group_name=group_name,
        attrs=attrs,
        is_multi_select=is_multi_select,
        selection_type=selection_type,
        attr_group_type=attr_group_type,
        attr_status=attr_status,
    )
    return jsonify_response()


@merchant_assist.route("/attr_group/templates", methods=["GET"])
@merchant_assist.route("/attr_group/templates/<string:merchant_id>", methods=["GET"])
@merchant_assist.route("/attr_group/<string:merchant_id>", methods=["GET"])
def get_attr_groups(merchant_id=None):
    if merchant_id is None:
        merchant_id = request.args.get("merchantId", None)
    user_id = request.headers.get("userId", None)
    manager = AttrManager(merchant_id=merchant_id, staff_id=user_id)
    attr_groups = manager.get_attr_groups()
    attr_groups = [json_format.MessageToDict(ag, including_default_value_fields=True) for ag in attr_groups]
    da_helper = OrderingServiceDataAccessHelper()
    categorys = da_helper.get_categories(merchant_id=merchant_id, return_proto=False)
    categorys = {x.get('id'): x.get('name') for x in categorys}
    result = []
    for ag in attr_groups:
        if ag.get('status') == "DELETED":
            continue
        for attr in ag.get('attrs', []):
            dishses = OrderingServiceDataAccessHelper().get_dishes(
                merchant_id=merchant_id, attr_id=attr.get('id'), return_proto=False
            )
            attr.update(
                {
                    'dishes': [
                        {
                            "id": x.get('id'),
                            "name": x.get('name'),
                            'category': [categorys.get(y) for y in x.get('categories')],
                        }
                        for x in dishses
                    ]
                }
            )
        result.append(ag)
    return jsonify_response(result)


@merchant_assist.route("/attr_group/reorder/<string:merchant_id>", methods=["POST"])
def reorder_attr_groups(merchant_id):
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    attr_group_ids = request.json.get("attrGroupIds", None)
    manager = AttrManager(merchant_id=merchant_id, staff_id=user_id)
    manager.reorder_attr_groups(attr_group_ids)
    return jsonify_response()


@merchant_assist.route("/attrs/reorder/<string:merchant_id>", methods=["POST"])
def reorder_attrs(merchant_id):
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    attr_ids = request.json.get("attrIds", None)
    group_id = request.json.get("groupId", None)
    manager = AttrManager(merchant_id=merchant_id, staff_id=user_id)
    manager.reorder_attrs(attr_ids, group_id)
    return jsonify_response()


@merchant_assist.route("/supply_condiment/<string:merchant_id>", methods=["POST"])
def add_or_update_supply_condiment(merchant_id):
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    supply_condiment_manager = SupplyCondimentManager(merchant_id=merchant_id)
    id = request.json.get("id", None)
    market_price = request.json.get("marketPrice", None)
    name = request.json.get("name", None)
    status = request.json.get("status", None)
    supply_condiment_manager.add_or_update_supply_condiment(name=name, market_price=market_price, id=id, status=status)
    return jsonify_response()


@merchant_assist.route("/supply_condiment/<string:merchant_id>", methods=["GET"])
def get_supply_condiments(merchant_id):
    supply_condiment_manager = SupplyCondimentManager(merchant_id=merchant_id)
    supply_condiments = supply_condiment_manager.get_supply_condiments()
    supply_condiments = [json_format.MessageToDict(s, including_default_value_fields=True) for s in supply_condiments]
    da_helper = OrderingServiceDataAccessHelper()
    categorys = da_helper.get_categories(merchant_id=merchant_id, return_proto=False)
    categorys = {x.get('id'): x.get('name') for x in categorys}
    result = []
    for sc in supply_condiments:
        if sc.get('status') == "OUT_OF_STOCK":
            continue
        dishses = OrderingServiceDataAccessHelper().get_dishes(
            merchant_id=merchant_id, supply_condiment_id=sc.get('id'), return_proto=False
        )
        sc.update(
            {
                'dishes': [
                    {'id': x.get('id'), "name": x.get('name'), 'category': [categorys.get(y) for y in x.get('categories')]}
                    for x in dishses
                ]
            }
        )
        result.append(sc)
    return jsonify_response(supply_condiments)


@merchant_assist.route("/supply_condiment/reorder/<string:merchant_id>", methods=["POST"])
def reorder_supply_condiments(merchant_id):
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    supply_condiment_ids = request.json.get("supplyCondimentIds", None)
    manager = SupplyCondimentManager(merchant_id=merchant_id, staff_id=user_id)
    manager.reorder_supply_condiments(supply_condiment_ids)
    return jsonify_response()


@merchant_assist.route("/dish_sale_time/<string:merchant_id>", methods=["POST"])
def add_or_update_merchant_dish_sale_time(merchant_id):
    """
    POST /merchant_assist/dish_sale_time/{merchantId}
    Args:
        id:
        name:
        dishIds: list of 菜品ID
        status: ACTIVE, INACTIVE,
        startDate: string of timestamp,
        endDate: string of timestamp,
        weekdays: list of values
                  WEEKDAY_NONE = 0;
                  WEEKDAY_MONDAY = 1;
                  WEEKDAY_TUESDAY = 2;
                  WEEKDAY_WEDNESDAY = 3;
                  WEEKDAY_THURSDAY = 4;
                  WEEKDAY_FRIDAY = 5;
                  WEEKDAY_SATURDAY = 6;
                  WEEKDAY_SUNDAY = 7;
        periods: [ {start: '00:00', end: '23:59'}]
    """
    user_id = request.headers.get("userId", None)
    user_authorize(merchant_id, user_id)
    id = request.json.get("id")
    name = request.json.get("name")
    dish_ids = request.json.get("dishIds")
    status = request.json.get("status")
    start_date = request.json.get("startDate")
    end_date = request.json.get("endDate")
    weekdays = request.json.get("weekdays")
    periods = request.json.get("periods")
    manager = DishSaleTimeManager(merchant_id=merchant_id)
    involved_ids = set()
    if dish_ids:
        involved_ids.update(dish_ids)
    dish_sale_time = None
    if id:
        dish_sale_time = manager.get_dish_sale_time(id=id)
    if name and not dish_sale_time:
        dish_sale_time = manager.get_dish_sale_time(name=name)
    if not dish_sale_time:
        dish_sale_time = manager.create_dish_sale_time(name)
    if dish_sale_time.dish_ids:
        involved_ids.update(dish_sale_time.dish_ids)
    manager.update_dish_sale_time(
        dish_sale_time,
        dish_ids=dish_ids,
        name=name,
        start_date=start_date,
        end_date=end_date,
        periods=periods,
        weekdays=weekdays,
        status=status,
    )
    manager.add_or_update_dish_sale_time(dish_sale_time)
    manager.batch_update_sale_times_of_dish(involved_ids)
    return jsonify_response(manager.to_json(dish_sale_time))


@merchant_assist.route("/dish_sale_time/<string:merchant_id>/list", methods=["POST"])
def get_merchant_dish_sale_time_list(merchant_id):
    manager = DishSaleTimeManager(merchant_id=merchant_id)
    return jsonify_response([manager.to_json(dish_sale_time) for dish_sale_time in manager.get_dish_sale_time_list()])


@merchant_assist.route("/dish_sale_time/<string:merchant_id>/single", methods=["POST"])
def get_merchant_dish_sale_time(merchant_id):
    id = request.json.get("id")
    manager = DishSaleTimeManager(merchant_id=merchant_id)
    return jsonify_response(manager.to_json(manager.get_dish_sale_time(id=id)))


@merchant_assist.route("/dish_sale_time/<string:merchant_id>/delete", methods=["POST"])
def delete_merchant_dish_sale_time(merchant_id):
    user_id = request.headers.get("userId", None)
    id = request.json.get("id")
    user_authorize(merchant_id, user_id)
    manager = DishSaleTimeManager(merchant_id=merchant_id)
    dish_sale_time = manager.get_dish_sale_time(id=id)
    deleted_count = manager.delete_dish_sale_time(dish_sale_time.id)
    manager.batch_update_sale_times_of_dish(dish_sale_time.dish_ids)
    return jsonify_response({'deletedCount': deleted_count})


@merchant_assist.route("/table_area/create", methods=["POST"])
def add_table_area():
    """添加桌台区域"""
    user_id = request.headers.get("userId", None)
    merchant_id = request.json.get("merchantId")
    area_name = request.json.get("areaName")
    user_authorize(merchant_id, user_id)
    table_manager = TableManager(merchant_id=merchant_id)
    table_area = table_manager.create_table_area(area_name=area_name)
    table_manager.add_or_update_table_area(table_area)
    return jsonify_response({"id": table_area.id, "areaName": table_area.area_name})


@merchant_assist.route("/table_area/update", methods=["POST"])
def update_table_area():
    user_id = request.headers.get("userId", None)
    merchant_id = request.json.get("merchantId")
    area_name = request.json.get("areaName")
    status = request.json.get("status")
    table_ids = request.json.get("tableIds")
    printer_sns = request.json.get("printerSns")
    id = request.json.get("id")
    user_authorize(merchant_id, user_id)
    table_manager = TableManager(merchant_id=merchant_id)
    table_area = table_manager.get_table_area(id=id)
    table_manager.update_table_area(
        table_area, area_name=area_name, status=status, table_ids=table_ids, printer_sns=printer_sns
    )
    table_manager.add_or_update_table_area(table_area)
    return jsonify_response()


@merchant_assist.route("/table_area/list", methods=["POST"])
def list_table_area():
    merchant_id = request.json.get("merchantId")
    table_manager = TableManager(merchant_id=merchant_id)
    areas = table_manager.list_table_areas()
    result = [json_format.MessageToDict(area) for area in areas]
    return jsonify_response(result)


@merchant_assist.route("/table/list", methods=["POST"])
def list_tables():
    merchant_id = request.json.get("merchantId")
    table_manager = TableManager(merchant_id=merchant_id)
    tables = table_manager.list_tables()
    result = [json_format.MessageToDict(table) for table in tables]
    return jsonify_response(result)


@merchant_assist.route("/qrcode/login", methods=["POST"])
def qrcode_login():
    user_id = request.json.get('userId')
    id = request.json.get('id')
    UserManager().qrcode_login(id, user_id)
    return jsonify_response()


@merchant_assist.route("/qrcode/check", methods=["POST"])
def qrcode_login_check():
    id = request.json.get('id')
    record = UserManager().qrcode_login_check(id)
    return jsonify_response(json_format.MessageToDict(record) if record else {})


@merchant_assist.route("/sale_summary/membership", methods=["POST"])
def membership_summary():
    body = check_body(
        {
            "merchant_ids": {"required": True, "type": list},
            "start_time": {"required": True, "type": int},  # 左闭
            "end_time": {"required": True, "type": int},  # 右开
        }
    )
    dao = DaoORMHelper(
        db=constants.MONGODB_TRANSACTION_DATABASE_NAME,
        collection=constants.MONGODB_TRANSACTION_COLLECTION_NAME,
        pb=wallet_pb.Transaction,
    )
    _type = wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE
    return dao.aggregate(
        [
            {
                "$match": {
                    "payeeId": {'$in': body.merchant_ids},
                    "type": wallet_pb.Transaction.TransactionType.Name(_type),
                    "state": "SUCCESS",
                    "paidTime": {"$gte": str(body.start_time), "$lt": str(body.end_time)},
                }
            },
            {"$group": {"_id": "$payeeId", "totalFee": {"$sum": "$paidFee"}, "count": {"$sum": 1}}},
            {"$project": {"_id": 0, "merchantId": "$_id", "totalFee": 1, "count": "$count"}},
        ]
    )


@merchant_assist.route("/get_merchants_info", methods=["POST"])
def get_registration_merchants():
    body = check_body({'merchant_ids': {'required': False, 'type': list, 'default': None}})
    ordering_da = OrderingServiceDataAccessHelper()
    merchant_da = MerchantDataAccessHelper()
    if body.merchant_ids is not None:
        registration_infos = ordering_da.get_registration_info_by_matcher({'merchantId': {'$in': body.merchant_ids}})
    else:
        registration_infos = ordering_da.get_registration_infos(return_proto=False)
    result = []
    for item in registration_infos:
        if not item:
            continue
        try:
            if not isinstance(item, dict):
                item = protobuf_to_dict(item)
            merchant = merchant_da.get_merchant(merchant_id=item['merchantId'], return_proto=False)
            if not merchant or merchant.get("status", "DELETED") in ("DELETED", "OFFLINE"):
                continue
        except:
            continue
        result.append({'registrationInfo': item, 'merchantInfo': merchant})
    return result


@merchant_assist.route("/user/merchant/qrcode_check/<string:merchant_id>", methods=["POST"])
def user_merchant_qrcode_check(merchant_id):
    user_id = request.headers.get("userId", None)
    url = BaseHelper().create_merchant_assist_login_qrcode(merchant_id, user_id)
    return jsonify_response({'id': user_id, "url": url})

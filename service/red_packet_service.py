# -*- coding: utf-8 -*-

from flask import request
from flask import Blueprint
from google.protobuf import json_format

from business_ops import red_packet_manager
from common.utils import requests_utils
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from proto.group_dining import red_packet_pb2 as red_packet_pb
from service import base_responses
from service.base_responses import make_json_response
from service.base_responses import create_responses_obj
from service import error_codes
from view_ops.red_packet_view_helper import RedPacketViewObjectHelper

red_packet = Blueprint("red_packet", __name__, url_prefix="/red_packet")


@red_packet.route("/open", methods=["POST"])
def open_red_packet():
    """ 开红包
    """
    request_json = request.json
    dining_id = request_json.get("diningEventId")
    user_id = requests_utils.get_headers_info(request, 'userId')
    issue_scene = red_packet_pb.RedPacket.IssueScene.Value(request_json.get("issueScene", "GROUP_DINING"))
    red_packet_id = request_json.get("redPacketId")

    resp = create_responses_obj(error_codes.RED_PACKET_NOT_EXISTS, error_codes.RED_PACKET_NOT_EXISTS_MSG)
    if issue_scene == red_packet_pb.RedPacket.GROUP_DINING:
        if not dining_id and red_packet_id:
            red_packet = RedPacketDataAccessHelper().get_red_packet(id=red_packet_id)
            dining_id = red_packet.dining_event_id
        resp = red_packet_manager.RedPacketManager().open_dining_red_packet(
            dining_id=dining_id, user_id=user_id, red_packet_id=red_packet_id)
    elif issue_scene == red_packet_pb.RedPacket.NEW_MEMBER:
        resp = red_packet_manager.RedPacketManager().open_red_packet(red_packet_id, user_id)
    elif issue_scene == red_packet_pb.RedPacket.SCAN_CODE_ORDER:
        resp = red_packet_manager.RedPacketManager().open_red_packet(red_packet_id, user_id)
    elif issue_scene == red_packet_pb.RedPacket.SCAN_CODE_PAY:
        resp = red_packet_manager.RedPacketManager().open_red_packet(red_packet_id, user_id)
    elif issue_scene == red_packet_pb.RedPacket.SCAN_CODE_SELF_DINING_PAYMENT:
        resp = red_packet_manager.RedPacketManager().open_red_packet(red_packet_id, user_id)

    return make_json_response(resp)


@red_packet.route("", methods=["GET"])
@red_packet.route("/<string:red_packet_id>", methods=["GET"])
def get_red_packet(red_packet_id=None):
    """ 返回红包详情
    """
    dining_id = request.args.get('diningId', None)
    transaction_id = request.args.get('transactionId', None)
    order_id = request.args.get("orderId", None)
    user_id = requests_utils.get_headers_info(request, 'userId')

    red_packet = RedPacketViewObjectHelper().get_red_packet(
        user_id=user_id, red_packet_id=red_packet_id, dining_id=dining_id, transaction_id=transaction_id, order_id=order_id)
    if red_packet:
        red_packet_ret = json_format.MessageToDict(red_packet, including_default_value_fields=True)
        return base_responses.jsonify_response(red_packet_ret)

    return make_json_response(status_response=create_responses_obj(
        error_codes.RED_PACKET_NOT_EXISTS, error_codes.RED_PACKET_NOT_EXISTS_MSG))


@red_packet.route("/list", methods=["GET"])
def get_user_red_packet():
    """ 用户红包列表
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if page else None
    helper = RedPacketViewObjectHelper()
    red_packet_list = helper.get_user_red_packet_list_v2(user_id, page, size)
    red_packet_list = [json_format.MessageToDict(red_packet, including_default_value_fields=True) for red_packet in red_packet_list]
    return base_responses.jsonify_response(red_packet_list)

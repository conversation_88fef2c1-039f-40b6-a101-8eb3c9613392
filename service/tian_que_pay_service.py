# -*- coding: utf-8 -*-

import pytz
from datetime import datetime
from datetime import timedelta

from flask import Blueprint
from flask import request
from flask import jsonify
from google.protobuf import json_format

import proto.payment_pb2 as payment_pb
from dao.payment_da_helper import PaymentDataAccessHelper
from business_ops.tian_que_pay_manager import TianQuePayManager
from business_ops.tian_que_income_manager import TianQueIncomeManager
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from service import base_responses
from proto.finance import wallet_pb2 as wallet_pb
from business_ops.user_manager import UserManager


_tian_que_pay = Blueprint(__name__, __name__, url_prefix="/tian_que_pay")


@_tian_que_pay.route("/merchant_init/<string:merchant_id>", methods=["POST"])
def merchant_init(merchant_id):
    mno = request.json.get("mno", None)
    ledger_account_flag = request.json.get("ledgerAccountFlag", "00")
    obj = TianQuePayManager(merchant_id=merchant_id, mno=mno)
    obj.merchant_init(mno, ledger_account_flag)
    return "success"


@_tian_que_pay.route("/merchant_sign/<string:merchant_id>", methods=["POST"])
def merchant_sign(merchant_id):
    """ 分账签约
    """
    mno = request.json.get("mno", None)
    ret_json = TianQuePayManager(merchant_id=merchant_id).merchant_sign(mno)
    return base_responses.jsonify_response(ret_json)


@_tian_que_pay.route("/ledger_set_mno_array/<string:merchant_id>", methods=["POST"])
def ledegr_set_mno_array(merchant_id):
    mno = request.json.get("mno", None)
    ret_json = TianQuePayManager(merchant_id=merchant_id).query_ledger_set_mno_array(mno)
    return base_responses.jsonify_response(ret_json)


@_tian_que_pay.route("/query_launch_ledger/<string:merchant_id>", methods=["GET"])
def query_launch_ledger(merchant_id):
    transaction_id = request.args.get("transactionId")
    ret_json = TianQuePayManager(merchant_id=merchant_id).query_launch_ledger(transaction_id)
    return base_responses.jsonify_response(ret_json)


@_tian_que_pay.route("/get_transfer_info", methods=["GET"])
def get_transfer_info():
    order_no = request.args.get("orderNo")
    ret_json = TianQuePayManager().get_tian_que_pay_transfer_info(order_no)
    return base_responses.jsonify_response(ret_json)


@_tian_que_pay.route("/get_file_url", methods=["GET"])
def get_file_url():
    bill_date = request.args.get("billDate")
    bill_type = request.args.get("billType")
    ret_json = TianQuePayManager().get_file_url(bill_date, bill_type)
    return base_responses.jsonify_response(ret_json)


@_tian_que_pay.route("/payment/<string:transaction_id>", methods=["GET"])
def get_payment(transaction_id):
    """ 支付查询
    """
    ret = TianQuePayManager().trade_query(transaction_id)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/payment/traceback", methods=["POST"])
def get_payment_v2():
    """根据微信、支付宝订单号查询
    只支持查询180天以内的订单
    """
    def timestamp_to_datetime(d):
        if isinstance(d, list):
            return [timestamp_to_datetime(row) for row in d]
        if not isinstance(d, dict):
            d = json_format.MessageToDict(d)
        if not d:
            return
        for k, v in d.items():
            if k.lower().endswith("time"):
                dt = datetime.fromtimestamp(
                    int(v),
                    tz=pytz.timezone('Asia/Shanghai')
                )
                d[k] = dt.strftime("%Y-%m-%d %H:%M:%S")
        return d

    merchant_id = request.json.get("merchant_id")
    wechat_or_alipay_order_id = request.json.get("wechat_or_alipay_order_id", "")
    transaction_id = request.json.get("transaction_id", "")
    tian_que_pay_info = PaymentDataAccessHelper().get_tian_que_pay_info(
        merchant_id=merchant_id, state=payment_pb.TianQuePayInfo.NORMAL
    )
    resp = TianQuePayManager().trade_query_v2(
        tian_que_pay_info.mno,
        wechat_or_alipay_order_id,
        transaction_id
    )
    transaction = TransactionDataAccessHelper().get_transaction_by_id(
        transaction_id=resp['respData']['ordNo']
    )
    user_info = UserManager().get_user(transaction.payer_id)

    result = {
        "tianqueInfo": resp['respData'],
        "transactionInfo": timestamp_to_datetime(transaction),
        "userInfo": timestamp_to_datetime(user_info)
    }
    if transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
        fanpiao = FanpiaoDataAccessHelper().get_fanpiao(
            merchant_id=merchant_id,
            transaction_id=transaction.id
        )
        fanpiao = timestamp_to_datetime(fanpiao)
        fanpiao_payment_records = fanpiao.get('fanpiaoPaymentRecords')
        if fanpiao_payment_records:
            fanpiao['fanpiaoPaymentRecords'] = timestamp_to_datetime(fanpiao_payment_records)
        result['fanpiaoInfo'] = fanpiao
    else:
        order = OrderingServiceDataAccessHelper().get_order(
            transaction_id=transaction.id
        )
        order = timestamp_to_datetime(order)
        products = order.get("products")
        if products:
            order['products'] = timestamp_to_datetime(products)
        result["orderInfo"] = order
    return result


@_tian_que_pay.route("/real_name_commit/<string:merchant_id>", methods=["POST"])
def real_name_commit(merchant_id):
    manager = TianQuePayManager(merchant_id=merchant_id)
    ret = manager.merchant_real_name_commit_apply()
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/real_name_commit/<string:merchant_id>", methods=["GET"])
def get_real_name_commit(merchant_id):
    manager = TianQuePayManager(merchant_id=merchant_id)
    ret = manager.get_merchant_real_name_commit()
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/real_name_commit_apply_callback", methods=["POST"])
def real_name_commit_apply_callback():
    mno = request.json.get("mno", None)
    wx_apply_no = request.json.get("wxApplyNo", None)
    iden_status = request.json.get("idenStatus", None)
    info_qrcode = request.json.get("infoQrcode", None)
    reject_code = request.json.get("rejectCode", None)
    reject_info = request.json.get("rejectInfo", None)
    manager = TianQuePayManager(mno=mno)
    manager.merchant_real_name_commit_apply_callback(iden_status, info_qrcode, wx_apply_no, reject_code, reject_info)
    return jsonify(code="success", msg= "成功")


@_tian_que_pay.route("/real_name_commit/<string:merchant_id>", methods=["DELETE"])
def undo_real_name_commit(merchant_id):
    manager = TianQuePayManager(merchant_id=merchant_id)
    manager.undo_real_name_commit()
    return base_responses.jsonify_response()


@_tian_que_pay.route("/repair_ledger_launch/<string:transaction_id>/<string:order_id>", methods=["POST"])
def repair_ledger_launch(transaction_id=None, order_id=None):
    transaction_id = request.json.get("transactionId", None)
    transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
    order = OrderingServiceDataAccessHelper().get_order(id=order_id)
    obj = TianQuePayManager(merchant_id=order.merchant_id)
    obj.scan_code_order_launch_ledger(transaction, order)
    return "success"


@_tian_que_pay.route("/query_balance", methods=["GET"])
@_tian_que_pay.route("/query_balance/<string:merchant_id>", methods=["GET"])
def query_balance(merchant_id=None):
    mno = request.args.get("mno", None)
    ret = TianQuePayManager(merchant_id=merchant_id).query_balance(mno)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/query_refund/<string:merchant_id>", methods=["GET"])
def query_refund(merchant_id=None):
    """ 查询订单退款信息
    """
    transaction_id = request.args.get("transactionId")
    ret = TianQuePayManager(merchant_id=merchant_id).query_refund(transaction_id)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/query_transfer_info/<string:merchant_id>", methods=["GET"])
def query_transfer_info(merchant_id=None):
    """ 查询订单交易信息
    """
    transaction_id = request.args.get("transactionId")
    ret = TianQuePayManager(merchant_id=merchant_id).query_transfer_info(transaction_id)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/launch_ledger/<string:merchant_id>", methods=["POST"])
def launch_ledger(merchant_id):
    """ 在分账失败的情况下,手动执行分账
    """
    transaction_id = request.json.get("transactionId", None)
    transaction_da = TransactionDataAccessHelper()
    ordering_da = OrderingServiceDataAccessHelper()
    transaction = transaction_da.get_transaction_by_id(transaction_id)
    order = ordering_da.get_order(transaction_id=transaction_id)
    manager = TianQuePayManager(merchant_id=merchant_id)
    ret = manager.scan_code_order_launch_ledger(transaction, order)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/query_sign_contract/<string:merchant_id>", methods=["GET"])
def query_sign_contract(merchant_id):
    manager = TianQuePayManager(merchant_id=merchant_id)
    # 签约信息
    mno = request.args.get("mno", None)
    ret0 = manager.query_sign_contract(mno)
    ret1 = manager.query_ledger_set_mno_array(mno)
    return base_responses.jsonify_response({"sign": ret0, "setMnoArray": ret1})


@_tian_que_pay.route("/query_settlement/<string:merchant_id>", methods=["GET"])
def query_settlement(merchant_id):
    manager = TianQuePayManager(merchant_id=merchant_id)
    yesterday = datetime.now() - timedelta(1)
    yesterday = yesterday.strftime("%Y%m%d")
    query_time = request.args.get("queryTime", yesterday)
    ret = manager.query_settlement(query_time)
    return base_responses.jsonify_response(ret)


@_tian_que_pay.route("/income/<string:merchant_id>", methods=["POST"])
def income(merchant_id):
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    # 自然人对私入驻
    manager.add_or_update_income(
        operational_type = "01",
        have_license_no = "01",
        mec_type_flag = "00",
        reg_prov_cd = "************",
        reg_city_cd = "************",
        reg_dist_cd = "************",
        mcc_cd = "5812",
        identity_typ = "00",
        identity_no = "510902199008258894",
        act_no = "1234567909991999999",
        act_typ = "01",
        stm_man_id_no = "510902199008258894",
        legal_personid_positive_pic = "7b65e5f597634b1dabb904eab05d0860",
        legal_personid_opposite_pic = "7b65e5f597634b1dabb904eab05d0860",
        bank_card_positive_pic = "7b65e5f597634b1dabb904eab05d0860",
        store_pic = "7b65e5f597634b1dabb904eab05d0860",
        inside_scene_pic = "7b65e5f597634b1dabb904eab05d0860",
        icp_licence = "7b65e5f597634b1dabb904eab05d0860",
        act_nm = "谢昭辔",
        depo_bank = "************",
        depo_prov_cd = "**********",
        depo_city_cd = "**********",
        lbnk_no = "************"
    )
    return base_responses.jsonify_response()


@_tian_que_pay.route("/upload/picture/<string:merchant_id>", methods=["POST", "GET"])
def upload_picture(merchant_id):
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    image = request.files.get("image")
    picture_type = request.args.get("pictureType")
    manager.upload_picture(picture_type, image)
    return base_responses.jsonify_response()


@_tian_que_pay.route("/query_merchant_info/<string:merchant_id>", methods=["GET"])
def query_merchant_info(merchant_id):
    """ 查询入驻信息
    """
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    result = manager.query_income_result()
    if result:
        return base_responses.jsonify_response(result)
    return base_responses.jsonify_response()


@_tian_que_pay.route("/ant-shop/create", methods=["POST"])
def create_ant_shop():
    merchant_id = request.json.get("merchantId", None)
    manager = TianQuePayManager(merchant_id=merchant_id)
    ret = manager.create_ant_shop()
    return base_responses.jsonify_response(ret)

@_tian_que_pay.route("/ant-shop/query", methods=["POST"])
def query_ant_shop():
    merchant_id = request.json.get("merchantId", None)
    manager = TianQuePayManager(merchant_id=merchant_id)
    ret = manager.order_query_ant_shop()
    return base_responses.jsonify_response(ret)

# -*- coding: utf-8 -*-


from flask import Blueprint
from flask import request

from business_ops.chinaums_wechat_pay_manager import ChinaumsWechatPayManager
from service.base_responses import jsonify_response


_chinaums_wechat_pay = Blueprint(__name__, __name__, url_prefix="/chinaums_wechat_pay")


@_chinaums_wechat_pay.route("/pay_result", methods=["GET"])
def get_pay_result():
    manager = ChinaumsWechatPayManager()
    transaction_id = request.args.get("transactionId", None)
    ret_json = manager.query_payment(transaction_id)
    return jsonify_response(ret_json)


@_chinaums_wechat_pay.route("/<string:merchant_id>", methods=["POST"])
def add_config(merchant_id):
    mid = request.json.get("mid")
    tid = request.json.get("tid")
    # 默认打开异步分账
    asyn_division_flag = request.json.get("asynDivisionFlag", True)
    # 默认不打开同步分账
    division_flag = request.json.get("divisionFlag", False)
    manager = ChinaumsWechatPayManager(merchant_id=merchant_id)
    manager.add_or_update_chinaums_wechat_pay_config(mid, tid, asyn_division_flag, division_flag)
    return "success"

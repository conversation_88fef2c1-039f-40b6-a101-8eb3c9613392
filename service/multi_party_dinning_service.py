# -*- encoding: utf-8 -*-

'''
@Time        :   2024/12/23 17:31:55
'''
import logging

from flask import Blueprint

from common.request import check_body
from business_ops.multi_party_dinning_manager import MultiPartyDinningService


logger = logging.getLogger(__name__)
multi_party_dinning_bp = Blueprint("multi_party_dinning", __name__, url_prefix="/multi_party_dinning")


@multi_party_dinning_bp.route('/order', methods=['POST'])
def order_info():
    body = check_body({
        "order_id": {"required": False, "type": str},
        "merchant_id": {"required": False, "type": str},
        "table_id": {"required": False, "type": str},
        "user_id": {"required": False, "type": str},
    })
    service = MultiPartyDinningService(
        merchant_id=body.merchant_id,
        table_id=body.table_id,
        user_id=body.user_id
    )
    return service.get_order_info(body.order_id)


@multi_party_dinning_bp.route('/shopping_cart', methods=['POST'])
def shopping_cart():
    body = check_body({
        "merchant_id": {"required": True, "type": str},
        "table_id": {"required": True, "type": str},
        "user_id": {"required": True, "type": str},
    })
    service = MultiPartyDinningService(
        merchant_id=body.merchant_id,
        table_id=body.table_id,
        user_id=body.user_id
    )
    return service.get_shopping_cart()


@multi_party_dinning_bp.route('/add_dishes', methods=['POST'])
def add_dishes():
    body = check_body(
        {
            "merchantId": {"required": True, "type": str},
            "storeId": {"required": True, "type": str},
            "userId": {"required": True, "type": str},
            "tableId": {"required": True, "type": str},
            "orderId": {"required": True, "type": str},
            "dishList": {"required": True, "type": list},
            "remark": {"required": False, "type": str},
        },
        snake=False
    )
    service = MultiPartyDinningService(
        merchant_id=body.merchant_id,
        table_id=body.table_id,
        user_id=body.user_id
    )
    service.add_dishes_to_order(body.store_id, body.order_id, body.dish_list, body.remark)

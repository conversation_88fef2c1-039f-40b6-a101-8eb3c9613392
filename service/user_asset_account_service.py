# -*- coding: utf-8 -*-

from flask import Blueprint

from common import protobuf_transformer

from controller.user_asset_account_controller import UserAssetAccountController
from service.base_responses import jsonify_response


bp_name = 'user_asset_account'
_user_asset_account = Blueprint(bp_name, bp_name, url_prefix='/user_asset_account')


@_user_asset_account.route("/<string:operation>", methods=["POST"])
def operate(operation):
    ctrl = UserAssetAccountController(operation)
    result = ctrl.do_operate()
    if result is None:
        return jsonify_response()
    if isinstance(result, list):
        return jsonify_response(protobuf_transformer.batch_protobuf_to_dict(result))
    return jsonify_response(protobuf_transformer.protobuf_to_dict(result))

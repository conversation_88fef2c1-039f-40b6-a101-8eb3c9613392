import logging
import urllib.parse
import os

from google.protobuf import json_format

from service import error_codes
from service import errors
import proto.authorizer_pb2 as authorizer_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.staff_pb2 as staff_pb
import proto.user_pb2 as user_pb
from business_ops.merchant_manager import MerchantManager
from business_ops.user_manager import UserManager
from common import constants
from common.config import config
from common.constant import const
from common.utils import access_token_helper
from common.utils import date_utils
from common.utils import encoding_utils
from common.utils import id_manager
from common import http
from common.utils.WXBizDataCrypt import WXBizDataCrypt
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from dao.user_da_helper import <PERSON>r<PERSON>ata<PERSON>ccessHelper
from wechat_lib import ticket_api_helper


logger = logging.getLogger(__name__)


def create_component_url(api_url):
    # 获取第三方平台 component_access_token
    appid = config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
    access_token = access_token_helper.get_authorizer_access_token(appid)
    component_url = 'https://api.weixin.qq.com/cgi-bin/component/{}?component_access_token={}'.format(api_url, access_token)
    return component_url


def get_component_verify_ticket():
    """
    2、获取第三方平台component_access_token

    http请求方式: GET（请使用http协议）
    http://tickets.zhiyi.cn/component_verify_ticket/get

    返回结果示例
        ticke

    :return:
    """
    domain = os.environ.get("TICKETS_SERVICE_DOMAIN", "http://tickets.zhiyi.cn")
    url = f'{domain}/component_verify_ticket/get'
    resp = http.get(url=url)
    return resp.text


def get_component_access_token(component_appid, appsecret_value, ticket_value):
    """
    2、获取第三方平台component_access_token

    http请求方式: POST（请使用https协议）
    https://api.weixin.qq.com/cgi-bin/component/api_component_token

    POST数据示例:
        {
            "component_appid":"appid_value" ,
            "component_appsecret": "appsecret_value",
            "component_verify_ticket": "ticket_value"
        }
        component_appid	        第三方平台appid
        component_appsecret	    第三方平台appsecret
        component_verify_ticket	微信后台推送的ticket，此ticket会定时推送，具体请见本页的推送说明

    返回结果示例
        {"component_access_token":"61W3mEpU66027wgNZ_MhGHNQDHnFATkDa9-2llqrMBjUwxRSNPbVsMmyD-yq8wZETSoE5NQgecigDrSHkPtIYA", "expires_in":7200}
        component_access_token	第三方平台access_token
        expires_in	            有效期

    :return:
    """
    url = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token'
    request = {
        "component_appid": component_appid,
        "component_appsecret": appsecret_value,
        "component_verify_ticket": ticket_value,
    }
    resp = http.post(url=url, json=request)
    result = resp.json()
    return result


def get_pre_auth_code(component_appid):
    """
    3、 获取预授权码pre_auth_code

    http请求方式: POST（请使用https协议）
    https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=xxx

    POST数据示例:
        {"component_appid":"appid_value" }
        component_appid:第三方平台方appid

    返回结果示例:
        {"pre_auth_code": "Cx_Dk6qiBE0Dmx4EmlT3oRfArPvwSQ-oa3NL_fwHM7VI08r52wazoZX2Rhpz1dEw", "expires_in": 600}
        pre_auth_code:预授权码
        expires_in:有效期，为10分钟
    :return:
    """
    url = create_component_url("api_create_preauthcode")
    request = {
        "component_appid": component_appid,
    }
    resp = http.post(url=url, json=request)
    result = resp.json()
    pre_auth_code = result['pre_auth_code']
    return pre_auth_code


def get_api_query_auth(component_appid, authorization_code):
    """
    4、使用授权码换取公众号或小程序的接口调用凭据和授权信息

    http请求方式: POST（请使用https协议）
    https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=xxxx

    POST数据示例:
        {
            "component_appid":"appid_value" ,
            "authorization_code": "auth_code_value"
        }
        component_appid	    第三方平台appid
        authorization_code	授权code,会在授权成功时返回给第三方平台，详见第三方平台授权流程说明

    返回结果示例:
        authorization_info	授权信息
        authorizer_appid	授权方appid
        authorizer_access_token	授权方接口调用凭据（在授权的公众号或小程序具备API权限时，才有此返回值），也简称为令牌
        expires_in	有效期（在授权的公众号或小程序具备API权限时，才有此返回值）
        authorizer_refresh_token	接口调用凭据刷新令牌（在授权的公众号具备API权限时，才有此返回值），刷新令牌主要用于第三方平台获取和刷新已授权用户的access_token，只会在授权时刻提供，请妥善保存。 一旦丢失，只能让用户重新授权，才能再次拿到新的刷新令牌
                :return:
    """
    request = {
        "component_appid": component_appid,
        "authorization_code": authorization_code,
    }
    url = create_component_url("api_query_auth")
    resp = http.post(url=url, json=request)
    result = resp.json()
    return result


def get_api_authorizer_token(component_appid, authorizer_appid, authorizer_refresh_token):
    """
    获取认证者的token

    http请求方式: POST（请使用https协议）
    https:// api.weixin.qq.com /cgi-bin/component/api_authorizer_token?component_access_token=xxxxx

    POST数据示例:
        {
            "component_appid":"appid_value",
            "authorizer_appid":"auth_appid_value",
            "authorizer_refresh_token":"refresh_token_value",
        }
        component_appid	            第三方平台appid
        authorizer_appid	        授权方appid
        authorizer_refresh_token	授权方的刷新令牌，刷新令牌主要用于第三方平台获取和刷新已授权用户的access_token，只会在授权时刻提供，请妥善保存。一旦丢失，只能让用户重新授权，才能再次拿到新的刷新令牌
    返回结果示例:
        {
            "authorizer_access_token": "aaUl5s6kAByLwgV0BhXNuIFFUqfrR8vTATsoSHukcIGqJgrc4KmMJ-JlKoC_-NKCLBvuU1cWPv4vDcLN8Z0pn5I45mpATruU0b51hzeT1f8",
            "expires_in": 7200,
            "authorizer_refresh_token": "BstnRqgTJBXb9N2aJq6L5hzfJwP406tpfahQeLNxX0w"
        }
        uthorizer_access_token	授权方令牌
        expires_in	有效期，为2小时
        authorizer_refresh_token	刷新令牌
    :return:
    """
    request = {
        "component_appid": component_appid,
        "authorizer_appid": authorizer_appid,
        "authorizer_refresh_token": authorizer_refresh_token,
    }
    url = create_component_url("api_authorizer_token")
    resp = http.post(url=url, json=request)
    result = resp.json()
    return result


# 移到 wechat_lib
def get_api_get_authorizer_info(authorizer_appid):
    """
    6、获取授权方的帐号基本信息

    http请求方式: POST（请使用https协议）
    https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=xxxx

    POST数据示例:
        {
            "component_appid":"appid_value" ,
            "authorizer_appid": "auth_appid_value"
        }
        component_appid	    第三方平台appid
        authorizer_appid	授权方appid
    返回结果示例:
        小程序获取和公众号获取不一致

    :return:
    """
    request = {
        "component_appid": config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME),
        "authorizer_appid": authorizer_appid,
    }
    url = create_component_url("api_get_authorizer_info")
    resp = http.post(url=url, json=request)
    result = resp.json()
    return result


def get_api_get_authorizer_option(component_appid, authorizer_appid, option_name):
    """
    7、获取授权方的选项设置信息

    http请求方式: POST（请使用https协议）
    https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_option?component_access_token=xxxx

    POST数据示例:
        {
            "component_appid":"appid_value",
            "authorizer_appid": " auth_appid_value ",
            "option_name": "option_name_value"
        }
        component_appid	    第三方平台appid
        authorizer_appid	授权公众号或小程序的appid
        option_name	        选项名称
    返回结果示例:
        {
            "authorizer_appid":"wx7bc5ba58cabd00f4",
            "option_name":"voice_recognize",
            "option_value":"1"
        }
        authorizer_appid	授权公众号或小程序的appid
        option_name	        选项名称
        option_value	    选项值

    :return:
    """
    request = {
        "component_appid": component_appid,
        "authorizer_appid": authorizer_appid,
        "option_name": option_name,
    }
    url = create_component_url("api_get_authorizer_info")
    resp = http.post(url=url, json=request)
    result = resp.json()
    # 不存在
    # authorizer_appid = result['authorizer_appid']
    # option_name = result['option_name']
    errcode = result['errcode']
    option_value = None
    if errcode == 0:
        option_value = result['option_value']

    return option_value


def get_wechat_app_access_token(appid, appsecret):
    """
    7、获取公众号或小程序access_token

    http请求方式: GET（请使用https协议）
        https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET

    POST数据示例:
        grant_type	是	获取 access_token 填写 client_credential
        appid	是	第三方用户唯一凭证
        secret	是	第三方用户唯一凭证密钥，即appsecret
    返回结果示例:
        {
            "authorizer_appid":"wx7bc5ba58cabd00f4",
            "option_name":"voice_recognize",
            "option_value":"1"
        }
        access_token	获取到的凭证
        expires_in	凭证有效时间，单位：秒

    :return:
    """
    # GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}'.format(appid, appsecret)
    resp = http.get(url=url)
    result = resp.json()
    return result


def get_user_info_with_code(code):
    """用户同意授权后，通过从微信得到的授权code换取网页授权的access_token,
        并进而拉取该用户的微信信息。详见:
        https://mp.weixin.qq.com/wiki?action=doc&id=mp1421140842&t=0.4622491167403

    Args:
        code: (string) 从微信得到的授权code

    Returns:
        (json) 从微信返回的用户信息JSON结构体
    """
    # 通过授权code换取Access Token
    access_token_url = (
        'https://api.weixin.qq.com/sns/oauth2/access_token?appid={}&secret={}&code={}&grant_type=authorization_code'.format(
            config.SHILAI_MP_APPID, config.SHILAI_MP_APP_SECRET, code
        )
    )
    resp = http.get(access_token_url)
    res_json = resp.json()
    # 根据Access Token拉取用户信息
    user_info_url = 'https://api.weixin.qq.com/sns/userinfo?access_token={}&openid={}&lang=zh_CN'.format(
        res_json['access_token'], res_json['openid']
    )
    resp = http.get(user_info_url)
    return resp.json()


class UserAuthHelper:
    _WX_ERROR_CODE_MAP = {
        40029: error_codes.LOGIN_CODE_INVALID,
        40163: error_codes.LOGIN_CODE_INVALID,
        45011: error_codes.LOGIN_API_REACH_LIMIT,
        40226: error_codes.LOGIN_ACCOUNT_RISK,
        -1: error_codes.LOGIN_UNKOWN_ERROR,
    }

    def __init__(self):
        # self.db_utils = DbUtils()
        self.db_utils = None
        # self.from_platform = from_platform

    def user_login_code_to_session(self, js_code, from_platform, join_method=None):
        """
        http请求方式: GET（请使用https协议）
        https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type={}

        GET数据示例:
            appid	    string		是	小程序 appId
            secret	    string		是	小程序 appSecret
            js_code	    string		是	登录时获取的 code
            grant_type	string		是	授权类型，此处只需填写 authorization_code

        返回结果示例:
            openid	        string	用户唯一标识
            session_key	    string	会话密钥
            unionid	        string	用户在开放平台的唯一标识符，在满足 UnionID 下发条件的情况下会返回，详见 UnionID 机制说明。
            errcode	        number	错误码
            errmsg	        string	错误信息

            错误码
                # -1    : 系统繁忙，此时请开发者稍候再试
                # 0     : 请求成功
                # 40029	: code 无效
                # 45011	: 频率限制，每个用户每分钟100次

        :param js_code:
        :param from_platform: 平台类型
        :param join_method: 加入途径
        :return: 用户信息 user_pb2.User()
        """
        response_json = self.get_code_to_session(from_platform, js_code)
        if response_json is not None:
            openid = response_json['openid']
            session_key = response_json['session_key']
            unionid = response_json['unionid'] if 'unionid' in response_json else ''

            user_manager = UserManager()
            if unionid:
                user = user_manager.get_or_make_platform_user_by_union_id(from_platform, unionid)
                user.wechat_profile.unionid = unionid
            else:
                user = user_manager.get_or_make_platform_user_by_openid(from_platform, openid)
            user.wechat_profile.session_key = session_key
            user.wechat_profile.openid = openid
            if join_method and not user.join_method:
                user.join_method = join_method

            user_manager.update_or_create_user_by_platform(from_platform, user)
            return user
        else:
            return None

    def save_user_info(self, from_platform, iv, user_id, encrypted_data):
        """
        保存用户信息
        :param from_platform: 平台类型
        :param iv:
        :param user_id:
        :param encrypted_data:
        :return: 保存成功或者失败
        """
        # 获取用户信息
        user_manager = UserManager()
        user = user_manager.get_platform_user_by_id(from_platform, user_id)
        if user:
            session_key = user.wechat_profile.session_key
            decrypt_data = self.decrypt_data(from_platform, session_key, encrypted_data, iv)

            user.wechat_profile.openid = decrypt_data['openId']
            user.wechat_profile.nickname = decrypt_data['nickName']
            user.wechat_profile.sex = decrypt_data['gender']
            user.wechat_profile.city = decrypt_data['city']
            user.wechat_profile.province = decrypt_data['province']
            user.wechat_profile.country = decrypt_data['country']
            user.wechat_profile.headimgurl = decrypt_data['avatarUrl']

            user.member_profile.nickname = decrypt_data['nickname']
            user.member_profile.name = decrypt_data['nickname']
            user.member_profile.sex = '女' if decrypt_data['gender'] == 2 else '男'
            user.member_profile.head_image_url = decrypt_data['avatarUrl']
            if 'unionId' in decrypt_data.keys():
                user.wechat_profile.unionid = decrypt_data['unionId']

            user_manager.update_or_create_user_by_platform(from_platform, user)
            return True

        return False

    def get_code_to_session(self, from_platform, js_code):
        """
        官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
        请求微信 code_to_session 接口, 同 self.user_login_code_to_session()
        :param from_platform: 平台类型
        :param js_code: 用户登录凭证（有效期五分钟）
        """
        platform_config = self.get_platform_config(from_platform)
        code2Session_url = "https://api.weixin.qq.com/sns/jscode2session?" "appid={}&secret={}&js_code={}&grant_type={}".format(
            platform_config['appid'], platform_config['secret'], js_code, const.GRANT_TYPE
        )

        response = http.get(code2Session_url)
        response_json = response.json()
        logger.info(response_json)
        if not 'errcode' in response_json.keys():
            return response_json

        return None

    def _wx_code_to_session(self, from_platform, js_code):
        """
        官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
        微信小程序最新版登录api，修复get_code_to_session数据校验问题
        """
        platform_config = self.get_platform_config(from_platform)
        url = 'https://api.weixin.qq.com/sns/jscode2session'
        params = {
            "appid": platform_config['appid'],
            "secret": platform_config['secret'],
            "js_code": js_code,
            "grant_type": const.GRANT_TYPE,
        }
        resp = http.get(url, params=params)
        data = resp.json()
        error_msg = f"jscode2session error, from_platform={from_platform}, url={url}, params={params}, resp={data}"
        if not data or not isinstance(data, dict):
            logger.error(error_msg)
            raise
        errcode = data.get("errcode", 0)
        try:
            errcode = int(errcode)
        except ValueError:
            logger.error(error_msg)
            raise
        if errcode != 0:
            logger.error(error_msg)
            raise errors.Error(err=self._WX_ERROR_CODE_MAP.get(errcode, self._WX_ERROR_CODE_MAP[-1]))
        if not data.get("unionid") and not data.get("openid"):
            logger.error(error_msg)
            raise
        logger.info(f"jscode2session, from_platform={from_platform}, url={url}, params={params}, resp={data}")
        return data

    def temp_user_auth(self, user_id, from_platform, js_code, encrypted_data, iv, join_method=None):
        """
        临时用户授权【码牌使用饭票支付】
        """
        wx_session = self._wx_code_to_session(from_platform, js_code)
        user_manager = UserManager()
        users = user_manager.query_users(user_id)
        if not users:
            raise errors.Error(err=error_codes.LOGIN_USER_NOT_REGISTER)
        if len(users) > 1:
            user_id = users[0].user_pb.id
            users[0].user_pb.id = user_id + "_0"
        user = self._update_user_pb(
            users[0].user_pb,
            from_platform,
            wx_session.get('session_key', ''),
            wx_session.get("openid"),
            wx_session.get("unionid"),
            encrypted_data,
            iv,
            join_method,
        )
        user.joined_time = date_utils.timestamp_second()
        return UserDataAccessHelper().update_miniprogram_user(user, users[0].idx)

    def _update_user_pb(
        self, user: user_pb.User, from_platform, session_key, openid, unionid, encrypted_data, iv, join_method
    ) -> user_pb.User:
        user.wechat_profile.openid = openid
        user.wechat_profile.unionid = unionid
        if from_platform is None or from_platform == constants.PLATFORM_USER:
            user.third_party_uniq_id = unionid
        user.wechat_profile.session_key = session_key
        if encrypted_data and iv:
            decrypt_data = self.decrypt_data(from_platform, session_key, encrypted_data, iv)
            user.wechat_profile.nickname = decrypt_data['nickName']
            user.wechat_profile.sex = decrypt_data['gender']
            user.wechat_profile.city = decrypt_data['city']
            user.wechat_profile.province = decrypt_data['province']
            user.wechat_profile.country = decrypt_data['country']
            user.wechat_profile.headimgurl = decrypt_data['avatarUrl']
            if from_platform == constants.PLATFORM_USER:
                user.member_profile.nickname = decrypt_data['nickName']
                user.member_profile.name = decrypt_data['nickName']
                user.member_profile.sex = '女' if decrypt_data['gender'] == 2 else '男'
                user.member_profile.head_image_url = decrypt_data['avatarUrl']
        if join_method and not user.join_method:
            user.join_method = join_method
        return user

    def user_auth_v2(self, from_platform, js_code, encrypted_data, iv, join_method=None):
        """用户完成授权流程
        Args:
            from_platform: (string) 平台（小程序）来源
            js_code: (string) 用户登录凭据
            encrypted_data: (string) 加密的用户数据
            iv: (string) 加密初始向量
            join_method: (integer|None) 用户加入途径
        Return:
            (user_pb.User) 用户实例
        """
        wx_session = self._wx_code_to_session(from_platform, js_code)
        unionid = wx_session.get("unionid")
        openid = wx_session.get("openid")
        session_key = wx_session.get('session_key', '')
        user_manager = UserManager()
        user = user_manager.get_platform_user_by_union_id(from_platform, unionid)
        if not user:
            user = user_manager.get_or_make_platform_user_by_openid(from_platform, openid)
        user = self._update_user_pb(user, from_platform, session_key, openid, unionid, encrypted_data, iv, join_method)
        user_manager.update_or_create_user_by_platform(from_platform, user)
        return user

    def decrypt_data(self, from_platform, session_key, encrypted_data, iv):
        """
        解密加密的用户信息
        :param from_platform: 平台类型
        :param session_key: code_to_session 返回的会话密钥
        :param encrypted_data: 加密数据
        :param iv: 加密算法的初始向量
        """
        platform_config = self.get_platform_config(from_platform)
        pc = WXBizDataCrypt(platform_config['appid'], session_key)
        decrypt_data = pc.decrypt(encrypted_data, iv)
        # BUG: UnicodeEncodeError: 'ascii' codec can't encode characters in position 29-31: ordinal not in range(128)
        return decrypt_data

    def phone_auth_v2(self, user_id, from_platform, js_code, encrypted_data, iv):
        wx_session = self._wx_code_to_session(from_platform, js_code)
        decrypt_data = self.decrypt_data(from_platform, wx_session['session_key'], encrypted_data, iv)
        phone_number = decrypt_data.get("purePhoneNumber")
        if not phone_number:
            raise errors.Error(err=error_codes.LOGIN_WX_PHONE_NOT_FOUND)
        if from_platform != constants.PLATFORM_MERCHANT:
            user = UserDataAccessHelper().get_user(user_id)
            if not user:
                raise errors.Error(err=error_codes.LOGIN_USER_NOT_REGISTER)
            user.member_profile.mobile_phone = phone_number
            user_manager = UserManager()
            user_manager.update_or_create_user_by_platform(from_platform, user)
        else:
            user = UserManager().get_or_make_platform_user_by_id(from_platform, user_id)
            user.phone = phone_number
            UserManager().update_or_create_user_by_platform(from_platform, user)
        return phone_number

    def phone_auth(self, user_id, from_platform, js_code, encrypted_data, iv):
        """手机号授权
        Args
            from_platform: (string) 平台（小程序）来源
            js_code: (string) 用户登录凭据
            encrypted_data: (string) 加密的用户数据
            iv: (string) 加密初始向量
        Return:
            (user_pb.User)
        """
        session_json = self.get_code_to_session(from_platform, js_code)
        if session_json is not None:
            decrypt_data = self.decrypt_data(from_platform, session_json['session_key'], encrypted_data, iv)
            phone_number = decrypt_data.get("purePhoneNumber")
            user = UserDataAccessHelper().get_user(user_id)
            user.member_profile.mobile_phone = phone_number
            user_manager = UserManager()
            user_manager.update_or_create_user_by_platform(from_platform, user)
            return phone_number
        return None

    def user_auth(self, from_platform, js_code, encrypted_data, iv, join_method=None):
        """用户完成授权流程

        Args:
            from_platform: (string) 平台（小程序）来源
            js_code: (string) 用户登录凭据
            encrypted_data: (string) 加密的用户数据
            iv: (string) 加密初始向量
            join_method: (integer|None) 用户加入途径

        Return:
            (user_pb.User) 用户实例
        """
        session_json = self.get_code_to_session(from_platform, js_code)
        if session_json is not None:
            unionid = session_json.get("unionid")
            openid = session_json.get("openid")
            user_manager = UserManager()
            # TODO: 有两个问题需要梳理清楚这里用户查重的逻辑:
            #       1. 若用户之前曾经登录小程序但未授权，某些条件下微信平台返回用户的unionid为空;
            #       2. 商户端用户授权时，使用的openid并非时来平台下的openid，而是自己公众号下的openid.
            user = user_manager.get_platform_user_by_union_id(from_platform, unionid)
            if not user:
                user = user_manager.get_or_make_platform_user_by_openid(from_platform, openid)
            logger.info(f"微信用户登陆: {session_json} {from_platform}")
            user.wechat_profile.openid = openid
            user.wechat_profile.unionid = unionid
            if from_platform is None or from_platform == constants.PLATFORM_USER:
                user.third_party_uniq_id = unionid
            user.wechat_profile.session_key = session_json.get("session_key", "")
            if encrypted_data and iv:
                decrypt_data = self.decrypt_data(from_platform, session_json['session_key'], encrypted_data, iv)
                user.wechat_profile.nickname = decrypt_data['nickName']
                user.wechat_profile.sex = decrypt_data['gender']
                user.wechat_profile.city = decrypt_data['city']
                user.wechat_profile.province = decrypt_data['province']
                user.wechat_profile.country = decrypt_data['country']
                user.wechat_profile.headimgurl = decrypt_data['avatarUrl']
                logger.info(f"微信获取用户信息: {decrypt_data}")

                if from_platform == constants.PLATFORM_USER:
                    user.member_profile.nickname = decrypt_data['nickName']
                    user.member_profile.name = decrypt_data['nickName']
                    user.member_profile.sex = '女' if decrypt_data['gender'] == 2 else '男'
                    user.member_profile.head_image_url = decrypt_data['avatarUrl']

            if join_method and not user.join_method:
                user.join_method = join_method

            user_manager.update_or_create_user_by_platform(from_platform, user)
            return user
        return None

    def get_platform_config(self, from_platform):
        """
        获取平台配置
        :param from_platform: 平台来源
        :return 配置内容 { app, secret }
        """
        if from_platform == constants.PLATFORM_MERCHANT:
            appid = config.WECHAT_MINIPROGRAM_MERCHANT_APPID
            secret = config.WECHAT_MINIPROGRAM_MERCHANT_SECRET
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            appid = config.WECHAT_MINIPROGRAM_STAFF_APPID
            secret = config.WECHAT_MINIPROGRAM_STAFF_SECRET
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            appid = config.WECHAT_MINIPROGRAM_SHIYI_CAT_APPID
            secret = config.WECHAT_MINIPROGRAM_SHIYI_CAT_SECRET
        else:
            appid = config.WECHAT_MINIPROGRAM_APPID
            secret = config.WECHAT_MINIPROGRAM_SECRET

        return {'appid': appid, 'secret': secret}

    def get_authorizer_auth_url(self, staff_id, user_id, merchant_id):
        """
        商家授权
        流程：跳转到微信授权页面，自定义回调URL: /authorizer/authorized?unionid={unionid}，
             商家授权完成时，页面会跳转到此回调URL，微信服务器给开发服务器发送一条“授权”推送。
         # 参考：https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
        :param staff_id: 推广员ID
        :param user_id: 用户ID
        :param merchant_id: 门店ID
        :return: 商家授权的url
        """
        pre_auth_code = get_pre_auth_code(config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME))
        values = {'userId': user_id, 'staffId': staff_id, 'merchantId': merchant_id}

        redirect_uri = urllib.parse.quote_plus(
            '{}/authorizer/authorized?{}'.format(
                config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), urllib.parse.urlencode(values)
            )
        )

        # component_appid	是	第三方平台方appid
        # pre_auth_code	是	预授权码
        # redirect_uri	是	回调URI
        # auth_type	是	要授权的帐号类型：1则商户点击链接后，手机端仅展示公众号、2表示仅展示小程序，3表示公众号和小程序都展示。如果为未指定，则默认小程序和公众号都展示。第三方平台开发者可以使用本字段来控制授权的帐号类型。
        # biz_appid	否	指定授权唯一的小程序或公众号
        auth_type = "3"  # 公众号和小程序都展示
        url = (
            "https://mp.weixin.qq.com/safe/bindcomponent?action=bindcomponent&no_scan=1"
            "&component_appid="
            + config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
            + "&pre_auth_code="
            + pre_auth_code
            + "&redirect_uri="
            + redirect_uri
            + "&auth_type="
            + auth_type
            + "#wechat_redirect"
        )
        return url

    def redirect_sns_auth(self, staff_id, merchant_id, authorized_redirect_path):
        """
        商家授权
        流程：跳转到微信授权页面，自定义回调URL: /authorizer/authorized?unionid={unionid}，商家授权完成时，页面会跳转到此回调URL，微信服务器给开发服务器发送一条“授权”推送。
         # 参考：https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
        :param staff_id: 推广员ID
        :param merchant_id: 门店ID
        :param authorized_redirect_path: 授权成功后跳转的路径
        :return: 商家授权的url
        """
        # STATE字符串为自定义内容
        state_str = '{}|{}|{}'.format(authorized_redirect_path, staff_id, merchant_id)
        callback_url = urllib.parse.quote_plus(
            '{}/sns/authorized'.format(config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME))
        )
        redirect_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid={}&redirect_uri={}&response_type=code&scope=snsapi_userinfo&state={}#wechat_redirect'.format(
            config.SHILAI_MP_APPID, callback_url, state_str
        )
        return redirect_url

    def sns_authorized(self, code, state):
        """用户登陆小程序并同意个人信息授权后，通过微信提供的授权code获取该用户信息

        Args:
            code: (string) 微信平台提供的授权code
            state: (string) 之前服务器在处理授权过程中提供的回调链接中携带的自定义信息

        Returns:
            (list) 生成新用户的ID, 跳转链接
        """
        # NOTE: 微信拉取用户信息返回结果为ISO-8859-1编码格式，需要转换为UTF-8，否则在某些平台上会显示乱码
        user_info = get_user_info_with_code(code)
        user = MerchantUserDataAccessHelper().get_user_by_union_id(user_info['unionid'])
        if not user:
            # 创建一个新用户
            user = user_pb.MerchantUser()
            user.id = id_manager.generate_user_id()
            user.wechat_profile.openid = user_info['openid']
            user.wechat_profile.nickname = encoding_utils.try_convert_text_to_utf8(user_info['nickname'])
            user.wechat_profile.sex = user_info['sex']
            user.wechat_profile.province = encoding_utils.try_convert_text_to_utf8(user_info['province'])
            user.wechat_profile.city = encoding_utils.try_convert_text_to_utf8(user_info['city'])
            user.wechat_profile.country = encoding_utils.try_convert_text_to_utf8(user_info['country'])
            user.wechat_profile.headimgurl = user_info['headimgurl']
            user.wechat_profile.unionid = user_info['unionid']
            MerchantUserDataAccessHelper().add_user(user)

        # 返回回调地址
        parts = state.split('|')
        redirect_path = parts[0]
        staff_id = parts[1]
        merchant_id = parts[2]
        redirect_url = '{}{}?staffId={}&merchantId={}'.format(
            config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), redirect_path, staff_id, merchant_id
        )
        return (user.id, redirect_url)

    def add_authorizer_token(self, auth_code):
        """生成并添加一个新的授权商户AuthorizerToken。

        Args:
            auth_code: (string)

        Returns:
            (auth_pb.AuthorizerToken) 新创建的AuthorizerToken结构体
        """
        resp = get_api_query_auth(config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME), auth_code)
        if 'errcode' in resp and resp['errcode'] != 0:
            return None

        authorization_info = resp['authorization_info']
        authorizer_appid = authorization_info['authorizer_appid']
        authorizer_access_token = authorization_info['authorizer_access_token']
        expires_in = authorization_info['expires_in']
        authorizer_refresh_token = authorization_info['authorizer_refresh_token']

        # 设置授权商户小程序授权token
        authorizer_token = authorizer_pb.AuthorizerToken()
        authorizer_token.appid = authorizer_appid
        authorizer_token.access_token = authorizer_access_token
        authorizer_token.expires_in = expires_in
        authorizer_token.refresh_token = authorizer_refresh_token
        token_json = json_format.MessageToDict(authorizer_token, including_default_value_fields=True)
        access_token_helper.add_authorizer_token(token_json)
        return authorizer_token

    def add_authorizer_ticket(self, appid, access_token, ticket_type):
        """生成并添加一个新的授权商户API Ticket。

        Args:
            appid: (string) 商户APPID
            access_token: (string) 商户Access Token
            ticket_type: (authorizer_pb.AuthorizerTicket.TicketType) 生成API Ticket的类型
        """
        if ticket_type == authorizer_pb.AuthorizerTicket.WX_CARD:
            ticket = ticket_api_helper.get_ticket_for_card_api(access_token, appid)
        elif ticket_type == authorizer_pb.AuthorizerTicket.JS_API:
            ticket = ticket_api_helper.get_ticket_for_js_api(access_token, appid)

        if ticket:
            ticket_json = json_format.MessageToDict(ticket, including_default_value_fields=True)
            access_token_helper.add_authorizer_api_ticket(ticket_json)

    def authorizer_authorized(self, user_id, staff_id, merchant_id, auth_code):
        """
        商家授权回调
        使用授权码换取公众号或小程序的接口调用凭据和授权信息
        # 响应 执行下一个步骤的 HTML 代码
        # 流程：使用 auth_code, expires_id 可获取授权商家基本信息和接口调用凭据，初始化商家数据：初始化两张会员卡（一张用于非注册会员，一张用户注册会员）
        :param user_id: 商家的用户id
        :param staff_id: 推广员ID
        :param merchant_id: 门店ID
        :param auth_code: 授权的信息
        :return: 商家授权回调
        """
        # 添加商户AuthorizationToken
        authorizer_token = self.add_authorizer_token(auth_code)
        authorizer_appid = authorizer_token.appid
        authorizer_access_token = authorizer_token.access_token

        # 添加商户API Ticket
        self.add_authorizer_ticket(authorizer_appid, authorizer_access_token, authorizer_pb.AuthorizerTicket.WX_CARD)
        self.add_authorizer_ticket(authorizer_appid, authorizer_access_token, authorizer_pb.AuthorizerTicket.JS_API)

        merchant = MerchantManager().init_merchant(id=merchant_id, user_id=user_id, binding_staff_id=staff_id)

        # 获取授权方的微信公众号或者小程序信息
        json_authorizer_info = get_api_get_authorizer_info(authorizer_appid)
        authorizer = json_format.ParseDict(json_authorizer_info, authorizer_pb.Authorizer(), ignore_unknown_fields=True)
        authorizer.authorization_info.authorization_appid = authorizer_appid
        authorizer.merchant_id = merchant_id
        merchant.shilai_platform_authorizer_info.CopyFrom(authorizer)

        # 基本信息
        basic_info = merchant_rules_pb.MerchantBasic()
        basic_info.name = authorizer.authorizer_info.principal_name
        basic_info.display_name = authorizer.authorizer_info.nick_name
        basic_info.logo_url = authorizer.authorizer_info.head_img
        merchant.basic_info.CopyFrom(basic_info)

        MerchantDataAccessHelper().add_merchant(merchant)
        MerchantManager().init_registration_info(merchant_id)

    def staff_add(self, user_id, inviting_staff, is_certified=False, role=None):
        """
        商家信息
        流程: 超级管理员可以添加地推(业务)员工, 会将该员工的User id复制到Staff表格中, 员工享有注册商家的权限
        :param request staff_pb2.ShilaiStaff()
        :return:
        """
        staff_da = StaffDataAccessHelper()
        staff = staff_da.get_staff(user_id)
        if not staff:
            staff = staff_pb.ShilaiStaff()
            staff.id = user_id
            staff.joined_time = date_utils.timestamp_second()
            if role is None:
                role = staff_pb.ShilaiStaff.EMPLOYEE
            staff.role = role
        if inviting_staff != user_id:
            staff.manager_id = inviting_staff
        staff.inviting_staff_id = inviting_staff
        staff.is_certified = is_certified
        staff_da.update_or_create_staff(staff)
        return staff

    def staff_invitation_info(self, user_id, invitation_id):
        """
        当用户扫描二维码，进入 pages/staff/invitation 页面，点击接受邀请，即调用此接口激活成为 Staff
        :param user_id: 推广员的id
        :return:
        """
        # 获取推广员是通过那一个商户二维码生成的
        qrcode_info = QrcodeDataAccessHelper().get_staff_qrcode(invitation_id)
        role = staff_pb.ShilaiStaff.EMPLOYEE
        # 代理只有唯一的二维码,绑定此ID
        if "03b9dba6d3934026b09afb676d5111ea" == invitation_id:
            role = staff_pb.ShilaiStaff.AGENT
        staff_id = qrcode_info.base_info.staff_id
        staff = self.staff_add(user_id, staff_id, is_certified=True, role=role)
        return staff

    def staff_info(self, user_id):
        """
        获取推广员信息
        :param user_id: 推广员的id
        :return:
        """
        staff = StaffDataAccessHelper().get_staff(user_id)
        if staff:
            return json_format.MessageToDict(staff, including_default_value_fields=True, use_integers_for_enums=True)
        else:
            return None

    # def delete_staff_info(self, staff_id):
    #     """
    #     1.	删除员工信息
    #     """
    #     # 保存请求结果到数据库中
    #     db_utils = DbUtils()
    #     staff = db_utils.get_staff(staff_id)
    #     json_obj = json_format.MessageToDict(staff,
    #                                          including_default_value_fields=True,
    #                                          use_integers_for_enums=True)
    #     return json_obj
    def get_authorizer_info(self, merchant_id):
        """
        # 使用授权码换取公众号或小程序的接口调用凭据和授权信息
        :param merchant_id:
        :return:
        """
        merchant_id = 'b617e666-5ca2-4c41-a302-b7c7a9045666'
        authorizer_info = self.db_utils.get_authorizer_info(merchant_id)
        json_obj = json_format.MessageToDict(authorizer_info, including_default_value_fields=True, use_integers_for_enums=True)
        return json_obj

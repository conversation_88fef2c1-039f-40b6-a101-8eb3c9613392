
# 通用返回类型， 0-9999
SUCCESS = 0
SUCCESS_MSG = 'ok'
FAIL = 3
FAIL_MSG = "fail"
UNKNOWN_ERROR = 1
UNKNOWN_ERROR_MSG = '未知类型错误'
INVALID_REQUEST_DATA = 2
INVALID_REQUEST_DATA_MSG = '非法的请求参数'
SHOW_ERROR_MESSAGE = 9

UNAUTHORIZED = 401
UNAUTHORIZED_MSG = '用户未登录'
PERMISSION_DENIED = 403
PERMISSION_DENIED_MSG = '您没有操作权限'
SERVER_ERROR = 500
SERVER_ERROR_MSG = '系统繁忙，请稍后再试'
PARAMETER_NOT_ENOUGH = 700
PARAMETER_NOT_ENOUGH_MSG = '参数不足'
PAGE_NOT_FOUND = 404
PAGE_NOT_FOUND_MSG = '页面到火星上去了'
REQUEST_HANDLING = 405
REQUEST_HANDLING_MSG = '请求处理中,请勿重复提交'
NOT_IMPLEMENT = (406, "操作未实现")
REQUESTS_TIMEOUT = (407, "接口请求超时")
SHOW_MESSAGE = (409, "服务开小差了,请稍后再试")

# auth_service相关错误代码，10001~19999

DISH_NOT_ENOUGH = (1013, "菜品'{}'余量不足")
DISH_NOT_FOUND= (1014, "菜品已被清空")

# merchant_service相关错误代码，20001~29999
MERCHANT_MEMBERCARD_CATEGORY_NOT_FOUND = 20001
MERCHANT_MEMBERCARD_CATEGORY_NOT_FOUND_MSG = '找不到匹配的会员卡类型'
MERCHANT_NOT_FOUND = 20002
MERCHANT_NOT_FOUND_MSG = '找不到指定的商户信息'
MERCHANT_UPDATE_PERMISSION_DENIED = 20003
MERCHANT_UPDATE_PERMISSION_DENIED_MSG = '没有修改商户的权限'
MERCHANT_CREATE_SUBMERCHANT_ERROR = 20004
MERCHANT_CREATE_SUBMERCHANT_ERROR_MSG = '创建子商户出错，请重试'
MERCHANT_STORE_INVITATION_INVITER_PERMISSION_DENIED = 20005
MERCHANT_STORE_INVITATION_INVITER_PERMISSION_DENIED_MSG = '该邀请者无邀请权限'
MERCHANT_ACTIVATE_PERMISSION_DENIED = 20006
MERCHANT_ACTIVATE_PERMISSION_DENIED_MSG = '没有上线商户的权限'
MERCHANT_ACTIVATE_FAILURE = 20007
MERCHANT_ACTIVATE_FAILURE_MSG = '商户上线失败'
MERCHANT_STORE_NOT_FOUND = 20008
MERCHANT_STORE_NOT_FOUND_MSG = '找不到指定门店信息'


# misc_service相关错误代码，30001~39999
# 非法图片类型
INVALID_IMAGE_TYPE = (30002, "请检查上传的图片类型，仅限于png、PNG、jpg、JPG、bmp")


# staff_service相关错误代码，40001~49999
STAFF_NOT_CERTIFIED = 40001
STAFF_NOT_CERTIFIED_MSG = '该业务员用户尚未通过时来平台认证'
STAFF_NOT_FOUND = 40002
STAFF_NOT_FOUND_MSG = '找不到相应的业务员信息'

# user_service相关错误代码，50001~59999
USER_MEMBER_PROFILE_NOT_FOUND = 50001
USER_MEMBER_PROFILE_NOT_FOUND_MSG = '该用户尚未在时来平台注册，不可通过auto_activate方式领取会员卡'
USER_NOT_FOUND = 50002
USER_NOT_FOUND_MSG = '找不到相应的用户'
USER_INFO_ERROR = 50003
USER_INFO_ERROR_MSG = "请重新扫码进入"
NOT_ALIPAY_USER = 50004
NOT_ALIPAY_USER_MSG = "不是支付宝用户"

LOGIN_CODE_INVALID = (50005, '本次登录无效，请删除小程序后重新进入登录')  # 40163、40029
LOGIN_API_REACH_LIMIT = (50006, '您的账号出现频繁登录，请稍后再试')  # 45011
LOGIN_ACCOUNT_RISK = (50007, '您的账号属于高风险，请咨询微信客服')  # 40226
LOGIN_UNKOWN_ERROR= (50008, '登录失败，请咨询时来客服')  # -1
LOGIN_USER_NOT_REGISTER = (50009, '您的账号未注册，请先通过小程序注册登录')
LOGIN_WX_PHONE_NOT_FOUND = (50010, '手机号授权失败，请先在微信号中绑定手机号')
LOGIN_REPEAT_ERROR = (50011, '请勿重复登录，2秒后再试')

# 优惠券相关错误代码，60001~69999
COUPON_NOT_FOUND = 60001
COUPON_NOT_FOUND_MSG = '找不到相应的优惠券'
COUPON_CATEGORY_NOT_FOUND = (60002, "找不到优惠券类型")
COUPON_ALREADY_USED = 60003
COUPON_ALREADY_USED_MSG = '该优惠券之前已核销，不能重复使用'
COUPON_CATEGORY_NOT_SUPPORT = 60004
COUPON_CATEGORY_NOT_SUPPORT_MSG = '不支持或配置不正确的优惠券类型'
COUPON_ACCEPT_ERROR = 60005
COUPON_ACCEPT_ERROR_MSG = '领取该优惠券时发生错误，请重试'
COUPON_CATEGORY_SPEC_CONFIG_NOT_SUPPORT = 60006
COUPON_CATEGORY_SPEC_CONFIG_NOT_SUPPORT_MSG = '不支持配置该优惠券类型'
COUPON_CATEGORY_SPEC_CONFIG_NOT_FOUND = 60007
COUPON_CATEGORY_SPEC_CONFIG_NOT_FOUND_MSG = '找不到该优惠券类型的设定'
COUPON_EXPIRED_CANNOT_REFUND = (60008, "券包已过期,无法退款")
COUPON_CATEGORY_CANNOT_REFUND = (60009, "优惠券类型不支持退款")

# 支付相关错误代码，70001~79999
PAYMENT_NOT_FOUND = 70001
PAYMENT_NOT_FOUND_MSG = '找不到相应的支付记录'
PAYMENT_PAID_FEE_SHOULD_BIGGER_THAN_ZERO = 70002
PAYMENT_PAID_FEE_SHOULD_BIGGER_THAN_ZERO_MSG = '支付金额必须大于0'
NO_DISCOUNT_TIME_RANGES = 70003
NO_DISCOUNT_TIME_RANGES_MSG = '当前时间段不可使用'
PAY_METHOD_DONNOT_SUPPORT_BUSINESS = 70004
PAY_METHOD_DONNOT_SUPPORT_BUSINESS_MSG = "不支持的业务类型"
BUSINESS_NOT_SUPPORT = 70005
BUSINESS_NOT_SUPPORT_MSG = "不支持的业务类型"
REFUND_NOT_SUPPORT = 70006
REFUND_NOT_SUPPORT_MSG = "暂不支持退款,请与服务员联系"
ALIPAY_PREPAY_ERROR = 70007
ALIPAY_PREPAY_ERROR_MSG = "支付宝支付出错"
TIAN_QUE_PAY_INFO_NOT_FOUND = (70008, "商户支付信息查询出错")
TIAN_QUE_PAY_PERMITION_DENIED = (70009, "商户支付信息已禁用")
TIAN_QUE_PAY_NOT_REAL_NAME_COMMIT_APPLY = (70010, "商户支付信息未实名认证")
TIAN_QUE_PAY_PREPAY_FAIL = (70011, "调用微信支付失败")
TIAN_QUE_PAY_INFO_STATE_ERROR = (70012, "天阙支付信息状态出错")
TIAN_QUE_PAY_VERIFY_FAIL = (70013, "天阙验签失败")
TIAN_QUE_PAY_TRADE_FAIL = (70014, "天阙交易失败")
TIAN_QUE_PAY_LAUNCH_LEDGER_FAIL = (70015, "天阙分账失败")
TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_FAIL = (70016, "天阙实名认证提交失败")
TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_NOT_EXISTS = (70017, "天阙实名认证信息不存在")
TIAN_QUE_PAY_UNDO_REAL_NAME_COMMIT_APPLY_FAIL = (70018, "天阙实名认证撤销失败")
TRADING_IN_PROCESS = (70019, "交易进行中,请勿重复提交")
CHINAUMS_WECHAT_PAY_FAIL = (70020, "银联商务交易失败")
CHINAUMS_WECHAT_PAY_NOT_VERIFIED = (70021, "银联商务支付验签失败")
CHINAUMS_WECHAT_PAY_PREPAY_FAIL = (70022, "银联商务预支付失败")
CHINAUMS_WECHAT_PAY_CHECK_INFO_FAIL = (70023, "商户没有开通银联支付")
REFUND_REQUEST_FAILED = (70024, "发起退款失败: {reason}")
TOO_EARLIER_TRANSACTION_CANNOT_REFUND = (70025, "太早之前的订单无法发起退款,请联系商家")
ALIPAY_TRANSFER_ERROR = (70026, "支付宝转账失败")
ALIPAY_MINIPROGRAM_GET_USERINFO_ERROR = (70027, "支付宝小程序获取用户信息失败")
TIAN_QUE_PAY_TYPE_NOT_SUPPORTED = (70028, "天阙不支持的支付类型")

# 约饭相关错误代码, 80001~89999
GROUP_DINING_MERCHANT_ID_NOT_PROVIDED = 80001
GROUP_DINING_MERCHANT_ID_NOT_PROVIDED_MSG = '商户id参数错误'
GROUP_DINING_INITIATOR_ID_NOT_PROVIDED = 80002
GROUP_DINING_INITIATOR_ID_NOT_PROVIDED_MSG = '发起者id参数错误'
GROUP_DINING_EVENT_TIME_NOT_PROVIDED = 80003
GROUP_DINING_EVENT_TIME_NOT_PROVIDED_MSG = '约饭时间参数错误'
GROUP_DINING_STORE_ID_NOT_PROVIDED = 80004
GROUP_DINING_STORE_ID_NOT_PROVIDED_MSG = '门店id参数错误'
DINING_NOT_EXISTS = 80005
DINING_NOT_EXISTS_MSG = '饭局不存在'
CANNOT_JOIN_DINING = 80006
CANNOT_JOIN_DINING_MSG = '无法加入饭局'
INVITE_PERMISSION_DENIED = 80007
INVITE_PERMISSION_DENIED_MSG = '没有邀请权限'
TOO_MANY_ACCEPTED = 80008
TOO_MANY_ACCEPTED_MSG = '参与人数己达上限'
MERCHANT_NOT_EXISTS = 80010
MERCHANT_NOT_EXISTS_MSG = '商户不存在'
STORE_NOT_EXISTS = 80011
STORE_NOT_EXISTS_MSG = '门店不存在'
DINING_FINISHED = 80012
DINING_FINISHED_MSG = '饭局己结束'
RED_PACKET_NOT_EXISTS = 80014
RED_PACKET_NOT_EXISTS_MSG = '红包不存在'
RED_PACKET_DRAWN = 80015
RED_PACKET_DRAWN_MSG = '红包已经被领取'
RED_PACKET_CANCELLED = 80016
RED_PACKET_CANCELLED_MSG = '红包已退回'
NOT_BEEN_INVITED = 80016
NOT_BEEN_INVITED_MSG = '未被邀请'
NOT_PAID_TO_INITIATOR = 80017
NOT_PAID_TO_INITIATOR_MSG = '未付款给发起人,无法领取'
TRANSACTION_NOT_EXISTS = 80018
TRANSACTION_NOT_EXISTS_MSG = '订单不存在'
DINING_NOT_PAID = 80019
DINING_NOT_PAID_MSG = '饭局未买单'
NOT_THE_INITIATOR = 80020
NOT_THE_INITIATOR_MSG = '不是发起者,无法买单'
CANNOT_INVIT_SELF = 80021
CANNOT_INVIT_SELF_MSG = '不能邀请自己'
GROUP_DINING_STATE_ERROR_CANNOT_JOIN = 80022
GROUP_DINING_STATE_ERROR_CANNOT_JOIN_MSG = '饭局己结束,不能加入'
INVITER_ID_ERROR = 80023
INVITER_ID_ERROR_MSG = "邀请人错误"
PAY_METHOD_NOT_SUPPORT = 80024
PAY_METHOD_NOT_SUPPORT_MSG = '支付方式不支持'
USER_CNT_ERROR = 80025
USER_CNT_ERROR_MSG = '参数异常'
DINING_TRANSFER_ALREADY = 80026
DINING_TRANSFER_ALREADY_MSG = '饭局己转账,请勿重复支付'
NO_DINING_POLICY = 80027
NO_DINING_POLICY_MSG = '门店未设置约饭策略'
DINING_ALREADY_PAID = 80028
DINING_ALREADY_PAID_MSG = '饭局己支付'
DIRECTOR_CANNOT_QUIT = 80029
DIRECTOR_CANNOT_QUIT_MSG = '局长不能退出'
DIRECTOR_CANNOT_BE_KICKED = 80030
DIRECTOR_CANNOT_BE_KICKED_MSG = '局长不能被踢'
NOT_DIRECTOR = 80031
NOT_DIRECTOR_MSG = '不是局长'
GROUP_DINING_STATE_ERROR = 80032
GROUP_DINING_STATE_ERROR_MSG = '饭局己结束'
INVITER_NOT_IN_DINING = 80033
INVITER_NOT_IN_DINING_MSG = '邀请者不在饭局中'
WAITING_DIRECTOR_AGREE = 80034
WAITING_DIRECTOR_AGREE_MSG = '等待局长同意'
KICKED_BY_DIRECTOR = 80035
KICKED_BY_DIRECTOR_MSG = '被局长踢出,无法再加入'
SIGNINED_USERS_TOO_LITTLE = 80036
SIGNINED_USERS_TOO_LITTLE_MSG = '签到人不足两人,无少买单'
DIRECTOR_DID_NOT_SIGNINED = 80037
DIRECTOR_DID_NOT_SIGNINED_MSG = '请等待局长先签到'
CREATE_GROUP_DINING_FAILED = 80038
CREATE_GROUP_DINING_FAILED_MSG = '创建饭局失败'
GROUP_DINING_FEE_NOT_MATCHED = 80039
GROUP_DINING_FEE_NOT_MATCHED_MSG = '饭局支付金额错误'
JOIN_GROUP_DINING_FAILED = 80040
JOIN_GROUP_DINING_FAILED_MSG = '加入饭局失败'
TRANSACTION_STATUS_ERROR = 80041
TRANSACTION_STATUS_ERROR_MSG = "订单状态错误"

# 钱包相关错误代码，90001~99999
WALLET_BALANCE_NOT_ENOUGH = 90001
WALLET_BALANCE_NOT_ENOUGH_MSG = '钱包余额不足'
WALLET_WITHDRAW_THRESHOLD = 90002
WALLET_WITHDRAW_THRESHOLD_MSG = '提现时，账户余额不得低于{}元'
WALLET_WITHDRAW_LIMIT_PER_DAY = 90003
WALLET_WITHDRAW_LIMIT_PER_DAY_MSG = '单日提现金额不得高于{}元'
WALLET_WITHDRAW_LEAST_AMOUNT = 90004
WALLET_WITHDRAW_LEAST_AMOUNT_MSG = '单次提现金额不得低于{}元'
WALLET_WITHDRAW_TIMES_PER_TIME = 90005
WALLET_WITHDRAW_TIMES_PER_TIME_MSG = '每日提现次数不大于{}次'
WALLET_TOPUP_BILL_MUST_BIGGER_THAN_ZERO = 90006
WALLET_TOPUP_BILL_MUST_BIGGER_THAN_ZERO_MSG = '充值金额必须大于0'
WALLET_TRANSFER_AMOUNT_REACH_LIMIT = 90007
WALLET_TRANSFER_AMOUNT_REACH_LIMIT_MSG = '单日转出金额超上限'
WALLET_TRANSFER_TIMES_REACH_LIMIT = 90008
WALLET_TRANSFER_TIMES_REACH_LIMIT_MSG = '单日转出次数超上限'
WECHAT_PROMOTION_TRANSFER_ERROR = (90009, "微信企业付款出错,请联系我们")
WECHAT_PROMOTION_TRANSFER_FAILED = (90010, "微信企业付款失败,请联系我们")
WALLET_WITHDRAW_NOT_SUPPORTED = (90011, "目前只支持微信用户提现")
WALLET_WITHDRAW_FAILED = (90012, "提现失败，请联系商家")

# 点菜相关
MERCHANT_NOT_ENABLE_DISH_ORDER = 100001
MERCHANT_NOT_ENABLE_DISH_ORDER_MSG = '门店未开通点菜'
ORDERING_SERVICE_NOT_SUPPORTED = 100002
ORDERING_SERVICE_NOT_SUPPORTED_MSG = '不支持的点菜系统'
CREATE_ORDER_FAIL = 100003
CREATE_ORDER_FAIL_MSG = '创建订单失败'
ORDER_NOT_FOUND = 100004
ORDER_NOT_FOUND_MSG = '订单不存在'
PAIDFEE_BILLFEE_NOT_MATCH = 100005
PAIDFEE_BILLFEE_NOT_MATCH_MSG = '菜品折扣有变化，请清空购物车，重新点单'
ORDER_ALREADY_PAID = 100006
ORDER_ALREADY_PAID_MSG = '订单己支付'
TABLE_NOT_MATCHED = 100007
TABLE_NOT_MATCHED_MSG = '台桌不匹配,请联系收银员'
KERUYUN_CLEAR_TABLE_FAILED = 100008
KERUYUN_CLEAR_TABLE_FAILED_MSG = '客如云清台失败,请联系收银员'
KERUYUN_TABLE_NOT_TAKE_EFFORT = 100009
KERUYUN_TABLE_NOT_TAKE_EFFORT_MSG = '客如云桌台未生效,请联系收银员'
KERUYUN_TABLE_CANNOT_USED = 100010
KERUYUN_TABLE_CANNOT_USED_MSG = '客如云桌台不可用,请联系收银员'
KERUYUN_TABLE_IN_DINING = 100011
KERUYUN_TABLE_IN_DINING_MSG = '当前桌台就餐中,请联系收银员'
KERUYUN_TABLE_LOCKED = 100012
KERUYUN_TABLE_LOCKED_MSG = '当前桌台被锁定,请联系收银员'
KERUYUN_TABLE_NOT_FOUND = 100013
KERUYUN_TABLE_NOT_FOUND_MSG = '客如云桌台未找到,请联系收银员'
ADD_DISH_FAILED = 100014
ADD_DISH_FAILED_MSG = '加菜失败,请联系收银员'
DISH_SOLD_OUT = 100015
DISH_SOLD_OUT_MSG = "菜品'{}'己售罄,请联系收银员"
KERUYUN_CREATE_ORDER_ERROR = 100016
KERUYUN_CREATE_ORDER_ERROR_MSG = '客如云创建订单失败'
KERUYUN_PAY_ORDER_ERROR = 100017
KERUYUN_PAY_ORDER_ERROR_MSG = '客如云支付订单失败'
ORDER_ALREADY_PAID = 100018
ORDER_ALREADY_PAID_MSG = '订单已在其它端支付'
ORDER_POS_RETURN = 100019
ORDER_POS_RETURN_MSG = '订单已退货'
DISH_NOT_EXISTS = 100020
DISH_NOT_EXISTS_MSG = "菜品不存在"
DISH_NOT_IN_SALE_TIME = 100021
DISH_NOT_IN_SALE_TIME_MSG = "菜品'{}'不在售卖时间"
DISH_ATTR_SOLD_OUT = 100022
DISH_ATTR_SOLD_OUT_MSG = "'{}'已售磬"
DISH_SUPPLY_CONDIMENT_SOLD_OUT = 100023
DISH_SUPPLY_CONDIMENT_SOLD_OUT_MSG = "'{}'已售磬"
CANNOT_ADD_DISH_WITH_SUPPLY_CONDIMENTS = 100024
CANNOT_ADD_DISH_WITH_SUPPLY_CONDIMENTS_MSG = "加菜不能有配料"
TABLE_ONLY_FOR_PAY = 100025
TABLE_ONLY_FOR_PAY_MSG = "该桌台不能用于下单"
TABLE_HAS_ORDER_CREATING = 100026
TABLE_HAS_ORDER_CREATING_MSG = "该桌台已有订单,请重新扫码"
ORDER_UPDATED = 100027
ORDER_UPDATED_MSG = "订单已更新,请重新扫码支付"
ADD_DISH_DONNOT_SUPPORT_COMBO_MEAL = 100028
ADD_DISH_DONNOT_SUPPORT_COMBO_MEAL_MSG = "套餐不支持加菜"
ORDER_CANNOT_PAY = 100029
ORDER_CANNOT_PAY_MSG = "部分菜品不能扫码支付,请移步前台支付"
TABLE_IN_PAYING = (100030, "其他用户正在支付,请稍后")
KERUYUN_POST_ERROR = (100031, "客如云接口调用失败")
KERUYUN_CREATE_ORDER_TIMEOUT = (100032, "客如云创建订单接口超时")
SYNCING_DISH = (100033, "菜品同步中,请勿重复操作")
WRONG_KERUYUN_SHOP_ID = (100034, "请确认客如云门店ID正确并且已授权")
ORDERING_FAILED_NEED_RETRY = (100035, "扫码点餐请求处理失败,需要重试")
POS_NOT_SUPPORTED = (100036, "收银机不支持")
PLEASE_DONNOT_CREATE_ORDER_REPEATEDLY = (100037, "请勿重复下单")
ORDER_NOT_PAID = (100038, "订单未支付")
ORDER_PAYING = (10039, "订单处理中,请勿重复提交")
KERUYUN_SYNC_ORDER_ERROR = (10040, "客如云订单Sync同步调用出错")
KERUYUN_BUSINESS_EXCEPTION = (10041, "客如云支付失败")
KERUYUN_DUPLICATE_KEY_ERROR = (10042, "kry订单ID重复")
ORDER_NOT_CONFIRMED = (10043, "订单未确认")
USER_PAYING = (10044, "用户支付中")
ORDER_POS_RETURN = (100045, "该订单已退款")
ORDER_PAYING_V2 = (10046, "订单正在支付中，请稍后重试")
TABLE_DISABLED = (10047, "该桌台不能使用，请重新选择桌台")
ORDER_ADDING_DISH = (10048, "订单正在加菜中，请稍后重试")

# 券包
COUPON_PACKAGE_NOT_FOUND = 110001
COUPON_PACKAGE_NOT_FOUND_MSG = '券包不存在'
WRONG_COUPON_OWNER = 110002
WRONG_COUPON_OWNER_MSG = '优惠券不属于该用户'
BUY_COUPON_PACKAGE_WRONG_PAID_FEE = 110003
BUY_COUPON_PACKAGE_WRONG_PAID_FEE_MSG = '网络异常，请重试'
DO_NOT_MEET_REQUIREMENTS = 110004
DO_NOT_MEET_REQUIREMENTS_MSG = '该优惠券消费满{}元才可使用'
CANNOT_USE_COUPON_PACKAGE_WITH_POS_DISCOUNT = 110005
CANNOT_USE_COUPON_PACKAGE_WITH_POS_DISCOUNT_MSG = "券包与POS打折不能同时使用"
COUPON_PACKAGE_CANNOT_REFUND = 110006
COUPON_PACKAGE_CANNOT_REFUND_MSG = "券包已使用,不能退款"
COUPON_PACKAGE_NOT_YOURS = 110007
COUPON_PACKAGE_NOT_YOURS_MSG = "只能退自己的卡包哦"

# 外卖
SHIPPING_ADDRESS_CANNOT_BE_EMPTY = 120001
SHIPPING_ADDRESS_CANNOT_BE_EMPTY_MSG = '地址不能为空'
CANNOT_GET_ORDER_LOCK = 120002
CANNOT_GET_ORDER_LOCK_MSG = '无法获取订单锁'
TOO_FAR_AWAY_TO_SEND = 120003
TOO_FAR_AWAY_TO_SEND_MSG = '配送距离太远,无法配送'
NO_SHIPPING_FEE_SELECTED = 120004
NO_SHIPPING_FEE_SELECTED_MSG = '未选择配送费'
NOT_THE_BUSINESS_TIMES = 120005
NOT_THE_BUSINESS_TIMES_MSG = '不在营业时间'

# 投喂
NOT_ENABLE_IFEEDU = 130001
NOT_ENABLE_IFEEDU_MSG = "商户未开通投喂功能"
NO_SUCH_WISHLIST = 130002
NO_SUCH_WISHLIST_MSG = "心愿单不存在"
WISHLIST_CANNOT_BE_FEEDING = 130003
WISHLIST_CANNOT_BE_FEEDING_MSG = "该心愿清单不能被投喂"
NOT_ENOUGH_FEEDS = 130004
NOT_ENOUGH_FEEDS_MSG = "投喂份数不足"


# 商户助手
MERCHANT_ASSIST_NO_SUCH_MERCHANT = 140001
MERCHANT_ASSIST_NO_SUCH_MERCHANT_MSG = "商户不存在"
MERCHANT_ASSIST_NO_SUCH_USER = 140002
MERCHANT_ASSIST_NO_SUCH_USER_MSG = "用户不存在"
MERCHANT_ASSIST_UPDATE_PERMISSION_DENIED = 140003
MERCHANT_ASSIST_UPDATE_PERMISSION_DENIED_MSG = "用户没有操作权限"
MERCHANT_ASSIST_CANNOT_REMOVE_YOURSELF = 140004
MERCHANT_ASSIST_CANNOT_REMOVE_YOURSELF_MSG = "不能移除自己"
MERCHANT_ASSIST_CANNOT_REFUND_TRANSACTION = 140005
MERCHANT_ASSIST_CANNOT_REFUND_TRANSACTION_MSG = "该订单已打款给商户,无法退款"

# 卡包
FANPIAO_PAY_PROCESSING = 150001
FANPIAO_PAY_PROCESSING_MSG = "卡包买单进行中,请勿重复提交"
FANPIAO_NOT_EXISTS = 150002
FANPIAO_NOT_EXISTS_MSG = "卡包不存在"
FANPIAO_NOT_YOURS = 150003
FANPIAO_NOT_YOURS_MSG = "卡包ID错误"
FANPIAO_NOT_ENOUGH_MONEY = 150004
FANPIAO_NOT_ENOUGH_MONEY_MSG = "卡包余额不足"
CANNOT_USE_FANPIAO_WITH_POS_DISCOUNT = 150005
CANNOT_USE_FANPIAO_WITH_POS_DISCOUNT_MSG = "卡包与POS机打折不能同时使用"
CANNOT_USE_FANPIAO_WITH_COUPON = 150006
CANNOT_USE_FANPIAO_WITH_COUPON_MSG = "卡包与优惠券不能同时使用"
CANNOT_USE_KERUYUN_MEMBER_CARD_WITH_COUPON = 150007
CANNOT_USE_KERUYUN_MEMBER_CARD_WITH_COUPON_MSG = "客如云会员卡不能与优惠券同时使用"
CANNOT_USE_SHILAI_MEMBER_CARD_PAY_WITH_COUPON = 150008
CANNOT_USE_SHILAI_MEMBER_CARD_PAY_WITH_COUPON_MSG = "时来会员卡支付和优惠券不能同时使用"
FANPIAO_CANNOT_REFUND = 150009
FANPIAO_CANNOT_REFUND_MSG = (150010, "卡包已使用,无法退款")
FANPIAO_QRCODE_NOT_EXISTS = (150011, "卡包二维码不存在")
FANPIAO_QRCODE_EXPIRED = (150012, "二维码不存在或已过期")
FANPIAO_QRCODE_USED = (150013, "卡包二维码已使用,请刷新页面")
FANPIAO_REFUND_STATUS_ERROR = (150014, "卡包状态错误，无法退款")
FANPIAO_LIMIT_REFUND_ERROR = (150015, "该卡包已标记为禁止退款")
FANPIAO_EXISTS_REFUND = (150016, "该卡包已存在退款定时任务中")

# 核销码相关
CANNOT_FIND_DISH_VERIFICATION_CODE = 160001
CANNOT_FIND_DISH_VERIFICATION_CODE_MSG = "核销码不存在"
DISH_VERIFICATION_CODE_VERIFIED = 160002
DISH_VERIFICATION_CODE_VERIFIED_MSG = "券已核销"
DONNOT_CHOOSE_DISH_VERIFICATION_CODE_DISH = 160003
DONNOT_CHOOSE_DISH_VERIFICATION_CODE_DISH_MSG = "没有选择菜品核销券对应的菜品"

# 客如云会员
CANNOT_CREATE_KERUYUN_CUSTOMER = 170001
CANNOT_CREATE_KERUYUN_CUSTOMER_MSG = "手机和微信openid都不存在,不能注册"

# 时来会员相关
MEMBER_CARD_INVALID = 180001
MEMBER_CARD_INVALID_MSG = "会员卡异常"
MEMBER_CARD_NOT_ENOUGH_BALANCE = 180002
MEMBER_CARD_NOT_ENOUGH_BALANCE_MSG = "会员卡余额不足"
RECHARGE_CATEGORY_NOT_EXISTS = 180003
RECHARGE_CATEGORY_NOT_EXISTS_MSG = "会员卡充值类型不存在"
RECHARGE_CATEGORY_AMOUNT_NOT_MATCH = 180004
RECHARGE_CATEGORY_AMOUNT_NOT_MATCH_MSG = "会员卡充值金额不匹配"
RECHARGE_CATEGORY_SELL_PRICE_NOT_MATCH = 180005
RECHARGE_CATEGORY_SELL_PRICE_NOT_MATCH_MSG = "会员卡充值售价不匹配"
MEMBER_CARD_NOT_EXISTS = (180006, "会员卡不存在")

# 活动相关
# 邀请分享活动
SHARE_NOT_EXISTS = (190001, "分享链接不存在")
SHARE_EXPIRED = (190002, "分享链接已过期")
SHARE_INACTIVE = (190003, "分享链接无效")
SHARE_COUPON_CATEGORY_NOT_EXISTS = (190004, "分享优惠券类型不存在")
RECEIVED_RECENTLY = (190005, "近期您已经领取过了哦")
SHARE_ISSUE_COUPON_FAIL = (190006, "接受失败,请稍后再试")
CANNOT_ACCEPT_SELF = (190007, "不能邀请自己")

# 配置相关
UPDATE_TIMESTAMP_UPDATING = (200001, "时间戳更新中")
PRINTER_CONFIG_NOT_FOUND = (200002, "打印机全局配置缺失")
PRINTER_CONFIG_SET_VOICE_ERROR = (200003, "打印机{printer_sn}，不支持该声音设置")

# 哗啦啦
hualala_create_order_fail = (210001, "哗啦啦创建订单失败")
hualala_refund_order_fail = (210002, "哗啦啦申请退款失败")

# 菜品券
DISH_COUPON_CATEGORY_DISH_NOT_EXISTS = (220001, "创建菜品券,菜品不存在")
DISH_COUPON_CATEGORY_DISH_ID_CONNOT_BE_NONE = (220002, "创建菜品券,菜品ID必传")

ORDER_DEALING = (220003, "订单处理中,请勿重复提交")
CANNOT_GET_WALLET_LOCK = (220004, "提现中,请稍后")

SHOPPING_CARD_NOT_EXISTS = (220005, "购物车不存在")
TABLE_NOT_EXISTS = (220006, "桌台不存在")
SHOPPING_CARD_CREATED_BY_OTHERS = (220007, "购物车已创建,请重新扫码进入")
PHONE_VERIFY_CODE_NOT_MATCH = (220008, "验证码不存在或已过期,请重新获取")
SAME_PHONE_DO_NOT_NEED_CHANGE = (220009, "相同手机号无需重新绑定")
BUY_FANPIAO_FAILED = (220010, "购买卡包失败")
SHOPPING_CARD_UPDATED = (220011, "购物车已被他人更新")
HAS_RECENTLY_PAID_ORDER = (220012, "最近已支付过订单，是否继续下单？")
PRINTER_IS_BEEN_BOND = (220013, "打印机已被绑定")
NO_PERMISSION = (220014, "该账号无删除权限")
GROUP_PURCHASE_FULL_ERROR = (220015, "该团已满员，请直接支付订单")
QRCODE_INVALID = (220015, "二维码已失效")
SHOPPING_CARD_NO_NEED_UPDATE = (220016, "购物车不用更新")

# 权限管理
PERMISSION_ADD_OR_UPDATE_ERROR = (230001, "权限入库或更新失败")
PERMISSION_EXISTS = (230002, "该角色已存在，直接更新即可")

# 数据库操作
DB_ADD_OR_UPDATE_ERROR = (240001, "数据库插入或更新数据失败")
DB_PARSE_DATA_ERROR = (240002, "无法识别该数据结构，{data}")

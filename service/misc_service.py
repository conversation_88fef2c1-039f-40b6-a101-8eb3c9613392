import logging

from flask import Blueprint
from flask import jsonify
from flask import request


from business_ops import constants
from business_ops.misc_bo_helper import MiscBusinessHelper
from common.utils import date_utils
from common.utils import id_manager
from common.utils import requests_utils
from service.base_responses import success_responses_obj
from wechat_lib import media_api_helper
from dao.base_helper import BaseHelper
from dao.click_event_da_helper import ClickEventDataAccessHelper
from dao.feedback_da_helper import FeedbackDataAccessHelper
from service import base_responses
import proto.bi.click_event_pb2 as click_event_pb
import proto.misc_pb2 as misc_pb


logger = logging.getLogger(__name__)

misc = Blueprint('misc', __name__)

# TODO: 将URL路径改为'/id', 从命名上隐藏内部实现细节(因uuid已具有技术指向性）
@misc.route('/uuid', methods=['GET'])
def get_uuid():
    """从服务器端生成和获取一个全局唯一的实体ID"""
    # 创建uuid
    unique_id = MiscBusinessHelper().get_uuid()
    responses_obj = success_responses_obj()
    responses_obj['uuid'] = unique_id
    resp = jsonify(responses_obj)
    return resp


@misc.route('/image/upload', methods=['POST'])
def upload_image():
    """ 图片上传服务
    POST /image/upload
    响应主题 {imageUrl}
    流程: 上传一张图片, 返回图片的url
    """
    # 保存上传图片
    image_url = MiscBusinessHelper().save_request_image(request)
    responses_obj = success_responses_obj()
    responses_obj['data'] = {'imageUrl': image_url}
    resp = jsonify(responses_obj)
    return resp


@misc.route('/image/<string:filename>', methods=['GET'])
def get_uploaded_image(filename):
    """获取之前上传的图片"""
    resp = MiscBusinessHelper().get_response_image(filename)
    return resp

@misc.route('/files/<string:filename>', methods=['GET'])
def get_data_file(filename):
    """获取之前上传的图片"""
    resp = MiscBusinessHelper().get_data_file(filename)
    return resp

@misc.route('/media/upload', methods=['POST'])
def upload_media():
    """ 上传素材

    POST /media/upload
    请求主体:
        type: (string) 素材类型，临时或者永久
        file: (binary) 文件流
    """
    upload_type = request.form['uploadType']

    response_obj = success_responses_obj()

    file = request.files['file']
    save_path = "{}/{}".format(constants.TEMP_UPLOAD_DIR, file.filename)
    file.save(save_path)

    if upload_type == "media":
        # 调用上传素材接口，返回 URL
        media_id = media_api_helper.upload_temp_media(save_path)
        response_obj['mediaId'] = media_id
    elif upload_type == "image":
        url = media_api_helper.upload_img(save_path)
        response_obj['url'] = url

    resp = jsonify(response_obj)
    return resp

@misc.route('/save/formid', methods=['POST'])
def save_formid():
    user_id = requests_utils.get_headers_info(request, "userId")
    timestamp = date_utils.timestamp_second()
    form_ids = request.json.get("formIds")
    for formid in form_ids:
        form = misc_pb.MiniProgramFormid()
        form.user_id = user_id
        form.formid = formid
        form.timestamp = date_utils.timestamp_second()
        FeedbackDataAccessHelper().save_form(form)
    return 'success'

@misc.route('/bi/click_event', methods=['POST'])
def add_click_event():
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_id = request.json.get('merchantId')
    event_type = request.json.get('eventType')

    click_event = click_event_pb.ClickEvent()
    click_event.id = id_manager.generate_common_id()
    click_event.user_id = user_id
    if merchant_id:
        click_event.merchant_id = merchant_id
    click_event.event_type = event_type
    click_event.event_time = date_utils.timestamp_second()
    ClickEventDataAccessHelper().add_click_event(click_event)

    return base_responses.make_json_response()

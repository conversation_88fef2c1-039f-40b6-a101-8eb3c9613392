# -*- coding: utf-8 -*-

import time

from flask import Blueprint
from flask import request
from google.protobuf import json_format

import proto.staff_pb2 as staff_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.qrcode_printer_manager import QrcodePrinterManager
from business_ops.ordering.table_manager import TableManager
from business_ops.merchant_manager import MerchantManager
from business_ops.staff_assist import MenuManager
from business_ops.staff_manager import StaffManager
from business_ops.wallet_manager import WalletManager
from business_ops.transaction_manager import TransactionManager
from cache.redis_client import RedisClient
from common.utils import id_manager
from common.utils import requests_utils
from dao.staff_da_helper import StaffDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.wallet_da_helper import WalletDataAccessHelper
from service.base_responses import jsonify_response
from service import errors, error_codes
from common.request import check_body


staff_bp = Blueprint(__name__, __name__, url_prefix="/staff_assist/staff")


@staff_bp.route("/list", methods=["GET"])
def staff_list():
    user_id = request.headers.get("userId", None)
    if not user_id:
        return jsonify_response()
    manager = StaffManager(user_id)
    ret = manager.get_staff_list()
    return jsonify_response(ret)


@staff_bp.route("/list/v2", methods=["GET"])
def staff_list_v2():
    body = check_body({"user_id": {"required": True, "type": str}})
    return StaffManager().get_staff_list_v2(body['user_id'])


@staff_bp.route("/role-list", methods=["GET"])
def staff_config():
    result = {
        "BD_EMPLOYEE": "BD拓展员",
        "BD_GROUP_MANAGER": "BD小组经理",
        "BD_CITY_MANAGER": "BD城市经理",
        "BD_REGION_MANAGER": "BD大区经理",
        "EMPLOYEE": "销售人员",
    }
    return jsonify_response(result)


@staff_bp.route("/update", methods=["POST"])
def update_staff():
    manager = StaffManager()
    is_certified = request.json.get("isCertified", None)
    role = request.json.get("role", None)
    user_id = request.headers.get("userId", None)
    staff_id = request.json.get("staffId", None)
    manager.update_staff(user_id=user_id, staff_id=staff_id, role=role, is_certified=is_certified)
    return jsonify_response()


@staff_bp.route("/page_config", methods=["GET"])
def page_config():
    return jsonify_response()


@staff_bp.route("/login", methods=["POST"])
def login():
    """业务助手登陆,为了过审"""
    user_id = "7a6723d7ec0ff6c1468f04a190ca7744"
    return jsonify_response({"userId": user_id})


@staff_bp.route("/web-login", methods=["POST"])
def web_login():
    username = request.json.get("username", None)  # shilai
    passwd = request.json.get("passwd", None)  # Ashilai.1234
    if not (username == "shilai" and passwd == "a4762871d2c836182fa2cb219d0ccd9e"):
        raise errors.ShowError("登陆失败")
    conn = RedisClient().get_connection()
    session = id_manager.generate_common_id()
    conn.set(session, 1, ex=24 * 60 * 60)
    return jsonify_response({"session": session})


@staff_bp.route("/print-qrcode/<string:merchant_id>", methods=["POST"])
def print_qrcode(merchant_id):
    city = request.json.get("city", None)
    wifi_account = request.json.get("wifiAccount", None)
    wifi_pwd = request.json.get("wifiPwd", None)
    copy_num = request.json.get("copyNum", None)
    printer_manager = QrcodePrinterManager(
        merchant_id=merchant_id, city=city, wifi_account=wifi_account, wifi_pwd=wifi_pwd, copy_num=1
    )
    table_manager = TableManager(merchant=printer_manager.merchant)
    table_manager.async_tables()
    table_names = request.json.get("tableNames", None)
    if table_names:
        if table_names[-1] == ",":
            table_names = table_names[:-1]
    table_manager.create_tables_without_pos(table_names)
    wechat_alipay_two_in_one = request.json.get("wechatAlipayTwoInOne", None)
    mphelper = request.json.get("mphelper", None)
    buy_fanpiao_qrcode = request.json.get("buyFanpiaoQrcode", None)
    wechat_qrcode = request.json.get("wechatQrcode", None)
    home_two_in_one = request.json.get("homeTwoInOne", None)
    page = request.json.get("page", None)

    for i in range(0, copy_num):
        printer_manager.create_qrcode(
            wechat_alipay_two_in_one=wechat_alipay_two_in_one,
            mphelper=mphelper,
            wechat_qrcode=wechat_qrcode,
            page=page,
            table_names=table_names,
            buy_fanpiao_qrcode=buy_fanpiao_qrcode,
            home_two_in_one=home_two_in_one,
        )

    merchant_manager = MerchantManager()
    merchant_manager.add_or_update_merchant(printer_manager.merchant)

    return jsonify_response()


@staff_bp.route("/create-tables/<string:merchant_id>", methods=["POST"])
def create_tables(merchant_id):
    printer_manager = QrcodePrinterManager(merchant_id=merchant_id)
    table_manager = TableManager(merchant=printer_manager.merchant)
    table_names = request.json.get("tableNames", None)
    table_manager.create_tables_without_pos(table_names)
    return jsonify_response()


@staff_bp.route("/print-qrcode/<string:merchant_id>", methods=["GET"])
def get_print_qrcode(merchant_id):
    printer_manager = QrcodePrinterManager(merchant_id=merchant_id)
    table_info = printer_manager.get_table_info()
    table_info = json_format.MessageToDict(table_info, including_default_value_fields=True)
    return jsonify_response(table_info)


@staff_bp.route("/userinfo", methods=["GET"])
def get_staff_userinfo():
    user_id = requests_utils.get_headers_info(request, "userId")
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff(user_id)
    staff = json_format.MessageToDict(staff, including_default_value_fields=True)
    return jsonify_response(staff)


@staff_bp.route("/userinfo", methods=["POST"])
def update_staff_userinfo():
    user_id = requests_utils.get_headers_info(request, "userId")
    name = request.json.get("name", None)
    phone = request.json.get("phone", None)
    city = request.json.get("city", None)
    province = request.json.get("province", None)
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff(user_id)
    if name is not None:
        staff.name = name
    if phone is not None:
        staff.phone = phone
    if province is not None:
        staff.province = province
    if city is not None:
        staff.city = city
    staff_da.update_or_create_staff(staff)
    return jsonify_response()


@staff_bp.route("/menu/<string:operation>", methods=["POST"])
def staff_assist_menu(operation):
    user_id = request.headers.get("userId", None)
    manager = MenuManager(operation=operation, user_id=user_id)
    ret = manager.do_operate(to_dict=True)
    if not ret:
        return jsonify_response()
    return jsonify_response(ret)


valid_operaters = [
    "ad7cd36e-f1b7-4d20-83f2-5115b2267376",  # inmove test
    "6cadf6e7-5aa4-4309-9832-4b4fbf02b6de",  # jie test
    "1dcf6245-1739-408d-a55a-ae16c5b8b4d2",  # 船长 test
]


@staff_bp.route("/wallet/bonus", methods=["POST"])
def wallet_bonus():
    operator_id = request.headers.get("userId", None)
    to_user_id = request.json.get("toUserId", None)
    fee = request.json.get("fee", None)
    if fee is None:
        return jsonify_response()
    fee = int(fee)
    # if fee <= 0:
    #     return jsonify_response()
    # if operator_id not in valid_operaters:
    #     return jsonify_response()
    transaction_manager = TransactionManager()
    pay_method = wallet_pb.Transaction.WALLET
    transaction = transaction_manager.handle_activity_bonus(
        payer_id=operator_id, payee_id=to_user_id, pay_method=pay_method, bill_fee=fee, paid_fee=fee
    )
    transaction_manager.update_transaction(transaction)
    wallet_manager = WalletManager()
    if fee > 0:
        wallet_manager.increase_balance(to_user_id, fee)
    elif fee < 0:
        wallet_manager.decrease_balance(to_user_id, fee)
    return jsonify_response()


@staff_bp.route("/wallet/bonus/undo", methods=["POST"])
def undo_wallet_bonus():
    operator_id = request.headers.get("userId", None)
    transaction_id = request.json.get("transactionId", None)
    if not transaction_id:
        return jsonify_response()
    # if operator_id not in valid_operaters:
    #     return jsonify_response()

    transaction_manager = TransactionManager()
    transaction = transaction_manager.get_transaction_by_id(transaction_id)
    if not transaction:
        return jsonify_response()
    if transaction.state != wallet_pb.Transaction.SUCCESS:
        raise errors.ShowError("已处于撤消状态，不可重复处理")
    transaction.state = wallet_pb.Transaction.REFUNDED
    transaction_manager.update_transaction(transaction)

    wallet_manager = WalletManager()
    wallet_manager.decrease_balance(transaction.payee_id, transaction.paid_fee)

    return jsonify_response()


@staff_bp.route("/wallet/bonus/list", methods=["POST"])
def wallet_bonus_list():
    user_id = request.headers.get("userId", None)
    end_create_time = request.json.get("startCreateTime", int(time.time()))
    start_create_time = request.json.get("endCreateTime", end_create_time - 60 * 60)
    latest_paid_time = request.json.get("latestPaidTime", int(time.time()))
    transaction_da = TransactionDataAccessHelper()
    transactions = transaction_da.get_transactions(
        type=wallet_pb.Transaction.ACTIVITY_BONUS, last_paid_time=latest_paid_time, orderby=[("createTime", -1)], size=100
    )
    user_da = UserDataAccessHelper()
    result = []
    for transaction in transactions:
        user_id = transaction.payee_id
        user = user_da.get_user(user_id=user_id)
        if not user:
            continue
        paid_time = transaction.paid_time
        if paid_time == 0:
            paid_time = transaction.create_time
        result.append(
            {
                "transactionId": transaction.id,
                "fee": transaction.paid_fee,
                "createTime": transaction.create_time,
                "paidTime": paid_time,
                "headImageUrl": user.member_profile.head_image_url,
                "phone": user.member_profile.mobile_phone,
                "nickname": user.member_profile.nickname,
                "state": wallet_pb.Transaction.TransactionState.Name(transaction.state),
            }
        )
    return jsonify_response(result)


@staff_bp.route("/get_userinfo", methods=["POST"])
def get_userinfo():
    phone = request.json.get("phone")
    user_da = UserDataAccessHelper()
    user = user_da.get_user_by_condition(phone=phone)
    if user is None:
        user = user_da.get_user(user_id=phone)
    if not user:
        return jsonify_response()
    wallet_da = WalletDataAccessHelper()
    wallet = wallet_da.get_user_wallet(owner_id=user.id)
    balance = 0 if wallet is None else wallet.balance
    result = {
        "phone": phone,
        "nickname": user.member_profile.nickname,
        "headImageUrl": user.member_profile.head_image_url,
        "balance": balance,
        "id": user.id,
    }
    return jsonify_response(result)

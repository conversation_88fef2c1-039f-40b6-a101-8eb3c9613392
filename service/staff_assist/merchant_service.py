# -*- coding: utf-8 -*-

import logging
import requests
import time
import os

from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
import proto.ordering.registration_pb2 as registration_pb
from flask import Blueprint
from flask import request
from flask import send_file
from google.protobuf import json_format
from business_ops.staff_manager import StaffManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.table_manager import TableManager
from business_ops.merchant_manager import MerchantManager
from business_ops.tian_que_income_manager import TianQueIncomeManager
from business_ops.tian_que_pay_manager import TianQuePayManager
from business_ops.merchant_phone_member import MerchantPhoneMemberManager
from business_ops.merchant_assist_manager import MerchantAssistManager
from business_ops.membership_manager import MembershipManager
from dao.base_helper import BaseHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import base_responses
from service import errors, error_codes
from wechat_lib.ocr import WechatOCR
from dao.staff_da_helper import StaffDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from common.request import check_body
from business_ops.printer.printer_config_manager import PrinterConfigManager
from business_ops.printer.printer_api import PRINTER_API_MAP
import proto.membership_pb2 as membership_pb

merchant_bp = Blueprint("merchant_bp", "merchant_bp", url_prefix="/staff_assist/merchant")
logger = logging.getLogger(__name__)


@merchant_bp.route("/list", methods=["GET"])
def get_merchant_list():
    manager = MerchantManager()
    merchant_list = manager.get_merchant_list()
    ret = []
    for m in merchant_list:
        ret.append({"id": m.id, "name": manager.get_merchant_name(m)})
    return base_responses.jsonify_response(ret)


@merchant_bp.route("/help/address", methods=["GET"])
def help_address():
    manager = MerchantManager()
    merchant_list = manager.get_merchant_list_multi_status()
    ret = []
    for m in merchant_list:
        if len(m.stores) == 0:
            continue
        store = m.stores[0]
        address = store.address
        city = store.poi.address_components.city
        display_name = m.basic_info.display_name
        district = store.poi.address_components.district
        phone = store.phone
        province = store.poi.address_components.province
        address = f"{province}{city}{district}{address};{phone};{display_name}"
        ret.append({"address": address})
    return base_responses.jsonify_response(ret)


@merchant_bp.route("/<string:merchant_id>", methods=["GET"])
def get_merchant(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    merchant_vo = manager.get_merchant()
    merchant_vo = json_format.MessageToDict(merchant_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(merchant_vo)


@merchant_bp.route("/<string:merchant_id>", methods=["POST"])
def update_merchant(merchant_id):
    staff_id = request.headers.get("staffId", None)
    # 封面图
    store_cover_photo_urls = request.json.get("storeCoverPhotoUrls", None)
    # 商户logo
    logo_url = request.json.get("logoUrl", None)
    display_name = request.json.get("displayName", None)
    catering_type = request.json.get("cateringType", None)
    contact_name = request.json.get("contactName", None)
    contact_phone = request.json.get("contactPhone", None)
    contact_email = request.json.get("contactEmail", None)
    address = request.json.get("address", None)
    phone = request.json.get("phone", None)
    opening_hours = request.json.get("openingHours", None)
    longitude = request.json.get("longitude", None)
    latitude = request.json.get("latitude", None)
    avg_cost_per_person = request.json.get("avgCostPerPerson", None)
    # 主业务员
    main_staff_id = request.json.get("mainStaffId", None)
    # 从业务员
    assist_staff_ids = request.json.get("assistStaffIds", None)
    province = request.json.get("province", None)
    city = request.json.get("city", None)
    district = request.json.get("district", None)
    system_message = request.json.get("systemMessage", None)
    enable_ordering_service = request.json.get("enableOrderingService", None)
    is_ramen_joint_project = request.json.get("isRamenJointProject", None)
    is_shaxian_joint_project = request.json.get("isShaxianJointProject", None)
    enable_cancel_pay_broadcast = request.json.get("enableCancelPayBroadcast", None)
    promotion_type = request.json.get("promotionType", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id, check_permission=True)
    manager.update_merchant(
        store_cover_photo_urls,
        logo_url,
        display_name,
        catering_type,
        contact_name,
        contact_phone,
        contact_email,
        address,
        phone,
        opening_hours,
        avg_cost_per_person,
        main_staff_id,
        assist_staff_ids,
        province=province,
        city=city,
        district=district,
        longitude=longitude,
        latitude=latitude,
        system_message=system_message,
        enable_ordering_service=enable_ordering_service,
        is_ramen_joint_project=is_ramen_joint_project,
        is_shaxian_joint_project=is_shaxian_joint_project,
        enable_cancel_pay_broadcast=enable_cancel_pay_broadcast,
        promotion_type=promotion_type,
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/payment/<string:merchant_id>", methods=["GET"])
def get_merchant_payment(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    payment_vo = manager.get_merchant_payment()
    payment_vo = json_format.MessageToDict(payment_vo, including_default_value_fields=True)
    payment_vo['settlementRate'] = round(payment_vo["settlementRate"], 4)
    return base_responses.jsonify_response(payment_vo)


@merchant_bp.route("/payment/signing/<string:merchant_id>", methods=["POST"])
def tian_que_signing(merchant_id, mno=None):
    if mno is None:
        mno = request.json.get("mno", None)
    if not mno:
        return base_responses.jsonify_response()
    manager = TianQuePayManager(merchant_id=merchant_id)
    # 一定要传mno过去,否则就会用原来的mno去做签约
    ret = manager.merchant_sign(mno=mno)
    resp_data = ret.get("respData", {})
    if resp_data and resp_data.get("bizMsg", {}) != "该商户已签约, 请勿重复签约":
        sign_mno = resp_data.get("retUrl").split("?")[-1]
        url = "https://openapi.tianquetech.com/merchant/sign/signContract?signContract=02&mno={}".format(sign_mno)
        result = requests.get(url)
        logger.info(f"mno: {mno} signContract: {result.content}")
        time.sleep(10)
    ret0 = manager.query_ledger_set_mno_array(mno=mno)
    ret1 = manager.query_sign_contract(mno=mno)
    ret0_biz_msg = ret0.get("respData", {}).get("bizMsg")
    ret1_biz_msg = ret1.get("respData", {}).get("bizMsg")

    manager.merchant_init(mno, "00")
    manager.create_ant_shop(mno)
    time.sleep(30)
    manager.order_query_ant_shop(mno)
    errmsg = "{};{};{}".format(ret0_biz_msg, ret1_biz_msg, manager.merchant.alipay_merchant_shop_info.id)
    raise errors.ShowError(errmsg)


@merchant_bp.route("/payment/<string:merchant_id>", methods=["POST"])
def update_merchant_payment(merchant_id):
    staff_id = request.headers.get("staffId", None)
    store_front_photo_urls = request.json.get("storeFrontPhotoUrls", None)
    store_photo_urls = request.json.get("storePhotoUrls", None)
    license_photo_url = request.json.get("licensePhotoUrl", None)
    food_distribution_permit_url = request.json.get("foodDistributionPermitUrl", None)
    id_front_photo = request.json.get("idFrontPhoto", None)
    id_front_photos = request.json.get("idFrontPhotos", None)
    id_back_photo = request.json.get("idBackPhoto", None)
    id_back_photos = request.json.get("idBackPhotos", None)
    bank_card_front_photo_url = request.json.get("bankCardFrontPhotoUrl", None)
    bank_card_front_photo_urls = request.json.get("bankCardFrontPhotoUrls", None)
    bank_account_type = request.json.get("bankAccountType", None)
    bank_name = request.json.get("bankName", None)
    branch_name = request.json.get("branchName", None)
    account_number = request.json.get("accountNumber", None)
    account_name = request.json.get("accountName", None)
    settlement_rate = request.json.get("settlementRate", None)
    bank_city = request.json.get("bankCity", None)
    mno = request.json.get("nextMno", None)
    ledger_info_url = request.json.get("ledgerInfoUrl", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id, check_permission=True)
    manager.update_merchant_payment(
        store_front_photo_urls,
        store_photo_urls,
        license_photo_url,
        food_distribution_permit_url,
        id_front_photo,
        id_back_photo,
        bank_card_front_photo_url,
        bank_account_type,
        bank_name,
        branch_name,
        account_number,
        account_name,
        settlement_rate,
        bank_city,
        bank_card_front_photo_urls=bank_card_front_photo_urls,
        id_front_photos=id_front_photos,
        id_back_photos=id_back_photos,
        ledger_info_url=ledger_info_url,
    )
    tian_que_signing(merchant_id, mno)

    doincome = request.json.get("doincome", None)
    if not doincome:
        return base_responses.jsonify_response()

    operational_type = request.json.get("operationalType", None)
    have_license_no = request.json.get("haveLicenseNo", None)
    mec_type_flag = request.json.get("mecTypeFlag", None)
    parent_no = request.json.get("parentNo", None)
    indenpendent_model = request.json.get("indenpendentModel", None)
    qrcode_list = request.json.get("qrcodeList", None)
    bank_card_rate = request.json.get("bankCardRate", None)
    settle_type = request.json.get("settleType", None)
    online_type = request.json.get("onlineType", None)
    online_name = request.json.get("onlineName", None)
    online_type_info = request.json.get("onlineTypeInfo", None)
    cpr_reg_nm_cn = request.json.get("cprRegNmCn", None)
    regist_code = request.json.get("registCode", None)
    prov_cd = request.json.get("provCd", None)
    city_cd = request.json.get("cityCd", None)
    dist_cd = request.json.get("distCd", None)
    detail_address = request.json.get("detailAddress", None)
    org_code = request.json.get("orgCode", None)
    tax_reg_no = request.json.get("taxRegNo", None)
    cpr_reg_addr = request.json.get("cprRegAddr", None)
    reg_prov_cd = request.json.get("regProvCd", None)
    reg_city_cd = request.json.get("regCityCd", None)
    reg_dist_cd = request.json.get("regDistCd", None)
    mcc_cd = request.json.get("mccCd", None)
    identity_typ = request.json.get("identityTyp", None)
    identity_no = request.json.get("identityNo", None)
    act_nm = request.json.get("actNm", None)
    act_typ = request.json.get("actTyp", None)
    stm_man_id_no = request.json.get("stmManIdNo", None)
    act_no = request.json.get("actNo", None)
    depo_bank = request.json.get("depoBank", None)
    depo_prov_cd = request.json.get("depoProvCd", None)
    depo_city_cd = request.json.get("depoCityCd", None)
    lbnk_no = request.json.get("lbnkNo", None)
    lbnk_nm = request.json.get("lbnkNm", None)
    license_pic = request.json.get("licensePic", None)
    tax_regist_license_pic = request.json.get("taxRegistLicensePic", None)
    org_code_pic = request.json.get("orgCodePic", None)
    legal_personid_positive_pic = request.json.get("legalPersonidPositivePic", None)
    legal_personid_opposite_pic = request.json.get("legalPersonidOppositePic", None)
    opening_account_license_pic = request.json.get("openingAccountLicensePic", None)
    bank_card_positive_pic = request.json.get("bankCardPositivePic", None)
    bank_card_opposite_pic = request.json.get("bankCardOppositePic", None)
    settle_person_idcard_opposite = request.json.get("settlePersonIdcardOpposite", None)
    settle_person_idcard_positive = request.json.get("settlePersonIdcardPositive", None)
    store_pic = request.json.get("storePic", None)
    inside_scene_pic = request.json.get("insideScenePic", None)
    ipc_licence = request.json.get("ipcLicence", None)
    letter_of_auth_pic = request.json.get("letterOfAuthPic", None)
    union_settle_without_license = request.json.get("unionSettleWithoutLicense", None)

    manager = TianQueIncomeManager(merchant_id=merchant_id)
    manager.income(
        operational_type=operational_type,
        have_license_no=have_license_no,
        mec_type_flag=mec_type_flag,
        parent_no=parent_no,
        indenpendent_model=indenpendent_model,
        qrcode_list=qrcode_list,
        bank_card_rate=bank_card_rate,
        settle_type=settle_type,
        online_type=online_type,
        online_name=online_name,
        online_type_info=online_type_info,
        cpr_reg_nm_cn=cpr_reg_nm_cn,
        regist_code=regist_code,
        prov_cd=prov_cd,
        city_cd=city_cd,
        dist_cd=dist_cd,
        detail_address=detail_address,
        org_code=org_code,
        tax_reg_no=tax_reg_no,
        cpr_reg_addr=cpr_reg_addr,
        reg_prov_cd=reg_prov_cd,
        reg_city_cd=reg_city_cd,
        reg_dist_cd=reg_dist_cd,
        mcc_cd=mcc_cd,
        identity_typ=identity_typ,
        identity_no=identity_no,
        act_nm=act_nm,
        act_typ=act_typ,
        stm_man_id_no=stm_man_id_no,
        act_no=act_no,
        depo_bank=depo_bank,
        depo_prov_cd=depo_prov_cd,
        depo_city_cd=depo_city_cd,
        lbnk_no=lbnk_no,
        lbnk_nm=lbnk_nm,
        license_pic=license_pic,
        tax_regist_license_pic=tax_regist_license_pic,
        org_code_pic=org_code_pic,
        legal_personid_opposite_pic=legal_personid_opposite_pic,
        legal_personid_positive_pic=legal_personid_positive_pic,
        opening_account_license_pic=opening_account_license_pic,
        bank_card_positive_pic=bank_card_positive_pic,
        bank_card_opposite_pic=bank_card_opposite_pic,
        settle_person_idcard_opposite=settle_person_idcard_opposite,
        settle_person_idcard_positive=settle_person_idcard_positive,
        store_pic=store_pic,
        inside_scene_pic=inside_scene_pic,
        ipc_licence=ipc_licence,
        letter_of_auth_pic=letter_of_auth_pic,
        union_settle_without_license=union_settle_without_license,
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/tian_que/update_pic_id_url/<string:merchant_id>", methods=["POST"])
def update_pic_id_url(merchant_id):
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    id = request.json.get("id", None)
    url = request.json.get("url", None)
    manager.update_tian_que_pic_id_url(id, url)
    return base_responses.jsonify_response()


@merchant_bp.route("/tian_que/query_income_result/<string:merchant_id>", methods=["GET"])
def query_income_result(merchant_id):
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    result = manager.query_income_result()
    if result:
        return base_responses.jsonify_response(result)
    return base_responses.jsonify_response()


@merchant_bp.route("/tian_que/income_info/<string:merchant_id>", methods=["GET"])
def query_income_info(merchant_id):
    manager = TianQueIncomeManager(merchant_id=merchant_id)
    info = manager.query_income_info()
    if info:
        return base_responses.jsonify_response(info)
    return base_responses.jsonify_response()


@merchant_bp.route("/pos_info/<string:merchant_id>", methods=["GET"])
def get_merchant_pos_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    pos_info_vo = manager.get_pos_info()
    if pos_info_vo:
        pos_info_vo = json_format.MessageToDict(pos_info_vo, including_default_value_fields=True)
        return base_responses.jsonify_response(pos_info_vo)
    return base_responses.jsonify_response()


@merchant_bp.route("/pos_info/<string:merchant_id>", methods=["POST"])
def update_merchant_pos_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    pos_type = request.json.get("posType", "KERUYUN")

    # 客如云
    long_password = request.json.get("longPassword", None)
    short_password = request.json.get("shortPassword", None)
    shop_id = request.json.get("shopId", None)
    group_id = request.json.get("groupId", None)
    account = request.json.get("account", None)

    # 哗啦啦
    app_secret = request.json.get("appSecret", None)
    app_key = request.json.get("appKey", None)

    # 云打印
    one_dish_cut = request.json.get("oneDishCut", None)
    feie_printer_sns = request.json.get("feiePrinterSns", None)
    merchant_print = request.json.get("merchantPrint", None)
    kitchen_print = request.json.get("kitchenPrint", None)
    enable_dish_sort = request.json.get("enableDishSort", None)
    enable_x_sign_before_amount = request.json.get("enableXSignBeforeAmount", None)

    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_pos_info(
        pos_type=pos_type,
        long_password=long_password,
        short_password=short_password,
        shop_id=shop_id,
        account=account,
        group_id=group_id,
        app_secret=app_secret,
        app_key=app_key,
        feie_printer_sns=feie_printer_sns,
        one_dish_cut=one_dish_cut,
        enable_dish_sort=enable_dish_sort,
        merchant_print=merchant_print,
        kitchen_print=kitchen_print,
        print_format_type=request.json.get("printFormatType", None),
        enable_x_sign_before_amount=enable_x_sign_before_amount,
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/pos_info/update/<string:merchant_id>", methods=["POST"])
def update_feie_pos_info(merchant_id):
    """修改飞鹅打印机数据"""
    feie_printer_sns = request.json.get("feiePrinterSns", None)
    manager = StaffManager(staff_id=None, merchant_id=merchant_id)
    manager.amend_feie_pos_info(feie_printer_sns=feie_printer_sns)
    return base_responses.jsonify_response()


@merchant_bp.route("/pos_info/delete/<string:merchant_id>", methods=["POST"])
def delete_feie_pos_info(merchant_id):
    """删除飞鹅打印机绑定数据"""
    staff_id = request.headers.get("staffId", None)
    staff = StaffDataAccessHelper().get_staff(staff_id)
    if not staff:
        raise errors.Error(err=error_codes.NO_PERMISSION)
    printer_sn = request.json.get("printerSn", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.delete_feie_pos_info(printer_sn=printer_sn)
    return base_responses.jsonify_response()


@merchant_bp.route("/pos_info/query", methods=["POST"])
def query_feie_pos_belong():
    """通过 sn 编号查询该机器属于哪个商户（已绑定的前提）"""
    printer_sn = request.json.get("printerSn", None)
    if not printer_sn:
        raise errors.Error(err=(99999, "打印机编号不能为空"))
    order_service_da = OrderingServiceDataAccessHelper()
    matcher = {'printerConfig.feiePrinter.printerSns': printer_sn}
    store_names = []
    merchant_da = MerchantDataAccessHelper()
    ret = order_service_da.get_registration_info_by_matcher(matcher)
    if not ret:
        return base_responses.jsonify_response()
    for i in ret:
        merchant = merchant_da.get_merchant_by_id(i.merchant_id)
        if merchant is not None:
            store_names.append(merchant.basic_info.display_name)
    if len(store_names) > 1:
        merchant_names = {"merchantNames": ",".join(store_names)}
    else:
        merchant_names = {"merchantNames": "".join(store_names)}
    return base_responses.jsonify_response(merchant_names)


@merchant_bp.route("/ordering_info/<string:merchant_id>", methods=["GET"])
def get_merchant_ordering_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    ordering_vo = manager.get_ordering_info()
    ordering_vo = json_format.MessageToDict(ordering_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(ordering_vo)


@merchant_bp.route("/ordering_info/<string:merchant_id>", methods=["POST"])
def update_merchant_ordering_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    enable_eat_in = request.json.get("enableEatIn", None)
    enable_take_away = request.json.get("enableTakeAway", None)
    enable_self_pick_up = request.json.get("enableSelfPickUp", None)
    enable_take_out = request.json.get("enableTakeOut", None)
    enable_people_count = request.json.get("enablePeopleCount", None)
    required_order_items = request.json.get("requiredOrderItems", None)
    packaging_box_config = request.json.get("packagingBoxConfig", None)
    shipping_fee = request.json.get("shippingFee", None)
    enable_shipping_fee = request.json.get("enableShippingFee", None)
    minimal_bill_fee = request.json.get("minimalBillFee", None)
    max_take_out_distance = request.json.get("maxTakeOutDistance", None)
    take_out_commission_rate = request.json.get("takeOutCommissionRate", None)
    take_away_commission_rate = request.json.get("takeAwayCommissionRate", None)
    self_pick_up_commission_rate = request.json.get("selfPickUpCommissionRate", None)
    package_box_type = request.json.get("packageBoxType", None)
    show_full_mobile_phone = request.json.get("showFullMobilePhone")
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_ordering_info(
        enable_eat_in=enable_eat_in,
        enable_take_away=enable_take_away,
        enable_self_pick_up=enable_self_pick_up,
        enable_take_out=enable_take_out,
        enable_people_count=enable_people_count,
        required_order_items=required_order_items,
        packaging_box_config=packaging_box_config,
        shipping_fee=shipping_fee,
        minimal_bill_fee=minimal_bill_fee,
        max_take_out_distance=max_take_out_distance,
        take_out_commission_rate=take_out_commission_rate,
        take_away_commission_rate=take_away_commission_rate,
        self_pick_up_commission_rate=self_pick_up_commission_rate,
        enable_shipping_fee=enable_shipping_fee,
        package_box_type=package_box_type,
        show_full_mobile_phone=show_full_mobile_phone,
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/customize_info/<string:merchant_id>", methods=["GET"])
def get_customize_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    customize_info_vo = manager.get_customize_info()
    customize_info_vo = json_format.MessageToDict(customize_info_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(customize_info_vo)


@merchant_bp.route("/customize_info/<string:merchant_id>", methods=["POST"])
def update_customize_info(merchant_id):
    staff_id = request.headers.get("staffId", None)
    business_type = request.json.get("businessType", None)
    pay_type = request.json.get("payType", None)
    enable_payment_reminder = request.json.get("enablePaymentReminder", None)
    check_out_print = request.json.get("checkOutPrint", None)
    enable_invoice = request.json.get("enableInvoice", None)
    enable_shilai_member_card_pay = request.json.get("enableShilaiMemberCardPay", None)
    enable_shilai_member_card_recharge = request.json.get("enableShilaiMemberCardRecharge", None)
    splash_image_url = request.json.get("splashImageUrl", None)
    splash_mode = request.json.get("splashMode", None)
    banner_mode = request.json.get("bannerMode", None)
    enable_ordering_coupon_package_union_pay = request.json.get("enableOrderingCouponPackageUnionPay", None)
    enable_coupon_package_refund = request.json.get("enableCouponPackageRefund", None)
    enable_fanpiao_refund = request.json.get("enableFanpiaoRefund", None)
    enable_shilai_promotion_splash = request.json.get("enableShilaiPromotionSplash", None)
    enable_show_sold_out_dishes = request.json.get("enableShowSoldOutDishes", None)
    enable_invite_share = request.json.get("enableInviteShare", None)
    dish_discount_rate = request.json.get("dishDiscountRate", None)
    red_packet_discount_rate = request.json.get("redPacketDiscountRate", None)
    enable_subsidies_overflow_fee = request.json.get("enableSubsidiesOverflowFee", None)
    disable_show_fanpiao_purchase_number = request.json.get("disableShowFanpiaoPurchaseNumber", None)
    disable_show_fanpiao_price = request.json.get('disableShowFanpiaoPrice', None)
    disable_show_coupon_package_purchase_number = request.json.get("disableShowCouponPackagePurchaseNumber", None)
    fanpiao_sales_boost_factor = request.json.get("fanpiaoSalesBoostFactor", None)
    enable_show_time_limit_sale = request.json.get("enableShowTimeLimitSale", None)
    enable_dish_incremental = request.json.get("enableDishIncremental", None)
    disable_show_sold_number = request.json.get("disableShowSoldNumber", None)
    meal_code_base_value = request.json.get("mealCodeBaseValue", None)
    serial_number_base_value = request.json.get("serialNumberBaseValue", None)
    meal_code_max_value = request.json.get("mealCodeMaxValue", None)
    serial_number_max_value = request.json.get("serialNumberMaxValue", None)
    enterprise_wechat_number = request.json.get("enterpriseWechatNumber", None)
    enable_auto_recover_sold_out = request.json.get("enableAutoRecoverSoldOut", None)
    enable_auto_recover_remain_quantity = request.json.get("enableAutoRecoverRemainQuantity", None)
    enable_auto_recover_attr_sold_out = request.json.get("enableAutoRecoverAttrSoldOut", None)
    enable_auto_recover_supply_condiments_sold_out = request.json.get("enableAutoRecoverSupplyCondimentsSoldOut", None)
    enforce_phone_registration = request.json.get("enforcePhoneRegistration", None)
    advertising_info = request.json.get("advertisingInfo", None)
    display_advertising_info = request.json.get("displayAdvertisingInfo", None)
    store_mp_image_url = request.json.get("storeMpImageUrl", None)
    enable_number_plate_pay_with_marketing = request.json.get("enableNumberPlatePayWithMarketing", None)
    enable_number_plate_pay_with_coupon_package = request.json.get("enableNumberPlatePayWithCouponPackage", None)
    enable_number_plate_pay_with_fanpiao = request.json.get("enableNumberPlatePayWithFanpiao", None)
    enable_fanpiao_balance_refund = request.json.get("enableFanpiaoBalanceRefund", None)
    is_refund_the_way = request.json.get("isRefundTheWay", None)
    balance_refund_method = request.json.get("balanceRefundMethod", None)
    enable_number_plate_splash = request.json.get("enableNumberPlateSplash", None)
    enable_number_plate_coupon_splash = request.json.get("enableNumberPlateCouponSplash", None)
    disable_number_plate_pay_fanpiao_pay = request.json.get("disableNumberPlatePayFanpiaoPay", None)
    # 点餐完成之后禁止显示流水号
    disable_display_serial_number_after_ordering = request.json.get('disableDisplaySerialNumberAfterOrdering', None)
    enable_firstly_addon = request.json.get("enableFirstlyAddon")
    image_size = request.json.get('imageSize')
    disable_show_ad = request.json.get('disableShowAd')
    red_packet_discount = request.json.get('redPacketDiscount', None)
    shilai_extra_discount = request.json.get('shilaiExtraDiscount', None)
    block_rate = request.json.get('blockRate', None)
    member_card_recharge_rule = request.json.get('memberCardRechargeRule', None)

    package_type = request.json.get('packageType')

    operating_status = request.json.get("operatingStatus")

    is_pre_order = request.json.get("isPreOrder")
    is_pre_order_show_meal_code = request.json.get('isPreOrderShowMealCode')
    enable_many_people_order = request.json.get("enableManyPeopleOrder")
    enable_table_serial_number = request.json.get("enableTableSerialNumber")
    enable_show_membership = request.json.get('enableShowMembership')
    pay_success_ad_config = request.json.get('paySuccessAdConfig')

    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_splash_image(splash_image_url, splash_mode)
    manager.update_customize_info(
        enable_fanpiao_balance_refund=enable_fanpiao_balance_refund,
        is_refund_the_way=is_refund_the_way,
        balance_refund_method=balance_refund_method,
        business_type=business_type,
        pay_type=pay_type,
        enable_payment_reminder=enable_payment_reminder,
        check_out_print=check_out_print,
        enable_invoice=enable_invoice,
        enable_shilai_member_card_pay=enable_shilai_member_card_pay,
        enable_shilai_member_card_recharge=enable_shilai_member_card_recharge,
        enable_ordering_coupon_package_union_pay=enable_ordering_coupon_package_union_pay,
        enable_coupon_package_refund=enable_coupon_package_refund,
        enable_fanpiao_refund=enable_fanpiao_refund,
        enable_shilai_promotion_splash=enable_shilai_promotion_splash,
        enable_show_sold_out_dishes=enable_show_sold_out_dishes,
        enable_invite_share=enable_invite_share,
        dish_discount_rate=dish_discount_rate,
        red_packet_discount_rate=red_packet_discount_rate,
        enable_subsidies_overflow_fee=enable_subsidies_overflow_fee,
        disable_show_fanpiao_purchase_number=disable_show_fanpiao_purchase_number,
        disable_show_coupon_package_purchase_number=disable_show_coupon_package_purchase_number,
        fanpiao_sales_boost_factor=fanpiao_sales_boost_factor,
        enable_show_time_limit_sale=enable_show_time_limit_sale,
        enable_dish_incremental=enable_dish_incremental,
        banner_mode=banner_mode,
        disable_show_sold_number=disable_show_sold_number,
        meal_code_base_value=meal_code_base_value,
        meal_code_max_value=meal_code_max_value,
        enterprise_wechat_number=enterprise_wechat_number,
        enable_auto_recover_sold_out=enable_auto_recover_sold_out,
        enable_auto_recover_remain_quantity=enable_auto_recover_remain_quantity,
        enable_auto_recover_attr_sold_out=enable_auto_recover_attr_sold_out,
        enable_auto_recover_supply_condiments_sold_out=enable_auto_recover_supply_condiments_sold_out,
        enforce_phone_registration=enforce_phone_registration,
        advertising_info=advertising_info,
        display_advertising_info=display_advertising_info,
        store_mp_image_url=store_mp_image_url,
        serial_number_base_value=serial_number_base_value,
        serial_number_max_value=serial_number_max_value,
        enable_number_plate_pay_with_coupon_package=enable_number_plate_pay_with_coupon_package,
        enable_number_plate_pay_with_fanpiao=enable_number_plate_pay_with_fanpiao,
        enable_number_plate_pay_with_marketing=enable_number_plate_pay_with_marketing,
        enable_number_plate_splash=enable_number_plate_splash,
        disable_number_plate_pay_fanpiao_pay=disable_number_plate_pay_fanpiao_pay,
        disable_display_serial_number_after_ordering=disable_display_serial_number_after_ordering,
        enable_firstly_addon=enable_firstly_addon,
        enable_number_plate_coupon_splash=enable_number_plate_coupon_splash,
        image_size=image_size,
        disable_show_ad=disable_show_ad,
        red_packet_discount=red_packet_discount,
        shilai_extra_discount=shilai_extra_discount,
        block_rate=block_rate,
        package_type=package_type,
        member_card_recharge_rule=member_card_recharge_rule,
        disable_show_fanpiao_price=disable_show_fanpiao_price,
        operating_status=operating_status,
        is_pre_order=is_pre_order,
        is_pre_order_show_meal_code=is_pre_order_show_meal_code,
        enable_many_people_order=enable_many_people_order,
        enable_table_serial_number=enable_table_serial_number,
        enable_show_membership=enable_show_membership,
        pay_success_ad_config=pay_success_ad_config,
    )
    # pos_merchant_manager = ShilaiPosMerchantManager(merchant_id=merchant_id)
    return base_responses.jsonify_response()


@merchant_bp.route("/marketing_config/<string:merchant_id>", methods=["GET"])
def get_merchant_marketing_config(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    marketing_config_vo = manager.get_merchant_marketing_config()
    marketing_config_vo = json_format.MessageToDict(marketing_config_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(marketing_config_vo)


@merchant_bp.route("/marketing_config/<string:merchant_id>", methods=["POST"])
def update_merchant_marketing_config(merchant_id):
    staff_id = request.headers.get("staffId", None)
    enable_fanpiao = request.json.get("enableFanpiao", None)
    enable_buy_fanpiao = request.json.get("enableBuyFanpiao", None)
    max_discount = request.json.get("maxDiscount", None)
    fanpiao_and_coupon_discount = request.json.get("fanpiaoAndCouponDiscount", None)
    fanpiao_pay_commission_rate = request.json.get("fanpiaoPayCommissionRate", None)
    is_demonstration_merchant = request.json.get("isDemonstrationMerchant", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_merchant_marketing_config(
        enable_fanpiao=enable_fanpiao,
        enable_buy_fanpiao=enable_buy_fanpiao,
        max_discount=max_discount,
        fanpiao_and_coupon_discount=fanpiao_and_coupon_discount,
        fanpiao_pay_commission_rate=fanpiao_pay_commission_rate,
        is_demonstration_merchant=is_demonstration_merchant,
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/coupon_package/<string:merchant_id>", methods=["POST"])
def coupon_package_category(merchant_id):
    category_id = request.json.get("categoryId", None)
    display_scene = request.json.get("displayScene", None)
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_coupon_package_category(category_id, display_scene=display_scene)
    return base_responses.jsonify_response()


@merchant_bp.route("/fanpiao_category/<string:merchant_id>", methods=["POST"])
def update_fanpiao_category(merchant_id):
    staff_id = request.headers.get("staffId", None)
    fanpiao_category_id = request.json.get("fanpiaoCategoryId", None)
    state = request.json.get("state", None)
    base_selling_quantity = request.json.get("baseSellingQuantity", None)
    display_scene = request.json.get("displayScene", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.update_fanpiao_category(
        fanpiao_category_id, state, base_selling_quantity=base_selling_quantity, display_scene=display_scene
    )
    return base_responses.jsonify_response()


@merchant_bp.route("/fanpiao_categories/<string:merchant_id>", methods=["GET"])
def get_fanpiao_categories(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    fanpiao_categories = manager.get_fanpiao_categories()
    categories = [json_format.MessageToDict(category, including_default_value_fields=True) for category in fanpiao_categories]
    return base_responses.jsonify_response(categories)


@merchant_bp.route("/sync_dishes/<string:merchant_id>", methods=["POST"])
def sync_dishes(merchant_id):
    staff_id = request.headers.get("staffId", None)
    manager = StaffManager(staff_id=staff_id, merchant_id=merchant_id)
    manager.sync_dish_with_one_button()
    return base_responses.jsonify_response()


@merchant_bp.route("/merchant_assist/qrcode/<string:merchant_id>", methods=["POST"])
def create_merchant_assist_qrcode(merchant_id):
    staff_id = request.headers.get("staffId", None)
    url = BaseHelper().create_merchant_assist_login_qrcode(merchant_id, staff_id)
    return base_responses.jsonify_response({"url": url})


@merchant_bp.route("/sync_dish_categories/<string:merchant_id>", methods=["POST"])
def sync_dish_categories(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    dish_categories = manager.async_categories()
    dish_categories = [json_format.MessageToDict(category) for category in dish_categories]
    return base_responses.jsonify_response(dish_categories)


@merchant_bp.route("/recommended_food/<string:merchant_id>", methods=["GET"])
def get_recommended_foods(merchant_id):
    manager = StaffManager(merchant_id=merchant_id)
    recommend_food_vo = manager.get_recommend_foods()
    recommend_food_vo = json_format.MessageToDict(recommend_food_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(recommend_food_vo)


@merchant_bp.route("/recommended_food/<string:merchant_id>", methods=["POST"])
def set_recommended_foods(merchant_id):
    manager = StaffManager(merchant_id=merchant_id)
    recommended_dish_ids = request.json.get("recommendedDishIds", None)
    recommended_food_photo_urls = request.json.get("recommendedFoodPhotoUrls", None)
    enable_recommend_dish = request.json.get("enableRecommendDish", None)
    manager.set_recommend_foods(recommended_food_photo_urls, recommended_dish_ids, enable_recommend_dish)
    return base_responses.jsonify_response()


@merchant_bp.route("/dish_list/<string:merchant_id>", methods=["GET"])
def get_dish_list(merchant_id):
    manager = StaffManager(merchant_id=merchant_id)
    dish_list_vo = manager.get_merchant_dishes()
    dish_list_vo = json_format.MessageToDict(dish_list_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(dish_list_vo)


@merchant_bp.route("/sync/tables/<string:merchant_id>", methods=["POST"])
def sync_tables(merchant_id):
    """
    本地生成桌台二维码图片
    """
    table_names = TableManager(merchant_id=merchant_id).sync_tables()
    tools_server_domain = os.environ.get("TOOLS_SERVER_DOMAIN", "http://tools.server")
    url = f"{tools_server_domain}/tools/create-qrcode/{merchant_id}"
    resp = requests.post(url, verify=False, headers={"Host": "tools.server"})
    if resp.status_code != 200:
        raise Exception(f"sync_tables error, url={url}, status={resp.status_code}, error_msg={resp.text}, reason={resp.reason}")
    return base_responses.jsonify_response({"tableNames": table_names})


@merchant_bp.route("/pickup/tables/<string:merchant_id>", methods=["POST"])
def pickup_tables(merchant_id):
    meal_type = request.json.get("mealType", None)
    manager = TableManager(merchant_id=merchant_id)
    manager.batch_set_table(meal_type)
    return base_responses.jsonify_response()


@merchant_bp.route("/dish/fuzzy_match", methods=["GET"])
def search_dish_by_name():
    staff_id = request.headers.get("staffId", None)
    dish_name = request.args.get("dishName", None)
    most = int(request.args.get("most", 10))
    manager = StaffManager(staff_id=staff_id)
    dishes = manager.fuzzy_match_dish_name(dish_name=dish_name, most=most)
    return base_responses.jsonify_response(dishes)


@merchant_bp.route("/dish/fuzzy_match", methods=["POST"])
def add_dish_fuzzy_match():
    staff_id = request.headers.get("staffId", None)
    dish_name = request.json.get("dishName", None)
    image_url = request.json.get("imageUrl", None)
    manager = StaffManager(staff_id=staff_id)
    manager.fuzzy_match_add_dish(dish_name, image_url)
    return base_responses.jsonify_response()


@merchant_bp.route("/dish/image/compress/<string:merchant_id>", methods=["POST"])
def dish_image_compress(merchant_id):
    """商户菜品图片压缩"""
    manager = DishManager(merchant_id=merchant_id)
    download_url = manager.compress_dish_image()
    return base_responses.jsonify_response({"downloadUrl": download_url})


@merchant_bp.route("/dish/update/<string:merchant_id>", methods=["POST"])
def update_dish(merchant_id):
    dishes = request.json.get("dishes", None)
    session = request.json.get("session", None)
    if not session:
        raise errors.ShowError("请先登陆")
    manager = DishManager(merchant_id=merchant_id)
    manager.staff_update_dish(dishes, session=session)
    return base_responses.jsonify_response()


@merchant_bp.route("/import/phone_member/<string:merchant_id>", methods=["GET", "POST"])
def import_phone_member(merchant_id):
    file, phones = None, None
    if request.files:
        file = request.files.get("file")
    if request.json:
        phones = request.json.get("phones", None)
    if not file and not phones:
        return base_responses.jsonify_response()
    manager = MerchantPhoneMemberManager(merchant_id=merchant_id)
    manager.import_merchant_phones(file, phones)
    return base_responses.jsonify_response()


@merchant_bp.route("/import/membership/balance/<string:merchant_id>", methods=["POST"])
def import_membership_balance(merchant_id):
    """飞鹅模式商家导入会员储值余额
    1. file: 以txt文件的方式传入数据,每一行的格式为: 手机号,金额
    2. phones: 以数组的方式传入数据: [{"phone": "1234567", "balance": 123}]
    a). 如果手机号已经绑定到某个用户了,那么会直接加到这个用户账户上
    b). 如果手机号没有绑定到用户,那么会做临时存储,等用户下次进入小程序时会同步到账户上
    3. increase:
      如果是上面a情况,如果increase不传或者传false,那么新的值会覆盖旧的值. 如果为true,那么会新增到用户账户上
      如果是a情况,不论increase是什么值,统一为覆盖
    金额单位统一为：元
    """
    file, phones = None, None
    if request.files:
        file = request.files.get("file")
    increase = False
    if request.json:
        phones = request.json.get("phones", None)
        increase = request.json.get("increase", False)
    if file is None and phones is None:
        return base_responses.jsonify_response()
    manager = MembershipManager(merchant_id=merchant_id, increase=increase)
    error_phones = manager.import_membership_balance(phones=phones, file=file)
    if not error_phones:
        return base_responses.jsonify_response()
    err_msg = ",".join(error_phones)
    raise errors.ShowError(f"导入出错: {err_msg}")


@merchant_bp.route("/export/membership/balance/<string:merchant_id>", methods=["GET", "POST"])
def export_membership_balance(merchant_id):
    manager = MembershipManager(merchant_id=merchant_id)
    output = manager.export_membership_balance(title='用户储值')
    return send_file(output, attachment_filename=f"用户储值-{merchant_id}.xlsx", as_attachment=True, cache_timeout=1)


@merchant_bp.route("/update/config/<string:merchant_id>", methods=["POST"])
def update_config(merchant_id):
    staff_id = request.headers.get("staffId", None)
    # configs是字典
    configs = request.json.get("configs", None)
    manager = StaffManager(merchant_id=merchant_id, staff_id=staff_id)
    manager.update_config(**configs)
    return base_responses.jsonify_response()


@merchant_bp.route("/random-scene/<string:merchant_id>", methods=["GET"])
def get_random_scene(merchant_id):
    ordering_da = OrderingServiceDataAccessHelper()
    table = ordering_da.get_table(merchant_id=merchant_id)
    return base_responses.jsonify_response({"scene": table.id})


@merchant_bp.route("/staff/white-list", methods=["GET"])
def get_staff_white_list():
    user_ids = [
        "c97cf5c1-407d-402c-b573-b90c3d88c353",  # "宋美丽"
        "c61fa7c8-db70-4e18-af6e-4c387a148950",  # "谢昭辔"
        "1dbb0d87-18a6-4b86-913f-2ee426ec5e78",  # "Yuan小蛋"
        "0e3ed7f7-d6f8-413b-b11d-e651c462d347",  # "Jeslyn"
        "1379ac90-49f9-41f7-a220-2882ba240599",  # "杰"
        "3051be5e-97c2-4b19-9780-28fdab6d191f",  # 魏华夏
        "e5fcf77d-178f-4221-8d1d-213e3abc95c8",  # 刘垒
        "bbf500a6-a1ad-4268-9ed8-a39c70981bb9",  # 杨杰锋
    ]
    return base_responses.jsonify_response(user_ids)


@merchant_bp.route("/wechat-lib/ocr", methods=["POST"])
def wechat_lib_ocr():
    type = "idcard"
    if request.json:
        type = request.json.get("type", None)
    f = request.files['file']
    wechat_ocr = WechatOCR()
    ret = wechat_ocr.ocr(f, type)
    return base_responses.jsonify_response(ret)


@merchant_bp.route("/feie/printers", methods=["GET"])
def get_printers_info():
    """ """
    merchant_id = request.args.get("merchantId", None)
    manager = MerchantAssistManager(merchant_id=merchant_id)
    printers = manager.get_feie_printers()
    return base_responses.jsonify_response(printers)


@merchant_bp.route("/printer_config/<string:merchant_id>", methods=["GET"])
def get_printer_config(merchant_id):
    body = check_body(
        {
            "merchant_id": {"required": True, "type": str},
        }
    )
    printer_config_manager = PrinterConfigManager()
    return printer_config_manager.get_config(**body)


@merchant_bp.route("/printer_config/<string:merchant_id>", methods=["POST"])
def update_printer_config(merchant_id):
    body = check_body(
        {
            "merchant_id": {"required": True, "type": str},
            "registration_config": {"required": True, "type": dict},
            "printer_configs": {"required": True, "type": list},
        }
    )
    printer_config_manager = PrinterConfigManager()
    printer_config_manager.update_config(**body)


@merchant_bp.route("/printer_config/set_voice/<string:merchant_id>", methods=["POST"])
def set_printer_voice(merchant_id):
    body = check_body(
        {
            "merchant_id": {"required": True, "type": str},
            "printer_sn": {"required": True, "type": str},
            "printer_type": {
                "required": True,
                "type": str,
                "in": [item.name for item in registration_pb.PrinterConfigByType.Type.DESCRIPTOR.values],
            },
            "voice": {
                "required": True,
                "type": str,
                "in": [item.name for item in registration_pb.PrinterConfigByType.VoiceType.DESCRIPTOR.values],
            },
        }
    )
    printer_sn = body.printer_sn
    printer_api = PRINTER_API_MAP[body.printer_type]
    if not printer_api.set_voice(printer_sn, body.voice):
        raise errors.Error(err=error_codes.PRINTER_CONFIG_SET_VOICE_ERROR, printer_sn=printer_sn)
    printer_config_manager = PrinterConfigManager()
    return printer_config_manager.add_or_update(
        {'voice': body.voice}, matcher={"merchant_id": merchant_id, "printer_sn": printer_sn}
    )


@merchant_bp.route("/dish/clear/<string:merchant_id>", methods=["GET", "POST"])
def clear_dish(merchant_id):
    if not merchant_id:
        raise errors.ShowError("商家ID不存在")

    manager = DishManager(merchant_id=merchant_id)
    return base_responses.jsonify_response(manager.clear_dish())


@merchant_bp.route("/merchant_user/clear/<string:merchant_id>", methods=["GET", "POST"])
def clear_merchant_user(merchant_id):
    if not merchant_id:
        raise errors.ShowError("商家ID不存在")
    merchant_user_da = MerchantUserDataAccessHelper()
    users = merchant_user_da.get_users(merchant_id)
    user_ids = []
    for user in users:
        for m in user.merchants:
            if m.merchant_id != merchant_id:
                continue
            user.merchants.remove(m)
            merchant_user_da.update_or_create_user(user)
            user_ids.append(user.id)
            break
    return base_responses.jsonify_response(user_ids)


@merchant_bp.route("/recharge_config/<string:merchant_id>", methods=["GET"])
def get_recharge_config(merchant_id):
    """获取会员充值列表"""
    membership_da = MembershipDataAccessHelper()
    status = membership_pb.MemberCardRechargeConfig.ACTIVE
    configs = membership_da.get_member_card_recharge_configs(merchant_id, status=status)
    configs.sort(key=lambda config: config.sell_price)
    configs = [json_format.MessageToDict(config, including_default_value_fields=True) for config in configs]
    return base_responses.jsonify_response(configs)

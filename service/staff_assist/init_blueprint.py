# -*- coding: utf-8 -*-

import logging

from flask import request

from common.utils.log_utils import LogUtils
from service import base_responses
from service import errors
from service import error_codes
from service.staff_assist.merchant_service import merchant_bp
from service.staff_assist.staff_service import staff_bp

logger = logging.getLogger(__name__)


def init_blueprint(app):
    blueprints = [
        merchant_bp, staff_bp
    ]

    @app.errorhandler(404)
    def error_404(e):
        status_response = {'errcode': error_codes.PAGE_NOT_FOUND, 'errmsg': error_codes.PAGE_NOT_FOUND_MSG}
        return base_responses.jsonify_response(status_response=status_response)

    @app.errorhandler(Exception)
    def error_handler(e):
        logger.exception(e)
        if isinstance(e, errors.Error):
            status_response = {
                'errcode': e.errcode,
                'errmsg': e.errmsg
            }
        else:
            status_response = {
                "errcode": error_codes.SERVER_ERROR,
                "errmsg": error_codes.SERVER_ERROR_MSG
            }
        return base_responses.jsonify_response(status_response=status_response)

    @app.after_request
    def response_json(response):
        """ 记录请求参数和返回的errcode和errmsg
        """
        try:
            LOG = LogUtils(request)
            LOG.info_api_responses(response)
        except Exception as e:
            logger.exception(e)
        return response

    for blueprint in blueprints:
        app.register_blueprint(blueprint)

from flask import Blueprint
from flask import jsonify
from flask import request
from google.protobuf import json_format

from business_ops.coupon_manager import CouponManager
from business_ops.merchant_manager import MerchantManager
from business_ops.invite_share_manager import InviteShareManager
from common.utils import requests_utils
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from proto import coupons_pb2 as coupons_pb
from service import base_responses
from service import error_codes
from strategy import shilai_brain
from view_ops import coupon_view_helper

coupon = Blueprint('coupon', __name__)


@coupon.route('/coupon/list', methods=['OPTIONS', 'GET'])
def get_coupons_from_merchant():
    user_id = requests_utils.get_headers_info(request, 'userId')
    merchant_id = request.values.get('merchantId')
    state = request.values.get("state", "ACCEPTED")
    state = coupons_pb.Coupon.CouponState.Value(state)
    page = request.values.get("page", None)
    size = request.values.get("size", None)  # 默认一次返回20条数据
    page = int(page) if page else None
    size = int(size) if size else None
    if not user_id:
        return base_responses.jsonify_response(data=[])
    if state == 0:
        shilai_brain.update_coupon_for_user(user_id, merchant_id)
    coupons = coupon_view_helper.get_coupons(
        user_id=user_id, merchant_id=merchant_id, state=state, page=page, size=size)
    if coupons:
        coupons = [json_format.MessageToDict(coupon, including_default_value_fields=True) for coupon in coupons]
        for coupon in coupons:
            date_info = coupon.get("dateInfo")
            end_timestamp = int(coupon.get("acceptTime")) + int(date_info.get("fixedTerm")) * date_utils.ONE_DAY
            if date_info.get("type") == "DATE_TYPE_FIX_TERM":
                date_info.update({"beginTimestamp": coupon.get("acceptTime"), "endTimestamp": end_timestamp})
                coupon.update({
                    "expiredTime": end_timestamp,
                    "startTime": coupon.get("acceptTime")
                })
            elif date_info.get("type") == "DATE_TYPE_FIX_TIME_RANGE":
                coupon.update({
                    "expiredTime": end_timestamp,
                    "startTime": date_info.get("beginTimestamp")
                })
        return base_responses.jsonify_response(data=coupons)
    return base_responses.jsonify_response(data=[])


@coupon.route('/coupon/package/<string:merchant_id>', methods=['GET'])
def get_coupon_package(merchant_id):
    """ 用户在这个商户下有哪些可用的优惠券.且优惠券的类型为券包
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        return base_responses.jsonify_response({})
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_id)
    user_da = UserDataAccessHelper()
    user = user_da.get_user(user_id)
    coupon_list_vo = CouponManager().get_user_coupons(user, merchant)
    data = json_format.MessageToDict(coupon_list_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(data)


@coupon.route('/coupon/accept', methods=['POST'])
def accept_coupon_from_merchant():
    user_id = requests_utils.get_headers_info(request, 'userId')
    coupon_id = request.json['couponId']
    wechat_coupon_id = requests_utils.get_value_from_json(request.json, 'code')

    try:
        CouponManager().accept_coupon(
            coupon_id=coupon_id, user_id=user_id, wechat_coupon_id=wechat_coupon_id)
        response_obj = base_responses.success_responses_obj()
    except Exception:
        response_obj = base_responses.error_responses()

    resp = jsonify(response_obj)
    return resp


@coupon.route('/coupon/check_new_coupons', methods=['POST'])
def check_new_coupons():
    user_id = requests_utils.get_headers_info(request, 'userId')
    merchant_id = request.json['merchantId']

    coupons = shilai_brain.update_coupon_for_user(user_id, merchant_id)
    result = base_responses.success_responses_obj()
    coupons_json = [json_format.MessageToDict(coupon, including_default_value_fields=True)
                    for coupon in coupons]
    for coupon_json in coupons_json:
        category = CouponCategoryDataAccessHelper().get_coupon_category(coupon_json['couponCategoryId'])
        coupon_json['couponCategory'] = json_format.MessageToDict(category, including_default_value_fields=True)
    result['coupons'] = coupons_json

    resp = jsonify(result)
    return resp


# @Deprecated 使用 /merchant/<string:merchant_id>/coupon_category/<string:coupon_category_id>/ext
@coupon.route('/coupon/<string:coupon_category_id>/ext', methods=['GET'])
def get_card_list(coupon_category_id):
    merchant_id = request.values.get('merchantId')

    coupon_category_ext = MerchantManager().get_card_ext(merchant_id, coupon_category_id)
    ext_json = json_format.MessageToDict(coupon_category_ext, preserving_proto_field_name=True)
    success_responses_obj = base_responses.create_responses_obj(
        error_codes.SUCCESS, error_codes.SUCCESS_MSG)
    success_responses_obj["cardExt"] = ext_json
    resp = jsonify(success_responses_obj)
    return resp


@coupon.route('/coupon/<string:coupon_id>', methods=['GET'])
def get_coupon(coupon_id):
    """获取用户优惠券
    """
    coupon = CouponCategoryDataAccessHelper().get_coupon_by_id(coupon_id)
    if coupon:
        coupon = coupon_view_helper.convert_to_ui_coupon(coupon)
    response_obj = base_responses.success_responses_obj()
    if coupon:
        response_obj['coupon'] = json_format.MessageToDict(coupon, including_default_value_fields=True)
    resp = jsonify(response_obj)
    return resp


@coupon.route("/coupon/issue_coupon/<string:merchant_id>", methods=["POST"])
def issue_coupon(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    coupon_category_id = request.json.get("couponCategoryId", None)
    success_responses_obj = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
    if coupon_category_id is None:
        return jsonify(success_responses_obj)
    result = None
    coupon = CouponManager().issue_coupon_to_user(coupon_category_id, user_id, state=coupons_pb.Coupon.ACCEPTED)
    if coupon:
        coupon_vo = coupon_view_helper.convert_to_ui_coupon(coupon)
        result = json_format.MessageToDict(coupon_vo, including_default_value_fields=True)
        return base_responses.jsonify_response(result)
    return base_responses.jsonify_response()


@coupon.route("/coupon/issue/<string:merchant_id>", methods=["POST"])
def issue_coupon_v2(merchant_id):
    user_id = request.headers.get("userId", None)
    coupon_category_id = request.json.get("couponCategoryId", None)
    invite_share_id = request.json.get("inviteShareId", None)
    type = request.json.get("type", None)
    if type == "INVITE_SHARE":
        manager = InviteShareManager(user_id=user_id, merchant_id=merchant_id, id=invite_share_id)
        manager.try_to_issue_invite_share_coupon_to_user()
    else:
        # 默认动作
        coupon_manager = CouponManager()
        state = coupons_pb.Coupon.ACCEPTED
        coupon = coupon_manager.issue_coupon_to_user(coupon_category_id, user_id, state=state)
        coupon_vo = coupon_view_helper.convert_to_ui_coupon(coupon)
        result = json_format.MessageToDict(coupon_vo, including_default_value_fields=True)
        return base_responses.jsonify_response(result)
    return base_responses.jsonify_response()

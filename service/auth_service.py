import random
from urllib import parse

from flask import Blueprint
from flask import jsonify
from flask import redirect
from flask import request

from google.protobuf import json_format

from business_ops.merchant_manager import MerchantManager
from business_ops.fanpiao_manager import FanpiaoManager
from common.utils import requests_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from service import error_codes
from service.auth_service_helper import UserAuthHelper
from service.base_responses import create_responses_obj
from service.base_responses import error_responses
from service.base_responses import jsonify_response

import proto.merchant_rules_pb2 as merchant_rules_pb


auth = Blueprint('auth', __name__)


@auth.route('/user/login', methods=['OPTIONS', 'POST'])
def user_login_api():
    """
    登录凭证校验。通过 wx.login() 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。更多使用方法详见 小程序登录。
    # POST /user/login
    # 请求主体 { code }
    # 请求头 { fromPlatform: “merchant” }
    # 流程：使用 code 调用微信接口 https://api.weixin.qq.com/sns/jscode2session 获取openid, session_key, unionid，其中 session_key 不能在客户端使用
    # 响应主体 { openid, unionid }
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api-backend/code2Session.html
    :return:
    """
    from_platform = requests_utils.get_platform(request)
    # 获取参数
    request_json = request.json
    js_code = request_json['code']
    join_method = requests_utils.get_value_from_json(request_json, 'sceneCode')
    user = UserAuthHelper().user_login_code_to_session(js_code, from_platform, join_method)
    if user:
        success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
        success_responses_obj['userId'] = user.id
        resp = jsonify(success_responses_obj)
    else:
        resp = error_responses()

    return resp


@auth.route('/sns/authorized', methods=['GET'])
def sns_authorized():
    """用户通过公众号路径授权
    # GET /authorizer/auth
    # 请求参数 { userId }
    #
    """
    code = request.values.get('code')
    state = request.values.get('state')
    (user_id, redirect_url) = UserAuthHelper().sns_authorized(code, state)
    resp = redirect(redirect_url, code=302)
    resp.set_cookie('userId', user_id)
    return resp


@auth.route('/authorizer/auth', methods=['GET'])
def authorizer_auth():
    """有公众号的商家授权成为商户
    # GET /authorizer/auth
    # 请求参数 { userId }
    #
    """
    staff_id = request.values.get('staffId')
    merchant_id = request.values.get('merchantId')

    user_id = request.cookies.get('userId')
    # 已授权或者该商户用户已经存在时，则重定向到授权完成页(/authorizer/authorized)
    if user_id and MerchantUserDataAccessHelper().get_user(user_id):
        url = UserAuthHelper().get_authorizer_auth_url(staff_id, user_id, merchant_id)
        return redirect(url, code=302)
    else:  # 未授权或者用户不存在时，则重定向到SNS授权页进行授权(/sns/authorized)
        url = UserAuthHelper().redirect_sns_auth(staff_id, merchant_id, request.path)
        return redirect(url, code=302)


@auth.route('/authorizer/authorized', methods=['GET'])
def authorizer_authorized():
    """有公众号的商家授权回调
    # GET /authorizer/authorized
    # 请求参数 { auth_code, expires_in, unionid }
    # 响应 执行下一个步骤的 HTML 代码
    # 流程：使用 auth_code, expires_id 可获取授权商家基本信息和接口调用凭据，初始化商家数据：初始化两张会员卡（一张用于非注册会员，一张用户注册会员）
    """
    # NOTE: 这是由外部微信平台引导过来的跳转链接，url里含有escape字符"&amp;"导致query_string解析异常，
    #       所以这里采用手动解析query_string的方式。
    # 例子: /authorizer/authorized?merchantId=9b041462e8f44cefaeb0a0be29dbcea5&amp;userId=ed62527d...
    query_string = parse.urlparse(request.full_path).query
    args = dict(parse.parse_qsl(query_string))
    user_id = args.get('userId')
    staff_id = args.get('staffId')
    merchant_id = args.get('merchantId')
    auth_code = args.get('auth_code')

    UserAuthHelper().authorizer_authorized(user_id, staff_id, merchant_id, auth_code)
    fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
    fanpiao_manager.add_or_update_fanpiao_category(10000, 11, "100元饭票", 10000, base_selling_quantity=random.randint(66, 76))
    fanpiao_manager.add_or_update_fanpiao_category(20000, 14, "200元饭票", 20000, base_selling_quantity=random.randint(59, 69))
    fanpiao_manager.add_or_update_fanpiao_category(30000, 17, "300元饭票", 30000, base_selling_quantity=random.randint(32, 42))
    fanpiao_manager.add_or_update_fanpiao_category(50000, 18, "500元饭票", 50000, base_selling_quantity=random.randint(11, 21))
    fanpiao_manager.add_or_update_fanpiao_category(
        10000, 5, "100元饭票", 10000, is_for_risk_control_user=True, fanpiao_risk_level=10
    )
    fanpiao_manager.add_or_update_fanpiao_category(
        20000, 6, "200元饭票", 20000, is_for_risk_control_user=True, fanpiao_risk_level=10
    )
    fanpiao_manager.add_or_update_fanpiao_category(
        30000, 7, "300元饭票", 30000, is_for_risk_control_user=True, fanpiao_risk_level=10
    )
    fanpiao_manager.add_or_update_fanpiao_category(
        10000, 3, "100元饭票", 10000, is_for_risk_control_user=True, fanpiao_risk_level=20
    )
    fanpiao_manager.add_or_update_fanpiao_category(
        20000, 4, "200元饭票", 20000, is_for_risk_control_user=True, fanpiao_risk_level=20
    )
    fanpiao_manager.add_or_update_fanpiao_category(
        30000, 5, "300元饭票", 30000, is_for_risk_control_user=True, fanpiao_risk_level=20
    )

    # 跳转到公众号文章链接，引导用户点击进入小程序
    url = 'https://w.url.cn/s/AwYnWpK'
    return redirect(url, code=302)


@auth.route('/authorizer/binding', methods=['GET'])
def authorizer_binding():
    """没有公众号的商家绑定成为商户"""
    # staff_id = request.values.get('staffId')
    merchant_id = request.values.get('merchantId')

    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_da = MerchantDataAccessHelper()
    if user_id:
        merchant_manager = MerchantManager()
        merchant = merchant_manager.init_merchant(
            id=merchant_id,
            user_id=user_id,
            binding_staff_id=user_id,
            join_method=merchant_rules_pb.SHILAI_MP_SUBMERCHANT,
            assist_build=True,
        )
        merchant_da.update_or_create_merchant(merchant)
        merchant_manager.init_registration_info(merchant.id)

        fanpiao_manager = FanpiaoManager(merchant=merchant)
        fanpiao_manager.add_or_update_fanpiao_category(10000, 11, "100元饭票", 10000, base_selling_quantity=random.randint(66, 76))
        fanpiao_manager.add_or_update_fanpiao_category(20000, 14, "200元饭票", 20000, base_selling_quantity=random.randint(59, 69))
        fanpiao_manager.add_or_update_fanpiao_category(30000, 17, "300元饭票", 30000, base_selling_quantity=random.randint(32, 42))
        fanpiao_manager.add_or_update_fanpiao_category(50000, 18, "500元饭票", 50000, base_selling_quantity=random.randint(11, 21))
        fanpiao_manager.add_or_update_fanpiao_category(
            10000, 5, "100元饭票", 10000, is_for_risk_control_user=True, fanpiao_risk_level=10
        )
        fanpiao_manager.add_or_update_fanpiao_category(
            20000, 6, "200元饭票", 20000, is_for_risk_control_user=True, fanpiao_risk_level=10
        )
        fanpiao_manager.add_or_update_fanpiao_category(
            30000, 7, "300元饭票", 30000, is_for_risk_control_user=True, fanpiao_risk_level=10
        )
        fanpiao_manager.add_or_update_fanpiao_category(
            10000, 3, "100元饭票", 10000, is_for_risk_control_user=True, fanpiao_risk_level=20
        )
        fanpiao_manager.add_or_update_fanpiao_category(
            20000, 4, "200元饭票", 20000, is_for_risk_control_user=True, fanpiao_risk_level=20
        )
        fanpiao_manager.add_or_update_fanpiao_category(
            30000, 5, "300元饭票", 30000, is_for_risk_control_user=True, fanpiao_risk_level=20
        )
        # 跳转到公众号文章链接，引导用户点击进入小程序
        # url = 'https://w.url.cn/s/AwYnWpK'
        # return redirect(url, code=302)
        return jsonify_response({"id": merchant.id})
    else:
        # url = UserAuthHelper().redirect_sns_auth(staff_id, merchant_id, request.path)
        raise Exception("创建商户失败")

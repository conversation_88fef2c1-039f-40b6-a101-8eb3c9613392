# -*- coding: utf-8 -*-

from google.protobuf import json_format

from flask import Blueprint
from flask import request
from service.base_responses import jsonify_response

from business_ops.invite_share_manager import InviteShareManager
from business_ops.coupon.coupon_category_manager import CouponCategoryManager


_invite_share = Blueprint(__name__, __name__, url_prefix="/invite_share")


@_invite_share.route("/config/<string:merchant_id>", methods=["POST"])
def set_config(merchant_id):
    manager = InviteShareManager(merchant_id=merchant_id)
    cash_reward_uplimit = request.json.get("cashRewardUplimit", None)
    cash_reward_per_invitee = request.json.get("cashRewardPerInvitee", None)
    type = request.json.get("type", "FIX")
    cash_reward_rate_per_invitee = request.json.get("cashRewardRatePerInvitee", None)
    effective_days = request.json.get("effectiveDays", 999)
    receive_issue_coupon_seconds = request.json.get("receiveIssueCouponSeconds", 1)
    proportion = request.json.get("proportion", None)
    new_user_proportion = request.json.get("newUserProportion", None)
    old_user_proportion = request.json.get("oldUserProportion", None)
    cash_reward_per_new_user = request.json.get("cashRewardPerNewUser", None)
    cash_reward_per_old_user = request.json.get("cashRewardPerOldUser", None)
    new_user_max_discount = request.json.get("newUserMaxDiscount", None)
    old_user_max_discount = request.json.get("oldUserMaxDiscount", None)

    new_user_least_cost = request.json.get("newUserLeastCost", None)
    new_user_reduce_cost = request.json.get("newUserReduceCost", None)
    old_user_least_cost = request.json.get("oldUserLeastCost", None)
    old_user_reduce_cost = request.json.get("oldUserReduceCost", None)

    title = request.json.get("title", "分享券")
    manager.activity_config_init(
        cash_reward_uplimit, cash_reward_per_invitee, type, effective_days, receive_issue_coupon_seconds,
        cash_reward_rate_per_invitee, invitor_proportion=proportion,
        new_user_proportion=new_user_proportion, old_user_proportion=old_user_proportion,
        cash_reward_per_new_user=cash_reward_per_new_user, cash_reward_per_old_user=cash_reward_per_old_user,
        new_user_max_discount=new_user_max_discount, old_user_max_discount=old_user_max_discount)
    coupon_category_manager = CouponCategoryManager(merchant_id=merchant_id)
    base_info = {"dateInfo": {"fixedBeginTerm": 0, "fixedTerm": 7}}
    coupon_category_manager.create_coupon_category(
        issue_scene="INVITE_SHARE",
        least_cost=new_user_least_cost, reduce_cost=new_user_reduce_cost, title=title,
        type="DATE_TYPE_FIX_TERM", base_info=base_info, proportion=None, user_type="NEW_USER"
    )
    coupon_category_manager.create_coupon_category(
        issue_scene="INVITE_SHARE",
        least_cost=old_user_least_cost, reduce_cost=old_user_reduce_cost, title=title,
        type="DATE_TYPE_FIX_TERM", base_info=base_info, proportion=None, user_type="OLD_USER"
    )
    return jsonify_response()


@_invite_share.route("/config/<string:merchant_id>", methods=["GET"])
def get_config(merchant_id):
    manager = InviteShareManager(merchant_id=merchant_id)
    config_vo = manager.get_activity_config()
    if config_vo:
        config_vo = json_format.MessageToDict(config_vo, including_default_value_fields=True)
        return jsonify_response(config_vo)
    return jsonify_response({})


@_invite_share.route("/<string:merchant_id>", methods=["POST"])
def create(merchant_id):
    user_id = request.headers.get("userId", None)
    manager = InviteShareManager(merchant_id=merchant_id, user_id=user_id)
    share = manager.create_share()
    share = json_format.MessageToDict(share, including_default_value_fields=True)
    return jsonify_response(share)


@_invite_share.route("/<string:merchant_id>", methods=["GET"])
def get_share(merchant_id):
    user_id = request.headers.get("userId", None)
    id = request.args.get("id", None)
    manager = InviteShareManager(merchant_id=merchant_id, user_id=user_id)
    share = manager.get_share(id)
    share = json_format.MessageToDict(share, including_default_value_fields=True)
    return jsonify_response(share)


@_invite_share.route("/accept/<string:merchant_id>", methods=["POST"])
def accept(merchant_id):
    user_id = request.headers.get("userId", None)
    id = request.json.get("id", None)
    manager = InviteShareManager(id=id, user_id=user_id, merchant_id=merchant_id)
    coupon_category_fee = manager.accept()
    return jsonify_response({"leastCost": coupon_category_fee.least_cost, "reduceCost": coupon_category_fee.reduce_cost})


@_invite_share.route("/can_receive_coupon/<string:merchant_id>", methods=["GET"])
def can_receive_coupon(merchant_id):
    user_id = request.headers.get("userId", None)
    id = request.args.get("id", None)
    manager = InviteShareManager(id=id, user_id=user_id, merchant_id=merchant_id)
    can_receive_coupon = manager.can_receive_coupon()
    return jsonify_response({"canReceiveCoupon": can_receive_coupon.flag, "reduceCost": can_receive_coupon.reduce_cost, "leastCost": can_receive_coupon.least_cost})


@_invite_share.route("/record", methods=["GET"])
def user_record():
    user_id = request.headers.get("userId", None)
    manager = InviteShareManager(user_id=user_id)
    user_record = manager.user_record()
    user_record = json_format.MessageToDict(user_record, including_default_value_fields=True)
    return jsonify_response(user_record)


@_invite_share.route("/record_list/<string:merchant_id>", methods=["GET"])
@_invite_share.route("/record_list", methods=["GET"])
def record_list(merchant_id=None):
    user_id = request.headers.get("userId", None)
    previous_ranking = int(request.args.get("previousRanking", 0))
    manager = InviteShareManager(user_id=user_id, merchant_id=merchant_id)
    record_list = manager.get_invitee_record_list(previous_ranking=previous_ranking)
    record_list = json_format.MessageToDict(record_list, including_default_value_fields=True)
    return jsonify_response(record_list)


@_invite_share.route("/income_rank_list/<string:merchant_id>", methods=["GET"])
@_invite_share.route("/income_rank_list", methods=["GET"])
def income_rank_list(merchant_id=None):
    user_id = request.headers.get("userId", None)
    previous_ranking = int(request.args.get("previousRanking", 0))
    manager = InviteShareManager(user_id=user_id, merchant_id=merchant_id)
    rank_list = manager.get_inviter_income_rank_list(previous_ranking=previous_ranking)
    rank_list = json_format.MessageToDict(rank_list, including_default_value_fields=True)
    return jsonify_response(rank_list)

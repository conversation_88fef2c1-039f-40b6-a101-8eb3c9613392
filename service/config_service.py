# -*- coding: utf-8 -*-


from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops.config_manager import ConfigManager
from dao.config_da_helper import ConfigDataAccessHelper
from service.base_responses import jsonify_response
import proto.config_pb2 as config_pb
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper


_config = Blueprint(__name__, __name__, url_prefix="/config")


@_config.route("/update_timestamp/<string:merchant_id>", methods=["GET"])
def get_update_timestamp(merchant_id):
    config_da = ConfigDataAccessHelper()
    update_timestamp = config_da.get_update_timestamp(merchant_id=merchant_id)
    if not update_timestamp:
        return jsonify_response()
    update_timestamp = json_format.MessageToDict(update_timestamp, including_default_value_fields=True)
    return jsonify_response(update_timestamp)


@_config.route("/update_timestamp/<string:merchant_id>", methods=["POST"])
def update_update_timestamp(merchant_id):
    dish_catalog_update_timestamp = request.json.get("dishCatalogUpdateTimestamp", None)
    merchant_info_update_timestamp = request.json.get("merchantInfoUpdateTimestamp", None)
    merchant_coupon_package_update_timestamp = request.json.get("merchantCouponPackageUpdateTimestamp", None)
    merchant_fanpiao_update_timestamp = request.json.get("merchantFanpiaoUpdateTimestamp", None)
    config_manager = ConfigManager(merchant_id=merchant_id)
    config_manager.update_update_timestamp(
        dish_catalog_update_timestamp=dish_catalog_update_timestamp,
        merchant_info_update_timestamp=merchant_info_update_timestamp,
        merchant_coupon_package_update_timestamp=merchant_coupon_package_update_timestamp,
        merchant_fanpiao_update_timestamp=merchant_fanpiao_update_timestamp
    )
    return jsonify_response()


@_config.route("/platform-global-config", methods=["POST"])
def get_platform_global_config():
    config_da = ConfigDataAccessHelper()
    result = config_da.get_platform_global_config()
    result = json_format.MessageToDict(result)
    return jsonify_response(result)


@_config.route("/get_promotion_config/<string:merchant_id>", methods=["GET"])
def get_promotion_config(merchant_id):
    categories = OrderingServiceDataAccessHelper().get_categories(merchant_id=merchant_id, no_discount=False)
    dicount_category_ids = [category.id for category in categories]
    config_da = ConfigDataAccessHelper()
    results = config_da.get_promotion_configs(merchant_id)
    has_max_discount = False
    if results:
        tmp = []
        for result in results:
            if result.type == config_pb.PromotionConfig.TypeEnum.GLOBAL_DISCOUNT:
                has_max_discount = True
            for category_id in result.dish_category_ids:
                if category_id not in dicount_category_ids:
                    result.dish_category_ids.remove(category_id)
            if len(result.dish_category_ids) > 0:
                tmp.append(json_format.MessageToDict(result, including_default_value_fields=True))
        results = tmp

    if not has_max_discount:
        merchant_info = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
        if merchant_info and dicount_category_ids:
            coupon_config = merchant_info.preferences.coupon_config
            config = config_pb.PromotionConfig(
                max_discount = int(100 - coupon_config.max_discount),
                update_time = coupon_config.max_discount_update_timestamp,
                type=config_pb.PromotionConfig.TypeEnum.GLOBAL_DISCOUNT,
                enable_sync_to_pos=True,
                merchant_id=merchant_id,
                name="全场折扣自动合并"
            )
            for category_id in dicount_category_ids:
                config.dish_category_ids.append(category_id)
            results.append(json_format.MessageToDict(config, including_default_value_fields=True))

    return results

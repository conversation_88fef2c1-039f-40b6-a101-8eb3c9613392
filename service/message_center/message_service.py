# -*- coding: utf-8 -*-

from flask import Blueprint

from common import protobuf_transformer

from controller.message_center.message_controller import MessageController
from service.base_responses import jsonify_response


bp_name = 'message_center'
_messag_center = Blueprint(bp_name, bp_name, url_prefix='/message_center')


@_messag_center.route("/<string:operation>", methods=["POST"])
def operate(operation):
    ctrl = MessageController(operation)
    result = ctrl.do_operate()
    if result is None:
        return jsonify_response()
    if isinstance(result, list):
        return jsonify_response(protobuf_transformer.batch_protobuf_to_dict(result))
    return jsonify_response(protobuf_transformer.protobuf_to_dict(result))


@_messag_center.route("/logs", methods=["POST"])
def logs():
    pass

# -*- coding: utf-8 -*-

import logging
import math

from flask import Blueprint
from flask import jsonify
from flask import request

from google.protobuf import json_format
import maya

import proto.merchant_rules_pb2 as merchant_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.group_dining_manager import GroupDiningManager
from business_ops.message_manager import MessageManager
from business_ops.ordering.order_manager import OrderManager
from common.utils import requests_utils
from common.config import config
from common import constants
from dao.base_helper import BaseHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.chat_message_da_helper import ChatMessageDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from proto.group_dining import group_dining_pb2 as group_dining_pb
from service.base_responses import make_json_response
from service.base_responses import create_responses_obj
from service import base_responses
from service import error_codes
from service.errors import GroupDiningNotFound
from service.errors import UserNotFound
from strategy import group_dining_strategy
from view_ops.group_dining_view_helper import GroupDiningViewObjectHelper
from view_ops.merchant_view_helper import MerchantViewObjectHelper
from view_ops.transfer_info_view_helper import TransferInfoViewObjectHelper

logger = logging.getLogger(__name__)
group_dining = Blueprint("group_dining", __name__, url_prefix="/group_dining")


@group_dining.route("/quit/<string:dining_id>", methods=["POST"])
def quit(dining_id):
    """ 用户主动退出饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not (dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED):
        # 饭局状态不为进行中无法退出
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        logger.info("饭局状态错误,不能退出: state: {}".format(dining.state))
        return base_responses.jsonify_response(status_response=resp)
    if user_id == dining.director_id:
        # 当前局长不能退出
        resp = create_responses_obj(error_codes.DIRECTOR_CANNOT_QUIT,
                                    error_codes.DIRECTOR_CANNOT_QUIT_MSG)
        logger.info("当前局长不能退出: dining_id: {}, user_id: {}".format(dining_id, user_id))
        return base_responses.jsonify_response(resp)
    GroupDiningManager(user_id=user_id, dining_id=dining_id).quit_group_dining()
    logger.info("{}退出饭局".format(user_id))
    return base_responses.jsonify_response()


@group_dining.route("/kick/<string:dining_id>", methods=["POST"])
def kick(dining_id):
    """ 被踢出饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    kicked_user_ids = request.json.get("kickedUserId")  # 被踢的人id
    if not (dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED):
        # 饭局只有进行中的状态才能踢人
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        logger.info("饭局状态错误,不能踢人: state: {}".format(dining.state))
        return base_responses.jsonify_response(status_response=resp)
    if user_id != dining.director_id:
        # 只有局长才能踢人
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        logger.info("你不是局长不能踢人: user_id: {}, dining_id: {}, director_id: {}".format(user_id, dining_id, dining.director_id))
        return base_responses.jsonify_response(status_response=resp)
    if dining.director_id in kicked_user_ids:
        # 局长不能被踢
        resp = create_responses_obj(error_codes.DIRECTOR_CANNOT_BE_KICKED,
                                    error_codes.DIRECTOR_CANNOT_BE_KICKED_MSG)
        logger.info("局长不能被踢: user_id: {}, dining_id: {}".format(user_id, dining_id))
        return base_responses.jsonify_response(status_response=resp)
    GroupDiningManager().kick_user(kicked_user_ids, dining_id)
    return base_responses.jsonify_response()


@group_dining.route("/nominate/<string:dining_id>", methods=["POST"])
def nominate(dining_id):
    """ 把另一个人任命为局长
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    invitee_id = request.json.get("inviteeId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not (dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED):
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        logger.info("饭局状态错误,不能转让: {}".format(dining.state))
        return base_responses.jsonify_response(status_response=resp)
    if dining.director_id != user_id:
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        logger.info("不是局长,不能转让: {}".format(user_id))
        return base_responses.jsonify_response(status_response=resp)
    invitation = InvitationDataAccessHelper().get_invitation(user_id=invitee_id,
                                                             dining_id=dining_id,
                                                             state=group_dining_pb.Invitation.ACCEPTED)
    if not invitation or invitation.state != group_dining_pb.Invitation.ACCEPTED:
        resp = create_responses_obj(error_codes.NOT_BEEN_INVITED,
                                    error_codes.NOT_BEEN_INVITED_MSG)
        logger.info("用户未在饭局中,无法成为局长: {}".format(invitee_id))
        return base_responses.jsonify_response(status_response=resp)
    GroupDiningManager().nominate(dining.id, invitee_id)
    logger.info("修改饭局: {}局长为: {}".format(dining_id, invitee_id))
    return base_responses.jsonify_response()


@group_dining.route("/update_invite_permission/<string:dining_id>", methods=["POST"])
def update_invite_permission(dining_id):
    """ 修改饭局的邀请权限
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    open_invite_permission = int(request.json.get("openInvitePermission", '0'))
    if not (dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED):
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        logger.info("饭局状态错误,不能设置邀请权限: {}".format(dining.state))
        return base_responses.jsonify_response(status_response=resp)
    if dining.director_id != user_id:
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        logger.info("不是局长,不能设置邀请权限: {}".format(user_id))
        return base_responses.jsonify_response(status_response=resp)
    open_invite_permission = False if open_invite_permission == '0' else True
    open_invite_permission = open_invite_permission != '0'
    GroupDiningDataAccessHelper().update(dining_id=dining_id, open_invite_permission=open_invite_permission)
    # TODO: 给消息中心发消息
    return base_responses.jsonify_response()


@group_dining.route("/list_nearby", methods=["GET"])
def get_nearby_dining_events():
    """ 返回用户饭局列表
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    lat = request.args.get("lat", 22.524020182291668)
    lng = request.args.get("lng", 113.9372493489)
    if lat == "":
        lat = 22.524020182291668
    if lng == "":
        lng = 113.9372493489
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    max_distance = request.args.get("maxDistance", 5000000)
    last_event_time = request.args.get("lastEventTime", 0)
    nearby_events = GroupDiningManager().get_nearby_group_dining_events(lat=lat, lng=lng, user_id=user_id,
                                                                        max_distance=max_distance,
                                                                        page=page, size=size,
                                                                        last_event_time=last_event_time)
    nearby_events_json = [json_format.MessageToDict(event, including_default_value_fields=True) for event in nearby_events]
    return make_json_response({"nearbyDiningEvents": nearby_events_json})


@group_dining.route("/list_nearby_detail", methods=["GET"])
def get_nearby_dining_events_detail():
    user_id = requests_utils.get_headers_info(request, "userId")
    page = request.args.get("page", None)
    size = request.args.get("size", None)
    page = int(page) if page else None
    size = int(size) if size else None
    lat = request.args.get("lat", 22.524020182291668)
    lng = request.args.get("lng", 113.9372493489)
    if lat == "":
        lat = 22.524020182291668
    if lng == "":
        lng = 113.9372493489
    max_distance = request.args.get("maxDistance", 5000000)
    nearby_events = GroupDiningManager().get_nearby_group_dining_events_detail(
        lat=lat, lng=lng, user_id=user_id, max_distance=max_distance, page=page, size=size)
    nearby_events_json = [json_format.MessageToDict(event, including_default_value_fields=True) for event in nearby_events]
    return make_json_response({"nearbyDiningEvents": nearby_events_json})


@group_dining.route("/accept/<string:dining_id>", methods=["POST"])
def accept(dining_id):
    """ 接受邀请加入饭局
    Args:
        dining_id: 饭局id
        inviter_id: 邀请人id
    Return:
        Success
    """
    invitee_id = requests_utils.get_headers_info(request, "userId")
    user = UserDataAccessHelper().get_user(invitee_id)
    if not user:
        # 检查被邀请人存不存在
        resp = create_responses_obj(error_codes.USER_NOT_FOUND,
                                    error_codes.USER_NOT_FOUND_MSG)
        return make_json_response(resp)

    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not dining:
        # 检查饭局存不存在
        resp = create_responses_obj(error_codes.DINING_NOT_EXISTS,
                                    error_codes.DINING_NOT_EXISTS_MSG)
        return make_json_response(resp)
    invitation_da = InvitationDataAccessHelper()
    invitation = invitation_da.get_invitation(
        user_id=invitee_id, dining_id=dining_id, state=group_dining_pb.Invitation.ACCEPTED)
    if invitation:
        resp = None
        if invitation.state == group_dining_pb.Invitation.ACCEPTED:
            # 已经是加入直接返回成功
            logger.info("已经加入过饭局,直接返回成功: {}, {}".format(dining.id, invitee_id))
            resp = create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        if resp:
            return make_json_response(resp)

    # 只有'SCHEDULED'状态的饭局才可以加入
    if not (dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED):
        logger.info("接受邀请: 饭局{}状态错误".format(dining.id))
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR_CANNOT_JOIN,
                                    error_codes.GROUP_DINING_STATE_ERROR_CANNOT_JOIN_MSG)
        return make_json_response(resp)

    # 设置接受邀请
    resp = GroupDiningManager(dining_id=dining.id, user_id=invitee_id).accept_invitation()
    return make_json_response(resp)


@group_dining.route("/<string:dining_id>", methods=["GET"])
def get_dining(dining_id):
    """ 查询饭局
    Args:
        dining_id: 饭局id

    Return:
        GroupDiningEvent结构体
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningViewObjectHelper().get_dining_by_id(user_id, dining_id)
    if dining:
        dining = json_format.MessageToDict(dining, including_default_value_fields=True)
        return base_responses.jsonify_response(dining)
    response_obj = create_responses_obj(error_codes.DINING_NOT_EXISTS, error_codes.DINING_NOT_EXISTS_MSG)
    return base_responses.jsonify_response(status_response=response_obj)


@group_dining.route("/detail/<string:dining_id>", methods=["GET"])
def get_dining_detail(dining_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningViewObjectHelper().get_dining_detail(user_id, dining_id)
    if dining:
        dining = json_format.MessageToDict(dining, including_default_value_fields=False)
        return base_responses.jsonify_response(dining)
    raise GroupDiningNotFound()


@group_dining.route("/create", methods=["POST"])
def create():
    """ 新增饭局
    Args:
        merchant_id: 门店id
        event_time: 开始时间
        max_group_size: 最多参与人数
        payment_rule: 支付方式

    Return:
        id: 标识饭局的id
    """
    merchant_id = request.json.get("merchantId")
    # store_id = request.json.get("storeId")
    # 历史遗留问题,前端不用再传storeId了
    initiator_id = requests_utils.get_headers_info(request, "userId")
    if not initiator_id:
        raise UserNotFound()
    # %Y-%m-%d %H:%M
    event_time = int(request.json.get("eventDate"))
    max_group_size = request.json.get("maxGroupSize", 8)
    payment_rule = group_dining_pb.GroupDiningEvent.PaymentRule.Value(request.json.get("paymentRule", "ALL_SHARING"))
    visibility = group_dining_pb.GroupDiningEvent.Visibility.Value(request.json.get("visibility", "PRIVATE"))
    group_dining_event_id = request.json.get('groupDiningEventId')

    # 新增饭局
    resp, dining = GroupDiningManager(merchant_id=merchant_id, dining_id=group_dining_event_id).create_group_dining_event(
        initiator_id=initiator_id, event_time=event_time, max_group_size=max_group_size,
        payment_rule=payment_rule, visibility=visibility)
    if resp.get("errcode") == error_codes.SUCCESS:
        # 创建者默认加入饭局
        GroupDiningManager(user_id=initiator_id, dining_id=dining.id).accept_invitation()
        resp.update({"id": dining.id})
    return make_json_response(resp)


@group_dining.route("/cancel/<string:dining_id>", methods=["POST"])
def cancel(dining_id):
    """ 取消饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    group_dining_da = GroupDiningDataAccessHelper()
    group_dining_event = group_dining_da.get_dining_by_id(dining_id)
    if group_dining_event.director_id != user_id:
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        return base_responses.jsonify_response(status_response=resp)
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if dining.state != group_dining_pb.GroupDiningEvent.SCHEDULED:
        resp = create_responses_obj(error_codes.DINING_ALREADY_PAID,
                                    error_codes.DINING_ALREADY_PAID_MSG)
        return base_responses.jsonify_response(status_response=resp)

    GroupDiningManager(dining_id=dining_id, user_id=user_id).cancel_group_dining_event()
    return base_responses.jsonify_response()


@group_dining.route("/coupon_policies", methods=["POST"])
def get_group_dining_coupon_policies():
    """ 获取饭局获取策略
    """
    cost_per_person = request.json.get("costPerPerson")
    total_discount = request.json.get("totalDiscount")
    policies = group_dining_strategy.generate_coupon_policies(cost_per_person=int(cost_per_person),
                                                              total_discount=int(total_discount))
    json_policies = [json_format.MessageToDict(policy, including_default_value_fields=True) for policy in policies]
    return make_json_response({'policies': json_policies})


@group_dining.route("/qrcode/<string:dining_id>", methods=["POST"])
def create_group_dining_event_qrcode(dining_id):
    """ 获取饭局邀请页面二维码信息
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
        return jsonify(success_responses_obj)
    request_json = request.json
    merchant_id = request_json.get("merchantId")
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    store_id = merchant.stores[0].id
    url = BaseHelper().generate_group_dining_event_wxcode(merchant_id, store_id, dining_id, user_id)

    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj['qrCodeUrl'] = url
    resp = jsonify(success_responses_obj)
    return resp


@group_dining.route("/qrcode/<string:dining_id>", methods=["GET"])
def get_group_dining_event_qrcode(dining_id):
    """ 获取饭局邀请页面二维码信息
    """
    resp = BaseHelper().get_group_dining_event_wxcode(dining_id)
    return resp


@group_dining.route("/merchants", methods=["POST"])
def get_group_dining_merchants():
    """ 获取用户附近的商家
    """
    page = request.json.get("page", None)
    size = request.json.get("size", None)
    page = int(page) if page else None
    size = int(size) if size else None
    lat = request.json.get("lat", 22.524020182291668)
    lng = request.json.get("lng", 113.9372493489)
    if lat == "":
        lat = 22.524020182291668
    if lng == "":
        lng = 113.9372493489
    max_distance = request.json.get('maxDistance', 5000000)
    stores = MerchantViewObjectHelper().get_nearby_stores(status=merchant_pb.RUNNING, lat=lat, lng=lng, page=page, size=size, max_distance=max_distance)
    stores_json = [json_format.MessageToDict(store, including_default_value_fields=True) for store in stores]
    return make_json_response({'merchants': stores_json})


@group_dining.route("/merchant_list", methods=["GET"])
def get_merchant_group_dining_list():
    """ 商户饭局列表
    """
    merchant_id = request.args.get('merchantId', None)
    user_id = requests_utils.get_headers_info(request, "userId")
    store = GroupDiningManager(user_id=user_id).get_merchant_group_dining_list(
        merchant_id=merchant_id, user_id=user_id)
    ret_json = json_format.MessageToDict(store, including_default_value_fields=True)
    return base_responses.jsonify_response(ret_json)


@group_dining.route("/list", methods=["GET"])
def get_user_group_dining_list():
    """ 获取用户饭局列表
    """
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        return base_responses.jsonify_response([])
    state = [
        group_dining_pb.Invitation.ACCEPTED,
        group_dining_pb.Invitation.PENDING_APPROVE
    ]
    monetary_state = [
        group_dining_pb.Invitation.PAYMENT_PENDING,
        group_dining_pb.Invitation.TRANSFER_PENDING,
        group_dining_pb.Invitation.RED_PACKET_PENDING
    ]
    group_dining_list = GroupDiningViewObjectHelper().get_user_group_dining_list(user_id=user_id,
                                                                                 page=page, size=size,
                                                                                 state=state,
                                                                                 monetary_state=monetary_state)
    json_object = [json_format.MessageToDict(dining, including_default_value_fields=True) for dining in group_dining_list]
    return base_responses.jsonify_response(json_object)


@group_dining.route("/approve/<string:dining_id>", methods=["POST"])
def approve(dining_id):
    """ 局长同意用户加入饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    invitee_id = request.json.get("inviteeId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not dining:
        # 饭局不存在
        resp = create_responses_obj(error_codes.DINING_NOT_EXISTS,
                                    error_codes.DINING_NOT_EXISTS_MSG)
        return base_responses.jsonify_response(status_response=resp)
    if dining.state != group_dining_pb.GroupDiningEvent.SCHEDULED:
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        return base_responses.jsonify_response(status_response=resp)
    if dining.director_id != user_id:
        # 不是局长,不能同意
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        return base_responses.jsonify_response(status_response=resp)
    resp = GroupDiningManager(user_id=invitee_id, dining_id=dining_id).accept_invitation()
    MessageManager().publish_agree_or_reject(invitee_id, user_id, dining_id, group_dining_pb.Invitation.ACCEPTED)
    return base_responses.jsonify_response(status_response=resp)


@group_dining.route("/reject/<string:dining_id>", methods=["POST"])
def reject(dining_id):
    """ 局长拒绝用户加入饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    invitee_id = request.json.get("inviteeId")
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not dining:
        # 饭局不存在
        resp = create_responses_obj(error_codes.DINING_NOT_EXISTS,
                                    error_codes.DINING_NOT_EXISTS_MSG)
        return base_responses.jsonify_response(status_response=resp)
    if dining.state != group_dining_pb.GroupDiningEvent.SCHEDULED:
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        return base_responses.jsonify_response(status_response=resp)
    if dining.director_id != user_id:
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        return base_responses.jsonify_response(status_response=resp)
    InvitationDataAccessHelper().update(user_id=invitee_id,
                                        dining_id=dining_id,
                                        state=group_dining_pb.Invitation.REJECTED)
    MessageManager().publish_agree_or_reject(invitee_id, user_id, dining_id, group_dining_pb.Invitation.REJECTED)
    return base_responses.jsonify_response()


@group_dining.route("/apply_for/<string:dining_id>", methods=["POST"])
def apply_for(dining_id):
    """ 用户申请加入饭局
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    message = request.json.get('message', "")  # 申请加入的附言
    GroupDiningManager().apply_for_group_dining(dining_id=dining_id,
                                                applicant_id=user_id,
                                                message=message)
    return base_responses.jsonify_response()


@group_dining.route("/update/<string:dining_id>", methods=["POST"])
def update(dining_id):
    """ 局长修改饭局信息
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    payment_rule = request.json.get('paymentRule', None)
    event_time = request.json.get("eventDate", None)
    open_invite_permission = request.json.get("openInvitePermission", None)
    if open_invite_permission is not None:
        open_invite_permission = open_invite_permission != '0'
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    if not dining:
        logger.info("饭局不存在")
        return base_responses.jsonify_response()
    if user_id != dining.director_id:
        resp = create_responses_obj(error_codes.NOT_DIRECTOR,
                                    error_codes.NOT_DIRECTOR_MSG)
        logger.info("不是局长,不能修改饭局 user_id: {}, director_id: {}".format(user_id, dining.director_id))
        return base_responses.jsonify_response(status_response=resp)
    if dining.state != group_dining_pb.GroupDiningEvent.SCHEDULED:
        resp = create_responses_obj(error_codes.GROUP_DINING_STATE_ERROR,
                                    error_codes.GROUP_DINING_STATE_ERROR_MSG)
        logger.info("只有进行中的饭局才能修改")
        return base_responses.jsonify_response(status_response=resp)
    GroupDiningDataAccessHelper().update(dining_id=dining_id, payment_rule=payment_rule,
                                         event_time=event_time, open_invite_permission=open_invite_permission)
    return base_responses.jsonify_response()


@group_dining.route("/transfer_info/<string:dining_id>", methods=["GET"])
def transfer_info(dining_id):
    """ 组员支付信息
    """
    transfer_info = TransferInfoViewObjectHelper().get_transfer_info(dining_id)
    if transfer_info:
        json_obj = json_format.MessageToDict(transfer_info, including_default_value_fields=True)
    return base_responses.jsonify_response(json_obj)


@group_dining.route("/chat_history/<string:dining_id>", methods=["GET"])
def chat_history(dining_id):
    """ 饭局聊天历史
    """
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    messages = ChatMessageDataAccessHelper().get_group_dining_messages(dining_event_id=dining_id,
                                                                       page=page, size=size,
                                                                       orderby=[("createTime", -1)])
    ret = []
    for message in messages:
        user = UserDataAccessHelper().get_user(message.sender_id)
        json_obj = json_format.MessageToDict(message, including_default_value_fields=True)
        json_obj.update({
            "nickname": user.member_profile.nickname,
            "headimgurl": user.member_profile.head_image_url
        })
        ret.append(json_obj)
    result = {"type": "GROUP_DINING_EVENT_CHAT_MESSAGE", "chatMessages": ret}
    return base_responses.jsonify_response(result)


@group_dining.route("/signin/<string:dining_id>", methods=["POST"])
def signin(dining_id):
    """签到"""
    dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
    invitation = InvitationDataAccessHelper().get_invitation(dining_id=dining_id, user_id=dining.director_id)
    user_id = requests_utils.get_headers_info(request, "userId")
    if user_id != dining.director_id and not invitation.signin:
        resp = create_responses_obj(error_codes.DIRECTOR_DID_NOT_SIGNINED,
                                    error_codes.DIRECTOR_DID_NOT_SIGNINED_MSG)
        return base_responses.jsonify_response(status_response=resp)
    InvitationDataAccessHelper().update(dining_id=dining_id, user_id=user_id, signin=True)
    return base_responses.jsonify_response()


@group_dining.route('/qrcode', methods=['POST'])
def qrcode():
    """ 用户扫码进入饭局主页时生成饭局二维码.并创建饭局
    如果4个小时内用户在这个商户这个桌台有一个还未支付的饭局,那么就么说新建一个
    否则就返回原来的饭局信息
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    people_count = int(request.json.get('peopleCount', 1))
    merchant_id = request.json.get('merchantId')
    table_id = request.json.get('tableId')
    table = OrderingServiceDataAccessHelper().get_table(ordering_service_table_id=table_id)
    table_id = table.id
    payment_rule = group_dining_pb.GroupDiningEvent.PaymentRule.Value(request.json.get("paymentRule", "ALL_SHARING"))

    start_time = int(maya.when('4 hours ago').epoch)
    end_time = int(maya.when('now').epoch)
    state = group_dining_pb.Invitation.ACCEPTED
    invitation_dao = InvitationDataAccessHelper()
    # 用户在这个商户4个小时内的一个饭局
    recently_invitation = invitation_dao.get_recently_accepted_group_dining(
        user_id, merchant_id, start_time, end_time, state)
    group_dining = None
    manager = OrderManager(merchant_id=merchant_id)

    policies = []
    group_dining_id = ''

    if manager.registration_info.ordering_config.enable_group_dining:
        logger.info('门店开通约饭: {}'.format(merchant_id))
        if recently_invitation and recently_invitation.monetary_state == group_dining_pb.Invitation.PAYMENT_PENDING:
            # 如果存在4小时内的饭局,并且是未支付的.那么就返回相关信息
            group_dining = GroupDiningDataAccessHelper().get_dining_by_id(recently_invitation.dining_event_id)
            domain = config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME)
            qrcode_url = "{}/{}".format(domain, 'group_dining/qrcode/{}'.format(group_dining.id))
            logger.info('用户有进行中的饭局,直接返回饭局相关信息: {}, {}'.format(user_id, group_dining.id))
            group_dining_id = group_dining.id
        if not group_dining:
            qrcode = BaseHelper().generate_group_dining_event_wxcode_with_table(
                merchant_id, user_id, people_count, payment_rule, table_id)
            qrcode_url = qrcode.qrcode_url
            manager = GroupDiningManager(
                merchant_id=merchant_id, dining_id=qrcode.group_dining_event_id, user_id=user_id)
            now = int(maya.when('now').datetime(to_timezone='Asia/Shanghai').timestamp())
            visibility = group_dining_pb.GroupDiningEvent.PUBLIC
            _, group_dining = manager.create_group_dining_event(
                initiator_id=user_id, event_time=now, max_group_size=people_count,
                payment_rule=payment_rule, visibility=visibility)
            if group_dining:
                # 第一次创建,局长默认加入
                manager.accept_invitation()
                group_dining_id = group_dining.id
            logger.info('用户没有正在进行中的饭局,新建一个饭局: {}, {}'.format(user_id, group_dining.id))

        policies = group_dining.group_dining_coupon_policies
        policies = [json_format.MessageToDict(policy, including_default_value_fields=True) for policy in policies]
    else:
        logger.info('门店未开通约饭: {}'.format(merchant_id))

    ret = {
        'qrcodeUrl': qrcode_url,
        'policies': policies,
        'userUplimit': people_count,  # 饭局人数上限
        'userCnt': 1,  # 当前已加入人数
        'groupDiningEventId': group_dining_id
    }
    return base_responses.jsonify_response(data=ret)


@group_dining.route("/get_transfer_fee/<string:group_dining_event_id>", methods=["GET"])
def get_transfer_fee(group_dining_event_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    dining = GroupDiningViewObjectHelper().get_dining_detail(user_id, group_dining_event_id)
    order = OrderingServiceDataAccessHelper().get_order(dining_id=dining.id)
    table = OrderingServiceDataAccessHelper().get_table(ordering_service_table_id=order.table_id)
    invitations = InvitationDataAccessHelper().get_invitations(
        dining_id=group_dining_event_id, state=group_dining_pb.Invitation.ACCEPTED)
    paid_count, not_paid_count = 0, 0
    userlist = []

    count = len(invitations)
    aa_fee = int(math.ceil(float(order.paid_fee) / count))

    current_user_paid = False
    for invitation in invitations:
        if invitation.inviter_id == invitation.invitee_id:
            # 支付者不用再返回
            continue
        user = UserDataAccessHelper().get_user(invitation.invitee_id)
        if invitation.transaction_id:
            transaction = TransactionDataAccessHelper().get_transaction_by_id(invitation.transaction_id)
            if transaction.state == wallet_pb.Transaction.SUCCESS:
                if user.id == user_id:
                    current_user_paid = True
                paid_count += 1
                userlist.append({
                    'headimgurl': user.wechat_profile.headimgurl,
                    'nickname': user.wechat_profile.nickname,
                    'fee': transaction.paid_fee,
                    'paid': True
                })
                continue
        userlist.append({
            'headimgurl': user.wechat_profile.headimgurl,
            'nickname': user.wechat_profile.nickname,
            'fee': 0,
            'paid': False
        })
        not_paid_count += 1
    red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
    red_packet_id = ''
    if red_packet and red_packet.value_assignments.get(user_id) is not None and not red_packet.drawn_users.get(user_id) is not None:
        red_packet_id = red_packet.id

    ret = {
        'tableName': table.name,
        'billFee': order.paid_fee,
        'paidFee': order.paid_fee - aa_fee,
        'headimgurl': dining.headimgurl,
        'nickname': dining.nickname,
        'fee': aa_fee,
        'userList': userlist,
        'paidCount': paid_count,
        'notPaidCount': not_paid_count,
        'directorId': dining.director_id,
        'paid': current_user_paid,
        'redPacketId': red_packet_id
    }
    return base_responses.jsonify_response(ret)


@group_dining.route('/policies/<string:group_dining_event_id>', methods=['GET'])
def get_group_dining_policy(group_dining_event_id):
    dining = GroupDiningDataAccessHelper().get_dining_by_id(group_dining_event_id)
    if dining:
        policies = dining.group_dining_coupon_policies
        policies = [json_format.MessageToDict(policy) for policy in policies]
        qrcode_url = "{}{}".format(config.get_config_value(constants.SERVICE_DOMAIN_ENV_NAME), '/group_dining/qrcode/{}'.format(group_dining_event_id))
        user_cnt = InvitationDataAccessHelper().count_invitation(dining_id=group_dining_event_id)
        ret = {
            'qrcodeUrl': qrcode_url,
            'policies': policies,
            'userUplimit': dining.max_group_size,  # 饭局人数上限
            'userCnt': user_cnt,  # 当前已加入人数
            'groupDiningEventId': group_dining_event_id,
            'directorId': dining.director_id
        }
        return base_responses.jsonify_response(ret)
    return base_responses.jsonify_response()

# -*- coding: utf-8 -*-


import logging

from flask import Blueprint
from flask import request

from google.protobuf import json_format

from business_ops.shopping_card_manager import ShoppingCardManager
from business_ops.ordering.table_manager import TableManager
from business_ops.ordering.order_manager import OrderManager
from common.utils import distribute_lock
from common.cache_server_keys import CacheServerKeys
from dao.user_da_helper import UserDataAccessHelper
from service import base_responses
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)

bp_name = "shopping_card"

_shopping_card = Blueprint(bp_name, bp_name, url_prefix="/shopping-card")


@_shopping_card.route("/query", methods=["POST"])
def get_shopping_card():
    """ 获取桌台当前的购物车信息
    """
    merchant_id = request.json.get("merchantId", None)
    table_id = request.json.get("tableId", None)
    opnumber = request.json.get("opnumber", None)
    if not table_id:
        return base_responses.jsonify_response()
    shopping_card_manager = ShoppingCardManager(merchant_id=merchant_id)
    table_manager = TableManager(merchant=shopping_card_manager.merchant)
    table = table_manager.get_table_by_id(ordering_service_table_id=table_id)
    if not table:
        raise errors.Error(err=error_codes.TABLE_NOT_EXISTS)
    shopping_card = shopping_card_manager.get_shopping_card(table=table)
    if not shopping_card:
        return base_responses.jsonify_response()
    if opnumber is not None and shopping_card.opnumber == opnumber:
        raise errors.ShoppingCardNoNeedUpdate()
    shopping_card = json_format.MessageToDict(shopping_card, including_default_value_fields=True)
    return base_responses.jsonify_response(shopping_card)


@_shopping_card.route("/update", methods=["POST"])
def add_or_update_shopping_card_dish():
    """ 创建或者更新购物车的菜品
    """
    table_id = request.json.get("tableId", None)
    merchant_id = request.json.get("merchantId", None)
    dish_list = request.json.get("dishList", None)
    remove_uuids = request.json.get("removeUuids", None)
    dish_dict = request.json.get("dishDict", None)
    dishes_dict = request.json.get("dishesDict", None)
    people_count = request.json.get("peopleCount", None)
    session_id = request.json.get("sessionId", None)
    opnumber = request.json.get("opnumber", 1)
    user_id = request.headers.get("userId", None)
    is_first_time_required_items = request.json.get("isFirstTimeRequiredItems", None)
    key = CacheServerKeys.get_table_shopping_card_key(table_id)
    ttl = 30000
    retry_count = 10
    retry_delay = 1000
    outer_ex = None
    with distribute_lock.redislock(key, ttl, retry_count, retry_delay) as lock:
        if not lock:
            return base_responses.jsonify_response()
        try:

            order_manager = OrderManager(merchant_id=merchant_id)
            shopping_card_manager = ShoppingCardManager(
                merchant=order_manager.merchant, order_manager=order_manager)
            table_manager = TableManager(merchant=order_manager.merchant)
            table = table_manager.get_table_by_id(ordering_service_table_id=table_id)

            # 如果购物车存在,session_id又为空
            # 说明两个人同时创建购物车了.后面一个人需要刷新之后重试
            shopping_card = shopping_card_manager.get_shopping_card(table)
            if shopping_card and (session_id is None or session_id == ""):
                session_id = shopping_card.session_id

            # 如果用户第一次自动添加必选菜,但是必选菜已经被别的用户自动添加了,那么直接返回购物车
            if shopping_card and is_first_time_required_items is not None and shopping_card.is_required_item_added:
                shopping_card = json_format.MessageToDict(
                    shopping_card, including_default_value_fields=True)
                return base_responses.jsonify_response(shopping_card)

            if not shopping_card:
                shopping_card = shopping_card_manager.create_shopping_card(
                    table, dish_list, people_count=people_count)
            else:
                shopping_card_manager.add_dishes_to_shopping_card(shopping_card, dish_list)
            if dish_dict is not None:
                shopping_card_manager.update_dish_in_shopping_card(
                    shopping_card, dish_dict)
            if dishes_dict is not None:
                for d in dishes_dict:
                    shopping_card_manager.update_dish_in_shopping_card(
                        shopping_card, d)
            if remove_uuids is not None:
                shopping_card_manager.remove_dish_from_shopping_card(
                    shopping_card, remove_uuids)
            logger.info(f"购物车: {shopping_card.opnumber} {opnumber}")
            shopping_card.opnumber += 1
            if is_first_time_required_items:
                shopping_card.is_required_item_added = True
            if user_id is not None:
                user_da = UserDataAccessHelper()
                user = user_da.get_user(user_id)
                if user:
                    shopping_card.users[user.id].added_user_id = user.id
                    shopping_card.users[user.id].added_nickname = user.member_profile.nickname
                    shopping_card.users[user.id].added_headimgurl = user.member_profile.head_image_url
            shopping_card_manager.add_or_update_shopping_card_cache(shopping_card)
            shopping_card_manager.send_message()
            shopping_card = json_format.MessageToDict(
                shopping_card, including_default_value_fields=True)
            return base_responses.jsonify_response(shopping_card)
        except Exception as ex:
            outer_ex = ex
    raise outer_ex

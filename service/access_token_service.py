# -*- coding: utf-8 -*-

import logging

from flask import request, Blueprint
from google.protobuf import json_format

from common.config import config
import common.logger.main_service_logger
from common import constants

from common.utils import date_utils
from common.utils import distribute_lock
from common.base_app import create_app
from dao.auth_da_helper import AuthDataAccessHelper
from proto import authorizer_pb2 as authorizer_pb
from service import auth_service_helper
from service.base_responses import make_json_response
from service.base_responses import success_responses_obj
from service.base_responses import error_responses
from wechat_lib import ticket_api_helper


logger = logging.getLogger("access_token_service")

"""
# API
获取有效的开发者token
    请求方式:
       GET /get_token
    Args:
        无
    Returns:
        component_access_token：有效的token

获取授权方的帐号基本信息
    http请求方式:
        GPT /auth_process/api_get_authorizer_info
    Args:
        authorizer_appid: 授权方appid
    Returns:
        authorizer_info: 授权方的帐号基本信息 详见：https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
"""

name = "access_token"
_access_token_bp = Blueprint(name, name)


@_access_token_bp.route('/get_component_token', methods=['GET'])
def get_component_token_api():
    """ 获取有效的开发者token
    """
    appid = config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
    token = get_and_update_access_token_if_needed(appid)
    return make_json_response(status_response={'access_token': token.access_token})

@_access_token_bp.route('/get_platform_token', methods=['GET'])
def get_platform_token_api():
    """ 根据平台获取开发者token
    """
    appid = config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
    token = get_and_update_access_token_if_needed(appid)
    return make_json_response(status_response={'access_token': token.access_token})

@_access_token_bp.route('/get_authorizer_token/<string:appid>', methods=['GET'])
def get_authorizer_token_api(appid):
    """ 根据appid获取开发者token
    """
    token = get_and_update_access_token_if_needed(appid)
    return make_json_response(status_response={'access_token': token.access_token})

@_access_token_bp.route('/get_token', methods=['GET'])
def token_api():
    """ 获取有效的开发者token
    """
    appid = config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
    token = get_and_update_access_token_if_needed(appid)
    return make_json_response({'access_token': token.access_token})

@_access_token_bp.route('/authorizer_token', methods=['POST'])
def add_authorizer_token():
    """ 添加开发者token
    """
    authorizer_token = json_format.ParseDict(request.json, authorizer_pb.AuthorizerToken(), ignore_unknown_fields=True)
    AuthDataAccessHelper().set_authorizer_token(authorizer_token)
    response = make_json_response(status_response={'access_token': authorizer_token.access_token})
    return response

@_access_token_bp.route('/authorizer_api_ticket', methods=['POST'])
def add_authorizer_api_ticket():
    """ 添加authorizer api ticket
    """
    ticket = json_format.ParseDict(request.json, authorizer_pb.AuthorizerTicket(), ignore_unknown_fields=True)
    AuthDataAccessHelper().set_authorizer_api_ticket(ticket)
    return make_json_response()

@_access_token_bp.route('/get_wechat_api_ticket/<string:appid>/<string:ticket_type_str>', methods=['GET'])
def get_wechat_api_ticket(appid, ticket_type_str):
    ticket_type = None
    if ticket_type_str.upper() == 'WX_CARD':
        ticket_type = authorizer_pb.AuthorizerTicket.WX_CARD
    elif ticket_type_str.upper() == 'JS_API':
        ticket_type = authorizer_pb.AuthorizerTicket.JS_API
    if ticket_type is not None:
        api_ticket = get_and_update_ticket_if_needed(appid, ticket_type)
        response_obj = success_responses_obj()
        response_obj['ticket'] = api_ticket.ticket
    else:
        response_obj = error_responses()
    return make_json_response(response_obj)

def get_and_update_ticket_if_needed(appid, ticket_type):
    """ 获取最新的ticket,如果ticket快到期了,就尝试更新它
    Args:
        appid: (string)
        ticket_type: authorizer_pb.AuthorizerTicket.TicketType
    Return:
        最新的ticket
    """
    key = "{}-{}-ticket".format(appid, ticket_type)
    auth_da = AuthDataAccessHelper()
    with distribute_lock.redislock(key=key, ttl=3000, retry_count=2, retry_delay=200) as lock:
        if lock:
            ticket = auth_da.get_authorizer_api_ticket(appid, ticket_type)
            if ticket:
                expires_in = ticket.expires_in
                timestamp = ticket.updated_time
                current_timestamp = date_utils.timestamp_second()
                # 如果ticket的剩余过期时间大于5分钟,直接返回
                if current_timestamp < (timestamp + expires_in - 60 * 5):
                    return ticket
            access_token = get_and_update_access_token_if_needed(appid)
            updated_ticket = None
            if ticket_type == authorizer_pb.AuthorizerTicket.JS_API:
                updated_ticket = ticket_api_helper.get_ticket_for_js_api(access_token=access_token.access_token,
                                                                         appid=appid)
            elif ticket_type == authorizer_pb.AuthorizerTicket.WX_CARD:
                updated_ticket = ticket_api_helper.get_ticket_for_card_api(access_token=access_token.access_token,
                                                                           appid=appid)
            if updated_ticket:
                auth_da.set_authorizer_api_ticket(updated_ticket)
                return updated_ticket
    return auth_da.get_authorizer_api_ticket(appid, ticket_type)

def get_and_update_access_token_if_needed(appid):
    """ 获取最新的access_token,如果access_token快过期了,就尝试更新它
   Args:
        appid: (string)
    Return:
        最新的access_token
    """
    key = "{}-access_token".format(appid)
    auth_da = AuthDataAccessHelper()
    with distribute_lock.redislock(key=key, ttl=3000, retry_count=2, retry_delay=200) as lock:
        if lock:
            token = auth_da.get_authorizer_token(appid)
            refresh_token = None
            if token:
                refresh_token = token.refresh_token
                expires_in = token.expires_in
                timestamp = token.access_token_updated_time
                current_timestamp = date_utils.timestamp_second()
                # 如果token的剩余过期时间大于5分钟,直接返回
                if current_timestamp < (timestamp / 1000 + expires_in - 60 * 5):
                    return token
            if appid == config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME):
                logger.info("更新开发者授权token...")
                secret = config.get_config_value(constants.WECHAT_APP_SECRET_ENV_NAME)
                ticket = auth_da.get_component_verify_ticket(appid)
                ticket_value = ticket.component_verify_ticket
                result = auth_service_helper.get_component_access_token(appid, secret, ticket_value)
                if not 'errcode' in result:
                    token.access_token = result['component_access_token']
                    token.expires_in = result['expires_in']
                    token.access_token_updated_time = date_utils.timestamp_millisecond()
            elif appid == config.SHILAI_MP_APPID:
                logger.info('更新时来公众号token')
                token = _update_authorizer_token(config.SHILAI_MP_APPID,
                                                 config.SHILAI_MP_APP_SECRET,
                                                 authorizer_token=token)

            elif appid == config.WECHAT_MINIPROGRAM_MERCHANT_APPID:
                logger.info('更新商户小程序授权token')
                token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_MERCHANT_APPID,
                                                 config.WECHAT_MINIPROGRAM_MERCHANT_SECRET,
                                                 authorizer_token=token)

            elif appid == config.WECHAT_MINIPROGRAM_APPID:
                logger.info('更新C端小程序授权token..')
                token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_APPID,
                                                 config.WECHAT_MINIPROGRAM_SECRET,
                                                 authorizer_token=token)

            elif appid == config.WECHAT_MINIPROGRAM_STAFF_APPID:
                logger.info('更新业务助手小程序授权token')
                token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_STAFF_APPID,
                                                 config.WECHAT_MINIPROGRAM_STAFF_SECRET,
                                                 authorizer_token=token)

            elif refresh_token:
                result = auth_service_helper.get_api_authorizer_token(config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME),
                    appid, refresh_token)
                if 'errcode' not in result:
                    token.access_token = result['authorizer_access_token']
                    token.refresh_token = result['authorizer_refresh_token']
                    token.expires_in = result['expires_in']
                    token.access_token_updated_time = date_utils.timestamp_millisecond()
                    logger.info("更新商户的授权token 成功")
                else:
                    logger.info("更新商户的授权token 失败")
            if token:
                auth_da.set_authorizer_token(token)
                return token
    return auth_da.get_authorizer_token(appid)

def init_token():
    """初始化时来账号(开放平台、公众号、小程序)Token
    """

    auth_da = AuthDataAccessHelper()
    # 更新开发者token
    ticket = auth_da.get_component_verify_ticket(config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME))
    ticket_value = ticket.component_verify_ticket
    result = auth_service_helper.get_component_access_token(config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME),
        config.get_config_value(constants.WECHAT_APP_SECRET_ENV_NAME), ticket_value)
    data = authorizer_pb.AuthorizerToken()
    data.appid = config.get_config_value(constants.WECHAT_APP_ID_ENV_NAME)
    data.access_token = result['component_access_token']
    data.expires_in = result['expires_in']
    data.access_token_updated_time = date_utils.timestamp_millisecond()
    auth_da.set_authorizer_token(data)

    # 更新时来公众号token
    shilai_mp_token = _update_authorizer_token(config.SHILAI_MP_APPID,
                                               config.SHILAI_MP_APP_SECRET)
    auth_da.set_authorizer_token(shilai_mp_token)

    # 更新商户小程序token
    merchant_miniprogram_token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_MERCHANT_APPID,
                                                          config.WECHAT_MINIPROGRAM_MERCHANT_SECRET)
    auth_da.set_authorizer_token(merchant_miniprogram_token)

    # 更新C端小程序token
    user_miniprogram_token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_APPID,
                                                      config.WECHAT_MINIPROGRAM_SECRET)
    auth_da.set_authorizer_token(user_miniprogram_token)

    # 更新业务助手token
    staff_miniprogram_token = _update_authorizer_token(config.WECHAT_MINIPROGRAM_STAFF_APPID,
                                                       config.WECHAT_MINIPROGRAM_STAFF_SECRET)
    auth_da.set_authorizer_token(staff_miniprogram_token)

def init_ticket():
    """初始化时来账号(开放平台、公众号、小程序)的Ticket

    """
    auth_da = AuthDataAccessHelper()

    # 更新时来公众号ticket
    authorizer_token = auth_da.get_authorizer_token(config.SHILAI_MP_APPID)
    card_ticket = ticket_api_helper.get_ticket_for_card_api(access_token=authorizer_token.access_token,
                                                            appid=authorizer_token.appid)
    js_ticket = ticket_api_helper.get_ticket_for_js_api(access_token=authorizer_token.access_token,
                                                        appid=authorizer_token.appid)
    auth_da.set_authorizer_api_ticket(card_ticket)
    auth_da.set_authorizer_api_ticket(js_ticket)

def _update_authorizer_token(appid, secret, authorizer_token=None):
    """更新 authorizer_token

    Args:
        appid: (string) appid
        secret: (string) 密钥
        authorizer_token: (AuthorizerToken) 实例
    """
    if authorizer_token is None:
        authorizer_token = authorizer_pb.AuthorizerToken()
        authorizer_token.appid = appid

    result = auth_service_helper.get_wechat_app_access_token(appid, secret)
    logger.info(f"_update_authorizer_token={result}, appid={appid}, secret={secret}")
    authorizer_token.access_token = result['access_token']
    authorizer_token.expires_in = result['expires_in']
    authorizer_token.access_token_updated_time = date_utils.timestamp_millisecond()
    return authorizer_token


app = create_app(blueprints=[_access_token_bp])


if __name__ == "__main__":
    # init_token()
    # init_ticket()
    app.config.update(DEBUG=True)
    app.run(host="0.0.0.0", port=int(config.get_config_value(constants.ACCESS_TOKEN_SERVICE_PORT_ENV_NAME)))
    # app.run(host="0.0.0.0", port=8886)

# -*- coding: utf-8 -*-

from flask import Blueprint

from common import protobuf_transformer

from controller.promotion.coupon.coupon_controller import CouponController
from service.base_responses import jsonify_response


bp_name = 'coupon_v2'
_coupon_v2 = Blueprint(bp_name, bp_name, url_prefix='/coupon_v2')


@_coupon_v2.route("/<string:operation>", methods=["POST"])
def operate(operation):
    ctrl = CouponController(operation)
    result = ctrl.do_operate()
    if result is None:
        return jsonify_response()
    if isinstance(result, list):
        return jsonify_response(protobuf_transformer.batch_protobuf_to_dict(result))
    return jsonify_response(protobuf_transformer.protobuf_to_dict(result))

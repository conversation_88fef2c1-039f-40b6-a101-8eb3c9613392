# -*- coding: utf-8 -*-

from flask import Blueprint

from common import protobuf_transformer

from controller.promotion.group_purchase.group_purchase_invitation_controller import GroupPurchaseInvitationController
from service.base_responses import jsonify_response


bp_name = 'group_purchase_invitation'
_group_purchase_invitation = Blueprint(bp_name, bp_name, url_prefix='/group_purchase_invitation')


@_group_purchase_invitation.route("/<string:operation>", methods=["POST"])
def operate(operation):
    ctrl = GroupPurchaseInvitationController(operation)
    result = ctrl.do_operate()
    if result is None:
        return jsonify_response()
    if isinstance(result, list):
        return jsonify_response(protobuf_transformer.batch_protobuf_to_dict(result))
    return jsonify_response(protobuf_transformer.protobuf_to_dict(result))

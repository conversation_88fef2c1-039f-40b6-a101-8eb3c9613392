from flask import Blueprint
from flask import request
from business_ops.transaction_analysis.transaction_analysis_manager import TransactionAnalysisManager
from service.base_responses import jsonify_response

transaction_analysis = Blueprint("transaction_analysis", __name__, url_prefix="/merchant/transaction_analysis")


@transaction_analysis.route("/merchant", methods=["POST"])
def get_merchant_basic_info():
    manager = TransactionAnalysisManager()
    result = manager.get_merchant_simple_info()
    return jsonify_response(result)


@transaction_analysis.route("/coupon", methods=["POST"])
def get_coupon_aggregation():
    merchant_id = request.json.get("merchantId")
    start_date = request.json.get("start")
    end_date = request.json.get("end")
    manager = TransactionAnalysisManager(merchant_id=merchant_id)
    result = manager.aggregate_coupon_transaction(start_date, end_date)
    return jsonify_response(result)


@transaction_analysis.route("/fanpiao", methods=["POST"])
def get_fanpiao_aggregation():
    merchant_id = request.json.get("merchantId")
    start_date = request.json.get("start")
    end_date = request.json.get("end")
    manager = TransactionAnalysisManager(merchant_id=merchant_id)
    result = manager.aggregate_fanpiao_transaction(start_date, end_date)
    return jsonify_response(result)

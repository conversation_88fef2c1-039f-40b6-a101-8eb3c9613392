# -*- coding: utf-8 -*-


"""
用于前端日志记录
"""

import time

from flask import Blueprint
from flask import request

from dao.frontend_log_da_helper import FrontendLogDataAccessHelper
from service.base_responses import jsonify_response

_flsbp = Blueprint(__name__, __name__, url_prefix="/flsbp")


@_flsbp.route("/record", methods=["POST"])
def save_record():
    value = request.json.get("value", None)
    user_id = request.headers.get("userId", None)
    if value is None:
        return jsonify_response()
    if isinstance(value, dict):
        if value.get("timestamp") is None:
            value.update({"timestamp": int(time.time())})
        if value.get("userId") is None:
            value.update({"userId": user_id})
        FrontendLogDataAccessHelper().add_or_update_log(value)
    return jsonify_response()


@_flsbp.route("/record", methods=["GET"])
def get_record():
    user_id = request.headers.get("userId", None)
    logs = FrontendLogDataAccessHelper().get_logs(user_id)
    if not logs:
        return jsonify_response()
    logs = [l for l in logs]
    return jsonify_response(logs)

# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request
from flask import jsonify
import urllib
from google.protobuf import json_format

import proto.merchant_rules_pb2 as merchant_pb
from dao.base_helper import BaseHelper
from business_ops.merchant_manager import MerchantManager
from business_ops.staff_manager import StaffManager
from common import constants
from common.utils import requests_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto import staff_pb2 as staff_pb
from service import error_codes
from service.auth_service_helper import UserAuthHelper
from service.base_responses import create_responses_obj
from service.merchant_service import merchant
from service.base_responses import error_responses
from service.base_responses import success_responses_obj
from service.base_responses import jsonify_response

staff = Blueprint('staff', __name__)


@staff.route('/staff/merchant/qrcode/<string:qrcode_id>', methods=['GET'])
def merchant_barcode_get(qrcode_id):
    """
    获取商家授权二维码
    GET /merchant/qrcode/<string:qrcode_id>
    请求参数

    响应主体
        二维码图片流
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    """
    resp = BaseHelper().get_merchant_barcode(qrcode_id)
    return resp


@staff.route('/staff/merchant/get_qrcode_info', methods=['GET'])
def get_qrcode_info():
    """
        获取商家二维码id 对应的 业务员和商家信息
        GET /merchant/get_qrcodeid_info
        请求参数
            qrcodeId
        响应主体
            {staffId, merchantId}
    """
    qrcode_id = request.values.get("qrcodeId")

    qrcode_id_info = BaseHelper().get_merchant_qrcode_id_info(qrcode_id)
    if qrcode_id_info:
        merchant_id = qrcode_id_info['merchantId']
        staff_id = qrcode_id_info['staffId']
        staff = StaffDataAccessHelper().get_staff(staff_id)
        if not staff.is_certified:
            return jsonify(create_responses_obj(
                error_codes.STAFF_NOT_CERTIFIED, error_codes.STAFF_NOT_CERTIFIED_MSG))

        success_responses_obj = create_responses_obj(0, 'success')
        if 'null' == merchant_id:
            success_responses_obj["staffId"] = staff_id
        else:
            success_responses_obj["staffId"] = staff_id
            success_responses_obj["merchantId"] = merchant_id

        resp = jsonify(success_responses_obj)
        return resp
    else:
        resp = error_responses()
        return resp


def get_managed_merchants(staff_id, status):
    """获取业务员名下所有关联商家(若为经理角色，则包括所有下属员工关联商家)的全部信息。

        Args:
            staff_id: (string) 员工ID
            status: (merchant_pb.MerchantStatus) 商家状态

        Returns: (list of Merchant) 所有关联商家
    """
    merchant_da = MerchantDataAccessHelper()
    staff_da = StaffDataAccessHelper()
    managed_merchants = merchant_da.get_merchant_list_bound_to_staff(staff_id, status=status)
    assist_managed_merchants = merchant_da.get_merchant_list_bound_to_assist_staff(staff_id, status=status)
    managed_merchants.extend(assist_managed_merchants)
    managed_staff_list = staff_da.get_managed_staff_list(staff_id)
    if not managed_staff_list:
        return managed_merchants

    for staff in managed_staff_list:
        merchants = get_managed_merchants(staff.id, status)
        managed_merchants.extend(merchants)

    return managed_merchants


@merchant.route("/staff/merchant/list/v2", methods=["GET"])
def get_staff_merchant_list_v2():
    staff_manager = StaffManager()
    staff_id = requests_utils.get_headers_info(request, "userId")
    status = request.args.get("status", None)
    merchant_name = request.args.get("merchantName", None)
    pos_type = request.args.get("posType", None)
    if merchant_name is not None:
        merchant_name = urllib.parse.unquote(merchant_name)
    prev_create_timestamp = request.args.get("prevCreateTimestamp", None)
    size = request.args.get("size", 100)
    if size:
        size = int(size)
    merchants = staff_manager.get_staff_merchant_list_with_specified_pos_type(
        staff_id, prev_create_timestamp, status, size, fuzzy_query_name=merchant_name, pos_type=pos_type)
    merchants = staff_manager.get_staff_merchants_info(merchants)
    return jsonify_response(merchants)


@merchant.route("/staff/demonstration/merchant/list", methods=["GET"])
def get_demonstration_merchant_list():
    staff_manager = StaffManager()
    merchants = staff_manager.get_demonstration_merchant_list()
    return jsonify_response(merchants)


@merchant.route('/staff/merchant/list', methods=['GET'])
def get_staff_merchant_list():
    """
        获取业务员名下所有商家的全部信息
        请求方式:
            GET /merchant/list
        Args:
            {staffId}
         Returns:
            {basic_info}
            data : merchant.Merchant()
    """
    staff_id = requests_utils.get_headers_info(request, "userId")
    status = request.args.get("status", None)
    if status is not None:
        status = merchant_pb.MerchantStatus.Value(status)
    prev_create_timestamp = request.args.get("prevCreateTimestamp", None)
    size = request.args.get("size", 5000)
    if size:
        size = int(size)

    merchant_manager = MerchantManager()
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff(staff_id)
    if not staff:
        return jsonify_response()
    staffs = staff_da.get_staff_list(manager_id=staff_id)
    if staff.role == staff_pb.ShilaiStaff.ADMIN:
        merchant_list = merchant_manager.get_staff_merchant_list(
            prev_create_timestamp=prev_create_timestamp, size=size, status=status, admin=True)
    else:
        if staff.role == staff_pb.ShilaiStaff.MANAGER:
            staffs.append(staff)
            merchant_list = merchant_manager.get_staff_merchant_list(
                prev_create_timestamp=prev_create_timestamp, size=size, status=status, staffs=staffs)
        else:
            merchant_list = merchant_manager.get_staff_merchant_list(
                user_id=staff_id, prev_create_timestamp=prev_create_timestamp, size=size, status=status)

    if merchant_list:
        ordering_da = OrderingServiceDataAccessHelper()
        merchants = [json_format.MessageToDict(merchant, including_default_value_fields=True)
                     for merchant in merchant_list]
        for m in merchants:
            registration_info = ordering_da.get_registration_info(merchant_id=m.get("id"))
            if not registration_info:
                m.update({"posType": "SHILAI"})
            else:
                registration_info = json_format.MessageToDict(registration_info, including_default_value_fields=True)
                m.update({"posType": registration_info.get("posType")})
            staff = staff_da.get_staff(m.get("bindingStaffId"))
            if staff and staff.wechat_profile:
                m.update({"bindingStaffNickname": staff.wechat_profile.nickname})
        return jsonify_response(merchants)
    return jsonify_response()


@staff.route("/staff/fuzzy-query/merchant", methods=["GET"])
def fuzzy_query_merchant():
    staff_id = requests_utils.get_headers_info(request, "userId")
    name = request.args.get("name", None)
    status = request.args.get("status", None)
    version = request.args.get("version", "1.0")
    if status is not None:
        status = merchant_pb.MerchantStatus.Value(status)
    merchant_manager = MerchantManager()
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff(staff_id)
    if not staff:
        return jsonify_response()
    staffs = staff_da.get_staff_list(manager_id=staff_id)
    if staff.role == staff_pb.ShilaiStaff.ADMIN:
        merchant_list = merchant_manager.fuzzy_query_merchant(name=name, status=status, admin=True)
    else:
        if staff.role == staff_pb.ShilaiStaff.MANAGER:
            staff_ids = [s.id for s in staffs]
            staff_ids.append(staff.id)
            merchant_list = merchant_manager.fuzzy_query_merchant(
                name=name, status=status, staff_ids=staff_ids)
        else:
            merchant_list = merchant_manager.fuzzy_query_merchant(
                name=name, user_id=staff_id, status=status)

    if merchant_list and version == "1.0":
        merchants = [json_format.MessageToDict(merchant, including_default_value_fields=True)
                     for merchant in merchant_list]
        for m in merchants:
            staff = staff_da.get_staff(m.get("bindingStaffId"))
            if staff and staff.wechat_profile:
                m.update({"bindingStaffNickname": staff.wechat_profile.nickname})
        return jsonify_response(merchants)
    elif merchant_list and version == "2.0":
        staff_manager = StaffManager()
        merchants = staff_manager.get_staff_merchants_info(merchants)
        return jsonify_response(merchants)

    return jsonify_response()


@staff.route('/staff/qrcode/create', methods=['OPTIONS', 'POST'])
def staff_qrcode_create():
    """
    生成拓展员二维码小程序二维码
    GET /merchant/qrcode
    请求参数 {staffId}
    响应主体 {qrcodeUrl}
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    #
    """
    staff_id = requests_utils.get_headers_info(request, "userId")
    bar_code_url = BaseHelper().create_qrcode(staff_id=staff_id, platform=constants.PLATFORM_SHILAI_STAFF)
    success_responses_obj = create_responses_obj(0, 'success')
    success_responses_obj['barCodeUrl'] = bar_code_url
    resp = jsonify(success_responses_obj)
    return resp


@staff.route('/staff/info', methods=['GET'])
def staff_info():
    """查询staff信息
    请求方式:
       GET /staff/info
    Args:
        userId
    Returns:
        merchant : staff_pb2.ShilaiStaff()
    """
    staff_id = requests_utils.get_headers_info(request, "userId")
    staff_proto = StaffDataAccessHelper().get_staff(staff_id)
    resp = None
    if staff_proto:
        json_obj = json_format.MessageToDict(staff_proto, including_default_value_fields=True)
        success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
        success_responses_obj['staff'] = json_obj
        resp = jsonify(success_responses_obj)
    else:
        resp = create_responses_obj(error_codes.STAFF_NOT_FOUND, error_codes.STAFF_NOT_FOUND_MSG)

    return resp


@staff.route('/staff/invitation/accept', methods=['POST'])
def staff_invitation_info():
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, "userId")
    invitation_id = request_json['invitationId']
    staff = UserAuthHelper().staff_invitation_info(user_id, invitation_id)
    # 返回结果体
    success_responses_obj = create_responses_obj(0, 'success')
    json_obj = json_format.MessageToDict(staff, including_default_value_fields=True, use_integers_for_enums=True)
    success_responses_obj['staff'] = json_obj
    resp = jsonify(success_responses_obj)
    return resp


@staff.route('/staff/delete', methods=['POST'])
def delete_staff_info(staff_id):

    # 返回结果体
    resp = success_responses_obj()
    resp = jsonify(resp)
    return resp

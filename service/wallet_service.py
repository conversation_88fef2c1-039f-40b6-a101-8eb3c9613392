# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request
from google.protobuf import json_format

import proto.finance.wallet_pb2 as wallet_pb
from business_ops.wallet_manager import WalletManager
from business_ops.payment_manager import PaymentManager
from common.utils import requests_utils
from controller.wallet_controller import WalletController
from controller.vip_membership_controller import VipMembershipController
from dao.user_da_helper import UserDataAccessHelper
from service import errors
from service.base_responses import make_json_response
from service import base_responses

wallet = Blueprint("wallet", __name__, url_prefix="/wallet")
vip_membership_bp = Blueprint("VipMembership", __name__, url_prefix="/vip-membership")


@wallet.route("", methods=["GET"])
def get_my_wallet():
    """获取用户钱包
    Args:
        userId

    Return:
        Wallet结构体
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    # merchant_id = request.args.get('merchantId', None)
    wallet = WalletManager().get_user_wallet(user_id)
    wallet = json_format.MessageToDict(wallet, including_default_value_fields=True)
    return make_json_response(wallet)


@wallet.route("/withdraw", methods=["POST"])
def withdraw():
    """用户发起提现申请"""
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, "userId")
    pay_method = request.json.get("payMethod", "WECHAT_PAY")
    amount = request_json.get("amount", 0)
    if amount is None or amount == 0:
        raise errors.ParameterError()
    user = UserDataAccessHelper().get_user(user_id)
    if not user:
        raise errors.UserNotFound()
    pay_method = wallet_pb.Transaction.PayMethod.Value(pay_method)
    ret = WalletManager().withdraw(user_id=user_id, amount=amount, pay_method=pay_method)
    if ret:
        payment_manager = PaymentManager(pay_method=pay_method)
        payment_manager.withdraw(user, amount)
    return base_responses.jsonify_response()


@wallet.route("/<string:operation>", methods=["POST"])
def user_wallet(operation):
    controller = WalletController(operation)
    result = controller.do_operate()
    return base_responses.jsonify_response(result)


@vip_membership_bp.route("/<string:operation>", methods=["POST"])
def user_wallet(operation):
    controller = VipMembershipController(operation)
    result = controller.do_operate()
    return base_responses.jsonify_response(result)

import logging
import sys
import os

import maya
from flask import Flask
from flask import request
from flask import Blueprint
from google.protobuf import json_format

from business_ops.logistics.dada_manager import DadaManager
from common.utils import id_manager
from service import base_responses
from service import error_codes, errors

logger = logging.getLogger(__name__)
logistics = Blueprint(__name__, __name__, url_prefix='/logistics')

@logistics.route('/dada_order_callback/<string:order_id>', methods=['POST'])
def dada_callback(order_id):
    """ 每次订单状态发生变化时，会对添加订单接口中callback的URL进行回调
    https://newopen.imdada.cn/#/development/file/order?_k=da8zf7
    """
    client_id = request.json.get("client_id")
    order_id = request.json.get('order_id')
    order_status = request.json.get('order_status')
    cancel_reason = request.json.get('cancel_reason')
    cancel_from = request.json.get('cancel_from')
    update_time = int(request.json.get('update_time', 0))
    signature = request.json.get('signature')
    dm_id = request.json.get('dm_id')
    dm_name = request.json.get('dm_name')
    dm_mobile = request.json.get('dm_mobile')
    manager = DadaManager()
    manager.update_order_status(client_id, order_id, order_status, cancel_reason, cancel_from, update_time, signature, dm_id, dm_name, dm_mobile)
    return 'success'

# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops.verification_code_manager import VerificationCodeManager
from business_ops import coupon_category_manager
from business_ops import merchant_store_manager
from business_ops.dish_verification_code_manager import DishVerificationCodeManager
from business_ops.merchant_manager import MerchantManager
from business_ops.brand_dish_verification_code_manager import BrandDishVerificationCodeManager
from common.config import config
from common.utils import date_utils
from common.utils import id_manager
from common.utils import requests_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto import coupon_category_pb2 as coupon_category_pb
from proto import merchant_rules_pb2 as merchant_rules_pb
from proto import strategy_pb2 as strategy_pb
from proto.ordering import registration_pb2 as registration_pb
from service import errors
from service.base_responses import make_json_response
from service.base_responses import jsonify_response
from service.errors import CreateSubmerchantError
from service.errors import MerchantNotFoundError
from view_ops.merchant_view_helper import MerchantViewObjectHelper

# 路由前缀 /merchant/<string:merchant_id>/info
merchant_info = Blueprint("merchant_info", __name__, url_prefix="/merchant/<string:merchant_id>/info")


@merchant_info.before_request
def check_permission():
    """检查访问权限

    Merchant 配置只允许商户管理员和拓展业务员编辑
    """
    merchant_id = request.view_args['merchant_id']
    merchant_da_helper = MerchantDataAccessHelper()
    merchant_manager = MerchantManager()
    merchant = merchant_manager.get_valid_merchant(merchant_id)
    if not merchant:
        raise MerchantNotFoundError()

    del request.view_args['merchant_id']
    setattr(request, 'merchant', merchant)

    if request.method == "POST":
        from_platform = requests_utils.get_platform(request)
        user_id = requests_utils.get_headers_info(request, 'userId')

        if user_id:
            merchants = None
            if from_platform == config.PLATFORM_MERCHANT:
                merchants = merchant_da_helper.get_merchant_list(manager_id=user_id)
            elif from_platform == config.PLATFORM_SHILAI_STAFF:
                merchants = merchant_da_helper.get_merchant_list(binding_staff_id=user_id)

            if merchants:
                for merchant in merchants:
                    if merchant.id == merchant_id:
                        return None
        # raise PermissionError()
    return None


@merchant_info.route("", methods=["GET"])
def get_info():
    """获取商户信息
    """
    merchant = request.merchant
    from_platform = requests_utils.get_platform(request)
    if from_platform == config.PLATFORM_USER:
        merchant = MerchantViewObjectHelper().convert_merchant_info_vo(merchant)

    if merchant:
        merchant_json = json_format.MessageToDict(merchant, including_default_value_fields=True)
        return make_json_response({'merchant': merchant_json})
    return make_json_response()


@merchant_info.route("/basic/save", methods=["POST"])
def save_basic_info():
    """保存商家基本信息
    """
    merchant = request.merchant
    basic_info = json_format.ParseDict(request.json, merchant_rules_pb.MerchantBasic(),
                                       ignore_unknown_fields=True)
    merchant.basic_info.CopyFrom(basic_info)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/store/save", methods=["POST"])
def save_store_info():
    """保存商户单个门店信息
    """
    merchant = request.merchant

    store = json_format.ParseDict(request.json, merchant_rules_pb.Store(),
                                  ignore_unknown_fields=True)
    merchant_store_manager.update_or_create_store(merchant, store)

    return make_json_response()


@merchant_info.route("/qualification/save", methods=["POST"])
def save_qualification_info():
    """保存商户资质信息
    """
    merchant = request.merchant
    qualification_info = json_format.ParseDict(request.json, merchant_rules_pb.MerchantQualifications(),
                                               ignore_unknown_fields=True)
    merchant.merchant_qualifications.CopyFrom(qualification_info)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/payment/save", methods=["POST"])
def save_payment_info():
    """保存商户银行信息
    """
    merchant = request.merchant
    payment_info = json_format.ParseDict(request.json, merchant_rules_pb.PaymentInfo(),
                                         ignore_unknown_fields=True)
    merchant.payment_info.CopyFrom(payment_info)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/submerchant/save", methods=["POST"])
def save_shilai_mp_submerchant_info():
    """保存时来公众号子商户信息
    """
    merchant = request.merchant
    request_json = request.json
    brand_name = requests_utils.get_value_from_json(request_json, 'brandName')
    logo_url = requests_utils.get_value_from_json(request_json, 'logoUrl')
    auth_letter_media_id = requests_utils.get_value_from_json(request_json, 'authLetterMediaId')
    auth_letter_end_time = requests_utils.get_value_from_json(request_json, 'authLetterEndTime')

    auth_letter_end_timestamp_second = date_utils.date_to_timestamp_second(auth_letter_end_time, str_format='%Y-%m-%d')

    merchant = MerchantManager().update_shilai_mp_submerchant_info(merchant.id,
                                                                   brand_name=brand_name,
                                                                   logo_url=logo_url,
                                                                   auth_letter_media_id=auth_letter_media_id,
                                                                   auth_letter_end_time=auth_letter_end_timestamp_second)
    if merchant:
        return make_json_response({
            "merchant": json_format.MessageToDict(merchant, including_default_value_fields=True)
        })
    raise CreateSubmerchantError()


@merchant_info.route("/coupon_config/save", methods=["POST"])
def save_coupon_config():
    """保存优惠券配置
    """
    merchant = request.merchant
    coupon_config = json_format.ParseDict(request.json, merchant_rules_pb.CouponConfig(),
                                          ignore_unknown_fields=True)
    merchant.preferences.coupon_config.CopyFrom(coupon_config)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/design_config/save", methods=["POST"])
def save_design_config():
    """保存设计的全局配置
    """
    merchant = request.merchant
    design_config = json_format.ParseDict(request.json, merchant_rules_pb.DesignConfig(),
                                          ignore_unknown_fields=True)
    merchant.preferences.design_config.CopyFrom(design_config)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/wechat_mch_id/save", methods=["POST"])
def save_wechat_mch_id():
    """保存微信支付子商户号
    """
    request_json = request.json
    merchant = request.merchant
    wechat_mch_id = request_json.get("wechatMchId", None)
    settlement_platform = request_json.get("settlementPlatform", "WECHAT_PAY")

    if settlement_platform == "WECHAT_PAY":
        settlement_platform = merchant_rules_pb.Merchant.WECHAT_PAY
    else:
        settlement_platform = merchant_rules_pb.Merchant.SHILAI_ACCOUNT
        wechat_mch_id = config.SHILAI_SETTLEMENT_ACCOUNT

    merchant.settlement_platform = settlement_platform
    merchant.wechat_mch_id = wechat_mch_id
    MerchantDataAccessHelper().update_or_create_merchant(merchant)

    return make_json_response()


@merchant_info.route("/ordering_service_info", methods=["GET"])
def get_ordering_service_info():
    """ 获取商户信息
    """
    merchant = request.merchant
    registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=merchant.id)
    if registration_info:
        registration_info = json_format.MessageToDict(registration_info, including_default_value_fields=True)
    else:
        registration_info = {}

    return jsonify_response(data=registration_info)


@merchant_info.route("/ordering_service_info/save", methods=["POST"])
def save_ordering_service_info():
    """保存点餐服务相关信息
    """
    registration_info = json_format.ParseDict(request.json, registration_pb.OrderingServiceRegistrationInfo(),
                                              ignore_unknown_fields=True)
    OrderingServiceDataAccessHelper().add_or_update_registration_info(registration_info)
    return make_json_response()


@merchant_info.route("/delete", methods=["POST"])
def delete_merchant_info():
    """ 删除商户信息，更新商户状态为 DELETED
    """
    merchant = request.merchant
    merchant.status = merchant_rules_pb.DELETED
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response()


@merchant_info.route("/coupon_category_spec/save", methods=["POST"])
def save_coupon_category_spec():
    """ 更新拉新券、广告券等优惠券设定
    """
    merchant = request.merchant
    coupon_category_spec = json_format.ParseDict(request.json, strategy_pb.CouponCategorySpec(),
                                                 ignore_unknown_fields=True)
    issue_scene = coupon_category_spec.cash_coupon_spec.coupon_category_spec.issue_scene

    # 更新 id
    if not coupon_category_spec.id:
        coupon_category_spec.id = id_manager.generate_coupon_strategy_id()

    # 更新 name
    if not coupon_category_spec.name:
        if issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER:
            coupon_category_spec.name = "缺省拉新代金券"
        elif issue_scene == coupon_category_pb.CouponCategory.WECHAT_MOMENTS_AD:
            coupon_category_spec.name = "缺省广告代金券"
        else:
            coupon_category_spec.name = "缺省代金券"

    MerchantManager().save_coupon_category_spec(merchant, coupon_category_spec)

    return make_json_response({
        "merchant": json_format.MessageToDict(merchant, including_default_value_fields=True)
    })


@merchant_info.route("/generate_brand_dish_verification_codes", methods=["POST"])
def generate_brand_dish_verification_codes():
    nums = request.json.get("nums", 10)
    strategy_id = request.json.get("strategyId")
    brand_id = request.json.get("brandId")
    BrandDishVerificationCodeManager(brand_id=brand_id).generate_brand_dish_verification_codes(strategy_id, nums)
    return "success"


@merchant_info.route("/generate_dish_verification_codes", methods=["POST"])
def generate_dish_verification_codes():
    nums = request.json.get("nums", 10)
    strategy_id = request.json.get("strategyId")
    brand_id = request.json.get("brandId")
    DishVerificationCodeManager(brand_id=brand_id).generate_dish_verification_codes(strategy_id, nums)
    return "success"


@merchant_info.route("/create_dish_verification_code_strategy", methods=["POST"])
def create_dish_coupon_code_strategy():
    """ 创建菜品核销券策略
    """
    """
    message DateInfo {
    // 目前支持三种计时类型:
    //   1. DATE_TYPE_FIX_TIME_RANGE 表示固定日期区间;
    //   2. DATE_TYPE_FIX_TERM 表示固定时长 (自领取后按天算)。
    //   3. DATE_TYPE_PERMANENT 永久有效类型
    string type = 1;
    // DATE_TYPE_FIX_TIME_RANGE专用, 表示起用时间。
    // 从1970年1月1日00:00:00至起用时间的秒数。
    int64 begin_timestamp = 2;
    // DATE_TYPE_FIX_TIME_RANGE专用, 表示结束时间。
    int64 end_timestamp = 3;
    // DATE_TYPE_FIX_TERM时专用，表示自领取后多少天开始生效，领取后当天生效填写0。（单位为天）
    int32 fixed_begin_term = 4;
    // DATE_TYPE_FIX_TERM专用，表示自生效开始后多少天内有效，不支持填写0。
    int32 fixed_term = 5;
    //  DATE_TYPE_FIX_TERM专用， 表示卡券统一过期时间
    int64 end_time = 6;
    }
    """
    dish_id = request.json.get("dishId")
    name = request.json.get("name", "菜品核销券")
    brand_id = request.json.get("brandId")
    rate = request.json.get("rate")
    date_info = request.json.get("dateInfo")
    strategy_id = DishVerificationCodeManager(brand_id=brand_id).create_dish_coupon_strategy(dish_id, name, rate, date_info)
    return strategy_id


@merchant_info.route("/create_dish_verification_code_coupon_category", methods=["POST"])
def create_dish_coupon_category():
    """ 创建菜品券
    """
    strategy_id = request.json.get("strategyId")
    brand_id = request.json.get("brandId")
    merchants = MerchantDataAccessHelper().get_merchant_list(brand_id=brand_id)
    for merchant in merchants:
        DishVerificationCodeManager(merchant=merchant).create_coupon_category(strategy_id)
    return "success"


@merchant_info.route("/create_brand_dish_verification_code_strategy", methods=["POST"])
def create_brand_dish_coupon_code_strategy():
    dish_brand_id = request.json.get("dishBrandId")
    brand_id = request.json.get("brandId")
    name = request.json.get("name", "品牌菜品核销券")
    rate = request.json.get("rate")
    date_info = request.json.get("dateInfo")
    strategy_id = BrandDishVerificationCodeManager(brand_id=brand_id).create_brand_dish_coupon_strategy(
        dish_brand_id, name, rate, date_info)
    return strategy_id


@merchant_info.route("/create_brand_dish_verification_code_coupon_category", methods=["POST"])
def create_brand_dish_verification_code_coupon_category():
    strategy_id = request.json.get("strategyId")
    brand_id = request.json.get("brandId")
    BrandDishVerificationCodeManager(brand_id=brand_id).create_coupon_category(strategy_id)
    return "success"


@merchant_info.route("/create_code_verification_strategy", methods=["POST"])
def create_code_verification_strategy():
    name = request.json.get("name", "核销优惠券")
    least_cost = request.json.get("leastCost")
    reduce_cost = request.json.get("reduceCost")
    reduce_rate = request.json.get("reduceRate", 0)
    day_of_month = request.json.get("dayOfMonth", [])
    daily_time_range = request.json.get("dailyTimeRange", [])
    VerificationCodeManager().create_code_verification_strategy(
        name, least_cost, reduce_cost, reduce_rate, day_of_month, daily_time_range)
    return "success"


@merchant_info.route("/create_code_verification_coupon_category", methods=["POST"])
def create_code_verification_coupon_category():
    """ 根据 verification_code_strategy 生成 CouponCategory
    """
    brand_id = request.json.get("brandId")
    verification_code_strategy_id = request.json.get("verificationCodeStrategyId")
    merchants = MerchantDataAccessHelper().get_merchant_list(brand_id=brand_id)
    for merchant in merchants:
        VerificationCodeManager(merchant=merchant).generate_coupon_category(verification_code_strategy_id)
    return "success"


@merchant_info.route("/coupon_category_spec/<string:coupon_category_spec_id>/create_coupon_category", methods=["POST"])
def create_coupon_category(coupon_category_spec_id):
    """ 根据指定的优惠券设置生成优惠券类型
    """
    merchant = request.merchant
    issue_scene = request.json.get("issueScene")

    coupon_category_specs = []
    new_member_coupons = merchant.preferences.coupon_config.new_member_coupons
    wechat_moments_ad_coupons = merchant.preferences.coupon_config.wechat_moments_ad_coupons
    coupon_packages = merchant.preferences.coupon_config.coupon_packages
    regular_coupons = merchant.preferences.coupon_config.regular_coupons
    if issue_scene:
        issue_scene = coupon_category_pb.CouponCategory.IssueScene.Value(issue_scene)
        if issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER:
            coupon_category_specs.extend(new_member_coupons)
        elif issue_scene == coupon_category_pb.CouponCategory.WECHAT_MOMENTS_AD:
            coupon_category_specs.extend(wechat_moments_ad_coupons)
        elif issue_scene == coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            coupon_category_specs.extend(coupon_packages)
        elif issue_scene == coupon_category_pb.CouponCategory.REGULAR_COUPONS:
            # 常规优惠券
            coupon_category_specs.extend(regular_coupons)
    else:
        coupon_category_specs.extend(new_member_coupons)
        coupon_category_specs.extend(wechat_moments_ad_coupons)
        coupon_category_specs.extend(coupon_packages)

    if coupon_category_specs:
        coupon_category_spec = None
        for spec in coupon_category_specs:
            if spec.id == coupon_category_spec_id:
                coupon_category_spec = spec
                break

        if coupon_category_spec:
            coupon_category = coupon_category_manager.create(merchant, coupon_category_spec)

            # 删除已绑定的优惠券类型
            if coupon_category_spec.coupon_category_id:
                CouponCategoryDataAccessHelper().update_coupon_category_state(id=coupon_category_spec.coupon_category_id, state=coupon_category_pb.CouponCategory.DELETED)

            # 保存优惠券类型设定
            coupon_category_spec.coupon_category_id = coupon_category.id
            MerchantManager().save_coupon_category_spec(merchant, coupon_category_spec)

            return make_json_response({"couponCategoryId": coupon_category.id})

    raise errors.CouponCategorySpecNotFoundError()


@merchant_info.route("/coupon_package/create", methods=["POST"])
def add_new_coupon_package():
    merchant = request.merchant
    json_obj = request.json
    coupon_category_spec = strategy_pb.CouponCategorySpec()
    coupon_category_spec.id = id_manager.generate_common_id()
    coupon_category_spec.name = json_obj['name']

    coupon_package_spec = json_format.ParseDict(
        json_obj['couponPackageSpec'], strategy_pb.CouponPackageCategorySpec(), ignore_unknown_fields=True)
    coupon_category_spec.coupon_package_spec.CopyFrom(coupon_package_spec)
    coupon_category_spec.coupon_package_spec.coupon_category_spec.date_info.type = 'DATE_TYPE_FIX_TERM'
    coupon_category_spec.coupon_package_spec.coupon_category_spec.date_info.fixed_begin_term = 0
    coupon_category_spec.coupon_package_spec.coupon_category_spec.date_info.fixed_term = int(json_obj['validDays'])
    # 创建特惠券包
    coupon_category = coupon_category_manager.create(merchant, coupon_category_spec)
    coupon_category_spec.coupon_category_id = coupon_category.id

    merchant.preferences.coupon_config.coupon_packages.append(coupon_category_spec)
    MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response({
        "couponPacakge": json_format.MessageToDict(coupon_category_spec, including_default_value_fields=True)
    })


@merchant_info.route("/coupon_package/<string:coupon_package_id>/deactivate", methods=["POST"])
def deactivate_coupon_package(coupon_package_id):
    merchant = request.merchant
    coupon_category_spec = None
    for coupon_package in merchant.preferences.coupon_config.coupon_packages:
        if coupon_package.id == coupon_package_id:
            coupon_category_spec = coupon_package
            coupon_package.state = coupon_category_pb.CouponCategory.INACTIVE
            break

    if coupon_category_spec:
        MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response({
        "couponPackage": json_format.MessageToDict(coupon_category_spec, including_default_value_fields=True)
    })


@merchant_info.route("/coupon_package/<string:coupon_package_id>/activate", methods=["POST"])
def activate_coupon_package(coupon_package_id):
    merchant = request.merchant
    coupon_category_spec = None
    for coupon_package in merchant.preferences.coupon_config.coupon_packages:
        if coupon_package.id == coupon_package_id:
            coupon_category_spec = coupon_package
            coupon_package.state = coupon_category_pb.CouponCategory.ACTIVE
            break

    if coupon_category_spec:
        MerchantDataAccessHelper().update_or_create_merchant(merchant)
    return make_json_response({
        "couponPackage": json_format.MessageToDict(coupon_category_spec, including_default_value_fields=True)
    })

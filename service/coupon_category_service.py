# -*- coding: utf-8 -*-

import logging

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops.coupon_manager import CouponManager
from business_ops.merchant_manager import MerchantManager
from business_ops.coupon.coupon_category_manager import CouponCategoryManager
from common.utils import requests_utils
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from proto import coupon_category_pb2 as coupon_category_pb
from service import errors
from service.base_responses import make_json_response
from service.base_responses import jsonify_response
from view_ops import coupon_view_helper

logger = logging.getLogger(__name__)
# 商户的优惠券类别
coupon_category = Blueprint("coupon_category", __name__,
                            url_prefix="/merchant/<string:merchant_id>/coupon_category")

def check_permission():
    """检查访问权限
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if user_id:
        user = UserDataAccessHelper().get_user(user_id)
        if user:
            setattr(request, "user", user)
            return None

    raise errors.UnauthorizedError()

@coupon_category.route("/<string:coupon_category_id>/ext", methods=["POST"])
def get_coupon_category_ext_for_wechat_miniprogram(merchant_id, coupon_category_id):
    """获取微信小程序组件需要的优惠券参数

    此接口仅用于之前创建并投放的优惠券，之后将不走微信流程
    """
    coupon_category_ext = MerchantManager().get_card_ext(merchant_id, coupon_category_id)
    coupon_category_ext_json = json_format.MessageToDict(coupon_category_ext, preserving_proto_field_name=True)
    return make_json_response({"cardExt": coupon_category_ext_json})

@coupon_category.route("/issue", methods=["POST"])
def issue_coupon_categories(merchant_id):
    """根据场景投放优惠券

    用于营销场景，支持投放拉新券和微信朋友圈广告券等，如果用户已领取，则不返回

    Args:
        merchant_id: (string) 商户ID
        issue_scene:
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    issue_scene = requests_utils.get_value_from_json(request.json, 'issueScene')
    if issue_scene == 'WECHAT_MOMENTS_AD':
        issue_scene = coupon_category_pb.CouponCategory.WECHAT_MOMENTS_AD
    elif issue_scene == 'NEW_MEMBER':
        issue_scene = coupon_category_pb.CouponCategory.NEW_MEMBER

    if issue_scene:
        coupons = CouponManager().issue_coupons_to_user(merchant_id=merchant_id,
                                                        user_id=user_id,
                                                        issue_scene=issue_scene)
        if coupons:
            coupons_json = []
            for coupon in coupons:
                coupon_vo = coupon_view_helper.convert_to_ui_coupon(coupon)
                coupon_json = json_format.MessageToDict(coupon_vo,
                                                        including_default_value_fields=True)
                coupons_json.append(coupon_json)
            return make_json_response({"coupons": coupons_json})
    return make_json_response()


@coupon_category.route("/new_member_coupon_category", methods=["GET"])
def new_member_coupon_category(merchant_id):
    """ 随机返回一个NEW_MEMBER类型的coupon category
    """
    coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(
        merchant_id=merchant_id, issue_scene=coupon_category_pb.CouponCategory.NEW_MEMBER)
    if coupon_categories and len(coupon_categories) > 0:
        return make_json_response({"coupon_category": json_format.MessageToDict(coupon_categories[0], including_default_value_fields=True)})
    return make_json_response()


@coupon_category.route("", methods=["POST"])
def create_coupon_category(merchant_id):
    """ 创建优惠券类型,目前仅支持生成以下几种券类型:
    1. 菜品券: issueScene = DISH
    2. 裂变券: issueScene = INVITE_SHARE
    """
    least_cost = request.json.get("leastCost", None)
    reduce_cost = request.json.get("reduceCost", None)
    title = request.json.get("title", None)
    type = request.json.get("type", None)
    base_info = request.json.get("baseInfo", None)
    issue_scene = request.json.get("issueScene", None)
    proportion = request.json.get("proportion", None)
    coupon_category_manager = CouponCategoryManager(merchant_id=merchant_id)
    user_type = request.json.get("userType", None)
    dish_id = request.json.get("dishId", None)
    dish_brand_id = request.json.get("dishBrandId", None)
    coupon_category = coupon_category_manager.create_coupon_category(
        issue_scene, least_cost=least_cost, reduce_cost=reduce_cost, title=title,
        type=type, base_info=base_info, proportion=proportion, user_type=user_type,
        dish_id=dish_id, dish_brand_id=dish_brand_id)
    return jsonify_response({"id": coupon_category.id})

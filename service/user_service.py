import logging
import time
import traceback

from flask import Blueprint
from flask import jsonify
from flask import request
from google.protobuf import json_format

from business_ops.user_manager import UserManager
from business_ops.alipay_miniprogram_manager import AliPayMiniprogramManager
from business_ops.ordering.keruyun_member_manager import KeruyunMemberManager
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.coupon_package_manager import CouponPackageManager
from business_ops.merchant_phone_member import MerchantPhoneMemberManager
from business_ops.member_card_manager import MemberCardManager
from business_ops.sms_manager import SmsManager
from common.utils import requests_utils
from common.utils import date_utils

# from common.utils.log_utils import LogUtils
from common.utils import id_manager
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.feedback_da_helper import FeedbackDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto import feedback_pb2 as feedback_pb
from service import base_responses, constants
from service import errors
from service import error_codes
from service.auth_service_helper import UserAuthHelper
from view_ops import transaction_view_helper
from common.config import config

logger = logging.getLogger(__name__)
user = Blueprint('user', __name__)

_wx_user_auth_helper = UserAuthHelper()
_alipay_user_manager = AliPayMiniprogramManager()


def check_wx_sign_up_items():
    request_json = request.json
    from_platform = requests_utils.get_platform(request)
    code = requests_utils.get_value_from_json(request_json, 'code')
    encrypted_data = requests_utils.get_value_from_json(request_json, 'encryptedData')
    iv = requests_utils.get_value_from_json(request_json, 'iv')
    join_method = requests_utils.get_value_from_json(request_json, 'sceneCode')
    return from_platform, code, encrypted_data, iv, join_method


@user.route('/temp_user/sign_up', methods=['POST'])
def wx_temp_user_sign_up():
    """
    微信小程序临时用户认证【码牌使用饭票支付】
    通过前端传递的encrypted_data、iv来补齐临时用户认证信息
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    if not user_id:
        raise errors.Error(errcode=error_codes.PARAMETER_NOT_ENOUGH, errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG)
    item = check_wx_sign_up_items()
    if not item[2] or not item[3]:
        raise errors.Error(errcode=error_codes.PARAMETER_NOT_ENOUGH, errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG)
    user = _wx_user_auth_helper.temp_user_auth(user_id, *item)
    return base_responses.jsonify_response(
        {
            "id": user.id,
            "authorized": bool(user.member_profile and user.member_profile.mobile_phone),
            "wechatOpenid": user.wechat_profile.openid,
        }
    )


@user.route('/user/sign_up/v2', methods=['OPTIONS', 'POST'])
def wx_user_sign_up_v2():
    """
    微信小程序登录，最新版
    """
    user = _wx_user_auth_helper.user_auth_v2(*check_wx_sign_up_items())
    return base_responses.jsonify_response(
        {
            "id": user.id,
            "authorized": bool(user.member_profile and user.member_profile.mobile_phone),
            "wechatOpenid": user.wechat_profile.openid,
        }
    )


@user.route('/user/phone_authorize/v2', methods=["POST"])
def wx_phone_authorized_v2():
    user_id = requests_utils.get_headers_info(request, 'userId')
    if not user_id:
        raise errors.Error(errcode=error_codes.PARAMETER_NOT_ENOUGH, errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG)
    from_platform, code, encrypted_data, iv, join_method = check_wx_sign_up_items()
    if not encrypted_data or not iv:
        raise errors.Error(errcode=error_codes.PARAMETER_NOT_ENOUGH, errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG)

    phone = _wx_user_auth_helper.phone_auth_v2(
        user_id, from_platform=from_platform, js_code=code, encrypted_data=encrypted_data, iv=iv
    )

    if from_platform != constants.PLATFORM_MERCHANT:
        manager = MerchantPhoneMemberManager()
        manager.bind_phone_member_to_user(phone, user_id)
        # 手机号授权成功后,查出用户所有的储值,如果商户是时来pos机商户,就清空会员储值,同步到时来pos机端
        manager = MemberCardManager()
        manager.sync_user_member_card_balance(user_id)

    return base_responses.jsonify_response({"phone": phone})


@user.route('/user/sign_up', methods=['OPTIONS', 'POST'])
def user_sign_up():
    """
    微信授权登录流程
    请求主体 { code, encryptedData, iv }
    请求头 { fromPlatform }
    流程: 使用 code 获取 session_key, 用 session_key, iv, 小程序appid 解密 encryptData，获得用户基本信息和 unionid
    """
    user = UserAuthHelper().user_auth(*check_wx_sign_up_items())
    if user:
        ret = {"id": user.id, "authorized": False, "wechatOpenid": user.wechat_profile.openid}
        u = UserDataAccessHelper().get_user(user.id)
        if u and u.member_profile and u.member_profile.mobile_phone:
            # 返回用户有没有在时来平台授权手机
            ret.update({"authorized": True})
        return base_responses.jsonify_response(ret)
    else:
        error_response = base_responses.create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
        return base_responses.jsonify_response(status_response=error_response)


@user.route('/user/alipay/sign_up/v2', methods=['POST'])
def alpay_user_singn_up_v2():
    """
    支付宝小程序登录，最新版
    """
    auth_code = request.json.get('authCode')
    version = request.json.get("version", None)
    alipay_user = _alipay_user_manager.user_auth_v2(auth_code, version)
    user = UserDataAccessHelper().get_user_by_alipay_user_id(alipay_user['alipayUserId'])
    if not user:
        user = _alipay_user_manager.add_user(alipay_user)
    return base_responses.jsonify_response(json_format.MessageToDict(user, including_default_value_fields=True))


@user.route('/user/alipay/sign_up', methods=['POST'])
def alipay_user_sign_up():
    '''支付宝授权登录'''
    auth_code = request.json.get('authCode')
    version = request.json.get("version", None)
    manager = AliPayMiniprogramManager()
    if version is None:
        alipay_user = manager.get_user_info(auth_code)
    else:
        alipay_user = manager.get_user_info_v2(auth_code)
    if not alipay_user:
        error_response = base_responses.create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
        return base_responses.jsonify_response(status_response=error_response)
    user = UserDataAccessHelper().get_user_by_alipay_user_id(alipay_user.get('alipayUserId'))
    if not user:
        user = manager.add_user(alipay_user, user)
    user = json_format.MessageToDict(user, including_default_value_fields=True)
    logger.info('alipay user sign up: {}'.format(user))
    return base_responses.jsonify_response(user)


@user.route('/user/phone_authorize', methods=["POST"])
def phone_authorized():
    """用户手机授权"""
    user_id = requests_utils.get_headers_info(request, 'userId')
    from_platform = requests_utils.get_platform(request)
    request_json = request.json
    code = requests_utils.get_value_from_json(request_json, 'code')
    encrypted_data = requests_utils.get_value_from_json(request_json, 'encryptedData')
    iv = requests_utils.get_value_from_json(request_json, 'iv')
    ua = UserAuthHelper()
    phone = ua.phone_auth(user_id, from_platform, code, encrypted_data, iv)
    if phone:
        manager = MerchantPhoneMemberManager()
        manager.bind_phone_member_to_user(phone, user_id)
        # 手机号授权成功后,查出用户所有的储值,如果商户是时来pos机商户,就清空会员储值,同步到时来pos机端
        manager = MemberCardManager()
        manager.sync_user_member_card_balance(user_id)
        return base_responses.jsonify_response({"phone": phone})
    unknown_error = base_responses.create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
    return base_responses.jsonify_response(status_response=unknown_error)


@user.route("/alipay/miniprogram/user_info", methods=["POST"])
def alipay_miniprogram_get_user_info():
    resp = request.json.get("resp", None)
    user_id = request.headers.get("userId")
    alipay_manager = AliPayMiniprogramManager()
    user_info = alipay_manager.miniprogram_get_user_info(resp)
    user_da = UserDataAccessHelper()
    user = user_da.get_user(user_id)
    if user:
        user.member_profile.mobile_phone = user_info.get("mobile")
        user_da.update_or_create_user(user)
    if user_info.get("mobile"):
        mobile = user_info.get("mobile")
        prefix = mobile[0:3]
        suffix = mobile[-3:-1]
        user_info.update({"mobile": "{}******{}".format(prefix, suffix)})
    return base_responses.jsonify_response(user_info)


@user.route("/user/user_merchant_info/<string:merchant_id>", methods=["GET"])
def get_user_merchant_info(merchant_id):
    user_id = request.headers.get("userId")
    user_manager = UserManager()
    user_merchant_info = user_manager.get_user_merchant_info(user_id, merchant_id)
    user_merchant_info = json_format.MessageToDict(user_merchant_info, including_default_value_fields=True)
    return base_responses.jsonify_response(user_merchant_info)


# deprecated
@user.route('/user/info', methods=['OPTIONS', 'GET'])
def get_user_info():
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    from_platform = requests_utils.get_platform(request)
    user = UserManager().get_platform_user_by_id(from_platform, user_id)
    user_json = json_format.MessageToDict(user, including_default_value_fields=True, use_integers_for_enums=True)
    success_responses_obj = base_responses.create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj['user'] = user_json
    resp = jsonify(success_responses_obj)
    # LOG.info_api_responses(resp)
    return resp


@user.route('/user/info/save', methods=['OPTIONS', 'POST'])
def save_user_info():
    """
    # POST /user/info/save
    # 请求主体 { encryptData, iv, userId}
    # 请求头 { fromPlatform: “merchant” }
    # 流程：通过session_key, iv, 小程序appid 解密 encryptData，得到用户基本信息(nickName, gender, province, city, country, avatarUrl, unionId)
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api/wx.getUserInfo.html
    """
    # LOG = LogUtils(request)
    request_json = request.json
    from_platform = requests_utils.get_platform(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    iv = request_json['iv']
    encrypted_data = request_json['encryptedData']

    save_flag = UserAuthHelper().save_user_info(from_platform, iv, user_id, encrypted_data)
    if save_flag:
        resp = base_responses.success_responses_obj()
    else:
        resp = base_responses.error_responses()

    resp = jsonify(resp)
    # LOG.info_api_responses(resp)
    return resp


@user.route('/user/get_merchants', methods=['OPTIONS', 'GET'])
def get_merchants_for_user():
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    merchants = MerchantDataAccessHelper().get_merchants_by_user(user_id)
    merchant_json_data = [json_format.MessageToDict(merchant, including_default_value_fields=True) for merchant in merchants]

    success_responses_obj = base_responses.create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj["merchants"] = merchant_json_data
    resp = jsonify(success_responses_obj)
    # LOG.info_api_responses(resp)
    return resp


# 考虑使用 /member_card/list
@user.route('/user/member_card/list', methods=['OPTIONS', 'GET'])
def get_member_cards_for_user():
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    merchant_id = request.values.get('merchantId')

    if not user_id:
        success_responses_obj = base_responses.success_responses_obj()
        resp = jsonify(success_responses_obj)
        # LOG.info_api_responses(resp)
        return resp

    if merchant_id:
        member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id=merchant_id, user_id=user_id)
    else:
        member_cards = MembershipDataAccessHelper().get_member_cards_for_user(user_id)

    member_cards_json = [json_format.MessageToDict(card, including_default_value_fields=True) for card in member_cards]
    # 给每张MemberCard信息中补充MemberCardCategory信息
    for card_json in member_cards_json:
        card_category = MembershipDataAccessHelper().get_member_card_category(card_json['cardCategoryId'])
        card_json['cardCategory'] = json_format.MessageToDict(card_category, including_default_value_fields=True)

    success_responses_obj = base_responses.create_responses_obj(error_codes.SUCCESS, 'success')
    success_responses_obj["memberCards"] = member_cards_json
    resp = jsonify(success_responses_obj)
    # LOG.info_api_responses(resp)
    return resp


@user.route('/user/card/<string:card_category_id>/wx_activate', methods=['OPTIONS', 'POST'])
def wx_activate_card(card_category_id):
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    req_json = request.json
    code = requests_utils.get_value_from_json(req_json, "code")
    activate_ticket = requests_utils.get_value_from_json(req_json, "activateTicket")
    form_id = requests_utils.get_value_from_json(req_json, "formId")

    resp_obj = UserManager().handle_wx_activate_member_card(user_id, card_category_id, code, activate_ticket, form_id)
    resp = jsonify(resp_obj)
    # LOG.info_api_responses(resp)
    return resp


@user.route('/user/card/<string:card_category_id>/auto_activate', methods=['OPTIONS', 'POST'])
def auto_activate_card(card_category_id):
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    req_json = request.json
    code = requests_utils.get_value_from_json(req_json, "code")
    form_id = requests_utils.get_value_from_json(req_json, "formId")

    resp_obj = UserManager().handle_auto_activate_member_card(user_id, card_category_id, code, form_id)
    resp = jsonify(resp_obj)
    # LOG.info_api_responses(resp)
    return resp


@user.route('/user/saving_stats', methods=['GET'])
def get_user_saving_statistics():
    # LOG = LogUtils(request)
    user_id = requests_utils.get_headers_info(request, 'userId')
    success_responses_obj = base_responses.success_responses_obj()
    if user_id:
        stats = transaction_view_helper.get_user_saving_statistics(user_id)
        resp_obj = json_format.MessageToDict(stats, including_default_value_fields=True)
        success_responses_obj['stats'] = resp_obj

    resp = jsonify(success_responses_obj)
    # LOG.info_api_responses(resp)
    return resp


@user.route("/user/user_center", methods=['GET'])
def user_center():
    user_id = requests_utils.get_headers_info(request, "userId")
    user_center_info = UserManager().get_user_center_info(user_id)
    user_center_info = json_format.MessageToDict(user_center_info, including_default_value_fields=True)
    return base_responses.jsonify_response(user_center_info)


@user.route("/user/home_page", methods=['GET'])
def home_page():
    user_id = requests_utils.get_headers_info(request, "userId")
    home_page = UserManager().get_user_home_page(user_id)
    home_page = json_format.MessageToDict(home_page, including_default_value_fields=True)
    return base_responses.jsonify_response(home_page)


@user.route("/user/user_visited_stores", methods=["GET"])
def get_user_visited_stores():
    """用户去过的餐厅"""
    user_id = requests_utils.get_headers_info(request, "userId")
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    stores = UserManager().get_user_visited_stores(user_id, page=page, size=size)
    stores = [json_format.MessageToDict(store, including_default_value_fields=True) for store in stores]
    return base_responses.jsonify_response(stores)


@user.route("/user/feedback", methods=["POST"])
def feedback():
    user_id = requests_utils.get_headers_info(request, "userId")
    feedback = request.json.get('feedback', None)
    if feedback:
        date = int(time.time())
        feedback_obj = feedback_pb.Feedback()
        feedback_obj.feedback = feedback
        feedback_obj.user_id = user_id
        feedback_obj.date = date
        feedback_obj.id = id_manager.generate_feedback_id()
        FeedbackDataAccessHelper().add_feedback(feedback_obj)
    return base_responses.jsonify_response()


@user.route("/user/update", methods=["POST"])
def update():
    """更新用户信息"""
    user_id = requests_utils.get_headers_info(request, "userId")
    headimgurl = request.json.get('headImageUrl', None)
    nickname = request.json.get('nickname', None)
    sex = request.json.get('sex', None)
    birth_year = request.json.get('birthYear', None)
    birth_month = request.json.get('birthMonth', None)
    birth_day = request.json.get('birthDay', None)
    personal_signature = request.json.get("personalSignature", None)
    tags = request.json.get("tags", None)
    phone = request.json.get("phone", None)
    if user_id is None:
        return base_responses.jsonify_response()
    UserDataAccessHelper().update(
        user_id=user_id,
        head_image_url=headimgurl,
        nickname=nickname,
        sex=sex,
        birth_year=birth_year,
        birth_month=birth_month,
        birth_day=birth_day,
        personal_signature=personal_signature,
        tags=tags,
        phone=phone,
    )
    return base_responses.jsonify_response()


@user.route("/user/report", methods=["POST"])
def report():
    """举报用户"""
    user_id = requests_utils.get_headers_info(request, "userId")
    reported_id = request.json.get('reportedId', None)
    content = request.json.get('content', None)
    report = feedback_pb.Report()
    report.id = id_manager.generate_common_id()
    report.content = content
    report.reporter_id = user_id
    report.reported_id = reported_id
    report.date = date_utils.timestamp_second()
    FeedbackDataAccessHelper().add_report(report)
    return base_responses.jsonify_response()


@user.route('/user/user_profile/', methods=['GET'])
@user.route('/user/user_profile/<string:profile_id>', methods=['GET'])
def get_user_profile(profile_id=None):
    if profile_id is None:
        profile_id = requests_utils.get_headers_info(request, "userId")
    if profile_id is None:
        return base_responses.jsonify_response()
    user_profile = UserManager().get_user_profile(profile_id)
    user_profile_json = json_format.MessageToDict(user_profile, including_default_value_fields=True)
    return base_responses.jsonify_response(user_profile_json)


@user.route("/user/add_shipping_address", methods=["POST"])
def add_shipping_address():
    user_id = requests_utils.get_headers_info(request, "userId")
    province = request.json.get("province", '')
    city = request.json.get("city")
    mobile_phone = request.json.get("mobilePhone")
    username = request.json.get('username')
    gender = request.json.get('gender')
    street = request.json.get('street')
    house_number = request.json.get('houseNumber')
    latitude = request.json.get('latitude')
    longitude = request.json.get('longitude')
    UserManager().add_shipping_address(
        user_id, mobile_phone, username, gender, street, house_number, longitude, latitude, province, city
    )
    return base_responses.jsonify_response()


@user.route("/user/remove_shipping_address", methods=["POST"])
def remove_shipping_address():
    user_id = requests_utils.get_headers_info(request, "userId")
    shipping_address_id = request.json.get('shippingAddressId')
    UserDataAccessHelper().remove_shipping_address(user_id, shipping_address_id)
    return base_responses.jsonify_response()


@user.route("/user/update_shipping_address/<string:shipping_address_id>", methods=["POST"])
def update_shipping_address(shipping_address_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    mobile_phone = request.json.get("mobilePhone")
    username = request.json.get('username')
    gender = request.json.get('gender')
    street = request.json.get('street')
    house_number = request.json.get('houseNumber')
    latitude = request.json.get('latitude')
    longitude = request.json.get('longitude')
    UserManager().update_shipping_address(
        shipping_address_id, user_id, mobile_phone, username, gender, street, house_number, latitude, longitude
    )
    return base_responses.jsonify_response()


@user.route('/user/shipping_address/<string:shipping_address_id>', methods=["GET"])
def get_shipping_address(shipping_address_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user = UserDataAccessHelper().get_user(user_id)
    if not user:
        raise errors.UserNotFound()
    for address in user.member_profile.shipping_address:
        if address.id == shipping_address_id:
            address = json_format.MessageToDict(address, including_default_value_fields=True)
            return base_responses.jsonify_response(address)
    return base_responses.jsonify_response()


@user.route("/user/keruyun_member_info", methods=["GET"])
def get_keruyun_member_info():
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_id = request.args.get('merchantId', None)
    registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=merchant_id)
    manager = KeruyunMemberManager(registration_info)
    keruyun_member_info = manager.get_customer_info_vo(user_id)
    # keruyun_member_info = json_format.MessageToDict(keruyun_member_info, including_default_value_fields=True)
    result = {"remainValue": 0}
    if keruyun_member_info:
        result.update({"remainValue": keruyun_member_info.remain_value})
    return base_responses.jsonify_response(result)


@user.route("/refund/coupon_package/<string:transaction_id>", methods=["POST"])
def coupon_package_refund(transaction_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    reason = request.json.get("reason", None)
    if not reason or reason == "":
        reason = "不会用"
    phone = request.json.get("phone", None)
    manager = CouponPackageManager(user_id=user_id)
    manager.coupon_package_refund(transaction_id, reason=reason, phone=phone)
    return base_responses.jsonify_response()


@user.route("/refund/fanpiao/<string:transaction_id>", methods=["POST"])
def fanpiao_refund(transaction_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    reason = request.json.get("data", {}).get("reason", None)
    if not reason or reason == "":
        reason = "不会用"
    phone = request.json.get("data", {}).get("phone", None)
    manager = FanpiaoManager(user_id=user_id)
    manager.fanpiao_refund(transaction_id, reason=reason, phone=phone)
    return base_responses.jsonify_response()


@user.route("/user/phone_member_info/<string:merchant_id>", methods=["GET"])
def get_phone_member_info(merchant_id):
    user_id = request.headers.get("userId")
    manager = MerchantPhoneMemberManager(merchant_id=merchant_id)
    phone_member = manager.get_merchant_phone_member(user_id=user_id)
    if not phone_member:
        return base_responses.jsonify_response()
    phone_member = json_format.MessageToDict(phone_member)
    return base_responses.jsonify_response(phone_member)


@user.route("/phone/verify/code", methods=["POST"])
def get_phone_verify_code():
    scene = request.json.get("scene", None)
    phone = request.json.get("phone", None)
    user_id = request.headers.get("userId", None)
    user_manager = UserManager()
    code = user_manager.generate_phone_verify_code(scene, phone, user_id=user_id)
    sms_manager = SmsManager()
    if not code:
        return base_responses.jsonify_response()
    if scene == SmsManager.BIND_PHONE_AUTH_CODE:
        sms_manager.send_bind_phone_code([phone], code)
    return base_responses.jsonify_response()


@user.route("/bind/phone", methods=["POST"])
def bind_phone_for_user():
    """验证码换绑手机号"""
    raise errors.ShowError("功能升级中，请稍后")
    # user_id = request.headers.get("userId", None)
    # code = request.json.get("code", None)
    # user_manager = UserManager()
    # user_manager.phone_code_verify(code, user_id=user_id)
    # return base_responses.jsonify_response()


@user.route("/opengid", methods=["POST"])
def get_opengid():
    user_id = request.headers.get("userId", None)
    merchant_id = request.json.get("merchantId", None)
    error_type = request.json.get("type")
    mappings = {
        "********************************": "歌志轩",  # 以免有表情包
        "d4d87595a5874c1f8a8232cfc5a93c72": "花好月圆螺蛳粉",
        "aff3821073f84c07acc343e101c5607a": "唤牛牛杂粿条",
        "776cc48fdcb04b8a8fb77e112a49acb0": "壹只卤鹅",
        "cadb9c6a4ac5418193b3003333033785": "楚先笙襄阳牛肉面",
        "e14bb2210f74496db3c3eda88081f8d0": "港岛记",
    }
    if error_type == "error" or merchant_id not in mappings:
        logger.info(f"Ignore OpenGID: userId: {user_id} and MerchantId: {merchant_id} and error type: {error_type}")
        return base_responses.jsonify_response({"userInGroup": False})
    encrypted_data = request.json.get("encryptedData", None)
    iv = request.json.get("iv", None)
    logger.info(f">>>>>>>>>>>>>> request json: {request.json}")
    user_helper = UserAuthHelper()
    user_da = UserDataAccessHelper()
    user = user_da.get_user(user_id)
    if not user:
        raise errors.UnauthorizedError()
    # from_platform = "wxdd5cafec95f6cc46"  # 时来饭票
    # from_platform = "wxaa3c47ef72452be7"  # 时来时享
    from_platform = config.wechat_miniprogram_appid
    session_key = user.wechat_profile.session_key  # 需要最新的session key
    logger.info(f">>>>>>>>>>>>>>> session key: {session_key}")
    try:
        decrypted_data = user_helper.decrypt_data(from_platform, session_key, encrypted_data, iv)
        logger.info("opengid: {}".format(decrypted_data))
        opengid = decrypted_data.get("opengid")
        logger.info(f">>>>>>>>>>>>>>> open gid: {opengid}")
        from business_ops.wechat_enterprise_gateway import WechatGroupChatUser

        user_check = WechatGroupChatUser(opengid, user=user)
        user_in_group_chat = user_check.validate_user_in_group(mappings[merchant_id])
        return base_responses.jsonify_response({"userInGroup": user_in_group_chat})
    except Exception as e:
        logger.error(traceback.format_exc())
        return base_responses.jsonify_response()


@user.route("/get-userinfo/<string:user_id>", methods=["GET"])
def get_userinfo(user_id):
    user_da = UserDataAccessHelper()
    user = user_da.get_user(user_id)
    result = {"headImgUrl": user.member_profile.head_image_url, "nickname": user.member_profile.nickname}
    return base_responses.jsonify_response(result)


@user.route('/user/config', methods=["POST"])
def update_user_config():
    request_json = request.json or {}
    if not request_json.get('userId'):
        request_json.update({'userId': request.headers.get("userId", None)})
        if not request_json.get('userId'):
            raise errors.UnauthorizedError()
    user_da = UserDataAccessHelper()
    user_da.add_or_update_user_config(request_json)
    return base_responses.jsonify_response()


@user.route('/user/config', methods=["GET"])
def get_user_config():
    user_id = request.headers.get("userId", None)
    if not user_id:
        raise errors.UnauthorizedError()
    config = UserDataAccessHelper().get_user_config(user_id)
    return base_responses.jsonify_response(json_format.MessageToDict(config, including_default_value_fields=True))

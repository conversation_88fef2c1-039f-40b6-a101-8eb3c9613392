# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops.transaction_manager import TransactionManager
from common.utils import requests_utils
from dao.invitation_da_helper import InvitationDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb
from view_ops.order_list_view_helper import Order<PERSON><PERSON><PERSON>iew<PERSON>elper
from view_ops.transaction_info_view_helper import TransactionInfoViewObjectHelper
from view_ops.transaction_list_view_helper import TransactionListViewHelper
from service import base_responses
from service.base_responses import error_responses
from service.errors import UserNotFound

transaction = Blueprint('transaction', __name__, url_prefix="/transaction")


@transaction.route('/<string:transaction_id>', methods=['GET'])
def get_transaction(transaction_id):
    """ 返回具体交易信息
    """
    transaction = TransactionInfoViewObjectHelper().get_transaction_info(transaction_id)
    if transaction:
        json_object = json_format.MessageToDict(transaction, including_default_value_fields=True)
        return base_responses.jsonify_response(json_object)
    return base_responses.jsonify_response(status_response=error_responses())


@transaction.route("/order_list", methods=["GET"])
def get_transaction_list():
    """ 用户交易列表
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        raise UserNotFound()
    transaction_type = request.args.get('transactionType', wallet_pb.Transaction.GROUP_DINING_PAYMENT)
    transaction_type = wallet_pb.Transaction.TransactionType.Value(transaction_type)
    state = request.args.get('state', "ACCEPTED")
    state = group_dining_pb.Invitation.InvitationState.Value(state)
    monetary_state = request.args.get('monetaryState', "") or None
    if monetary_state:
        monetary_state = group_dining_pb.Invitation.MonetaryState.Value(monetary_state)
    page = request.args.get('page', 1)
    size = request.args.get('size', 20)
    page = int(page) if page else None
    size = int(size) if size else None
    invitation_da = InvitationDataAccessHelper()
    if transaction_type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
        # 组局
        group_dining_order_list = OrderListViewHelper().get_group_dining_order_list(
            user_id=user_id, state=state, monetary_state=monetary_state, page=page, size=size)
        order_list = [json_format.MessageToDict(order, including_default_value_fields=True)
                      for order in group_dining_order_list]
        monetary_state = group_dining_pb.Invitation.TRANSFER_PENDING
        transfer_pending_num = invitation_da.count_invitation(invitee_id=user_id,
                                                              monetary_state=monetary_state)
        monetary_state = group_dining_pb.Invitation.RED_PACKET_PENDING
        red_packet_pending_num = invitation_da.count_invitation(invitee_id=user_id,
                                                                monetary_state=monetary_state)
        ret = {
            "orderList": order_list,
            "transferPendingNum": transfer_pending_num,  # 待付款饭局数
            "redPacketPendingNum": red_packet_pending_num  # 待领取红包饭局数
        }
        return base_responses.jsonify_response(ret)
    elif transaction_type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
        # 个人消费
        self_dining_order_list = OrderListViewHelper().get_self_dining_order_list(user_id=user_id,
                                                                                  page=page,
                                                                                  size=size)
        order_list = [json_format.MessageToDict(order, including_default_value_fields=True)
                      for order in self_dining_order_list]
        return base_responses.jsonify_response({"orderList": order_list})
    else:
        return base_responses.jsonify_response()
    return base_responses.jsonify_response()


@transaction.route("/user_bill", methods=["GET"])
def user_bill():
    """ 用户账单
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        raise UserNotFound()
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    transactions = TransactionManager().user_bill(user_id, page=page, size=size)
    transactions = [json_format.MessageToDict(transaction, including_default_value_fields=True)
                    for transaction in transactions]
    return base_responses.jsonify_response(transactions)


@transaction.route("/ordering/list", methods=["GET"])
@transaction.route("/buy_coupon_package/list", methods=["GET"])
@transaction.route("/buy_fanpiao/list", methods=["GET"])
@transaction.route("/ordering/list/<string:merchant_id>", methods=["GET"])
@transaction.route("/buy_coupon_package/list/<string:merchant_id>", methods=["GET"])
@transaction.route("/buy_fanpiao/list/<string:merchant_id>", methods=["GET"])
def user_transaction_list(merchant_id=None):
    user_id = request.headers.get("userId", None)
    transaction_list_view_helper = TransactionListViewHelper()
    latest_time = request.args.get("latestTime", None)
    status = request.args.get("status", None)
    if not user_id:
        return base_responses.jsonify_response([])
    if str(request.url_rule).startswith("/transaction/ordering/list"):
        # transaction_list_vo = transaction_list_view_helper.get_user_order_list(
        #     user_id=user_id, create_time=latest_time, merchant_id=merchant_id)
        transaction_list_vo = transaction_list_view_helper.get_user_order_list_v2(
            user_id=user_id, latest_create_time=latest_time, merchant_id=merchant_id, status=status)
    elif str(request.url_rule).startswith("/transaction/buy_coupon_package/list"):
        transaction_list_vo = transaction_list_view_helper.get_user_buy_coupon_package_list(
            user_id=user_id, last_paid_time=latest_time, merchant_id=merchant_id)
    elif str(request.url_rule).startswith("/transaction/buy_fanpiao/list"):
        transaction_list_vo = transaction_list_view_helper.get_user_buy_fanpiao_list(
            user_id=user_id, last_paid_time=latest_time, merchant_id=merchant_id)
    transaction_list_vo = json_format.MessageToDict(transaction_list_vo, including_default_value_fields=True)
    return base_responses.jsonify_response(transaction_list_vo)

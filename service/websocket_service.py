# -*- coding: utf-8 -*-

import sys
import os

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

# 设置logging的配置
from scripts.services import service_common
from common.logger import websocket_service_logger

from gevent import monkey
monkey.patch_all()

from geventwebsocket import WebSocketServer
from geventwebsocket import Resource

from service.websockets.websocket_service import WebsocketService

if __name__ == '__main__':
    HOST = "127.0.0.1"
    PORT = 8002

    WebSocketServer(
        (HOST, PORT),

        Resource([
            ('/wsservice/.*', WebsocketService)
        ]),

        debug=True
    ).serve_forever()

# -*- coding: utf-8 -*-

from flask import jsonify

from service import error_codes


def jsonify_response(data={}, status_response=None):
    if status_response is None:
        status_response = {'errcode': error_codes.SUCCESS, 'errmsg': 'success'}
    ret = {}
    if data:
        ret = {
            "data": data
        }
    ret.update(**status_response)
    return jsonify(ret)


def create_responses_obj(errcode, errmsg):
    responses_obj = {'errcode': errcode, 'errmsg': errmsg}
    return responses_obj

def create_boshijie_response_obj(code, message):
    response_json = {'code': code, 'message': message}
    return response_json

def success_responses_obj():
    success_responses_obj = {'errcode': error_codes.SUCCESS, 'errmsg': 'success'}
    return success_responses_obj


def fail_responses_obj():
    fail_responses_obj = {"errcode": error_codes.FAIL, "errmsg": "fail"}
    return fail_responses_obj


def error_responses():
    return {'errcode': error_codes.UNKNOWN_ERROR, 'errmsg': u'请求失败'}


def make_json_response(data={}, status_response=success_responses_obj()):
    """返回 Content-Type 为 application/json 类型的 Response
    """
    return jsonify({**status_response, **data})

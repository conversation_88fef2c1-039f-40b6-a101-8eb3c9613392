# -*- coding: utf-8 -*-

import random
import logging

from flask import Blueprint
from flask import request

from google.protobuf import json_format

from business_ops.merchant_manager import MerchantManager
from business_ops.fanpiao_manager import FanpiaoManager
from service import base_responses


bp_name = "advertising"

_advertising = Blueprint(bp_name, bp_name, url_prefix="/advertising")

logger = logging.getLogger(__name__)


@_advertising.route("/query", methods=["POST"])
def get_advertising():
    """ 获取一个广告
    """
    merchant_id = request.json.get("merchantId", None)
    user_id = request.headers.get("userId", None)
    merchant_manager = MerchantManager(merchant_id=merchant_id)
    if not merchant_manager.merchant.display_advertising_info.display_advertising:
        return base_responses.jsonify_response()
    if len(merchant_manager.merchant.display_advertising_info.merchant_ids) == 0:
        return base_responses.jsonify_response()
    merchant_id = random.choice(merchant_manager.merchant.display_advertising_info.merchant_ids)
    merchant_manager = MerchantManager(merchant_id=merchant_id)
    if not merchant_manager.merchant.advertising_info.has_advertising:
        return base_responses.jsonify_response()
    advertising_info = json_format.MessageToDict(
        merchant_manager.merchant.advertising_info, including_default_value_fields=True)
    fanpiao_manager = FanpiaoManager(merchant=merchant_manager.merchant)
    fanpiao_categories = fanpiao_manager.get_fanpiao_categories()
    maximum_discount = 0
    for fanpiao_category in fanpiao_categories:
        logger.info(f"{fanpiao_category.name}, {fanpiao_category.discount}")
        maximum_discount = max(fanpiao_category.discount, maximum_discount)
    advertising_info = {
        "merchantId": merchant_id,
        "merchantName": merchant_manager.merchant.basic_info.display_name,
        "logo": merchant_manager.merchant.basic_info.logo_url,
        "advertisingInfo": advertising_info,
        "minimumDiscount": 100 - maximum_discount
    }
    return base_responses.jsonify_response(advertising_info)

# -*- coding: utf-8 -*-

import logging
import time

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from dao.message_center.operation_da_helper import OperationDataAccessHelper
import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.config_pb2 as config_pb
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from business_ops.payment_manager import PaymentManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.transaction_manager import TransactionManager
from business_ops.wallet_manager import WalletManager
from business_ops.activity_manager import ActivityManager
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.direct_pay_manager_helper import DirectPayManagerHelper
from common.utils import requests_utils
from common.utils import id_manager
from common.request import check_body
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from service.base_responses import jsonify_response, create_responses_obj
from service import error_codes
from service import errors
from common.protobuf_transformer import batch_protobuf_to_dict
from business_ops.merchant_manager import MerchantManager
from common.config import config


logger = logging.getLogger(__name__)

_fanpiao = Blueprint(__name__, __name__, url_prefix="/fanpiao")


@_fanpiao.route("/add_fanpiao_category", methods=["POST"])
def add_fanpiao_category():
    total_value = request.json.get("totalValue")
    discount = request.json.get("discount")
    name = request.json.get("name")
    merchant_id = request.json.get("merchantId")
    sell_price = request.json.get("sellPrice", 0)
    base_selling_quantity = request.json.get("baseSellingQuantity", None)
    if sell_price == 0:
        sell_price = total_value
    fanpiao_da = FanpiaoDataAccessHelper()
    category = fanpiao_pb.FanpiaoCategory()
    category.id = id_manager.generate_common_id()
    category.total_value = total_value
    category.create_time = int(time.time())
    category.discount = discount
    category.name = name
    category.merchant_id = merchant_id
    category.sell_price = sell_price
    if base_selling_quantity is not None:
        category.base_selling_quantity = int(base_selling_quantity)
    fanpiao_da.add_or_update_fanpiao_category(category)
    return jsonify_response({"id": category.id})


@_fanpiao.route("/fanpiao_category/<string:fanpiao_category_id>", methods=["DELETE"])
def delete_fanpiao(fanpiao_category_id):
    fanpiao_da = FanpiaoDataAccessHelper()
    fanpiao_category = fanpiao_da.get_fanpiao_category_by_id(id=fanpiao_category_id)
    fanpiao_category.status = fanpiao_pb.FanpiaoCategory.INACTIVE
    fanpiao_da.add_or_update_fanpiao_category(fanpiao_category)
    return jsonify_response()


@_fanpiao.route("/fanpiao_category/merchant/<string:merchant_id>", methods=["DELETE"])
def delete_fanpiaos(merchant_id):
    fanpiao_da = FanpiaoDataAccessHelper()
    fanpiao_categories = fanpiao_da.get_fanpiao_categories(merchant_id=merchant_id)
    for category in fanpiao_categories:
        category.status = fanpiao_pb.FanpiaoCategory.INACTIVE
        fanpiao_da.add_or_update_fanpiao_category(category)
    return jsonify_response()


@_fanpiao.route("/fanpiao_categories/<string:merchant_id>", methods=["GET"])
@_fanpiao.route("/fanpiao_categories", methods=["GET"])
@_fanpiao.route("/get_fanpiao_categories", methods=["GET"])
def get_fanpiao_categories(merchant_id=None):
    if merchant_id is None:
        merchant_id = request.args.get('merchantId', None)
    user_id = request.headers.get("userId", None)
    manager = FanpiaoManager(merchant_id=merchant_id, user_id=user_id)
    categories = manager.get_fanpiao_categories()
    categories.sort(key=lambda x: x.total_value)
    categories = [json_format.MessageToDict(category, including_default_value_fields=True) for category in categories]
    return jsonify_response(categories)


@_fanpiao.route("/get_user_fanpiao_fee", methods=["GET"])
def get_user_fanpiao_fee():
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        return jsonify_response({"balance": 0})
    merchant_id = request.args.get('merchantId', None)
    manager = FanpiaoManager(merchant_id=merchant_id)
    fanpiaos = FanpiaoDataAccessHelper().get_fanpiaos(
        user_id=user_id,
        status=fanpiao_pb.Fanpiao.ACTIVE,
        merchant_id=merchant_id,
        brand_id=manager.merchant.brand_info.id
        if merchant_id and manager.merchant and manager.merchant.enable_brand_fanpiao and manager.merchant.brand_info.id
        else None,
    )
    fanpiaos.sort(key=lambda x: x.total_value - x.total_used_fee)
    remaining_value = 0
    for fanpiao in fanpiaos:
        remaining_value += fanpiao.total_value - fanpiao.total_used_fee
    return jsonify_response({"balance": remaining_value})


@_fanpiao.route("/get_user_fanpiao_list", methods=["GET"])
def get_user_fanpiao_list():
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_id = request.args.get('merchantId', None)
    not_only_active = request.args.get("notOnlyActive")  # TODO(inmove): 临时处理，表示是否不只返回活跃的卡包
    if merchant_id == "":
        merchant_id = None
    fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
    fanpiao_list_vo = fanpiao_manager.get_user_fanpiao_list(
        user_id=user_id, merchant_id=merchant_id, not_only_active=not_only_active
    )
    fanpiao_manager.check_user_ever_bought_fanpiao(fanpiao_list_vo, user_id, merchant_id)
    fanpiao_list = json_format.MessageToDict(fanpiao_list_vo, including_default_value_fields=True)
    return jsonify_response(fanpiao_list)


@_fanpiao.route("/get_order_show_paid_fee", methods=["POST"])
def get_order_show_paid_fee():
    user_id = requests_utils.get_headers_info(request, "userId")
    order_id = request.json.get("orderId")
    if not order_id or order_id == "":
        logger.info("get_order_show_paid_fee order_id: {}".format(order_id))
        return jsonify_response({"paidFee": 0})
    if user_id is None:
        raise errors.ShowError("用户ID为空")
    order = OrderingServiceDataAccessHelper().get_order(id=order_id)
    fanpiao_pay_manager = FanpiaoPayManager(merchant_id=order.merchant_id)
    paid_fee = fanpiao_pay_manager.get_show_order_paid_fee(order, user_id, raise_error=False)
    logger.info("get_order_show_paid_fee: {}, {}".format(order.id, paid_fee.fee))
    ret = {
        "paidFee": paid_fee.fee,
        "remainFee": paid_fee.remain_fee,
        "remainDiscountFee": paid_fee.remain_discount_fee,
        "remainNoDiscountFee": paid_fee.remain_no_discount_fee,
    }
    return jsonify_response(ret)


@_fanpiao.route("/get_use_show_paid_fee", methods=["POST"])
def get_use_show_paid_fee():
    user_id = requests_utils.get_headers_info(request, "userId")
    bill_fee = request.json.get("billFee", 0)
    no_discount_fee = request.json.get("noDiscountFee", 0)
    merchant_id = request.json.get("merchantId", None)
    if bill_fee == 0:
        return jsonify_response({"paidFee": 0})
    if user_id is None:
        raise errors.ShowError("用户ID为空")
    paid_fee = FanpiaoPayManager(merchant_id=merchant_id).get_use_fanpiao_fee(
        bill_fee=bill_fee, no_discount_fee=no_discount_fee, user_id=user_id, merchant_id=merchant_id, raise_error=False
    )
    ret = {
        "paidFee": paid_fee.fee,
        "remainFee": paid_fee.remain_fee,
        "remainDiscountFee": paid_fee.remain_discount_fee,
        "remainNoDiscountFee": paid_fee.remain_no_discount_fee,
    }
    return jsonify_response(ret)


@_fanpiao.route("/fanpiao-usage/<string:merchant_id>", methods=["GET"])
def get_fanpiao_usage(merchant_id):
    fanpiao_id = request.args.get("fanpiaoId", None)
    manager = FanpiaoManager()
    usage_vo = manager.get_fanpiao_usage(fanpiao_id)
    if not usage_vo:
        return jsonify_response()
    usage_vo = json_format.MessageToDict(usage_vo, including_default_value_fields=True)
    return jsonify_response(usage_vo)


@_fanpiao.route("/recently/buy/<string:merchant_id>", methods=["GET"])
def get_recently_buy_fanpiao_list(merchant_id):
    fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
    recently_buy_fanpiao_list = fanpiao_manager.get_recently_buy_fanpiao_list()
    recently_buy_fanpiao_list = [
        json_format.MessageToDict(f, including_default_value_fields=True) for f in recently_buy_fanpiao_list
    ]
    return jsonify_response(recently_buy_fanpiao_list)


@_fanpiao.route("/platform/purchase/records", methods=["GET"])
def get_platform_fanpiao_purchase_records():
    """返回平台卡包购买记录"""
    fanpiao_manager = FanpiaoManager()
    ret = fanpiao_manager.get_platform_fanpiao_purchase_records()
    return jsonify_response(ret)


@_fanpiao.route("/qrcode/query", methods=["POST"])
def query_fanpiao_qrcode():
    fanpiao_qrcode_id = request.json.get("fanpiaoQrcodeId", None)
    fanpiao_manager = FanpiaoManager()
    qrcode_obj = fanpiao_manager.get_fanpiao_qrcode_obj(fanpiao_qrcode_id=fanpiao_qrcode_id, raise_error=True)
    qrcode_obj = json_format.MessageToDict(qrcode_obj, including_default_value_fields=True)
    return jsonify_response(qrcode_obj)


@_fanpiao.route("/qrcode/pay-success", methods=["POST"])
def pay_success():
    fanpiao_qrcode_id = request.json.get("fanpiaoQrcodeId", None)
    bill_fee = request.json.get("billFee", None)
    balance = request.json.get("balance", None)
    paid_fee = request.json.get("paidFee", None)
    fanpiao_manager = FanpiaoManager()
    qrcode_obj = fanpiao_manager.get_fanpiao_qrcode_obj(fanpiao_qrcode_id=fanpiao_qrcode_id)
    qrcode_obj.bill_fee = bill_fee
    qrcode_obj.paid_fee = paid_fee
    qrcode_obj.pay_method = fanpiao_pb.FanpiaoScanQrcode.MEMBER_ACCOUNT
    qrcode_obj.user_member_card_balance = balance
    qrcode_obj.balance = balance
    qrcode_obj.is_paid = "1"
    fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
    fanpiao_manager.publish_qrcode_info_complete(qrcode_obj)
    return jsonify_response()


@_fanpiao.route("/scan-qrcode", methods=["POST"])
def get_user_fanpiao_qrcode():
    """生成卡包扫码支付的二维码"""
    user_id = requests_utils.get_headers_info(request, "userId")
    qrcode_id = requests_utils.get_from_request(request, "qrcodeId", None)
    merchant_id = requests_utils.get_from_request(request, "merchantId", None)
    fanpiao_manager = FanpiaoManager()
    qrcode_obj = fanpiao_manager.get_user_fanpiao_qrcode(user_id, qrcode_id)
    if qrcode_obj and qrcode_obj.merchant_id:
        merchant_id = qrcode_obj.merchant_id
        paid_fee = FanpiaoPayManager(merchant_id=merchant_id).get_use_fanpiao_fee(
            bill_fee=qrcode_obj.bill_fee,
            no_discount_fee=0,
            user_id=user_id,
            merchant_id=qrcode_obj.merchant_id,
            raise_error=False,
        )
        remain_fee = paid_fee.remain_fee
        qrcode_obj.remain_fee = remain_fee
    if merchant_id:
        fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
        qrcode_obj.merchant_name = fanpiao_manager.merchant.basic_info.display_name
        qrcode_obj.balance = fanpiao_manager.get_user_fanpiao_fee(user_id=user_id)
    logger.info("scan-qrcode: {}".format(qrcode_obj))
    qrcode_obj = json_format.MessageToDict(qrcode_obj, including_default_value_fields=True)
    return jsonify_response(qrcode_obj)


@_fanpiao.route("/pos-order/prepay", methods=["POST"])
def pos_order_prepay():
    fanpiao_qrcode_id = request.json.get("fanpiaoQrcodeId", None)
    bill_fee = request.json.get("billFee", None)
    merchant_id = request.json.get("merchantId", None)
    order_id = request.json.get("orderId", None)
    callback_url = request.json.get("callbackUrl", None)
    payment_transaction_id = request.json.get("paymentTransactionId", None)
    helper = DirectPayManagerHelper()
    qrcode_obj = helper.get_and_preprocess_qrcode_obj(
        fanpiao_qrcode_id,
        merchant_id,
        order_id,
        bill_fee,
        callback_url,
    )
    no_discount_fee = helper.get_shilai_order_no_discount_fee(order_id, merchant_id)
    result = helper.pos_order_prepay(qrcode_obj, payment_transaction_id, no_discount_fee=no_discount_fee)
    return jsonify_response(result)


@_fanpiao.route("/pos-order/refund", methods=["POST"])
def pos_order_refund():
    """
    :refund_fee: 原价
    """
    payment_transaction_id = request.json.get("paymentTransactionId", None)
    refund_fee = request.json.get("refundFee", None)
    refund_dishes = request.json.get("refundDishes", [])
    fanpiao_manager = FanpiaoManager()
    qrcode_obj = fanpiao_manager.get_fanpiao_qrcode_obj(payment_transaction_id)

    transaction_manager = TransactionManager()
    transaction = transaction_manager.get_transaction_by_id(qrcode_obj.transaction_id)

    # 部分退，考虑分类折扣
    helper = DirectPayManagerHelper()
    no_discount_fee = helper.get_shilai_refund_order_no_discount_fee(qrcode_obj.order_id, qrcode_obj.merchant_id, refund_dishes=refund_dishes)
    fanpiao_da = FanpiaoDataAccessHelper()
    fanpiao = fanpiao_da.get_fanpiaos(record_transaction_id=transaction.id)[0]
    has_no_discount = helper.get_shilai_order_discount_status(qrcode_obj.order_id, qrcode_obj, refund_dishes, fanpiao)

    # 不支持按金额退款的情况：分类不打折，且按金额退
    if has_no_discount and not refund_dishes:
        return jsonify_response({"errmsg": "该订单不支持按金额退"})

    # 尝试卡包退款
    fanpiao_pay_manager = FanpiaoPayManager(merchant_id=qrcode_obj.merchant_id)
    refund_transaction = fanpiao_pay_manager.pos_order_refund(transaction, refund_fee=refund_fee)

    # 如果是卡包退款,需要设置一个负数的营销补贴金额
    platform_discount_fee = qrcode_obj.platform_discount_fee
    payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.WECHAT_PAY, merchant_id=refund_transaction.payee_id)
    order_manager = OrderManager(merchant_id=refund_transaction.payee_id)
    fanpiao_commission_fee = order_manager.cal_fanpiao_commission_fee(refund_transaction, no_discount_fee=no_discount_fee)
    payment_manager.update_pos_order_merchant_transfer_fee(refund_transaction, platform_discount_fee, fanpiao_commission_fee)

    # 如果qrcode_obj.bill_fee不为0,那么就还需要把剩余的部分重新支付
    qrcode_obj.bill_fee -= refund_fee
    result = helper.fanpiao_qrcode_prepay(qrcode_obj, partial_refund=True, no_discount_fee=no_discount_fee)
    if result:
        fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
        return jsonify_response(result)
    fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
    result = {"isPartialRefund": False}
    return jsonify_response(result)


@_fanpiao.route("/balance/refund-apply", methods=["POST"])
def balance_refund_apply():
    """卡包余额退款申请"""
    fanpiao_id = request.json.get("fanpiaoId", None)
    merchant_id = request.json.get("merchantId", None)
    user_id = request.headers.get("userId", None)
    # 中奖的卡包不允许退款
    if fanpiao_id in [
        "260adcffdd384083b34f9f6ac9cf0595",
        "511e75f9b92147b39f5505c3d9428376",
        "ad7a152f9ce04b84bc7ed6a9c51f856a",
        "31ebb5d6432947129c4dbe64fbcf32b0",
        "bf3c4bffd3f84e0d976f8bec4c41a4bb",
        "1882fb288448416b89deee5465314701",
    ]:
        raise errors.ShowError("该卡包不能申请退款")
    if user_id is None:
        raise errors.ShowError("用户不存在")
    fanpiao_da = FanpiaoDataAccessHelper()
    config_da = ConfigDataAccessHelper()
    config = config_da.get_fanpiao_config(merchant_id)
    if not config:
        raise errors.ShowError("该商户不允许卡包余额退款")
    if not config.enable_fanpiao_balance_refund:
        raise errors.ShowError("该商户不允许卡包余额退款")
    fanpiao = fanpiao_da.get_fanpiao(id=fanpiao_id)
    if not fanpiao:
        raise errors.ShowError("卡包不存在")
    if fanpiao.user_id != user_id:
        raise errors.ShowError("卡包拥有者错误")
    if fanpiao.status != fanpiao_pb.Fanpiao.ACTIVE:
        raise errors.ShowError("该卡包当前状态不允许余额退款")
    fanpiao.balance_refund_apply = True
    fanpiao_da.add_or_update_fanpiao(fanpiao)
    return jsonify_response()


@_fanpiao.route("/get-by-partial-id", methods=["POST"])
def get_fanpiao_by_partial_id():
    """根据卡包ID的前几位查询卡包"""
    merchant_id = request.json.get("merchantId", None)
    partial_fanpiao_id = request.json.get("partialFanpiaoId", None)  # 卡包ID的前6位
    if len(partial_fanpiao_id) != 6 and len(partial_fanpiao_id) != 32:
        raise errors.ShowError("请输入6位或32位ID")
    if partial_fanpiao_id is None or len(partial_fanpiao_id) < 4:
        raise errors.ShowError("卡包ID错误")
    manager = FanpiaoManager(merchant_id=merchant_id)
    if len(partial_fanpiao_id) == 6:
        results = manager.get_fanpiao_by_short_id(short_id=partial_fanpiao_id)
    elif len(partial_fanpiao_id) == 32:
        fanpiao = manager.get_fanpiao_by_id(fanpiao_id=partial_fanpiao_id)
        if fanpiao is None:
            results = []
        else:
            results = [json_format.MessageToDict(fanpiao, fanpiao_pb.Fanpiao)]
    if not results:
        raise errors.ShowError("卡包不存在")
    return jsonify_response(results)


_PERMISSION_STAFF_ID = [
    "0e3ed7f7-d6f8-413b-b11d-e651c462d347",  # 刘泳贤
    "e5fcf77d-178f-4221-8d1d-213e3abc95c8",  # 刘垒
    "c97cf5c1-407d-402c-b573-b90c3d88c353",  # 宋美丽
    "5fef9b53-0184-4c14-b608-d32e9b387ee9",  # 姚龙
    "607818a3-37cd-4283-8e50-7778cc059e20",  # 莫武
    "5317d1ea-4ba4-41cf-9f1e-0dc0c2b282f8",  # 叶晓红
]


@_fanpiao.route("/to-wallet", methods=["POST"])
def fanpiao_to_wallet():
    """将用户卡包余额退到时来钱包"""
    user_id = request.headers.get("userId", None)
    if user_id not in _PERMISSION_STAFF_ID:
        raise errors.ShowError("没有权限操作")
    merchant_id = request.json.get("merchantId", None)
    compute_method = request.json.get("computeMethod", None)
    fanpiao_id = request.json.get("fanpiaoId", None)
    manager = FanpiaoManager(merchant_id=merchant_id)
    fanpiao = manager.get_fanpiao_by_id(fanpiao_id)
    manager.staff_perform_balance_refund(fanpiao, compute_method, user_id)
    return jsonify_response()


@_fanpiao.route("/refund", methods=["POST"])
def refund_fanpiao():
    """饭票退款"""
    body = check_body(
        {
            "user_id": {"required": True, "type": str},
            "fanpiao_id": {"required": True, "type": str},
            "is_refund_the_way": {"required": True, "type": bool},  # True：原路返回，False：退到零钱
            "balance_refund_method": {  # BY_ORIG_PRICE: 按原价折算退款，BY_BALANCE：按饭票余额退款
                "required": True,
                "type": str,
                "in": _balance_refund_methods,
            },
        }
    )
    if config.deployment_env == 'prod' and body.user_id not in _PERMISSION_STAFF_ID:
        raise errors.ShowError("没有权限操作")
    manager = FanpiaoManager()
    fanpiao = manager.get_fanpiao_by_id(body.fanpiao_id)
    manager.staff_perform_balance_refund_v2(fanpiao, body.is_refund_the_way, body.balance_refund_method, body.user_id)


@_fanpiao.route("/update", methods=["POST"])
def update_fanpiao():
    user_id = request.headers.get("userId", None)
    if user_id not in _PERMISSION_STAFF_ID:
        raise errors.ShowError("没有权限操作")
    fanpiao_id = request.json.get("fanpiaoId", None)
    merchant_id = request.json.get("merchantId")
    status = request.json.get("status", None)
    expire_time = request.json.get('expireTime', 0)
    manager = FanpiaoManager(merchant_id=merchant_id)
    fanpiao = manager.get_fanpiao_by_id(fanpiao_id)
    manager.update_fanpiao(fanpiao, status=status, expire_time=expire_time)
    logger.info(f"{user_id} 更新饭票: {fanpiao_id} {fanpiao_pb.Fanpiao.Status.Name(fanpiao.status)} update {status} {expire_time}")
    OperationDataAccessHelper().add_operation(user_id, 'FANPIAO', f"更新饭票: {fanpiao.id} {status or expire_time}")
    return jsonify_response()


# 用于分析卡包信息（需求单：https://www.tapd.cn/59487053/prong/stories/view/1159487053001000383）
@_fanpiao.route("/record-log", methods=["POST"])
def fanpiao_record_log():
    """用于分析卡包信息"""
    param = {
        "userId": request.json.get("userId", ""),
        "nickName": request.json.get("nickName", ""),
        "type": request.json.get("type", None),
        "merchantId": request.json.get("merchantId", ""),
        "merchantName": request.json.get("merchantName", ""),
        "mealTicketId": request.json.get("mealTicketId", ""),
        "createTime": request.json.get("purchaseTime", ""),
        "source": request.json.get("source", None),
    }
    try:
        FanpiaoManager().add_fanpiao_record_log(param)
    except Exception as e:
        return jsonify_response(str(e))
    return jsonify_response()


@_fanpiao.route("/first-buy-fanpiao-activity", methods=["POST"])
def activity_first_fanpiao():
    """返回当前卡包活动中,用户第一次购买的卡包"""
    user_id = request.headers.get("userId")
    merchant_id = request.json.get("merchantId")
    activity_manager = ActivityManager()
    fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
    if user_id is None:
        return jsonify_response()
    first_time_buy_fanpiao_activity = activity_manager.get_current_first_time_buy_fanpiao_activity(merchant_id=merchant_id)
    if not first_time_buy_fanpiao_activity:
        return jsonify_response()
    fanpiaos = fanpiao_manager.get_user_fanpiaos(user_id=user_id, merchant_id=merchant_id)
    if len(fanpiaos) == 0:
        return jsonify_response()
    for index, fanpiao in enumerate(fanpiaos):
        if not (
            first_time_buy_fanpiao_activity.start_timestamp
            < str(fanpiao.buy_time)
            < first_time_buy_fanpiao_activity.end_timestamp
        ):
            return jsonify_response()
    return jsonify_response({"fanpiaoId": fanpiaos[-1].id})


@_fanpiao.route('/can-use-fanpiao', methods=['POST'])
def can_use_fanpiao():
    merchant_id = request.json.get("merchantId")
    fanpiao_manager = FanpiaoManager(merchant_id=merchant_id)
    ret = True
    if fanpiao_manager.merchant and len(fanpiao_manager.merchant.stores) > 0:
        store = fanpiao_manager.merchant.stores[0]
        ret = store.enable_fanpiao
    return jsonify_response(ret)


_balance_refund_methods = list(
    map(
        config_pb.FanpiaoConfig.BalanceRefundMethod.Name,
        config_pb.FanpiaoConfig.BalanceRefundMethod.values(),
    )
)
_fanpiao_status = list(
    map(
        fanpiao_pb.Fanpiao.Status.Name,
        fanpiao_pb.Fanpiao.Status.values(),
    )
)


@_fanpiao.route("/add_refund_cronjob", methods=["POST"])
def add_refund_task():
    body = check_body(
        {
            "user_id": {"required": True, "type": str},
            "merchant_id": {"required": True, "type": str},
            "fanpiao_id": {"required": True, "type": str},
            "is_refund_the_way": {"required": True, "type": bool},  # True: 原路退回, False: 时来钱包
            "balance_refund_method": {
                "required": True,
                "type": str,
                "in": _balance_refund_methods,
            },  # BY_ORIG_PRICE: 原价，BY_BALANCE：余额
        }
    )
    if body.pop("user_id") not in _PERMISSION_STAFF_ID:
        raise errors.ShowError("没有权限操作")
    manager = FanpiaoManager(merchant_id=body.merchant_id)
    fanpiao = manager.get_fanpiao_by_id(body.fanpiao_id)
    if manager.exists(fanpiao.id):
        raise errors.Error(err=error_codes.FANPIAO_EXISTS_REFUND)
    manager.check_refund_cronjob(fanpiao)
    merchant_manager = MerchantManager(merchant_id=body.merchant_id)
    return manager.add_refund_cronjob(
        status=fanpiao_pb.Fanpiao.Status.Name(fanpiao.status),
        user_id=fanpiao.user_id,
        merchant_name=merchant_manager.get_merchant_name(),
        balance=fanpiao.total_value - fanpiao.total_used_fee,
        discount=100 - fanpiao.discount,
        sell_price=fanpiao.sell_price,
        buy_time=fanpiao.buy_time,
        **body,
    )


@_fanpiao.route("/search", methods=["POST"])
def search_fanpiao():
    body = check_body(
        {
            "merchant_id": {"required": True, "type": str},
            "short_id": {"required": False, "type": str},
        }
    )
    help = FanpiaoDataAccessHelper()
    fanpiaos = help.get_fanpiao_by_partial_id_v2(**body)
    manager = FanpiaoManager(merchant_id=body.merchant_id)
    fanpiaos = [manager.get_fanpiao_usage(f.id) for f in fanpiaos]
    return batch_protobuf_to_dict(fanpiaos)


@_fanpiao.route("/query_refund_cronjob", methods=["POST"])
def query_refund_task():
    body = check_body(
        {
            "merchant_id": {"required": False, "type": str},
            "merchant_name": {"required": False, "type": str},
            "fanpiao_id": {"required": False, "type": str},
            "is_refund_the_way": {"required": False, "type": bool},
            "balance_refund_method": {"required": False, "type": str, "in": _balance_refund_methods},
            "status": {"required": False, "type": str, "in": _fanpiao_status},
        }
    )
    manager = FanpiaoManager(merchant_id=body.merchant_id)
    return manager.query_refund_cronjob(**body)


@_fanpiao.route("/update_refund_cronjob", methods=["POST"])
def update_refund_cronjob():
    body = check_body(
        {
            "id": {"required": True, "type": str},
            "is_refund_the_way": {"required": False, "type": bool},
            "balance_refund_method": {"required": False, "type": str, "in": _balance_refund_methods},
        }
    )
    manager = FanpiaoManager()
    return manager.update_refund_cronjob(**body)

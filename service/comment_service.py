# -*- coding: utf-8 -*-

"""
Filename: comment_service.py
Date: 2020-06-12 11:23:50
Title: 扫码点餐评论功能
"""

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from common.utils import requests_utils
from service.base_responses import jsonify_response
from business_ops.comment_manager import CommentManager

_comment = Blueprint(__name__, __name__, url_prefix="/comment")


@_comment.route("/create/<string:merchant_id>", methods=["POST"])
def create_comment(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    rating = request.json.get("rating", 1)
    comment = request.json.get("comment")
    order_id = request.json.get("orderId")
    store_id = "{}_0".format(merchant_id)
    CommentManager().create_comment(user_id, rating, comment, merchant_id, store_id, order_id)
    return jsonify_response()


@_comment.route("/get_comments/<string:merchant_id>", methods=["GET"])
def get_comments(merchant_id):
    latest_create_time = request.args.get('latestCreateTime')
    comment_list_vo = CommentManager().get_comments(merchant_id, latest_create_time)
    comment_list_vo = json_format.MessageToDict(comment_list_vo, including_default_value_fields=True)
    return jsonify_response(comment_list_vo)

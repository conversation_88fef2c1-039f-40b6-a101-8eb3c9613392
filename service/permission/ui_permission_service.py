# -*- encoding: utf-8 -*-
'''
@Time        :2024/05/09 21:46:07
前端页面功能权限控制
'''

import time
import logging

from flask import Blueprint

from common.request import check_body
from service import errors, error_codes
from business_ops.permission_manager import UIPermissionManager
from proto import permission_pb2 as permission_pb


bp_name = "permission"
_ui_permission_bp = Blueprint(bp_name, bp_name, url_prefix="/ui_permission")

logger = logging.getLogger(__name__)
_ui_permission_manager = UIPermissionManager()
_get_timestamp = lambda: int(time.time())
_role_enum_str = list(map(permission_pb.RoleEnum.Name, permission_pb.RoleEnum.values()))


@_ui_permission_bp.route("/insert", methods=["POST"])
def insert():
    data = check_body(
        {
            "name": {"required": True, "type": str},
            "role": {"required": True, "type": str},
            "permission_set": {"required": True, "type": dict},
        }
    )
    role = _ui_permission_manager.create_role_permission(**data)
    if _ui_permission_manager.get(id=role.id):
        raise errors.Error(err=error_codes.PERMISSION_EXISTS)
    return _ui_permission_manager.add_or_update(role)


@_ui_permission_bp.route("/get", methods=["POST"])
def get():
    body = check_body(
        {
            "name": {"required": True, "type": str},
            "role": {"required": True, "type": str},
        }
    )
    return _ui_permission_manager.get(matcher=body)


@_ui_permission_bp.route("/query", methods=["GET"])
def query():
    return _ui_permission_manager.query()


@_ui_permission_bp.route("/delete", methods=["POST"])
def delete():
    data = check_body({"ids": {"required": True, "type": list}})
    return _ui_permission_manager.delete({"id": {"$in": data.ids}})


@_ui_permission_bp.route("/update", methods=["POST"])
def update():
    data = check_body(
        {
            "id": {"required": True, "type": str},
            "name": {"required": True, "type": str},
            "role": {"required": True, "type": str},
            "permission_set": {"required": True, "type": dict},
        }
    )
    data.update_time = _get_timestamp()
    return _ui_permission_manager.add_or_update(data)


@_ui_permission_bp.route("/update/all_role", methods=["POST"])
def all_role():
    """为所有角色新增功能权限"""
    data = check_body({"name": {"required": True, "type": str}, "permission_set": {"required": True, "type": dict}})
    permission_set = dict()
    for k, v in data.permission_set.items():
        v = v.strip()
        if isinstance(v, int):
            v = permission_pb.StatusEnum.Name(v)
        else:
            v = permission_pb.StatusEnum.Name(permission_pb.StatusEnum.Value(v))
        permission_set[k.strip()] = v

    for role in permission_pb.RoleEnum.keys():
        _permission_set = permission_set.copy()
        doc = _ui_permission_manager.get_role(
            data.name,
            role,
            projection={"id": 1, "permissionSet": 1, "role": 1},
        )
        if not doc:
            doc = _ui_permission_manager.create_role_permission(
                name=data.name,
                role=role,
                permission_set=_permission_set,
            )
        else:
            doc.permission_set = {**_permission_set, **doc.permission_set}
            doc.updateTime = _get_timestamp()
        if permission_pb.RoleEnum.Name(permission_pb.RoleEnum.ADMIN) == doc.role:
            doc.permission_set = {
                k: permission_pb.StatusEnum.Name(permission_pb.StatusEnum.ENABLE) for k in doc.permission_set.keys()
            }
        elif permission_pb.RoleEnum.Name(permission_pb.RoleEnum.OTHER) == doc.role:
            doc.permission_set = {
                k: permission_pb.StatusEnum.Name(permission_pb.StatusEnum.DISABLE) for k in doc.permission_set.keys()
            }
        doc = _ui_permission_manager.add_or_update(doc)
        logger.info(doc)

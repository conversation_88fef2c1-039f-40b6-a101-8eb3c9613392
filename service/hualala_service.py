# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request

from google.protobuf import json_format

from business_ops.ordering.hualala_shop_manager import HualalaShopManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.table_manager import TableManager
from business_ops.ordering.shop_manager import ShopManager
from service.base_responses import jsonify_response


_hualala = Blueprint(__name__, __name__, url_prefix="/hualala")


@_hualala.route("/group/<string:merchant_id>", methods=["GET"])
def get_all_shop(merchant_id):
    manager = HualalaShopManager(merchant_id=merchant_id)
    group_id = request.args.get("groupId", None)
    group = manager.get_group_info(group_id=group_id)
    return jsonify_response(group)


@_hualala.route("/shop/<string:merchant_id>", methods=["GET"])
def get_shop(merchant_id):
    manager = HualalaShopManager(merchant_id=merchant_id)
    group_id = request.args.get("groupId", None)
    shop_id = request.args.get("shopId", None)
    shop = manager.get_shop_info(group_id=group_id, shop_id=shop_id)
    return jsonify_response(shop)


@_hualala.route("/dish_category/<string:merchant_id>", methods=["GET"])
def get_dish_categories(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    dish_categories = manager.async_categories()
    dish_categories = [json_format.MessageToDict(category, including_default_value_fields=True)
                       for category in dish_categories]
    return jsonify_response(dish_categories)


@_hualala.route("/pay/subject/<string:merchant_id>", methods=["GET"])
def get_pay_subjects(merchant_id):
    manager = ShopManager(merchant_id=merchant_id)
    group_id = request.args.get("groupId", None)
    shop_id = request.args.get("shopId", None)
    pay_subjects = manager.get_pay_subject(group_id, shop_id)
    if not pay_subjects:
        return jsonify_response()
    for pay_subject in pay_subjects:
        print(pay_subject.get("subjectGroupName"))
    return jsonify_response(pay_subject)


@_hualala.route("/group/pay/subject/<string:merchant_id>", methods=["GET"])
def get_group_pay_subjects(merchant_id):
    manager = ShopManager(merchant_id=merchant_id)
    group_id = request.args.get("groupId", None)
    pay_subjects = manager.get_group_pay_subject(group_id)
    if not pay_subjects:
        return jsonify_response()
    for pay_subject in pay_subjects:
        print(pay_subject.get("subjectGroupName"))
    return jsonify_response(pay_subject)

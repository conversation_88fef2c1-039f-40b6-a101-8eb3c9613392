# -*- coding: utf-8 -*-


from flask import Blueprint
from flask import request

from google.protobuf import json_format

from business_ops.activity_manager import ActivityManager
from service.base_responses import jsonify_response

_activity = Blueprint(__name__, __name__, url_prefix="/activity")


@_activity.route("/subscribe-wechat-message/<string:merchant_id>", methods=["POST"])
def subscribe_wechat_message(merchant_id):
    """
    """
    activity_type = request.json.get("activityType", None)
    user_id = request.headers.get("userId", None)
    manager = ActivityManager(user_id=user_id, merchant_id=merchant_id)
    manager.subscribe_wechat_message(activity_type)
    return jsonify_response()


@_activity.route("/send-wechat-message", methods=["POST"])
def send_subscribe_wechat_message():
    activity_type = request.json.get("activityType", None)
    manager = ActivityManager()
    manager.send_activity_wechat_message(activity_type)
    return jsonify_response()


@_activity.route("/create", methods=["POST"])
def create_activity():
    """
    2022-09-23: 增加第一次购买饭票送礼物的活动
    http :12916/activity/create name=第一次购买包票送礼物的活动 startTime="2022-09-26 00:00:00" endTime="2022-10-01 00:00:00" activityType=FIRST_TIME_BUY_FANPIAO
    """
    manager = ActivityManager()
    activity = manager.create_activity(
        start_timestamp=request.json.get("startTimestamp"),
        end_timestamp=request.json.get("endTimestamp"),
        name=request.json.get("name"),
        activity_type=request.json.get("activityType")
    )
    return jsonify_response(json_format.MessageToDict(activity, including_default_value_fields=True))


@_activity.route("/clear/merchant", methods=["POST"])
def clear_merchant():
    activity_id = request.json.get("activityId")
    manager = ActivityManager()
    activity = manager.get_activity_config(activity_id)
    manager.clear_merchant(activity)
    return jsonify_response()


@_activity.route("/update", methods=["POST"])
def update_activity():
    manager = ActivityManager()
    manager.update_activity(
        activity_id=request.json.get("activityId"),
        start_timestamp=request.json.get("startTimestamp"),
        end_timestamp=request.json.get("endTimestamp"),
        name=request.json.get("name"),
        activity_type=request.json.get("activityType"),
        disable_activity=request.json.get("disableActivity")
    )
    return jsonify_response()


@_activity.route("/list", methods=["POST"])
def list_activities():
    manager = ActivityManager()
    merchant_id = None
    if request.json:
        merchant_id = request.json.get("merchantId")
    activities = manager.list_activities(merchant_id=merchant_id)
    activities = reversed(activities)
    return jsonify_response([
        json_format.MessageToDict(a, including_default_value_fields=True) for a in activities
    ])


@_activity.route("/merchant/update", methods=["POST"])
def update_merchant_activity_config():
    manager = ActivityManager()
    merchant_id = request.json.get("merchantId")
    activity_ids = request.json.get("activityIds")
    merchant_activity_config = manager.get_merchant_activity_config(merchant_id=merchant_id)
    manager.update_merchant_activity_config(merchant_activity_config, activity_ids=activity_ids)
    manager.add_or_update_merchant_activity_config(merchant_activity_config)
    return jsonify_response()

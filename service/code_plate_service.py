# -*- coding: utf-8 -*-

# -*- coding: utf-8 -*-

import os

from flask import Blueprint
from flask import jsonify
from flask import request
from google.protobuf import json_format
from common.utils import id_manager 
from common import constants
from common.config import config
from common.utils import requests_utils
from dao.base_helper import BaseHelper
from dao.code_plate_da_helper import CodePlateHelper
from dao.staff_da_helper import StaffDataAccessHelper
from service import error_codes
from service.base_responses import create_responses_obj
from service.base_responses import error_responses
from service.base_responses import success_responses_obj
from service.base_responses import jsonify_response


# 码牌生成二维码逻辑
code_plate = Blueprint("code_plate", __name__)

@code_plate.route('/merchant/code_plate/qrcode/create', methods=['POST'])
def create_qrcode_for_merchant():
    """批量生成码牌二维码，用于商户支付码牌
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())
    # 生成随机id的字符串
    request_json = request.json
    num = requests_utils.get_value_from_json(request_json, 'num')
    scene = requests_utils.get_value_from_json(request_json, 'scene')
    h5_domain = os.environ.get("H5_SERVICE_DOMAIN", "http://shilai-h5.zhiyi.cn")
    for _ in range(int(num)):
        id = id_manager.generate_qrcode_id()
        if not scene:
            encode_text = f'{h5_domain}/direct-payment?scene={id}_V2'
            qrcode_id = BaseHelper().create_code_plate_qrcode(id, encode_text)
            # 增加绑定二维码
            CodePlateHelper().add_binings({"qrcode_id":qrcode_id,"merchant_id":""})
        else:
            # 根据场景生成二维码
            encode_text = f'{h5_domain}/direct-order?scene={scene}'
            BaseHelper().create_publicity_qrcode(id, encode_text, scene)
    response_obj = success_responses_obj()
    resp = jsonify(response_obj)
    return resp

@code_plate.route('/merchant/code_plate/qrcode/binding', methods=['POST'])
def binding_qrcode_for_merchant():
    """商户绑定码牌二维码，用于商户支付码牌
    """
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())
    # 绑定商户id和二维码id
    merchant_id = requests_utils.get_value_from_json(request_json, 'merchantId')
    qrcode_id = requests_utils.get_value_from_json(request_json, 'qrcodeId')
    binings = {
        "merchant_id": merchant_id,
        "qrcode_id": qrcode_id
    }
    # 看商家是否绑定
    # rst = CodePlateHelper().get_binings(merchant_id=merchant_id)
    # if rst and len(rst) > 0 and rst[0].qrcode_id != '':
    #     return jsonify(create_responses_obj(error_codes.FAIL ,"商家已经绑定二维码"))
    # 看二维码是否绑定
    rst = CodePlateHelper().get_binings(qrcode_id=qrcode_id)
    if rst and len(rst) > 0 and rst[0].merchant_id != '':
        return jsonify(create_responses_obj(error_codes.FAIL ,"二维码已经绑定过商家: {}".format(rst[0].merchant_id)))
    # 增加绑定二维码关系映射
    CodePlateHelper().update_or_create_binings(binings)
    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    resp = jsonify(success_responses_obj)
    return resp

@code_plate.route('/merchant/code_plate/qrcode/get', methods=['POST'])
def get_qrcode_for_merchant():
    """商户绑定码牌二维码，用于商户支付码牌
    """
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())
    # 绑定商户id和二维码id
    merchant_id = requests_utils.get_value_from_json(request_json, 'merchantId')
    qrcode_id = requests_utils.get_value_from_json(request_json, 'qrcodeId')
    # 获取绑定二维码关系映射
    code_plates = CodePlateHelper().get_binings(qrcode_id=qrcode_id, merchant_id=merchant_id)
    json_binings = [json_format.MessageToDict(code_plate, including_default_value_fields=True) for code_plate in code_plates]
    return jsonify_response(json_binings)

@code_plate.route('/merchant/code_plate/qrcode/delete', methods=['POST'])
def delete_qrcode_for_merchant():
    """商户解绑二维码
    """
    request_json = request.json
    user_id = requests_utils.get_headers_info(request, 'userId')
    staff = StaffDataAccessHelper().get_staff(user_id)
    if not staff:
        return jsonify(error_responses())
    # 绑定商户id和二维码id
    merchant_id = requests_utils.get_value_from_json(request_json, 'merchantId')
    qrcode_id = requests_utils.get_value_from_json(request_json, 'qrcodeId')
    if merchant_id is None or qrcode_id is None:
        return jsonify(create_responses_obj(error_codes.FAIL ,"参数错误"))
    # 看商家是否绑定此二维码
    rst = CodePlateHelper().get_binings(merchant_id=merchant_id, qrcode_id=qrcode_id)
    if rst is None:
        return jsonify(error_responses())
    if len(rst) == 0:
        return jsonify(create_responses_obj(error_codes.FAIL ,"商家没有绑定此二维码"))
    # 删除绑定商家绑定的二维码
    CodePlateHelper().delete_bining(merchant_id=merchant_id, qrcode_id=qrcode_id)
    success_responses_obj = create_responses_obj(error_codes.SUCCESS, 'success')
    resp = jsonify(success_responses_obj)
    return resp
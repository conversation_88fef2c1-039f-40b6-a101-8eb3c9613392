import json
import logging
import threading

from geventwebsocket import WebSocketApplication
from google.protobuf import json_format

from cache.redis_client import RedisClient
from proto.websocket import common_pb2 as common_pb
from service.websockets.message_handler import WebSocketMessageHandler

logger = logging.getLogger(__name__)

class WebsocketService(WebSocketApplication):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.message_handler = WebSocketMessageHandler(RedisClient())

    def on_open(self):
        try:
            # /wsservice/<user_id>
            # /wsservice/group_dining/<groupDiningId>/<userId>
            self.redis = RedisClient()
            path = self.ws.path.split("/")
            self.group_dining_id = None
            if len(path) == 3:
                self.user_id = path[2]
                self.subscribe(self.user_id)
            elif len(path) == 5:
                self.user_id = path[4]
                self.group_dining_id = path[3]
            else:
                raise Exception('websocket 连接路径出错')
            if self.group_dining_id:
                self.subscribe(self.group_dining_id)
            self.thread = threading.Thread(target=self.__subscribe_redis_message)
            self.thread.setDaemon(True)
            self.thread.start()
        except BaseException as e:
            logger.exception("WebSocket连接出错: {}".format(e))

    def on_message(self, message):
        if message is None:
            return
        try:
            message_json = json.loads(message)
            logger.info("message_json: %s" % (message_json))
            event_message = json_format.ParseDict(message_json, common_pb.EventMessage(), ignore_unknown_fields=True)
            self.message_handler.handle_websocket_message(event_message)
            if event_message.type == common_pb.JOIN_GROUP_DINING_CHAT_ROOM:
                self.subscribe(event_message.join_group_dining_chat_room.group_dining_event_id)
            elif event_message.type == common_pb.LEAVE_GROUP_DINING_CHAT_ROOM:
                self.unsubscribe(event_message.leave_group_dining_chat_room.group_dining_event_id)
        except BaseException as e:
            logger.error("接收消息出错: {}".format(e))

    def on_close(self, reason):
        logger.info("WebSocket断开连接，原因: {}".format(reason))

        if self.redis:
            logger.info("redis: %s" % (self.redis))
            self.redis.pubsub.unsubscribe()
            self.redis.pubsub.close()
            self.message_handler.handle_websocket_close(self.user_id)

    def subscribe(self, room_id):
        """ 订阅 房间消息
        """
        ret = self.redis.pubsub.subscribe(room_id)
        logger.info("subscribe: %s, %s" % (room_id, ret))

    def unsubscribe(self, room_id):
        """ 取消 订阅 房间消息
        """
        ret = self.redis.pubsub.unsubscribe(room_id)
        logger.info("unsubscribe: %s, %s" % (room_id, ret))

    def __subscribe_redis_message(self):
        try:
            for message in self.redis.pubsub.listen():
                self.ws.send(message['data'].decode())
        except Exception as ex:
            logger.exception("subscribe_redis_message: %s" % (ex))

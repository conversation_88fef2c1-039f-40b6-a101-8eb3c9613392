
import json
import logging

from google.protobuf import json_format

from business_ops.message_manager import MessageManager
from common.utils import date_utils
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.chat_message_da_helper import ChatMessageDataAccessHelper
from dao.user_da_helper import UserData<PERSON>ccessHelper
from dao.private_message_da_helper import PrivateMessageDataAccessHelper
from proto.websocket import common_pb2 as common_pb

logger = logging.getLogger(__name__)

class WebSocketMessageHandler():
    def __init__(self, redis_helper):
        self.redis_helper = redis_helper

    def handle_websocket_message(self, event_message):
        if event_message.type == common_pb.GROUP_DINING_EVENT_CHAT_MESSAGE:
            self.handle_group_dining_event_chat(event_message)
        elif event_message.type == common_pb.PRIVATE_MESSAGE:
            self.handle_private_message(event_message)
        elif event_message.type == common_pb.DELETE_PRIVATE_MESSAGE:
            self.handle_delete_private_message(event_message)
        elif event_message.type == common_pb.NOTIFICATION_LAST_VIEWED_TIME:
            self.handle_notification_last_viewed_time(event_message)
        elif event_message.type == common_pb.APPLY_FOR_LAST_VIEWED_TIME:
            self.handle_apply_for_last_viewed_time(event_message)
        elif event_message.type == common_pb.JOIN_PRIVATE_CHAT_ROOM:
            self.handle_join_private_chat_room(event_message)
        elif event_message.type == common_pb.LEAVE_PRIVATE_CHAT_ROOM:
            self.handle_leave_private_chat_room(event_message)

    def handle_websocket_close(self, user_id):
        """ 当客户端断开时,需要设置用户离开房间
        """
        redis_client = RedisClient().get_connection()
        key = "private_room_%s" % (user_id)
        rooms = redis_client.hgetall(key)
        for room in rooms:
            redis_client.hdel(room, user_id)
        redis_client.delete(key)

    def handle_join_private_chat_room(self, event):
        """ 加入私聊房间
        """
        private_message_da = PrivateMessageDataAccessHelper()
        message = event.join_private_chat_room
        user_id = message.user_id
        chatter_id = message.chatter_id
        id = id_manager.generate_message_id(user_id, chatter_id)
        private_message_da.add_or_update_last_view_time(user_id=user_id, id=id,
                                                        last_viewed_time=date_utils.timestamp_second(),
                                                        deleted=False)
        redis_client = RedisClient().get_connection()
        redis_client.hset(id, user_id, 1)
        key = "private_room_%s" % (user_id)
        redis_client.hset(key, id, 1)

    def handle_leave_private_chat_room(self, event):
        """ 退出私聊房间
        """
        private_message_da = PrivateMessageDataAccessHelper()
        message = event.leave_private_chat_room
        user_id = message.user_id
        chatter_id = message.chatter_id
        id = id_manager.generate_message_id(user_id, chatter_id)
        private_message_da.add_or_update_last_view_time(user_id=user_id, id=id,
                                                        last_viewed_time=date_utils.timestamp_second(),
                                                        deleted=False)
        redis_client = RedisClient().get_connection()
        redis_client.hdel(id, user_id)
        key = "private_room_%s" % (user_id)
        redis_client.hdel(key, id)


    def handle_notification_last_viewed_time(self, event):
        message = event.notification_last_viewed_time
        last_viewed_time = date_utils.timestamp_second()
        MessageManager().add_or_update_last_view_time(user_id=message.user_id,
                                                      type=common_pb.SYSTEM_NOTIFICATION,
                                                      last_viewed_time=last_viewed_time)

    def handle_apply_for_last_viewed_time(self, event):
        message = event.apply_for_last_viewed_time
        MessageManager().add_or_update_last_view_time(user_id=message.user_id,
                                                      type=common_pb.APPLY_FOR_GROUP_DINING_MESSAGE,
                                                      last_viewed_time=date_utils.timestamp_second())

    def handle_delete_private_message(self, event):
        message = event.private_message
        private_message_da = PrivateMessageDataAccessHelper()
        id = id_manager.generate_message_id(message.sender_id, message.receiver_id)
        private_message_da.delete_private_message(user_id=message.sender_id,
                                                  id=id)
        MessageManager().add_or_update_last_viewed_time(user_id=message.sender_id,
                                                        type=common_pb.PRIVATE_MESSAGE,
                                                        message_id=id,
                                                        last_viewed_time=date_utils.timestamp_second(),
                                                        deleted=True)

    def handle_group_dining_event_chat(self, event_message):
        chat_message = event_message.chat_message
        chat_message.id = id_manager.generate_common_id()
        chat_message.create_time = date_utils.timestamp_second()
        ChatMessageDataAccessHelper().add_group_dining_message(chat_message)
        channel = '{}'.format(chat_message.group_dining_event_id)
        message_json = json_format.MessageToDict(event_message, including_default_value_fields=True)
        user = UserDataAccessHelper().get_user(event_message.chat_message.sender_id)
        message_json["chatMessage"].update({
            "nickname": user.member_profile.nickname,
            "headimgurl": user.member_profile.head_image_url
        })
        self.redis_helper.conn.publish(channel, json.dumps(message_json))
        logger.info("publish group dining event chat message: {}, to channel: {}".format(message_json, channel))

    def handle_private_message(self, event_message):
        """ 私聊消息
        """
        private_message_da = PrivateMessageDataAccessHelper()
        event_message.create_time = date_utils.timestamp_second()
        message = event_message.chat_message
        message.create_time = date_utils.timestamp_second()
        sender_id = message.sender_id
        receiver_id = message.receiver_id

        message.id = id_manager.generate_message_id(sender_id, receiver_id)
        private_message_da.add_message(message)

        sender = UserDataAccessHelper().get_user(sender_id)
        receiver = UserDataAccessHelper().get_user(receiver_id)

        # 给接收者发消息,增加发送者的头像和昵称
        message_dict = json_format.MessageToDict(event_message, including_default_value_fields=True)
        message_dict['chatMessage'].update({
            "nickname": sender.member_profile.nickname,
            "headimgurl": sender.member_profile.head_image_url
        })
        self.redis_helper.conn.publish(receiver_id, json.dumps(message_dict))

        # 给发送者发消息,增加接收者的头像和昵称
        message_dict = json_format.MessageToDict(event_message, including_default_value_fields=True)
        message_dict['chatMessage'].update({
            "nickname": sender.member_profile.nickname,
            "headimgurl": sender.member_profile.head_image_url
        })
        self.redis_helper.conn.publish(sender_id, json.dumps(message_dict))

        # 把所有在房间的用户的最后读消息时间设为当前时间
        redis_client = RedisClient().get_connection()
        users = redis_client.hgetall(message.id)
        for user in users.keys():
            MessageManager().add_or_update_last_viewed_time(user_id=user.decode('utf8'),
                                                            type=common_pb.PRIVATE_MESSAGE,
                                                            message_id=message.id,
                                                            last_viewed_time=message.create_time,
                                                            deleted=False)

        if receiver_id not in users.keys():
            # 如果接收者不在私聊房间里的话,就需要给接收者发消息,更新消息中心页面的未读消息的数量
            count = MessageManager().get_private_cnt(sender_id, receiver_id)
            event = {
                "createTime": event_message.create_time,
                "headimgurl": sender.member_profile.head_image_url,
                "message": event_message.chat_message.message,
                "messageCnt": count,
                "nickname": sender.member_profile.nickname,
                "userId": sender_id,
                "type": "PRIVATE_MESSAGE_NOTIFY"
            }
            self.redis_helper.conn.publish(receiver_id, json.dumps(event))

        # 通知消息发送者,把最后一条消息的内容更新,把未读消息的数量去掉
        event = {
            "createTime": event_message.create_time,
            "headimgurl": receiver.member_profile.head_image_url,
            "message": event_message.chat_message.message,
            "messageCnt": 0,
            "nickname": receiver.member_profile.nickname,
            "userId": receiver_id,
            "type": "PRIVATE_MESSAGE_NOTIFY"
        }
        self.redis_helper.conn.publish(sender_id, json.dumps(event))

from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops import merchant_store_manager
from business_ops.merchant_manager import MerchantManager
from business_ops.merchant_store_manager import get_merchant_store
from common.utils import requests_utils
from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from proto import merchant_rules_pb2 as merchant_rules_pb
from service import errors
from service import base_responses
from view_ops.merchant_view_helper import MerchantViewObjectHelper
from view_ops.dining_event_view_helper import DiningEventViewObjectHelper

store = Blueprint("store", __name__)

@store.before_request
def check_permission():
    """操作权限检查

    只有商户的超级管理员才有权限对商户进行操作
    """
    if request.method == "POST":
        merchant_id = request.view_args["merchant_id"]
        store_id = request.view_args["store_id"]
        merchant, store = get_merchant_store(merchant_id, store_id)

        if not store:
            raise errors.MerchantStoreNotFoundError()

        user_id = requests_utils.get_headers_info(request, "userId")
        role = MerchantManager().get_merchant_staff_role(merchant_id, user_id)
        if role != merchant_rules_pb.SUPER_ADMIN:
            raise errors.PermissionError()

        setattr(request, "merchant", merchant)
        setattr(request, "store", store)
    return None

# TODO: 如果接口没有使用了就删除
@store.route("/merchant/<string:merchant_id>/store/<string:store_id>", methods=["GET"])
def get_merchant_store_info(merchant_id, store_id):
    """获取商户门店信息
    """
    _, store = get_merchant_store(merchant_id, store_id)
    if store:
        store_vo = MerchantViewObjectHelper().convert_merchant_store_vo(store)
        return base_responses.make_json_response({
            'store': json_format.MessageToDict(store_vo, including_default_value_fields=True)
        })
    return base_responses.make_json_response()

@store.route("/merchant/<string:merchant_id>/store/<string:store_id>/manager/<string:manager_id>/remove", methods=["POST"])
def remove_store_manager(merchant_id, store_id, manager_id):
    """移除商户门店的管理员
    """
    merchant = request.merchant
    store = request.store
    merchant_store_manager.remove_store_manager(merchant=merchant,
            store=store, manager_id=manager_id)

    return base_responses.make_json_response()


@store.route("/combo_meals/<string:merchant_id>", methods=["GET"])
def combo_meals(merchant_id):
    """ 获取门店的套餐列表
    """
    page = request.values.get("page", None)
    size = request.values.get("size", None)
    page = int(page) if page else None
    size = int(size) if size else None
    combo_meals = DiningEventViewObjectHelper().get_combo_meals(merchant_id, page, size)
    result = []
    for combo_meal in combo_meals:
        json_obj = json_format.MessageToDict(combo_meal)
        if json_obj.get("availableTimePeriod") == "":
            json_obj.update({"availableTimePeriod": "套餐券使用时间：00:00 23:59"})
        elif "套餐券" not in json_obj.get("availableTimePeriod"):
            json_obj.update({
                "availableTimePeriod": "%s%s" % ("套餐券使用时间：", json_obj.get("availableTimePeriod"))})
        result.append(json_obj)
    return base_responses.make_json_response({"comboMeals": result})

@store.route("/combo_meal/<string:combo_meal_id>", methods=["GET"])
def combo_meal_by_id(combo_meal_id=None):
    """ 返回combo_meal_id指定的套餐
    """
    combo_meal = ComboMealDataAccessHelper().get_combo_meal_by_id(combo_meal_id)
    if combo_meal:
        combo_meal_json = json_format.MessageToDict(combo_meal, including_default_value_fields=True)
        return base_responses.make_json_response({"comboMeal": combo_meal_json})
    return base_responses.make_json_response()

# 根据 error_codes 内容自定义 Error/Exception
from service import error_codes

class Error(Exception):
    errcode = error_codes.SERVER_ERROR
    errmsg = error_codes.SERVER_ERROR_MSG

    def __init__(self, errcode=None, errmsg=None, err=None, **kargs):
        if errcode:
            self.errcode = errcode
        if errmsg:
            self.errmsg = errmsg
        if err is not None:
            self.errcode = err[0]
            self.errmsg = err[1]
            if kargs:
                self.errmsg = self.errmsg.format(**kargs)
        super(Error, self).__init__(errmsg, None)

    def __str__(self):
        return "错误码: {}, 错误内容: {}".format(self.errcode, self.errmsg)

class ShowError(Exception):
    def __init__(self, message, *args, **kargs):
        super(ShowError, self).__init__(message, None)
        self.message = message
        self.errcode = error_codes.SHOW_MESSAGE[0]
        self.errmsg = error_codes.SHOW_MESSAGE[1]
        errcode = kargs.get("errcode")
        if errcode:
            self.errcode = errcode
        self.errmsg = message

class GroupPurchaseFullError(Error):
    errcode = error_codes.GROUP_PURCHASE_FULL_ERROR[0]
    errmsg = error_codes.GROUP_PURCHASE_FULL_ERROR[1]

class QrcodeInvalid(Error):
    errcode = error_codes.QRCODE_INVALID[0]
    errmsg = error_codes.QRCODE_INVALID[1]

class DishNotEnough(Error):
    def __init__(self, *args, **kargs):
        super(DishNotEnough, self).__init__(
            err=error_codes.DISH_NOT_ENOUGH)
        if kargs.get("name"):
            self.errmsg = self.errmsg.format(kargs.get("name"))

class RequestsTimeoutError(Error):
    def __init__(self, *args, **kargs):
        super(RequestsTimeoutError, self).__init__(err=error_codes.REQUESTS_TIMEOUT)

class AlipayPayError(Error):
    errcode = error_codes.ALIPAY_PREPAY_ERROR
    errmsg = error_codes.ALIPAY_PREPAY_ERROR_MSG

class NotAlipayUser(Error):
    errcode = error_codes.NOT_ALIPAY_USER
    errmsg = error_codes.NOT_ALIPAY_USER_MSG

class UserInfoError(Error):
    errcode = error_codes.USER_INFO_ERROR
    errmsg = error_codes.USER_INFO_ERROR_MSG

class OrderUpdated(Error):
    errcode = error_codes.ORDER_UPDATED
    errmsg = error_codes.ORDER_UPDATED_MSG

class DishAttrSoldOut(Error):
    errcode = error_codes.DISH_ATTR_SOLD_OUT
    errmsg = error_codes.DISH_ATTR_SOLD_OUT_MSG
    def __init__(self, name):
        self.errmsg = self.errmsg.format(name)

class DishSupplyCondimentSoldOut(Error):
    errcode = error_codes.DISH_SUPPLY_CONDIMENT_SOLD_OUT
    errmsg = error_codes.DISH_SUPPLY_CONDIMENT_SOLD_OUT_MSG
    def __init__(self, name):
        self.errmsg = self.errmsg.format(name)

class TransactionNotExists(Error):
    errcode = error_codes.TRANSACTION_NOT_EXISTS
    errmsg = error_codes.TRANSACTION_NOT_EXISTS_MSG

class TransactionStatusError(Error):
    errcode = error_codes.TRANSACTION_STATUS_ERROR
    errmsg = error_codes.TRANSACTION_STATUS_ERROR_MSG

class DishNotExists(Error):
    errcode = error_codes.DISH_NOT_EXISTS
    errmsg = error_codes.DISH_NOT_EXISTS_MSG
    def __init__(self, name):
        self.errmsg = self.errmsg.format(name)
class DishNotInSaleTime(Error):
    errcode = error_codes.DISH_NOT_IN_SALE_TIME
    errmsg = error_codes.DISH_NOT_IN_SALE_TIME_MSG
    def __init__(self, name):
        self.errmsg = self.errmsg.format(name)
class NotEnoughFeeds(Error):
    errcode = error_codes.NOT_ENOUGH_FEEDS
    errmsg = error_codes.NOT_ENOUGH_FEEDS_MSG
class PayMethodNotSupport(Error):
    errcode = error_codes.PAY_METHOD_NOT_SUPPORT
    errmsg = error_codes.PAY_METHOD_NOT_SUPPORT_MSG
class WishListCannotBeFeeding(Error):
    errcode = error_codes.WISHLIST_CANNOT_BE_FEEDING
    errmsg = error_codes.WISHLIST_CANNOT_BE_FEEDING_MSG
class NoSuchWishlist(Error):
    errcode = error_codes.NO_SUCH_WISHLIST
    errmsg = error_codes.NO_SUCH_WISHLIST_MSG
class NotEnableIFeedU(Error):
    errcode = error_codes.NOT_ENABLE_IFEEDU
    errmsg = error_codes.NOT_ENABLE_IFEEDU_MSG
class NotTheBusinessTimes(Error):
    errcode = error_codes.NOT_THE_BUSINESS_TIMES
    errmsg = error_codes.NOT_THE_BUSINESS_TIMES_MSG
class JoinGroupDiningFailed(Error):
    errcode = error_codes.JOIN_GROUP_DINING_FAILED
    errmsg = error_codes.JOIN_GROUP_DINING_FAILED_MSG
class NoShippingFeeSelected(Error):
    errcode = error_codes.NO_SHIPPING_FEE_SELECTED
    errmsg = error_codes.NO_SHIPPING_FEE_SELECTED_MSG
class TooFarAwayToSend(Error):
    errcode = error_codes.TOO_FAR_AWAY_TO_SEND
    errmsg = error_codes.TOO_FAR_AWAY_TO_SEND_MSG
class CannotGetOrderLock(Error):
    errcode = error_codes.CANNOT_GET_ORDER_LOCK
    errmsg = error_codes.CANNOT_GET_ORDER_LOCK_MSG
class ShippingAddressCannotBeEmpty(Error):
    errcode = error_codes.SHIPPING_ADDRESS_CANNOT_BE_EMPTY
    errmsg = error_codes.SHIPPING_ADDRESS_CANNOT_BE_EMPTY_MSG
class DonnotMeetRequirements(Error):
    errcode = error_codes.DO_NOT_MEET_REQUIREMENTS
    errmsg = error_codes.DO_NOT_MEET_REQUIREMENTS_MSG
    def __init__(self, least_cost):
        self.errmsg = self.errmsg.format(least_cost / float(100))
class BuyCouponPackageWrongPaidFee(Error):
    errcode = error_codes.BUY_COUPON_PACKAGE_WRONG_PAID_FEE
    errmsg = error_codes.BUY_COUPON_PACKAGE_WRONG_PAID_FEE_MSG

class WrongCouponOwner(Error):
    errcode = error_codes.WRONG_COUPON_OWNER
    errmsg = error_codes.WRONG_COUPON_OWNER_MSG

class CouponPackageNotFound(Error):
    errcode = error_codes.COUPON_PACKAGE_NOT_FOUND
    errmsg = error_codes.COUPON_PACKAGE_NOT_FOUND_MSG

class CannotUseCouponPackageWithPosDiscount(Error):
    errcode = error_codes.CANNOT_USE_COUPON_PACKAGE_WITH_POS_DISCOUNT
    errmsg = error_codes.CANNOT_USE_COUPON_PACKAGE_WITH_POS_DISCOUNT_MSG

class OrderPosReturn(Error):
    errcode = error_codes.ORDER_POS_RETURN
    errmsg = error_codes.ORDER_POS_RETURN_MSG

class RequestHandling(Error):
    errcode = error_codes.REQUEST_HANDLING
    errmsg = error_codes.REQUEST_HANDLING_MSG

class PayMethodDonnotSupportBusiness(Error):
    errcode = error_codes.PAY_METHOD_DONNOT_SUPPORT_BUSINESS
    errmsg = error_codes.PAY_METHOD_DONNOT_SUPPORT_BUSINESS_MSG

class BusinessNotSupport(Error):
    errcode = error_codes.BUSINESS_NOT_SUPPORT
    errmsg = error_codes.BUSINESS_NOT_SUPPORT_MSG

class RefundNotSupport(Error):
    errcode = error_codes.REFUND_NOT_SUPPORT
    errmsg = error_codes.REFUND_NOT_SUPPORT_MSG

class PaidfeeShouldBiggerThanZero(Error):
    errcode = error_codes.PAYMENT_PAID_FEE_SHOULD_BIGGER_THAN_ZERO
    errmsg = error_codes.PAYMENT_PAID_FEE_SHOULD_BIGGER_THAN_ZERO_MSG

class CouponAlreadyUsed(Error):
    errcode = error_codes.COUPON_ALREADY_USED
    errmsg = error_codes.COUPON_ALREADY_USED_MSG

class NoDiscountTimeRanges(Error):
    errcode = error_codes.NO_DISCOUNT_TIME_RANGES
    errmsg = error_codes.NO_DISCOUNT_TIME_RANGES_MSG

class GroupDiningFeeNotMatched(Error):
    errcode = error_codes.GROUP_DINING_FEE_NOT_MATCHED
    errmsg = error_codes.GROUP_DINING_FEE_NOT_MATCHED_MSG

class KeruyunCreateOrderError(Error):
    errcode = error_codes.KERUYUN_CREATE_ORDER_ERROR
    errmsg = error_codes.KERUYUN_CREATE_ORDER_ERROR_MSG

class KeruyunPayOrderError(Error):
    errmsg = error_codes.KERUYUN_PAY_ORDER_ERROR_MSG
    errcode = error_codes.KERUYUN_PAY_ORDER_ERROR

class DishSoldOut(Error):
    errcode = error_codes.DISH_SOLD_OUT
    errmsg = error_codes.DISH_SOLD_OUT_MSG

    def __init__(self, name):
        self.errmsg = self.errmsg.format(name)

class AddDishFailed(Error):
    errcode = error_codes.ADD_DISH_FAILED
    errmsg = error_codes.ADD_DISH_FAILED_MSG

class CannotAddDishWithSupplyCondiments(Error):
    errcode = error_codes.CANNOT_ADD_DISH_WITH_SUPPLY_CONDIMENTS
    errmsg = error_codes.CANNOT_ADD_DISH_WITH_SUPPLY_CONDIMENTS_MSG

class KeruyunTableNotFound(Error):
    errcode = error_codes.KERUYUN_TABLE_NOT_FOUND
    errmsg = error_codes.KERUYUN_TABLE_NOT_FOUND_MSG

class KeruyunTableInDining(Error):
    errcode = error_codes.KERUYUN_TABLE_IN_DINING
    errmsg = error_codes.KERUYUN_TABLE_IN_DINING_MSG

class KeruyunTableLocked(Error):
    errcode = error_codes.KERUYUN_TABLE_LOCKED
    errmsg = error_codes.KERUYUN_TABLE_LOCKED_MSG

class KeruyunTableNotTakeEffort(Error):
    errcode = error_codes.KERUYUN_TABLE_NOT_TAKE_EFFORT
    errmsg = error_codes.KERUYUN_TABLE_NOT_TAKE_EFFORT_MSG

class KeruyunTableCannotUsed(Error):
    errcode = error_codes.KERUYUN_TABLE_CANNOT_USED
    errmsg = error_codes.KERUYUN_TABLE_CANNOT_USED_MSG

class KeruyunClearTableFailed(Error):
    errcode = error_codes.KERUYUN_CLEAR_TABLE_FAILED
    errmsg = error_codes.KERUYUN_CLEAR_TABLE_FAILED_MSG

class AddDishDonnotSupportComboMeal(Error):
    errcode = error_codes.ADD_DISH_DONNOT_SUPPORT_COMBO_MEAL
    errmsg = error_codes.ADD_DISH_DONNOT_SUPPORT_COMBO_MEAL_MSG

class ParameterError(Error):
    errcode = error_codes.PARAMETER_NOT_ENOUGH
    errmsg = error_codes.PARAMETER_NOT_ENOUGH_MSG

class UserNotFound(Error):
    errcode = error_codes.USER_NOT_FOUND
    errmsg = error_codes.USER_NOT_FOUND_MSG

class TableNotMatched(Error):
    errcode = error_codes.TABLE_NOT_MATCHED
    errmsg = error_codes.TABLE_NOT_MATCHED_MSG

class TableOnlyForPay(Error):
    errcode = error_codes.TABLE_ONLY_FOR_PAY
    errmsg = error_codes.TABLE_ONLY_FOR_PAY_MSG

class OrderAlreadyPaid(Error):
    errcode = error_codes.ORDER_ALREADY_PAID
    errmsg = error_codes.ORDER_ALREADY_PAID_MSG

class OrderNotFound(Error):
    errcode = error_codes.ORDER_NOT_FOUND
    errmsg = error_codes.ORDER_NOT_FOUND_MSG

class PaidfeeBillfeeNotMatch(Error):
    errcode = error_codes.PAIDFEE_BILLFEE_NOT_MATCH
    errmsg = error_codes.PAIDFEE_BILLFEE_NOT_MATCH_MSG

class GroupDiningNotFound(Error):
    errcode = error_codes.DINING_NOT_EXISTS
    errmsg = error_codes.DINING_NOT_EXISTS_MSG

class OrderingServiceNotSupported(Error):
    errcode = error_codes.ORDERING_SERVICE_NOT_SUPPORTED
    errmsg = error_codes.ORDERING_SERVICE_NOT_SUPPORTED_MSG

class MerchantNotEnableDishOrder(Error):
    errcode = error_codes.MERCHANT_NOT_ENABLE_DISH_ORDER
    errmsg = error_codes.MERCHANT_NOT_ENABLE_DISH_ORDER_MSG

# 操作权限错误
class PermissionError(Error):
    errcode = error_codes.PERMISSION_DENIED
    errmsg = error_codes.PERMISSION_DENIED_MSG

# 找不到指定商户错误
class MerchantNotFoundError(Error):
    errcode = error_codes.MERCHANT_NOT_FOUND
    errmsg = error_codes.MERCHANT_NOT_FOUND_MSG

class MerchantStoreNotFoundError(Error):
    errcode = error_codes.MERCHANT_STORE_NOT_FOUND
    errmsg = error_codes.MERCHANT_STORE_NOT_FOUND_MSG

# 创建子商户错误
class CreateSubmerchantError(Error):
    errcode = error_codes.MERCHANT_CREATE_SUBMERCHANT_ERROR
    errmsg = error_codes.MERCHANT_CREATE_SUBMERCHANT_ERROR_MSG

# 未登陆错误
class UnauthorizedError(Error):
    errcode = error_codes.UNAUTHORIZED
    errmsg = error_codes.UNAUTHORIZED_MSG

# 没有相应优惠券错误
class CouponNotFound(Error):
    errcode = error_codes.COUPON_NOT_FOUND
    errmsg = error_codes.COUPON_NOT_FOUND_MSG

# 领取优惠券出错
class AcceptCouponError(Error):
    errcode = error_codes.COUPON_ACCEPT_ERROR
    errmsg = error_codes.COUPON_ACCEPT_ERROR_MSG

# 不支持的优惠券类型
class CouponCategoryNotSupportError(Error):
    errcode = error_codes.COUPON_CATEGORY_NOT_SUPPORT
    errmsg = error_codes.COUPON_CATEGORY_NOT_SUPPORT_MSG

# 不支持优惠券类型的设定
class CouponCategorySpecNotSupportError(Error):
    errcode = error_codes.COUPON_CATEGORY_SPEC_CONFIG_NOT_SUPPORT
    errmsg = error_codes.COUPON_CATEGORY_SPEC_CONFIG_NOT_SUPPORT_MSG

# 找不到优惠券类型的设定
class CouponCategorySpecNotFoundError(Error):
    errcode = error_codes.COUPON_CATEGORY_SPEC_CONFIG_NOT_FOUND
    errmsg = error_codes.COUPON_CATEGORY_SPEC_CONFIG_NOT_FOUND_MSG

class TableHasOrderCreating(Error):
    errcode = error_codes.TABLE_HAS_ORDER_CREATING
    errmsg = error_codes.TABLE_HAS_ORDER_CREATING_MSG


#######################################################
# 商户助手相关 start
#######################################################
class MerchantAssistNoSuchMerchant(Error):
    errcode = error_codes.MERCHANT_ASSIST_NO_SUCH_MERCHANT
    errmsg = error_codes.MERCHANT_ASSIST_NO_SUCH_MERCHANT_MSG


class MerchantAssistNoSuchUser(Error):
    errcode = error_codes.MERCHANT_ASSIST_NO_SUCH_USER
    errmsg = error_codes.MERCHANT_ASSIST_NO_SUCH_USER_MSG


class MerchantAssistPermissionDenied(Error):
    errcode = error_codes.MERCHANT_ASSIST_UPDATE_PERMISSION_DENIED
    errmsg = error_codes.MERCHANT_ASSIST_UPDATE_PERMISSION_DENIED_MSG


class CannotRemoveYourself(Error):
    errcode = error_codes.MERCHANT_ASSIST_CANNOT_REMOVE_YOURSELF
    errmsg = error_codes.MERCHANT_ASSIST_CANNOT_REMOVE_YOURSELF_MSG


class CannotRefundTransaction(Error):
    errcode = error_codes.MERCHANT_ASSIST_CANNOT_REFUND_TRANSACTION
    errmsg = error_codes.MERCHANT_ASSIST_CANNOT_REFUND_TRANSACTION_MSG
#######################################################
# 商户助手相关 end
#######################################################


#######################################################
# 饭票相关 start
#######################################################


class FanpiaoPayProcessing(Error):
    errcode = error_codes.FANPIAO_PAY_PROCESSING
    errmsg = error_codes.FANPIAO_PAY_PROCESSING_MSG


class FanpiaoNotExists(Error):
    errcode = error_codes.FANPIAO_NOT_EXISTS
    errmsg = error_codes.FANPIAO_NOT_EXISTS_MSG


class FanpiaoNotYours(Error):
    errcode = error_codes.FANPIAO_NOT_YOURS
    errmsg = error_codes.FANPIAO_NOT_YOURS_MSG


class CouponPackageNotYours(Error):
    errcode = error_codes.COUPON_PACKAGE_NOT_YOURS
    errmsg = error_codes.COUPON_PACKAGE_NOT_YOURS_MSG


class FanpiaoNotEnoughMoney(Error):
    errcode = error_codes.FANPIAO_NOT_ENOUGH_MONEY
    errmsg = error_codes.FANPIAO_NOT_ENOUGH_MONEY_MSG


class CannotUseFanpiaoWithPosDiscount(Error):
    errcode = error_codes.CANNOT_USE_FANPIAO_WITH_POS_DISCOUNT
    errmsg = error_codes.CANNOT_USE_FANPIAO_WITH_POS_DISCOUNT_MSG


class CannotUseFanpiaoWithCoupon(Error):
    errcode = error_codes.CANNOT_USE_FANPIAO_WITH_COUPON
    errmsg = error_codes.CANNOT_USE_FANPIAO_WITH_COUPON_MSG


class CannotUseKeruyunMemberCardWithCoupon(Error):
    errcode = error_codes.CANNOT_USE_KERUYUN_MEMBER_CARD_WITH_COUPON
    errmsg = error_codes.CANNOT_USE_KERUYUN_MEMBER_CARD_WITH_COUPON_MSG


class CannotUseShilaiMemberCardPayWithCoupon(Error):
    errcode = error_codes.CANNOT_USE_SHILAI_MEMBER_CARD_PAY_WITH_COUPON
    errmsg = error_codes.CANNOT_USE_SHILAI_MEMBER_CARD_PAY_WITH_COUPON_MSG


#######################################################
# 饭票相关end
#######################################################


class CannotFindDishVerificationCode(Error):
    errcode = error_codes.CANNOT_FIND_DISH_VERIFICATION_CODE
    errmsg = error_codes.CANNOT_FIND_DISH_VERIFICATION_CODE_MSG

class DishVerificationCodeVerified(Error):
    errcode = error_codes.DISH_VERIFICATION_CODE_VERIFIED
    errmsg = error_codes.DISH_VERIFICATION_CODE_VERIFIED_MSG

class DonnotChooseDishVerificationCodeDish(Error):
    errcode = error_codes.DONNOT_CHOOSE_DISH_VERIFICATION_CODE_DISH
    errmsg = error_codes.DONNOT_CHOOSE_DISH_VERIFICATION_CODE_DISH_MSG

class CannotCreateKeruyunCustomer(Error):
    errcode = error_codes.CANNOT_CREATE_KERUYUN_CUSTOMER
    errmsg = error_codes.CANNOT_CREATE_KERUYUN_CUSTOMER_MSG


class MemberCardInvalid(Error):
    errcode = error_codes.MEMBER_CARD_INVALID
    errmsg = error_codes.MEMBER_CARD_INVALID_MSG


class MemberCardNotEnoughBalance(Error):
    errcode = error_codes.MEMBER_CARD_NOT_ENOUGH_BALANCE
    errmsg = error_codes.MEMBER_CARD_NOT_ENOUGH_BALANCE_MSG


class RechargeCategoryNotExists(Error):
    errcode = error_codes.RECHARGE_CATEGORY_NOT_EXISTS
    errmsg = error_codes.RECHARGE_CATEGORY_NOT_EXISTS_MSG


class RechargeCategoryAmountNotMatch(Error):
    errcode = error_codes.RECHARGE_CATEGORY_AMOUNT_NOT_MATCH
    errmsg = error_codes.RECHARGE_CATEGORY_AMOUNT_NOT_MATCH_MSG


class RechargeCategorySellPriceNotMatch(Error):
    errcode = error_codes.RECHARGE_CATEGORY_SELL_PRICE_NOT_MATCH
    errmsg = error_codes.RECHARGE_CATEGORY_SELL_PRICE_NOT_MATCH_MSG


class CouponPackageCannotRefund(Error):
    errcode = error_codes.COUPON_PACKAGE_CANNOT_REFUND
    errmsg = error_codes.COUPON_PACKAGE_CANNOT_REFUND_MSG


class FanpiaoCannotRefund(Error):
    errcode = error_codes.FANPIAO_CANNOT_REFUND
    errmsg = error_codes.FANPIAO_CANNOT_REFUND_MSG


class OrderCannotPay(Error):
    errcode = error_codes.ORDER_CANNOT_PAY
    errmsg = error_codes.ORDER_CANNOT_PAY_MSG


class KeruyunCreateOrderTimeout(Error):
    def __init__(self, err):
        self.errcode = err[0]
        self.errmsg = err[1]


class OrderingFailedNeedRetry(Error):
    def __init__(self, err):
        self.errcode = err[0]
        self.errmsg = err[1]


class KeruyunSyncOrderError(Error):
    errcode = error_codes.KERUYUN_SYNC_ORDER_ERROR[0]
    errmsg = error_codes.KERUYUN_SYNC_ORDER_ERROR[1]


class KeruyunBusinessException(Error):
    errcode = error_codes.KERUYUN_BUSINESS_EXCEPTION[0]
    errmsg = error_codes.KERUYUN_BUSINESS_EXCEPTION[1]


class KeruyunDuplicateKeyError(Error):
    errcode = error_codes.KERUYUN_DUPLICATE_KEY_ERROR[0]
    errmsg = error_codes.KERUYUN_DUPLICATE_KEY_ERROR[1]

class ShoppingCardUpdated(Error):
    errcode = error_codes.SHOPPING_CARD_UPDATED[0]
    errmsg = error_codes.SHOPPING_CARD_UPDATED[1]

class ShoppingCardNoNeedUpdate(Error):
    errcode = error_codes.SHOPPING_CARD_NO_NEED_UPDATE[0]
    errmsg = error_codes.SHOPPING_CARD_NO_NEED_UPDATE[1]

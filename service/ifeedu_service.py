# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format
from flask import Blueprint
from flask import request

from business_ops.ifeedu_manager import IFeedUManager
from business_ops.ifeedu.feed_manager import get_poster
from common.utils import requests_utils
from dao.ifeedu_da_helper import IFeedUDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service import base_responses

ifeedu = Blueprint(__name__, __name__, url_prefix='/ifeedu')

logger = logging.getLogger(__name__)


@ifeedu.route('/dish_list/<string:merchant_id>')
def dish_list(merchant_id):
    config = IFeedUManager(merchant_id=merchant_id).get_config()
    if config:
        config = json_format.MessageToDict(config, including_default_value_fields=True)
        return base_responses.jsonify_response(config)
    return base_responses.jsonify_response()


@ifeedu.route('/init/<string:merchant_id>')
def init(merchant_id):
    IFeedUManager(merchant_id=merchant_id).init(merchant_id)
    return base_responses.jsonify_response()


@ifeedu.route('/create_wishlist/<string:merchant_id>', methods=["POST"])
def create_wishlist(merchant_id):
    dish_ids = request.json.get("dishIds")
    inviter_id = request.json.get("inviterId")
    user_id = requests_utils.get_headers_info(request, "userId")
    wishlist = IFeedUManager(merchant_id=merchant_id).create_wishlist(user_id, inviter_id, dish_ids)
    return base_responses.jsonify_response({"wishlistId": wishlist.id})


@ifeedu.route('/wishlist_detail/<string:wishlist_id>', methods=["GET"])
def wishlist_detail(wishlist_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    wishlist_detail = IFeedUManager(wishlist_id=wishlist_id, user_id=user_id).get_wishlist_detail()
    if wishlist_detail:
        wishlist_detail = json_format.MessageToDict(wishlist_detail, including_default_value_fields=True)
        return base_responses.jsonify_response(wishlist_detail)
    return base_responses.jsonify_response()


@ifeedu.route("/<string:user_id>/poster/<string:wishlist_id>")
def route_get_poster(user_id, wishlist_id):
    wishlist = IFeedUDataAccessHelper().get_wishlist_by_id(wishlist_id=wishlist_id)
    poster = get_poster(wishlist)
    return poster


@ifeedu.route("/wishlist_feeding_ranklist/<string:wishlist_id>", methods=["GET"])
def get_wishlist_feeding_ranklist(wishlist_id):
    ranklist = IFeedUManager(wishlist_id=wishlist_id).get_wishlist_feeding_ranklist()
    ranklist = [json_format.MessageToDict(rank, including_default_value_fields=True) for rank in ranklist]
    return base_responses.jsonify_response(ranklist)


@ifeedu.route("/popularity_ranklist/<string:merchant_id>", methods=["GET"])
def popularity_ranklist(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    user = UserDataAccessHelper().get_user(user_id)
    initiator_id = request.args.get('initiatorId', None)
    ranklist = []
    if not user:
        return base_responses.jsonify_response(ranklist)
    ranklist = IFeedUManager(merchant_id=merchant_id).get_popularity_ranklist(initiator_id)
    ranklist = [json_format.MessageToDict(rank, including_default_value_fields=True) for rank in ranklist]
    return base_responses.jsonify_response(ranklist)


@ifeedu.route('/my_feed_stats', methods=["GET"])
def mywishlist():
    user_id = requests_utils.get_headers_info(request, "userId")
    mywishlists = IFeedUManager(user_id=user_id, check_open_feed=False).get_my_wishlist()
    mywishlists = [json_format.MessageToDict(wishlist, including_default_value_fields=True) for wishlist in mywishlists]

    # 从protobuf转成字典后,造成float精度损失
    for wishlist in mywishlists:
        for dish in wishlist.get("dishList"):
            dish.update({
                "availableCopies": round(float(dish.get("availableCopies")), 2),
                "totalCopies": round(float(dish.get("totalCopies")), 2),
                "usedCopies": round(float(dish.get("usedCopies")), 2)
            })
    return base_responses.jsonify_response(mywishlists)


@ifeedu.route("/feed_other_stats", methods=["GET"])
def feed_other_stats():
    user_id = requests_utils.get_headers_info(request, "userId")
    feed_other_stats = IFeedUManager(user_id=user_id, check_open_feed=False).get_feed_other_stats()
    feed_other_stats = [
        json_format.MessageToDict(feed_stats, including_default_value_fields=True) for feed_stats in feed_other_stats]
    return base_responses.jsonify_response(feed_other_stats)

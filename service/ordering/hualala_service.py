# -*- coding: utf-8 -*-

import logging
import functools

from flask import Blueprint
from flask import jsonify
from flask import request

from business_ops.ordering.order_manager import OrderManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper

hualala = Blueprint(__name__, __name__, url_prefix='/hualala_callback')

logger = logging.getLogger(__name__)


def hualala_success_return_decorator(fn):
    @functools.wraps(fn)
    def inner(*args, **kargs):
        try:
            fn(*args, **kargs)
        except Exception as ex:
            logger.exception("调用接口出错: {}".format(ex))
        return jsonify(code="000")
    return inner


@hualala.route("/notify/orderStatus", methods=["POST"])
@hualala_success_return_decorator
def order_status():
    group_id = str(request.json.get("groupId", None))
    shop_id = str(request.json.get("shopId", None))
    body = request.json.get("body", None)
    registration_info = OrderingServiceDataAccessHelper().get_registration_info(hualala_shop_id=shop_id, hualala_group_id=group_id)
    manager = OrderManager(merchant_id=registration_info.merchant_id, registration_info=registration_info)
    decrypt_data = manager.order_manager.aes_decrypt(body)
    manager.order_status_callback(body=decrypt_data)

# -*- coding: utf-8 -*-

from flask import Blueprint
from flask import request

from google.protobuf import json_format

from business_ops.ordering.dish_attr_manager import DishAttrManager
from service.base_responses import jsonify_response


bp_name = "dish_attr"
_dish_attr_service = Blueprint(bp_name, bp_name, url_prefix="/dish/attr")


@_dish_attr_service.route("", methods=["POST"])
def dish_attr():
    """ 商户助手菜品属性的操作
    """
    merchant_id = request.json.get("merchantId", None)
    operation = request.json.get("operation", None)
    manager = DishAttrManager(merchant_id=merchant_id, operation=operation)
    ret = manager.operate(request)
    if ret is None:
        return jsonify_response()
    if isinstance(ret, list):
        ret = [json_format.MessageToDict(r, including_default_value_fields=True)
               for r in ret]
    else:
        ret = json_format.MessageToDict(ret, including_default_value_fields=True)
    return jsonify_response(ret)

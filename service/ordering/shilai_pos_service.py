# -*- coding: utf-8 -*-


""" 时来pos机回调消息
"""

from flask import Blueprint
from flask import request

import service.ordering.dish_service as dish_service
import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service.base_responses import jsonify_response
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

bp_name = "shilai_pos"
_shilai_pos = Blueprint(bp_name, bp_name, url_prefix="/shilai-pos/callback")


@_shilai_pos.route("/categories", methods=["POST"])
def sync_categories():
    """同步整个菜品分类"""
    merchant_id = request.json.get("merchantId", None)
    store_id = request.json.get("storeId", None)
    dish_manager = DishManager(merchant_id=merchant_id, store_id=store_id)
    dish_manager.async_categories()
    return jsonify_response()


@_shilai_pos.route("/dish/update", methods=["POST"])
def dish_update():
    """单个菜品更新"""
    merchant_id = request.json.get("merchantId", None)
    store_id = request.json.get("storeId", None)
    dish_ids = request.json.get("dishIds", None)
    dish_manager = DishManager(merchant_id=merchant_id, store_id=store_id)
    dish_manager.shilai_pos_sync_dishes(dish_ids)
    return jsonify_response()


@_shilai_pos.route('/dish/flush', methods=["POST"])
def flush_dish_in_cache():
    ordering_da = OrderingServiceDataAccessHelper()
    merchant_id = request.json.get("merchantId", None)
    dish_ids = request.json.get("dishIds", None)
    dishes = ordering_da.get_dishes(dish_ids=dish_ids)
    status = 0
    if dishes:
        ordering_da.add_or_update_dishes(dishes=dishes, merchant_id=merchant_id)
        status = 1
    return jsonify_response({"success": status})


@_shilai_pos.route("menu/update", methods=["POST"])
def menu_update():
    """整个菜单更新"""


@_shilai_pos.route("/order/update", methods=["POST"])
def update_order():
    """订单更新"""
    merchant_id = request.json.get("merchantId", None)
    store_id = request.json.get("storeId", None)
    order_id = request.json.get("orderId", None)
    operation = request.json.get("operation", None)
    back_comment = request.json.get("backComment", None)
    back_fee = request.json.get("backFee", 0)
    is_refund = request.json.get("isRefund", False)
    manager = OrderManager(merchant_id=merchant_id, store_id=store_id)
    if operation == manager.SHILAI_POS_ORDER_CREATE:
        manager.pos_order_create(order_id)
        return jsonify_response()
    ordering_da = OrderingServiceDataAccessHelper()
    transaction_da = TransactionDataAccessHelper()
    order = ordering_da.get_order(ordering_service_order_id=order_id)
    if not order:
        return jsonify_response()
    transaction = transaction_da.get_transaction_by_id(order.transaction_id)
    if operation == manager.SHILAI_POS_ORDER_REFUND:
        refund_transaction = manager.pos_return_order(transaction=transaction, order=order)
        order_manager_v2 = OrderManagerV2(
            merchant=manager.merchant, order=order, transactions=[refund_transaction], back_comment=back_comment
        )
        order_manager_v2.update_order_operation_record_refund(type=wallet_pb.Transaction.ORDERING_REFUND)
    elif operation == manager.SHILAI_POS_ORDER_PARTIAL_REFUND:
        return dish_service.partial_back_items(order=order, platform="POS", back_fee=back_fee, is_refund=is_refund)
    elif operation == manager.SHILAI_POS_ORDER_CONFIRM:
        manager.order_confirm(order=order)
        ordering_da.add_or_update_order(order)
    elif operation == manager.SHILAI_POS_ORDER_REFUSE:
        manager.order_refuse(order=order)
        ordering_da.add_or_update_order(order)
    elif operation == manager.SHILAI_POS_ORDER_PAID:
        if order:
            order.status = dish_pb.DishOrder.PAID
            ordering_da.add_or_update_order(order)
            multiy_party_manager = MultiPartyDinningManager(order=order)
            multiy_party_manager.user_info = {
                "id": "",
                "type": "CUSTOMER",
                "avatarLink": ""
            }
            multiy_party_manager.payment_event(type="PAID")
    elif operation == manager.SHILAI_POS_ORDER_ITEM_UPDATE:
        manager.merge_pos_order(order)
    elif operation == manager.SHILAI_POS_ORDER_REVOKE:
        manager.order_revoke(order=order)
        ordering_da.add_or_update_order(order)
    manager.send_order_to_data_center(order=order)
    return jsonify_response()

# -*- coding: utf-8 -*-


from flask import Blueprint
from flask import request

from google.protobuf import json_format

from service.base_responses import jsonify_response
from business_ops.dish_addon_manager import DishAddonManager

bp_name = "dish_addon"
_addon_service = Blueprint(bp_name, bp_name, url_prefix="/dish/addon")


@_addon_service.route("", methods=["POST"])
def dish_addon():
    """ 商户助手菜品加料相关的操作
    """
    operation = request.json.get("operation", None)
    merchant_id = request.json.get("merchantId", None)
    manager = DishAddonManager(merchant_id=merchant_id, operation=operation)
    ret = manager.operate(request)
    if ret is None:
        return jsonify_response()
    if isinstance(ret, list):
        ret = [json_format.MessageToDict(r, including_default_value_fields=True)
               for r in ret]
    else:
        ret = json_format.MessageToDict(ret, including_default_value_fields=True)
    return jsonify_response(ret)

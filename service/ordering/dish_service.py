import logging
import copy

from flask import request
from flask import Blueprint
from flask import redirect
from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.ordering.table_manager import TableManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from business_ops.data_center_helper import DataCenterHelper
from business_ops.shopping_card_manager import ShoppingCardManager
from business_ops.message_manager import MessageManager
from common.utils import requests_utils
from common.utils import date_utils
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from service import base_responses
from service import error_codes, errors
from view_ops.order_list_view_helper import OrderL<PERSON><PERSON>iewHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from proto.ordering import registration_pb2 as registration_pb
from business_ops.ordering import feie_order_manager
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager


logger = logging.getLogger(__name__)
ordering = Blueprint(__name__, __name__)


@ordering.route('/merchant/dish_catalog/<string:merchant_id>', methods=['GET'])
def dish_catalog(merchant_id):
    """获取商户的菜单"""
    user_id = requests_utils.get_headers_info(request, "userId")
    meal_type = request.args.get('mealType', "EAT_IN")
    show_all = request.args.get("showAll", False)
    enable_sale_time = request.args.get("enableSaleTime", False)  # 获取限时售卖时间,实时计算让前端计算
    meal_type_name = meal_type
    if meal_type == '':
        meal_type = "EAT_IN"
    meal_type = dish_pb.DishOrder.MealType.Value(meal_type)

    manager = DishManager(merchant_id=merchant_id, user_id=user_id)

    # dishes = manager.dish_catalog(user_id, meal_type, show_all=show_all, enable_sale_time=enable_sale_time)
    # dishes = json_format.MessageToDict(dishes, including_default_value_fields=True)
    dishes = manager.get_dish_catalog(
        user_id, meal_type, show_all=show_all, merchant_id=merchant_id, enable_sale_time=enable_sale_time
    )

    dish_remain_map = manager.get_dish_remain_quantity_map()
    for category in dishes.get("dishes", []):
        for dish in category.get("dishList", []):
            manager.reflush_dish_remain_quantity(dish, dish_remain_map)

    if merchant_id == "e5650ffe768a45f9aa00504b0421ec06" and meal_type_name == "EAT_IN":
        dishes.update({"tableInfo": {"tableId": "371987849475792896", "tableName": "堂食"}})

    # 对于复杂结构体,including_default_value_fields=True也不会设置默认值
    if dishes.get("packagingBoxConfig") is None:
        dishes.update({"packagingBoxConfig": {}})
    return base_responses.jsonify_response(dishes)


@ordering.route("/merchant/dish_category_list/<string:merchant_id>", methods=["GET"])
def get_dish_category_of_merchant(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    result = manager.get_category_of_merchant()
    return base_responses.jsonify_response(result)


@ordering.route('/merchant/table_info/<string:table_id>', methods=["GET"])
def get_table_info(table_id):
    """由于支付宝二维码生成时使用的客如云的桌台ID.有变化的可能.所以增加此接口.供支付宝调用来获取实际对应的桌台信息"""
    ordering_da = OrderingServiceDataAccessHelper()
    real_table_id = ordering_da.get_mapping_table(table_id)
    logger.info('table_id: {} to real_table_id: {}'.format(table_id, real_table_id))
    if real_table_id:
        table_id = real_table_id
    table = ordering_da.get_table(ordering_service_table_id=str(table_id))
    if table:
        meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)
        result = {
            "tableName": table.name,
            "tableId": table.ordering_service_table_id,
            "merchantId": table.merchant_id,
            "isTakeAway": table.is_take_away,
            "mealType": meal_type,
            "id": table.id,
        }
        return base_responses.jsonify_response(result)
    return base_responses.jsonify_response()


@ordering.route('/merchant/dish_catalog/scene/<string:scene_id>', methods=['GET'])
def dish_catalog_by_scene_id(scene_id):
    """通过扫二维码"""
    user_id = requests_utils.get_headers_info(request, "userId")
    table_id = scene_id

    # 商户桌贴的scene_id是桌台id
    # 其它的id则是ordering_qrcode表中的id字段.
    # 如果扫码的时候能查到桌台则说明是从商户桌贴扫码进入
    table = OrderingServiceDataAccessHelper().get_table(id=table_id)
    qr_code = QrcodeDataAccessHelper().get_ordering_wxacode(scene_id)
    if not table:
        table = OrderingServiceDataAccessHelper().get_table(qr_code.table_id)

    manager = OrderManager(merchant_id=table.merchant_id)
    if table.meal_type == dish_pb.DishOrder.EAT_IN:
        manager.add_user_to_table_recently_order(user_id, table.ordering_service_table_id)

    meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)
    if manager.registration_info.ordering_config.enable_self_pick_up:
        if table.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)
    if manager.registration_info.ordering_config.enable_take_out:
        if table.meal_type == dish_pb.DishOrder.TAKE_OUT:
            meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)
    if manager.registration_info.ordering_config.enable_take_away:
        if table.meal_type == dish_pb.DishOrder.TAKE_AWAY:
            meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)
    if manager.registration_info.ordering_config.enable_keruyun_fast_food_take_away:
        if table.meal_type == dish_pb.DishOrder.KERUYUN_FAST_FOOD_TAKE_AWAY:
            meal_type = dish_pb.DishOrder.MealType.Name(table.meal_type)

    result = {
        "tableName": table.name,
        "tableId": table.ordering_service_table_id,
        "merchantId": table.merchant_id,
        'groupDiningEventId': "",
        'peopleCount': 0,
        "isTakeAway": table.is_take_away,  # 是否是外带桌
        "mealType": meal_type,
        "onlyForPay": table.only_for_pay,  # 是否是用于付款的桌
        "isSelectTablePay": table.is_select_table_pay,
    }
    return base_responses.jsonify_response(result)


@ordering.route('/merchant/tables/<string:merchant_id>', methods=["GET"])
def get_tables(merchant_id):
    tables = TableManager(merchant_id=merchant_id).get_tables()
    result = json_format.MessageToDict(tables, including_default_value_fields=True)
    return base_responses.jsonify_response(result)


@ordering.route("/order/update/<string:merchant_id>", methods=["POST"])
def update_order(merchant_id):
    order_id = request.json.get("orderId", None)
    release_lock = request.json.get("releaseLock", None)
    status = request.json.get("status", None)
    order_manager = OrderManager(merchant_id=merchant_id)
    order_manager.update_order(order_id=order_id, status=status, release_lock=release_lock)
    return base_responses.jsonify_response()


@ordering.route('/order/create/<string:merchant_id>', methods=['POST'])
def create_order(merchant_id):
    """创建订单"""
    user_id = requests_utils.get_headers_info(request, "userId")
    table_id = request.json.get('tableId', None)
    remark = request.json.get('remark', "")
    people_count = request.json.get('peopleCount', None)
    meal_type = request.json.get('mealType', 'EAT_IN')
    if meal_type == '':
        meal_type = 'EAT_IN'
    # if merchant_id == "e5650ffe768a45f9aa00504b0421ec06" and meal_type == "EAT_IN":
    #     meal_type = "TAKE_AWAY"
    meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
    dish_list = request.json.get("dishList", [])
    phone = request.json.get('phone', '')
    appointment_time = request.json.get('appointmentTime', '')
    shipping_address_id = request.json.get("shippingAddressId")
    shipping_fee = request.json.get("shippingFee", 0)
    package_fee = request.json.get("packageFee", 0)
    force_create = request.json.get("forceCreate", None)
    if meal_type == dish_pb.DishOrder.TAKE_OUT and not shipping_address_id:
        raise errors.ShowError("外卖必须要传地址")
    if shipping_fee == '':
        shipping_fee = 0
    if user_id is None or user_id == "":
        raise errors.UserInfoError()

    manager = OrderManager(merchant_id=merchant_id, user_id=user_id)
    manager.set_repeat_create_order_flag()
    # 解决支付宝二维码错乱的问题
    real_table_id = OrderingServiceDataAccessHelper().get_mapping_table(table_id)
    logger.info('table_id: {} to real_table_id: {}'.format(table_id, real_table_id))
    if real_table_id:
        table_id = real_table_id

    # shopping_card_manager = ShoppingCardManager(merchant=manager.merchant)
    # shopping_card = shopping_card_manager.clear_shopping_card(
    #     table_id=table_id, message_code=MessageManager.SHOPPING_CARD_CLEAR
    # )

    # 创建时来order
    order = manager.create_order(
        table_id,
        dish_list,
        people_count,
        remark,
        meal_type,
        force_create,
        phone,
        appointment_time,
        shipping_address_id,
        package_fee,
        # shopping_card,
    )
    if not order:
        resp = base_responses.create_responses_obj(error_codes.CREATE_ORDER_FAIL, error_codes.CREATE_ORDER_FAIL_MSG)
        return base_responses.jsonify_response(status_response=resp)

    manager.create_ordering_order(order)

    if manager.registration_info.ordering_config.enable_many_people_order:
        multi_party_dinning_manager = MultiPartyDinningManager(order=order)
        multi_party_dinning_manager.create_order(order)

    ret = {"orderId": order.id, 'enableDiscountFee': order.enable_discount_fee, 'billFee': order.bill_fee}
    paid_fee = order.keruyun_take_out_packaging_fee + order.keruyun_take_out_shipping_fee + order.paid_fee
    if paid_fee == 0:
        bill_fee = order.bill_fee
        pay_method = wallet_pb.Transaction.WALLET
        business_manager = SelfDishOrderPaymentManager(
            order=order,
            merchant_id=order.merchant_id,
            paid_fee=paid_fee,
            bill_fee=bill_fee,
            user_id=order.user_id,
            pay_method=pay_method,
        )
        business_manager.prepay()
        ret.update({"transactionId": order.transaction_id})
    logger.info("创建订单: {}".format(ret))
    # try:
    #     shopping_card_manager.send_order_create_message(order)
    # except Exception as ex:
    #     logger.exception(ex)

    return base_responses.jsonify_response(ret)


@ordering.route('/order/get/<string:order_id>', methods=["GET"])
def get_order(order_id):
    """获取订单详情"""
    user_id = requests_utils.get_headers_info(request, "userId")
    ordering_da = OrderingServiceDataAccessHelper()
    order = ordering_da.get_order(id=order_id)
    if order is None:
        order = ordering_da.get_order(transaction_id=order_id)
    if not order:
        # 根据订单ID获取订单,订单有可能是收银机端的订单,所以有可能从数据库查不到订单
        manager = OrderManager()
        order = manager.get_order_v2(order_id, user_id)
        if not order:
            return base_responses.jsonify_response()
        order = json_format.MessageToDict(order, including_default_value_fields=True)
        return base_responses.jsonify_response(order)
    if not order:
        return base_responses.jsonify_response()
    # 从数据库能查询到订单,需要从pos机再同步一次,把最新的订单信息同步下来
    manager = OrderManager(merchant_id=order.merchant_id)
    order = manager.get_order(order_id, user_id, order=order)
    order = json_format.MessageToDict(order, including_default_value_fields=True)
    return base_responses.jsonify_response(order)


@ordering.route("/recently/unpaid-order/<string:merchant_id>", methods=["GET"])
def get_table_recently_unpaid_order(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    table_id = request.args.get('tableId', None)
    manager = OrderManager(merchant_id=merchant_id)
    order = manager.get_recently_unpaid_order(merchant_id=merchant_id, user_id=user_id, table_id=table_id)
    if order:
        order = json_format.MessageToDict(order, including_default_value_fields=True)
        return base_responses.jsonify_response(order)
    return base_responses.jsonify_response()


@ordering.route('/order/recently', methods=["GET"])
def get_recently_order():
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_id = request.args.get('merchantId', None)
    table_id = request.args.get('tableId', None)
    if None in (user_id, merchant_id, table_id):
        return base_responses.jsonify_response()
    manager = OrderManager(merchant_id=merchant_id)
    order = manager.get_user_recently_order(user_id, merchant_id, table_id=table_id)
    if not order:
        return base_responses.jsonify_response()
    order = json_format.MessageToDict(order, including_default_value_fields=False)
    return base_responses.jsonify_response(order)


@ordering.route("/order/recently/table", methods=["GET"])
def get_table_recently_order():
    user_id = requests_utils.get_headers_info(request, "userId")
    merchant_id = request.args.get("merchantId", None)
    table_id = request.args.get("tableId", None)
    manager = OrderManager(merchant_id=merchant_id)
    order = manager.get_recently_order(user_id=user_id, table_id=table_id)
    if not order:
        return base_responses.jsonify_response()
    order_detail = manager.get_order_detail_info(order)
    if not order_detail:
        return base_responses.jsonify_response()
    order = json_format.MessageToDict(order, including_default_value_fields=False)
    return base_responses.jsonify_response(order)


@ordering.route('/order/add_dish', methods=["POST"])
def add_dish():
    """加菜"""
    order_id = request.json.get("orderId")
    dish_list = request.json.get("dishList", None)
    ordering_da = OrderingServiceDataAccessHelper()
    order = ordering_da.get_order(id=order_id)
    if not order:
        raise errors.AddDishFailed()
    manager = OrderManager(merchant_id=order.merchant_id, order=order)
    manager.check_can_add_dish(order)
    manager.add_dish_to_shilai_order(dish_list, order, order.user_id)
    manager.add_dish_ordering_order(dish_list, order, order.user_id)
    ordering_da.add_or_update_order(order)
    try:
        shopping_card_manager = ShoppingCardManager(merchant=manager.merchant)
        shopping_card_manager.clear_shopping_card(table_id=order.table_id, message_code=MessageManager.SHOPPING_CARD_CLEAR)
        shopping_card_manager.send_order_update_message(order)
    except Exception as ex:
        logger.exception(ex)
    return base_responses.jsonify_response()


@ordering.route('/order/get_meal', methods=['POST'])
def get_meal():
    """设置order为己取餐"""
    # user_id = requests_utils.get_headers_info(request, "userId")
    order_id = request.json.get("orderId")
    OrderingServiceDataAccessHelper().update_order(id=order_id, is_get_meal=True)
    return base_responses.jsonify_response()


@ordering.route('/keruyun/order/detail', methods=['GET'])
def get_keruyun_order_detail():
    order_id = request.args.get('orderId', None)
    order = OrderingServiceDataAccessHelper().get_order(id=order_id)
    manager = OrderManager(merchant_id=order.merchant_id)
    keruyun_order = manager.get_order_detail(order.ordering_service_trade_id)
    if not keruyun_order:
        return base_responses.jsonify_response()
    return base_responses.jsonify_response(keruyun_order)


@ordering.route("/keruyun/order/list/<string:merchant_id>", methods=["GET"])
def get_keruyun_order_list(merchant_id):
    manager = OrderManager(merchant_id=merchant_id)
    order_list = manager.get_order_list(1, 100)
    return base_responses.jsonify_response(order_list)


@ordering.route('/order/list', methods=['GET'])
@ordering.route('/order/list/<string:merchant_id>', methods=['GET'])
def get_order_list(merchant_id=None):
    user_id = requests_utils.get_headers_info(request, "userId")
    if user_id is None or user_id == "":
        return base_responses.jsonify_response({"orders": [], "hasMore": False})
    create_time = request.args.get('createTime', date_utils.timestamp_second())
    orders, count = OrderListViewHelper().get_user_ordering_list(user_id, merchant_id, create_time=str(create_time))
    orders = [json_format.MessageToDict(order, including_default_value_fields=True) for order in orders]
    has_more = count > 0
    result = {'orders': orders, 'hasMore': has_more}
    return base_responses.jsonify_response(result)


@ordering.route("/data-center/order/list", methods=["POST"])
def get_order_list_from_data_center():
    uri = "/data-center/order/list"
    helper = DataCenterHelper()
    url = helper.generate_url(uri)
    return redirect(url, code=307)


@ordering.route("/data-center/order/export", methods=["POST"])
def export_order_list_from_data_center():
    uri = "/data-center/order/export"
    helper = DataCenterHelper()
    url = helper.generate_url(uri)
    return redirect(url, code=307)


@ordering.route("/data-center/order", methods=["POST"])
def get_order_from_data_center():
    uri = "/data-center/order"
    helper = DataCenterHelper()
    url = helper.generate_url(uri)
    return redirect(url, code=307)


@ordering.route('/order/discount_plan/<string:merchant_id>', methods=['GET'])
def get_discount_plan(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = DishManager(merchant_id=merchant_id)
    manager.get_discount_plan(user_id, enbale_sale_time_discount=True)

    # merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    # max_discount_update_timestamp = merchant.preferences.coupon_config.max_discount_update_timestamp
    # if manager.discount_plan.update_timestamp < max_discount_update_timestamp:
    #     OrderingServiceDataAccessHelper().remove_discount_plan(merchant_id, user_id)
    #     manager.discount_plan = None
    # manager.get_discount_plan(user_id)

    ret = {'generalUserDiscount': manager.discount_plan.dish_discounts.general_user_discount}
    return base_responses.jsonify_response(ret)


@ordering.route("/order/recommend_dishes/<string:merchant_id>", methods=["GET"])
def get_recommend_dishes(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = DishManager(merchant_id=merchant_id)
    recommend_dish_vo = manager.get_recommend_dishes(user_id)
    return base_responses.jsonify_response(json_format.MessageToDict(recommend_dish_vo, including_default_value_fields=True))


@ordering.route("/order/recommend_dishes/<string:merchant_id>/all", methods=["GET"])
def get_all_recommend_dishes(merchant_id):
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = DishManager(merchant_id=merchant_id)
    recommend_dish_vo = manager.get_all_recommend_dishes(user_id)
    return base_responses.jsonify_response(json_format.MessageToDict(recommend_dish_vo, including_default_value_fields=True))


@ordering.route("/order/recommend_dishes/<string:merchant_id>/update", methods=["POST"])
def update_recommend_dishes(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    dish_ids = request.json.get("dishIds")
    enable_recommend_dish = request.json.get("enableRecommendDish")
    registration_info = manager.update_recommend_dishes(enable_recommend_dish, dish_ids)
    return base_responses.jsonify_response(json_format.MessageToDict(registration_info, including_default_value_fields=True))


@ordering.route("/order/sync_order_info/<string:order_id>", methods=["POST"])
def async_order_info(order_id):
    order = OrderingServiceDataAccessHelper().get_order(id=order_id)
    user_id = request.headers.get("userId", None)
    if order and order.status == dish_pb.DishOrder.APPROVED:
        manager = OrderManager(merchant_id=order.merchant_id)
        manager._merge_order_detail_to_shilai_order(order, user_id=user_id)
    return base_responses.jsonify_response()


@ordering.route("/order/calculate_shipping_fee/<string:merchant_id>", methods=["GET"])
def calculate_shipping_fee(merchant_id):
    """计算配送费"""
    address_id = request.args.get('addressId', None)
    meal_type = request.args.get("mealType", "KERUYUN_TAKE_OUT")
    meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
    manager = OrderManager(merchant_id=merchant_id)
    user_id = requests_utils.get_headers_info(request, "userId")
    shipping_fee = manager.calculate_delivery_fee(user_id, address_id)
    return base_responses.jsonify_response({"shippingFee": shipping_fee})


@ordering.route("/order/bell/<string:merchant_id>", methods=["POST"])
def order_bell(merchant_id):
    table_id = request.json.get("tableId")
    order_id = request.json.get("orderId")
    source = request.json.get("source")
    content = request.json.get("content", "")
    if content == "" or content is None:
        content = " "
    manager = OrderManager(merchant_id=merchant_id)
    ordering_da = OrderingServiceDataAccessHelper()
    table = ordering_da.get_table(ordering_service_table_id=table_id, merchant_id=merchant_id)
    order = ordering_da.get_order(id=order_id)
    manager.pos_order_bell(table, source, content, order)
    return base_responses.jsonify_response()


@ordering.route("/order/open/<string:merchant_id>", methods=["POST"])
def order_open(merchant_id):
    """开台,并创建一个0元订单"""
    user_id = requests_utils.get_headers_info(request, "userId")
    manager = OrderManager(merchant_id=merchant_id, user_id=user_id)
    order = manager.create_zero_order()
    if order:
        return base_responses.jsonify_response({"orderId": order.id})
    return base_responses.jsonify_response()


@ordering.route("/tables/<string:merchant_id>", methods=["GET"])
def get_merchant_tables(merchant_id):
    manager = TableManager(merchant_id=merchant_id)
    tables = manager.get_shilai_tables()
    tables = json_format.MessageToDict(tables, including_default_value_fields=True)
    return base_responses.jsonify_response(tables)


@ordering.route("/required_dishes/<string:merchant_id>", methods=["GET"])
@ordering.route("/pos_required_dishes/<string:merchant_id>", methods=["GET"])
def get_pos_required_dish(merchant_id):
    manager = DishManager(merchant_id=merchant_id)
    required_order_dishes = manager.registration_info.ordering_config.required_order_items
    required_order_dishes = [
        json_format.MessageToDict(dish, including_default_value_fields=True) for dish in required_order_dishes
    ]
    return base_responses.jsonify_response(required_order_dishes)


@ordering.route("/pos_create_order/<string:merchant_id>", methods=["POST"])
def pos_create_order(merchant_id):
    """在pos机上开台"""
    manager = OrderManager(merchant_id=merchant_id)
    dish_list = request.json.get("dishList", [])
    people_count = request.json.get("peopleCount", 1)
    table_id = request.json.get("tableId", None)
    meal_type = dish_pb.DishOrder.EAT_IN
    order = manager.create_order(table_id, dish_list, people_count, meal_type=meal_type)
    manager.create_ordering_order(order)
    return base_responses.jsonify_response()


@ordering.route("/dish/sold-number/<string:merchant_id>", methods=["GET"])
def get_dish_sold_number(merchant_id):
    ordering_da = OrderingServiceDataAccessHelper()
    dishes = ordering_da.get_dishes(merchant_id=merchant_id, return_proto=False)
    ret = {}
    for dish in dishes:
        ret.update({str(dish.get('id')): {"soldNumber": dish.get('soldNumber', 0) + dish.get('soldNumberBaseValue', 0)}})
    return base_responses.jsonify_response(ret)


@ordering.route("/mpqrcode/table", methods=["GET"])
def get_table_id_by_mp_code():
    """通过公众号二维码的后缀来查询对应的桌台信息"""
    mpqrcode = request.args.get("mpqrcode", None)
    ordering_da = OrderingServiceDataAccessHelper()
    mpqrcode_table_id_map = ordering_da.get_mpqrcode_table_map(mpqrcode=mpqrcode)
    if not mpqrcode_table_id_map:
        return base_responses.jsonify_response()
    mpqrcode_table_id_map = json_format.MessageToDict(mpqrcode_table_id_map, including_default_value_fields=True)
    return base_responses.jsonify_response(mpqrcode_table_id_map)


@ordering.route("/patch/print", methods=["POST"])
def patch_print():
    order_id = request.json.get("orderId", None)
    operation = request.json.get("operation", "KITCHEN")
    ordering_da = OrderingServiceDataAccessHelper()
    order = ordering_da.get_order(id=order_id)
    if not order:
        raise errors.ShowError("订单不存在")
    manager = OrderManager(merchant_id=order.merchant_id)
    table = ordering_da.get_table(ordering_service_table_id=str(order.table_id))
    if operation == "KITCHEN":
        manager.patch_kitchen_print(order, table)
    elif operation == "CHECKOUT":
        manager.patch_checkout_print(order, table)
    return base_responses.jsonify_response()


@ordering.route("/union/merchant/table", methods=["POST"])
def get_union_merchant_table():
    table_id = request.json.get("tableId", None)
    qrcode_merchant_id = request.json.get("qrcodeMerchantId", None)
    merchant_id = request.json.get("merchantId", None)

    qrcode_table_manager = TableManager(merchant_id=qrcode_merchant_id)
    table = qrcode_table_manager.get_table_by_id(table_id)
    if not table:
        raise errors.ShowError("桌台不存在")
    table_manager = TableManager(merchant_id=merchant_id)
    table = table_manager.get_table_by_name(name=table.name)
    if not table:
        raise errors.ShowError("桌台不存在")
    table = json_format.MessageToDict(table, including_default_value_fields=True)
    return base_responses.jsonify_response(table)


@ordering.route("/partial/back-items", methods=["POST"])
def partial_back_items(user_id=None, order=None, merchant_id=None, uuids=None, platform=None, back_fee=0, is_refund=False):
    """部分退款"""
    user_id = request.headers.get("userId", user_id)
    order_id = request.json.get("orderId")
    merchant_id = request.json.get("merchantId", merchant_id)
    # {product.uuid: {"quantity": 2}}
    uuids = request.json.get("uuids", uuids)
    back_comment = request.json.get("backComment", None)
    staff_name = request.json.get("staffName", None)
    refund_print = bool(request.json.get("printBackItems", False))
    back_fee = request.json.get("backFee", back_fee)
    is_refund = request.json.get("isRefund", False)
    if not refund_print:
        print_helper = PrinterConfigDataAccessHelper()
        print_configs = print_helper.get_printer_configs(merchant_id=merchant_id)
        refund_print = any(
            [config.kitchen_print_format.refund_print for config in print_configs if config.enable_kitchen_print]
        )
    staff_da = StaffDataAccessHelper()
    staff = staff_da.get_staff(user_id)
    if staff:
        staff_name = staff.name
    order_manager = OrderManagerV2(
        merchant_id=merchant_id,
        user_id=user_id,
        order_id=order_id,
        order=order,
        back_comment=back_comment,
        staff_name=staff_name,
        platform=platform,
    )
    if back_fee > 0 and not order_manager.check_refund_fee():
        raise errors.ShowError("该订单不支持按金额退")
    
    order_manager.partial_refund(uuids, back_fee, is_refund)

    is_feie = lambda t: registration_pb.OrderingServiceRegistrationInfo.PosType.FEIE == t
    # 目前只支持飞鹅模式部分退款打印
    if refund_print and is_feie(order_manager.registration_info.pos_type):
        order_da = OrderingServiceDataAccessHelper()
        table = order_da.get_table(ordering_service_table_id=order_manager.order.table_id)
        printer_manager = feie_order_manager.get_printer_manager(order_manager)
        printer_manager.is_refund = True
        order = copy.deepcopy(order_manager.order)
        while order.products:
            order.products.pop()
        for products in order.back_products:
            order.products.extend(products.products)
        printer_manager.kitchen_print_create_order(order, table)
        printer_manager.print(choice_type="kitchen", print_times=1, refund_print=True)

    order_manager.update_order_operation_record_partial_refund(type=wallet_pb.Transaction.PARTIAL_REFUND)
    data_center_helper = DataCenterHelper(
        order=order_manager.order, order_operation_record=order_manager.order_operation_record
    )
    data_center_helper.send_order_to_data_center()
    return base_responses.jsonify_response()

# -*- coding: utf-8 -*-

import logging
import time
import functools

from flask import Blueprint
from flask import request
from flask import jsonify

import proto.ordering.dish_pb2 as dish_pb
from business_ops.redis_pubsub_manager import RedisPubsubManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.order_manager import OrderManager
from common.utils import id_manager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper

keruyun = Blueprint(__name__, __name__, url_prefix='/keruyun_callback')

logger = logging.getLogger(__name__)


def keruyun_success_return_decorator(fn):
    @functools.wraps(fn)
    def inner(*args, **kargs):
        try:
            fn(*args, **kargs)
        except Exception as ex:
            logger.exception("调用接口出错: {}".format(ex))
        return jsonify(code=0, message="成功[OK]", messageUuid=id_manager.generate_common_id())
    return inner


@keruyun.after_request
def pubsub_message(response):
    order_id = request.json.get("orderId")
    dish_type_ids = request.json.get("dishTypeIds")
    operation = request.json.get('operation')
    shop_identy = request.args.get("shopIdenty")
    dish_shop_ids = request.json.get("dishShopIds")
    dish_brand_ids = request.json.get("dishBrandIds")
    reason = request.json.get("reason")
    trade_id = request.json.get('tradeId')
    deliverer_id = request.json.get("delivereryId")
    rider_name = request.json.get("deliverer")
    rider_phone = request.json.get("delivererPhone")
    cancel_code = request.json.get("cancelCode")
    cancel_reason = request.json.get("cancelReason")
    delivery_status = request.json.get("deliveryStatus")
    timestamp = request.args.get("timestamp")
    kargs = {
        "order_id": order_id,
        "dish_type_ids": dish_type_ids,
        "operation": operation,
        "shop_identy": shop_identy,
        "dish_shop_ids": dish_shop_ids,
        "dish_brand_ids": dish_brand_ids,
        "reason": reason,
        "trade_id": trade_id,
        "deliverer_id": deliverer_id,
        "rider_name": rider_name,
        "rider_phone": rider_phone,
        "cancel_code": cancel_code,
        "cancel_reason": cancel_reason,
        "delivery_status": delivery_status,
        "timestamp": timestamp
    }
    path = request.path
    pubsub_manager = RedisPubsubManager()
    pubsub_manager.publish(path, **kargs)

    return response


@keruyun.route("/order/change_table", methods=["POST"])
@keruyun_success_return_decorator
def change_table():
    order_id = request.json.get("orderId")
    to_table_id = request.json.get("toTableId", None)
    order = OrderingServiceDataAccessHelper().get_order(ordering_service_order_id=order_id)
    if order:
        manager = OrderManager(merchant_id=order.merchant_id)
        manager.change_order_table(order, table_id=to_table_id)


@keruyun.route('/dish/update_category', methods=["POST"])
@keruyun_success_return_decorator
def update_category():
    dish_type_ids = request.json.get("dishTypeIds")
    operation = request.json.get('operation')

    shop_identy = request.args.get("shopIdenty")

    registration_info = OrderingServiceDataAccessHelper().get_registration_info(keruyun_shop_id=str(shop_identy))
    manager = DishManager(registration_info=registration_info, merchant_id=registration_info.merchant_id)

    if operation == 1:
        dish_type_ids = [str(dish_type_id) for dish_type_id in dish_type_ids]
        manager.async_categories(dish_type_ids=dish_type_ids)

    if operation == 2:
        dish_type_ids = [str(dish_type_id) for dish_type_id in dish_type_ids]
        manager.async_categories(dish_type_ids=dish_type_ids)

    if operation == 3:
        for category_id in dish_type_ids:
            manager.change_dish_category_status(category_id, dish_pb.DishCategory.DELETE)

    if operation == 4:
        for category_id in dish_type_ids:
            manager.change_dish_category_status(category_id, dish_pb.DishCategory.ENABLE)

    if operation == 5:
        for category_id in dish_type_ids:
            manager.change_dish_category_status(category_id, dish_pb.DishCategory.DISABLE)


@keruyun.route('/order/payment_notice', methods=["POST"])
@keruyun_success_return_decorator
def payment_notice():
    """ pos机收款通知
    """
    logger.info("######pos payment notice: {}".format(request.json))
    logger.info("######pos payment notice: {}".format(request.args))


@keruyun.route("/order/rejected", methods=["POST"])
@keruyun_success_return_decorator
def reject():
    """ 正餐拒绝
    """
    pass


@keruyun.route("/dish/update", methods=["POST"])
@keruyun_success_return_decorator
def dish_update():
    """ 菜品变更
    """
    logger.info("dish update request.json: {}".format(request.json))
    logger.info("dish update request.args: {}".format(request.args))

    operation = request.json.get("operation")
    dish_shop_ids = request.json.get("dishShopIds")
    dish_brand_ids = request.json.get("dishBrandIds")

    shop_identy = request.args.get("shopIdenty")
    ordering_da = OrderingServiceDataAccessHelper()

    registration_infos = ordering_da.get_registration_infos(keruyun_shop_id=str(shop_identy))
    for registration_info in registration_infos:
        manager = DishManager(registration_info=registration_info, merchant_id=registration_info.merchant_id)

        if not manager.merchant.stores[0].enable_ordering_service:
            continue

        if operation == 9:  # 估清
            for dish_id in dish_shop_ids:
                manager.change_dish_status(str(dish_id), dish_pb.Dish.GUQING)

        if operation == 10:  # 上线
            for dish_id in dish_shop_ids:
                manager.change_dish_status(str(dish_id), dish_pb.Dish.NORMAL)

        if operation == 5:  # 启用
            for dish_brand_id in dish_brand_ids:
                manager.change_brand_dish_status(dish_brand_id, dish_pb.Dish.NORMAL)

        if operation == 6:  # 停用
            for dish_brand_id in dish_brand_ids:
                manager.change_brand_dish_status(dish_brand_id, dish_pb.Dish.DISABLE)

        if operation == 14:  # 新增菜品
            for dish_id in dish_shop_ids:
                manager.sync_dish(dish_id=str(dish_id))
            now = int(time.time())
            MerchantDataAccessHelper().update_merchant(
                merchant_id=registration_info.merchant_id, max_discount_update_timestamp=now)

        if operation == 2:  # 品牌菜品修改
            for dish_brand_id in dish_brand_ids:
                manager.sync_dish(dish_brand_id=str(dish_brand_id))
            now = int(time.time())
            MerchantDataAccessHelper().update_merchant(
                merchant_id=registration_info.merchant_id, max_discount_update_timestamp=now)

        if operation == 3:  # 门店菜修改
            dishes = manager.get_all_dish()
            category_dishes = {}
            for dish in dishes:
                category_dishes.update({str(dish.get("id")): dish})
            if dish_brand_ids:
                for dish_brand_id in dish_brand_ids:
                    manager.sync_dish(dish_brand_id=str(dish_brand_id))
            if dish_shop_ids:
                for dish_id in dish_shop_ids:
                    manager.sync_dish(
                        dish_id=str(dish_id),
                        category_dish=category_dishes.get(str(dish_id)))
            now = int(time.time())
            MerchantDataAccessHelper().update_merchant(
                merchant_id=registration_info.merchant_id, max_discount_update_timestamp=now)

        if operation == 4:  # 品牌删除
            if dish_shop_ids:
                for dish_id in dish_shop_ids:
                    manager.change_dish_status(str(dish_id), dish_pb.Dish.OFFLINE)
            if dish_brand_ids:
                for dish_brand_id in dish_brand_ids:
                    manager.change_brand_dish_status(dish_brand_id, dish_pb.Dish.OFFLINE)

        if operation == 15:  # 门店删除
            for dish_id in dish_shop_ids:
                manager.change_brand_dish_status(dish_brand_id, dish_pb.Dish.OFFLINE)


@keruyun.route("/order/approved", methods=["POST"])
@keruyun_success_return_decorator
def approve():
    pass


@keruyun.route("/order/pos_return_order", methods=["POST"])
@keruyun_success_return_decorator
def pos_return_order():
    """ 退货
    """
    order_id = request.json.get("orderId")
    order = OrderingServiceDataAccessHelper().get_order(ordering_service_order_id=order_id)
    if not order:
        logger.info("订单{}不存在,退款失败".format(order_id))
        return jsonify(code=0, message="成功[OK]", messageUuid=id_manager.generate_common_id())
    if order.status != dish_pb.DishOrder.PAID:
        logger.info("订单{}不是已支付状态,直接返回成功".format(order_id))
        return jsonify(code=0, message="成功[OK]", messageUuid=id_manager.generate_common_id())

    manager = OrderManager(merchant_id=order.merchant_id)
    try:
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_order_refund_redis_callback_key(order)
        if redis_client.setnx(key, 1):
            redis_client.expire(key, 24 * 60 * 60)
            manager.initiate_ordering_refund(order, transaction_id=order.transaction_id)
            logger.info("退款成功: {}".format(order_id))
    except Exception as ex:
        logger.exception("退款失败: {}".format(ex))
        pass


@keruyun.route("/order/status", methods=["POST"])
@keruyun_success_return_decorator
def order_status():
    """ 订单操作类型 1.订单创建；13.订单确认（接受订单)；14.拒绝订单；15.订单取消；16.订单作废；19.退货；20.退款；21.反结账；26.订单完成；91.合单；92挂账；93.支付完成
    """
    trade_id = request.json.get('tradeId')
    order = OrderingServiceDataAccessHelper().get_order(ordering_service_trade_id=str(trade_id))
    if order:
        operation = request.json.get('operation', -1)
        if operation == 15:
            order.status = dish_pb.DishOrder.POS_CANCELLED
            OrderingServiceDataAccessHelper().add_or_update_order(order=order)
        elif operation == 16:
            order.status = dish_pb.DishOrder.POS_INVALID
            OrderingServiceDataAccessHelper().add_or_update_order(order=order)
        elif operation == 93:
            order.status = dish_pb.DishOrder.POS_PAID
            key = CacheServerKeys.get_order_repeat_pay_key(order)
            redis_client = RedisClient().get_connection()
            if redis_client.set(key, 1, ex=6000, nx=True):
                OrderingServiceDataAccessHelper().add_or_update_order(order=order)
        elif operation == 19:
            redis_client = RedisClient().get_connection()
            key = CacheServerKeys.get_order_refund_redis_callback_key(order)
            if redis_client.setnx(key, 1):
                redis_client.expire(key, 24 * 60 * 60)
                manager = OrderManager(merchant_id=order.merchant_id)
                manager.initiate_ordering_refund(order, transaction_id=order.transaction_id)


@keruyun.route("/food_delivery/cancel_order", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_cancel_order():
    """ 客如云取消订单
    """
    pass


@keruyun.route("/food_delivery/accept_order", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_accept_order():
    """ 商家接单
    """
    order_id = request.json.get("orderId")
    ordering_da = OrderingServiceDataAccessHelper()
    order = ordering_da.get_order(ordering_service_order_id=order_id)
    if not order:
        logger.info("找不到订单: {}".format(order_id))
        return jsonify(code=0, message="成功[OK]", messageUuid=id_manager.generate_common_id())
    order.ordering_service_take_out_order_status = dish_pb.DishOrder.ACCEPTED
    ordering_da.add_or_update_order(order=order)


@keruyun.route("/food_delivery/refuse_order", methods=["POST"])
@keruyun_success_return_decorator
def refuse_order():
    """ 外卖客如云拒绝订单
    """
    order_id = request.json.get("orderId")
    reason = request.json.get("reason")


@keruyun.route("/food_delivery/refund_approved", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_refund_approved():
    """ 商家同意外卖退款
    """
    order_id = request.json.get("orderId")


@keruyun.route("/food_delivery/refund_rejected", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_refund_rejected():
    """ 商家拒绝外卖退款
    """
    order_id = request.json.get("orderId")
    reason = request.json.get("reason")


@keruyun.route("/food_delivery/delivery_status", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_delivery_status():
    """ 客如云推送配送状态
    """
    order_id = request.json.get("orderId")
    ordering_da = OrderingServiceDataAccessHelper()
    order = ordering_da.get_order(ordering_service_order_id=order_id)
    deliverer_id = request.json.get("delivereryId")
    rider_name = request.json.get("deliverer")
    rider_phone = request.json.get("delivererPhone")
    cancel_code = request.json.get("cancelCode")
    cancel_reason = request.json.get("cancelReason")
    if order:
        delivery_status = request.json.get("deliveryStatus")
        logger.info("客如云推送配送状态: {}, {}, {}".format(delivery_status, cancel_code, cancel_reason))
        if delivery_status == 1:
            order.shipping_status = dish_pb.DishOrder.WAITING_FOR_ACCEPT
        elif delivery_status == 2:
            order.shipping_status = dish_pb.DishOrder.WAITING_FOR_PICK
        elif delivery_status == 3:
            order.shipping_status = dish_pb.DishOrder.SHIPPING
            order.shipping_info.rider_name = rider_name
            order.shipping_info.rider_phone = rider_phone
        elif delivery_status == 4:
            order.shipping_status = dish_pb.DishOrder.SUCCESS
        elif delivery_status == 5:
            order.shipping_status = dish_pb.DishOrder.CANCELED
        ordering_da.add_or_update_order(order=order)


@keruyun.route("/food_delivery/trash_order", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_trash_order():
    """ 商户作废订单通知
    """
    order_id = request.json.get("orderId")
    reason = request.json.get("reason")


@keruyun.route("/food_delivery/return", methods=["POST"])
@keruyun_success_return_decorator
def food_delivery_return():
    """ 客如云外卖退货通知
    """
    order_id = request.json.get("orderId")
    reason = request.json.get("reason")


@keruyun.route("/delivery/order", methods=["POST"])
@keruyun_success_return_decorator
def delivery_order():
    """ 配送下单接口
    """
    trade_id = request.json.get("orderId")
    order = OrderingServiceDataAccessHelper().get_order(ordering_service_trade_id=str(trade_id))
    if order:
        order.shipping_status = dish_pb.DishOrder.SHIPPING
        OrderingServiceDataAccessHelper().add_or_update_order(order=order)


@keruyun.route("/delivery/cancel_order", methods=["POST"])
@keruyun_success_return_decorator
def delivery_cancel_order():
    """ 配送取消通知
    """
    trade_id = request.json.get("orderId")
    order = OrderingServiceDataAccessHelper().get_order(ordering_service_trade_id=str(trade_id))
    if order:
        order.shipping_status = dish_pb.DishOrder.CANCELED
        OrderingServiceDataAccessHelper().add_or_update_order(order=order)

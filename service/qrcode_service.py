# -*- coding: utf-8 -*-


from flask import Blueprint
from flask import request

from google.protobuf import json_format

from dao.base_helper import BaseHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from service.base_responses import jsonify_response


_qrcode = Blueprint("qrcode", "qrcode", url_prefix="/qrcode-info")


@_qrcode.route("/generate", methods=["POST"])
def generate_qrcode_by_page():
    """ 根据小程序页面生成二维码
    """
    page = request.json.get("page", None)
    merchant_id = request.json.get("merchantId", None)
    store_id = request.json.get("storeId", None)
    # 场景
    scene = request.json.get("scene", None)
    function = request.json.get("function", None)
    name = request.json.get("name", None)
    helper = BaseHelper()
    qrcode = helper.create_wechat_qrcode_by_page(
        page=page, merchant_id=merchant_id, store_id=store_id,
        scene=scene, function=function, name=name)
    qrcode = json_format.MessageToDict(qrcode, including_default_value_fields=True)
    return jsonify_response(qrcode)


@_qrcode.route("/<string:qrcode_id>", methods=["POST"])
def get_qrcode(qrcode_id):
    qrcode_da = QrcodeDataAccessHelper()
    qrcode_info = qrcode_da.get_qrcode(id=qrcode_id)
    qrcode_info = json_format.MessageToDict(qrcode_info, including_default_value_fields=True)
    return jsonify_response(qrcode_info)

# -*- coding: utf-8 -*-
from flask import Flask

from service.auth_service import auth
from service.coupon_category_service import coupon_category
from service.coupon_service import coupon
from service.membership_service import membership
from service.merchant_info_service import merchant_info
from service.merchant_service import merchant
from service.merchant_stats_service import merchant_stats
from service.merchant_store_service import store
from service.misc_service import misc
from service.payment_service import payment
from service.staff_service import staff
from service.transaction_analysis.transaction_analysis_service import transaction_analysis
from service.user_service import user
from service.group_dining_service import group_dining
from service.wallet_service import wallet
from service.wallet_service import vip_membership_bp
from service.red_packet_service import red_packet
from service.transaction_service import transaction
from service.message_service import message
from service.ordering.dish_service import ordering
from service.ordering.keruyun_service import keruyun
from service.ordering.hualala_service import hualala
from service.logistics_service import logistics
from service.ifeedu_service import ifeedu
from service.merchant_assist.merchant_assist_service import merchant_assist
from service.fanpiao_service import _fanpiao
from service.verification_code_service import _verification_code
from service.comment_service import _comment
from service.tian_que_pay_service import _tian_que_pay
from service.staff_assist.merchant_service import merchant_bp
from service.staff_assist.staff_service import staff_bp
from service.permission.ui_permission_service import _ui_permission_bp
from service.hualala_service import _hualala
from service.invite_share_service import _invite_share
from service.config_service import _config
from service.frontend_log_service import _flsbp
from service.activity_service import _activity
from service.ordering.shilai_pos_service import _shilai_pos
from service.qrcode_service import _qrcode
from service.advertising_service import _advertising
from service.shopping_card_service import _shopping_card
from service.ordering.dish_attr_service import _dish_attr_service
from service.code_plate_service import code_plate
from service.handheld_pos_service import _handheld_pos
from service.promotion.coupon.coupon_service import _coupon_v2
from service.promotion.coupon.coupon_template_service import _coupon_template
from service.promotion.group_purchase.group_purchase_invitation_service import _group_purchase_invitation
from service.promotion.group_purchase.group_purchase_template_service import _group_purchase_template
from service.promotion.group_purchase.group_purchase_service import _group_purchase
from service.message_center.message_service import _messag_center
from service.ordering.dish_addon_service import _addon_service
from service.multi_party_dinning_service import multi_party_dinning_bp


BLUEPRINTS = [
    auth, user, _ui_permission_bp, _config, _verification_code, 
    merchant, store, merchant_stats, merchant_info, hualala, _hualala, keruyun, 

    merchant_assist, staff_bp, merchant_bp, staff, 
    misc, payment, _shilai_pos, message, 

    coupon_category, coupon, wallet, red_packet, _advertising, _shopping_card,
    membership, _invite_share, group_dining,  _activity,
    transaction, ordering, logistics, ifeedu, _flsbp,
    _fanpiao, _comment, _tian_que_pay,
    _qrcode, transaction_analysis, _dish_attr_service,
    code_plate, _handheld_pos, _coupon_v2, _coupon_template, _group_purchase,
    _group_purchase_invitation, _group_purchase_template, vip_membership_bp,
    _messag_center, _addon_service,
    multi_party_dinning_bp
]


def init_blueprint(app: Flask):
    from common.base_app import create_app
    return create_app(blueprints=BLUEPRINTS, app=app)

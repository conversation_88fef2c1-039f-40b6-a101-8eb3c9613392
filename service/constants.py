"""后台Web Service相关的配置项与常量值定义。
"""
import os

# Web对外服务域名(用于提供回调URL等)
SERVICE_DOMAIN = os.environ.get("SERVICE_DOMAIN", 'http://test.shilai.zhiyi.cn')

# 微信事件消息接受服务端口
WECHAT_EVENT_HANDLING_SERVICE_ADDRESS = '**********'
WECHAT_EVENT_HANDLING_SERVICE_PORT = 8885
# 中控Access Token服务端口
ACCESS_TOKEN_SERVICE_ADDRESS = '**********'
ACCESS_TOKEN_SERVICE_PORT = 8886
# 后端主体Web Service服务端口
MAIN_WEB_SERVICE_ADDRESS = '**********'
MAIN_WEB_SERVICE_PORT = 8888

# 用户请求来源：来自用户小程序端
PLATFORM_USER = "user"
# 用户请求来源：来自商户小程序端
PLATFORM_MERCHANT = "merchant"
# 用户请求来源：来自时来业务员小程序端
PLATFORM_SHILAI_STAFF = "shilai-staff"

# 时来公众号APPID
SHILAI_MP_APPID = 'wx56a5980f33bee8f9'
# 时来公众号AppSecret
SHILAI_MP_APP_SECRET = '4f3483f03a00f5e5592e458a07c7bed3'

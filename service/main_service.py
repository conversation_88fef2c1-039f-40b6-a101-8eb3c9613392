# -*- encoding: utf-8 -*-
'''
@time        :2024/06/29 11:44:42
'''

from common.config import config
import common.logger.main_service_logger
from service.init_blueprint import BLUEPRINTS
from common.base_app import create_app, print_route


app = create_app(blueprints=BLUEPRINTS)
# print_route(app)


if __name__ == "__main__":

    import argparse
    parser = argparse.ArgumentParser()
    port = parser.add_argument("--port", "-p", help="端口", default=12901, type=int)
    args = parser.parse_args()

    app.config.update(DEBUG=True)
    app.logger.info("-------main server begin start------")
    app.run(host="0.0.0.0", port=args.port)

    # python service/main_service.py -p 12917

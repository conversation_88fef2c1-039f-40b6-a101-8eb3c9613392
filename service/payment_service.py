# -*- coding: utf-8 -*-
import logging
from dicttoxml import dicttoxml

from flask import Blueprint
from flask import request
from flask import jsonify
from flask import Response

from business_ops.payment_manager import PaymentManager
from business_ops.coupon_package_manager import CouponPackageManager
from business_ops.ifeedu_manager import IFeedUManager
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.group_dining_payment import GroupDiningPaymentManager
from business_ops.group_dish_order_payment import GroupDishOrderPaymentManager
from business_ops.group_dining_transfer import GroupDiningTransferManager
from business_ops.group_dish_order_transfer import GroupDishOrderTransferManager
from business_ops.self_dining_discount_payment import SelfDiningDiscountPaymentManager
from business_ops.self_dining_payment import SelfDiningPaymentManager
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.ordering.keruyun_member_manager import KeruyunMemberManager
from business_ops.self_dish_order_payment_with_coupon_package import SelfDishOrderPaymentWithCouponPackageManager
from business_ops.direct_pay_with_coupon_package_purchase import DirectPayWithCouponPackagePurchaseManager
from business_ops.fanpiao_pos_order_manager import FanpiaoPosOrderManager
from business_ops.self_dish_order_payment_with_fanpiao import OrderingFanpiaoManager
from business_ops.shilai_member_manager import ShilaiMemberManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.transaction_manager import TransactionManager
from business_ops.direct_pay_manager import DirectPayManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.self_dish_order_payment_with_open_group_purchase import OrderingWithOpenGroupPurchase
from business_ops.vip_membership_manager import VipMembershipManager
from business_ops.actions import Action
from common.utils import requests_utils
from common.utils import distribute_lock
from common.cache_server_keys import CacheServerKeys
from dao.payment_da_helper import PaymentDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto import payment_pb2 as payment_pb
from proto.ifeedu import common_pb2 as common_pb
from service import errors
from service.base_responses import make_json_response
from service.base_responses import error_responses

logger = logging.getLogger(__name__)

payment = Blueprint('payment', __name__, url_prefix="/payment")


def get_business_manager_for_prepay(transaction_type, **kargs):
    user_id = kargs.get("user_id")
    merchant_id = kargs.get("merchant_id")
    bill_fee = kargs.get("bill_fee")
    paid_fee = kargs.get("paid_fee")
    order_id = kargs.get("order_id")
    pay_method = kargs.get("pay_method")
    discount_fee = kargs.get("discount_fee")
    fanpiao_id = kargs.get("fanpiao_id")
    coupon_id = kargs.get("coupon_id")
    no_discount_bill_fee = kargs.get("no_discount_bill_fee")
    return_url = kargs.get("return_url")
    coupon_package_id = kargs.get("coupon_package_id")
    wishlist_id = kargs.get("wishlist_id")
    feed_items = kargs.get("feed_items")
    item_type = kargs.get("item_type")
    fanpiao_category_id = kargs.get("fanpiao_category_id")
    user_cnt = kargs.get("user_cnt")
    dining_id = kargs.get("dining_id")
    is_invoice = kargs.get("is_invoice")
    recharge_category_id = kargs.get("recharge_category_id")
    is_phone_member_pay = kargs.get("is_phone_member_pay")
    is_from_group = kargs.get("is_from_group")
    other_actions = kargs.get("other_actions")

    if transaction_type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        if coupon_package_id and coupon_package_id != "":
            transaction_type = wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE
    if transaction_type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
        manager = SelfDiningPaymentManager(
            user_id=user_id, merchant_id=merchant_id,
            bill_fee=bill_fee, paid_fee=paid_fee, pay_method=pay_method,
            coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        order_manager_v2 = OrderManagerV2(order_id=order_id)
        manager = SelfDishOrderPaymentManager(
            user_id=user_id, merchant_id=merchant_id, order=order_manager_v2.order,
            no_discount_bill_fee=no_discount_bill_fee, is_invoice=is_invoice,
            bill_fee=bill_fee, paid_fee=paid_fee, pay_method=pay_method,
            is_phone_member_pay=is_phone_member_pay, order_manager_v2=order_manager_v2,
            coupon_id=coupon_id, return_url=return_url, discount_fee=discount_fee,
            fanpiao_id=fanpiao_id, is_from_group=is_from_group,
            coupon_ids=request.json.get('couponIds'),
            is_group_purchase=request.json.get('isGroupPurchase'),
            coin_deduction=request.json.get('coinDeduction')
        )
    elif transaction_type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE:
        manager = SelfDishOrderPaymentWithCouponPackageManager(
            user_id=user_id, merchant_id=merchant_id, order_id=order_id,
            is_invoice=is_invoice, bill_fee=bill_fee, paid_fee=paid_fee, pay_method=pay_method,
            coupon_package_id=coupon_package_id, return_url=return_url)
    elif transaction_type == wallet_pb.Transaction.DIRECT_PAY_WITH_COUPON_PACKAGE_PURCHASE:
        manager = DirectPayWithCouponPackagePurchaseManager(
            user_id=user_id, merchant_id=merchant_id, bill_fee=bill_fee, paid_fee=paid_fee,
            pay_method=pay_method, coupon_package_id=coupon_package_id)
    elif transaction_type == wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT:
        manager = SelfDiningDiscountPaymentManager(
            user_id=user_id, merchant_id=merchant_id, order_id=order_id,
            no_discount_bill_fee=no_discount_bill_fee,
            bill_fee=bill_fee, paid_fee=paid_fee, pay_method=pay_method,
            coupon_id=coupon_id, return_url=return_url, discount_fee=discount_fee, fanpiao_id=fanpiao_id)
    elif transaction_type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
        manager = GroupDiningPaymentManager(
            merchant_id=merchant_id, user_id=user_id, pay_method=pay_method,
            bill_fee=bill_fee, paid_fee=paid_fee, dining_id=dining_id,
            user_cnt=user_cnt, order_id=order_id,
            no_discount_bill_fee=no_discount_bill_fee, coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.GROUP_DINING_TRANSFER:
        manager = GroupDiningTransferManager(
            merchant_id=merchant_id, user_id=user_id, pay_method=pay_method,
            bill_fee=bill_fee, paid_fee=paid_fee, dining_id=dining_id,
            user_cnt=user_cnt, order_id=order_id,
            no_discount_bill_fee=no_discount_bill_fee, coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
        manager = GroupDishOrderPaymentManager(
            merchant_id=merchant_id, user_id=user_id, pay_method=pay_method,
            bill_fee=bill_fee, paid_fee=paid_fee, dining_id=dining_id,
            user_cnt=user_cnt, order_id=order_id,
            no_discount_bill_fee=no_discount_bill_fee, coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.GROUP_DISH_ORDER_TRANSFER:
        manager = GroupDishOrderTransferManager(
            merchant_id=merchant_id, user_id=user_id, pay_method=pay_method,
            bill_fee=bill_fee, paid_fee=paid_fee, dining_id=dining_id,
            user_cnt=user_cnt, order_id=order_id,
            no_discount_bill_fee=no_discount_bill_fee, coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
        manager = CouponPackageManager(
            user_id=user_id, paid_fee=paid_fee, coupon_package_id=coupon_package_id,
            merchant_id=merchant_id,
            pay_method=pay_method, return_url=return_url)
    elif transaction_type == wallet_pb.Transaction.FEEDING_PAYMENT:
        manager = IFeedUManager(
            wishlist_id=wishlist_id, feed_items=feed_items, item_type=item_type, user_id=user_id,
            pay_method=pay_method, bill_fee=bill_fee, paid_fee=paid_fee)
    elif transaction_type == wallet_pb.Transaction.FANPIAO_PURCHASE:
        manager = FanpiaoManager(
            pay_method=pay_method, user_id=user_id, merchant_id=merchant_id,
            fanpiao_category_id=fanpiao_category_id, order_id=order_id, other_actions=other_actions)
    elif transaction_type == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_RECHARGE:
        manager = KeruyunMemberManager(
            pay_method=pay_method, user_id=user_id, merchant_id=merchant_id, bill_fee=bill_fee, paid_fee=paid_fee)
    elif transaction_type == wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE:
        manager = ShilaiMemberManager(
            pay_method=pay_method, user_id=user_id, merchant_id=merchant_id, bill_fee=bill_fee, paid_fee=paid_fee,
            recharge_category_id=recharge_category_id)
    elif transaction_type == wallet_pb.Transaction.DIRECT_PAY:
        order_manager_v2 = OrderManagerV2()
        manager = DirectPayManager(
            pay_method=pay_method, user_id=user_id, merchant_id=merchant_id,
            bill_fee=bill_fee,
            paid_fee=paid_fee, order_manager_v2=order_manager_v2, coupon_id=coupon_id)
    elif transaction_type == wallet_pb.Transaction.OPEN_GROUP_PURCHASE:
        group_purchase_template_manager = GroupPurchaseTemplateManager()
        group_purchase_template = group_purchase_template_manager.get_group_purchase_template(
            id=request.json.get('groupPurchaseTemplateId')
        )
        manager = GroupPurchaseManager(
            user_id=user_id,
            merchant_id=merchant_id,
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            wallet_fee=request.json.get("walletFee", 0),
            pay_method=pay_method,
            group_purchase_template=group_purchase_template
        )
    elif transaction_type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_OPEN_GROUP_PURCHASE:
        manager = OrderingWithOpenGroupPurchase(
            user_id=user_id,
            merchant_id=merchant_id,
            group_purchase_template_id=request.json.get('groupPurchaseTemplateId'),
            pay_method=request.json.get('payMethod'),
            bill_fee=request.json.get('billFee'),
            paid_fee=request.json.get('paidFee'),
            order_id=request.json.get('orderId'),
        )
    elif transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE:
        manager = VipMembershipManager(
            user_id=user_id,
            merchant_id=merchant_id,
            pay_method=request.json.get('payMethod'),
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            transaction_type=transaction_type
        )
    elif transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE:
        manager = VipMembershipManager(
            user_id=user_id,
            merchant_id=merchant_id,
            pay_method=request.json.get('payMethod'),
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            recharge_config_id=request.json.get("rechargeConfigId"),
            transaction_type=transaction_type
        )
    elif transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE:
        manager = VipMembershipManager(
            user_id=user_id,
            merchant_id=merchant_id,
            pay_method=request.json.get('payMethod'),
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            recharge_config_id=request.json.get("rechargeConfigId"),
            transaction_type=transaction_type
        )
    else:
        raise errors.BusinessNotSupport()
    return manager


@payment.route('/prepay', methods=['POST'])
def prepay():
    """
    必要参数:
        billFee: 消费金额
        paidFee: 支付金额
        payMethod: 支付方式
        transactionType: 交易类型
    """
    user_id = requests_utils.get_headers_info(request, 'userId')
    # 必要参数如下
    bill_fee = request.json.get('billFee', 0)
    paid_fee = request.json.get('paidFee', 0)
    no_discount_bill_fee = request.json.get('noDiscountBillFee', 0)
    pay_method = request.json.get("payMethod", "WECHAT_PAY")
    if pay_method == "":
        pay_method = "WECHAT_PAY"
    pay_method = wallet_pb.Transaction.PayMethod.Value(pay_method)
    transaction_type = request.json.get("transactionType", "SELF_DINING_PAYMENT")
    transaction_type = wallet_pb.Transaction.TransactionType.Value(transaction_type)
    fanpiao_category_id = request.json.get("fanpiaoCategoryId")
    fanpiao_id = request.json.get("fanpiaoId")
    return_url = request.json.get('returnUrl', None)
    coupon_package_id = request.json.get('couponPackageId')
    discount_fee = request.json.get('discountFee')  # 随机立减金额
    wishlist_id = request.json.get("wishListId")
    feed_items = request.json.get("feedItems")
    item_type = request.json.get("itemType", "FOOD_DISH")
    item_type = common_pb.ItemType.Value(item_type)
    merchant_id = request.json.get('merchantId')
    coupon_id = request.json.get('couponId', '')
    dining_id = request.json.get("groupDiningId")
    user_cnt = request.json.get("userCnt", 1)
    order_id = request.json.get("orderId")
    is_invoice = request.json.get("isInvoice")
    is_from_group = request.json.get("isFromGroup")
    recharge_category_id = request.json.get("rechargeCategoryId")
    is_phone_member_pay = request.json.get("isPhoneMemberPay", False)
    other_actions = request.json.get("otherActions", {})
    if order_id == '':
        order_id = None

    if paid_fee <= 0 and transaction_type not in [
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
            wallet_pb.Transaction.OPEN_GROUP_PURCHASE,
            wallet_pb.Transaction.DIRECT_PAY,
    ]:
        raise errors.PaidfeeShouldBiggerThanZero()

    manager = get_business_manager_for_prepay(
        user_id=user_id,
        bill_fee=bill_fee,
        paid_fee=paid_fee,
        no_discount_bill_fee=no_discount_bill_fee,
        pay_method=pay_method,
        transaction_type=transaction_type,
        fanpiao_category_id=fanpiao_category_id,
        fanpiao_id=fanpiao_id,
        return_url=return_url,
        coupon_package_id=coupon_package_id,
        discount_fee=discount_fee,
        wishlist_id=wishlist_id,
        feed_items=feed_items,
        item_type=item_type,
        merchant_id=merchant_id,
        coupon_id=coupon_id,
        dining_id=dining_id,
        user_cnt=user_cnt,
        order_id=order_id,
        is_invoice=is_invoice,
        recharge_category_id=recharge_category_id,
        is_phone_member_pay=is_phone_member_pay,
        is_from_group=is_from_group,
        other_actions=other_actions
    )

    result = manager.prepay()

    if result:
        return make_json_response(result)
    return make_json_response(status_response=error_responses())


@payment.route('/<string:transaction_id>/success', methods=['POST'])
def handle_payment_success(transaction_id):
    """接收小程序端发送来的支付成功消息。
    """
    transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
    result = PaymentManager(transaction.pay_method).handle_payment_success(transaction_id)
    return make_json_response(result)


def get_business_manager(transaction, retry=True, **kargs):
    order_id = kargs.get("order_id")
    merchant_id = kargs.get("merchant_id")
    fanpiao_category_id = kargs.get("fanpiao_category_id")
    wishlist_id = kargs.get("wishlist_id")
    coupon_package_id = kargs.get("coupon_package_id")
    fanpiao_qrcode_id = kargs.get("fanpiao_qrcode_id")
    transaction_id = kargs.get("transaction_id")
    logger.info(kargs)
    if not transaction and retry:
        transaction_manager = TransactionManager()
        transaction_manager.add_inexistence_transaction(**kargs)
        raise errors.ShowError("transaction_id {} 找不到 稍后重试".format(transaction_id))
    if transaction.state == wallet_pb.Transaction.SUCCESS:
        raise errors.ShowError("transaction: {} 状态已经是 SUCCESS".format(transaction.id))
    if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
        # 个人用券买单
        # 废弃中
        manager = SelfDiningPaymentManager(transaction_id=transaction.id)
    elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        # 扫码点餐
        # 主力军
        order_manager_v2 = OrderManagerV2(order_id=order_id)
        manager = SelfDishOrderPaymentManager(
            transaction_id=transaction.id, order=order_manager_v2.order,
            order_manager_v2=order_manager_v2)
    elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE:
        manager = SelfDishOrderPaymentWithCouponPackageManager(
            transaction=transaction, order_id=order_id, coupon_package_id=coupon_package_id)
    elif transaction.type == wallet_pb.Transaction.DIRECT_PAY_WITH_COUPON_PACKAGE_PURCHASE:
        order_manager_v2 = OrderManagerV2()
        manager = DirectPayWithCouponPackagePurchaseManager(
            transaction=transaction, coupon_package_id=coupon_package_id,
            order_manager_v2=order_manager_v2)
    elif transaction.type == wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT:
        # 扫码买单
        # 从未使用过
        manager = SelfDiningDiscountPaymentManager(transaction_id=transaction.id, order_id=order_id)
    elif transaction.type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
        # 第一版 组局
        # 废弃中
        manager = GroupDiningPaymentManager(transaction_id=transaction.id, order_id=order_id)
    elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
        # 第二版 组局
        # 废弃中
        manager = GroupDishOrderPaymentManager(transaction_id=transaction.id, order_id=order_id)
    elif transaction.type == wallet_pb.Transaction.GROUP_DINING_TRANSFER:
        # 第一版 组局 AA支付给局长
        # 废弃中
        manager = GroupDiningTransferManager(transaction_id=transaction.id)
    elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_TRANSFER:
        # 第二版 组局 AA支付给买单人
        # 废弃中
        manager = GroupDishOrderTransferManager(transaction_id=transaction.id)
    elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
        # 购买券包
        # 主力军
        manager = CouponPackageManager(transaction_id=transaction.id)
    elif transaction.type == wallet_pb.Transaction.FEEDING_PAYMENT:
        # 投喂
        # 待观察
        manager = IFeedUManager(transaction_id=transaction.id, feed_plan_id=order_id, wishlist_id=wishlist_id)
    elif transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
        # 购买饭票
        # 主力军
        if order_id:
            # 扫码点餐饭票聚合支付
            manager = OrderingFanpiaoManager(
                transaction_id=transaction.id, merchant_id=merchant_id, fanpiao_category_id=fanpiao_category_id,
                order_id=order_id)
        elif fanpiao_qrcode_id:
            manager = FanpiaoPosOrderManager(
                transaction_id=transaction.id, merchant_id=merchant_id, fanpiao_category_id=fanpiao_category_id,
                fanpiao_qrcode_id=fanpiao_qrcode_id)
        else:
            manager = FanpiaoManager(
                transaction_id=transaction.id, merchant_id=merchant_id,
                fanpiao_category_id=fanpiao_category_id)

    elif transaction.type == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_RECHARGE:
        # 客如云会员卡充值
        manager = KeruyunMemberManager(transaction_id=transaction.id)
    elif transaction.type == wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE:
        # 时来会员卡充值
        manager = ShilaiMemberManager(transaction_id=transaction.id, user_id=transaction.payer_id)
    elif transaction.type == wallet_pb.Transaction.DIRECT_PAY:
        order_manager_v2 = OrderManagerV2()
        manager = DirectPayManager(transaction=transaction, order_manager_v2=order_manager_v2)
    elif transaction.type == wallet_pb.Transaction.OPEN_GROUP_PURCHASE:
        manager = GroupPurchaseManager(transaction=transaction)
    elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_OPEN_GROUP_PURCHASE:
        manager = OrderingWithOpenGroupPurchase(transaction=transaction)
    elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE:
        manager = VipMembershipManager(transaction=transaction)
    elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE:
        manager = VipMembershipManager(transaction=transaction)
    elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE:
        manager = VipMembershipManager(transaction=transaction)
    else:
        raise errors.BusinessNotSupport()
    return manager


@payment.route("/tian_que_pay/vip_membership_subscribe/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/vip_membership_recharge/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/vip_membership_subscribe_recharge_combine/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/ordering_with_open_group_purchase/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/open_group_purchase/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/direct-pay/coupon-package/<string:transaction_id>/<string:coupon_package_id>", methods=["POST"])
@payment.route("/tian_que_pay/direct-pay/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/self_dining_payment/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/ordering/<string:transaction_id>/<string:order_id>", methods=["POST"])
@payment.route("/tian_que_pay/group_dining_payment/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/group_dish_order_payment/<string:transaction_id>/<string:order_id>", methods=["POST"])
@payment.route("/tian_que_pay/group_dining_transfer/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/group_dish_order_transfer/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/coupon_package_purchase/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/feeding/<string:transaction_id>/<string:wishlist_id>", methods=["POST"])
@payment.route("/tian_que_pay/fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:fanpiao_category_id>", methods=["POST"])
@payment.route("/tian_que_pay/shilai_member_card_recharge/<string:transaction_id>", methods=["POST"])
@payment.route("/tian_que_pay/ordering_with_coupon_package/<string:transaction_id>/<string:order_id>/<string:coupon_package_id>", methods=["POST"])
@payment.route("/tian_que_pay/ordering_with_fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:order_id>/<string:fanpiao_category_id>", methods=["POST"])
@payment.route("/tian_que_pay/fanpiao_purchase/pos_pay/<string:transaction_id>/<string:fanpiao_qrcode_id>/<string:fanpiao_category_id>", methods=["POST"])
def tian_que_pay(transaction_id=None, order_id=None, merchant_id=None, fanpiao_category_id=None, wishlist_id=None, coupon_package_id=None, fanpiao_qrcode_id=None):
    """ 天阙支付回调接口, 接收并处理微信发来的支付结果通知。详见:
        https://pay.weixin.qq.com/wiki/doc/api/jsapi_sl.php?chapter=9_7
    """
    transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
    payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
    callback = payment_manager.notification(request.json)
    with distribute_lock.redislock(callback.wechat_transaction_id, 5000, 0, 0) as lock:
        if not lock:
            logger.warning(f"天阙回调未获取到锁, 其它进程正在处理，url={request.path}, json={request.json}")
            return jsonify(code="success", msg="成功")
        try:
            manager = get_business_manager(
                transaction, order_id=order_id, merchant_id=merchant_id, fanpiao_category_id=fanpiao_category_id,
                wishlist_id=wishlist_id, coupon_package_id=coupon_package_id, transaction_id=transaction_id,
                fanpiao_qrcode_id=fanpiao_qrcode_id)
            payment = payment_pb.WechatPaymentTransactionId()
            payment.wechat_transaction_id = callback.wechat_transaction_id
            PaymentDataAccessHelper().add_payment_transaction_id(payment)
            manager.notification()
            order_manager_v2 = OrderManagerV2()
            Action(manager.transaction, order_manager_v2=order_manager_v2).do_action()
        except (errors.Error, errors.ShowError) as e:
            logger.warning(e)
        except Exception as ex:
            logger.exception(f"天阙回调报错，url={request.path}, json={request.json}", exc_info=ex)
    return jsonify(code="success", msg="成功")


@payment.route("/payment_notification/self_dining_payment/<string:transaction_id>", methods=["POST"])
@payment.route("/payment_notification/ordering/<string:transaction_id>/<string:order_id>", methods=["POST"])
@payment.route("/payment_notification/coupon_package_purchase/<string:transaction_id>", methods=["POST"])
@payment.route("/payment_notification/fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:fanpiao_category_id>", methods=["POST"])
@payment.route("/payment_notification/shilai_member_card_recharge/<string:transaction_id>", methods=["POST"])
@payment.route("/payment_notification/ordering_with_coupon_package/<string:transaction_id>/<string:order_id>/<string:coupon_package_id>", methods=["POST"])
@payment.route("/payment_notification/ordering_with_fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:order_id>/<string:fanpiao_category_id>", methods=["POST"])
def payment_notification(transaction_id=None, order_id=None, merchant_id=None, fanpiao_category_id=None, wishlist_id=None, coupon_package_id=None):
    """ 微信支付回调接口, 接收并处理微信发来的支付结果通知。详见:
        https://pay.weixin.qq.com/wiki/doc/api/jsapi_sl.php?chapter=9_7
    """
    callback = PaymentManager(wallet_pb.Transaction.WECHAT_PAY).notification(request.data)

    transaction = TransactionDataAccessHelper().get_transaction_by_id(callback.transaction_id)
    if not callback.success:
        # 如果支付失败
        TransactionDataAccessHelper().update_state(transaction.id, wallet_pb.Transaction.FAILURE)
        logger.info("微信支付,第三方返回支付失败: {}, {}".format(transaction.id, callback))
        return jsonify(ret_code="0000", ret_msg="交易成功")
    with distribute_lock.redislock(callback.wechat_transaction_id, 5000, 0, 0) as lock:
        if not lock:
            logger.info('微信回调未获取到锁,其它进程正在处理: {}, {}'.format(transaction.id, callback))
            return jsonify(ret_code="0000", ret_msg="交易成功")
        try:
            manager = get_business_manager(
                transaction, order_id=order_id, merchant_id=merchant_id, fanpiao_category_id=fanpiao_category_id,
                wishlist_id=wishlist_id, coupon_package_id=coupon_package_id, transaction_id=transaction_id)
            payment = payment_pb.WechatPaymentTransactionId()
            payment.wechat_transaction_id = callback.wechat_transaction_id
            PaymentDataAccessHelper().add_payment_transaction_id(payment)
            manager.notification()
        except Exception as ex:
            logger.exception(ex)
            logger.info('微信回调出错: {}'.format(callback))
    return Response(callback.resp_xml, mimetype='text/xml')


@payment.route("/alipay/self_dining_payment/<string:transaction_id>", methods=["POST"])
@payment.route("/alipay/ordering/<string:transaction_id>/<string:order_id>", methods=["POST"])
@payment.route("/alipay/group_dining_payment/<string:transaction_id>", methods=["POST"])
@payment.route("/alipay/group_dish_order_payment/<string:transaction_id>/<string:order_id>", methods=["POST"])
@payment.route("/alipay/group_dining_transfer/<string:transaction_id>", methods=["POST"])
@payment.route("/alipay/group_dish_order_transfer/<string:transaction_id>", methods=["POST"])
@payment.route("/alipay/coupon_package_purchase/<string:transaction_id>", methods=["POST"])
@payment.route("/alipay/feeding/<string:transaction_id>/<string:wishlist_id>", methods=["POST"])
@payment.route("/alipay/fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:fanpiao_category_id>", methods=["POST"])
@payment.route("/alipay/ordering_with_coupon_package/<string:transaction_id>/<string:order_id>/<string:coupon_package_id>", methods=["POST"])
@payment.route("/alipay/ordering_with_fanpiao_purchase/<string:transaction_id>/<string:merchant_id>/<string:order_id>/<string:fanpiao_category_id>", methods=["POST"])
def alipay_notification(transaction_id=None, order_id=None, merchant_id=None, fanpiao_category_id=None, wishlist_id=None, coupon_package_id=None):
    """ 支付宝支付
    """
    data = request.form.to_dict()
    if not (data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED")):
        return 'success'
    callback = PaymentManager(wallet_pb.Transaction.ALIPAY).notification(data)
    transaction = TransactionDataAccessHelper().get_transaction_by_id(callback.transaction_id)
    if not callback.success:
        # 如果支付失败
        TransactionDataAccessHelper().update_state(transaction.id, wallet_pb.Transaction.FAILURE)
        logger.info("支付宝支付,第三方返回支付失败: {}, {}".format(transaction.id, callback))
        return 'success'
    with distribute_lock.redislock(callback.trade_no, 5000, 0, 0) as lock:
        if not lock:
            logger.info('未获取到锁,其它进程正在处理,直接返回成功: {}, {}'.format(transaction.id, callback))
            return 'success'
        try:
            manager = get_business_manager(
                transaction, order_id=order_id, merchant_id=merchant_id, fanpiao_category_id=fanpiao_category_id,
                wishlist_id=wishlist_id, coupon_package_id=coupon_package_id, transaction_id=transaction_id)
            payment = payment_pb.WechatPaymentTransactionId()
            payment.wechat_transaction_id = callback.trade_no
            PaymentDataAccessHelper().add_payment_transaction_id(payment)
            manager.notification()
        except Exception as ex:
            logger.exception(ex)
            logger.info('支付宝回调出错: {}'.format(callback))
    return 'success'


def ordering_business_refund_success(payment_manager, transaction, refund_transaction_id):
    """ 扫码点餐退款,回调处理业务逻辑
    """
    order_manager = OrderManager(user_id=transaction.payer_id, merchant_id=transaction.payee_id)
    ordering_refund = order_manager.ordering_refund(transaction, refund_transaction_id=refund_transaction_id)
    if not ordering_refund.flag:
        return
    if transaction.pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
        payment_manager.update_scan_code_order_merchant_transfer_fee(
            ordering_refund.order, ordering_refund.refund_transaction)


def coupon_package_business_refund_success(payment_manager, transaction, refund_transaction_id):
    """ 券包退款,回调处理业务逻辑
    """
    coupon_package_manager = CouponPackageManager(user_id=transaction.payer_id)
    coupon_package_manager.refund_coupon_package_with_callback(transaction=transaction)


def fanpiao_business_refund_success(payment_manager, transaction, refund_transaction_id):
    """ 饭票退款处理业务逻辑.在第三方退款成功后才会进入到此函数.此处只需要修改饭票状态即可
    """
    fanpiao_manager = FanpiaoManager(user_id=transaction.payer_id)
    fanpiao_manager.refund_user_fanpiao_with_callback(transaction=transaction)


def business_refund_success(transaction, refund_transaction_id):
    payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
    if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        ordering_business_refund_success(payment_manager, transaction, refund_transaction_id)
    elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
        coupon_package_business_refund_success(payment_manager, transaction, refund_transaction_id)
    elif transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
        fanpiao_business_refund_success(payment_manager, transaction, refund_transaction_id)


def ordering_business_refund_failed(payment_manager, transaction, refund_transaction_id):
    """ 扫码点餐退款,回调处理业务逻辑
    """
    order_manager = OrderManager(user_id=transaction.payer_id, merchant_id=transaction.payee_id)
    order_manager.ordering_refund_rollback(transaction)


def coupon_package_business_refund_failed(payment_manager, transaction, refund_transaction_id):
    """ 券包退款,回调处理业务逻辑
    """
    coupon_package_manager = CouponPackageManager(user_id=transaction.payer_id)
    coupon_package_manager.rollback_refund_coupon_package(transaction=transaction)


def fanpiao_business_refund_failed(payment_manager, transaction, refund_transaction_id):
    """ 饭票退款处理业务逻辑.在第三方退款成功后才会进入到此函数.此处只需要修改饭票状态即可
    """
    fanpiao_manager = FanpiaoManager(user_id=transaction.payer_id)
    fanpiao_manager.rollback_refund_user_fanpiao(transaction=transaction)


def business_refund_failed(transaction, refund_transaction_id):
    payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
    if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        ordering_business_refund_failed(payment_manager, transaction, refund_transaction_id)
    elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
        coupon_package_business_refund_failed(payment_manager, transaction, refund_transaction_id)
    elif transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
        fanpiao_business_refund_failed(payment_manager, transaction, refund_transaction_id)


@payment.route("/wechat_pay/refund/<string:pay_transaction_id>/<string:business_transaction_id>/<string:refund_transaction_id>", methods=["POST"])
def wechat_pay_refund_callback(pay_transaction_id, business_transaction_id, refund_transaction_id):
    """ 微信原路退款的回调
    1. pay_transaction_id: 唤起微信支付时传过去的transactionId.传给微信支付的out_trade_no
    2. business_transaction_id: 业务transactionId.购买饭票,购买券包,扫码下单时同pay_transaction_id.券包扫码点餐合并支付时不同于pay_transaction_id
    3. refund_transaction_id: 唤起退款时传给微信的out_refund_no
    """
    resp_xml = dicttoxml({
        'return_code': "success",
        "return_msg": "OK"
    }, attr_type=False, custom_root='xml')
    key = CacheServerKeys.get_refund_callback_repeat_key(pay_transaction_id, business_transaction_id, refund_transaction_id)
    with distribute_lock.redislock(key, 15000, 0, 0) as lock:
        if not lock:
            return Response(resp_xml, mimetype="text/xml")
        manager = PaymentManager(wallet_pb.Transaction.WECHAT_PAY)
        refund_callback = manager.refund_notification(request.data)
        transaction_da = TransactionDataAccessHelper()
        transaction = transaction_da.get_transaction_by_id(business_transaction_id)
        if not refund_callback:
            logger.info("退款失败: {}, {}, {}".format(pay_transaction_id, refund_transaction_id, refund_transaction_id))
            business_refund_failed(transaction, refund_transaction_id)
            return Response(resp_xml, mimetype='text/xml')
        if not refund_callback.flag:
            logger.info("退款失败: {}, {}, {}".format(pay_transaction_id, refund_transaction_id, refund_transaction_id))
            business_refund_failed(transaction, refund_transaction_id)
        else:
            logger.info("退款成功: {}, {}, {}".format(pay_transaction_id, refund_transaction_id, refund_transaction_id))
            business_refund_success(transaction, refund_transaction_id)
    return Response(resp_xml, mimetype='text/xml')


@payment.route("/tian_que_pay/refund/<string:pay_transaction_id>/<string:business_transaction_id>/<string:refund_transaction_id>", methods=["POST"])
def tian_que_pay_refund_callback(pay_transaction_id, business_transaction_id, refund_transaction_id):
    """ 天阙支付退款回调地址
    """
    # transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id=business_transaction_id)
    # business_refund(transaction, refund_transaction_id)
    # return jsonify(code="success", msg="成功")
    return jsonify(code="success", msg="成功")


@payment.route("/alipay/refund/<string:pay_transaction_id>/<string:business_transaction_id>/<string:refund_transaction_id>", methods=["POST"])
def alipay_refund_callback(pay_transaction_id, business_transaction_id, refund_transaction_id):
    """ 支付宝原路退款的回调:
        https://opendocs.alipay.com/apis/api_1/alipay.trade.refund
        支付宝原路退款没有回调
    """
    return "success"

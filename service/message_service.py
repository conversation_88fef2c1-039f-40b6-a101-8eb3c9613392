from flask import Blueprint
from flask import request
from google.protobuf import json_format

from business_ops.message_manager import Message<PERSON>anager
from common.utils import requests_utils
from common.utils import date_utils
from common.utils import id_manager
from dao.private_message_da_helper import PrivateMessageDataAccessHelper
from dao.apply_for_group_dining_message_da_helper import ApplyForGroupDiningMessageDataAccessHelper
from dao.invitation_da_helper import InvitationData<PERSON>ccessHelper
from dao.user_da_helper import UserDataAccessHelper
from proto.websocket import common_pb2 as common_pb
from service import base_responses

message = Blueprint("message", __name__, url_prefix="/message")

@message.route("/private_message_notifies", methods=["GET"])
def private_message_notifies():
    """ 消息中心页:
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    if not user_id:
        return base_responses.jsonify_response()
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    create_time = request.args.get('createTime', None)
    messages = MessageManager().get_private_message_notifies(user_id=user_id,
                                                             page=page, size=size)
    messages = [json_format.MessageToDict(message, including_default_value_fields=True) for message in messages]
    return base_responses.jsonify_response(messages)

@message.route("/message_notifies", methods=["GET"])
def message_notifies():
    """ 消息中心页:
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    result = {}

    # 系统消息
    last_system_notification = MessageManager().get_last_system_notification(user_id)
    result.update({
        "systemNotification": {
            "unreadCnt": last_system_notification.count,
            "message": last_system_notification.message,
            "messageDate": last_system_notification.messageDate,
        }
    })

    # 申请消息
    # 1. 现在只有申请加入饭局的消息
    last_apply_for_message = MessageManager().get_last_apply_for_message(user_id)
    result.update({
        "applyForMessage": {
            "unreadCnt": last_apply_for_message.count,
            "message": last_apply_for_message.message,
            "messageDate": last_apply_for_message.messageDate,
        }
    })
    return base_responses.jsonify_response(result)

@message.route("/private_message_list", methods=["GET"])
def private_message_list():
    """ 和某个人的私聊消息列表
    """
    user_id = requests_utils.get_headers_info(request, "userId")
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    chatter_id = request.args.get('chatterId', None)
    create_time = request.args.get('createTime', None)

    id = id_manager.generate_message_id(user_id, chatter_id)
    MessageManager().add_or_update_last_viewed_time(user_id=user_id,
                                                    type=common_pb.PRIVATE_MESSAGE,
                                                    message_id=id,
                                                    last_viewed_time=date_utils.timestamp_second(),
                                                    deleted=False)
    messages = PrivateMessageDataAccessHelper().get_messages(id=id, orderby=[("createTime", -1)],
                                                             page=page, size=size)
    user = UserDataAccessHelper().get_user(user_id=user_id)
    chatter = UserDataAccessHelper().get_user(user_id=chatter_id)
    messages = [json_format.MessageToDict(message) for message in messages]
    for message in messages:
        if message.get("senderId") == user_id:
            message.update({
                "headimgurl": user.member_profile.head_image_url,
                "nickname": user.member_profile.nickname
            })
        else:
            message.update({
                "headimgurl": chatter.member_profile.head_image_url,
                "nickname": chatter.member_profile.nickname
            })
    ret = {"type": "PRIVATE_MESSAGE", "chatMessages": messages}
    return base_responses.jsonify_response(ret)

@message.route("/system_message_list", methods=["GET"])
def system_message_list():
    """ 系统消息列表
    """
    user_id = requests_utils.get_headers_info(request, "userId")    
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    create_time = request.args.get('createTime', None)
    messages = MessageManager().get_system_notification_list(user_id, page=page, size=size, create_time=create_time)
    MessageManager().add_or_update_last_viewed_time(user_id=user_id,
                                                    type=common_pb.SYSTEM_NOTIFICATION,
                                                    last_viewed_time=date_utils.timestamp_second())
    return base_responses.jsonify_response(messages)

@message.route("/apply_for_message_list", methods=["GET"])
def apply_for_message_list():
    user_id = requests_utils.get_headers_info(request, "userId")
    applicant_id = request.args.get("applicantId", None)
    page = request.args.get('page', None)
    size = request.args.get('size', None)
    page = int(page) if page else None
    size = int(size) if size else None
    create_time = request.args.get('createTime', None)

    if applicant_id is not None:
        messages = ApplyForGroupDiningMessageDataAccessHelper().get_messages(applicant_id=user_id,
                                                                             page=page, size=size,
                                                                             orderby=[("applyTime", -1)])
    else:
        messages = ApplyForGroupDiningMessageDataAccessHelper().get_messages(director_id=user_id,
                                                                             page=page, size=size,
                                                                             orderby=[("applyTime", -1)])
    MessageManager().add_or_update_last_viewed_time(user_id=user_id,
                                                    type=common_pb.APPLY_FOR_GROUP_DINING_MESSAGE,
                                                    last_viewed_time=date_utils.timestamp_second())
    ret = []
    for message in messages:
        invitation = InvitationDataAccessHelper().get_invitation(invitee_id=message.applicant_id,
                                                                 dining_id=message.group_dining_event_id)
        message.state = invitation.state
        message = json_format.MessageToDict(message, including_default_value_fields=True)
        ret.append({"type": "APPLY_FOR_GROUP_DINING_MESSAGE", "applyForGroupDining": message})
    return base_responses.jsonify_response(ret)

from business_ops.transaction_manager import TransactionManager
from proto.finance import wallet_pb2 as wallet_pb
from proto.ui import user_pb2 as user_pb


def get_user_saving_statistics(user_id):
    """获取用户节省的相关数据
    """
    type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT,
                 wallet_pb.Transaction.GROUP_DINING_PAYMENT,
                 wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]
    transactions = TransactionManager().get_dining_payment_type_transactions(
        payer_id=user_id, state=wallet_pb.Transaction.SUCCESS, type_list=type_list)
    sum_used_coupons = 0
    sum_saving_amount = 0
    ordering_sum = 0
    for transaction in transactions:
        if transaction.use_coupon_id:
            sum_used_coupons += 1
            sum_saving_amount += int(transaction.bill_fee) - transaction.paid_fee
        if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            ordering_sum += 1

    staticstics = user_pb.UserSaviningStaticstics()
    staticstics.sum_used_coupons = sum_used_coupons
    staticstics.sum_saving_amount = sum_saving_amount
    staticstics.ordering_sum = ordering_sum
    return staticstics

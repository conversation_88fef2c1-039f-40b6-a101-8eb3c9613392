# -*- coding: utf-8 -*-

from business_ops.red_packet_manager import RedPacketManager
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto.group_dining import red_packet_pb2 as red_packet_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb
from proto.ordering import dish_pb2 as dish_pb
from proto.ui import red_packet_pb2 as ui_red_packet_pb
from proto.finance import wallet_pb2 as wallet_pb
from proto.page import red_packet_list_pb2 as red_packet_list_pb
from proto.page import red_packet_info_pb2 as red_packet_info_pb

class RedPacketViewObjectHelper():

    def get_red_packet(self, user_id, red_packet_id=None, dining_id=None, transaction_id=None, order_id=None):
        red_packet = RedPacketDataAccessHelper().get_red_packet(id=red_packet_id, dining_id=dining_id,
                                                                transaction_id=transaction_id, order_id=order_id)
        if red_packet:
            red_packet_vo = red_packet_info_pb.RedPacket()
            red_packet_vo.id = red_packet.id
            if red_packet.issue_scene == red_packet_pb.RedPacket.GROUP_DINING:
                dining = GroupDiningDataAccessHelper().get_dining_by_id(red_packet.dining_event_id)
                merchant = MerchantDataAccessHelper().get_merchant(dining.merchant_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.dining_id = red_packet.dining_event_id
                red_packet_vo.transaction_id = dining.transaction_id
            elif red_packet.issue_scene == red_packet_pb.RedPacket.NEW_MEMBER:
                transaction_id = red_packet.transaction_id
                if transaction_id == '':
                    transaction_id = red_packet.new_member_transaction_id
                transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
                merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.transaction_id = red_packet.new_member_transaction_id
            elif red_packet.issue_scene == red_packet_pb.RedPacket.SCAN_CODE_ORDER:
                order = OrderingServiceDataAccessHelper().get_order(transaction_id=red_packet.transaction_id)
                merchant = MerchantDataAccessHelper().get_merchant(order.merchant_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
            elif red_packet.issue_scene == red_packet_pb.RedPacket.SCAN_CODE_PAY:
                transaction = TransactionDataAccessHelper().get_transaction_by_id(red_packet.transaction_id)
                merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.transaction_id = red_packet.transaction_id
            elif red_packet.issue_scene == red_packet_pb.RedPacket.SCAN_CODE_SELF_DINING_PAYMENT:
                transaction = TransactionDataAccessHelper().get_transaction_by_id(red_packet.transaction_id)
                merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.transaction_id = red_packet.transaction_id

            return red_packet_vo
        return None

    def get_dining_red_packet(self, user_id, dining_id):
        """ 返回饭局己领取红包列表
        Args:
            dining_id: (string)饭局ID
        Return:
            proto.ui.red_packet_pb2.RedPacket结构体
        """
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining_id)
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        red_packet_obj = ui_red_packet_pb.RedPacket()
        if not red_packet:
            return None
        value_assignments = red_packet.value_assignments
        red_packet_obj.id = red_packet.id

        drawn_users = red_packet.drawn_users
        drawn_users = sorted(drawn_users.items(), key=lambda y: y[1], reverse=True)
        user_dao = UserDataAccessHelper()
        for drawn_user, time in drawn_users:
            user = user_dao.get_user(drawn_user)
            u = red_packet_obj.drawn_users.add()
            u.user_id = drawn_user
            u.total_value = value_assignments.get(drawn_user, 0)
            u.nickname = user.member_profile.nickname
            u.headimgurl = user.member_profile.head_image_url
            u.drawn_time = time

        red_packet_obj.dining.CopyFrom(dining)
        red_packet_obj.total_value = red_packet.total_value
        # 当前用户是否能领取红包
        red_packet_obj.can_open = RedPacketManager().can_open_red_packet(user_id, dining)
        return red_packet_obj

    def get_user_red_packet_list_v2(self, user_id, page=None, size=None):
        if page is None and size is None:
            page = 1
            size = 200
        red_packet_da = RedPacketDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        order_da = OrderingServiceDataAccessHelper()
        red_packet_list = red_packet_da.get_red_packets(single_user_id=user_id, orderby=[("createTime", -1)], page=page, size=size)
        ret = []
        for red_packet in red_packet_list:
            order = order_da.get_order(transaction_id=red_packet.transaction_id)
            if not order:
                continue
            red_packet_vo = red_packet_list_pb.RedPacket()
            red_packet_vo.id = red_packet.id
            merchant = merchant_da.get_merchant(order.merchant_id)
            store = merchant.stores[0]
            red_packet_vo.store_name = store.name
            red_packet_vo.logo_url = merchant.basic_info.logo_url
            red_packet_vo.issue_scene = red_packet.issue_scene
            red_packet_vo.create_time = red_packet.create_time
            red_packet_vo.red_packet_value = red_packet.total_value
            red_packet_vo.already_open = red_packet.drawn_users.get(user_id) is not None
            # 红包是否已退回
            red_packet_vo.is_send_back = False
            if order.status == dish_pb.DishOrder.POS_RETURN:
                red_packet_vo.is_send_back = True
            red_packet_vo.status = red_packet.status
            ret.append(red_packet_vo)
        return ret

    def get_user_red_packet_list(self, user_id, page=None, size=None):
        """ 用户红包列表
        """
        if page is None and size is None:
            page = 1
            size = 100
        red_packet_list = RedPacketDataAccessHelper().get_red_packets(user_id=user_id, orderby=[("createTime", -1)], page=page, size=size)
        skip = 0
        if page is not None and size is not None:
            skip = (page - 1) * size
        ret = []
        transaction_da = TransactionDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        dining_da = GroupDiningDataAccessHelper()
        for red_packet in red_packet_list:
            red_packet_vo = red_packet_list_pb.RedPacket()
            red_packet_vo.id = red_packet.id
            if red_packet.issue_scene in [red_packet_pb.RedPacket.NEW_MEMBER,
                                          red_packet_pb.RedPacket.SCAN_CODE_SELF_DINING_PAYMENT]:
                # 新会员红包
                if skip and skip > 0:
                    skip -= 1
                    continue
                transaction = transaction_da.get_transaction_by_id(red_packet.new_member_transaction_id)
                if not transaction:
                    continue
                merchant = merchant_da.get_merchant(transaction.payee_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.issue_scene = red_packet.issue_scene
                red_packet_vo.create_time = red_packet.create_time
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.already_open = red_packet.drawn_users.get(user_id) is not None
                red_packet_vo.status = red_packet.status
            elif red_packet.issue_scene == red_packet_pb.RedPacket.SCAN_CODE_ORDER:
                # 点菜红包
                if skip and skip > 0:
                    skip -= 1
                    continue
                order = OrderingServiceDataAccessHelper().get_order(transaction_id=red_packet.transaction_id)
                if not order:
                    continue
                merchant = merchant_da.get_merchant(order.merchant_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.issue_scene = red_packet.issue_scene
                red_packet_vo.create_time = red_packet.create_time
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.already_open = red_packet.drawn_users.get(user_id) is not None
                # 红包是否已退回
                red_packet_vo.is_send_back = False
                if order.status == dish_pb.DishOrder.POS_RETURN:
                    red_packet_vo.is_send_back = True
                red_packet_vo.status = red_packet.status
            elif red_packet.issue_scene == red_packet_pb.RedPacket.GROUP_DINING:
                # 约饭红包
                dining = dining_da.get_dining_by_id(red_packet.dining_event_id)
                invitation = InvitationDataAccessHelper().get_invitation(user_id=user_id, dining_id=dining.id)
                if not dining or not invitation:
                    continue
                if invitation.monetary_state == group_dining_pb.Invitation.TRANSFER_PENDING:
                    continue
                transaction = TransactionDataAccessHelper().get_transaction_by_id(invitation.transaction_id)
                if dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
                    if not transaction:
                        continue
                    if transaction.state != wallet_pb.Transaction.SUCCESS:
                        continue
                if skip and skip > 0:
                    skip -= 1
                    continue
                merchant = merchant_da.get_merchant(dining.merchant_id)
                store = merchant.stores[0]
                red_packet_vo.store_name = store.name
                red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.issue_scene = red_packet.issue_scene
                red_packet_vo.create_time = red_packet.create_time
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.already_open = red_packet.drawn_users.get(user_id) is not None
                red_packet_vo.dining_id = dining.id
                red_packet_vo.status = red_packet.status
            else:
                if skip and skip > 0:
                    skip -= 1
                    continue
                merchant = merchant_da.get_merchant(red_packet.merchant_id)
                if not merchant or len(merchant.stores) == 0:
                    continue
                if merchant:
                    store = merchant.stores[0]
                    red_packet_vo.store_name = store.name
                    red_packet_vo.logo_url = merchant.basic_info.logo_url
                red_packet_vo.issue_scene = red_packet.issue_scene
                red_packet_vo.create_time = red_packet.create_time
                red_packet_vo.red_packet_value = red_packet.value_assignments.get(user_id)
                red_packet_vo.already_open = red_packet.drawn_users.get(user_id) is not None
                red_packet_vo.status = red_packet.status
            ret.append(red_packet_vo)
            if size:
                size -= 1
                if size == 0:
                    break
        return ret

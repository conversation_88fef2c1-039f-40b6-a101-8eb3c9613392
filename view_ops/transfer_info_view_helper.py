# -*- coding: utf-8 -*-

from business_ops.red_packet_manager import RedPacketManager
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from business_ops.group_dining_manager import GroupDiningManager
from proto.page import transfer_info_pb2 as transfer_info_pb
from proto.finance import wallet_pb2 as wallet_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb


class TransferInfoViewObjectHelper():

    def get_transfer_info(self, dining_id):
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        transaction = TransactionDataAccessHelper().get_transaction_by_id(dining.transaction_id)
        if dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            paid_fee = GroupDiningManager.calculate_transfer_bill_fee(dining_id, transaction)
        else:
            paid_fee = 0

        transfer_info = transfer_info_pb.TransferInfo()
        transfer_info.paid_fee = paid_fee
        invitees = InvitationDataAccessHelper().get_invitations(
            dining_id=dining_id, state=group_dining_pb.Invitation.ACCEPTED)
        red_packet_da = RedPacketDataAccessHelper()
        red_packet_manager = RedPacketManager()
        transaction_da = TransactionDataAccessHelper()
        red_packet = red_packet_da.get_red_packet(transaction_id=transaction.id)
        for invitee in invitees:
            invitee_vo = transfer_info.invitees.add()
            invitee_vo.paid = False
            invitee_vo.can_open_red_packet = False
            if invitee.transaction_id:
                transaction = transaction_da.get_transaction_by_id(invitee.transaction_id)
                if transaction and transaction.state == wallet_pb.Transaction.SUCCESS:
                    invitee_vo.paid = True
            if red_packet:
                invitee_vo.can_open_red_packet = red_packet_manager.can_open_red_packet(invitee.invitee_id, dining)
                value_assignments = red_packet.value_assignments
                if not invitee_vo.can_open_red_packet:
                    invitee_vo.red_packet_value = value_assignments.get(invitee.invitee_id)
                else:
                    invitee_vo.red_packet_value = 0
        return transfer_info

# -*- coding: utf-8 -*-

from business_ops.transaction_manager import TransactionManager
from business_ops.group_dining_manager import GroupDiningManager
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto.page import order_list_pb2 as order_list_pb
from proto.finance import wallet_pb2 as wallet_pb
from proto import coupon_category_pb2 as coupon_category_pb
from proto.ordering import dish_pb2 as dish_pb


class OrderListViewHelper():
    def get_group_dining_order_list(self, user_id=None, state=None,
                                    monetary_state=None, page=None, size=None):
        """ 约饭列表
        Args:
            user_id: (string)
            state: group_dining_pb.InvitationState
            monetary_state: group_dining_pb.MonetaryState
            page:
            size:
        Return:
            order_list.GroupDiningOrder
        """
        transaction_ma = TransactionManager()
        user_da = UserDataAccessHelper()
        group_dining_da = GroupDiningDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()
        invitation_da = InvitationDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()

        invitations = invitation_da.get_invitations(invitee_id=user_id, state=state,
                                                    monetary_state=monetary_state, orderby=[('acceptTime', -1)])
        skip = (page - 1) * size
        ret_list = []
        for invitation in invitations:
            dining = group_dining_da.get_dining_by_id(invitation.dining_event_id)
            transaction = transaction_da.get_transaction_by_id(dining.transaction_id)
            if not transaction:
                # 如果饭局没有产生订单
                continue
            if transaction.state != wallet_pb.Transaction.SUCCESS:
                # 如果饭局没有支付完成
                continue
            if skip > 0:
                # 代码层面做分页
                skip -= 1
                continue

            # 计算应该付多少钱
            paid_fee = GroupDiningManager.calculate_transfer_bill_fee(dining.id, transaction)

            merchant = merchant_da.get_merchant(dining.merchant_id)
            store = merchant.stores[0]
            initiator = user_da.get_user(dining.initiator_id)
            order = order_list_pb.GroupDiningOrder()
            order.payment_rule = dining.payment_rule
            order.event_time = dining.event_time
            order.bill_fee = transaction.bill_fee
            order.paid_fee = paid_fee
            if dining.director_id == user_id:  # 如果是局长显示总支付金额
                order.paid_fee = transaction.paid_fee
            order.dining_id = dining.id
            order.store_name = store.name
            order.merchant_logo_url = merchant.basic_info.logo_url
            policy = transaction_ma.calculate_policy(dining, order.bill_fee, dining.user_cnt)
            order.least_cost = 0
            order.reduce_cost = 0
            order.coupon_type = order_list_pb.NO_COUPON
            if policy:
                order.least_cost = policy.least_cost
                order.reduce_cost = policy.reduce_cost
                order.coupon_type = order_list_pb.NORMAL_COUPON

            order.initiator_nickname = initiator.member_profile.nickname
            red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
            order.red_packet_value = 0
            order.red_packet_total_value = 0
            if red_packet:
                order.red_packet_value = red_packet.drawn_users.get(user_id, 0)
                order.red_packet_total_value = red_packet.total_value
            order.monetary_state = invitation.monetary_state
            ret_list.append(order)
        return ret_list

    def get_self_dining_order_list(self, user_id, page=None, size=None):
        """ 单人用餐订单列表
        Args:
            user_id: (string)
            page:
            size:
        Return:
            无
        """
        transaction_da = TransactionDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        coupon_da = CouponDataAccessHelper()
        coupon_category_da = CouponCategoryDataAccessHelper()
        red_packet_da = RedPacketDataAccessHelper()
        multi_types = [
            wallet_pb.Transaction.SELF_DINING_PAYMENT,
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT
        ]
        transactions = transaction_da.get_transactions(
            payer_id=user_id, page=page, size=size, orderby=[('createTime', -1)],
            multi_types=multi_types, state=wallet_pb.Transaction.SUCCESS)
        order_list = []
        for transaction in transactions:
            order = order_list_pb.SelfDiningOrder()
            merchant = merchant_da.get_merchant(transaction.payee_id)
            if not merchant:
                continue
            order.transaction_id = transaction.id
            store = merchant.stores[0]
            order.store_name = store.name
            order.merchant_logo_url = merchant.basic_info.logo_url
            order.event_time = transaction.create_time
            if transaction.paid_time:
                order.event_time = transaction.paid_time
            order.bill_fee = transaction.bill_fee
            order.paid_fee = transaction.paid_fee
            order.coupon_type = order_list_pb.NO_COUPON
            use_coupon_id = transaction.use_coupon_id
            if use_coupon_id:
                coupon = coupon_da.get_coupon_by_id(use_coupon_id)
                coupon_category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
                if coupon_category:
                    order.coupon_type = order_list_pb.NORMAL_COUPON
                    order.least_cost = coupon_category.cash_coupon_spec.least_cost
                    order.reduce_cost = coupon_category.cash_coupon_spec.reduce_cost
                    if coupon_category.issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER and \
                       store.new_member_red_packet_value > 0:
                        order.coupon_type = order_list_pb.NEW_MEMBER_COUPON
                        red_packet = red_packet_da.get_red_packet(transaction_id=transaction.id)
                        if red_packet:
                            order.red_packet_value = red_packet.total_value
                            if not red_packet.drawn_users.get(user_id):
                                order.can_open_red_packet = True
            order_list.append(order)
        return order_list

    def get_user_ordering_list(self, user_id, merchant_id=None, create_time=None):
        """ 用户扫码点餐订单列表
        """
        status = [
            dish_pb.DishOrder.PAID,
            dish_pb.DishOrder.APPROVED,
            dish_pb.DishOrder.POS_RETURN
        ]
        orders = OrderingServiceDataAccessHelper().get_orders(
            user_id=user_id, create_time=create_time, status=status, orderby=[('createTime', -1)], merchant_id=merchant_id, size=50)
        order_list = []
        registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=merchant_id)
        last_create_time = create_time
        for order in orders:
            product = order.products[0]
            dish = OrderingServiceDataAccessHelper().get_dish(merchant_id=order.merchant_id, dish_id=product.id)
            if not dish:
                continue
            merchant = MerchantDataAccessHelper().get_merchant(order.merchant_id)
            product = order.products[0]
            dish = OrderingServiceDataAccessHelper().get_dish(merchant_id=order.merchant_id, dish_id=product.id)
            if not dish:
                continue
            order_vo = order_list_pb.ScanCodeOrder()
            order_vo.id = order.id
            order_vo.create_time = order.create_time
            order_vo.bill_fee = order.bill_fee - order.giving_fee
            order_vo.merchant_id = order.merchant_id
            if len(order.products) == 0:
                continue
            if len(dish.images) > 0:
                order_vo.first_product_image = dish.images[0]
            order_vo.first_product_name = dish.name
            order_vo.first_product_price = product.discount_price
            order_vo.product_count = len(order.products)
            order_vo.meal_code = order.meal_code
            if registration_info:
                order_vo.pay_type = registration_info.pay_type
            order_vo.status = order.status
            order_vo.is_take_away = not (order.meal_code == "")
            order_vo.logo_url = merchant.basic_info.logo_url
            order_vo.store_name = merchant.stores[0].name
            order_vo.meal_type = order.meal_type
            order_list.append(order_vo)
            last_create_time = order.create_time
        count = OrderingServiceDataAccessHelper().count_orders(
            user_id=user_id, merchant_id=merchant_id, create_time=str(last_create_time), status=status)
        return order_list, count

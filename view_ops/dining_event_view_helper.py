# -*- coding: utf-8 -*-

import time
import logging
import traceback
from datetime import datetime

import proto.ordering.keruyun.export_detail_pb2 as export_detail_pb
import proto.config_pb2 as config_pb
import proto.payment_pb2 as payment_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
from business_ops import merchant_store_manager
from business_ops.ordering.order_manager import OrderManager
from business_ops.config_manager import ConfigManager
from business_ops.ordering.shilai_ops_manager import ShilaiPosMerchantManager
from business_ops.app_components_config_manager import AppComponentConfigManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from common.utils import date_utils
from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.payment_da_helper import PaymentDataAccessHelper
from proto.page import dining_event_pb2 as dining_event_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.ordering.dish_pb2 as dish_pb

logger = logging.getLogger(__name__)


class DiningEventViewObjectHelper:

    """就餐页"""

    def get_combo_meals(self, merchant_id, page=None, size=None):
        """套餐列表"""
        combo_meals = ComboMealDataAccessHelper().get_combo_meals(merchant_id, page=page, size=size)
        result = []
        for combo_meal in combo_meals:
            ui_combo_meal = dining_event_pb.ComboMeal()
            ui_combo_meal.id = combo_meal.id
            ui_combo_meal.name = combo_meal.name
            ui_combo_meal.original_price = combo_meal.original_price
            ui_combo_meal.discount_price = combo_meal.discount_price
            ui_combo_meal.available_time_period = combo_meal.available_time_period
            for image_url in combo_meal.image_urls:
                ui_combo_meal.image_urls.append(image_url)  # 简约模式需要一张图片
                break
            result.append(ui_combo_meal)
        return result

    def get_store(self, merchant_id, user_id):
        """门店信息"""
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if not merchant:
            return None
        if len(merchant.stores) == 0:
            return None
        store = merchant.stores[0]
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(merchant_id)
        store_vo = dining_event_pb.Store()
        store_vo.pos_type = registration_info.pos_type
        store_vo.store_id = store.id
        store_vo.merchant_id = merchant.id
        store_vo.logo_url = merchant.basic_info.logo_url
        store_vo.name = store.name
        store_vo.merchant_name = merchant.basic_info.name
        store_vo.address = store.address
        store_vo.phone = store.phone
        store_vo.combo_meal_only = store.combo_meal_only
        store_vo.enable_group_dining = store.enable_group_dining
        store_vo.enable_combo_meal = store.enable_combo_meal
        store_vo.enable_ordering_service = store.enable_ordering_service  # 是否开启点菜功能
        store_vo.enable_invoice = store.enable_invoice  # 是否开启发票
        store_vo.disable_people_count = store.disable_people_count
        store_vo.splash_image_url = store.splash_image_url
        store_vo.enable_comment = store.enable_comment
        store_vo.enable_fanpiao = store.enable_fanpiao
        store_vo.enable_self_dining_discount_payment = store.enable_self_dining_discount_payment
        store_vo.enable_shilai_member_card_pay = store.enable_shilai_member_card_pay  # 是否开启时来会员卡支付
        store_vo.enable_shilai_member_card_recharge = store.enable_shilai_member_card_recharge  # 是否开启时来会员卡充值
        store_vo.disable_payment_reminder = store.disable_payment_reminder
        store_vo.enforce_phone_registration = store.enforce_phone_registration
        store_vo.disable_buy_fanpiao = store.disable_buy_fanpiao
        store_vo.splash_coupon_category_id = store.splash_coupon_category_id
        store_vo.splash_mode = store.splash_mode
        store_vo.enable_remove_required_item = store.enable_remove_required_item
        store_vo.enable_coupon_expiration_reminder = store.enable_coupon_expiration_reminder
        store_vo.enable_ordering_coupon_package_union_pay = store.enable_ordering_coupon_package_union_pay
        store_vo.disable_coupon_package_refund = store.disable_coupon_package_refund
        store_vo.disable_fanpiao_refund = store.disable_fanpiao_refund
        store_vo.enable_invite_share = store.enable_invite_share
        store_vo.package_box_type = store.package_box_type
        store_vo.enable_shilai_promotion_splash = store.enable_shilai_promotion_splash
        store_vo.enable_show_sold_out_dishes = store.enable_show_sold_out_dishes
        store_vo.banner_mode = store.banner_mode
        store_vo.merchant_system_message = merchant.system_message
        store_vo.enterprise_wechat_number = merchant.enterprise_wechat_number
        store_vo.display_advertising = merchant.display_advertising_info.display_advertising
        store_vo.enable_number_plate_pay_with_marketing = merchant.enable_number_plate_pay_with_marketing
        store_vo.enable_number_plate_pay_with_fanpiao = merchant.enable_number_plate_pay_with_fanpiao
        store_vo.enable_number_plate_pay_with_coupon_package = merchant.enable_number_plate_pay_with_coupon_package
        store_vo.enable_number_plate_splash = merchant.enable_number_plate_splash
        store_vo.is_ramen_joint_project = merchant.is_ramen_joint_project
        store_vo.store_mp_image_url = merchant.store_mp_image_url
        store_vo.enable_cancel_pay_broadcast = merchant.enable_cancel_pay_broadcast
        store_vo.disable_number_plate_pay_fanpiao_pay = merchant.disable_number_plate_pay_fanpiao_pay
        store_vo.app_component_config.CopyFrom(self.get_app_component_config(merchant))
        store_vo.disable_display_serial_number_after_ordering = merchant.disable_display_serial_number_after_ordering
        store_vo.coin_deduction_rate = self.get_merchant_coin_deduction_rate(merchant)
        store_vo.enable_coin_deduction = merchant.coin_deduction_coupon_template_id != ""
        store_vo.enable_number_plate_coupon_splash = merchant.enable_number_plate_coupon_splash
        store_vo.enable_table_serial_number = merchant.enable_table_serial_number
        store_vo.disable_show_ad = merchant.disable_show_ad
        store_vo.package_type = registration_info.package_type
        store_vo.operating_status = registration_info.operating_status
        store_vo.is_pre_order = registration_info.is_pre_order
        store_vo.pay_success_ad_config.CopyFrom(store.pay_success_ad_config)

        config_da = ConfigDataAccessHelper()
        fanpiao_config = config_da.get_fanpiao_config(merchant_id=merchant.id)
        coupon_package_conifg = config_da.get_coupon_package_config(merchant_id=merchant.id)
        ordering_config = config_da.get_ordering_config(merchant_id=merchant.id)
        store_vo.fanpiao_sales_boost_factor = 1
        if fanpiao_config is not None:
            store_vo.disable_show_fanpiao_purchase_number = fanpiao_config.disable_show_fanpiao_purchase_number
            store_vo.disable_show_fanpiao_price = fanpiao_config.disable_show_fanpiao_price
            store_vo.fanpiao_sales_boost_factor = fanpiao_config.fanpiao_sales_boost_factor
            if store_vo.fanpiao_sales_boost_factor == 0:
                store_vo.fanpiao_sales_boost_factor = 1
        if coupon_package_conifg is not None:
            store_vo.disable_show_coupon_package_purchase_number = (
                coupon_package_conifg.disable_show_coupon_package_purchase_number
            )
        if ordering_config is not None:
            store_vo.enable_dish_incremental = ordering_config.enable_dish_incremental
            store_vo.enable_show_time_limit_sale = ordering_config.enable_show_time_limit_sale
            store_vo.disable_show_sold_number = ordering_config.disable_show_sold_number
            store_vo.enable_phone_member_pay = ordering_config.enable_phone_member_pay
            store_vo.enable_client_dish_catalog_cache = ordering_config.enable_client_dish_catalog_cache
            store_vo.disable_show_discount_price = ordering_config.disable_show_discount_price
            store_vo.disable_scan_code_pay = ordering_config.disable_scan_code_pay
            store_vo.enable_firstly_addon = ordering_config.enable_firstly_addon

        config_manager = ConfigManager(merchant=merchant)
        merchant_activity_config = config_manager.get_merchant_activity_config()
        if merchant_activity_config:
            activity_configs = config_manager.get_activity_configs(merchant_activity_config.activity_ids)
            for activity_config in activity_configs:
                if activity_config.activity_type == config_pb.ActivityConfig.FANPIAO_SNAP_UP:
                    store_vo.enable_fanpiao_snap_up = True
                    store_vo.activity_splash_image_url = activity_config.activity_splash_image_url
                    store_vo.fanpiao_snap_up_activity_time.start_timestamp = activity_config.start_timestamp
                    store_vo.fanpiao_snap_up_activity_time.end_timestamp = activity_config.end_timestamp
                    store_vo.activity_theme = activity_config.theme

        if registration_info:
            store_vo.enable_order_bell = registration_info.enable_order_bell
            store_vo.enable_take_out = registration_info.ordering_config.enable_take_out
            store_vo.enable_self_pick_up = registration_info.ordering_config.enable_self_pick_up
            store_vo.enable_eat_in = registration_info.ordering_config.enable_eat_in
            store_vo.enable_keruyun_take_out = registration_info.ordering_config.enable_keruyun_take_out
            store_vo.enable_keruyun_fast_food = registration_info.ordering_config.enable_keruyun_fast_food
            store_vo.enable_take_away = registration_info.ordering_config.enable_take_away
            store_vo.enable_many_people_order = registration_info.ordering_config.enable_many_people_order
            store_vo.image_size = registration_info.ordering_config.image_size
            if registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
                store_vo.disable_scan_table_pay = store.disable_scan_table_pay

            store_vo.pay_type = registration_info.pay_type
            if registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
                order_status = [dish_pb.DishOrder.APPROVED, dish_pb.DishOrder.TO_BE_CONFIRMED]
                before_time = int(time.time()) - 60 * 60 * 6
                recently_order = ordering_da.get_user_recently_order(
                    user_id=user_id, merchant_id=merchant_id, order_status=order_status, before_time=before_time
                )
                if recently_order:
                    logger.info("recently_order: {}".format(recently_order.id))
                    manager = OrderManager(merchant_id=merchant_id)
                    if registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
                        keruyun_order_detail = manager.get_order_detail_info(recently_order)
                        if (
                            keruyun_order_detail
                            and keruyun_order_detail.base_info.trade_pay_status
                            == export_detail_pb.KeruyunOrderDetail.BaseInfo.PAID
                        ):
                            logger.info("未支付订单客如云端已支付: {}".format(recently_order.id))
                        else:
                            logger.info("订单未支付: {}".format(recently_order.id))
                            store_vo.recently_order_id = recently_order.id
                    elif registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
                        order_detail = manager.get_order_detail_info(recently_order)
                        if order_detail:
                            store_vo.recently_order_id = recently_order.id
            order_status = [dish_pb.DishOrder.OrderStatus.PAID]
            before_time = int(time.time()) - 60 * 60 * 3
            recently_order = ordering_da.get_user_recently_order(
                user_id=user_id, merchant_id=merchant_id, order_status=order_status, before_time=before_time
            )
            if recently_order:
                store_vo.recently_paid_order_id = recently_order.id

        for url in store.store_photo_urls:
            store_vo.image_urls.append(url)
        for url in store.recommended_food_photo_urls:
            store_vo.recommended_food_photo_urls.append(url)

        if len(store.store_cover_photo_urls) > 0:
            store_vo.store_cover_photo_urls = store.store_cover_photo_urls[0]
        else:
            if len(store_vo.image_urls) > 0:
                store_vo.store_cover_photo_urls = store_vo.image_urls[0]

        opening_hours = store.opening_hours
        now = datetime.now()
        weekday = now.weekday()
        now_seconds = now.timestamp()
        logger.info(f"当前时间: {now}, {weekday}, {now_seconds}")
        store_vo.opening = True
        if registration_info:
            for opening_time_range in registration_info.opening_time_ranges:
                if weekday == opening_time_range.day_of_week:
                    if opening_time_range.start_second_of_day < now_seconds < opening_time_range.end_second_of_day:
                        store_vo.opening = True
                        break
                    else:
                        store_vo.opening = False
        # 设置天阙相关的信息
        self.__set_tian_que_pay_info(merchant, store_vo)
        store_vo.opening_hours_desc = '{}-{}'.format(
            date_utils.hours_text(opening_hours.daily_start_time),
            date_utils.hours_text(opening_hours.daily_end_time % date_utils.ONE_DAY),
        )
        ret = merchant_store_manager.is_no_discount_time_ranges(merchant)
        store_vo.no_discount_time_ranges = ret.range
        self.__sync_pos_config(store_vo, registration_info)
        self.__set_vip_membership_config(merchant, store_vo)
        self.__set_shopping_mall_info(merchant, store_vo)
        return store_vo

    def __set_tian_que_pay_info(self, merchant, store_vo):
        tian_que_pay_info = PaymentDataAccessHelper().get_tian_que_pay_info(
            merchant_id=merchant.id, state=payment_pb.TianQuePayInfo.NORMAL
        )
        if tian_que_pay_info:
            store_vo.child_no = tian_que_pay_info.child_no

    def __sync_pos_config(self, store_vo, registration_info):
        if registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return
        manager = ShilaiPosMerchantManager(merchant_id=store_vo.merchant_id)
        member_config = None
        try:
            store = manager.get_store_setting()
            member_config = store.get("storeMemberAccountRechargeConfig")
            logger.info(f"member_config: {member_config}")
        except Exception as e:
            logger.error(traceback.format_exc())
        if member_config:
            store_vo.enable_shilai_member_card_pay = member_config['enableMiniProgramMemberAccountPay']
            store_vo.enable_shilai_member_card_recharge = member_config['enableMiniProgramRecharge']
        return store_vo

    def get_app_component_config(self, merchant):
        app_component_config_manager = AppComponentConfigManager()
        app_component_config = app_component_config_manager.get_app_component_config(promotion_type=merchant.promotion_type)
        if app_component_config is None:
            app_component_config = app_component_config_manager.default_app_component_config()
        return app_component_config

    def get_merchant_coin_deduction_rate(self, merchant):
        if merchant.coin_deduction_coupon_template_id == "":
            return 0
        coupon_template_manager = CouponTemplateManager()
        coupon_template = coupon_template_manager.get_coupon_template(id=merchant.coin_deduction_coupon_template_id)
        if not coupon_template:
            return 0
        return coupon_template.fixed_discount.discount

    def __set_vip_membership_config(self, merchant, store_vo):
        config_da = ConfigDataAccessHelper()
        vip_membership_config = config_da.get_vip_membership_config(merchant_id=merchant.id)
        if vip_membership_config is None:
            return
        store_vo.enable_vip_membership_recharge = vip_membership_config.enable_vip_membership_recharge
        store_vo.enable_vip_membership_payment = vip_membership_config.enable_vip_membership_payment
        store_vo.vip_membership_discount = vip_membership_config.vip_membership_discount

    def __set_shopping_mall_info(self, merchant, store_vo):
        store_vo.disable_shopping_mall = merchant.disable_shopping_mall
        store_vo.shopping_mall_poster_image_url = merchant.shopping_mall_poster_image_url
        store_vo.shopping_mall_turn_to_url = merchant.shopping_mall_turn_to_url
        dimage = "https://shilai-images.oss-cn-shenzhen.aliyuncs.com/pay-success-shopping-mall.png"
        if merchant.shopping_mall_poster_image_url == "":
            store_vo.shopping_mall_poster_image_url = dimage  # 默认图片地址
        if merchant.shopping_mall_turn_to_url == "":
            store_vo.shopping_mall_turn_to_url = "pages/index/index"  # 默认跳转地址

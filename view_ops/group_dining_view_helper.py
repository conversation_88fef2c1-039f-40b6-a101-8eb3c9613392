# -*- coding: utf-8 -*-

import maya

from business_ops.merchant_store_manager import get_merchant_store
from business_ops.red_packet_manager import RedPacketManager
from business_ops.group_dining_manager import GroupDiningManager
from cache.group_dining_redis_helper import get_user_invitations
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from proto.page import group_dining_event_pb2 as group_dining_event_pb
from proto.page import group_dining_store_list_pb2 as group_dining_store_list_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb
from proto.finance import wallet_pb2 as wallet_pb
from service.errors import GroupDiningNotFound


class GroupDiningViewObjectHelper():

    def get_dining_by_id(self, user_id, dining_id):
        """ 根据饭局ID查找饭局
        Args:
            user_id: (string)
            dining_id: (string)饭局ID
        Return:
            group_dining_ui_pb.GroupDiningEvent结构体
        """
        ui_dining = group_dining_event_pb.GroupDiningEvent()
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        if dining:
            ui_dining.dish_order_mode = dining.dish_order_mode
            if dining.dish_order_mode == group_dining_pb.GroupDiningEvent.COMBO_MEAL:
                combo_meal = ComboMealDataAccessHelper().get_combo_meal_by_id(dining.combo_meal_id)
                ui_dining.combo_meal_name = combo_meal.name
                ui_dining.combo_meal_id = combo_meal.id
                ui_dining.original_price = combo_meal.original_price
                ui_dining.discount_price = combo_meal.discount_price
            merchant, store = get_merchant_store(dining.merchant_id, dining.store_id)
            if not merchant or not store:
                return None
            ui_dining.max_group_size = dining.max_group_size
            ui_dining.id = dining.id
            ui_dining.title = dining.title
            ui_dining.intro = dining.intro
            ui_dining.merchant_id = dining.merchant_id
            ui_dining.name = merchant.basic_info.display_name
            ui_dining.logo_url = merchant.basic_info.logo_url
            ui_dining.address = store.address
            ui_dining.event_time = dining.event_time
            ui_dining.phone = store.phone
            ui_dining.state = dining.state
            ui_dining.payment_rule = dining.payment_rule
            ui_dining.initiator_id = dining.initiator_id
            ui_dining.director_id = dining.director_id
            ui_dining.transaction_id = dining.transaction_id
            ui_dining.open_invite_permission = dining.open_invite_permission
            ui_dining.user_monetary_state = GroupDiningManager(
                user_id=user_id, dining_id=dining_id).get_user_group_dining_monetary_state()
            invitation = InvitationDataAccessHelper().get_invitation(user_id=user_id, dining_id=dining_id)
            ui_dining.user_invitation_state = group_dining_pb.Invitation.PENDING
            if invitation:
                ui_dining.user_invitation_state = invitation.state
                ui_dining.user_monetary_state = invitation.monetary_state
            for policy in store.group_dining_coupon_policies:
                p = ui_dining.policies.add()
                p.CopyFrom(policy)
            if invitation:
                ui_dining.signin = invitation.signin
            self.__get_accepted_users(dining, ui_dining.invitees)
        return ui_dining

    def __get_accepted_users(self, dining, invitation_list):
        """ 获取接受邀请的用户列表
        Args:
            dining_id: 饭局列表
            invitation_list: 受邀列表
        Returns:
            接受邀请的用户列表
        """
        invitations = InvitationDataAccessHelper().get_invitations(
            dining_id=dining.id, state=group_dining_pb.Invitation.ACCEPTED,
            orderby=[("acceptTime", 1)])
        user_dao = UserDataAccessHelper()
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
        dining_transaction = TransactionDataAccessHelper().get_transaction_by_id(dining.transaction_id)
        if dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING and dining_transaction:
            paid_fee = GroupDiningManager.calculate_transfer_bill_fee(dining.id, dining_transaction)
        else:
            paid_fee = 0

        invitations.sort(key = lambda x: (1 if x.invitee_id == dining.director_id else 0, -1 * x.accept_time), reverse=True)
        for invitation in invitations:
            invitee = user_dao.get_user(invitation.invitee_id)
            i = invitation_list.add()
            i.nickname = invitee.member_profile.nickname
            i.headimgurl = invitee.member_profile.head_image_url
            i.id = invitation.invitee_id
            i.can_open_red_packet = RedPacketManager().can_open_red_packet(invitation.invitee_id, dining)
            i.paid = False
            i.paid_fee = paid_fee
            if invitee.id == dining.director_id and dining_transaction:
                i.paid_fee = dining_transaction.paid_fee
            transaction = TransactionDataAccessHelper().get_transaction_by_id(invitation.transaction_id)
            if transaction and transaction.state == wallet_pb.Transaction.SUCCESS:
                i.paid = True
            if red_packet:
                value_assignments = red_packet.value_assignments
                if not i.can_open_red_packet:  # 如果未开红包,返回红包金额为0.
                    i.red_packet_value = value_assignments.get(invitee.id, 0)
                else:
                    i.red_packet_value = 0

    def get_user_group_dining_list(self, user_id, page=None, size=None, state=None, monetary_state=None):
        """ 用户饭局列表
        """
        invitation_da = InvitationDataAccessHelper()
        user_da = UserDataAccessHelper()

        invitations = get_user_invitations(user_id)
        group_dining_da = GroupDiningDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        ret = []
        longest_time = maya.when("now").add(days=-1).datetime().timestamp()
        for invitation in invitations:

            tmp = invitation[0].decode('utf8')
            dining_event_id = tmp.split(":")[0]

            dining = group_dining_da.get_dining_by_id(dining_event_id)
            if longest_time > dining.event_time:
                # 一天前的饭局就不返回了
                continue

            invitation = InvitationDataAccessHelper().get_invitation(dining_id=dining_event_id, invitee_id=user_id)
            merchant = merchant_da.get_merchant(dining.merchant_id)

            group_dining_event = group_dining_store_list_pb.GroupDiningEvent()

            group_dining_event.store_name = merchant.stores[0].name
            group_dining_event.logo_url = merchant.basic_info.logo_url
            group_dining_event.event_time = dining.event_time
            group_dining_event.monetary_state = invitation.monetary_state
            group_dining_event.title = dining.title
            group_dining_event.id = dining_event_id

            dining_invitations = invitation_da.get_invitations(
                dining_id=invitation.dining_event_id, state=group_dining_pb.Invitation.ACCEPTED)
            for i in dining_invitations:
                user_info = user_da.get_user(i.invitee_id)
                if user_info:
                    group_dining_event.headimgurl.append(user_info.wechat_profile.headimgurl)
            ret.append(group_dining_event)
        return ret

    def get_dining_detail(self, user_id, dining_id):
        """ 获取饭局详细信息,用于浏览饭局页
        Args:
            user_id: (string)用户id
            dining_id: (string)饭局id
        """

        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        if not dining:
            raise GroupDiningNotFound()
        merchant = MerchantDataAccessHelper().get_merchant(dining.merchant_id)
        store = merchant.stores[0]
        director = UserDataAccessHelper().get_user(dining.director_id)

        dining_vo = group_dining_store_list_pb.NearbyGroupDiningEventDetail()

        dining_vo.id = dining.id
        dining_vo.title = dining.title
        dining_vo.intro = dining.intro
        dining_vo.headimgurl = director.member_profile.head_image_url
        dining_vo.nickname = director.member_profile.nickname
        for tag in director.member_profile.tags:
            dining_vo.tags.append(tag)
        dining_vo.logo = merchant.basic_info.logo_url
        dining_vo.store_name = store.name
        dining_vo.address = store.address
        dining_vo.dish_order_mode = dining.dish_order_mode
        accepted_count = InvitationDataAccessHelper().count_invitation(
            dining_id=dining_id, state=group_dining_pb.Invitation.ACCEPTED)
        dining_vo.user_cnt = accepted_count
        dining_vo.user_limit = dining.max_group_size
        dining_vo.payment_rule = dining.payment_rule
        dining_vo.event_date = dining.event_time
        invitation = InvitationDataAccessHelper().get_invitation(dining_id=dining_id, invitee_id=user_id)
        if invitation:
            dining_vo.invitation_state = invitation.state
        else:
            dining_vo.invitation_state = group_dining_pb.Invitation.PENDING
        dining_vo.birth_year = director.member_profile.birth_year
        dining_vo.birth_month = director.member_profile.birth_month
        dining_vo.birth_day = director.member_profile.birth_day
        dining_vo.combo_meal_name = ""
        if dining_vo.dish_order_mode == group_dining_pb.GroupDiningEvent.COMBO_MEAL:
            combo_meal = ComboMealDataAccessHelper().get_combo_meal_by_id(dining.combo_meal_id)
            dining_vo.combo_meal_name = combo_meal.name
        dining_vo.director_id = dining.director_id
        if len(store.store_photo_urls) > 0:
            dining_vo.banner = store.store_photo_urls[0]
        return dining_vo

# -*- coding: utf-8 -*-

import logging

from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAcc<PERSON>Helper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.ifeedu_da_helper import IFeedUDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceData<PERSON>ccessHelper
from proto.page import transaction_info_pb2 as transaction_info_pb
from proto.finance import wallet_pb2 as wallet_pb

logger = logging.getLogger(__name__)


class TransactionInfoViewObjectHelper():
    def get_transaction_info(self, transaction_id):
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
        if not transaction:
            return None
        if transaction.type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
            # 饭局买单
            return self.__generate_group_dining_transaction_info(transaction)
        elif transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
            # 个人买单
            return self.__generate_self_dining_transaction_info(transaction)
        elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            # 扫码点餐
            return self.__generate_self_dish_order_transaction_info(transaction)
        elif transaction.type == wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT:
            # 扫码买单
            return self.__generate_self_dining_discount_payment_info(transaction)
        elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
            # 扫码约饭
            return self.__generate_group_dish_order_payment_transaction_info(transaction)
        elif transaction.type in [wallet_pb.Transaction.GROUP_DISH_ORDER_TRANSFER,
                                  wallet_pb.Transaction.GROUP_DINING_TRANSFER]:
            # 扫码AA
            return self.__generate_group_dish_order_transfer_transaction_info(transaction)
        elif transaction.type in [wallet_pb.Transaction.FEEDING_PAYMENT]:
            return self.__generate_feeding_transaction_info(transaction)
        return None

    def __generate_feeding_transaction_info(self, transaction):
        ifeedu_da = IFeedUDataAccessHelper()

        feed_plan = ifeedu_da.get_feed_plan(transaction_id=transaction.id)
        wishlist = ifeedu_da.get_wishlist_by_id(feed_plan.wish_list_id)
        wishlist_dishes = {}
        for item in wishlist.wish_items:
            wishlist_dishes.update({item.id: item.is_user_like})

        transaction_info = transaction_info_pb.FeedingTransactionInfo()
        transaction_info.user_id = wishlist.user_id
        feed_plan = IFeedUDataAccessHelper().get_feed_plan(transaction_id=transaction.id)
        hit_number = 0
        for feed_item in feed_plan.feed_items:
            if wishlist_dishes.get(feed_item.id):
                dish = transaction_info.dishes.add()
                dish.name = feed_item.name
                dish.image_url = feed_item.image_url
                hit_number += 1
        transaction_info.hit_number = hit_number
        return transaction_info

    def __generate_group_dish_order_payment_transaction_info(self, transaction):
        transaction_info = transaction_info_pb.GroupDishOrderPaymentTransactionInfo()
        transaction_info.bill_fee = transaction.bill_fee
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        store = merchant.stores[0]
        transaction_info.store_name = store.name
        transaction_info.merchant_id = transaction.payee_id
        transaction_info.logo_url = merchant.basic_info.logo_url
        transaction_info.paid_time = transaction.paid_time
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        coupon_id = transaction.use_coupon_id
        if coupon_id:
            coupon = CouponDataAccessHelper().get_coupon_by_id(coupon_id)
            coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
            transaction_info.coupon_reduce = coupon_category.cash_coupon_spec.reduce_cost
        return transaction_info

    def __generate_group_dish_order_transfer_transaction_info(self, transaction):
        transaction_info = transaction_info_pb.GroupDishOrderTransferTransactionInfo()
        invitation = InvitationDataAccessHelper().get_invitation(transaction_id=transaction.id)
        group_dining_event_id = invitation.dining_event_id
        dining = GroupDiningDataAccessHelper().get_dining_by_id(group_dining_event_id)
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        transaction_info.paid_time = transaction.paid_time
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        return transaction_info

    def __generate_self_dining_discount_payment_info(self, transaction):
        transaction_info = transaction_info_pb.SelfDiningDiscountPaymentTransactionInfo()
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        store = merchant.stores[0]
        transaction_info.store_name = store.name
        transaction_info.merchant_id = transaction.payee_id
        transaction_info.logo_url = merchant.basic_info.logo_url
        transaction_info.paid_time = transaction.paid_time
        transaction_info.no_discount_bill_fee = transaction.no_discount_bill_fee
        transaction_info.bill_fee = transaction.bill_fee
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        return transaction_info

    def __generate_group_dining_transaction_info(self, transaction):
        transaction_info = transaction_info_pb.GroupDiningTransactionInfo()
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        store = merchant.stores[0]
        transaction_info.store_name = store.name
        transaction_info.merchant_id = transaction.payee_id
        transaction_info.logo_url = merchant.basic_info.logo_url
        transaction_info.paid_time = transaction.paid_time
        dining = GroupDiningDataAccessHelper().get_dining_by_transaction_id(transaction.id)
        transaction_info.payment_rule = dining.payment_rule
        transaction_info.user_cnt = dining.user_cnt
        transaction_info.group_dining_event_id = dining.id
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        return transaction_info

    def __generate_self_dining_transaction_info(self, transaction):
        transaction_info = transaction_info_pb.SelfDiningTransactionInfo()
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        store = merchant.stores[0]
        transaction_info.store_name = store.name
        transaction_info.merchant_id = transaction.payee_id
        transaction_info.logo_url = merchant.basic_info.logo_url
        transaction_info.paid_time = transaction.paid_time
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        return transaction_info

    def __generate_self_dish_order_transaction_info(self, transaction):
        transaction_info = transaction_info_pb.SelfDiningTransactionInfo()
        transaction_info.paid_fee = transaction.paid_fee
        transaction_info.saved_fee = transaction.bill_fee - transaction.paid_fee
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        store = merchant.stores[0]
        transaction_info.store_name = store.name
        transaction_info.merchant_id = transaction.payee_id
        transaction_info.logo_url = merchant.basic_info.logo_url
        transaction_info.paid_time = transaction.paid_time
        order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
        transaction_info.ifeedu_fee = order.ifeedu_fee
        coupon_id = transaction.use_coupon_id
        if coupon_id:
            coupon = CouponDataAccessHelper().get_coupon_by_id(coupon_id)
            coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
            transaction_info.coupon_reduce = coupon_category.cash_coupon_spec.reduce_cost
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet and red_packet.value_assignments.get(transaction.payer_id) is not None and \
           red_packet.drawn_users.get(transaction.payer_id) is None:
            transaction_info.red_packet_id = red_packet.id
        return transaction_info

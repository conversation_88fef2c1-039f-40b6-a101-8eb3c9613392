# -*- coding: utf-8 -*-

import time

import proto.page.transaction_list_pb2 as transaction_list_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.coupons_pb2 as coupons_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
from business_ops.data_center_helper import DataCenterHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper


class TransactionListViewHelper:

    def get_merchant_name(self, merchant):
        store = merchant.stores[0]
        if store.name:
            return store.name
        if merchant.basic_info.display_name:
            return merchant.basic_info.display_name
        if merchant.basic_info.name:
            return merchant.basic_info.name
        return ""

    def get_user_order_list_v2(self, user_id, merchant_id=None, latest_create_time=None, status=None):
        helper = DataCenterHelper()
        data = helper.get_order_list(merchant_id, user_id, create_time=latest_create_time, status=status).get("data")
        orders = data.get("orders")
        merchant_da = MerchantDataAccessHelper()
        user_order_list_vo = transaction_list_pb.UserOrderListVO()
        merchants = {}
        for order in orders:
            if order.get("status") in [
                    "ORDERED"
            ]:
                continue
            merchant_id = order.get("merchantId")
            merchant = merchants.get(merchant_id)
            if not merchant:
                merchant = merchant_da.get_merchant(merchant_id=merchant_id)
                merchants.update({merchant_id: merchant})
            items = order.get("items")
            order_vo = user_order_list_vo.orders.add()
            order_vo.id = order.get("id")
            order_vo.create_time = int(order.get("createTime"))
            order_vo.bill_fee = order.get("totalBillFee")
            order_vo.merchant_id = merchant_id
            order_vo.meal_code = order.get("mealCode", "")
            status = order.get("status")
            if status == "REFUNDED":
                status = "POS_RETURN"
            order_vo.status = dish_pb.DishOrder.OrderStatus.Value(status)
            order_vo.merchant_name = self.get_merchant_name(merchant)
            for transaction_info in order.get("transactionInfos"):
                order_vo.transaction_type = transaction_info.get("type")
            meal_type = order.get("dineInOrder", {}).get("mealType")
            if meal_type:
                meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
                order_vo.meal_type = meal_type
            self.__deal_with_items(order_vo, items)
            user_order_list_vo.latest_time = order_vo.create_time
        if len(orders) > 0:
            user_order_list_vo.has_more = True
        return user_order_list_vo

    def __deal_with_items(self, order_vo, items):
        if items is None:
            return
        for item in items:
            dish_vo = order_vo.dishes.add()
            dish_vo.name = item.get("name")
            order_vo.dish_count += int(item.get("quantity"))
            dish_vo.image_url = item.get("imageUrl")

    def get_user_order_list(self, user_id, merchant_id=None, create_time=None):
        """ 用户扫码点餐订单列表
        """
        status = [
            dish_pb.DishOrder.PAID,
            dish_pb.DishOrder.APPROVED,
            dish_pb.DishOrder.POS_RETURN
        ]
        ordering_da = OrderingServiceDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()
        user_order_list_vo = transaction_list_pb.UserOrderListVO()
        if create_time is None:
            create_time = int(time.time())
        last_create_time = str(create_time)

        orders = ordering_da.get_orders(user_id=user_id, last_create_time=last_create_time, status=status, orderby=[("createTime", -1)], size=50, merchant_id=merchant_id)

        for order in orders:
            merchant = merchant_da.get_merchant(merchant_id=order.merchant_id)
            transaction = transaction_da.get_transaction_by_id(order.transaction_id)
            if not transaction:
                continue
            dish_count = 0
            order_vo = user_order_list_vo.orders.add()
            dish_count += self.deal_with_products(order_vo, order.merchant_id, order.products)
            for add_products in order.add_products:
                dish_count += self.deal_with_products(order_vo, order.merchant_id, add_products.products)
            order_vo.id = order.id
            order_vo.user_id = order.user_id
            order_vo.merchant_id = order.merchant_id
            order_vo.merchant_name = self.get_merchant_name(merchant)
            order_vo.create_time = order.create_time
            order_vo.paid_time = order.paid_time
            order_vo.bill_fee = transaction.bill_fee
            order_vo.paid_fee = transaction.paid_fee
            order_vo.dish_count = dish_count
            order_vo.meal_type = order.meal_type
            order_vo.status = order.status
            order_vo.meal_code = order.meal_code
            last_create_time = str(order.create_time)
            user_order_list_vo.latest_time = order.create_time
        order_count = ordering_da.count_orders(
            user_id=user_id, create_time=last_create_time, status=status)

        if order_count > 0:
            user_order_list_vo.has_more = True
        return user_order_list_vo

    def deal_with_products(self, order_vo, merchant_id, products):
        ordering_da = OrderingServiceDataAccessHelper()
        dish_count = 0
        for product in products:
            dish = ordering_da.get_dish(dish_id=product.id, merchant_id=merchant_id)
            if not dish:
                continue
            if dish.is_discount_dish:
                continue
            dish_vo = order_vo.dishes.add()
            dish_vo.id = dish.id
            if len(dish.images) > 0:
                dish_vo.image_url = dish.images[0]
            dish_vo.name = dish.name
            dish_count += int(product.quantity)
        return dish_count

    def set_coupon_package_can_refund(self, coupon_package):
        coupon_da = CouponDataAccessHelper()
        if coupon_package.status == coupons_pb.CouponPackage.REFUND:
            return False
        for coupon_id in coupon_package.coupon_ids:
            coupon = coupon_da.get_coupon_by_id(coupon_id)
            if not coupon:
                return False
            if coupon.state != coupons_pb.Coupon.ACCEPTED:
                return False
        return True

    def get_user_buy_coupon_package_list(self, user_id, merchant_id=None, last_paid_time=None):
        if last_paid_time is None:
            last_paid_time = int(time.time())
        last_paid_time = str(last_paid_time)
        transaction_da = TransactionDataAccessHelper()
        coupon_da = CouponDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        transactions = transaction_da.get_transactions(
            payer_id=user_id, orderby=[("paidTime", -1)], last_paid_time=last_paid_time, state=wallet_pb.Transaction.SUCCESS,
            type=wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE, size=50, payee_id=merchant_id)
        user_buy_coupon_package_list_vo = transaction_list_pb.UserBuyCouponPackageListVO()
        for transaction in transactions:
            last_paid_time = str(transaction.paid_time)
            user_buy_coupon_package_list_vo.latest_time = transaction.paid_time
            merchant = merchant_da.get_merchant(transaction.payee_id)
            coupon_package = coupon_da.get_coupon_package(transaction_id=transaction.id)
            if not coupon_package:
                continue
            if coupon_package.status == coupons_pb.CouponPackage.DELETED:
                continue
            coupon_package_spec = self.get_coupon_package_spec(merchant, coupon_package.coupon_category_id)
            if not coupon_package_spec:
                continue
            coupon_package_vo = user_buy_coupon_package_list_vo.coupon_packages.add()
            coupon_package_vo.id = coupon_package.id
            coupon_package_vo.merchant_id = transaction.payee_id
            coupon_package_vo.merchant_name = self.get_merchant_name(merchant)
            coupon_package_vo.bill_fee = transaction.bill_fee
            coupon_package_vo.paid_fee = transaction.bill_fee
            coupon_package_vo.status = coupon_package.status
            coupon_package_vo.name = coupon_package_spec.name
            coupon_package_vo.coupon_count = coupon_package_spec.coupon_package_spec.coupon_count
            coupon_package_vo.paid_time = transaction.paid_time
            coupon_package_vo.can_refund = self.set_coupon_package_can_refund(coupon_package)
            coupon_package_vo.transaction_id = transaction.id
        count = transaction_da.count_transactions(
            user_id=user_id, last_paid_time=last_paid_time, orderby=[("paidTime", -1)],
            type=wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE, state=wallet_pb.Transaction.SUCCESS)
        if count > 0:
            user_buy_coupon_package_list_vo.has_more = True
        return user_buy_coupon_package_list_vo

    def get_coupon_package_spec(self, merchant, coupon_category_id):
        for coupon_package in merchant.preferences.coupon_config.coupon_packages:
            if coupon_package.coupon_category_id == coupon_category_id:
                return coupon_package
        return None

    def get_user_buy_fanpiao_list(self, user_id, merchant_id=None, last_paid_time=None):
        user_buy_fanpiao_list_vo = transaction_list_pb.UserBuyFanpiaoListVO()
        if last_paid_time is None:
            last_paid_time = int(time.time())
        transaction_da = TransactionDataAccessHelper()
        fanpiao_da = FanpiaoDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        transactions = transaction_da.get_transactions(
            payer_id=user_id, orderby=[("paidTime", -1)], last_paid_time=last_paid_time, payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS, type=wallet_pb.Transaction.FANPIAO_PURCHASE, size=10)
        if len(transactions) == 0:
            return user_buy_fanpiao_list_vo
        count = transaction_da.count_transactions(
            user_id=user_id,
            last_paid_time=transactions[-1].paid_time,
            type=wallet_pb.Transaction.FANPIAO_PURCHASE,
            orderby=[("paidTime", 1)],
            state=wallet_pb.Transaction.SUCCESS)
        if count > 0:
            user_buy_fanpiao_list_vo.has_more = True

        for index, transaction in enumerate(transactions):
            last_paid_time = str(transaction.paid_time)
            user_buy_fanpiao_list_vo.latest_time = transaction.paid_time
            fanpiao = fanpiao_da.get_fanpiao(transaction_id=transaction.id)
            if not fanpiao:
                continue
            merchant = merchant_da.get_merchant(merchant_id=transaction.payee_id)
            fanpiao_vo = user_buy_fanpiao_list_vo.fanpiaos.add()
            fanpiao_vo.id = fanpiao.id
            fanpiao_vo.merchant_id = merchant.id
            fanpiao_vo.merchant_name = self.get_merchant_name(merchant)
            fanpiao_vo.name = fanpiao.name
            fanpiao_vo.paid_time = transaction.paid_time
            fanpiao_vo.bill_fee = transaction.bill_fee
            fanpiao_vo.paid_fee = transaction.paid_fee
            fanpiao_vo.status = fanpiao.status
            fanpiao_vo.can_refund = self.can_fanpiao_refund(fanpiao)
            fanpiao_vo.transaction_id = transaction.id
        return user_buy_fanpiao_list_vo

    def can_fanpiao_refund(self, fanpiao):
        if fanpiao.disable_refund:
            return False
        if fanpiao.status == fanpiao_pb.Fanpiao.REFUND:
            return False
        if fanpiao.status == fanpiao_pb.Fanpiao.INACTIVE:
            return False
        if fanpiao.total_used_fee == 0:
            return True
        return False

# -*- coding: utf-8-*-

##############################################
# 商户配置
#
##############################################

import logging
import time
import os
from datetime import datetime

import proto.page.merchant_assist.merchant_info_pb2 as merchant_info_pb
import proto.user_pb2 as user_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops import constants as const
from business_ops.ordering.order_manager import OrderManager
from business_ops.payment_manager import PaymentManager
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.coupon_package_manager import CouponPackageManager
from business_ops.shilai_member_manager import ShilaiMemberManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from business_ops.app_components_config_manager import AppComponentConfigManager
from common.utils import date_utils
from common.utils import file_access_helper
from common.aliyun_oss_helper import AliyunOSSHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto.ordering import dish_pb2 as dish_pb
from service import errors
from view_ops.merchant_assist.merchant_assist import MerchantAssist
from view_ops.merchant_assist.transaction_view_info import TransactionViewObj

logger = logging.getLogger(__name__)


class MerchantInfo(MerchantAssist):
    def __init__(self, merchant_id=None, user_id=None):
        super(MerchantInfo, self).__init__(merchant_id, user_id)

    def refund(self, transaction_id, check_user=True, **kargs):
        if check_user:
            self._check_user_role(merchant_rules_pb.ADMIN)
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.state != wallet_pb.Transaction.SUCCESS:
            raise errors.TransactionStatusError()
        if transaction.pay_method in [wallet_pb.Transaction.FANPIAO_PAY, wallet_pb.Transaction.WALLET]:
            today = datetime.today()
            today_zero = today.replace(hour=0, minute=0, second=0)
            if transaction.paid_time < int(today_zero.timestamp()):
                raise errors.ShowError("跨天的智能营销订单不允许退款")
        try:
            order_da = OrderingServiceDataAccessHelper()
            order = order_da.get_order(transaction_id=transaction_id)
            manager = OrderManager(merchant=self._merchant)
            refund_transaction = manager.initiate_ordering_refund(
                order, transaction_id=transaction_id, transaction=transaction, **kargs
            )
            order_manager_v2 = OrderManagerV2(
                merchant=self._merchant, transactions=[refund_transaction], order=order, back_comment=kargs.get('back_comment')
            )
            order_manager_v2.update_order_operation_record_refund(type=wallet_pb.Transaction.ORDERING_REFUND)
        except Exception as ex:
            logger.info("退款失败: {}".format(ex))
            raise ex

    def coupon_package_refund(self, transaction_id, check_user=True):
        if check_user:
            self._check_user_role(merchant_rules_pb.ADMIN)
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.state != wallet_pb.Transaction.SUCCESS:
            raise errors.TransactionStatusError()
        CouponPackageManager().coupon_package_refund(transaction)
        payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
        payment_manager.coupon_package_refund(transaction, reason="购买时来券包退款")

    def fanpiao_refund(self, transaction_id, check_user=True):
        if check_user:
            self._check_user_role(merchant_rules_pb.ADMIN)
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.state != wallet_pb.Transaction.SUCCESS:
            raise errors.TransactionStatusError()
        FanpiaoManager().fanpiao_refund(transaction)
        payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
        payment_manager.fanpiao_refund(transaction, reason="购买时来饭票退款")

    def member_card_recharge_refund(self, transaction_id, check_user=True):
        if check_user:
            self._check_user_role(merchant_rules_pb.ADMIN)
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.state != wallet_pb.Transaction.SUCCESS:
            raise errors.TransactionStatusError()
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id=transaction.payee_id)
        ShilaiMemberManager(merchant_id=transaction.payee_id, user_id=transaction.payer_id).refund(
            transaction=transaction, merchant=merchant
        )
        payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant=merchant)
        payment_manager.member_card_recharge_refund(transaction=transaction, reason="时来会员储值退款")

    def remove_employee(self, employee_id):
        self._check_user_role(merchant_rules_pb.ADMIN)
        if employee_id == self._user.id:
            raise errors.CannotRemoveYourself()
        user = MerchantUserDataAccessHelper().get_user(employee_id)
        for i, merchant in enumerate(user.merchants):
            if merchant.merchant_id == self._merchant.id:
                user.merchants.pop(i)
        MerchantUserDataAccessHelper().update_or_create_user(user)

    def edit_employee(self, user_merchant):
        self._check_user_role(merchant_rules_pb.ADMIN)
        user = MerchantUserDataAccessHelper().get_user(user_merchant.get('id'))
        for merchant in user.merchants:
            if merchant.merchant_id == self._merchant.id:
                if user_merchant.get('role'):
                    merchant.role = merchant_rules_pb.Role.Value(user_merchant.get('role'))
                if user_merchant.get('nickname'):
                    merchant.user_name = user_merchant.get('nickname')
                break
        MerchantUserDataAccessHelper().update_or_create_user(user)

    def get_merchant_user_list(self):
        self._check_user_role(merchant_rules_pb.ADMIN)
        ret = []
        users = MerchantUserDataAccessHelper().get_users(self._merchant.id)
        for user in users:
            for merchant in user.merchants:
                if merchant.merchant_id == self._merchant.id and merchant.status == user_pb.MerchantUser.NORMAL:
                    user_vo = merchant_info_pb.MerchantAssistUserVo()
                    user_vo.id = user.id
                    user_vo.nickname = merchant.user_name or user.wechat_profile.nickname
                    user_vo.headimgurl = user.wechat_profile.headimgurl
                    user_vo.status = user.status
                    user_vo.role = merchant.role if user.role != merchant_rules_pb.SHILAI_STAFF else user.role
                    user_vo.phone = user.phone
                    ret.append(user_vo)
                    break
        return ret

    def update_dish_category(self, category_id, status, no_discount, sns=None):
        ordering_da = OrderingServiceDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        dish_category = ordering_da.get_category(merchant_id=self._merchant.id, id=str(category_id))
        if not dish_category:
            return
        if status is not None:
            status = dish_pb.DishCategory.Status.Value(status)
            dish_category.status = status
        if no_discount is not None:
            dish_category.no_discount = no_discount
            self._merchant.preferences.coupon_config.max_discount_update_timestamp = int(time.time())
        ordering_da.add_or_update_category(category=dish_category)
        merchant_da.update_or_create_merchant(merchant=self._merchant)

    def set_dish_attr_multi_select(self, group_id, multi_select):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self._merchant.id, attr_group_id=group_id)
        for dish in dishes:
            for attr in dish.attrs:
                if attr.group_id == group_id:
                    attr.is_multi_select = multi_select
            ordering_da.add_or_update_dish(dish=dish)

    def change_dish_status(self, dish_id, status):
        self._check_user_role(merchant_rules_pb.ADMIN)
        ordering_da = OrderingServiceDataAccessHelper()
        status = dish_pb.Dish.Status.Value(status)
        dish = ordering_da.get_dish(merchant_id=self._merchant.id, dish_id=str(dish_id))
        if not dish:
            return None
        logger.info("把菜品状态从 {} 改变为 {}".format(dish.status, status))
        if dish.status == status:
            logger.info("菜品状态: {} == {}, 无需更改".format(status, dish.status))
            return None
        dish.status = status
        ordering_da.add_or_update_dish(dish=dish)
        dish_manager = DishManager(merchant=self._merchant)
        if dish.status == dish_pb.Dish.GUQING:
            dish_manager.set_dish_activation(dish, 0)
        elif dish.status == dish_pb.Dish.NORMAL:
            dish_manager.set_dish_activation(dish, 1)

    def change_attr_status(self, attr_id, status):
        if attr_id is None or status is None:
            return
        self._check_user_role(merchant_rules_pb.ADMIN)
        ordering_da = OrderingServiceDataAccessHelper()
        status = dish_pb.Attr.Status.Value(status)
        dishes = ordering_da.get_dishes(merchant_id=self._merchant.id, attr_id=attr_id)
        for dish in dishes:
            for attr in dish.attrs:
                if str(attr_id) == str(attr.id):
                    attr.status = status
            ordering_da.add_or_update_dish(dish=dish)

    def update_attr(self, attr_id, status, group_id, multi_select):
        self.change_attr_status(attr_id=attr_id, status=status)
        if group_id is None or multi_select is None:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self._merchant.id, attr_group_id=group_id)
        for dish in dishes:
            for attr in dish.attrs:
                if attr.group_id == group_id:
                    attr.is_multi_select = multi_select
            ordering_da.add_or_update_dish(dish=dish)

    def change_supply_condiment_status(self, supply_condiment_id, status):
        if supply_condiment_id is None or status is None:
            return
        self._check_user_role(merchant_rules_pb.ADMIN)
        status = dish_pb.SupplyCondiment.Status.Value(status)
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self._merchant.id, supply_condiment_id=supply_condiment_id)
        for dish in dishes:
            for supply_condiment in dish.supply_condiments:
                if str(supply_condiment.id) == str(supply_condiment_id):
                    supply_condiment.status = status
            ordering_da.add_or_update_dish(dish=dish)

    def update_supply_condiment(self, dish_id, supply_condiment_id, status, supply_condiment_uplimit):
        self._check_user_role(merchant_rules_pb.ADMIN)
        ordering_da = OrderingServiceDataAccessHelper()

        self.change_supply_condiment_status(supply_condiment_id, status)

        dish = ordering_da.get_dish(merchant_id=self._merchant.id, dish_id=str(dish_id))
        if supply_condiment_uplimit is not None and supply_condiment_uplimit != "":
            if dish.id == dish_id:
                dish.supply_condiment_uplimit = supply_condiment_uplimit
                ordering_da.add_or_update_dish(dish=dish)

    def get_app_component_config(self, merchant):
        app_component_config_manager = AppComponentConfigManager()
        app_component_config = app_component_config_manager.get_app_component_config(promotion_type=merchant.promotion_type)
        if app_component_config is None:
            app_component_config = app_component_config_manager.default_app_component_config()
        return app_component_config

    def get_merchant_info(self):
        """商户配置主页"""
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(merchant_id=self._merchant.id)
        app_component_config = self.get_app_component_config(self._merchant)
        merchant_info_vo = merchant_info_pb.MerchantAssistMerchantInfoVo()
        merchant_info_vo.merchant_id = self._merchant.id
        merchant_info_vo.is_ramen_joint_project = self._merchant.is_ramen_joint_project
        merchant_info_vo.store_id = "{}_0".format(self._merchant.id)
        merchant_info_vo.name = self._merchant.basic_info.display_name
        merchant_info_vo.logo_url = self._merchant.basic_info.logo_url
        merchant_info_vo.dish_number = ordering_da.count_dish(
            merchant_id=self._merchant.id,
            status=[
                dish_pb.Dish.GUQING,
                dish_pb.Dish.NORMAL,
                dish_pb.Dish.ENABLE,
                dish_pb.Dish.NOT_IN_TIME_LIMIT_SALE,
            ],
        )
        merchant_info_vo.sale_out_dish_number = ordering_da.count_dish(
            merchant_id=self._merchant.id, status=dish_pb.Dish.GUQING
        )
        if self._user is not None:
            merchant_info_vo.user_role = self._user.role
        else:
            merchant_info_vo.user_role = merchant_rules_pb.SHILAI_STAFF
        store = self._merchant.stores[0]
        merchant_info_vo.enable_shilai_member_card_recharge = store.enable_shilai_member_card_recharge
        merchant_info_vo.pos_type = registration_info.pos_type
        merchant_info_vo.ticket_package_name = app_component_config.ticket_package_name
        return merchant_info_vo

    def update_dish(self, dish_id, status, supply_condiment_uplimit, **kargs):
        ordering_da = OrderingServiceDataAccessHelper()
        dish = ordering_da.get_dish(dish_id=dish_id, merchant_id=self._merchant.id)
        if not dish:
            return
        if status is not None:
            status = dish_pb.Dish.Status.Value(status)
            dish.status = status
        if supply_condiment_uplimit is not None:
            dish.supply_condiment_uplimit = supply_condiment_uplimit
        if kargs.get("shilai_image") is not None:
            self.update_dish_shilai_image(dish, kargs.get("shilai_image"))

        price = kargs.get("price")
        if price is not None:
            dish.price = price

        name = kargs.get("name")
        if name is not None:
            dish.name = name

        category_id = kargs.get("category_id")
        if category_id is not None:
            dish.categories[0] = category_id

        ordering_da.add_or_update_dish(dish=dish)

    def update_dish_shilai_image(self, dish, image):
        dish.shilai_dish_image = image
        aliyun_oss_helper = AliyunOSSHelper()
        image_obj = aliyun_oss_helper.upload_image_network_stream(image)
        target_name = "thumb-{}".format(image_obj.name)
        thumb_image_obj = aliyun_oss_helper.resize(image_obj.name, target_name, model="m_lfit", w=480, h=480)
        dish.shilai_dish_thumb_image = thumb_image_obj.url

    def get_dishes(self, dish_status):
        if dish_status:
            dish_status = dish_pb.Dish.Status.Value(dish_status)
        ordering_service_da = OrderingServiceDataAccessHelper()

        vobj = merchant_info_pb.MerchantAssistDishListVo()

        attrs = {}
        supply_condiments = {}

        categories = ordering_service_da.get_categories(merchant_id=self._merchant.id, orderby=[('sort', 1)])
        for category in categories:
            if category.status == dish_pb.DishCategory.DELETE:
                continue
            category_id = category.id
            dishes = ordering_service_da.get_dishes(merchant_id=self._merchant.id, category_id=category_id, status=dish_status)
            if len(dishes) == 0:
                continue
            dishes_vo = vobj.dishes.add()

            # 填充菜品类别信息
            self._fill_category_vo(dishes_vo, category)

            for dish in dishes:
                dish_vo = dishes_vo.dish_list.add()
                # 填充菜品信息
                self._fill_dish_vo(dish_vo, dish)
                self._merge_attrs(attrs, dish)
                self._merge_supply_condiment(supply_condiments, dish)

        for attr_id, attr in attrs.items():
            vobj_attr = vobj.category_attr.attrs.add()
            vobj.category_attr.name = "属性"
            vobj_attr.name = attr.get("name")
            vobj_attr.status = attr.get("status")
            vobj_attr.id = attr_id
        for supply_condiment_id, supply_condiment in supply_condiments.items():
            vobj_supply_condiment = vobj.category_supply_condiment.supply_condiments.add()
            vobj.category_supply_condiment.name = "加料"
            vobj_supply_condiment.name = supply_condiment.get("name")
            vobj_supply_condiment.status = supply_condiment.get("status")
            vobj_supply_condiment.id = supply_condiment_id
        dish_count = ordering_service_da.count_dish(merchant_id=self._merchant.id)
        sold_out_dish_count = ordering_service_da.count_dish(merchant_id=self._merchant.id, status=dish_pb.Dish.GUQING)

        vobj.dish_count = dish_count
        vobj.sold_out_dish_count = sold_out_dish_count

        return vobj

    def export_order_data(self, start_paid_time=None, end_paid_time=None):
        self._check_user_role(merchant_rules_pb.ADMIN)
        order_list = TransactionViewObj(self._merchant.id, self._user.id).get_order_list(
            meal_type=None, start_paid_time=start_paid_time, end_paid_time=end_paid_time, size=20000
        )
        content = '消费时间,订单编号,订单金额,平台补贴金额,商家优惠金额,商家实收金额\n'
        start_time_str = date_utils.get_datetime_in_timezone(int(start_paid_time), date_utils.TIMEZONE_SHANGHAI).strftime(
            '%Y%m%d'
        )
        end_time_str = date_utils.get_datetime_in_timezone(int(end_paid_time), date_utils.TIMEZONE_SHANGHAI).strftime('%Y%m%d')
        for order in order_list:
            paid_time_str = date_utils.get_datetime_in_timezone(order.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime(
                '%Y-%m-%d %H:%M:%S'
            )
            content += '{},{},{},{},{},{}\n'.format(
                paid_time_str,
                order.order_id,
                order.bill_fee / 100,
                max(0, order.merchant_paid_in_fee - order.paid_fee) / 100,
                (order.bill_fee - order.merchant_paid_in_fee) / 100,
                order.merchant_paid_in_fee / 100,
            )
        file_name = '{}订单报表_{}-{}.csv'.format(self._merchant.basic_info.name, start_time_str, end_time_str)
        save_path = os.path.join(const.DATA_FILES_DIR, file_name)
        file_access_helper.write_file(save_path, content.encode())
        file_url = '{}{}'.format(const.REQUEST_FILE_URL, file_name)
        result = {'fileUrl': file_url}
        return result

    def _fill_category_vo(self, dishes_vo, category):
        dishes_vo.category.id = category.id
        dishes_vo.category.name = category.name
        dishes_vo.category.sort = category.sort
        dishes_vo.category.status = category.status
        dishes_vo.category.no_discount = category.no_discount

    def _fill_dish_vo(self, dish_vo, dish):
        dish_vo.id = dish.id
        dish_vo.name = dish.name
        if len(dish.images) > 0:
            dish_vo.image_url = dish.images[0]
        dish_vo.shilai_image_url = dish.shilai_dish_image
        dish_vo.price = dish.price
        dish_vo.status = dish.status
        dish_vo.sort = dish.sort
        dish_vo.category_id = dish.categories[0]

        attr_list_vo_dict = {}

        for attr in dish.attrs:
            attr_list_vo = attr_list_vo_dict.get(attr.group_id)
            if not attr_list_vo:
                attr_list_vo = dish_vo.attr_list.add()
                attr_list_vo_dict.update({attr.group_id: attr_list_vo})
            attr_list_vo.group_id = attr.group_id
            attr_list_vo.group_name = attr.group_name
            attr_list_vo.is_multi_select = attr.is_multi_select
            attr_vo = attr_list_vo.attrs.add()
            self._fill_attr_vo(attr_vo, attr)

        for supply_condiment in dish.supply_condiments:
            supply_condiment_vo = dish_vo.supply_condiments.add()
            self._fill_supply_condiment_vo(supply_condiment_vo, supply_condiment)
        dish_vo.supply_condiment_uplimit = dish.supply_condiment_uplimit

    def _fill_attr_vo(self, attr_vo, attr):
        attr_vo.id = attr.id
        attr_vo.name = attr.name
        attr_vo.reprice = attr.reprice
        attr_vo.type = attr.type

    def _fill_supply_condiment_vo(self, supply_condiment_vo, supply_condiment):
        supply_condiment_vo.id = supply_condiment.id
        supply_condiment_vo.name = supply_condiment.name
        supply_condiment_vo.market_price = supply_condiment.market_price

    def _merge_attrs(self, attrs, dish):
        for attr in dish.attrs:
            attrs.update({attr.id: {"name": attr.name, "status": attr.status}})

    def _merge_supply_condiment(self, supply_condiments, dish):
        for supply_condiment in dish.supply_condiments:
            supply_condiments.update({supply_condiment.id: {"name": supply_condiment.name, "status": supply_condiment.status}})

import logging
import traceback

import pandas as pd
from google.protobuf import json_format

import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.page.merchant_assist.stats_pb2 as stats_pb
import proto.ordering.registration_pb2 as registration_pb
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from bi import payment_analyzer
from business_ops.ordering.shilai_ops_manager import Shi<PERSON><PERSON>SManager, ShilaiPosMerchantManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from view_ops.merchant_assist.merchant_assist import MerchantAssist
import proto.ordering.dish_pb2 as dish_pb

logger = logging.getLogger(__name__)


class StatsViewInfo(MerchantAssist):
    def __init__(self, merchant_id=None, user_id=None):
        super(StatsViewInfo, self).__init__(merchant_id, user_id)

    def get_merchant_marketing_stats(self, start_date, end_date, start_time, end_time, type):
        """
        储值没有order, 买券包和饭票没有 order
        储值是一个商家的个别行为，对于个别商家我们允许了，大部分情况下不允许，因为这跟我们饭票相冲的
        目前平台上有几家有这个情况，以及未来会有优质商家有这种情况
        """
        if start_date and end_date:
            transaction_time = self._calculate_transactions_time(start_date, end_date, type)
            start_time, end_time = int(transaction_time.start_time), int(transaction_time.end_time)
        else:
            start_time, end_time = int(start_time), int(end_time)
        stats = payment_analyzer.get_merchant_payment_stats(
            merchant_id=self._merchant.id, start_time=start_time, end_time=end_time)
        stats_vo = stats_pb.MerchantStats()
        stats_vo.merchant_id = self._merchant.id
        stats_vo.start_time = start_time
        stats_vo.end_time = end_time
        stats_vo.is_ramen_joint_project = self._merchant.is_ramen_joint_project
        # 饭票数据
        stats_vo.fanpiao_stats.total_buy_count = stats.fanpiao_stats.total_buy_count  # 购买次数
        # 券包数据
        stats_vo.coupon_package_stats.total_buy_count = stats.coupon_package_stats.total_buy_count
        stats_vo.coupon_package_stats.total_bill_count = stats.total_coupon_bill_count
        stats_vo.coupon_package_stats.total_received_fee = stats.total_coupon_paid_fee + stats.total_coupon_subsidy_fee
        stats_vo.coupon_package_stats.total_paid_fee = stats.total_coupon_paid_fee
        stats_vo.coupon_package_stats.subsidy = stats.total_coupon_subsidy_fee
        logger.info(f"stats_vo.coupon_package_stats: {stats_vo.coupon_package_stats}")
        # 会员储值数据
        stats_vo.member_recharge_stats.total_recharge_count = stats.member_recharge_stats.total_recharge_count  # 储值次数
        stats_vo.member_recharge_stats.total_recharge_value = stats.member_recharge_stats.total_recharge_value  # 储值总金额
        stats_vo.member_recharge_stats.total_bill_count = stats.total_recharge_member_bill_count  # 消费订单数
        stats_vo.member_recharge_stats.total_bill_fee = stats.total_recharge_member_bill_fee  # 消费总金额
        self.aggregate_coupon_transaction(stats_vo, start_time, end_time)
        self.aggregate_fanpiao_transactions(stats_vo, start_time, end_time)
        return stats_vo

    @property
    def ordering_dao(self):
        return OrderingServiceDataAccessHelper()

    @property
    def transaction_dao(self):
        return TransactionDataAccessHelper()

    @classmethod
    def to_json(cls, obj, columns=None):
        if not obj:
            return {}
        origin = json_format.MessageToDict(obj, including_default_value_fields=True)
        if columns and isinstance(columns, list):
            return {key: value for key, value in origin.items() if key in columns}
        return origin

    def get_mini_fanpiao_orders(self, start_time, end_time):
        """
        获取 饭票支付的订单
        Returns:

        """
        logger.info(f"[*] get_mini_fanpiao_orders: {start_time} ~ {end_time}")
        paid_orders = self.ordering_dao.get_orders(
            merchant_id=self._merchant.id, start_paid_time=start_time, end_paid_time=end_time,
            status=dish_pb.DishOrder.PAID
        )
        logger.info(f"[*] paid_orders: {self._merchant.id} {len(paid_orders)}")
        tran_ids = [o.transaction_id for o in paid_orders]
        tran_id2obj = {t.id: self.to_json(t, columns=['id', 'payMethod', 'paidFee'])
                       for t in self.transaction_dao.get_transactions(ids=tran_ids)}
        orders = list()
        for order in paid_orders:
            _tran = tran_id2obj.get(order.transaction_id)
            if _tran and _tran['payMethod'] == 'FANPIAO_PAY':
                # logger.info(f"[*] FANPIAO_PAY: {_tran}")
                order_json = self.to_json(order, columns=['id', 'paidInFee', "fanpiaoCommissionFee"])  # 实收
                order_json['payMethod'] = _tran['payMethod']
                order_json['transactionPaidFee'] = _tran['paidFee']  # 实付
                orders.append(order_json)
        logger.info(f"[*] get_mini_fanpiao_orders Count: {len(orders)}")
        return orders

    def get_pos_fanpiao_orders(self, start_time, end_time):
        logger.info(f"[*] get_pos_fanpiao_orders: {start_time} ~ {end_time}")
        manager = ShilaiOPSManager(merchant_id=self._merchant.id)
        orders = list()
        for order in manager.get_pos_shilai_orders(start_time, end_time):
            orders.append({
                'id': order['id'],
                'transactionPaidFee': order['totalPaidFee'],
                'payMethod': order['payMethod'],
                'paidInFee': order['totalReceivableFee'],
                'fanpiaoCommissionFee': order.get('fanpiaoCommissionFee', 0)
            })
        logger.info(f"[*] get_pos_fanpiao_orders Count: {len(orders)}")
        return orders

    @property
    def has_pos_service(self):
        merchant = self.ordering_dao.get_registration_info(self._merchant.id)
        return merchant.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI

    @property
    def fanpiao_dao(self):
        return FanpiaoDataAccessHelper()

    def get_fanpiao_purchase_count(self, stats_vo, start_time, end_time):
        """
        获取饭票购买总额
        """
        fanpiao_list = self.fanpiao_dao.get_fanpiaos(
            merchant_id=self._merchant.id, buy_start_time=start_time, buy_end_time=end_time,
            status=fanpiao_pb.Fanpiao.ACTIVE
        )
        stats_vo.fanpiao_stats.total_fanpiao_purchase_fee = sum([fp.sell_price for fp in fanpiao_list])
        stats_vo.fanpiao_stats.total_buy_count = len(fanpiao_list)
        logger.info(f"get_fanpiao_purchase_count: "
                    f"{stats_vo.fanpiao_stats.total_fanpiao_purchase_fee} and {stats_vo.fanpiao_stats.total_buy_count}")
        return stats_vo

    def get_coupon_purchase_count(self, start_time, end_time):
        """
        获取饭票购买总额
        """
        state = wallet_pb.Transaction.SUCCESS
        types = [wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE, wallet_pb.Transaction.COUPON_PACKAGE_REFUND]
        fanpiao_purchase = self.transaction_dao.get_transactions(
            payee_id=self._merchant.id, start_time=start_time, end_time=end_time,
            state=state, multi_types=types)
        return sum([t.bill_fee for t in fanpiao_purchase])

    def aggregate_coupon_transaction(self, stats_vo, start_time, end_time):
        """
        聚合券包的信息
        """
        # get coupon orders
        state = wallet_pb.Transaction.SUCCESS
        coupon_transaction_ids = [t.id for t in self.transaction_dao.get_transactions(
            payee_id=self._merchant.id, state=state, start_time=start_time, end_time=end_time) if t.use_coupon_id]
        coupon_orders = [o for o in self.ordering_dao.get_orders(
            merchant_id=self._merchant.id, start_paid_time=start_time, end_paid_time=end_time,
            status=dish_pb.DishOrder.PAID) if o.transaction_id in coupon_transaction_ids]
        stats_vo.coupon_package_stats.total_coupon_purchase_fee = self.get_coupon_purchase_count(start_time, end_time)
        stats_vo.coupon_package_stats.total_bill_count = len(coupon_orders)
        stats_vo.coupon_package_stats.total_paid_fee = sum([o.paid_fee for o in coupon_orders])
        stats_vo.coupon_package_stats.total_received_fee = sum([o.paid_in_fee for o in coupon_orders])
        # subsidy = total_received_fee - total_paid_fee
        stats_vo.coupon_package_stats.subsidy = sum([o.platform_discount_fee for o in coupon_orders])
        logger.info(f"aggregate_coupon_transaction: {stats_vo.coupon_package_stats}")
        return stats_vo

    def aggregate_fanpiao_transactions(self, stats_vo, start_time, end_time):
        """
        聚合 饭票的信息
        加上饭票购买总额
        """
        orders = self.get_mini_fanpiao_orders(start_time, end_time)
        if self.has_pos_service:
            try:
                orders.extend(self.get_pos_fanpiao_orders(start_time, end_time))
            except Exception as e:
                traceback.print_exc()
        if not orders:
            return stats_vo
        df = pd.DataFrame(orders)
        total_received_fee = int(df['paidInFee'].sum())
        total_fanpiao_commission_fee = int(df['fanpiaoCommissionFee'].sum())
        stats_vo.fanpiao_stats.total_bill_count = int(df.shape[0])
        stats_vo.fanpiao_stats.total_paid_fee = int(df['transactionPaidFee'].sum())
        stats_vo.fanpiao_stats.total_received_fee =  total_received_fee
        stats_vo.fanpiao_stats.subsidy = total_received_fee - \
            stats_vo.fanpiao_stats.total_paid_fee + total_fanpiao_commission_fee
        self.get_fanpiao_purchase_count(stats_vo, start_time, end_time)
        logger.info(f"[*] aggregate_fanpiao_transactions : {stats_vo.fanpiao_stats}")
        return stats_vo

    def get_merchant_dish_stats(self, start_time, end_time):
        """
        菜品销量统计
        """
        manager = ShilaiPosMerchantManager(merchant_id=self._merchant.id)
        return manager.get_merchant_dish_stats(start_time, end_time)

    def get_merchant_strategy_stats(self, start_time, end_time):
        """
        优惠策略统计
        """
        manager = ShilaiPosMerchantManager(merchant_id=self._merchant.id)
        return manager.get_merchant_strategy_stats(start_time, end_time)

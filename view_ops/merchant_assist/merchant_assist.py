# -*- coding: utf-8-*-

import datetime
import logging

import pytz
from collections import namedtuple

from google.protobuf import json_format
import proto.page.merchant_assist.transaction_pb2 as transaction_pb
from common.utils import date_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class MerchantAssist:
    def __init__(self, merchant_id=None, user_id=None):
        self._merchant = None
        self._user = None
        if merchant_id is not None:
            self._merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if user_id is not None:
            self._user = MerchantUserDataAccessHelper().get_user(user_id)

    @classmethod
    def to_json(cls, obj, columns=None):
        if not obj:
            return {}
        origin = json_format.MessageToDict(obj, including_default_value_fields=True)
        if columns and isinstance(columns, list):
            return {key: value for key, value in origin.items() if key in columns}
        return origin

    def _check_user_role(self, at_least_role):
        """ 目前只存在三种身份.修改只有管理员才能操作,只要用户的身份小于`at_least_role`,就直接不能操作修改
        以后需要的时候再为每种身份创建权限集
        """
        # role = None
        # for merchant in self._user.merchants:
        #     if merchant.merchant_id == self._merchant.id:
        #         role = merchant.role
        #         break
        # if not role:
        #     raise errors.MerchantAssistPermissionDenied()
        # if role == merchant_rules_pb.SHILAI_STAFF:
        #     return
        # if role == merchant_rules_pb.NULL:
        #     raise errors.MerchantAssistPermissionDenied()
        # if role > at_least_role:
        #     raise errors.MerchantAssistPermissionDenied()
        return

    def _calculate_transactions_time(self, start_date, end_date, type):
        TransactionTime = namedtuple("TransactionTime", ["start_time", "end_time"])
        type = transaction_pb.MerchantAssistTransactionVo.Type.Value(type)
        if type == transaction_pb.MerchantAssistTransactionVo.WEEK:
            # 最近一周,包括当天,所以结束时间要到明天0点
            now = date_utils.timestamp_second() + date_utils.ONE_DAY
            time_range = date_utils.get_time_range_from_timestamp_and_timedelta(now, 6)
            start_time, end_time = time_range.start_time, time_range.end_time
        elif type == transaction_pb.MerchantAssistTransactionVo.MONTH:
            now = date_utils.timestamp_second() + date_utils.ONE_DAY
            time_range = date_utils.get_time_range_from_timestamp_and_timedelta(now, 29)
            start_time, end_time = time_range.start_time, time_range.end_time
        elif type == transaction_pb.MerchantAssistTransactionVo.SELF_DEFINE:
            if not start_date or not end_date:
                logger.info("start_date or end_date is None: {}, {}".format(start_date, end_date))
                return self._calculate_transactions_time(start_date, end_date, "TODAY")
            timezone = pytz.timezone(date_utils.TIMEZONE_SHANGHAI)
            start_time = datetime.datetime.strptime(start_date, "%Y-%m-%d")
            end_time = datetime.datetime.strptime(end_date, "%Y-%m-%d")
            start_time = start_time.astimezone(timezone)
            end_time = end_time.astimezone(timezone)
            start_time = start_time.replace(hour=0, minute=0, second=0)
            end_time = end_time.replace(hour=0, minute=0, second=0)
            start_time = start_time.timestamp()
            end_time = end_time.timestamp()
            logger.info("_calculate_transactions_time: start_time: {}, end_time: {}".format(start_time, end_time))
        else:  # 把TODAY当做默认值
            now = date_utils.timestamp_second()
            time_range = date_utils.get_time_range_from_timestamp_and_timedelta(now, -1)
            start_time, end_time = time_range.end_time, time_range.start_time
        return TransactionTime(start_time=start_time, end_time=end_time)

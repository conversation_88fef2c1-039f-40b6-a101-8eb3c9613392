# -*- coding: utf-8 -*-
import os
import time
import tempfile
import logging
import traceback

import pandas as pd
from datetime import datetime

from google.protobuf import json_format
from collections import namedtuple, defaultdict, OrderedDict

import proto.finance.wallet_pb2 as wallet_pb
import proto.page.merchant_assist.transaction_pb2 as transaction_pb
import proto.page.merchant_assist.order_pb2 as order_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.ordering.registration_pb2 as registration_pb

from business_ops.ordering.shilai_ops_manager import <PERSON><PERSON><PERSON>SManager, Shi<PERSON>PosMerchantManager
from common.email_helper import EmailHelper
from common.utils import date_utils
from common.utils.log_utils import timeit
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.logistics.dada_da_helper import DadaDataAccessHelper
from dao.statistics.merchant_revenue_da_helper import MerchantRevenueDataAccessHelper
from dao.invite_share_da_helper import InviteShareDataAccessHelper
from view_ops.merchant_assist.merchant_assist import MerchantAssist

logger = logging.getLogger(__name__)


class TransactionViewObj(MerchantAssist):

    class PayMethodName(object):
        # miniprogram
        SHILAI = '时来'
        SHILAI_COMBINE = '智能营销买单'
        WECHAT_PAY = '微信'
        ALIPAY = '支付宝'
        SHILAI_MEMBER_CARD_PAY = '储蓄支付'
        COUPON_PAY = "券包支付"
        # POS
        CASH = '现金'

    class OrderSourceName(object):
        MINI_PROGRAM = '扫码点餐'
        NUMBER_PLATE_PAY = '码牌支付'

    def __init__(self, merchant_id, user_id):
        super(TransactionViewObj, self).__init__(merchant_id, user_id)

    def get_order(self, order_id):

        ordering_service_da = OrderingServiceDataAccessHelper()

        order = ordering_service_da.get_order(id=order_id)
        user = UserDataAccessHelper().get_user(order.user_id)
        order_vo = order_pb.MerchantAssistOrderVo()
        transaction = TransactionDataAccessHelper().get_transaction_by_id(order.transaction_id)
        refund_transaction = None
        if order.refund_transaction_id:
            refund_transaction = TransactionDataAccessHelper().get_transaction_by_id(order.refund_transaction_id)

        red_packet_value = 0
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet:
            red_packet_value = red_packet.total_value

        coupon_category = None
        if transaction.use_coupon_id:
            coupon_category = self.__get_coupon_category(transaction.use_coupon_id)

        products = []
        products.extend(order.products)
        for add_products in order.add_products:
            products.extend(add_products.products)
        for product in products:
            dish = ordering_service_da.get_dish(dish_id=str(product.id), merchant_id=order.merchant_id)
            if not dish:
                continue
            dish_vo = order_vo.dish_list.add()
            dish_vo.name = dish.name
            dish_vo.dish_id = dish.id
            dish_vo.quantity = product.quantity
            dish_vo.price = product.price
            dish_vo.discount_price = product.discount_price
            if len(dish.images) > 0:
                dish_vo.image_url = dish.images[0]
            dish_vo.uuid = product.uuid
            dish_vo.parent_uuid = product.parent_uuid
            for attr in product.attrs:
                attr_vo = dish_vo.attrs.add()
                attr_vo.CopyFrom(attr)
            for supply_condiment in product.supply_condiments:
                supply_condiment_vo = dish_vo.supply_condiments.add()
                supply_condiment_vo.CopyFrom(supply_condiment)

        order_vo.serial_number = str(order.serial_number)
        order_vo.transaction.bill_fee = transaction.bill_fee
        order_vo.transaction.paid_fee = transaction.paid_fee
        order_vo.transaction.discount_fee = transaction.bill_fee - transaction.paid_fee - red_packet_value
        if coupon_category is not None:
            order_vo.transaction.discount_fee -= coupon_category.cash_coupon_spec.reduce_cost
            order_vo.transaction.coupon_fee = coupon_category.cash_coupon_spec.reduce_cost
        order_vo.transaction.platform_discount_fee = self.get_order_platform_discount_fee(order)
        order_vo.transaction.create_time = transaction.create_time
        order_vo.transaction.pay_method = transaction.pay_method
        order_vo.transaction.transaction_id = transaction.id
        order_vo.transaction.order_id = order.id
        order_vo.transaction.meal_code = order.meal_code
        table = ordering_service_da.get_table(ordering_service_table_id=order.table_id)
        if table:
            order_vo.transaction.table_name = table.name
        else:
            order_vo.transaction.table_name = "桌台名"
        order_vo.transaction.people_count = order.people_count
        order_vo.meal_type = order.meal_type

        order_vo.shipping_info.logistics_platform = order.logistics_platform
        order_vo.shipping_info.address = "{}{}".format(order.shipping_address.street, order.shipping_address.house_number)
        order_vo.shipping_info.shipping_time = transaction.paid_time
        order_vo.shipping_info.user_phone = order.shipping_address.mobile_phone
        order_vo.shipping_info.username = order.shipping_address.username
        order_vo.shipping_info.gender = order.shipping_address.gender
        order_status = DadaDataAccessHelper().get_order_status(order.id)
        if order_status:
            order_vo.shipping_info.rider_phone = order_status.dm_mobile
            order_vo.shipping_info.rider_name = order_status.dm_name

        fee = transaction.paid_fee
        order_vo.total_fee = transaction.bill_fee

        registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=order.merchant_id)
        order_vo.can_refund = False
        if registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            order_vo.can_refund = True
        if refund_transaction is not None:
            order_vo.can_refund = False

        if refund_transaction is not None:
            order_vo.is_refund = True
        if refund_transaction:
            order_vo.refund_transaction.refund_time = refund_transaction.paid_time
            order_vo.refund_transaction.pay_method = refund_transaction.pay_method
            order_vo.refund_transaction.refund_fee = fee
            order_vo.refund_transaction.platform_discount_fee = self.get_order_platform_discount_fee(order)
            order_vo.refund_transaction.username = user.member_profile.nickname or user.member_profile.name

        order_vo.user_phone = user.member_profile.mobile_phone

        return order_vo

    def get_order_list(self, meal_type, last_paid_time=None, start_paid_time=None, end_paid_time=None, size=10):
        if meal_type:
            meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
        if meal_type == dish_pb.DishOrder.EAT_IN:
            meal_type = [dish_pb.DishOrder.EAT_IN, dish_pb.DishOrder.KERUYUN_FAST_FOOD]
        orders = OrderingServiceDataAccessHelper().get_orders(
            merchant_id=self._merchant.id, size=size, orderby=[("paidTime", -1)], last_paid_time=last_paid_time,
            start_paid_time=start_paid_time, end_paid_time=end_paid_time,
            status=[dish_pb.DishOrder.PAID, dish_pb.DishOrder.POS_RETURN], meal_type=meal_type)
        user_da = UserDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()
        ordering_service_da = OrderingServiceDataAccessHelper()
        red_packet_da = RedPacketDataAccessHelper()
        order_list = []
        for order in orders:
            order_vo = order_pb.MerchantAssistOrderListVo()
            order_vo.order_id = order.id
            order_vo.transaction_id = order.transaction_id
            order_vo.serial_number = str(order.serial_number)
            order_vo.refund_transaction_id = order.refund_transaction_id
            order_vo.meal_type = order.meal_type
            user = user_da.get_user(order.user_id)
            if not user:
                continue
            order_vo.username = user.wechat_profile.nickname
            order_vo.address = "{} {}".format(order.shipping_address.street, order.shipping_address.house_number)
            order_vo.create_time = order.create_time
            if user.member_profile.mobile_phone:
                order_vo.user_phone = user.member_profile.mobile_phone
            transaction = transaction_da.get_transaction_by_id(order.transaction_id)
            if order.status == dish_pb.DishOrder.PAID:
                transaction = transaction_da.get_transaction_by_id(order.transaction_id)
            elif order.status == dish_pb.DishOrder.POS_RETURN:
                transaction = transaction_da.get_transaction_by_id(order.refund_transaction_id)
            if not transaction:
                continue
            order_vo.paid_fee = transaction.paid_fee
            order_vo.bill_fee = transaction.bill_fee
            order_vo.paid_time = order.paid_time
            order_vo.date = date_utils.convert_timestamp_to_date(order_vo.paid_time)
            order_vo.status = order.status
            order_vo.meal_code = order.meal_code
            table = ordering_service_da.get_table(ordering_service_table_id=order.table_id)
            if table:
                order_vo.table_name = table.name

            red_packet = red_packet_da.get_red_packet(new_transaction_id=order.transaction_id)
            if red_packet:
                order_vo.red_packet_value = red_packet.total_value

            order_vo.discount_amount = order_vo.bill_fee - (order_vo.paid_fee - order_vo.red_packet_value)
            order_vo.coupon_fee = order.coupon_fee
            order_vo.platform_discount_fee = self.get_order_platform_discount_fee(order)
            # 如果是储值用户用余额进行消费，则商家实收为0(不计入打款)
            if transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
                order_vo.merchant_paid_in_fee = 0
            elif order.paid_in_fee > 0:
                order_vo.merchant_paid_in_fee = order.paid_in_fee
            order_vo.pay_method = transaction.pay_method
            self.set_order_user_type(order_vo, transaction, order=order)

            order_list.append(order_vo)
        return order_list

    def set_order_user_type(self, order_vo, transaction, order=None):
        if transaction.use_coupon_id == "":
            return
        invite_share_da = InviteShareDataAccessHelper()
        coupon_id = transaction.use_coupon_id
        if not coupon_id:
            return
        invitee_share = invite_share_da.get_invitee_share(coupon_id=coupon_id)
        if not invitee_share:
            return
        if invitee_share.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
            order_vo.order_user_type = order_pb.MerchantAssistOrderListVo.INVITE_SHARE_NEW_USER
        elif invitee_share.user_type == coupon_category_pb.InviteShareCouponCategory.OLD_USER:
            order_vo.order_user_type = order_pb.MerchantAssistOrderListVo.INVITE_SHARE_OLD_USER
        if order is not None and order.is_phone_member_pay:
            order_vo.order_user_type = order_pb.MerchantAssistOrderListVo.PHONE_MEMBER_PAY

    def _get_transaction_list_vo(self, transactions):
        transaction_list_vo = transaction_pb.MerchantAssistTransactionListVo()
        ordering_da = OrderingServiceDataAccessHelper()
        user_da = UserDataAccessHelper()
        for transaction in transactions:
            transaction_vo = transaction_list_vo.transactions.add()
            transaction_vo.user_id = transaction.payer_id
            transaction_vo.transaction_id = transaction.id
            transaction_vo.bill_fee = transaction.bill_fee
            transaction_vo.merchant_paid_in_fee = transaction.paid_fee
            order = ordering_da.get_order(transaction_id=transaction.id)
            if order:
                transaction_vo.merchant_paid_in_fee = order.paid_in_fee
                transaction_vo.platform_discount_fee = order.platform_discount_fee
                self.set_order_user_type(transaction_vo, transaction)
            transaction_vo.discount_amount = transaction.bill_fee - transaction.paid_fee
            user = user_da.get_user(transaction_vo.user_id)
            if user:
                transaction_vo.name = user.member_profile.nickname or user.member_profile.name
                transaction_vo.username = user.member_profile.name
            transaction_vo.paid_time = transaction.paid_time
            transaction_vo.date = date_utils.convert_timestamp_to_date(transaction_vo.paid_time)
            if transaction.use_coupon_id:
                coupon_category = self.__get_coupon_category(transaction.use_coupon_id)
                if coupon_category:
                    transaction_vo.least_cost = coupon_category.cash_coupon_spec.least_cost
                    transaction_vo.reduce_cost = coupon_category.cash_coupon_spec.reduce_cost
            red_packet = self.__get_red_packet(transaction)
            if red_packet:
                transaction_vo.red_packet_fee = red_packet.total_value
                transaction_vo.discount_amount += red_packet.total_value
            transaction_vo.paid_fee = transaction.paid_fee
        return transaction_list_vo

    def _convert_transaction_to_vo(self, transaction, transaction_vo):
        """
        字段:
            string user_id = 1;
            string transaction_id = 2;
            string name = 3;
            int64 paid_time = 4;
            int32 least_cost = 5;
            int32 reduce_cost = 6;
            int32 red_packet_fee = 7;
            int32 paid_fee = 8;
            string date = 9;
            string sort_date = 10;
            int32 bill_fee = 11;
            // 商户实收
            int32 merchant_paid_in_fee = 12;
            // 平台补贴
            int32 platform_discount_fee = 13;
            OrderUserType order_user_type = 24;
            int32 discount_amount = 25;
            string username = 26;
        Args:
            transaction:
        Returns:
        """
        order = None
        flag = 1
        orig_transaction = transaction
        if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            order = self.ordering_dao.get_order(transaction_id=transaction.id)
        if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
            order = self.ordering_dao.get_order(refund_transaction_id=transaction.id)
            flag = -1
        transaction_vo.user_id = transaction.payer_id
        transaction_vo.transaction_id = transaction.id
        transaction_vo.bill_fee = abs(transaction.bill_fee)
        transaction_vo.merchant_paid_in_fee = transaction.paid_fee
        if order:
            transaction_vo.merchant_paid_in_fee = order.paid_in_fee * flag
            transaction_vo.platform_discount_fee = order.platform_discount_fee * flag
            self.set_order_user_type(transaction_vo, transaction)
            if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                orig_transaction = self.transaction_dao.get_transaction_by_id(order.transaction_id)
        # 如果是储值用户用余额进行消费，则商家实收为0(不计入打款)
        if transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            transaction_vo.merchant_paid_in_fee = 0
        transaction_vo.discount_amount = flag * (abs(transaction.bill_fee) - abs(transaction.paid_fee))
        user = self.user_dao.get_user(transaction_vo.user_id)
        if user:
            transaction_vo.name = user.member_profile.nickname or user.member_profile.name
            transaction_vo.username = user.member_profile.name or user.member_profile.nickname
        transaction_vo.paid_time = transaction.paid_time
        transaction_vo.date = date_utils.convert_timestamp_to_date(transaction_vo.paid_time)
        if orig_transaction.use_coupon_id:
            coupon_category = self.__get_coupon_category(orig_transaction.use_coupon_id)
            if coupon_category:
                transaction_vo.least_cost = coupon_category.cash_coupon_spec.least_cost * flag
                transaction_vo.reduce_cost = coupon_category.cash_coupon_spec.reduce_cost * flag
        red_packet = self.__get_red_packet(orig_transaction)
        if red_packet:
            transaction_vo.red_packet_fee = red_packet.total_value * flag
            transaction_vo.discount_amount += red_packet.total_value * flag
        transaction_vo.paid_fee = transaction.paid_fee
        return transaction_vo

    def _get_transaction_list_vo_2(self, transactions):
        """
        """
        transaction_list_vo = transaction_pb.MerchantAssistTransactionListVo()
        ordering_da = OrderingServiceDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()
        user_da = UserDataAccessHelper()
        for transaction in transactions:
            orig_transaction = transaction
            transaction_vo = transaction_list_vo.transactions.add()
            order = None
            flag = 1
            if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
                order = ordering_da.get_order(transaction_id=transaction.id)
            if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                order = ordering_da.get_order(refund_transaction_id=transaction.id)
                flag = -1
            transaction_vo.user_id = transaction.payer_id
            transaction_vo.transaction_id = transaction.id
            transaction_vo.bill_fee = abs(transaction.bill_fee)
            transaction_vo.merchant_paid_in_fee = transaction.paid_fee
            if order:
                transaction_vo.merchant_paid_in_fee = order.paid_in_fee * flag
                transaction_vo.platform_discount_fee = order.platform_discount_fee * flag
                self.set_order_user_type(transaction_vo, transaction)
                if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                    orig_transaction = transaction_da.get_transaction_by_id(order.transaction_id)
            # 如果是储值用户用余额进行消费，则商家实收为0(不计入打款)
            if transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
                transaction_vo.merchant_paid_in_fee = 0
            transaction_vo.discount_amount = flag * (abs(transaction.bill_fee) - abs(transaction.paid_fee))
            user = user_da.get_user(transaction_vo.user_id)
            if user:
                transaction_vo.name = user.member_profile.nickname or user.member_profile.name
                transaction_vo.username = user.member_profile.name or user.member_profile.nickname
            transaction_vo.paid_time = transaction.paid_time
            transaction_vo.date = date_utils.convert_timestamp_to_date(transaction_vo.paid_time)
            if orig_transaction.use_coupon_id:
                coupon_category = self.__get_coupon_category(orig_transaction.use_coupon_id)
                if coupon_category:
                    transaction_vo.least_cost = coupon_category.cash_coupon_spec.least_cost * flag
                    transaction_vo.reduce_cost = coupon_category.cash_coupon_spec.reduce_cost * flag
            red_packet = self.__get_red_packet(orig_transaction)
            if red_packet:
                transaction_vo.red_packet_fee = red_packet.total_value * flag
                transaction_vo.discount_amount += red_packet.total_value * flag
            transaction_vo.paid_fee = transaction.paid_fee
        return transaction_list_vo

    @property
    def transaction_dao(self):
        return TransactionDataAccessHelper()

    @property
    def user_dao(self):
        return UserDataAccessHelper()

    @property
    def merchant_dao(self):
        return MerchantDataAccessHelper()

    @property
    def ordering_dao(self):
        return OrderingServiceDataAccessHelper()

    def get_member_card_recharge_list(self, last_paid_time=None, size=20):
        transaction_da = TransactionDataAccessHelper()
        multi_types = [
            wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE,
            wallet_pb.Transaction.MEMBER_CARD_RECHARGE_REFUND
        ]
        recharges = transaction_da.get_transactions(
            payee_id=self._merchant.id, state=wallet_pb.Transaction.SUCCESS,
            multi_types=multi_types, last_paid_time=last_paid_time, size=size, orderby=[("paidTime", -1)])
        vo_list = self._get_transaction_list_vo(recharges)
        vo_list.transactions.sort(key=lambda x: x.paid_time, reverse=True)
        return vo_list

    def get_whole_order_list(self, last_paid_time=None, size=10):
        manager = ShilaiOPSManager(merchant_id=self._merchant.id)
        transaction_list_vo = transaction_pb.MerchantAssistTransactionListVo()
        transaction_list = manager.get_transaction_pagination(last_paid_time, size=size)
        logger.info(f"get_transaction_for_mini_program({last_paid_time}): {len(transaction_list)}")
        order_ids = [row['orderId'] for row in transaction_list]
        mini_program_order_mapping = dict()   # { orderId: tranVo}
        if order_ids:
            for order in self.ordering_dao.get_orders(merchant_id=self._merchant.id, pos_order_ids=order_ids):
                transaction = self.transaction_dao.get_transaction_by_id(order.transaction_id)
                if order.ordering_service_order_id and transaction:
                    mini_program_order_mapping[order.ordering_service_order_id] = transaction
        logger.info(f"mini_program_order_mapping: {mini_program_order_mapping}")
        for transaction in transaction_list:
            transaction_vo = transaction_list_vo.transactions.add()
            if transaction['orderId'] in mini_program_order_mapping:
                self._convert_transaction_to_vo(mini_program_order_mapping[transaction['orderId']], transaction_vo)
            else:
                transaction_vo.user_id = ''
                transaction_vo.name = '收银机点单'
                transaction_vo.username = '收银机点单'
                transaction_vo.paid_fee = transaction['paidFee']
                transaction_vo.paid_time = transaction['paidTime']
                transaction_vo.date = transaction['date']
                transaction_vo.transaction_id = transaction['transactionId']
                transaction_vo.bill_fee = transaction.get('billFee') or 0
                transaction_vo.platform_discount_fee = transaction.get('platformDiscountFee') or 0
                transaction_vo.merchant_paid_in_fee = transaction.get('merchantPaidInFee') or 0
                transaction_vo.discount_amount = transaction.get('discountAmount') or 0
        return transaction_list_vo

    def _get_settlement_rate(self):
        merchant = self.merchant_dao.get_merchant_by_id(self._merchant.id)
        if merchant:
            return merchant.payment_info.settlement_rate

    @property
    def merchant_config(self):
        merchant = self.ordering_dao.get_registration_info(self._merchant.id)
        return merchant

    @property
    def dinner_type(self):
        return self.merchant_config.ordering_config.dinner_type

    @property
    def has_pos_service(self):
        return self.merchant_config.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI

    def get_transaction_list(self, last_paid_time):
        multi_types = [
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
            wallet_pb.Transaction.SELF_DINING_PAYMENT,
            wallet_pb.Transaction.ORDERING_REFUND
        ]
        transactions = TransactionDataAccessHelper().get_transactions(
            payee_id=self._merchant.id, state=wallet_pb.Transaction.SUCCESS, last_paid_time=last_paid_time, size=10,
            multi_types=multi_types, orderby=[("paidTime", -1)])
        return self._get_transaction_list_vo_2(transactions)

    def get_transaction_list_by_type(self, last_paid_time, type):
        type = wallet_pb.Transaction.TransactionType.Value(type)
        transactions = TransactionDataAccessHelper().get_transactions(
            payee_id=self._merchant.id, state=wallet_pb.Transaction.SUCCESS, type=type, last_paid_time=last_paid_time, size=10,
            orderby=[("paidTime", -1)])
        return self._get_transaction_list_vo(transactions)

    def get_orders(self, start_date, end_date, type, meal_type):
        transaction_time = self._calculate_transactions_time(start_date, end_date, type)
        start_time, end_time = transaction_time.start_time, transaction_time.end_time
        ordering_da = OrderingServiceDataAccessHelper()
        if meal_type is not None:
            meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
        orders = ordering_da.get_orders(
            merchant_id=self._merchant.id, start_paid_time=start_time, end_paid_time=end_time, meal_type=meal_type,
            status=[dish_pb.DishOrder.PAID, dish_pb.DishOrder.POS_RETURN])
        order_vo = order_pb.MerchantAssistOrderInfoVo()

        # 从开始至今订单总数
        total_order_num = ordering_da.count_orders(
            merchant_id=self._merchant.id, meal_type=meal_type, status=[dish_pb.DishOrder.PAID, dish_pb.DishOrder.POS_RETURN])

        # 时间段内新增订单
        incr_order_num = ordering_da.count_orders(
            merchant_id=self._merchant.id, meal_type=meal_type, start_time=start_time,
            end_time=end_time, status=[dish_pb.DishOrder.PAID, dish_pb.DishOrder.POS_RETURN])

        # 开业至今所有订单总数
        order_vo.total_order_num = total_order_num
        # start_date,end_date时间段内所有订单总数
        order_vo.incr_order_num = incr_order_num

        date_ranges = date_utils.get_date_range(start_time, end_time)
        self.__get_order_info_by_day(order_vo, orders, date_ranges)

        date_ranges = date_utils.get_time_ranges(start_time, end_time)
        if len(date_ranges) > 0:
            if len(date_ranges) == 1:
                order_vo.date_range = date_ranges[0].start_date
            else:
                order_vo.date_range = "{} - {}".format(date_ranges[0].start_date, date_ranges[-1].end_date)
        return order_vo

    def __get_order_info_by_day(self, order_vo, orders, date_ranges):
        _orders_info = {}
        for date in date_ranges:
            _order_info = order_vo.orders_by_day.add()
            _order_info.date = date.date
            _order_info.sort_date = date.sort_date
            _orders_info.update({date.date: _order_info})

        for _order in orders:
            transaction = TransactionDataAccessHelper().get_transaction_by_id(_order.transaction_id)
            if not transaction:
                continue
            _paid_date = datetime.fromtimestamp(_order.create_time).strftime("%m-%d")
            _order_info = _orders_info.get(_paid_date)
            if not _order_info:
                continue
            if _order.status == dish_pb.DishOrder.PAID:
                _order_info.num += 1
            else:
                _order_info.num += 1
            _orders_info.update({
                _paid_date: _order_info
            })
        order_vo.orders_by_day.sort(key = lambda x: x.sort_date)

    def get_day_revenues(self, start_date, end_date, type):
        transaction_time = self._calculate_transactions_time(start_date, end_date, type)
        start_time, end_time = transaction_time.start_time, transaction_time.end_time
        logger.info("get_transactions: start_time: {}, end_time: {}".format(start_time, end_time))
        day_revenue_da = MerchantRevenueDataAccessHelper()
        day_revenues = day_revenue_da.get_day_revenue(merchant_id=self._merchant.id, date=[start_date, end_date])
        transaction_vo = transaction_pb.MerchantAssistTransactionVo()
        order_count = 0
        for day_revenue in day_revenues:
            transaction_vo.total_bill_fee_amount += day_revenue.total_bill_fee
            transaction_vo.total_paid_fee_amount += day_revenue.total_paid_in_fee
            order_count += day_revenue.order_count
        if order_count > 0:
            transaction_vo.bill_fee_per_user = int(transaction_vo.total_bill_fee_amount / float(order_count) + 0.5)
        else:
            transaction_vo.bill_fee_per_user = 0
        transaction_vo.merchant_id = self._merchant.id
        transaction_vo.store_id = "{}_0".format(self._merchant.id)
        transaction_vo.total_payment = order_count
        transaction_vo.logo_url = self._merchant.basic_info.logo_url
        transaction_vo.name = self._merchant.basic_info.display_name

    def get_order_product_nums(self, order):
        if not order:
            return 0
        total = 0
        for product in order.products:
            total += product.quantity
        for add_products in order.add_products:
            for product in add_products.products:
                total += product.quantity
        return total

    def deal_with_self_dining_payment_transaction(self, transaction, transaction_vo):
        transaction_vo.total_bill_fee_amount += transaction.bill_fee
        transaction_vo.total_market_bill_fee_amount += transaction.bill_fee
        # transaction_vo.total_paid_fee_amount += payment.paid_in_fee

    def merge_pos_and_mini_program(self, mini_mappings, pos_mappings):
        for pay_method in pos_mappings:
            fee_list = pos_mappings[pay_method]
            # not_change = ['ALIPAY', 'WECHAT_PAY', 'COUPON_PAY', 'CASH']
            # if pay_method in not_change:
            #     mini_mappings[pay_method] = mini_mappings.get(pay_method) or []
            #     mini_mappings[pay_method].extend(fee_list)
            # elif pay_method == 'WECHAT_PAY':
            #     mini_mappings['WECHAT_PAY'] = mini_mappings.get('WECHAT_PAY') or []
            #     mini_mappings['WECHAT_PAY'].extend(fee_list)
            # elif pay_method == 'COUPON_PAY':
            #     mini_mappings['COUPON_PAY'] = mini_mappings.get('COUPON_PAY') or []
            #     mini_mappings['COUPON_PAY'].extend(fee_list)
            # elif pay_method == 'CASH':
            #     mini_mappings['CASH'] = mini_mappings.get('CASH') or []
            #     mini_mappings['CASH'].extend(fee_list)
            if pay_method == 'SHILAI_FANPIAO':
                mini_mappings['SHILAI_COMBINE'] = mini_mappings.get('SHILAI_COMBINE') or []
                mini_mappings['SHILAI_COMBINE'].extend(fee_list)
            else:
                mini_mappings[pay_method] = mini_mappings.get(pay_method) or []
                mini_mappings[pay_method].extend(fee_list)
        return mini_mappings

    def _get_valid_transaction_list(self, start_date, end_date, type):
        """
        排除 orders
        Args:
            start_date:
            end_date:
            type:

        Returns:

        """
        transaction_time = self._calculate_transactions_time(start_date, end_date, type)
        start_time, end_time = transaction_time.start_time, transaction_time.end_time
        logger.info("get_transactions: start_time: {}, end_time: {}".format(start_time, end_time))
        transaction_types = [
            wallet_pb.Transaction.SELF_DINING_PAYMENT,
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
            wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT,
            wallet_pb.Transaction.ORDERING_REFUND,
            wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE
        ]
        return start_time, end_time, TransactionDataAccessHelper().get_transactions(
            payee_id=self._merchant.id, state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time,
            multi_types=transaction_types)

    def __group_transaction_by_pay_method(self, pay_method_mappings):
        """
        组织两层结构
        """
        def _format_data(_method, total_payment=0, total_paid_fee=0):
            return {
                'payMethod': _method, 'totalPayment': total_payment,
                'totalPaidFeeAmount': total_paid_fee, 'details': []
            }
        print("pay_method_mappings: ", pay_method_mappings)
        top_layer = {
            self.PayMethodName.SHILAI: _format_data(self.PayMethodName.SHILAI),
        }
        for pay_method in pay_method_mappings:
            fee_list = pay_method_mappings[pay_method]
            total_payment = len(fee_list)
            total_paid_fee = sum(fee_list)
            if pay_method == 'SHILAI_MEMBER_CARD_PAY':
                top_layer[self.PayMethodName.SHILAI_MEMBER_CARD_PAY] = _format_data(
                    self.PayMethodName.SHILAI_MEMBER_CARD_PAY, total_payment, total_paid_fee)
                top_layer[self.PayMethodName.SHILAI_MEMBER_CARD_PAY]['details'].append(_format_data(
                    self.PayMethodName.SHILAI_MEMBER_CARD_PAY, total_payment, total_paid_fee))
            elif pay_method == 'CASH':
                top_layer[self.PayMethodName.CASH] = _format_data(
                    self.PayMethodName.CASH, total_payment, total_paid_fee)
                top_layer[self.PayMethodName.CASH]['details'].append(_format_data(
                    self.PayMethodName.CASH, total_payment, total_paid_fee))
            elif pay_method == 'SHILAI_COMBINE':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.SHILAI_COMBINE, total_payment, total_paid_fee))
            elif pay_method == 'COUPON_PAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.COUPON_PAY, total_payment, total_paid_fee))
            elif pay_method == 'WECHAT_PAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.WECHAT_PAY, total_payment, total_paid_fee))
            elif pay_method == 'ALIPAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.ALIPAY, total_payment, total_paid_fee))
            else:
                top_layer[pay_method] = _format_data(pay_method, total_payment, total_paid_fee)
                top_layer[pay_method]['details'].append(_format_data(pay_method, total_payment, total_paid_fee))
        results = []
        for pay_method in top_layer:
            if len(top_layer[pay_method]['details']) > 1:
                top_layer[pay_method]['details'] = sorted(
                    top_layer[pay_method]['details'], key=lambda x: x['totalPaidFeeAmount'], reverse=True)
            results.append(top_layer[pay_method])
        return results

    @classmethod
    def to_json(cls, obj, columns=None):
        if not obj:
            return {}
        origin = json_format.MessageToDict(obj, including_default_value_fields=True)
        if columns and isinstance(columns, list):
            return {key: value for key, value in origin.items() if key in columns}
        return origin

    def wrap_transaction_info(self, mp_transaction_vo, start_date, end_date, date_type):
        transaction_time = self._calculate_transactions_time(start_date, end_date, date_type)
        start_time, end_time = int(transaction_time.start_time), int(transaction_time.end_time)
        mp_transaction_vo['startTime'] = start_time
        mp_transaction_vo['endTime'] = end_time
        logger.info(f"get_transaction_info: {start_time} ~ {end_time}")
        manager = ShilaiOPSManager(merchant_id=self._merchant.id)
        summary = manager.get_transaction_summary(start_time, end_time)  # only POS
        for field in ['totalBillFeeAmount', 'totalBillFeeAmount', 'totalMarketBillFeeAmount',
                      'totalPaidFeeAmount', 'totalPayment', 'productNum']:
            mp_transaction_vo[field] = mp_transaction_vo.get(field) or 0
            mp_transaction_vo[field] += summary.get(field) or 0
        mp_transaction_vo['billFeePerUser'] = int(mp_transaction_vo['totalMarketBillFeeAmount'] / mp_transaction_vo['totalPayment']) \
            if mp_transaction_vo['totalPayment'] else 0
        day2nums = defaultdict(dict)
        logger.info(f"summary['transactionsByDay']: {summary['transactionsByDay']}")
        for item in summary['transactionsByDay']:
            day2nums[item['date']] = {'paid': item.get('totalPaidFeeAmount') or 0, 'bill': item.get('totalBillFeeAmount') or 0}
        logger.info(f"mp_transaction_vo['transactionsByDay']: {mp_transaction_vo['transactionsByDay']}")
        for item in mp_transaction_vo['transactionsByDay']:
            day2nums[item['date']]['bill'] = day2nums[item['date']].get('bill') or 0 + item.get('totalBillFeeAmount') or 0
            day2nums[item['date']]['paid'] = day2nums[item['date']].get('paid') or 0 + item.get('totalPaidFeeAmount') or 0
        logger.debug(f"day2nums: {day2nums}")
        merge_daily = []
        for day in day2nums:
            merge_daily.append({
                'date': day, 'totalBillFeeAmount': day2nums[day]['bill'], 'totalPaidFeeAmount': day2nums[day]['paid']
            })
        mp_transaction_vo['transactionsByDay'] = sorted(merge_daily, key=lambda x: x['date'])
        logger.debug(f"merge_daily: {merge_daily}")
        try:
            mp_transaction_vo["transactionsByPayMethod"] = self.__group_transaction_by_pay_method(
                self.merge_pos_and_mini_program(
                    mp_transaction_vo.get('rawPayMethodMappings') or {},
                    summary.get('transactionsByPayMethod') or {}
                )
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            mp_transaction_vo["transactionsByPayMethod"] = {"error": str(e)}
        return mp_transaction_vo

    def _compute_unpaid_orders(self, transaction_vo, start_time, end_time):
        _unpaid_orders = self.ordering_dao.get_orders(
            merchant_id=self._merchant.id, start_paid_time=start_time, end_paid_time=end_time,
            status=dish_pb.DishOrder.APPROVED
        )
        transaction_vo.total_unpaid_fee_amount = sum([o.bill_fee for o in _unpaid_orders])
        transaction_vo.total_unpaid_payment = len(_unpaid_orders)
        return transaction_vo

    @timeit
    def get_summary_by_date_range(self, start_date, end_date, date_type, start_time, end_time):
        """
        实收总额
        营收总额
        优惠金额
        订单数
        Args:
            start_date: 左开右开
            end_date: 左开右开
            start_time: 开始时间
            end_time: 结束时间
        Returns: dict, {}
        """
        if start_time and end_time:
            start_time, end_time = int(start_time), int(end_time)
        else:
            transaction_time = self._calculate_transactions_time(start_date, end_date, date_type)
            start_time, end_time = int(transaction_time.start_time), int(transaction_time.end_time)
        transaction_vo = transaction_pb.MerchantAssistTransactionVoV2()
        transaction_vo.type = transaction_pb.MerchantAssistTransactionVoV2.Type.Value(date_type)
        transaction_vo.payment_summary.total_paid_fee_amount = 0
        self._get_merchant_info(transaction_vo)
        self._compute_transaction_fee(transaction_vo, start_time, end_time)
        self._compute_member_card_fee(transaction_vo, start_time, end_time)
        self._compute_unpaid_orders(transaction_vo, start_time, end_time)
        # 补充储值支付
        for pay_item in transaction_vo.transactions_by_pay_method:
            if pay_item.pay_method == 'SHILAI_MEMBER_CARD_PAY':
                member_card_item = transaction_vo.transactions_by_member_card.add()
                member_card_item.source = '储值支付'
                member_card_item.total_paid_fee_amount = pay_item.total_paid_fee_amount
                member_card_item.total_payment = pay_item.total_payment
                transaction_vo.total_recharge_paid_fee = pay_item.total_paid_fee_amount
        # 结算金额
        settlement_rate = self._get_settlement_rate()
        if settlement_rate:
            bank_remove_fee = int(transaction_vo.payment_summary.total_paid_fee_amount * settlement_rate)
            transaction_vo.payment_summary.bank_remove_fee = bank_remove_fee
            transaction_vo.payment_summary.total_settlement_amount = \
                transaction_vo.payment_summary.total_paid_fee_amount - bank_remove_fee
        return self.to_json(transaction_vo)

    @property
    def pos_merchant_gateway(self):
        return ShilaiPosMerchantManager(merchant_id=self._merchant.id)

    @timeit
    def _get_merchant_info(self, transaction_vo):
        transaction_vo.merchant_id = self._merchant.id
        transaction_vo.name = self._merchant.stores[0].name
        transaction_vo.store_id = self._merchant.stores[0].id
        transaction_vo.dinner_type = self.dinner_type
        transaction_vo.enable_shilai_member_card_recharge = \
            self._merchant.stores[0].enable_shilai_member_card_recharge \
            or self._merchant.stores[0].enable_shilai_member_card_pay
        if self.has_pos_service:
            store_setting = self.pos_merchant_gateway.get_store_setting()
            if store_setting:
                member_config = store_setting.get('storeMemberAccountRechargeConfig', {})
                if member_config:
                    transaction_vo.enable_shilai_member_card_recharge = \
                        member_config['enableMiniProgramRecharge'] or \
                        member_config['enableMiniProgramMemberAccountPay'] or \
                        member_config['enablePosRecharge'] or \
                        member_config['enablePosMemberAccountPay']
                else:
                    transaction_vo.enable_shilai_member_card_recharge = False
        transaction_vo.logo_url = self._merchant.basic_info.logo_url
        return transaction_vo

    def _group_transaction_by_discount(self, transaction_vo):
        """
        时来优惠: 券包, 红包, 饭票
        """
        discount_item = transaction_vo.transactions_by_discount.add()
        discount_item.source = '时来优惠'
        discount_item.total_discount_fee_amount = transaction_vo.total_discount_fee_amount
        # discount_item.total_discount_fee_amount = sum([
        #     transaction_vo.payment_summary.fanpiao_discount_fee,  # 包含了券包优惠和时来补贴, 所以 要去掉
        #     transaction_vo.payment_summary.red_packet_fee,
        #     - transaction_vo.payment_summary.platform_discount_fee
        # ])  # 总优惠金额
        return transaction_vo

    def _compute_member_card_fee(self, transaction_vo, start_time, end_time):
        """
        单独的逻辑: 计算储值新增
        Args:
            transaction_vo:
            start_time:
            end_time:
        """
        if not self._merchant:
            return transaction_vo
        multi_types = [
            wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE,
            wallet_pb.Transaction.MEMBER_CARD_RECHARGE_REFUND
        ]
        state = wallet_pb.Transaction.SUCCESS
        transactions = self.transaction_dao.get_transactions(
            payee_id=self._merchant.id, multi_types=multi_types, state=state,
            start_time=start_time, end_time=end_time
        )
        logger.info(f"Count: {len(transactions)}")
        _success = [tran.paid_fee for tran in transactions if tran.type == multi_types[0]]
        _refund = [tran.paid_fee for tran in transactions if tran.type == multi_types[1]]
        recharge_fee = sum(_success) - sum(_refund)
        recharge_count = len(_success) - len(_refund)
        transaction_vo.total_recharge_fee = recharge_fee  # 新增储值
        member_card_item = transaction_vo.transactions_by_member_card.add()
        member_card_item.source = '新增储值'
        member_card_item.total_paid_fee_amount = recharge_fee
        member_card_item.total_payment = recharge_count
        logger.info(f"[*] _compute_member_card_fee from {start_time} to {end_time} : {member_card_item}")
        return transaction_vo

    def _compute_transaction_fee(self, transaction_vo, start_time, end_time):
        """
        {type: 1, paidFee: 1, payeeId: 1, state: 1, paidTime: 1, payMethod: 1, id: 1, billFee: 1, refund_transaction_id: 1, orderId: 1}
        todo: 加一个随机立减: discountAmount
        todo: 加一个券包优惠: couponFee
        todo: red_packet_fee 从 transaction 的 red_packet 中取, 红包返现, 这个 需要单独存起来 by zhaopei
        todo: fanpiao_commission_fee 佣金, 单独列在优惠 -> 时来佣金 , 而且是负的
        packageFee: 红包优惠
        Count all PAID orders:
            - totalBillFeeAmount:
            - totalMarketBillFeeAmount: deprecated
            - totalPaidFeeAmount: from paidInFee(实收)
            - totalPayment:
        Args:
            start_time:
            end_time:
        Returns:
        """
        run_time = time.time()
        _paid_orders = self.ordering_dao.get_orders(
            merchant_id=self._merchant.id, start_paid_time=start_time, end_paid_time=end_time,
            status=dish_pb.DishOrder.PAID
        )
        logger.info(f"[*] self.ordering_dao.get_orders: {time.time() - run_time} {len(_paid_orders)}")
        run_time = time.time()
        tran_ids = [o.transaction_id for o in _paid_orders]
        tran_id2obj = {t.id: t for t in self.transaction_dao.get_transactions(ids=tran_ids)}
        columns = ['id', 'billFee', 'paidInFee', 'platformDiscountFee', 'paidTime', 'discountAmount',
                   'couponFee', 'transactionId', 'fanpiaoCommissionFee', 'couponPackageCommissionFee']
        logger.info(f"[*] self.transaction_dao.get_transactions: {time.time() - run_time}")
        run_time = time.time()
        paid_orders = list()
        for order in _paid_orders:
            o_json = self.to_json(order, columns=columns)
            o_json['productNum'] = len(order.products)
            o_json['date'] = datetime.fromtimestamp(int(order.paid_time)).strftime("%Y-%m-%d")
            transaction = tran_id2obj.get(order.transaction_id)
            o_json['transactionBillFee'] = 0
            o_json['transactionPaidFee'] = 0
            o_json['redPacketFee'] = 0
            o_json['fanpiaoFee'] = 0
            o_json['payMethod'] = ''
            o_json['source'] = self.OrderSourceName.MINI_PROGRAM
            o_json['couponPackageCommissionFee'] = o_json.get('couponPackageCommissionFee') or 0
            if transaction:
                # 码牌支付
                if transaction.type == wallet_pb.Transaction.TransactionType.DIRECT_PAY:
                    o_json['source'] = self.OrderSourceName.NUMBER_PLATE_PAY
                raw_pay_method = wallet_pb.Transaction.PayMethod.Name(transaction.pay_method)
                o_json['transactionPaidFee'] = transaction.paid_fee
                o_json['transactionBillFee'] = transaction.bill_fee
                # 填充 payMethod
                if raw_pay_method in ['FANPIAO_PAY', 'WALLET'] or transaction.use_coupon_id:
                    o_json['payMethod'] = 'SHILAI_COMBINE'
                elif 'SHILAI_MEMBER_CARD_PAY' == raw_pay_method:
                    o_json['payMethod'] = "SHILAI_MEMBER_CARD_PAY"
                # elif transaction.use_coupon_id:
                #     # 券包支付
                #     o_json['payMethod'] = "COUPON_PAY"
                elif transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                    o_json['payMethod'] = "WECHAT_PAY"
                elif transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                    o_json['payMethod'] = "ALIPAY"
                else:
                    o_json['payMethod'] = raw_pay_method
                o_json['fanpiaoFee'] = transaction.bill_fee - transaction.paid_fee
                red_packet = self.__get_red_packet(transaction)
                o_json['redPacketFee'] = red_packet.total_value if red_packet else 0
            # 从transaction 获取 red_packet_fee 和 pay_method
            paid_orders.append(o_json)
        logger.info(f"[*] processing: {time.time() - run_time}")
        run_time = time.time()
        if not paid_orders:
            return transaction_vo
        df = pd.DataFrame(paid_orders)
        exclude_member_pay = df[df['payMethod'] != 'SHILAI_MEMBER_CARD_PAY']
        logger.info(f"df.shape: {df.shape}; exclude member: {exclude_member_pay.shape}")
        transaction_vo.total_payment = len(paid_orders)
        transaction_vo.product_num = int(df['productNum'].sum())   # 营收总额
        transaction_vo.total_bill_fee_amount = int(df['billFee'].sum())   # 营收总额
        transaction_vo.total_paid_fee_amount = int(df['paidInFee'].sum())  # 第二天打钱总额
        transaction_vo.bill_fee_per_user = int(transaction_vo.total_bill_fee_amount / transaction_vo.total_payment) \
            if transaction_vo.total_payment else 0
        transaction_vo.payment_summary.total_bill_fee_amount = int(exclude_member_pay['transactionBillFee'].sum())  # 要把储值支付过滤掉
        transaction_vo.payment_summary.total_paid_fee_amount = int(exclude_member_pay['paidInFee'].sum())
        transaction_vo.payment_summary.total_customer_fee_amount = int(exclude_member_pay['transactionPaidFee'].sum())
        transaction_vo.payment_summary.fanpiao_discount_fee = int(exclude_member_pay['fanpiaoFee'].sum())  # 饭票优惠 tran_bill - tran_paid_fee
        transaction_vo.payment_summary.red_packet_fee = int(exclude_member_pay['redPacketFee'].sum())  # 红包优惠
        transaction_vo.payment_summary.random_discount = int(exclude_member_pay['discountAmount'].sum())   # 随机立减
        transaction_vo.payment_summary.platform_discount_fee = int(exclude_member_pay['platformDiscountFee'].sum())  # 平台补贴
        transaction_vo.payment_summary.coupon_discount_fee = int(exclude_member_pay['couponFee'].sum())  # 券包优惠
        transaction_vo.payment_summary.commission_fee = int(exclude_member_pay['fanpiaoCommissionFee'].sum() + exclude_member_pay['couponPackageCommissionFee'].sum())  # 佣金
        self._group_orders_by_day(transaction_vo, paid_orders)
        self._group_orders_by_source(transaction_vo, paid_orders)
        self._group_orders_by_pay_method(transaction_vo, paid_orders)
        # todo: 不应用差集来计算 fanpiao 优惠
        transaction_vo.payment_summary.fanpiao_discount_fee -= transaction_vo.payment_summary.random_discount
        transaction_vo.payment_summary.fanpiao_discount_fee -= transaction_vo.payment_summary.coupon_discount_fee
        logger.info(f"[*] return: {time.time() - run_time}")
        transaction_vo.total_discount_fee_amount = transaction_vo.total_bill_fee_amount - transaction_vo.total_paid_fee_amount
        # 应该 = 饭票优惠 + 券包优惠 + 红包返现 - 时来补贴
        self._group_transaction_by_discount(transaction_vo)
        return transaction_vo

    @classmethod
    def convert_discount_to_minus(cls, transaction_vo):
        """
        该减的减
        Args:
            transaction_vo: dict

        Returns:

        """
        discount_list = ['fanpiaoDiscountFee', 'couponDiscountFee', 'redPacketFee', 'bankRemoveFee',
                         'dishDiscountFee', 'orderDiscountFee', 'randomDiscount', 'commissionFee']
        # 转化 负数
        for discount in discount_list:
            if transaction_vo['paymentSummary'].get(discount):
                transaction_vo['paymentSummary'][discount] = -transaction_vo['paymentSummary'][discount]
        return transaction_vo

    @classmethod
    def to_yuan(cls, number):
        return (number / 100) if number else 0

    @classmethod
    def _attach_summary_df_for_export(cls, writer, transaction_vo):
        title = "营业概览"
        logger.info(f"_attach_summary_df_for_export: {title}")
        columns = ['实收总额(元)', '营收总额(元)', '客单价(元)', '订单笔数']
        df = pd.DataFrame([{
            columns[0]: cls.to_yuan(transaction_vo.get("totalPaidFeeAmount") or 0),
            columns[1]: cls.to_yuan(transaction_vo.get("totalBillFeeAmount") or 0),
            columns[2]: cls.to_yuan(transaction_vo.get("billFeePerUser") or 0),
            columns[3]: transaction_vo.get("totalPayment") or 0,
        }], columns=columns)
        return df.T.to_excel(writer, title, header=False)

    @classmethod
    def _attach_pay_method_df_for_export(cls, writer, transaction_vo):
        title = "支付方式统计"
        logger.info(f"_attach_pay_method_df_for_export: {title}")
        columns = ['支付方式', '订单笔数', '实收总额(元)']
        df = pd.DataFrame([{
            columns[0]: detail['payMethod'],
            columns[1]: detail['totalPayment'],
            columns[2]: cls.to_yuan(detail['totalPaidFeeAmount']),
        } for pm in (transaction_vo.get('transactionsByPayMethod') or []) for detail in pm['details']])
        return df.to_excel(writer, title, index=False)

    @classmethod
    def _attach_source_df_for_export(cls, writer, transaction_vo):
        title = '订单来源分布'
        logger.info(f"_attach_source_df_for_export: {title}")
        columns = ['订单来源', '笔数', '实收总额(元)']
        df = pd.DataFrame([{
            columns[0]: item['source'],
            columns[1]: item['totalPayment'],
            columns[2]: cls.to_yuan(item['totalPaidFeeAmount']),
        } for item in (transaction_vo.get('transactionsBySource') or [])])
        logger.info(df)
        return df.to_excel(writer, title, index=False)

    @classmethod
    def _attach_member_card_df_for_export(cls, writer, transaction_vo):
        title = '储值统计'
        logger.info(f"_attach_member_card_df_for_export: {title}")
        columns = ['项目', '笔数', '实收总额(元)']
        df = pd.DataFrame([{
            columns[0]: item['source'],
            columns[1]: item['totalPayment'],
            columns[2]: cls.to_yuan(item['totalPaidFeeAmount']),
        } for item in (transaction_vo.get('transactionsByMemberCard') or [])])
        logger.info(df)
        return df.to_excel(writer, title, index=False)

    @classmethod
    def _attach_fanpiao_df_for_export(cls, writer, transaction_vo):
        title = "饭票统计"
        logger.info(f"_attach_fanpiao_df_for_export: {title}")
        columns = [
            "购买次数（次）", "消费订单数", "消费实付总金额（元）", "商家实收总金额（元）", "平台总补贴（元）", "平台复购次数（次/人）"
        ]
        fanpiao_stats = transaction_vo.get("fanpiaoStats", {})
        buy_rate = fanpiao_stats.get("totalBillCount", 0) / fanpiao_stats.get("totalBuyCount", 0) if fanpiao_stats.get("totalBuyCount", 0) else 0
        df = pd.DataFrame([{
            columns[0]: fanpiao_stats.get("totalBuyCount", 0),
            columns[1]: fanpiao_stats.get("totalBillCount", 0),
            columns[2]: cls.to_yuan(fanpiao_stats.get("totalPaidFee", 0)),
            columns[3]: cls.to_yuan(fanpiao_stats.get("totalReceivedFee", 0)),
            columns[4]: cls.to_yuan(fanpiao_stats.get("subsidy", 0)),
            columns[5]: buy_rate,
        }])
        logger.info(df)
        return df.T.to_excel(writer, title, header=False)

    @classmethod
    def _attach_coupon_df_for_export(cls, writer, transaction_vo):
        title = "券包统计"
        logger.info(f"_attach_coupon_df_for_export: {title}")
        columns = [
            "购买次数（次）", "消费订单数", "消费实付总金额（元）", "商家实收总金额（元）", "平台总补贴（元）", "平台复购次数（次/人）"
        ]
        coupon_stats = transaction_vo.get("couponPackageStats", {})
        buy_rate = coupon_stats.get("totalBillCount", 0) / coupon_stats.get("totalBuyCount", 0) \
            if coupon_stats.get("totalBuyCount", 0) else 0
        df = pd.DataFrame([{
            columns[0]: coupon_stats.get("totalBuyCount", 0),
            columns[1]: coupon_stats.get("totalBillCount", 0),
            columns[2]: cls.to_yuan(coupon_stats.get("totalPaidFee", 0)),
            columns[3]: cls.to_yuan(coupon_stats.get("totalReceivedFee", 0)),
            columns[4]: cls.to_yuan(coupon_stats.get("subsidy", 0)),
            columns[5]: buy_rate,
        }])
        logger.info(df)
        return df.T.to_excel(writer, title, header=False)

    @classmethod
    def _attach_member_df_for_export(cls, writer, transaction_vo):
        title = "会员列表"
        logger.info(f"_attach_member_df_for_export: {title}")
        columns = ["昵称", "电话", "加入时间"]
        memberships = transaction_vo.get("memberships", [])
        df = pd.DataFrame([{
            columns[0]: member.get("username", ''),
            columns[1]: member.get("phone", ''),
            columns[2]: datetime.fromtimestamp(int(member['joinTime'])).strftime("%Y/%m/%d %H:%M:%S")
            if member.get("joinTime") else '',
        } for member in memberships], columns=columns)
        logger.info(df)
        return df.to_excel(writer, title, index=False)

    @classmethod
    def _attach_dish_df_for_export(cls, writer, transaction_vo):
        title = "菜品销量排序"
        logger.info(f"_attach_dish_df_for_export: {title}")
        columns = ["排名", "菜品名称", "菜品销量"]
        dish_stats = transaction_vo.get("dishStatList", [])
        df = pd.DataFrame([{
            columns[0]: i + 1,
            columns[1]: item.get("dishName", ""),
            columns[2]: item.get("salesQuantity", 0),
        } for i, item in enumerate(dish_stats)], columns=columns)
        logger.info(df)
        return df.to_excel(writer, title, index=False)
    
    @classmethod
    def _attach_sub_dish_df_for_export(cls, writer, transaction_vo):
        title = "加料销量排序"
        logger.info(f"_attach_sub_dish_df_for_export: {title}")
        columns = ["排名", "加料名称", "加料销量"]
        dish_stats = transaction_vo.get("subDishStatList", [])
        df = pd.DataFrame([{
            columns[0]: i + 1,
            columns[1]: item.get("dishName", ""),
            columns[2]: item.get("salesQuantity", 0),
        } for i, item in enumerate(dish_stats)], columns=columns)
        logger.info(df)
        return df.to_excel(writer, title, index=False)

    @classmethod
    def _attach_category_df_for_export(cls, writer, transaction_vo):
        title = "菜品分类销量排序"
        logger.info(f"_attach_category_df_for_export: {title}")
        columns = ["排名", "菜品名称", "菜品销量"]
        category_stats = transaction_vo.get("dishCategoryStatList", [])
        df = pd.DataFrame([{
            columns[0]: i + 1,
            columns[1]: item.get("categoryName", ""),
            columns[2]: item.get("salesQuantity", 0),
        } for i, item in enumerate(category_stats)])
        logger.info(df)
        return df.to_excel(writer, title, index=False)

    def export_sale_summary_and_email(self, start_time, end_time, transaction_vo, export_email):
        """
        导出 transaction_vo 并 发送邮件
        Excel 格式:
        """
        eh = EmailHelper()
        from view_ops.merchant_assist.stats_view_info import StatsViewInfo
        marketing_stats = StatsViewInfo(merchant_id=self._merchant.id)
        marketing_transaction_vo = marketing_stats.get_merchant_marketing_stats(
            None, None, start_time, end_time, 'SELF_DEFINE')
        marketing_transaction_vo = self.to_json(marketing_transaction_vo)
        dish_transaction_vo = marketing_stats.get_merchant_dish_stats(start_time, end_time)
        from view_ops.merchant_assist.membership import Membership
        member_stats = Membership(merchant_id=self._merchant.id)
        membership_list_vo = member_stats.get_membership_list(
            None, None, None, start_time, end_time, type="SELF_DEFINE", size=None)
        membership_list_vo = self.to_json(membership_list_vo)
        end_time = datetime.fromtimestamp(int(end_time)).strftime("%Y年%m月%d日 %H时%M分")
        start_time = datetime.fromtimestamp(int(start_time)).strftime("%Y年%m月%d日 %H时%M分")
        logger.info(f"export_sale_summary_and_email: {start_time} {end_time} to {export_email}")
        with tempfile.NamedTemporaryFile() as temp:
            file_path = f"{temp.name}.xlsx"
            writer = pd.ExcelWriter(file_path, engine='openpyxl')
            logger.info(f"File Path: {file_path}")
            try:
                self._attach_summary_df_for_export(writer, transaction_vo)
                self._attach_pay_method_df_for_export(writer, transaction_vo)
                self._attach_member_card_df_for_export(writer, transaction_vo)
                self._attach_source_df_for_export(writer, transaction_vo)
                self._attach_fanpiao_df_for_export(writer, marketing_transaction_vo)
                self._attach_coupon_df_for_export(writer, marketing_transaction_vo)
                self._attach_member_df_for_export(writer, membership_list_vo)
                self._attach_dish_df_for_export(writer, dish_transaction_vo)
                self._attach_category_df_for_export(writer, dish_transaction_vo)
                self._attach_sub_dish_df_for_export(writer, dish_transaction_vo)
            except Exception as e:
                traceback.print_exc()
            writer.save()
            writer.close()
            file_stream = open(file_path, "rb").read()
            eh.send_email(
                export_email, "时来饭票营收报表", f"{transaction_vo['name']} & 时来饭票营收报表\n时间范围: {start_time} 到 {end_time}",
                file_stream, f"时来饭票营收报表({start_time.split(' ')[0]}到{end_time.split(' ')[0]}).xlsx")
        if os.path.isfile(file_path):
            logger.info(f"Remove File {file_path}: {os.remove(file_path)}")

    def _group_transaction_by_pay_method_v2(self, method2paid_fee, method2num):
        """
        组织两层结构
        """

        def _format_data(_method, total_payment=0, total_paid_fee=0):
            return {
                'payMethod': _method, 'totalPayment': total_payment,
                'totalPaidFeeAmount': total_paid_fee, 'details': []
            }
        top_layer = {
            self.PayMethodName.SHILAI: _format_data(self.PayMethodName.SHILAI),
        }
        for pay_method in method2paid_fee:
            total_payment = method2num[pay_method]
            total_paid_fee = method2paid_fee[pay_method]
            if pay_method == 'SHILAI_MEMBER_CARD_PAY':
                top_layer[self.PayMethodName.SHILAI_MEMBER_CARD_PAY] = _format_data(
                    self.PayMethodName.SHILAI_MEMBER_CARD_PAY, total_payment, total_paid_fee)
                top_layer[self.PayMethodName.SHILAI_MEMBER_CARD_PAY]['details'].append(_format_data(
                    self.PayMethodName.SHILAI_MEMBER_CARD_PAY, total_payment, total_paid_fee))
            elif pay_method == 'CASH':
                top_layer[self.PayMethodName.CASH] = _format_data(
                    self.PayMethodName.CASH, total_payment, total_paid_fee)
                top_layer[self.PayMethodName.CASH]['details'].append(_format_data(
                    self.PayMethodName.CASH, total_payment, total_paid_fee))
            elif pay_method == 'SHILAI_COMBINE':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.SHILAI_COMBINE, total_payment, total_paid_fee))
            elif pay_method == 'WECHAT_PAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.WECHAT_PAY, total_payment, total_paid_fee))
            elif pay_method == 'COUPON_PAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.COUPON_PAY, total_payment, total_paid_fee))
            elif pay_method == 'ALIPAY':
                top_layer[self.PayMethodName.SHILAI]['totalPayment'] += total_payment
                top_layer[self.PayMethodName.SHILAI]['totalPaidFeeAmount'] += total_paid_fee
                top_layer[self.PayMethodName.SHILAI]['details'].append(
                    _format_data(self.PayMethodName.ALIPAY, total_payment, total_paid_fee))
            else:
                top_layer[pay_method] = _format_data(pay_method, total_payment, total_paid_fee)
                top_layer[pay_method]['details'].append(_format_data(pay_method, total_payment, total_paid_fee))
        results = []
        for pay_method in top_layer:
            if len(top_layer[pay_method]['details']) > 1:
                top_layer[pay_method]['details'] = sorted(
                    top_layer[pay_method]['details'], key=lambda x: x['totalPaidFeeAmount'], reverse=True)
            results.append(top_layer[pay_method])
        return results

    def merge_pos_transaction_amount(self, mp_transaction_vo, start_date, end_date, date_type, start_time, end_time):
        """
        合并小程序和 POS 的结果
        Test cases: 娟姨
        刘长和
        捞厨好面
        1.0 港岛记/食汁味/越小栈
        2.0 财气猪脚
        total_discount_fee /
        处理储值支付: MEMBER_ACCOUNT
        Args:
            mp_transaction_vo: dict
            start_date: string
            end_date: string
            date_type: string
        Returns:
        """
        if start_time and end_time:
            start_time, end_time = int(start_time), int(end_time)
        else:
            transaction_time = self._calculate_transactions_time(start_date, end_date, date_type)
            start_time, end_time = transaction_time.start_time, transaction_time.end_time
        mp_transaction_vo['startTime'] = start_time
        mp_transaction_vo['endTime'] = end_time
        logger.info(f"[*] merge_pos_transaction_amount: {start_time} ~ {end_time}")
        manager = ShilaiOPSManager(merchant_id=self._merchant.id)
        summary = manager.get_transaction_summary_2(start_time, end_time)  # only POS
        # 对第一层求和
        for field in ['totalBillFeeAmount', 'totalPaidFeeAmount', 'totalDiscountFeeAmount', 'totalUnpaidFeeAmount',
                      'totalUnpaidPayment', 'totalPayment', 'productNum']:
            mp_transaction_vo[field] = mp_transaction_vo.get(field) or 0
            mp_transaction_vo[field] += summary.get(field) or 0
        mp_transaction_vo['billFeePerUser'] = int(mp_transaction_vo['totalBillFeeAmount'] / mp_transaction_vo['totalPayment']) \
            if mp_transaction_vo['totalPayment'] else 0
        logger.info(f"[*] mp_transaction_vo: {mp_transaction_vo}")
        # 对 PaymentSummary 求和
        for field in ['commissionFee', 'couponDiscountFee', 'dishDiscountFee', 'fanpiaoDiscountFee',
                      'orderDiscountFee', 'platformDiscountFee', 'randomDiscount', 'totalBillFeeAmount',
                      'totalCustomerFeeAmount', 'totalPaidFeeAmount', 'redPacketFee']:
            if not mp_transaction_vo.get("paymentSummary"):
                mp_transaction_vo["paymentSummary"] = {}
            if not mp_transaction_vo["paymentSummary"].get(field):
                mp_transaction_vo["paymentSummary"][field] = 0
            if not summary.get("paymentSummary"):
                summary["paymentSummary"] = {}
            mp_transaction_vo["paymentSummary"][field] += summary["paymentSummary"].get(field) or 0
        payment_summary = mp_transaction_vo['paymentSummary']
        # 对 分天 合并
        day2nums = OrderedDict()
        if not summary.get("transactionsByDay"):
            summary.update({"transactionsByDay": []})
        for item in summary['transactionsByDay']:
            day2nums[item['date']] = {'paid': item.get('totalPaidFeeAmount') or 0, 'bill': item.get('totalBillFeeAmount') or 0}
        for item in mp_transaction_vo['transactionsByDay']:
            if item['date'] not in day2nums:
                day2nums[item['date']] = {}
            day2nums[item['date']]['bill'] = (day2nums[item['date']].get('bill') or 0) + (item.get('totalBillFeeAmount') or 0)
            day2nums[item['date']]['paid'] = (day2nums[item['date']].get('paid') or 0) + (item.get('totalPaidFeeAmount') or 0)
        merge_daily = []
        for day in day2nums:
            merge_daily.append({
                'date': day, 'totalBillFeeAmount': day2nums[day]['bill'], 'totalPaidFeeAmount': day2nums[day]['paid']
            })
        mp_transaction_vo['transactionsByDay'] = sorted(merge_daily, key=lambda x: x['date'])
        # 对 source 合并
        if not summary.get("transactionsBySource"):
            summary["transactionsBySource"] = []
        if not mp_transaction_vo.get('transactionsBySource'):
            mp_transaction_vo['transactionsBySource'] = []
        mp_transaction_vo['transactionsBySource'].extend(summary['transactionsBySource'])
        # 计算优惠来源
        # other_discount = sum([
        #     payment_summary['orderDiscountFee'],
        #     payment_summary['dishDiscountFee'],
        # ])
        other_discount = summary.get("totalDiscountFeeAmount") or 0
        if other_discount:
            mp_transaction_vo['transactionsByDiscount'].append({
                'source': '其他优惠', 'totalDiscountFeeAmount': other_discount,
            })
        # 对 支付方式 合并
        method2fee = defaultdict(int)
        method2num = defaultdict(int)
        if not summary.get("transactionsByPayMethod"):
            summary["transactionsByPayMethod"] = []
        for item in summary['transactionsByPayMethod']:
            item['payMethod'] = 'SHILAI_COMBINE' if item['payMethod'] == 'SHILAI_FANPIAO' else item['payMethod']
            item['payMethod'] = 'SHILAI_MEMBER_CARD_PAY' if item['payMethod'] == 'MEMBER_ACCOUNT' else item['payMethod']
            method2fee[item['payMethod']] += item['totalPaidFeeAmount']
            method2num[item['payMethod']] += item['totalPayment']
        if not mp_transaction_vo.get("transactionsByPayMethod"):
            mp_transaction_vo["transactionsByPayMethod"] = []
        for item in mp_transaction_vo['transactionsByPayMethod']:
            method2fee[item['payMethod']] += item['totalPaidFeeAmount']
            method2num[item['payMethod']] += item['totalPayment']
        try:
            mp_transaction_vo["transactionsByPayMethod"] = self._group_transaction_by_pay_method_v2(
                method2fee, method2num
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            mp_transaction_vo["transactionsByPayMethod"] = {"error": str(e)}
        # 对储值列表合并
        member2fee = defaultdict(int)
        member2num = defaultdict(int)
        if not summary.get("transactionsByMemberCard"):
            summary["transactionsByMemberCard"] = []
        for item in summary['transactionsByMemberCard']:
            member2fee[item['source']] += item['totalPaidFeeAmount']
            member2num[item['source']] += item['totalPayment']
        if not mp_transaction_vo.get("transactionsByMemberCard"):
            mp_transaction_vo["transactionsByMemberCard"] = []
        for item in mp_transaction_vo['transactionsByMemberCard']:
            member2fee[item['source']] += item['totalPaidFeeAmount']
            member2num[item['source']] += item['totalPayment']
        mp_transaction_vo["transactionsByMemberCard"] = [
            {'source': source, 'totalPaidFeeAmount': member2fee.get(source, 0), 'totalPayment': member2num.get(source, 0)}
            for source in ['新增储值', '储值支付']
        ]
        mp_transaction_vo["totalRechargeFee"] = mp_transaction_vo["transactionsByMemberCard"][0]["totalPaidFeeAmount"]
        mp_transaction_vo["totalRechargePaidFee"] = mp_transaction_vo["transactionsByMemberCard"][1]["totalPaidFeeAmount"]
        # 结算金额
        settlement_rate = self._get_settlement_rate()
        bank_remove_fee = int(payment_summary['totalPaidFeeAmount'] * settlement_rate)
        payment_summary['bankRemoveFee'] = bank_remove_fee
        payment_summary['totalSettlementAmount'] = payment_summary['totalPaidFeeAmount'] - bank_remove_fee
        return mp_transaction_vo

    def _group_orders_by_source(self, transaction_vo, orders):
        """
        source_item = transaction_vo.transactions_by_source.add()
        source_item.source = '扫码点餐'
        source_item.total_paid_fee_amount = transaction_vo.total_paid_fee_amount  # 实收
        source_item.total_payment = transaction_vo.total_payment
        """
        if not orders:
            return transaction_vo
        df = pd.DataFrame(orders)
        groupings = ['source']
        agg_func = {'billFee': 'sum', 'paidInFee': 'sum', 'id': 'count'}
        for ind, agg in df.groupby(groupings).agg(agg_func).iterrows():
            pay_method = transaction_vo.transactions_by_source.add()
            pay_method.source = ind
            pay_method.total_payment = agg['id']
            pay_method.total_paid_fee_amount = agg['paidInFee']
        return transaction_vo

    def _group_orders_by_pay_method(self, transaction_vo, orders):
        if not orders:
            return transaction_vo
        df = pd.DataFrame(orders)
        groupings = ['payMethod']
        agg_func = {'billFee': 'sum', 'paidInFee': 'sum', 'id': 'count'}
        for ind, agg in df.groupby(groupings).agg(agg_func).iterrows():
            pay_method = transaction_vo.transactions_by_pay_method.add()
            pay_method.pay_method = ind
            pay_method.total_payment = agg['id']
            pay_method.total_paid_fee_amount = agg['paidInFee']
        return transaction_vo

    def _group_orders_by_day(self, transaction_vo, orders):
        if not orders:
            return transaction_vo
        df = pd.DataFrame(orders)
        groupings = ['date']
        for ind, agg in df.groupby(groupings).agg({'billFee': 'sum', 'paidInFee': 'sum'}).iterrows():
            day_info = transaction_vo.transactions_by_day.add()
            day_info.date = ind[5:]
            day_info.sort_date = ind
            day_info.total_bill_fee_amount = agg['billFee']
            day_info.total_paid_fee_amount = agg['paidInFee']
        return transaction_vo

    def get_transactions(self, start_date, end_date, type):
        start_time, end_time, transactions = self._get_valid_transaction_list(start_date, end_date, type)
        transaction_vo = transaction_pb.MerchantAssistTransactionVo()
        pay_method_mappings = defaultdict(list)
        _total_bill_fee_amount = 0  # 账单总额
        _total_paid_fee_amount = 0  # 实收总额
        _bill_fee_per_user = 0  # 账单单均金额
        _total_payment = 0  # 支付笔数
        _total_market_bill_fee_amount = 0  # 市场价账单总额
        _total_product_num = 0  # 商品总数
        ordering_da = OrderingServiceDataAccessHelper()
        # refund_tran_ids = [_tran.id for _tran in transactions if _tran.type != wallet_pb.Transaction.ORDERING_REFUND]
        # refund_orders = [ordering_da.get_order(refund_transaction_id=tid) for tid in refund_tran_ids]
        # for order in refund_orders:
        #     refund_tran_ids.append(order.transactionId)
        # print("[*] Refund Transaction ID:", refund_tran_ids)
        for _transaction in transactions:
            # 处理 支付方式
            # if _transaction in refund_tran_ids:
            #     continue
            pay_method = _transaction.pay_method
            pay_method = wallet_pb.Transaction.PayMethod.Name(pay_method)
            if pay_method in ['FANPIAO_PAY', 'WALLET']:
                pay_method = 'SHILAI_COMBINE'
            elif _transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                pay_method = "WECHAT_PAY"
            elif _transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                pay_method = "ALIPAY"
            if _transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
                pay_method_mappings[pay_method].append(_transaction.paid_fee)
                continue
            if _transaction.type == wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE:
                # 会员储值不用管
                _total_bill_fee_amount += _transaction.paid_fee
                _total_paid_fee_amount += _transaction.paid_fee
                pay_method_mappings[pay_method].append(_transaction.paid_fee)
                continue
            if _transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
                self.deal_with_self_dining_payment_transaction(_transaction, transaction_vo)
                _total_payment += 1
                _total_paid_fee_amount += _transaction.paid_fee
                pay_method_mappings[pay_method].append(_transaction.paid_fee)
                continue
            flag = 1
            if _transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                flag = -1
                order = ordering_da.get_order(refund_transaction_id=_transaction.id)
            else:
                order = ordering_da.get_order(transaction_id=_transaction.id)
                _total_product_num += self.get_order_product_nums(order)

            _total_payment += 1 * flag
            # 退款本来存的就是负数,所以不用再乘以flag了
            _total_bill_fee_amount += _transaction.bill_fee
            if order:
                _total_market_bill_fee_amount += order.market_bill_fee
                if order.market_bill_fee == 0:
                    _total_market_bill_fee_amount += _transaction.bill_fee
            origin_paid_fee = 0
            if order and order.paid_in_fee > 0:
                _total_paid_fee_amount += order.paid_in_fee * flag
                origin_paid_fee += order.paid_in_fee * flag
            else:
                _total_paid_fee_amount += _transaction.paid_fee
                origin_paid_fee += _transaction.paid_fee
                other_fees = self.__get_other_fees(_transaction)
                _total_paid_fee_amount -= flag * other_fees.commission_fee
                origin_paid_fee -= flag * other_fees.commission_fee
                _total_paid_fee_amount -= flag * other_fees.red_packet_fee
                origin_paid_fee -= flag * other_fees.red_packet_fee
                _total_paid_fee_amount += flag * other_fees.platform_discount_fee
                origin_paid_fee += flag * other_fees.platform_discount_fee
                _total_paid_fee_amount += flag * other_fees.ifeedu_fee
                origin_paid_fee += flag * other_fees.ifeedu_fee
            pay_method_mappings[pay_method].append(_transaction.paid_fee)
        if _total_payment > 0:
            _bill_fee_per_user = int(_total_bill_fee_amount / float(_total_payment) + 0.5)

        transaction_vo.merchant_id = self._merchant.id
        transaction_vo.store_id = "{}_0".format(self._merchant.id)
        transaction_vo.total_bill_fee_amount += _total_bill_fee_amount
        transaction_vo.total_market_bill_fee_amount += _total_bill_fee_amount  # _total_market_bill_fee_amount
        transaction_vo.total_paid_fee_amount += _total_paid_fee_amount
        transaction_vo.bill_fee_per_user += _bill_fee_per_user
        transaction_vo.total_payment += _total_payment
        transaction_vo.logo_url = self._merchant.basic_info.logo_url
        transaction_vo.name = self._merchant.basic_info.display_name
        transaction_vo.product_num += _total_product_num

        date_ranges = date_utils.get_date_range(start_time, end_time)
        self.__get_transaction_info_by_day(transaction_vo, transactions, date_ranges)
        date_ranges = date_utils.get_time_ranges(start_time, end_time)
        if len(date_ranges) > 0:
            if len(date_ranges) == 1:
                transaction_vo.date_range = date_ranges[0].start_date
            else:
                transaction_vo.date_range = "{} - {}".format(date_ranges[0].start_date, date_ranges[-1].end_date)
        transaction_vo_json = self.to_json(transaction_vo)
        try:
            transaction_vo_json['rawPayMethodMappings'] = pay_method_mappings
            transaction_vo_json['transactionsByPayMethod'] = self.__group_transaction_by_pay_method(pay_method_mappings)
        except Exception as e:
            import traceback
            traceback.print_exc()
        return transaction_vo_json

    def __get_transaction_info_by_day(self, transaction_vo, transactions, date_ranges):
        _transactions_info = {}
        for date in date_ranges:
            _transaction_info = transaction_vo.transactions_by_day.add()
            _transaction_info.date = date.date
            _transaction_info.sort_date = date.sort_date
            _transactions_info.update({date.date: _transaction_info})

        for _transaction in transactions:
            paid_time = _transaction.paid_time
            if paid_time == "":
                paid_time = _transaction.create_time
            _paid_date = datetime.fromtimestamp(paid_time).strftime("%m-%d")
            _transaction_info = _transactions_info.get(_paid_date, None)
            if _transaction_info is None:
                continue
            _transaction_info.total_bill_fee_amount = _transaction_info.total_bill_fee_amount + _transaction.bill_fee
            _transaction_info.total_paid_fee_amount = _transaction_info.total_paid_fee_amount + _transaction.paid_fee
            _transactions_info.update({
                _paid_date: _transaction_info
            })
        transaction_vo.transactions_by_day.sort(key = lambda x: x.sort_date)

    def __get_other_fees(self, transaction):
        OtherFee = namedtuple("OtherFee", ["red_packet_fee", "commission_fee", "platform_discount_fee", "ifeedu_fee"])
        ordering_service_da = OrderingServiceDataAccessHelper()
        logger.info("transaction.id: {}".format(transaction.id))
        transaction_da = TransactionDataAccessHelper()
        if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            order = ordering_service_da.get_order(transaction_id=transaction.id)
        elif transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
            # 兼容旧版,order没有paid_in_fee的订单
            order = ordering_service_da.get_order(refund_transaction_id=transaction.id)
            if not order:
                return OtherFee(0, 0, 0, 0)
            transaction = transaction_da.get_transaction_by_id(order.transaction_id)
        elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
            order = ordering_service_da.get_order(transaction_id=transaction.id)
        elif transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
            if transaction.use_coupon_id != "":
                red_packet = self.__get_red_packet(transaction)
                if red_packet:
                    return OtherFee(red_packet_fee=red_packet.total_value, commission_fee=0, platform_discount_fee=0, ifeedu_fee=0)
            return OtherFee(0, 0, 0, 0)
        if not order:
            return OtherFee(0, 0, 0, 0)
        red_packet = self.__get_red_packet(transaction)
        red_packet_value = 0
        if red_packet:
            red_packet_value = red_packet.total_value
        commission = order.commission if order else 0
        platform_discount_fee = self.get_order_platform_discount_fee(order)
        return OtherFee(
            red_packet_fee=red_packet_value, commission_fee=commission, platform_discount_fee=platform_discount_fee,
            ifeedu_fee=order.ifeedu_fee)

    def __get_coupon_category(self, coupon_id):
        coupon = CouponDataAccessHelper().get_coupon_by_id(coupon_id)
        if coupon:
            coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
            return coupon_category
        return None

    def __get_red_packet(self, transaction):
        if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
            red_packet = RedPacketDataAccessHelper().get_red_packet(transaction_id=transaction.id)
        else:
            red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet:
            return red_packet
        return None

    def get_order_platform_discount_fee(self, order):
        platform_discount_fee = order.platform_discount_fee
        if platform_discount_fee < 0:
            platform_discount_fee = 0
        return platform_discount_fee

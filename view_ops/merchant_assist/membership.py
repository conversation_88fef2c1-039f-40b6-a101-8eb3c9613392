import logging
from collections import namedtuple

import maya

import proto.page.merchant_assist.membership_pb2 as membership_pb
from common.utils import date_utils
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from view_ops.merchant_assist.merchant_assist import MerchantAssist


logger = logging.getLogger(__name__)


class Membership(MerchantAssist):
    def __init__(self, merchant_id=None, user_id=None):
        super(Membership, self).__init__(merchant_id=merchant_id, user_id=user_id)

    def get_membership_info(self, start_date, end_date, start_time, end_time, type):
        if start_time and end_time:
            start_time, end_time = int(start_time), int(end_time)
        else:
            transaction_time = self._calculate_transactions_time(start_date, end_date, type)
            start_time, end_time = transaction_time.start_time, transaction_time.end_time
        membership_da = MembershipDataAccessHelper()

        membership_info_vo = membership_pb.MerchantAssistMembershipInfoVo()

        # 商家总会员数
        total_count = membership_da.count_member_card(merchant_id=self._merchant.id)

        # 请求时间段内的会员总数
        time_range_count = membership_da.count_member_card(merchant_id=self._merchant.id, start_time=start_time, end_time=end_time)

        time_ranges = date_utils.get_time_ranges(start_time, end_time, fmt='%m-%d')
        for time_range in time_ranges:
            count = membership_da.count_member_card(merchant_id=self._merchant.id, start_time=time_range.start_time, end_time=time_range.end_time)
            membership_count = membership_info_vo.membership_count.add()
            membership_count.date = time_range.start_date
            membership_count.number = count

        date_ranges = date_utils.get_time_ranges(start_time, end_time)
        if len(date_ranges) > 0:
            if len(date_ranges) == 1:
                membership_info_vo.date_range = date_ranges[0].start_date
            else:
                membership_info_vo.date_range = "{} - {}".format(date_ranges[0].start_date, date_ranges[-1].end_date)

        membership_info_vo.total_membership_count = total_count
        membership_info_vo.today_membership_count = 0
        membership_info_vo.incr_membership_count = time_range_count
        return membership_info_vo

    def get_membership_list(self, last_initial_activate_time, start_date, end_date, start_time, end_time, type, size=10):
        if start_time and end_time:
            start_time, end_time = int(start_time), int(end_time)
        else:
            transaction_time = self._calculate_transactions_time(start_date, end_date, type)
            start_time, end_time = transaction_time.start_time, transaction_time.end_time
        if last_initial_activate_time is None or int(last_initial_activate_time) > end_time:
            last_initial_activate_time = end_time
        membership_da = MembershipDataAccessHelper()
        membership_list = MembershipDataAccessHelper().get_member_cards(
            merchant_id=self._merchant.id, last_initial_activate_time=last_initial_activate_time, size=size,
            activate_start_time=start_time, activate_end_time=end_time, orderby=[("initialActivateTime", -1)])
        membership_list_vo = membership_pb.MerchantAssistMembershipListVo()
        # 商家总会员数
        membership_list_vo.total_membership_count = membership_da.count_member_card(
            merchant_id=self._merchant.id)
        # 请求时间段内的会员总数
        membership_list_vo.incr_membership_count = membership_da.count_member_card(
            merchant_id=self._merchant.id, start_time=start_time, end_time=end_time)
        for membership in membership_list:
            user = UserDataAccessHelper().get_user(membership.user_id)
            if not user:
                logger.info("user not found: {}".format(membership.user_id))
                continue
            membership_vo = membership_list_vo.memberships.add()
            membership_vo.username = user.member_profile.nickname or user.member_profile.name
            # logger.info("user: user:id: {}, username: {}".format(user.id, membership_vo.username))
            membership_vo.phone = user.member_profile.mobile_phone
            membership_vo.join_time = membership.initial_activate_time
            membership_vo.initial_activate_time = membership.initial_activate_time
            membership_vo.user_id = user.id
        return membership_list_vo

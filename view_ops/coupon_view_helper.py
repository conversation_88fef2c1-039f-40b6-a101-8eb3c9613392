# -*- coding: utf-8 -*-

from business_ops.coupon_manager import CouponManager
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto.page import coupon_list_pb2 as coupon_list_pb
from proto import coupon_category_pb2 as coupon_category_pb
import proto.coupons_pb2 as coupons_pb

def get_coupons(user_id, state=None, merchant_id=None, page=1, size=100):
    """获取用户优惠券
    """
    coupons = CouponDataAccessHelper().get_coupon_list(
        user_id=user_id, merchant_id=merchant_id, state=state, page=1, size=500)
    ui_coupons = []
    if coupons:
        for coupon in coupons:
            ui_coupon = convert_to_ui_coupon(coupon)
            if ui_coupon:
                ui_coupons.append(ui_coupon)
    ui_coupons.sort(key = lambda coupon: coupon.accept_time, reverse=True)
    return ui_coupons


def convert_to_ui_coupon(coupon):
    """转换成 ui.coupon 结构

    Args:
        coupon: (Coupon) 优惠券信息

    Return:
        (page.coupon_list_pb.Coupon)
    """
    coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
    if coupon_category:
        if coupon_category.state == coupon_category_pb.CouponCategory.DELETED:
            return None
        if coupon_category.state == coupon_category_pb.CouponCategory.INACTIVE:
            return None
        ui_coupon = coupon_list_pb.Coupon()

        if coupon_category.issue_scene == coupon_category_pb.CouponCategory.BRAND_DISH_VERIFICATION_CODE:
            base_info = coupon_category.brand_dish_verification_code_coupon_spec.base_info
            coupon_category_spec = coupon_category.brand_dish_verification_code_coupon_spec
        elif coupon_category.issue_scene == coupon_category_pb.CouponCategory.INVITE_SHARE:
            base_info = coupon_category.invite_share_coupon_spec.base_info
            coupon_category_spec = coupon_category.invite_share_coupon_spec
        else:
            base_info = coupon_category.cash_coupon_spec.base_info
            coupon_category_spec = coupon_category.cash_coupon_spec
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(coupon.merchant_id)

        ui_coupon.id = coupon.id
        ui_coupon.merchant_id = coupon.merchant_id
        ui_coupon.use_condition.CopyFrom(coupon_category.cash_coupon_spec.advanced_info.use_condition)
        ui_coupon.state = coupon.state
        ui_coupon.coupon_type = coupon_category.coupon_type
        ui_coupon.least_cost = coupon_category_spec.least_cost
        ui_coupon.reduce_cost = coupon_category_spec.reduce_cost
        ui_coupon.description = base_info.description
        ui_coupon.logo_url = base_info.logo_url
        ui_coupon.title = base_info.title
        is_valid = CouponManager().is_coupon_in_valid_period(coupon)
        if is_valid is None:
            is_valid = False
        ui_coupon.is_valid = is_valid
        ui_coupon.brand_name = merchant.basic_info.display_name
        ui_coupon.accept_time = coupon.accept_time
        ui_coupon.is_limited = False
        date_info = base_info.date_info
        if date_info.type == "DATE_TYPE_FIX_TERM":
            ui_coupon.is_limited = True
            ui_coupon.expired_time = coupon.accept_time + date_info.fixed_term * date_utils.ONE_DAY
        elif date_info.type == "DATE_TYPE_FIX_TIME_RANGE":
            ui_coupon.is_limited = True
            ui_coupon.expired_time = date_info.end_timestamp
        ui_coupon.date_info.CopyFrom(date_info)
        if date_utils.get_datetime_in_timezone(ui_coupon.expired_time, date_utils.TIMEZONE_SHANGHAI).replace(hour=23, minute=55, second=0, microsecond=0) < date_utils.datetime_now_in_timezone(date_utils.TIMEZONE_SHANGHAI) and coupon.state != coupons_pb.Coupon.EXPIRED:
            coupon_da = CouponDataAccessHelper()
            coupon.state = coupons_pb.Coupon.EXPIRED
            coupon_da.update_or_create_coupon(coupon)
            return None
        return ui_coupon
    return None


from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto.page import member_card_list_pb2 as member_card_list_pb
from view_ops.merchant_view_helper import MerchantViewObjectHelper

def get_member_cards(user, page, size):
    """获取用户领取的会员卡
    """
    membership_da = MembershipDataAccessHelper()
    merchant_da = MerchantDataAccessHelper()
    member_cards = membership_da.get_member_cards(user_id=user.id, page=page, size=size)
    result = []
    for member_card in member_cards:
        merchant = merchant_da.get_merchant(member_card.merchant_id)
        member_card_category = membership_da.get_member_card_category(member_card.card_category_id)
        ui_member_card = member_card_list_pb.MemberCard()
        ui_member_card.id = member_card.id
        ui_member_card.merchant_id = merchant.id
        ui_member_card.color = member_card_category.wechat_card.member_card.base_info.color
        ui_member_card.logo_url = member_card_category.wechat_card.member_card.base_info.logo_url
        ui_member_card.brand_name = member_card_category.wechat_card.member_card.base_info.brand_name
        ui_member_card.title = member_card_category.wechat_card.member_card.base_info.title
        result.append(ui_member_card)
    return result

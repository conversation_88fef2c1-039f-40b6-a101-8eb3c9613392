import logging
from operator import attrgetter
from datetime import timedelta

import maya

import proto.coupon_category_pb2 as coupon_category_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.page.merchant_rules_pb2 as page_merchant_rules_pb
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from cache.redis_client import RedisClient
from cache.merchant_redis_helper import get_nearby_stores
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto.ui.merchant import order_pb2 as ui_merchant_order_pb
from proto.ui.merchant import rules_pb2 as ui_merchant_rules_pb
from proto.ui.merchant import user_pb2 as ui_merchant_user_pb
from proto.ui import group_dining_ui_pb2 as group_dining_ui_pb
from proto.page import group_dining_store_list_pb2 as group_dining_store_list_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb

logger = logging.getLogger(__name__)


class MerchantViewObjectHelper():

    MERCHANT_BY_NAME_WITHIN_DISTANCE_KEY = "get_merchant_by_name_within_distance_{}"

    def opening_hours_to_string(self, opening_hours):
        daily_start_time = opening_hours.daily_start_time
        daily_end_time = opening_hours.daily_end_time
        if daily_start_time == 0 and daily_end_time == 0:
            return "00:00-23:59"
        start_hour = int(daily_start_time / date_utils.ONE_HOUR)
        start_minute = int((daily_start_time - start_hour * date_utils.ONE_HOUR) / date_utils.ONE_MINUTE)
        end_hour = int(daily_end_time / date_utils.ONE_HOUR)
        end_minute = int((daily_end_time - end_hour * date_utils.ONE_HOUR) / date_utils.ONE_MINUTE)
        kargs = {
            "start_hour": start_hour,
            "start_minute": start_minute,
            "end_hour": end_hour,
            "end_minute": end_minute
        }
        s = "{start_hour:0>2}:{start_minute:0>2}-{end_hour:0>2}:{end_minute:0>2}".format(**kargs)
        return s

    def check_merchant_opening(self, opening_hours):
        if len(opening_hours.open_days) < 0:
            return True
        now = maya.when("now", timezone=date_utils.TIMEZONE_SHANGHAI)
        weekday = now.weekday - 1
        if weekday not in opening_hours.open_days:
            return False
        for weekday in opening_hours.open_days:
            now_time = now.hour * date_utils.ONE_HOUR + now.minute * date_utils.ONE_MINUTE
            if opening_hours.daily_start_time <= now_time <= opening_hours.daily_end_time:
                return True
        return False

    def get_merchant_by_name(self, name, lng, lat, radius):
        redis_client = RedisClient().get_connection()
        key = self.MERCHANT_BY_NAME_WITHIN_DISTANCE_KEY.format(name)
        self.get_merchant_by_name_cache(name)
        merchants = redis_client.georadius(key, lng, lat, radius, unit='m', withdist=True, sort="ASC")
        merchant_da = MerchantDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        search_merchant_vo = page_merchant_rules_pb.SearchMerchantVO()
        for merchant_cache in merchants:
            merchant_id = str(merchant_cache[0].decode("utf8"))
            distance = round(float(merchant_cache[1]), 2)
            merchant = merchant_da.get_merchant(merchant_id, status=merchant_rules_pb.RUNNING)
            registration_info = ordering_da.get_registration_info(merchant_id=merchant_id)
            if not merchant:
                continue
            if not registration_info:
                continue
            store = merchant.stores[0]
            merchant_vo = page_merchant_rules_pb.SearchMerchantVO.Merchant()
            merchant_vo.id = merchant.id
            merchant_vo.name = merchant.basic_info.display_name
            merchant_vo.address = store.address
            merchant_vo.phone = store.phone
            merchant_vo.distance = str(distance)

            merchant_vo.opening = self.check_merchant_opening(store.opening_hours)
            merchant_vo.opening_hours = self.opening_hours_to_string(store.opening_hours)

            if distance <= registration_info.max_take_out_distance:
                r = search_merchant_vo.reachable_merchants.add()
                r.CopyFrom(merchant_vo)
            else:
                r = search_merchant_vo.unreachable_merchants.add()
                r.CopyFrom(merchant_vo)
        return search_merchant_vo

    def get_merchant_by_name_cache(self, name):
        key = self.MERCHANT_BY_NAME_WITHIN_DISTANCE_KEY.format(name)
        redis_client = RedisClient().get_connection()
        if redis_client.ttl(key) > 5 * 60:
            return
        merchant_da = MerchantDataAccessHelper()
        merchants = merchant_da.get_merchants_by_name(name)
        for merchant in merchants:
            if len(merchant.stores) == 0:
                continue
            store = merchant.stores[0]
            if not (store and store.poi):
                continue
            lng = store.poi.location.longitude
            lat = store.poi.location.latitude
            redis_client.geoadd(key, lng, lat, merchant.id)
            redis_client.expire(key, 60 * 60)

    def get_merchants(self, city=None, enable_group_dining=None, status=None,
                      page=None, size=None):
        """获取商户信息列表
        """
        merchants = MerchantDataAccessHelper().get_merchant_list(
            city=city,
            enable_group_dining=enable_group_dining, status=status,
            page=page, size=size)
        resp_merchants = []
        for merchant in merchants:
            merchant_info = self.convert_merchant_info_vo(merchant)
            resp_merchants.append(merchant_info)
        return resp_merchants

    def get_stores(self, city=None, enable_group_dining=None, status=None, page=None, size=None):
        merchants = MerchantDataAccessHelper().get_merchant_list(
            city=city,
            enable_group_dining=enable_group_dining, status=status,
            page=page, size=size)
        resp_stores = []
        for merchant in merchants:
            merchant_info = self.convert_merchant_store_vo(merchant)
            resp_stores.append(merchant_info)
        return resp_stores

    def convert_merchant_info_vo(self, merchant=None):
        """转换商户信息的数据结构
        """
        if merchant:
            ui_merchant = ui_merchant_rules_pb.UIMerchant()
            ui_merchant.id = merchant.id
            ui_merchant.logo_url = merchant.basic_info.logo_url
            ui_merchant.name = merchant.basic_info.name
            ui_merchant.display_name = merchant.basic_info.display_name

            ui_basic_info = ui_merchant_rules_pb.UIBasicInfo()
            ui_basic_info.logo_url = merchant.basic_info.logo_url
            ui_basic_info.name = merchant.basic_info.name
            ui_basic_info.display_name = merchant.basic_info.display_name
            ui_merchant.basic_info.CopyFrom(ui_basic_info)
            for store in merchant.stores:
                ui_store = self.convert_merchant_store_vo(store)
                s = ui_merchant.stores.add()
                s.CopyFrom(ui_store)
            return ui_merchant
        return None

    def get_merchant_info(self, merchant_id):
        """
            获取商户信息
            :param merchant_id: (string) 商户ID
            :return: (ui.merchant.MerchantInfo)
        """
        merchant_info = MerchantDataAccessHelper().get_merchant(merchant_id)
        return self.convert_merchant_info_vo(merchant_info)

    def get_members(self, merchant_id, start_time=None, end_time=None):
        """获取商户最新会员列表

        Args:
            merchant_id: (string) 商户ID
            start_time: (int64) 查询开始时间
            end_time: (int64) 查询结束时间

        Return:
            (ui_merchant_user_pb.Member[])
        """
        member_cards = MembershipDataAccessHelper().get_member_cards_by_merchant_id(
            merchant_id,
            activate_start_time=start_time, activate_end_time=end_time)

        if member_cards:
            members = []
            for member_card in member_cards:
                user = UserDataAccessHelper().get_user(member_card.user_id)
                if not user:
                    continue

                member_profile = user.member_profile
                member = ui_merchant_user_pb.Member()
                member.user_id = user.id
                member.joined_time = user.joined_time
                member.headimgurl = member_profile.head_image_url
                member.nickname = member_profile.nickname
                member.name = member_profile.name
                member.mobile_phone = member_profile.mobile_phone
                member.sex = member_profile.sex
                member.birthday = "{}/{}/{}".format(member_profile.birth_year,
                                                    member_profile.birth_month,
                                                    member_profile.birth_day)
                members.append(member)
            return sorted(members, key=attrgetter('joined_time'), reverse=True)
        return None

    def get_merchant_transactions_group_dining(self, merchant_id, store_id=None, start_time=None, end_time=None):
        """获取商户最近的账单列表
            1. 约饭用餐账单列表
        """
        type_list = [wallet_pb.Transaction.GROUP_DINING_PAYMENT]
        transaction_ma = TransactionManager()
        transactions = transaction_ma.get_dining_payment_type_transactions(
            payee_id=merchant_id, payee_store_id=store_id, state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time, type_list=type_list)
        user_da = UserDataAccessHelper()

        pb_transactions = []
        for transaction in transactions:
            user = user_da.get_user(transaction.payer_id)
            if not user:
                continue
            dining = GroupDiningDataAccessHelper().get_dining_by_transaction_id(transaction.id)
            if not dining:
                continue

            bi_group_dining_transaction = group_dining_ui_pb.GroupDiningTransaction()
            bi_group_dining_transaction.user_nickname = user.member_profile.nickname
            bi_group_dining_transaction.bill_fee = transaction.bill_fee

            if dining.dish_order_mode == group_dining_pb.GroupDiningEvent.GENERAL:
                policy = transaction_ma.calculate_policy(dining, transaction.bill_fee, dining.user_cnt)
                if policy:
                    bi_group_dining_transaction.coupon_reduce_cost = policy.reduce_cost
                    bi_group_dining_transaction.coupon_least_cost = policy.least_cost
            elif dining.dish_order_mode == group_dining_pb.GroupDiningEvent.COMBO_MEAL:
                combo_meal = ComboMealDataAccessHelper().get_combo_meal_by_id(dining.combo_meal_id)
                bi_group_dining_transaction.coupon_reduce_cost = combo_meal.original_price - combo_meal.discount_price
                bi_group_dining_transaction.coupon_least_cost = combo_meal.original_price
            red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
            if red_packet:
                bi_group_dining_transaction.red_packet_amount = red_packet.total_value
            else:
                bi_group_dining_transaction.red_packet_amount = 0
            bi_group_dining_transaction.merchant_received_amount = transaction.paid_fee
            bi_group_dining_transaction.paid_time = transaction.paid_time
            pb_transactions.append(bi_group_dining_transaction)
        return pb_transactions

    def __get_group_dish_payment_transaction(self, transaction, user):
        pass

    def __get_ordering_refund_transaction(self, transaction, user):
        order = ui_merchant_order_pb.Order()
        order.user_id = transaction.payer_id
        if user:
            order.nickname = user.member_profile.nickname
        order.trade_time = transaction.paid_time
        order.trade_amount = transaction.paid_fee

        return order

    def __get_self_dining_order_from_transaction(self, transaction, user):
        order = ui_merchant_order_pb.Order()
        order.user_id = transaction.payer_id
        if user:
            order.nickname = user.member_profile.nickname
        order.trade_time = transaction.paid_time
        order.trade_amount = transaction.paid_fee
        red_packet = RedPacketDataAccessHelper().get_red_packet(transaction_id=transaction.id)
        if red_packet:
            order.red_packet.CopyFrom(red_packet)

        if transaction.use_coupon_id:
            try:
                coupon = CouponDataAccessHelper().get_coupon_by_id(transaction.use_coupon_id)
                coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
                    coupon.coupon_category_id)
                order.coupon_category.CopyFrom(coupon_category)
            except:
                pass
        return order

    def __get_self_dish_order_from_transaction(self, transaction, user):
        order = ui_merchant_order_pb.Order()
        order.user_id = transaction.payer_id
        if user:
            order.nickname = user.member_profile.nickname
        order.trade_time = transaction.paid_time
        order.trade_amount = transaction.paid_fee

        dish_order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
        if not dish_order:
            return
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet:
            order.red_packet.CopyFrom(red_packet)

        coupon_category = coupon_category_pb.CouponCategory()
        coupon_category.merchant_id = transaction.payee_id
        coupon_category.cash_coupon_spec.least_cost = dish_order.bill_fee
        coupon_category.cash_coupon_spec.reduce_cost = dish_order.discount_amount
        order.coupon_category.CopyFrom(coupon_category)
        return order

    def get_orders(self, merchant_id, store_id=None, start_time=None, end_time=None):
        """获取商户最近的账单列表

        Args:
            merchant_id: (string) 商户ID
            store_id: (string) 门店ID
            start_time: (int64) 查询开始时间
            end_time: (int64) 查询结束时间

        Return:
            (ui_merchant_order_pb.Order[])
        """
        type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
                     wallet_pb.Transaction.ORDERING_REFUND]
        transactions = TransactionManager().get_dining_payment_type_transactions(
            payee_id=merchant_id, payee_store_id=store_id, state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time, type_list=type_list)
        orders = []
        if transactions:
            sorted_transactions = sorted(transactions, key=attrgetter('paid_time'), reverse=True)

            user_ids = [transaction.payer_id for transaction in sorted_transactions]
            users = UserDataAccessHelper().get_users_by_ids(user_ids)
            id_to_user = {user.id: user for user in users}

            for transaction in sorted_transactions:
                order = None
                if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
                    order = self.__get_self_dining_order_from_transaction(transaction, id_to_user.get(transaction.payer_id))
                elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
                    order = self.__get_self_dish_order_from_transaction(transaction, id_to_user.get(transaction.payer_id))
                elif transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                    order = self.__get_ordering_refund_transaction(transaction, id_to_user.get(transaction.payer_id))

                if order:
                    orders.append(order)
        return orders

    def get_staffs(self, merchant_id):
        """获取商户员工信息列表

        Args:
            merchant_id: (string) 商户ID

        Return:
            (ui_merchant_user_pb.MerchantStaff[])
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant:
            merchant_staffs = []
            for user_account in merchant.manager_list:
                user = MerchantUserDataAccessHelper().get_user(user_account.user_id)
                merchant_staff = ui_merchant_user_pb.MerchantStaff()
                merchant_staff.user_id = user.id
                merchant_staff.nickname = user.wechat_profile.nickname
                merchant_staff.avatar_url = user.wechat_profile.headimgurl
                merchant_staff.role = user_account.role
                for store_id in user_account.store_id_list:
                    merchant_staff.store_id_list.append(store_id)
                merchant_staffs.append(merchant_staff)
            return merchant_staffs
        return None

    def convert_merchant_store_vo(self, store):
        ui_store = ui_merchant_rules_pb.UIStore()
        ui_store.id = store.id
        ui_store.name = store.name
        ui_store.address = store.address
        ui_store.phone = store.phone
        ui_store.avg_cost_per_person = store.avg_cost_per_person
        ui_store.enable_group_dining = store.enable_group_dining
        ui_store.new_member_red_packet_value = store.new_member_red_packet_value
        ui_store.combo_meal_only = store.combo_meal_only
        ui_store.enable_combo_meal = store.enable_combo_meal

        ui_address_components = ui_merchant_rules_pb.UIAddressComponents()
        ui_poi = ui_merchant_rules_pb.UIPoi()

        ui_address_components.district = store.poi.address_components.district

        for policy in store.group_dining_coupon_policies:
            _policy = ui_store.group_dining_coupon_policies.add()
            _policy.reduce_cost = policy.reduce_cost

        ui_poi.address_components.CopyFrom(ui_address_components)
        ui_store.poi.CopyFrom(ui_poi)

        opening_hours = store.opening_hours
        ui_store.opening_hours_desc = '{}-{}'.format(date_utils.hours_text(opening_hours.daily_start_time), date_utils.hours_text(opening_hours.daily_end_time % date_utils.ONE_DAY))
        ui_store.opening_hours_state = self.__get_opening_hours_state(opening_hours)
        return ui_store

    def __get_opening_hours_state(self, opening_hours):
        """获取营业时间的状态
        """
        current_timestamp = date_utils.datetime_now_in_timezone()
        seconds_of_today = timedelta(hours=current_timestamp.hour,
                                     minutes=current_timestamp.minute,
                                     seconds=current_timestamp.second).seconds
        if opening_hours.daily_start_time <= seconds_of_today <= opening_hours.daily_end_time:
            return '营业中'
        else:
            return '己打烊'

    def get_nearby_stores(self, city=None, enable_group_dining=None, status=None,
                          lat=None, lng=None, max_distance=None,
                          page=None, size=None):
        """ 返回门店列表,以离用户远近返回
        Args:
            city: (string)城市
            enable_group_dining: (bool)是否只支持约饭
            status: 门店的状态
            lat: (float)纬度
            lng: (float)经度
            max_distance: (int)距离限制,最远返回多远的门店
            page: (int)
            size: (int)
        Return:
            group_dining_store_list.GroupDiningStore的列表
        """
        group_dining_da = GroupDiningDataAccessHelper()
        user_da = UserDataAccessHelper()

        # 30分钟前
        start_time = maya.when("30 minutes ago").datetime().timestamp()
        # 3天后
        end_time = maya.when("now").add(days=3).datetime().timestamp()

        merchants = get_nearby_stores(lng=lng, lat=lat, radius=max_distance)

        resp_stores = []
        skip = (page - 1) * size
        for merchant_with_distance in merchants[skip:skip + size]:
            merchant_id = merchant_with_distance[0].decode('utf8')
            distance = int(merchant_with_distance[1])
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

            store_vo = group_dining_store_list_pb.Store()
            store = merchant.stores[0]

            store_vo.merchant_id = merchant.id
            store_vo.store_id = store.id
            store_vo.logo_url = merchant.basic_info.logo_url
            store_vo.name = store.name
            store_vo.address = store.address
            store_vo.avg_cost_per_person = store.avg_cost_per_person
            store_vo.new_member_red_packet_value = store.new_member_red_packet_value
            store_vo.dining_type = merchant.dining_type
            store_vo.enable_ordering_service = store.enable_ordering_service  # 是否开启点菜功能
            if len(store.group_dining_coupon_policies) > 0:
                store_vo.max_reduce_cost = store.group_dining_coupon_policies[-1].reduce_cost
                store_vo.min_reduce_cost = store.group_dining_coupon_policies[0].reduce_cost
            store_vo.distance = distance
            events = group_dining_da.get_dining_events(merchant_id=merchant.id,
                                                       event_start_time=start_time, event_end_time=end_time,
                                                       visibility=group_dining_pb.GroupDiningEvent.PUBLIC,
                                                       orderby=[('eventTime', -1)],
                                                       state=group_dining_pb.GroupDiningEvent.SCHEDULED)
            for event in events:
                director = user_da.get_user(event.director_id)
                if director:
                    store_vo.director_headimgurl.append(director.wechat_profile.headimgurl)

            resp_stores.append(store_vo)
        return resp_stores

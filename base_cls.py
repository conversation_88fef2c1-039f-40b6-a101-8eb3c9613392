# -*- coding: utf-8 -*-

import os
from google.protobuf import json_format


class BaseCls:

    def __init__(self, *args, **kargs):
        self.__dict__.update(**kargs)

    def __getattr__(self, key):
        value = self.__dict__.get(key, None)
        return value

    def __setattr__(self, key, value):
        self.__dict__[key] = value

    def to_dict(self, data):
        if data is None:
            return None
        data = json_format.MessageToDict(
            data, including_default_value_fields=True)
        return data

    def batch_to_dict(self, datas):
        if datas is None:
            return []
        return [self.to_dict(data) for data in datas]

    @property
    def is_test(self):
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == "test":
            return True
        return False

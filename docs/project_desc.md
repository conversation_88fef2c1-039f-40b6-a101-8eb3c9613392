# 项目描述

- 本项目为点餐小程序,业务助手小程序,商家助手小程序三合一
- 时来模式的点餐需要对接到pos-service的收银机系统,菜品以及菜品配置相关的内容,订单,商家信息等都需要做同步

#### 项目层级描述

1. 项目层级结构: 按照service->manager->dao递进,service主要是注册接口,处理逻辑大部分在manager层,而dao层主要负责数据库操作
2. 数据流基本都是使用proto对象来进行操作,并且在序列化的时候参数including_default_value_fields=True,确保每个消息字段都存在并且有默认值
3. 数据库的字段映射通过定义的proto对象序列化成Json用于存储,查询则是将数据库的Bson序列化为proto对象再做处理,有些地方为了性能和方便没有严格按照此规则
4. proto对象使用的是小蛇风格,数据库字段使用的是驼峰风格

### API描述

- POST /merchant_assist/dish/<string:merchant_id> 操作的数据在proto/ordering/dish.proto中的Dish消息中定义 #用于菜品数据修改
- GET /merchant_assist/dish/<string:merchant_id> 返回的data在proto/page/dish.proto中的DishDetailVO消息中定义 #用于商户助手菜品数据显示
- GET /merchant/dish_catalog/<string:merchant_id> 返回的data在proto/page/dish.proto中的DishCatalog消息中定义 #用于点餐小程序菜单页数据显示
- POST /staff_assist/merchant/printer_config<string:merchant_id> #操作的数据在proto/ordering/registration.proto中的PrinterConfigByType消息中定义 #用于修改打印机配置
- GET /staff_assist/merchant/printer_config<string:merchant_id> #返回的data在proto/ordering/registration.proto中的PrinterConfig和PrinterConfigByType消息中定义 #用于获取打印机配置

### proto描述

- proto/ordering/dish.proto #菜品,加料,属性等数据结构定义
- proto/page/dish.proto #菜品相关的页面查询返回数据结构定义
- proto/registration.proto #商家的扩展配置数据结构定义包含打印机配置数据结构定义

### 项目目录结构

├─ bi
│  ├─ ...
│  ├─ ordering
│  │  ├─ ...
├─ business_ops
│  ├─ ...
│  ├─ coupon
│  │  ├─ ...
│  ├─ handheld_pos
│  │  ├─ ...
│  ├─ ifeedu
│  │  └─ ...
│  ├─ logistics
│  │  ├─ ...
│  ├─ message_center
│  │  └─ ...
│  ├─ ordering
│  │  ├─ ...
│  ├─ printer
│  │  ├─ ...
│  ├─ promotion
│  │  ├─ coupon
│  │  │  ├─ ...
│  │  └─ group_purchase
│  │     ├─ ...
│  ├─ staff_assist
│  │  ├─ ...
│  ├─ user_group
│  │  ├─ ...
├─ cache
│  ├─ ...
├─ common
│  ├─ ...
│  ├─ client
│  │  ├─ ...
│  ├─ logger
│  │  ├─ ...
│  ├─ redis
│  │  └─ ...
│  ├─ utils
│  │  ├─ ...
├─ conf
│  ├─ ...
│  ├─ prod
│  │  ├─ ...
│  └─ test
│     ├─ ...
├─ controller
│  ├─ ...
│  ├─ message_center
│  │  └─ ...
│  ├─ promotion
│  │  ├─ coupon
│  │  │  ├─ ...
│  │  └─ group_purchase
│  │     ├─ ...
├─ dao
│  ├─ ...
│  ├─ logistics
│  │  └─ ...
│  ├─ message_center
│  │  ├─ ...
│  ├─ ordering
│  │  ├─ ...
│  ├─ promotion
│  │  ├─ coupon
│  │  │  ├─ ...
│  │  └─ group_purchase
│  │     ├─ ...
│  ├─ staff_assist
│  │  ├─ ...
│  ├─ statistics
│  │  └─ ...
├─ event_ops
│  ├─ ...
├─ mytests
│  ├─ ...
│  │  ├─ ...
│  ├─ core_tests
│  │  ├─ ...
│  │  └─ test_case
│  │     ├─ ...
│  ├─ interface
│  │  ├─ ...
│  │  ├─ data
│  │  │  ├─ ...
│  ├─ order
│  │  └─ ...
│  ├─ staff_assist
│  │  ├─ ...
├─ notification
│  └─ ...
├─ proto
│  ├─ ...
│  ├─ activity
│  │  ├─ ...
│  ├─ bi
│  │  ├─ ...
│  ├─ common
│  │  ├─ ...
│  ├─ finance
│  │  ├─ ...
│  ├─ group_dining
│  │  ├─ ...
│  ├─ ifeedu
│  │  ├─ ...
│  ├─ logistics
│  │  └─ ...
│  ├─ message_center
│  │  └─ ...
│  ├─ ordering
│  │  ├─ ...
│  │  ├─ hualala
│  │  │  └─ ...
│  │  ├─ keruyun
│  │  │  ├─ ...
│  ├─ page
│  │  ├─ ...
│  │  ├─ merchant_assist
│  │  │  ├─ ...
│  ├─ promotion
│  │  ├─ coupon
│  │  │  └─ ...
│  │  └─ group_purchase
│  │     └─ ...
│  ├─ sns
│  │  ├─ ...
│  ├─ statistics
│  │  └─ ...
│  ├─ ui
│  │  ├─ ...
│  │  ├─ merchant
│  │  │  ├─ ...
│  │  ├─ message_center
│  │  │  └─ ...
│  ├─ websocket
│  │  ├─ ...
│  ├─ wechat
│  │  ├─ ...
├─ service
│  ├─ ...
│  ├─ merchant_assist
│  │  └─ ...
│  ├─ message_center
│  │  └─ ...
│  ├─ ordering
│  │  ├─ ...
│  ├─ permission
│  │  └─ ...
│  ├─ promotion
│  │  ├─ coupon
│  │  │  ├─ ...
│  │  └─ group_purchase
│  │     ├─ ...
│  ├─ staff_assist
│  │  ├─ ...
│  ├─ transaction_analysis
│  │  ├─ ...
│  └─ websockets
│     ├─ ...
├─ strategy
│  ├─ ...
├─ view_ops
│  ├─ ...
│  ├─ merchant_assist
│  │  ├─ ...
└─ wechat_lib
   ├─ ...
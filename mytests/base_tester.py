# -*- coding: utf-8 -*-

import random


class BaseTester:

    sort = 999999

    def __init__(self, *args, **kargs):
        self.__dict__.update(**kargs)

    def __getattr__(self, key):
        if key in self.__dict__:
            return self.__dict__.get(key)
        return None

    def __setattr__(self, key, value):
        self.__dict__[key] = value

    @staticmethod
    def set_to_test(fn):
        setattr(fn, "test", True)
        return fn

    def random_name(self, base_name=None):
        random_value = str(random.randint(1, 50000))
        if base_name is None:
            return random_value
        return f"{base_name}-{random_value}"

    def write_id_to_file(self, path, id):
        with open(path, 'w') as f:
            f.write(id)

    def read_id_from_file(self, path):
        with open(path, "r") as f:
            id = f.readline()
            return id

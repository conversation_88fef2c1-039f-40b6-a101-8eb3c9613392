# -*- coding: utf-8 -*-

###########################
# 和钱包有关的unittest
# 1. 获取用户的钱包
# 2. 转账,成功
# 3. 转账,收款不存在
# 4. 转账,超过单日转账总额上限
# 5. 转账,超过单日转出上限
# 6. 转账,余额不足
###########################

import time
from pprint import pprint

import unittest

from unittest_helper import Meta, myprint, TestHelper

class WalletUnittest(unittest.TestCase, TestHelper, metaclass=Meta):

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(WalletUnittest, self).__init__(*args, **kargs)

        self.url_prefix = "/wallet"

    @myprint("获取用户的钱包")
    def test_get_wallet(self):
        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        pprint(self.ret)

    @myprint("转账")
    def test_transfer(self):
        payee_id = "ad4a6bef-c958-4a41-a4b6-b193d5059804"
        back_user_id = self.user_id
        bill_fee = 100

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        before_outcome_payer_balance = self.ret.get("wallet").get("balance")
        self.user_id = payee_id
        self._do_get()
        before_income_payer_balance = self.ret.get("wallet").get("balance")

        self.user_id = back_user_id
        self.path = "{prefix}/transfer".format(prefix=self.url_prefix)
        self.params = {
            "payeeId": payee_id,
            "billFee": bill_fee
        }
        self._do_post()
        # 超过单日次数
        if self.errcode == 90008 or self.errcode == 90007:
            bill_fee = 0

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        after_outcome_payer_balance = self.ret.get("wallet").get("balance")
        self.user_id = payee_id
        self._do_get()
        after_income_payer_balance = self.ret.get("wallet").get("balance")

        self.assertEqual(before_outcome_payer_balance - after_outcome_payer_balance, bill_fee)
        self.assertEqual(after_income_payer_balance - before_income_payer_balance, bill_fee)

    @myprint("转账人不存在")
    def test_transfer_payee_not_exists(self):
        payee_id = "ad4a6bef-c958-4a41-a4b6-b193d5059804"
        back_user_id = self.user_id
        bill_fee = 100

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        before_outcome_payer_balance = self.ret.get("wallet").get("balance", 0)
        self.user_id = payee_id
        self._do_get()
        before_income_payer_balance = self.ret.get("wallet").get("balance", 0)

        self.user_id = back_user_id
        self.path = "{prefix}/transfer".format(prefix=self.url_prefix)
        self.params = {
            "payeeId": payee_id,
            "billFee": bill_fee
        }
        self._do_post()
        # 收款人不存在
        self.assertEqual(self.ret.get("errcode"), 50002)

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        after_outcome_payer_balance = self.ret.get("wallet").get("balance", 0)
        self.user_id = payee_id
        self._do_get()
        after_income_payer_balance = self.ret.get("wallet").get("balance", 0)

        self.assertEqual(before_outcome_payer_balance - after_outcome_payer_balance, 0)
        self.assertEqual(after_income_payer_balance - before_income_payer_balance, 0)

    @myprint("转账余额不足")
    def test_transfer_payee_not_exists(self):
        payee_id = "ad4a6bef-c958-4a41-a4b6-b193d5059804"
        back_user_id = self.user_id
        bill_fee = 1000000

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        before_outcome_payer_balance = self.ret.get("wallet").get("balance", 0)
        self.user_id = payee_id
        self._do_get()
        before_income_payer_balance = self.ret.get("wallet").get("balance", 0)

        self.user_id = back_user_id
        self.path = "{prefix}/transfer".format(prefix=self.url_prefix)
        self.params = {
            "payeeId": payee_id,
            "billFee": bill_fee
        }
        self._do_post()
        self.assertEqual(self.errcode, 90001)

        self.path = "{prefix}".format(prefix=self.url_prefix)
        self._do_get()
        after_outcome_payer_balance = self.ret.get("wallet").get("balance", 0)
        self.user_id = payee_id
        self._do_get()
        after_income_payer_balance = self.ret.get("wallet").get("balance", 0)

        self.assertEqual(before_outcome_payer_balance - after_outcome_payer_balance, 0)
        self.assertEqual(after_income_payer_balance - before_income_payer_balance, 0)

if __name__ == '__main__':
    unittest.main(warnings = "ignore", failfast = True)

# -*- coding: UTF-8 -*-
import sys
import os
import calendar

parentPath = os.path.abspath("../../../")
if parentPath not in sys.path:
    print(parentPath)
    sys.path.insert(0, parentPath)

from flask import Flask
from flask import jsonify
from flask import request
from flask import redirect
from flask import Response
from common.utils import date_utils
from common.utils.db_utils import *
from common.utils.WXBizDataCrypt import *
import proto.merchant_rules_pb2 as merchant_rules_pb2
import proto.user_pb2 as user_pb2
import proto.common.query_pb2 as query_pb2
import proto.ui.merchant.user_pb2 as ui_merchant_user_pb2
import proto.ui.merchant.order_pb2 as ui_merchant_order_pb2

import proto.merchant_service_pb2 as merchant_service_pb2

from wechat_lib.authorization_process import *
import bo.merchant_assistant_process as merchant_assistant_process
import bo.member_card_manager as member_card_manager
from service.base_responses import *
import requests
import json
from common.config import *
from common.constant import const
from common.utils.log_utils import *
import uuid
import qrcode

# 获取订单列表

def create_get_url(api):
    url = 'http://{}:{}{}'.format(config.SERVICE_HOST, config.MERCHANT_ASSISTANT_SERVICE_PORT, api)
    return url


def query_merchant_new_member_list():
    url = create_get_url("/merchant/1/new_member/list")
    print(url)
    # query_request = merchant_service_pb2.QueryMerchantNewMembersRequest()
    # query_request.merchant_id = "1"
    # data = json_format.MessageToDict(query_request,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    response = requests.get(url)
    print(response.text)

query_merchant_new_member_list()

# 获取最新会员列表
def query_merchant_new_member_list():
    url = create_get_url("/merchant/1/order/list")
    # query_request = merchant_service_pb2.QueryMerchantOrdersRequest()
    # query_request.merchant_id = "1"
    # data = json_format.MessageToDict(query_request,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    # response = requests.post(url, json=data)
    response = requests.get(url)
    print(response.text)

# query_merchant_new_member_list()

# 获取营销计划
def query_merchant_new_plan_list():
    # LOG = LogUtils(request)
    pass

    # query_request = json_format.ParseDict(request.json, merchant_service_pb2.QueryMerchant(),
    #                                       ignore_unknown_fields=True)
    #
    # merchant_id = query_request.merchant_id
    #
    # response
    # resp = json_format.MessageToDict(response,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    # LOG.info_api_responses(resp)
    # return resp


# 查询趋势分析请求
def query_merchant_trend_analysis():
    # url = create_get_url("/merchant/trend_analysis")
    # query_request = merchant_service_pb2.QueryMerchantTrendAnalysisRequest()
    # query_request.merchant_id = "1"
    # query_request.date_filter.date_start = ""
    # query_request.date_filter.date_end = ""
    # data = json_format.MessageToDict(query_request,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    # response = requests.post(url, json=data)
    # print(response.text)
    url = create_get_url("/merchant/1/trend_analysis")
    # query_request = merchant_service_pb2.QueryMerchantOrdersRequest()
    # query_request.merchant_id = "1"
    # data = json_format.MessageToDict(query_request,
    #                                  including_default_value_fields=True,
    #                                  use_integers_for_enums=True)
    # response = requests.post(url, json=data)
    response = requests.get(url)
    print(response.text)

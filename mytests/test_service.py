# -*- coding: UTF-8 -*-
import requests
import json
from common.config import *
import proto.wechat_member_card_pb2 as wechat_member_card_pb2
import proto.membership_pb2 as membership_pb2
import proto.wechat_common_pb2 as wechat_common_pb2
import proto.authorizer_pb2 as authorizer_pb2
from common.utils.db_utils import *
from google.protobuf import json_format
# def test_get_revenue():
#     response = requests.get("http://*************:5000/get_token")
#     print(response.text)
#
# test_get_revenue()


# data = {
#         "card": {
#             "card_type": "MEMBER_CARD",
#             "member_card": {
#                 "background_pic_url": "https://mmbiz.qlogo.cn/mmbiz/",
#                 "base_info": {
#                     "logo_url": "http://mmbiz.qpic.cn/mmbiz/iaL1LJM1mF9aRKPZ/0",
#                     "brand_name": "海底捞",
#                     "code_type": "CODE_TYPE_TEXT",
#                     "title": "海底捞会员卡",
#                     "color": "Color010",
#                     "notice": "使用时向服务员出示此券",
#                     "service_phone": "020-88888888",
#                     "description": "不可与其他优惠同享",
#                     "date_info": {
#                         "type": "DATE_TYPE_PERMANENT"
#                     },
#                     "sku": {
#                         "quantity": 50000000
#                     },
#                     "get_limit": 3,
#                     "use_custom_code": False,
#                     "can_give_friend": True,
#                     "location_id_list": [
#                         123,
#                         12321
#                     ],
#                     "custom_url_name": "立即使用",
#                     "custom_url": "http://weixin.qq.com",
#                     "custom_url_sub_title": "6个汉字tips",
#                     "promotion_url_name": "营销入口1",
#                     "promotion_url": "http://www.qq.com",
#                     "need_push_on_view": True
#                 },
#                  "advanced_info": {
#                    "use_condition": {
#                        "accept_category": "鞋类",
#                        "reject_category": "阿迪达斯",
#                        "can_use_with_other_discount": True
#                    },
#                  "abstract": {
#                        "absticon_url_listract": "微信餐厅推出多种新季菜品，期待您的光临",
#                        "icon_url_list": [
#                            "http://mmbiz.qpic.cn/mmbiz/p98FjXy8LacgHxp3sJ3vn97bGLz0ib0Sfz1bjiaoOYA027iasqSG0sjpiby4vce3AtaPu6cIhBHkt6IjlkY9YnDsfw/0"
#                        ]
#                    },
#                    "text_image_list": [
#                        {
#                            "image_url": "http://mmbiz.qpic.cn/mmbiz/p98FjXy8LacgHxp3sJ3vn97bGLz0ib0Sfz1bjiaoOYA027iasqSG0sjpiby4vce3AtaPu6cIhBHkt6IjlkY9YnDsfw/0",
#                            "text": "此菜品精选食材，以独特的烹饪方法，最大程度地刺激食 客的味蕾"
#                        },
#                        {
#                            "image_url": "http://mmbiz.qpic.cn/mmbiz/p98FjXy8LacgHxp3sJ3vn97bGLz0ib0Sfz1bjiaoOYA027iasqSG0sj piby4vce3AtaPu6cIhBHkt6IjlkY9YnDsfw/0",
#                            "text": "此菜品迎合大众口味，老少皆宜，营养均衡"
#                        }
#                    ],
#                    "time_limit": [
#                        {
#                            "type": "MONDAY",
#                            "begin_hour":0,
#                            "end_hour":10,
#                            "begin_minute":10,
#                            "end_minute":59
#                        },
#                        {
#                            "type": "HOLIDAY"
#                        }
#                    ],
#                    "business_service": [
#                        "BIZ_SERVICE_FREE_WIFI",
#                        "BIZ_SERVICE_WITH_PET",
#                        "BIZ_SERVICE_FREE_PARK",
#                        "BIZ_SERVICE_DELIVER"
#                    ]
#                },
#                 "supply_bonus": True,
#                 "supply_balance": False,
#                 "prerogative": "test_prerogative",
#                 "auto_activate": True,
#                 "custom_field1": {
#                     "name_type": "FIELD_NAME_TYPE_LEVEL",
#                     "url": "http://www.qq.com"
#                 },
#                 "activate_url": "http://www.qq.com",
#                 "custom_cell1": {
#                     "name": "使用入口2",
#                     "tips": "激活后显示",
#                     "url": "http://www.qq.com"
#                 },
#                 "bonus_rule": {
#                     "cost_money_unit": 100,
#                     "increase_bonus": 1,
#                     "max_increase_bonus": 200,
#                     "init_increase_bonus": 10,
#                     "cost_bonus_unit": 5,
#                     "reduce_money": 100,
#                     "least_money_to_use_bonus": 1000,
#                     "max_reduce_bonus": 50
#                 },
#                 "discount": 10
#             }
#         }
#     }
#
# print(json.loads(json.dumps(data['card'])))
# wechatMember_card_spec = json_format.ParseDict(json.loads(json.dumps(data['card'])), wechat_member_card_pb2.WechatMemberCardCategory(),
#                              ignore_unknown_fields=True)
# print(wechatMember_card_spec)

# wechat_member_card_category = wechat_member_card_pb2.WechatMemberCardCategory()
# wechat_member_card_category.card_type = 'MEMBER_CARD'
# wechat_member_card_category.member_card.background_pic_url="https://mmbiz.qlogo.cn/mmbiz/"
# print(wechat_member_card_category)
#
# member_card_category = membership_pb2.MemberCardCategory()
# member_card_category.wechat_card_spec.CopyFrom(wechat_member_card_category.member_card)
#
# print(member_card_category)

# data = {
# "authorizer_info": {
# "nick_name": "微信SDK Demo Special",
# "head_img": "http://wx.qlogo.cn/mmopen/GPy",
# "service_type_info": { "id": 2 },
# "verify_type_info": { "id": 0 },
# "user_name":"gh_eb5e3a772040",
# "principal_name":"腾讯计算机系统有限公司",
# "business_info": {"open_store": 0, "open_scan": 0, "open_pay": 0, "open_card": 0, "open_shake": 0},
# "qrcode_url":"URL",
# "signature": "时间的水缓缓流去",
# "MiniProgramInfo": {
#     "network": {
#         "RequestDomain":["https://www.qq.com","https://www.qq.com"],
#         "WsRequestDomain":["wss://www.qq.com","wss://www.qq.com"],
#         "UploadDomain":["https://www.qq.com","https://www.qq.com"],
#         "DownloadDomain":["https://www.qq.com","https://www.qq.com"],
#     },
#     "categories":[{"first":"资讯","second":"文娱"},{"first":"工具","second":"天气"}],
#     "visit_status": 0,
# }
# },
# "authorization_info": {
# "authorization_appid": "wxf8b4f85f3a794e77",
# "func_info": [
# { "funcscope_category": { "id": 17 } },
# { "funcscope_category": { "id": 18 } },
# { "funcscope_category": { "id": 19 } }
# ]
# }
# }
#
# import urllib.parse
# values = {
#     'userId': "1",
#     'staffId': "2",
#     'merchantId': "3"
# }
# ""
# data = urllib.parse.quote_plus("http://tickets.zhiyi.cn/authorizer/authorized?userId=56bda466-c3a5-4c64-be74-250b6d812ca0&staffId=b617e666-5ca2-4c41-a302-b7c7a9045666&merchantId=oc2s75Am_axAwQB0bR6EIZKBLagM")
# print(data)
# print(json.loads(json.dumps(data)))
# wechatMember_card_spec = json_format.ParseDict(json.loads(json.dumps(data)), authorizer_pb2.Authorizer(),
#                              ignore_unknown_fields=True)
# print(wechatMember_card_spec)

# data = {
#     "card": {
#         "card_type": "MEMBER_CARD",
#         "member_card": {
#             "background_pic_url": "https://mmbiz.qlogo.cn/mmbiz/",
#             "base_info": {
#                 "logo_url": "http://mmbiz.qpic.cn/mmbiz/iaL1LJM1mF9aRKPZ/0",
#                 "brand_name": "海底捞",
#                 "code_type": "CODE_TYPE_TEXT",
#                 "title": "海底捞会员卡",
#                 "color": "Color010",
#                 "notice": "使用时向服务员出示此券",
#                 "service_phone": "020-88888888",
#                 "description": "不可与其他优惠同享",
#                 "date_info": {
#                     "type": "DATE_TYPE_PERMANENT"
#                 },
#                 "sku": {
#                     "quantity": 50000000
#                 },
#                 "get_limit": 3,
#                 "use_custom_code": False,
#                 "can_give_friend": True,
#                 "location_id_list": [
#                     123,
#                     12321
#                 ],
#                 "custom_url_name": "立即使用",
#                 "custom_url": "http://weixin.qq.com",
#                 "custom_url_sub_title": "6个汉字tips",
#                 "promotion_url_name": "营销入口1",
#                 "promotion_url": "http://www.qq.com",
#                 "need_push_on_view": True
#             },
#              "advanced_info": {
#                "use_condition": {
#                    "accept_category": "鞋类",
#                    "reject_category": "阿迪达斯",
#                    "can_use_with_other_discount": True
#                },
#                "business_service": [
#                    "BIZ_SERVICE_FREE_WIFI",
#                    "BIZ_SERVICE_WITH_PET",
#                    "BIZ_SERVICE_FREE_PARK",
#                    "BIZ_SERVICE_DELIVER"
#                ]
#            },
#             "supply_bonus": True,
#             "supply_balance": False,
#             "prerogative": "test_prerogative",
#             "auto_activate": True,
#             "custom_field1": {
#                 "name_type": "FIELD_NAME_TYPE_LEVEL",
#                 "url": "http://www.qq.com"
#             },
#             "activate_url": "http://www.qq.com",
#             "custom_cell1": {
#                 "name": "使用入口2",
#                 "tips": "激活后显示",
#                 "url": "http://www.qq.com"
#             },
#             "bonus_rule": {
#                 "cost_money_unit": 100,
#                 "increase_bonus": 1,
#                 "max_increase_bonus": 200,
#                 "init_increase_bonus": 10,
#                 "cost_bonus_unit": 5,
#                 "reduce_money": 100,
#                 "least_money_to_use_bonus": 1000,
#                 "max_reduce_bonus": 50
#             },
#             "discount": 10
#         }
#     }
# }
#
# print(json.loads(json.dumps(data['card'])))
# wechatMember_card_spec = json_format.ParseDict(json.loads(json.dumps(data['card'])), wechat_member_card_pb2.WechatMemberCardCategory(),
#                              ignore_unknown_fields=True)
# print(wechatMember_card_spec)

# 根据 userid 查询 商户信息
#     "managerList": {
#       $elemMatch: { userId: userid },
#     }

# userid -> merchantId

# merchantId
# @app.route('/merchant/info', methods=['GET'])
# def merchant_info():
#     """
#     15. 获取商家所有信息
#     GET /merchant/info
#     请求参数 {merchantId}
#     响应主体: Merchant里的所有数据
#     {}
#     """

# userid = '56bda466-c3a5-4c64-be74-250b6d812ca0'
#
# db_utils = DbUtils()
#
# data = db_utils.get_merchant(userid=userid)
# print(data)

#
# openid = 'onIO15IiLlDzotm1c4n6fhSeW13I'
#
# db_utils = DbUtils()
#
# data = db_utils.get_wechat_user(openid=openid)
# print(data)

decrypt_data = {'openId': 'onIO15IiLlDzotm1c4n6fhSeW13I', 'nickName': '喧嚣の风', 'gender': 2, 'language': 'zh_CN', 'city': 'Shenzhen', 'province': 'Guangdong', 'country': 'China', 'avatarUrl': 'https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLhx780iaeV1wckRlRUHHoRQXcEubJhVrdVXXaU3LJuMMvGkZNjaW1MHBkSnLITKN0X4cWxNXWNIwQ/132', 'unionId': 'olICR1tmRTREMgy-BKtbZF_s2ZVE', 'watermark': {'timestamp': 1554112698, 'appid': 'wxb481ce8d016fb2f3'}}
user  = user_pb2.User()

user.wechat_profile.openid = decrypt_data['openId']
user.wechat_profile.nickname = decrypt_data['nickName']
user.wechat_profile.sex = decrypt_data['gender']
user.wechat_profile.city = decrypt_data['city']
user.wechat_profile.province = decrypt_data['province']
user.wechat_profile.country = decrypt_data['country']
if 'country' in decrypt_data.keys():
    user.wechat_profile.unionid = decrypt_data['unionId']
user.wechat_profile.watermark.appid = decrypt_data['watermark']['appid']
user.wechat_profile.watermark.timestamp = decrypt_data['watermark']['timestamp']

# -*- coding: UTF-8 -*-
import sys
import os
import calendar

import requests
from common.config import *

def create_get_url(api):
    # url = 'http://{}:{}{}'.format(config.SERVICE_HOST, config.AUTHORIZATION_PROCESS_SERVICE_PORT, api)
    url = 'http://{}:{}{}'.format("**********", config.AUTHORIZATION_PROCESS_SERVICE_PORT, api)
    return url

# 获取有效的token

def token_api(appid):
    url = create_get_url("/get_token/{}".format(appid))
    print(url)
    response = requests.get(url)
    print(response.text)

def get_component_token_appid():
    url = create_get_url("/get_component_token")
    print(url)
    response = requests.get(url)
    print(response.text)


def get_platform_token_appid():
    url = create_get_url("/get_platform_token")
    print(url)
    response = requests.get(url)
    print(response.text)


def get_authorizer_token():
    url = create_get_url("/get_authorizer_token/{}".format(config.WECHAT_MINIPROGRAM_STAFF_APPID))
    print(url)
    response = requests.get(url)
    print(response.text)

# token_api(config.WECHAT_MINIPROGRAM_STAFF_APPID)
# get_component_token_appid()
# get_platform_token_appid()
get_authorizer_token()



# -*- coding: utf-8 -*-


"""
后付款模式单元测试:
1. 创建订单
2. 加菜
3. 时来钱包支付
"""
import json
import unittest

from unittest_helper import Meta, TestHelper


class CreateOrder(unittest.TestCase, TestHelper, metaclass=Meta):

    order_id = None

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(CreateOrder, self).__init__(*args, **kargs)

    def test_create_order_kry(self):
        fd = open("create-order.json", "r")
        self.params = json.load(fd)
        self.path = "/order/create/{}".format(self.merchant_id)
        self._do_post()
        self.assertEqual(self.ret.get("errcode"), 0)
        order_id = self.ret.get("data").get("orderId")
        self.assertIsNotNone(order_id)
        self.__class__.order_id = order_id
        print("创建订单ID: {}".format(order_id))

    def test_add_dish_kry(self):
        fd = open("add-dish.json", "r")
        self.params = json.load(fd)
        self.params.update({"orderId": self.__class__.order_id})
        self.path = "/order/add_dish"
        self._do_post()
        self.assertEqual(self.ret.get("errcode"), 0)

    def test_wallet_pay(self):
        self.path = "/order/get/{}".format(self.__class__.order_id)
        self._do_get()
        self.assertEqual(self.ret.get("errcode"), 0)

        self.path = "/payment/prepay"
        self.params = {
            'merchantId': self.merchant_id,
            'orderId': self.__class__.order_id,
            'billFee': self.ret.get("data").get("totalFee"),
            'paidFee': self.ret.get("data").get("paidFee"),
            'noDiscountBillFee': 0,
            'transactionType': 'SELF_DISH_ORDER_PAYMENT',
            'payMethod': 'WALLET',
            'isInvoice': False
        }
        self._do_post()
        self.assertEqual(self.ret.get("errcode"), 0)


if __name__ == '__main__':
    unittest.main(warnings="ignore", failfast=True)

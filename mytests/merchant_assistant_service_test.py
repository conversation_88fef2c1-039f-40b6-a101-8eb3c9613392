# -*- coding: UTF-8 -*-
import sys
import os
import calendar

import requests
from common.config import *


def create_get_url(api):
    # url = 'http://{}:{}{}'.format(config.SERVICE_HOST, config.MERCHANT_ASSISTANT_SERVICE_PORT, api)
    url = 'http://{}:{}{}'.format("**********", config.MERCHANT_ASSISTANT_SERVICE_PORT, api)
    return url


def token_api():
    url = create_get_url("/get_token")
    print(url)
    response = requests.get(url)
    print(response.text)


# 1.获取用户登录态
def user_login():
    """
    # POST /user/login
    # 请求主体 { code }
    # 请求头 { fromPlatform: “merchant” }
    # 流程：使用 code 调用微信接口 https://api.weixin.qq.com/sns/jscode2session 获取openid, session_key, unionid，其中 session_key 不能在客户端使用
    # 响应主体 { openid, unionid }
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api-backend/code2Session.html
    :return:
    """
    url = create_get_url("/user/login")
    print(url)
    # code = "1"
    code = "023EpWuH0Lj5Af2Qs9uH0unJuH0EpWuh"
    data = {'code': code}
    response = requests.post(url, json=data)
    print(response.text)


# user_login()


def get_merchants():
    url = create_get_url("/user/get_merchants")
    print(url)
    # code = "1"
    userId = "47ee9a0f-80f3-41d7-ac36-d3bbd31d73fc"
    data = {'userId': userId}
    response = requests.get(url, params=data)
    print(response.text)


# get_merchants()

# 2.保存用户基本信息
def user_info_save():
    """
    # POST /user/info/save
    # 请求主体 { encryptData, iv, openid }
    # 请求头 { fromPlatform: “merchant” }
    # 流程：通过session_key, iv, 小程序appid 解密 encryptData，得到用户基本信息(nickName, gender, province, city, country, avatarUrl, unionId)
    # 参考：https://developers.weixin.qq.com/miniprogram/dev/api/wx.getUserInfo.html
    """
    url = create_get_url("/user/info/save")
    print(url)
    iv = "iv"
    userId = "fdc34edb-b717-4c58-b892-a46e54d32dae"
    encrypted_data = "encrypted_data"
    data = {
        'iv': iv,
        'userId': userId,
        'encryptedData': encrypted_data,
    }
    response = requests.post(url, json=data)
    print(response.text)


# user_info_save()

# 3.商家授权
def authorizer_auth():
    """
    # GET /authorizer/auth
    # 请求参数 { unionid }
    # 流程：跳转到微信授权页面，自定义回调URL: /authorizer/authorized?unionid={unionid}，商家授权完成时，页面会跳转到此回调URL，微信服务器给开发服务器发送一条“授权”推送。
    # 参考：https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1453779503&token=&lang=zh_CN
    """
    url = create_get_url("/authorizer/auth")
    print(url)
    unionid = "unionid"
    merchant_id = "merchant_id"
    data = {
        'staffId': unionid,
        'userId': unionid,
        'merchant_id': merchant_id
    }
    response = requests.get(url, params=data)
    print(response.text)


# authorizer_auth()

# 4.商家授权回调
def authorizer_authorized():
    """
    # GET /authorizer/authorized
    # 请求参数 { auth_code, expires_in, unionid }
    # 响应 执行下一个步骤的 HTML 代码
    # 流程：使用 auth_code, expires_id 可获取授权商家基本信息和接口调用凭据，初始化商家数据：初始化两张会员卡（一张用于非注册会员，一张用户注册会员）
    """
    url = create_get_url("/authorizer/authorized")
    print(url)
    unionid = "unionid"
    merchant_id = "merchant_id"
    auth_code = "auth_code"
    expires_in = "expires_in"
    data = {
        'unionid': unionid,
        'merchant_id': merchant_id,
        'auth_code': auth_code,
        'expires_in': expires_in
    }
    response = requests.get(url, params=data)
    print(response.text)


# authorizer_authorized()

def staff_add():
    """
    1.	添加员工
    POST /staff/add
    请求参数 { userId }
    流程: 超级管理员可以添加地推(业务)员工, 会将该员工的User id复制到Staff表格中, 员工享有注册商家的权限
    """

    url = create_get_url("/staff/add")
    print(url)
    userId = "userId"

    data = {
        'id': 'weer',
        'userId': userId
    }
    response = requests.post(url, json=data)
    print(response.text)

# staff_add()

def staff_add():
    """
    1.	添加员工
    POST /staff/add
    请求参数 { userId }
    流程: 超级管理员可以添加地推(业务)员工, 会将该员工的User id复制到Staff表格中, 员工享有注册商家的权限
    """

    url = create_get_url("/staff/add")
    print(url)
    userId = "userId"

    data = {
        'id': 'weer',
        'userId': userId
    }
    response = requests.post(url, json=data)
    print(response.text)



def get_uuid():
    """
    2.	创建商家
    POST /merchant/create
    请求参数 {staffId}
    响应主题 {merchantId}
    流程: 业务员工创建merchant主体(除了merchantId, 其他为空), 并且有一张表来保存merchantId和staffId的对应关系.
    """
    url = create_get_url("/uuid")
    print(url)
    staffId = "staffId"

    # data = {
    #     'staffId': staffId,
    # }
    # response = requests.get(url, json=data)
    response = requests.get(url)
    print(response.text)


# get_uuid()

# 设置允许的文件格式
ALLOWED_EXTENSIONS = set(['png', 'jpg', 'JPG', 'PNG', 'bmp'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS


def image_upload():
    """
    3.	图片上传服务
    POST /image/upload
    响应主题 {imageUrl}
    流程: 上传一张图片, 返回图片的url
    """
    url = create_get_url("/image/upload")
    print(url)
    staffId = "staffId"

    data = {
        'staffId': staffId,
    }
    response = requests.post(url, json=data)
    print(response.text)


def merchant_qrcode_create():
    """
    4.	生成商家授权二维码
    GET /merchant/qrcode
    请求参数 {merchantId}
    响应主体 {qrcodeUrl}
    流程: 业务员工创建Merchant主体后, 生成一个二维码给商户扫描, 扫描后跳转到商户助手的小程序相应链接(/merchantHelper/merchantAuth/{merchantId})
    """

    url = create_get_url("/staff/qrcode/create")
    print(url)
    merchantId = "2ef3114b88a843c686df481cadb"
    userId = "114b-88a8-43c6-86df-be3c7"
    data = {
        'staffId': userId,
        'merchantId': merchantId,
        'userId': userId
    }
    # data = {}
    response = requests.post(url, )
    print(response.text)


merchant_qrcode_create()


def get_qrcode_info():
    url = create_get_url("/merchant/get_qrcode_info")
    print(url)
    qrcodeId = "c1dd7e39b45147b1aac77a9e31bc922e"
    data = {
        'qrcodeId': qrcodeId

    }
    response = requests.get(url, params=data)
    print(response.text)


# get_qrcode_info()

def authorizer_status():
    """
    5.	获取授权状态
    GET /authorizer/status
    请求参数 {merchantId}
    流程：去authorizer数据集查看是否有绑定了merchantId的
    :return:
    """
    url = create_get_url("/authorizer/status")
    print(url)
    merchantId = "merchant_id"

    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# authorizer_status()

def merchant_info_basic_save():
    """
    5.	保存商家基本信息
    POST /merchant/info/basic/save
    请求参数 {
    id, name, displayName, foundedYear, headquaterAddress, contactName, contactPhone, contactMobile, contactEmail
    }
    """
    id = "483442151d0c4643a3f7ad52edc28469"
    name = "name"
    displayName = "displayName"
    api = "/merchant/{}/info/basic/save".format(id)
    print(api)
    url = create_get_url(api)
    print(url)

    data = {
        'name': name,
    }
    response = requests.post(url, json=data)
    print(response.text)


# merchant_info_basic_save()

def merchant_info_stores_save():
    """
    6.	保存商家门店信息
    POST /merchant/info/stores/save
    请求参数{
    stores: [
    {
    name, address, phone, postalCode, numEmployees, capacity, posType
    }
    ]
    }
    流程: 业务员输入门店信息并保存到merchant实例的stores中.
    """
    id = "50a59ea7b57b48308c8365ca6d9268e9"
    api = "/merchant/{}/info/stores/save".format(id)
    url = create_get_url(api)
    print(url)

    data = {'stores': [
        {
            'name': '点都德(A8店)',
            'address': '广东省深圳市南山区科园路1002号A8音乐大厦4楼',
            'phone': '212321322',
        },
        {
            'name': '点都德(A7店)',
            'address': '广东省深圳市南山区科园路1002号A8音乐大厦6楼',
            'phone': '212321322',
        }
    ]
    }
    response = requests.post(url, json=data)
    print(response.text)


# merchant_info_stores_save()


def merchant_info_qualification_save():
    """
    7.	保存商家资质信息
    POST /merchant/info/qualification/save
    请求参数: MerchantQualifications里的所有数据
    {
      business_type, catering_type, customer_service_phone, food_distribution_permit_url, catering_service_permit_url, license_photo_url, license_number, is_license_forever, license_start, license_end, license_scope, id_holder_type, id_type, id_front_photo, id_back_photo, id_number, id_name, is_id_forever, id_card_start, id_card_end, entrance_photo_url, store_photo_url, pos_photo_url, other_photo_urls
    }
    流程: 业务员输入商家资质信息并保存到merchant 实例的MerchantQualifications中
    """
    id = "50a59ea7b57b48308c8365ca6d9268e9"
    api = "/merchant/{}/info/qualification/save".format(id)
    url = create_get_url(api)
    print(url)

    data = {
        'customer_service_phone': '1',
    }
    response = requests.post(url, json=data)
    print(response.text)


# merchant_info_qualification_save()

def merchant_info_payment_save():
    """
    8.	保存商家支付信息
    POST /merchant/info/payment/save
    请求参数: paymentInfo里的所有数据
    {
    bank_account_type, bank_name, branch_name, account_number
    }
    流程: 业务员输入商家支付信息并保存到Merchant实例的paymentInfo里面.
    """
    id = "50a59ea7b57b48308c8365ca6d9268e9"
    api = "/merchant/{}/info/payment/save".format(id)
    url = create_get_url(api)
    print(url)

    data = {
        'bank_name': 'bank_name',
    }
    response = requests.post(url, json=data)
    print(response.text)


# merchant_info_payment_save()


def merchant_activate():
    """
    9.	激活商家
    POST /merchant/activate
    请求参数 {merchantId}
    流程: 业务员工激活商家账户, 将merchant的status从inactive改为active. 这样商家就可以刷新商家助手进入BI界面了.
    """
    url = create_get_url('/merchant/activate')
    print(url)

    merchantId = '50a59ea7b57b48308c8365ca6d9268e9'
    data = {
        'merchantId': merchantId,
    }
    response = requests.post(url, json=data)
    print(response.text)


# merchant_activate()

def merchant_list():
    """
    10. 获取员工下的商家列表
    GET /merchant/list/
    请求参数: {staffId}
    响应主体: {merchant_id_list:[]}
    流程: 获取某个员工名下所有的商家列表
    """

    url = create_get_url('/merchant/list')
    print(url)
    staffId = 'unionid'
    data = {
        'staffId': staffId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_list()

def merchant_info_basic():
    """
    11. 获取商家基本信息
    GET /merchant/info/basic
    请求参数 {merchantId}
    响应主体
    {
    id, name, displayName, foundedYear, headquaterAddress, contactName, contactPhone, contactMobile, contactEmail
    }
    """
    url = create_get_url('/merchant/info/basic')
    print(url)
    merchantId = '50a59ea7b57b48308c8365ca6d9268e9'
    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_info_basic()

def merchant_info_stores():
    """
    12. 获取商家门店信息
    GET /merchant/info/stores
    请求参数 {merchantId}
    响应主体{
        stores: [
            {
                name, address, phone, postalCode, numEmployees, capacity, posType
            }
        ]
    }
    """
    url = create_get_url('/merchant/info/stores')
    print(url)
    merchantId = 'merchant_id'
    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_info_stores()

def merchant_info_qualification():
    """
    13. 获取商家资质信息
    GET /merchant/info/qualification
    请求参数 {merchantId}
    响应主体: MerchantQualifications里的所有数据
    {
      business_type, catering_type, customer_service_phone, food_distribution_permit_url, catering_service_permit_url, license_photo_url, license_number, is_license_forever, license_start, license_end, license_scope, id_holder_type, id_type, id_front_photo, id_back_photo, id_number, id_name, is_id_forever, id_card_start, id_card_end, entrance_photo_url, store_photo_url, pos_photo_url, other_photo_urls
    }
    """
    url = create_get_url('/merchant/info/qualification')
    print(url)
    merchantId = 'merchant_id'
    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_info_qualification()

def merchant_info_payment():
    """
    14. 获取商家支付信息
    GET /merchant/info/payment/get
    请求参数 {merchantId}
    响应主体: paymentInfo里的所有数据
    {
    bank_account_type, bank_name, branch_name, account_number
    }
    """

    url = create_get_url('/merchant/info/payment')
    print(url)
    merchantId = 'merchant_id'
    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_info_payment()

def merchant_info():
    """
    15. 获取商家所有信息
    GET /merchant/info
    请求参数 {merchantId}
    响应主体: Merchant里的所有数据
    {}
    """
    url = create_get_url('/merchant/info')
    print(url)
    merchantId = 'merchant_id'
    data = {
        'merchantId': merchantId,
    }
    response = requests.get(url, params=data)
    print(response.text)


# merchant_info()

#
def authorizer_info():
    url = create_get_url("/authorizer/info")
    print(url)

    data = {'merchantId': 'b617e666-5ca2-4c41-a302-b7c7a9045666'}
    response = requests.get(url, params=data)
    print(response.text)


def receiver_callback():
    appid = '1'
    api = "/{}/callback".format(appid)
    url = create_get_url(api)
    print(url)

    xml = """
<xml>
  <ToUserName><![CDATA[toUser]]></ToUserName>
  <FromUserName><![CDATA[fromUser]]></FromUserName>
  <CreateTime>1348831860</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[this is a test]]></Content>
  <MsgId>1234567890123456</MsgId>
</xml>
"""
    headers = {
        'Content-Type': 'text/xml',
    }

    response = requests.post(url, headers=headers, data=xml)
    print(response.text)


def receiver():
    url = create_get_url("/<string:appid>/callback")
    print(url)

    data = {'merchantId': 'b617e666-5ca2-4c41-a302-b7c7a9045666'}
    response = requests.post(url, params=data)
    print(response.text)


# authorizer_info()
# merchant_barcode()
# get_merchants()
# receiver_callback()

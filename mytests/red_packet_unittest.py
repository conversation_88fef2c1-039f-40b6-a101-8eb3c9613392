# -*- coding: utf-8 -*-
############
# redpacket unittest
############

import time
from pprint import pprint

import unittest

from unittest_helper import Meta, myprint, TestHelper

class RedPacketUnittest(unittest.TestCase, TestHelper, metaclass=Meta):
    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(RedPacketUnittest, self).__init__(*args, **kargs)
        self.url_prefix = "/red_packet"

if __name__ == "__main__":
    unittest.main(warnings="ignore", failfast=True)

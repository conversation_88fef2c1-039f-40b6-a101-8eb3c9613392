import os
import sys

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    print(parentPath)
    sys.path.insert(0, parentPath)

from scripts.services import service_common
service_common.set_environment_var('test')

from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper


if __name__ == '__main__':
    coupon_category_da = CouponCategoryDataAccessHelper()
    coupon_category = coupon_category_da.get_coupon_category('pAdhkxExCFhn-dvR0AQqQOYFSOYs')
    print(coupon_category)

    coupon_categories = coupon_category_da.get_coupon_categories('7b0b676a54fa4245bbb7753df0173b4e')
    print(coupon_categories)

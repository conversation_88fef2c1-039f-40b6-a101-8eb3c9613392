import os
import sys

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    print(parentPath)
    sys.path.insert(0, parentPath)

from scripts.services import service_common
service_common.set_environment_var('test')

from common.redis import constants
from cache.redis_client import RedisClient

# RedisClient
redis_helper = RedisClient()
redis_helper.publish('{}/{}'.format(constants.GROUP_DINING_ROOM_PREFIX, 'ID'), 'value')

# -*- coding: utf-8 -*-

import requests


def get_index(last_index):
    if last_index == "":
        return 'a'
    last_chr = last_index[-1]
    if last_chr == 'z':
        return "{}a".format(last_index)
    else:
        return "{}{}".format(last_index[:-1], chr(ord(last_chr) + 1))


def myprint(str):
    def print_decorate(fn):
        def print_some(*args, **kargs):
            print("\n>>>>>>>>>> {} 开始 >>>>>>>>>>>>>>>".format(str))
            fn(*args, **kargs)
            print(">>>>>>>>>> {} 结束 >>>>>>>>>>>>>>>".format(str))
            print("-------------------------------------------------------")
        return print_some
    return print_decorate


class Meta(type):
    def __new__(cls, name, bases, namespace, **kwargs):
        index = ""
        names = namespace.keys()
        keys = []
        for key in names:
            keys.append(key)
        for key in keys:
            if key.startswith("test_"):
                index = get_index(index)
                new_name = "test_{}_{}".format(index, key.split("test_")[1])
                namespace.update({
                    new_name: namespace.get(key)
                })
                del namespace[key]
        return super().__new__(cls, name, bases, namespace, **kwargs)


class TestHelper(object):

    def __init__(self):
        self.user_id = "ad7cd36e-f1b7-4d20-83f2-5115b2267376"  # inmove
        self.user_not_exists = "ad7cd36ethe user not exists5b2267376"
        self.merchant_id = "d3348a6659ac401486d53753ea58e8d2"
        self.store_id = "d3348a6659ac401486d53753ea58e8d2_0"
        self.invitee_id = "ad4a6bef-c958-4a41-a4b6-b193d5059804"
        self.host = "http://127.0.0.1:12901"
        self.params = {}
        self.path = ""

    def _do_get(self):
        if self.path == "":
            self.assertTrue(False)
        self.url = "{}{}".format(self.host, self.path)
        if self.params:
            self.url += "?"
        for key, value in self.params.items():
            if isinstance(value, list):
                for v in value:
                    self.url += "{key}={value}&".format(key = key, value = v)
            if isinstance(value, str):
                self.url += "{key}={value}&".format(key = key, value = value)
        if self.url[-1] == '&':
            self.url = self.url[:-1]
        headers = {
            "userId": self.user_id,
            "merchantId": self.merchant_id
        }
        ret = requests.get(self.url, timeout = 30, headers = headers).json()
        self.errcode = ret.get("errcode")
        self.ret = ret

    def _do_post(self):
        if self.path == "":
            self.assertTrue(False)
        self.url = "{}{}?".format(self.host, self.path)
        headers = {
            "userId": self.user_id,
            "merchantId": self.merchant_id
        }
        ret = requests.post(self.url, json = self.params, timeout = 30, headers = headers).json()
        self.errcode = ret.get("errcode")
        self.ret = ret

# -*- coding: utf-8 -*-


"""
菜单页 unittest
"""

import unittest

from unittest_helper import Meta, TestHelper


class DishCatalog(unittest.TestCase, TestHelper, metaclass=Meta):

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(DishCatalog, self).__init__(*args, **kargs)

    def test_dish_catalog_kry(self):
        self.path = "/merchant/dish_catalog/d3348a6659ac401486d53753ea58e8d2"
        self._do_get()
        self.assertEqual(self.ret.get("errcode"), 0)

    def test_dish_catalog_hll(self):
        self.path = "/merchant/dish_catalog/d3348a6659ac401486d53753ea58e8d3"
        self._do_get()
        self.assertEqual(self.ret.get("errcode"), 0)

    def test_dish_catalog_feie(self):
        self.path = "/merchant/dish_catalog/db4aaa401485499788d310c21ebcf9e5"
        self._do_get()
        self.assertEqual(self.ret.get("errcode"), 0)


if __name__ == '__main__':
    unittest.main(warnings="ignore", failfast=True)

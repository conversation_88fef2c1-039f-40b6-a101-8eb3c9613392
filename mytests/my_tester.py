# -*- coding: utf-8 -*-

import os
import importlib.util

import inspect

from mytests.base_tester import BaseTester


class MyTester:

    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

    BLACK_FONT_C = "\033[30m"
    RED_FONT_C = "\033[31m"
    GREEN_FONT_C = "\033[32m"
    YELLOW_FONT_C = "\033[33m"
    DARK_BLUE_FONT_C = "\033[34m"
    PINK_FONT_C = "\033[35m"
    LIGHT_BLUE_FONT_C = "\033[36m"
    LIGHT_GREY_FONT_C = "\033[90m"
    ORIGIN_FONT_C = "\033[91m"

    def color_value(self, color, value):
        return color + str(value) + self.ENDC

    def red_value(self, value):
        return self.color_value(self.RED_FONT_C, value)

    def green_value(self, value):
        return self.color_value(self.GREEN_FONT_C, value)

    def __init__(self):
        self.test_dir = os.path.dirname(os.path.abspath(__file__))
        self._messages = []
        self.waiting_for_test_class = []
        self.tested_class = set()

    def walkdir(self):
        print(f"遍历目录: {self.test_dir}")
        for root, dirs, files in os.walk(self.test_dir, topdown=False):
            for dir in dirs:
                dir = os.path.join(self.test_dir, dir)
                self.walkfiles(dir)

    def walkfiles(self, dir):
        for root, dirs, files in os.walk(dir, topdown=False):
            files.sort(key=lambda x: x)
            for file in files:
                filename = os.path.splitext(file)[0]
                if filename == "tester":
                    continue
                if not filename.endswith("tester"):
                    continue
                filepath = os.path.join(dir, file)
                self.import_filepath(filepath)

    def import_filepath(self, filepath):
        spec = importlib.util.spec_from_file_location("test", filepath)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        self.register_waiting_for_testing(module)

    def register_waiting_for_testing(self, module):
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if not inspect.isclass(attr):
                continue
            if self.shield_classname(attr.__name__):
                continue
            if issubclass(attr, BaseTester):
                self.waiting_for_test_class.append(attr)

    def run_test_cls(self, ):

        self._messages.append("###############################")
        self._messages.append("################## 开始测试 ###")
        self._messages.append("###############################")

        def sort_func(cls):
            cls_name = cls.__name__
            sort = cls.sort
            if sort is not None:
                return str(sort)
            return cls_name
        self.waiting_for_test_class.sort(key=sort_func)

        for cls in self.waiting_for_test_class:
            cls_name = cls.__name__
            if cls_name in self.tested_class:
                continue
            self.tested_class.add(cls.__name__)
            obj = cls()
            self.run_test(obj)
            self._messages.append(self.red_value("-------测试完毕---------------------------"))

    def run_test(self, obj):
        class_doc = obj.__class__.__doc__
        if class_doc:
            self._messages.append(f"{class_doc}")
        for attr_name in dir(obj):
            attr = getattr(obj, attr_name)
            if not inspect.ismethod(attr):
                continue
            if not hasattr(attr, "test"):
                continue
            doc = attr.__doc__
            if doc:
                doc = doc.split("\n")[0]
                self._messages.append(f"\t发起测试: {attr_name} {doc}")
            else:
                self._messages.append(f"\t发起测试: {attr_name}")
            attr()

    def shield_classname(self, classname):
        if classname in [
                "Tester"
        ]:
            return True

    def log_message(self):
        for message in self._messages:
            print(message)

    def run(self):
        self.walkdir()
        self.run_test_cls()
        self.log_message()


if __name__ == '__main__':
    tester = MyTester()
    tester.run()

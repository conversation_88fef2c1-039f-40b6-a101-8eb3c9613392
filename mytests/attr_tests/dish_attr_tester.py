# -*- coding: utf-8 -*-

from mytests.attr_tests.tester import Tester
from mytests.attr_tests.attr_group_tester import AttrGroupTester


class DishAttrTester(Tester):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.group = self.get_attr_group_by_name("属性分组001")
        self._dish_ids = [
            '26e99e1ac1cc4266be22d14a748092e2',
            # "c9ccff138f6c4a1fade981d38c62c8ae",
            # "c79a2cbe94214667a5dc8b21051a9bc8"
        ]

    @property
    def attr_group_tester(self):
        if hasattr(self, "_attr_group_tester") and self._attr_group_tester is not None:
            return self._attr_group_tester
        self._attr_group_tester = AttrGroupTester()
        return self._attr_group_tester

    def get_attr_group_by_name(self, name):
        groups = self.attr_group_tester.access_attr_groups()
        for group in groups:
            group_name = group.get("name")
            print(group_name, name)
            if name == group_name:
                return group
        return None

    def add_attr_group_to_dishes(self):
        operation = "ADD_ATTR_GROUP_TO_DISHES"
        params = {
            "operation": operation,
            "dishIds": self._dish_ids,
            "groupId": self.group.get("id")
        }
        ret = self.do_post(params)
        self.assert_success(ret)
        return ret.get("data")

    def remove_attr_group_from_dishes(self):
        operation = "REMOVE_ATTR_GROUP_FROM_DISHES"
        params = {
            "operation": operation,
            "dishIds": self._dish_ids,
            "groupId": self.group.get("id")
        }
        ret = self.do_post(params)
        self.assert_success(ret)
        return ret.get("data")

    def add_attr_to_dishes(self):
        operation = "ADD_ATTR_TO_DISHES"
        attr = self.group.get("attrs")[0]
        params = {
            "operation": operation,
            "dishIds": self._dish_ids,
            "groupId": self.group.get("id"),
            "attrId": attr.get("id")
        }
        ret = self.do_post(params)
        self.assert_success(ret)
        return ret.get("data")

    def remove_attr_from_dishes(self):
        operation = "REMOVE_ATTR_FROM_DISHES"
        attr = self.group.get("attrs")[0]
        params = {
            "operation": operation,
            "dishIds": self._dish_ids,
            "groupId": self.group.get("id"),
            "attrId": attr.get("id")
        }
        ret = self.do_post(params)
        self.assert_success(ret)
        return ret.get("data")


if __name__ == '__main__':
    obj = DishAttrTester()
    obj.add_attr_group_to_dishes()
    # obj.remove_attr_group_from_dishes()
    # obj.add_attr_to_dishes()
    # obj.remove_attr_from_dishes()

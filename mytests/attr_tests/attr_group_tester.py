# -*- coding: utf-8 -*-

from mytests.attr_tests.tester import Tester


class AttrGroupTester(Tester):

    def create_attr_group(self):
        operation = "CREATE_ATTR_GROUP"
        params = {
            "operation": operation,
            "groupName": "属性分组001"
        }
        ret = self.do_post(params)
        return ret.get("data")

    def access_attr_groups(self):
        operation = "ACCESS_ATTR_GROUPS"
        ret = self.do_post({"operation": operation})
        self.assert_success(ret)
        return ret.get("data")

    def update_attr_group(self, group_id):
        operation = "UPDATE_ATTR_GROUP"
        params = {
            "groupId": group_id,
            "operation": operation,
            "groupName": "属性分组002",
            "attrGroupType": "SPECIFICATION"
        }
        ret = self.do_post(params=params)
        self.assert_success(ret)
        return ret.get("data")

    def reorder_attr_groups(self):
        operation = "REORDER_ATTR_GROUPS"
        params = {
            "operation": operation,
            "groupSortArray": [
                '0cb2293a7b76421bb9b476886e777888',
                'ac8ef950fde948198a396552f371e61d',
                '11de1c9a03054b65a72e8706a912a2d7',
                "549c344d93db420b8bdd29a5451caf2b",
                "0cb2293a7b76421bb9b476886e777586",
            ]
        }
        ret = self.do_post(params=params)
        self.assert_success(ret)
        return ret.get("data")

if __name__ == '__main__':
    obj = AttrGroupTester()
    # group = obj.create_attr_group()
    # obj.update_attr_group(group.get("id"))
    obj.reorder_attr_groups()

# -*- coding: utf-8 -*-

import requests


class Tester:

    def __init__(self):
        self._domain = "test.shilai.zhiyi.cn"
        self._merchant_id = "db4aaa401485499788d310c21ebcf9e5"
        self._base_params = {"merchantId": self._merchant_id}

    @property
    def domain(self):
        return self._domain

    def generate_url(self):
        url = f"https://{self.domain}/dish/attr"
        return url

    def do_post(self, params):
        params.update(self._base_params)
        url = self.generate_url()
        ret = requests.post(url, json=params)
        ret = ret.json()
        return ret

    def assert_success(self, ret):
        assert ret.get("errcode") == 0 and ret.get("errmsg") == "success"

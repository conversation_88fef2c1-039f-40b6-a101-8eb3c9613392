# -*- coding: utf-8 -*-

from mytests.attr_tests.tester import Tester
from mytests.attr_tests.attr_group_tester import AttrGroupTester


class AttrTester(Tester):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.group = self.get_attr_group_by_name("属性分组001")

    @property
    def attr_group_tester(self):
        if hasattr(self, "_attr_group_tester") and self._attr_group_tester is not None:
            return self._attr_group_tester
        self._attr_group_tester = AttrGroupTester()
        return self._attr_group_tester

    def create_attr(self):
        operation = "CREATE_ATTR"
        params = {
            "operation": operation,
            "attrName": "属性组001的子属性",
            "groupId": self.group.get("id"),
            "attrReprice": 100
        }
        ret = self.do_post(params)
        return ret.get("data")

    def update_attr(self, attr):
        operation = "UPDATE_ATTR"
        params = {
            "operation": operation,
            "attrName": "属性组001的子属性001",
            "groupId": self.group.get("id"),
            "attrReprice": 200,
            "attrId": attr.get("id")
        }
        ret = self.do_post(params)
        return ret.get("data")

    def get_attr_group_by_name(self, name):
        groups = self.attr_group_tester.access_attr_groups()
        for group in groups:
            group_name = group.get("name")
            print(group_name, name)
            if name == group_name:
                return group
        return None

    def reorder_attrs(self):
        operation = "REORDER_ATTRS"
        attr_sort_array = [
            "d3f4ea6022ef410fa4af3a1d2701aa88",
            "9b605eb5546d42b8bf7cf1fe922c7ff0",
        ]
        params = {
            "operation": operation,
            "groupId": self.group.get("id"),
            "attrSortArray": attr_sort_array
        }
        ret = self.do_post(params)
        self.assert_success(ret)
        return ret.get("data")

if __name__ == '__main__':
    obj = AttrTester()
    # attr = obj.create_attr()
    # obj.update_attr(attr)
    obj.reorder_attrs()

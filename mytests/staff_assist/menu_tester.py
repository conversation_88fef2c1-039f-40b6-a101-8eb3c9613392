# -*- coding: utf-8 -*-

from mytests.staff_assist.tester import Tester
from business_ops.staff_assist.menu_manager import MenuManager


class MenuTester(Tester):

    """ 业务助手菜单功能测试用例
    """

    CRUD = MenuManager.A_CREATE | MenuManager.A_RETRIEVE | MenuManager.A_UPDATE | MenuManager.A_DELETE
    CRU = CRUD ^ MenuManager.A_DELETE
    CRD = CRUD ^ MenuManager.A_UPDATE
    CUD = CRUD ^ MenuManager.A_RETRIEVE
    RUD = CRUD ^ MenuManager.A_CREATE

    @Tester.set_to_test
    def create_menu(self):
        """ 创建菜单测试
        """
        authority = self.CRUD
        manager = MenuManager(operation=MenuManager.CREATE)
        menu = manager.do_operate(
            name=self.random_name(),
            authorities={"SUPER_ADMIN": authority}
        )
        self.write_id_to_file(self.menu_id_path, menu.id)
        assert menu is not None
        assert menu.authorities["SUPER_ADMIN"] == authority

    @Tester.set_to_test
    def get_menus(self):
        """ 获取所有菜单
        """
        manager = MenuManager(operation=MenuManager.GET_LIST)
        menus = manager.do_operate()
        assert menus is not None
        assert len(menus) > 0

    @Tester.set_to_test
    def update_menu(self):
        """ 更新菜单
        """
        authority = self.CRU
        id = self.read_id_from_file(self.menu_id_path)
        manager = MenuManager(operation=MenuManager.UPDATE, menu_id=id)
        menu = manager.do_operate(
            authorities={"SUPER_ADMIN": authority}
        )
        assert menu is not None
        assert menu.authorities["SUPER_ADMIN"] == authority

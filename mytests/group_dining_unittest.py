# -*- coding: utf-8 -*-
############
# 约饭功能unittest
# 1. 创建请客局
# 2. 创建AA局
# 3. 接受邀请
# 4. 接受邀请人不存在
# 5. 邀请自己
# 6. 接受邀请,饭局不存在
# 7. 接受邀请,邀请人不是创建者
############

import time
from pprint import pprint

import unittest

from unittest_helper import Meta, myprint, TestHelper

class GroupDiningUnittest(unittest.TestCase, TestHelper, metaclass=Meta):
    dining_id = None

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(GroupDiningUnittest, self).__init__(*args, **kargs)
        self.url_prefix = "/group_dining"

    @myprint("创建请客局")
    def test_create_treat_dining(self):
        self.path = "{}/create".format(self.url_prefix)
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "eventDate": "2019-08-01 20:20",
            "maxGroupSize": 8,
            "paymentRule": "TREATMENT"
        }
        self._do_post()
        self.assertTrue(self.errcode == 0)

    @myprint("创建AA局")
    def test_create_AA_dining(self):
        self.path = "{}/create".format(self.url_prefix)
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "eventDate": "2019-08-01 20:20",
            "maxGroupSize": 8,
            "paymentRule": "ALL_SHARING",
            "comboMealId": "311a139996484055d0abebe120562958"
        }
        self._do_post()
        self.assertTrue(self.errcode == 0)

        GroupDiningUnittest.dining_id = self.ret.get("id")

    @myprint("获取饭局")
    def test_get_group_dining(self):
        self.path = "{}/{}".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self._do_get()

    @myprint("接受邀请")
    def test_accept(self):
        backup_user_id = self.user_id
        self.path = "{}/{}/invitation/accept".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self.user_id = self.invitee_id
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 0)

    @myprint("接受邀请人不存在")
    def test_accept_user_not_exists(self):
        backup_user_id = self.user_id
        self.path = "{}/{}/invitation/accept".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self.user_id = "11111111111111111"
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 50002)

    @myprint("邀请自己")
    def test_accept_self(self):
        backup_user_id = self.user_id
        self.path = "{}/{}/invitation/accept".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self.user_id = backup_user_id
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 80021)

    @myprint("接受邀请,饭局不存在")
    def test_accept_dining_not_exists(self):
        backup_user_id = self.user_id
        self.path = "{}/{}/invitation/accept".format(self.url_prefix, "dining not exists")
        self.user_id = self.invitee_id
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 80005)

    @myprint("接受邀请,邀请人不是创建者")
    def test_accept_inviter_error(self):
        backup_user_id = self.user_id
        self.path = "{}/{}/invitation/accept".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self.user_id = backup_user_id
        self.params = {
            "inviterId": self.invitee_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 80023)

    @myprint("查询饭局")
    def test_get_dining(self):
        self.path = "{}/{}".format(self.url_prefix, GroupDiningUnittest.dining_id)
        self._do_get()
        self.assertEqual(self.errcode, 0)

if __name__ == "__main__":
    unittest.main(warnings="ignore", failfast=True)

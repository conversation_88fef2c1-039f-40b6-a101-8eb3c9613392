# -*- coding: utf-8 -*-

import unittest

from unittest_helper import Meta, myprint, TestHelper


class CouponUnittest(unittest.TestCase, TestHelper, metaclass=Meta):
    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(CouponUnittest, self).__init__(*args, **kargs)
        self.url_prefix = "/red_packet"

    @myprint("coupon list")
    def test_coupon_list(self):
        self.path = "/coupon/list"
        self.params = {
            "state": "ISSUED",
        }
        self._do_get()


if __name__ == '__main__':
    unittest.main(warnings="ignore", failfast=True)

import os
import sys

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    print(parentPath)
    sys.path.insert(0, parentPath)

import xmltodict

from common.config import config
from common.utils.WXBizMsgCrypt import WXBizMsgCrypt

appid = config.WECHAT_APP_ID
token = config.WECHAT_MESSAGE_VERIFY_TOKEN
sEncodingAESKey = config.WECHAT_MESSAGE_ENCODED_SYMMETRIC_KEY

request_data = b'<xml>\n    <AppId><![CDATA[wx9b0a448bd837194c]]></AppId>\n    <Encrypt><![CDATA[ynPo2/Y1PvBERus8pcyUyKwALcWR/t/vJ/VRgWz0X954dIjhV9aItWYnKxvqHCQRJDznuIezH187cMFGu+u+WIpJXHs+aP58QUxoNymLowm2a8auhx3GhyvW1jbCuoNhJZ7aAey7tisnlH/IP1PCw2opgLW8YefJxnlf6mZEQGLfJAgEWCFFH5X5LQ+XITOeMSO3bBJwf3uybBX88NhGXvNb8Gs/Ny3ZJBWDplKoeI7BqOHhY50DyDhNq8GbnQlqY62b3wbBAksRKfv7ekH5uBGARM59EQyv1Dk4iZ3KGrCWXgrAPbynppO7SSATeAEoqs9eixQkL8e9iJeb39tCeh/GfpFJMTrFCzGsaLxrkWowWtA8srStM+QmJsIpJKmuuKEVb+v8f5Fpx5EhO8VMQhGTf9ewMp7rTqH7wgXVG/p/VLkLWPQAs7UqSm+G1LOknYrpYreVERP+4XUk15EhDQ==]]></Encrypt>\n</xml>\n'

msg_sign = "ab9146cf1d98758597c9933446f32efcc599a7b2"
timestamp = "1557384221"
nonce = "945054653"


crypt_instance = WXBizMsgCrypt(token, sEncodingAESKey, appid)
ret, decrypt_xml = crypt_instance.DecryptMsg(request_data, msg_sign, timestamp, nonce)

decrypt_dict = xmltodict.parse(decrypt_xml)

print(decrypt_dict['xml']['AppId'])

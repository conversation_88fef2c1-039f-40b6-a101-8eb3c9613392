# -*- coding: utf-8 -*-

###########################
# 支付相关的unittest
# 1. 钱包个人买单
# 2. 钱包饭局买单
# 3. 钱包AA转账
# 4. 微信个人买单
# 5. 微信饭局买单
# 6. 微信AA转账
###########################

import time
from pprint import pprint

import unittest

from unittest_helper import Meta, myprint, TestHelper

class PaymentUnittest(unittest.TestCase, TestHelper, metaclass=Meta):

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(PaymentUnittest, self).__init__(*args, **kargs)
        self.url_prefix = "/payment"

    @myprint("创建AA局")
    def test_create_AA_dining(self):
        self.path = "/group_dining/create"
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "eventDate": "2019-08-01 20:20",
            "maxGroupSize": 8,
            "paymentRule": "ALL_SHARING"
        }
        self._do_post()
        self.assertTrue(self.errcode == 0)
        PaymentUnittest.dining_id = self.ret.get("id")

    @myprint("接受邀请")
    def test_accept(self):
        backup_user_id = self.user_id
        self.path = "/group_dining/{}/invitation/accept".format(PaymentUnittest.dining_id)
        self.user_id = self.invitee_id
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 0)

    @myprint("钱包饭局买单prepay")
    def test_wallet_group_dining_prepay(self):
        self.path = "{}/prepay".format(self.url_prefix)
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "billFee": 500,
            "paidFee": 495,
            "payMethod": "WALLET",
            "transactionType": "GROUP_DINING_PAYMENT",
            "diningId": PaymentUnittest.dining_id,
            "userCnt": 2
        }
        self._do_post()

    @myprint("钱包AA付款")
    def test_wallet_AA_group_dining_prepay(self):
        self.path = "{}/prepay".format(self.url_prefix)
        self.user_id = self.invitee_id
        self.params = {
            "diningId": PaymentUnittest.dining_id,
            "payMethod": "WALLET",
            "transactionType": "GROUP_DINING_TRANSFER"
        }
        self._do_post()
        print(self.ret)

    @myprint("创建AA局")
    def test_create_AA_dining_2(self):
        self.path = "/group_dining/create"
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "eventDate": "2019-08-01 20:20",
            "maxGroupSize": 8,
            "paymentRule": "ALL_SHARING"
        }
        self._do_post()
        self.assertTrue(self.errcode == 0)
        PaymentUnittest.dining_id = self.ret.get("id")

    @myprint("接受邀请")
    def test_accept_2(self):
        backup_user_id = self.user_id
        self.path = "/group_dining/{}/invitation/accept".format(PaymentUnittest.dining_id)
        self.user_id = self.invitee_id
        self.params = {
            "inviterId": backup_user_id
        }
        self._do_post()
        self.assertEqual(self.errcode, 0)


    @myprint("微信饭局买单prepay")
    def test_wechat_group_dining_prepay(self):
        self.path = "{}/prepay".format(self.url_prefix)
        self.params = {
            "merchantId": self.merchant_id,
            "storeId": self.store_id,
            "billFee": 500,
            "paidFee": 495,
            "payMethod": "WECHAT_PAY",
            "transactionType": "GROUP_DINING_PAYMENT",
            "diningId": "5a9db2500cdf4d78b7a20099c7a56b2d",
            "userCnt": 2
        }
        self._do_post()

if __name__ == '__main__':
    unittest.main(warnings="ignore", failfast=True)

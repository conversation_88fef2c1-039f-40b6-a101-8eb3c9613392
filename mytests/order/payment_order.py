# # -*- encoding: utf-8 -*-

# '''
# @Time        :   2024/05/29 12:19:30
# '''

# import unittest
# from mytests.core.http import get, post


# class PaymentOrderTest(unittest.TestCase):

#     def __init__(self):
#         super().__init__()
#         self._merchant_id = "db4aaa401485499788d310c21ebcf9e5"
#         self._user_id = "74ad8f2a-0d3e-47f4-81d0-f7232c382b35"

#     def _create(self):
#         resp = post(
#             f"https://test.shilai.zhiyi.cn/v1.6/order/create/{self._merchant_id}",
#             headers={"userId": self._user_id},
#             json={"dishList":[
#                     {
#                         "attrList":[],"availableCopies":0,"boxQty":0,
#                         "categoryId":"24ce159bd736423d90b81d3557627f7f",
#                         "childDishGroups":[
#                             {"allowDuplicate":False, "childDishes":[
#                                 {
#                                     "boxQty":0,"enableQuantitySetting":False,
#                                     "id":"90c8272722c140c6ad4bfd0998ba9a6d",
#                                     "isMust":False,"marketPrice":0,"name":"香干炒肉",
#                                     "price":0,"quantityIncrement":1,"remainQuantity":0,
#                                     "saleTimeStr":"",
#                                     "selectionType":{"lowerLimit":0,"type":"UNLIMITED","upperLimit":0},
#                                     "sort":0,"status":"NORMAL","tag":"NONE",
#                                     "image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/fe723088d50f494d5604e598a96e4af1.jpeg","shilaiSkuPirce":1,"supplyCondimentUplimit":0,"attrList":[],"supplyCondiments":[],"isSku":False,
#                                     "customId":"90c8272722c140c6ad4bfd0998ba9a6d-5be9050ee7b140d6bdf77642791b755b","isFixedGroup":True,"quantity":1,
#                                     "childDishGroupInfo":{"groupIndex":0,"groupId":"5be9050ee7b140d6bdf77642791b755b"
#                                     }}],"groupName":"aa","id":"5be9050ee7b140d6bdf77642791b755b","isFixed":True,"orderMax":1,"orderMin":0,"sort":0},
#                                     {"allowDuplicate":False,"childDishes":[{"boxQty":0,"enableQuantitySetting":False,"id":"a5301cb3f9434647858850e1cbc7fddf","isMust":False,"marketPrice":0,"name":"象牌苏打水","price":0,"quantityIncrement":1,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"NONE","upperLimit":0},"sort":0,"status":"NORMAL","tag":"NONE","image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/a5301cb3f9434647858850e1cbc7fddf.jpeg","shilaiSkuPirce":800,"supplyCondimentUplimit":0,"attrList":[{"attrSelectionType":{"type":"SINGLE"},"attrs":[{"id":"977a708649b447d0a325a1298a9ab4ee","name":"堂食","reprice":0,"status":"NORMAL","type":"TASTE"}],"groupId":"fde2a7fa31e244ee8d89b8c06bb889e8","groupName":"就餐类型","isMultiSelect":True,"selType":"SINGLE"}],"supplyCondiments":[],"isSku":True,"customId":"a5301cb3f9434647858850e1cbc7fddf-e162db9c61f843df9f4e8b5982207354","isFixedGroup":False,"groupId":"e162db9c61f843df9f4e8b5982207354","quantity":1,"addPrice":0,"attrs":[{"id":"977a708649b447d0a325a1298a9ab4ee","name":"堂食","reprice":0,"status":"NORMAL","type":"TASTE"}]}],"groupName":"bb","id":"e162db9c61f843df9f4e8b5982207354","isFixed":False,"orderMax":1,"orderMin":0,"sort":1}],"discountPrice":14,"dishType":"SINGLE","enableQuantitySetting":False,"hasDiscount":True,"id":"16343ec2eae3400a8be4e4178ed844f7","image":"","isIFeedU":False,"isVerificationDish":False,"marketPrice":0,"maxOrderNum":0,"minOrderNum":0,"name":"套餐AAA","originPrice":200,"price":200,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"NONE","upperLimit":0},"soldNumber":6,"sort":0,"status":"NORMAL","supplyCondimentUplimit":0,"supplyCondiments":[],"tag":"NONE","thumbImage":"","type":"MENU","uuid":"","noDiscount":False,"isSku":True,"attrs":[],"childDishes":[{"boxQty":0,"enableQuantitySetting":False,"id":"90c8272722c140c6ad4bfd0998ba9a6d","isMust":False,"marketPrice":0,"name":"香干炒肉","price":0,"quantityIncrement":1,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"UNLIMITED","upperLimit":0},"sort":0,"status":"NORMAL","tag":"NONE","image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/fe723088d50f494d5604e598a96e4af1.jpeg","shilaiSkuPirce":1,"supplyCondimentUplimit":0,"attrList":[],"supplyCondiments":[],"isSku":False,"customId":"90c8272722c140c6ad4bfd0998ba9a6d-5be9050ee7b140d6bdf77642791b755b","isFixedGroup":True,"quantity":1,"childDishGroupInfo":{"groupIndex":0,"groupId":"5be9050ee7b140d6bdf77642791b755b"}},{"boxQty":0,"enableQuantitySetting":False,"id":"a5301cb3f9434647858850e1cbc7fddf","isMust":False,"marketPrice":0,"name":"象牌苏打水","price":0,"quantityIncrement":1,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"NONE","upperLimit":0},"sort":0,"status":"NORMAL","tag":"NONE","image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/a5301cb3f9434647858850e1cbc7fddf.jpeg","shilaiSkuPirce":800,"supplyCondimentUplimit":0,"attrList":[{"attrSelectionType":{"type":"SINGLE"},"attrs":[{"id":"977a708649b447d0a325a1298a9ab4ee","name":"堂食","reprice":0,"status":"NORMAL","type":"TASTE"}],"groupId":"fde2a7fa31e244ee8d89b8c06bb889e8","groupName":"就餐类型","isMultiSelect":True,"selType":"SINGLE"}],"supplyCondiments":[],"isSku":True,"customId":"a5301cb3f9434647858850e1cbc7fddf-e162db9c61f843df9f4e8b5982207354","isFixedGroup":False,"groupId":"e162db9c61f843df9f4e8b5982207354","quantity":1,"addPrice":0,"attrs":[{"id":"977a708649b447d0a325a1298a9ab4ee","name":"堂食","reprice":0,"status":"NORMAL","type":"TASTE"}]}],"quantity":1,"addedUserId":"74ad8f2a-0d3e-47f4-81d0-f7232c382b35","addedNickname":"微信用户","addedHeadimgurl":"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132","cartType":"dish"},{"isHot":True,"attrList":[],"availableCopies":0,"boxQty":1,"categoryId":"********************************","childDishGroups":[],"discountPrice":0,"dishType":"SINGLE","enableQuantitySetting":False,"hasDiscount":False,"id":"db4aaa401485499788d310a912345679","image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/db4aaa401485499788d310a912345679.jpeg","isIFeedU":False,"isVerificationDish":False,"marketPrice":0,"maxOrderNum":0,"minOrderNum":1,"name":"配送费","originPrice":100,"price":100,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"NONE","upperLimit":0},"soldNumber":0,"sort":0,"status":"NORMAL","supplyCondimentUplimit":0,"supplyCondiments":[],"tag":"HOT","thumbImage":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/thumb_db4aaa401485499788d310a912345679.jpeg","type":"MENU","uuid":"","noDiscount":True,"isSku":False,"minSel":1,"isRequired":True,"quantity":1,"addedUserId":"74ad8f2a-0d3e-47f4-81d0-f7232c382b35","addedNickname":"微信用户","addedHeadimgurl":"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132","cartType":"dish"},{"attrList":[],"availableCopies":0,"boxQty":0,"categoryId":"********************************","childDishGroups":[],"discountPrice":0,"dishType":"SINGLE","enableQuantitySetting":False,"hasDiscount":True,"id":"7cd5b968205d46cbad1d741dae9f6d97","image":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/7cd5b968205d46cbad1d741dae9f6d97.jpeg","isIFeedU":False,"isVerificationDish":False,"marketPrice":0,"maxOrderNum":0,"minOrderNum":0,"name":"茶位费/人","originPrice":300,"price":300,"remainQuantity":0,"saleTimeStr":"","selectionType":{"lowerLimit":0,"type":"NONE","upperLimit":0},"soldNumber":0,"sort":20210520,"status":"NORMAL","supplyCondimentUplimit":0,"supplyCondiments":[],"tag":"NONE","thumbImage":"https://shilai-images.oss-cn-shenzhen.aliyuncs.com/thumb_7cd5b968205d46cbad1d741dae9f6d97.jpeg","type":"EVERYONE","uuid":"","isRequired":True,"quantity":1,"minSel":1,"addedUserId":"74ad8f2a-0d3e-47f4-81d0-f7232c382b35","addedNickname":"微信用户","addedHeadimgurl":"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132","cartType":"dish"}],"mealType":"TAKE_OUT","shippingAddressId":"e0348533ba98461d9ed365a7a4dd3bd7","shippingFee":0,"tableId":"07f540fe2c864f23b2bfa7539be0cb65","remark":"","peopleCount":1,"groupDiningEventId":"","phone":"13798220959","appointmentTime":"","discountAmountPrice":0,"forceCreate":False
#             }
#         ).json()
#         return resp

#     def _payment(self, order_id):
#         resp = post(
#             "https://test.shilai.zhiyi.cn/v1.6/payment/prepay",
#             headers={"userId": self._user_id},
#             json={
#                 "merchantId": self._merchant_id,
#                 "orderId":order_id,
#                 "billFee":600,"paidFee":600,"noDiscountBillFee":0,
#                 "transactionType":"SELF_DISH_ORDER_PAYMENT",
#                 "payMethod":"WALLET",
#                 "isInvoice":False,
#                 "isFromGroup":False,
#                 "userId":"74ad8f2a-0d3e-47f4-81d0-f7232c382b35"
#             }
#         )
#         return resp

#     def test_order(self):
#         order = self._create()
#         resp = self._payment(order['data']['orderId'])
#         print(resp)


# if __name__ == '__main__':
#     unittest.main(warnings="ignore", failfast=True)
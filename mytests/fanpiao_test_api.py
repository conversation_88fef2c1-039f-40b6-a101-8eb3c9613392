#!/usr/bin/python3
# -*- encoding: utf-8 -*-

'''
@Time        :   2024/01/03 11:57:54
该脚本每10s执行一次
'''

import traceback
import inspect
import asyncio
import functools
import random
import json
import time
from enum import Enum
from datetime import (
    datetime,
    timezone,
    timedelta,
    tzinfo
)
from typing import Any, Dict, Optional, Union

import httpx
from motor.motor_asyncio import AsyncIOMotorClient


_DEFAULT_USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/7.0.11(0x17000b21) NetType/4G Language/zh_CN"


def async_retry(times: Union[int, bool]=2, exc_types=(Exception, ), callback=None):
    def wrapper(func):
        @functools.wraps(func)
        async def _exec(*args, **kwargs):
            i = 0
            while True:
                try:
                    rtn = await func(*args, **kwargs) if inspect.iscoroutinefunction(func) else func(*args, **kwargs)
                    i = 0
                    return rtn
                except exc_types as e:
                    if not isinstance(times, bool) and i >= times:
                        raise
                    if callback is not None:
                        kwargs = await callback(i, e, *args, **kwargs)
                    i += 1
        return _exec
    return wrapper


def to_json(obj):
    return json.dumps(obj, ensure_ascii=False, indent=4)


def to_object(s):
    return json.loads(s)


def current_timestamp():
    return int(time.time())
    

def format_time(
    timestamp: float, fmt='%Y-%m-%d %H:%M:%S', 
    tz: tzinfo = timezone(timedelta(hours=8))
) -> str:
    d = datetime.fromtimestamp(timestamp, tz)
    return d.strftime(fmt)


class Request(object):
    
    __slots__ = [
        "method", "url", "data", "json", 
        "params", "cookies", 
        "headers", "timeout", "resp_type",
        "verify_ssl", "allow_redirects",
        "sleep_time", "files"
    ]

    def __init__(
        self,
        url: str,
        method: str = 'GET',
        data: Any = None,
        json: Any = None,
        params: Any = None,
        files: Any = None,
        cookies: Optional[httpx._types.CookieTypes] = None,
        headers: Dict = None,
        timeout: Union[float, httpx.Timeout] = None,
        resp_type: str = "bytes",
        verify_ssl: bool = False,
        allow_redirects: bool = False,
        sleep_time: float = None
    ):
        self.method = method
        self.url = url
        self.data = data
        self.json = json
        self.params = params
        self.cookies = cookies
        self.files = files
        self.headers = headers or {"user-agent": _DEFAULT_USER_AGENT}
        self.timeout = timeout or httpx.Timeout(10, connect=5)
        self.resp_type = resp_type
        self.verify_ssl = verify_ssl
        self.allow_redirects = allow_redirects
        self.sleep_time = sleep_time


class Response(object):
    __slots__ = ["resp", "content", "status", "msg", "cookies", "headers"]

    def __init__(self, resp, content=None, status=200, msg=''):
        self.resp = resp
        self.content = content
        self.status = status
        self.msg = msg
        self.cookies = dict(resp.cookies)
        self.headers = dict(resp.headers)

    def __str__(self):
        return f"status={self.status}, msg={self.msg}, content={self.content}"

    def __repr__(self):
        return self.__str__()


class HttpClient(object):

    def __init__(
            self,
            timeout: httpx.Timeout = None,
            proxies: httpx._types.ProxiesTypes = None,
            base_url: str = ""
    ):
        if timeout is None:
            timeout = httpx.Timeout(10, connect=5)
        self.session = httpx.AsyncClient(
            verify=False,
            timeout=timeout,
            proxies=proxies,
            base_url=base_url
        )

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        await self.stop()
        
    async def stop(self):
        await self.session.__aexit__()

    async def fetch(self, reqest: Request, **kwargs):
        if reqest.sleep_time is not None:
            await asyncio.sleep(reqest.sleep_time)
        resp = await self.session.request(
            reqest.method,
            reqest.url,
            data=reqest.data,
            json=reqest.json,
            params=reqest.params,
            headers=reqest.headers,
            cookies=reqest.cookies,
            files=reqest.files,
            follow_redirects=reqest.allow_redirects,
            **kwargs
        )
        if resp.status_code == 200:
            if reqest.resp_type == "bytes":
                content = resp.read()
            elif reqest.resp_type == "text":
                content = resp.text
            else:
                content = resp.json()
        else:
            content = resp.text
        return Response(
            resp,
            content=content,
            status=resp.status_code,
            msg=resp.reason_phrase
        )

    async def get(self, request: Request, **kwargs):
        request.method = "GET"
        return await self.fetch(request, **kwargs)

    async def post(self, request: Request, **kwargs):
        request.method = "POST"
        return await self.fetch(request, **kwargs)


class WeChatRobot(object):

    _base_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook"

    def __init__(self, key):
        self._key = key
        self._flush_url(key)
        self._request = HttpClient()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        await self.stop()
        
    async def stop(self):
        await self._request.stop()
        
    def _flush_url(self, key):
        self._web_hook_url = self._base_url + "/send?key=" + key
        self._file_url = self._base_url + "/upload_media?key=" + key + "&type=file"

    def set_key(self, key):
        self._key = key
        self._flush_url(key)

    @staticmethod
    def _check_response(resp: dict) -> bool:
        return bool(int(resp.get("errcode", 500)) == 0)

    async def send_markdown(self, content):
        json_params = {
            "msgtype": "markdown",
            "markdown": {
                "content": content,
            }
        }
        resp = await self._request.post(
            Request(
                self._web_hook_url,
                json=json_params,
                resp_type='json'
            )
        )
        return self._check_response(resp.content)

    async def send_text(self, content):
        json_params = {
            "msgtype": "text",
            "text": {
                "content": content,
                 "mentioned_list":["@all"],
            }
        }
        resp = await self._request.post(
            Request(
                self._web_hook_url,
                json=json_params,
                resp_type='json'
            )
        )
        return self._check_response(resp.content)
    

class ResponseError(Exception):
    pass


class AlertLevel(Enum):
    ERROR = "ERROR"
    FATAL = "FATAL"


class Status(Enum):
    UNPROCESSED = "UNPROCESSED"  # 未处理
    RESOLVED = "RESOLVED"  # 已解决


class FanpiaoApiTest(object):

    def __init__(
        self,
        wechat_key,
        user_ids: list,
        merchant_ids: list,
        mongo_url: str,
        base_url="https://shilai.zhiyi.cn/v1.7"
    ):
        self._base_url = base_url
        self._user_ids = user_ids
        self._merchant_ids = merchant_ids
        self._wechat_key = wechat_key
        self._mongo_url = mongo_url
        self._sem_lock = asyncio.Semaphore(value=5)

    @async_retry(times=3)
    async def _send_wechat(self, error_msg, wechat_key):
        async with WeChatRobot(wechat_key) as robot:
            await robot.send_text(f"小程序API报警通知，请相关人员尽快处理。\n{error_msg}")

    @async_retry(times=2)
    async def _fetch_http(self, method: str, api, **kwargs):
        async with self._sem_lock:
            kw = kwargs.copy()
            error_text = {
                "api": api,
                "method": method.upper(),
            }
            _kw = {k: v for k, v in kw.items() if k != "resp_type"}
            if _kw:
                error_text.update(_kw)

            try:
                resp = await getattr(self._http, method)(Request(api, **kw))
            except Exception as e:
                error_text.update({
                    "errorMsg": str(type(e)),
                    "alertLevel": AlertLevel.FATAL.value,
                    "status": Status.UNPROCESSED.value,
                    "createTime": current_timestamp()
                })
                raise ResponseError(to_json(error_text))
            print(f"usage_time={int(resp.resp.elapsed.total_seconds() * 1000)}ms, api={api}")
            content = resp.content
            if resp.status != 200 or (isinstance(content, dict) and content.get('errcode', 500) != 0):
                content = content or resp.msg
                error_text.update({
                    "errorMsg": content,
                    "alertLevel": AlertLevel.ERROR.value,
                    "status": Status.UNPROCESSED.value,
                    "createTime": current_timestamp()
                })
                raise ResponseError(to_json(error_text))
            return True

    async def gather_task(self, *tasks):
        resp = await asyncio.gather(*tasks, return_exceptions=True)
        result = []
        for res in resp:
            if isinstance(res, Exception):
                # error_info = "".join(traceback.format_exception(type(res), res, res.__traceback__))
                # print(error_info)
                result.append(res.args[0])
            else:
                result.append(res)
        return result

    async def run(self):
        client = AsyncIOMotorClient(self._mongo_url)
        self.collection = client['fanpiao_alert_db']['api']
        self._http = HttpClient(
            timeout=httpx.Timeout(10, connect=5),
            base_url=self._base_url
        )
        user_id = random.choice(self._user_ids)
        merchant_id = random.choice(self._merchant_ids)
        tasks = [
            self._fetch_http(
                "post", "/message_center/get_user_period_messages",
                json={"1": 1, "userId": user_id},
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "post", "/group_purchase/my_list",
                json={"withLeader": True, "multiStatus": "ACTIVE"},
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "post", "/group_purchase_template/get_merchant_group_purchase_template_list",
                json={"merchantId": merchant_id},
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "post", "/coupon_v2/user_coupon_list",
                json={"merchantId": merchant_id, "1": 1},
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/merchant/{merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/fanpiao/fanpiao_categories/{merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/merchant/coupon_package_v2/{merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/coupon/list?page=1&size=10000&state=ACCEPTED&merchantId={merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/fanpiao/get_user_fanpiao_fee?merchantId={merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/user/user_merchant_info/{merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/fanpiao/recently/buy/{merchant_id}",
                headers={"userId": user_id},
                resp_type="json",
            ),
            self._fetch_http(
                "get", f"/merchant/dish_catalog/{merchant_id}?enableSaleTime=true",
                headers={"userId": user_id},
                resp_type="json",
            )
            # self._fetch_http(
            #     "post", "/api/test/test01",
            #     json={"a": 'hahah', "b": [user_id, merchant_id]},
            #     headers={"userId": user_id},
            #     resp_type="json"
            # ),
            # self._fetch_http(
            #     "post", "/api/test/test02",
            #     headers={"userId": user_id},
            #     resp_type="json"
            # ),
            # self._fetch_http(
            #     "post", "/api/test/test03",
            #     headers={"userId": user_id},
            #     resp_type="json"
            # ),
        ]
        result = await self.gather_task(*tasks)
        result = [to_object(res) for res in result if res is not True]
        await self._update_records(result)
        await self._send_alert()
        await self._http.stop()

    async def _send_alert(self, delta_min=5):
        records = await self.collection.find({'status': Status.UNPROCESSED.value}).to_list(None)
        if records:
            tmp = []
            for record in records:
                if record['freq'] > 2 or (current_timestamp() - record['updateTime']) > delta_min * 60:
                    record["createTime"] = format_time(record['createTime'])
                    record["updateTime"] = format_time(record['updateTime'])
                    _id = record.pop("_id")
                    record.pop("updateTime")
                    tmp.append(to_json(record))
                    await self.collection.update_one(
                        {"_id": _id},
                        {"$set": {"updateTime": current_timestamp()}}
                    )
            if tmp:
                batch_size = 6
                for i in range(0, len(tmp), batch_size):
                    error_msg = "\n------------------------------------------------\n".join(tmp[i: i+batch_size])
                    await self._send_wechat(error_msg, self._wechat_key)

    async def _update_records(self, result):
        if result:
            for doc in result:
                record = await self.collection.find_one(
                    {"api": doc['api'], "status": Status.UNPROCESSED.value},
                    sort=[("createTime", -1)]
                )
                if record:
                    await self.collection.update_one(
                        {"_id": record["_id"]},
                        {"$inc": {"freq": 1}}
                    )
                else:
                    doc.update({
                        'freq': 1,
                        'updateTime': current_timestamp()
                    })
                    await self.collection.insert_one(doc)
        else:
            records = await self.collection.find({'status': Status.UNPROCESSED.value}).to_list(None)
            if records:
                for record in records:
                    await self.collection.update_one(
                        {"_id": record["_id"]},
                        {
                            "$set": {
                                "status": Status.RESOLVED.value,
                                "freq": 0,
                                "updateTime": current_timestamp()
                            }
                        }
                    )

    async def scheduler(self):
        print("start service ....")
        while True:
            try:
                await self.run()
            except:
                print(f'unkown error, msg={traceback.format_exc()}')
            await asyncio.sleep(10)


if __name__ == "__main__":
    wechat_key = "ecfd7620-45fb-4be1-bd9d-cb9603e311e4"  # 生产环境
    # wechat_key = "74d880ed-2864-4ab3-9011-de4bdd6f5b09"  # 测试环境

    user_ids = ["f4eff1df-0d54-4a2c-a73e-c5d29fb90270"]
    merchant_ids = [
        "8ec573585d9645229fb01713e30a2a6d",  # 飞鹅模式测试店
        "611e8d6b48e844a186d5ead5a8340ff0",  # 时来椰子鸡0906号店
    ]
    api = FanpiaoApiTest(
        wechat_key,
        user_ids,
        merchant_ids,
        mongo_url="mongodb://shilai:Mongo#Test123!@120.79.122.254:27017/admin?authSource=admin&readPreference=primary",
        # base_url="http://127.0.0.1:8764"
    )
    asyncio.run(api.scheduler())
import json

import requests
from google.protobuf import json_format

import proto.staff_pb2 as staff_pb
import test.interface.data.test_data as test_data
import test.interface.test_common as test_common
from service.auth_service_helper import UserAuthHelper

class StaffServiceTest():
    @staticmethod
    def setup_staff_service_test():
        UserAuthHelper().staff_add(test_data.staff_employee_userids["test_added"])
        print("done")

    @staticmethod
    def test_staff_add(user_id):
        url = test_common.test_common.create_get_url("/staff/add")
        data = {
            'userId': user_id
        }
        response = requests.post(url, headers=data)
        print(response)
        #go into mangodb to ensure data bulid

    @staticmethod
    def test_staff_info(user_id):
        url = test_common.TestCommon().create_get_url("/staff/info")
        data = {
            'userId': user_id
        }
        response_map = json.loads(requests.get(url, headers=data).content)
        print(response_map)
        assert (response_map["staff"] is not None)
        assert (response_map["staff"]["userId"] == user_id)
        assert (response_map["staff"]["role"] == "EMPLOYEE")

    @staticmethod
    def test_staff_info(user_id):
        url = test_common.TestCommon.create_get_url("/staff/info")
        data = {
            'userId': user_id
        }
        response_map = json.loads(requests.get(url, headers=data).content)
        print(response_map)
        assert (response_map["staff"] is not None)

        if (response_map["staff"] is not None):
            assert (response_map["staff"]["userId"] == user_id)
            assert (response_map["staff"]["role"] == "EMPLOYEE")

    @staticmethod
    def delete_staff_info(user_id):
        url = test_common.TestCommon.create_get_url("/staff/info")
        data = {
            'userId': user_id
        }
        response_map = json.loads(requests.get(url, headers=data).content)
        print(response_map)
        assert (response_map["staff"] is not None)

        if (response_map["staff"] is not None):
            assert (response_map["staff"]["userId"] == user_id)
            assert (response_map["staff"]["role"] == "EMPLOYEE")


if __name__ == "__main__":
    StaffServiceTest.test_staff_add(test_data.staff_employee_userids["test_added"])
    StaffServiceTest.test_staff_info(test_data.staff_employee_userids["test_added"])




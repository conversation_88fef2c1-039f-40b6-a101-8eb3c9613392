from common.client.mongodb_client import MongodbClient

import dao.constants as dao_constants
import test.interface.constants as config


class TestCommon:
    @staticmethod
    def create_get_url(self,api):
        url = 'http://{}:{}{}'.format(config.SERVICE_HOST, config.SERVICE_PORT, api)
        return url

    def setup(self):
        self.change_dao_constants()
        self.init_mongodb()

    def change_dao_constants(self):
        return 1

    def init_mongodb(self):
        mongodb_address = dao_constants.MONGODB_SERVER_ADDRESS
        mongodb_port = dao_constants.MONGODB_PORT
        mongodb_username = dao_constants.MONGODB_USER_NAME
        mongodb_password = dao_constants.MONGODB_ROOT_PASSWORD
        db = MongodbClient(mongodb_address, mongodb_port, mongodb_username, mongodb_password)
        return db

    def get_init_data(self):
        return None

    def clearup(self):
        return None


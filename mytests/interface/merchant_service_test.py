import json
import unittest
import requests

class UserServiceTest(unittest.TestCase):

    def test_get_merchants_for_user(self):
        url = ''
        respon = requests.get(url, params={'userId': '6314'})
        return None

    def test_save_user_info(self):
        url = ''
        raw_data = {}
        headers = '{fromPlatform: "merchant"}'
        querystring = {}
        headers = {}

        data = json.dumps(raw_data, ensure_ascii=False)
        response = requests.request("POST", url, data=data.encode(), headers=headers, params=querystring)
        print(response)

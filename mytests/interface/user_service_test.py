import json
import unittest
import requests

class UserServiceTest(unittest.TestCase):



    def test_save_user_info(self):
        url = ''
        raw_data = {}
        headers = '{fromPlatform: "merchant"}'
        querystring = {
            "access_token": "20_Y3ivCLxyc1iastTU90SsDLFP8cWnAqf7Mze3HJv7EZUBvwciKHMh7WfUoCtQ2dkoDLJcAEAPXV"
        }
        headers = {
            'cache-control': "no-cache",
            'Postman-Token': "1afb33c3-eee7-4bdd-8fbc-282ac1817a2e"
        }

        data = json.dumps(raw_data, ensure_ascii=False)
        response = requests.request("POST", url, data=data.encode(), headers=headers, params=querystring)
        print(response)

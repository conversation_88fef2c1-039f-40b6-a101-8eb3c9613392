# 与后端数据访问相关的配置变量

# TODO: 考虑将以下动态配置信息以环境变量形式传入(尤其是credentials相关信息)
MONGODB_SERVER_ADDRESS = '*************'
MONGODB_PORT = 8881
MONGODB_USER_NAME = ''
MONGODB_ROOT_PASSWORD = ''



# 时来平台授权相关数据库
MONGODB_AUTH_DATABASE_NAME = 'auth_db'
MONGODB_COMPONENT_VERIFY_TICKET_COLLECTION_NAME = 'component_verify_tickets'

# 时来平台用户相关数据库
MONGODB_USER_DATABASE_NAME = 'user_db'
MONGODB_USER_COLLECTION_NAME = 'users'

# 时来业务员相关数据库
MONGODB_STAFF_DATABASE_NAME = 'staff_db'
MONGODB_STAFF_COLLECTION_NAME = 'staffs'

# 商户相关数据库
MONGODB_BUSINESS_ENTITIES_DATABASE_NAME = 'business_entities_db'
MONGODB_MERCHANT_COLLECTION_NAME = 'merchants'

# 会员管理相关数据库
MONGODB_MEMBERSHIP_DATABASE_NAME = 'membership_db'
MONGODB_CARD_CATEGORY_COLLECTION_NAME = 'member_card_categories'
MONGODB_MEMBER_CARD_COLLECTION_NAME = 'member_cards'

# 优惠券管理相关数据库
MONGODB_COUPON_DATABASE_NAME = 'coupon_db'
MONGODB_COUPON_CATEGORY_COLLECTION_NAME = 'coupon_categories'
MONGODB_COUPON_COLLECTION_NAME = 'coupons'

# 其他杂项类型数据库
MONGODB_MISC_DATABASE_NAME = 'misc_db'
MONGODB_STAFF_QRCODE_COLLECTION_NAME = 'staff_qrcodes'
MONGODB_MERCHANT_QRCODE_COLLECTION_NAME = 'merchant_qrcodes'

SUCCESS_CODE = 0
FAILURE_CODE = 1

import requests
from pymongo import MongoClient

from google.protobuf import json_format

# user_id = "f4eff1df-0d54-4a2c-a73e-c5d29fb90270"
# merchant_id = "2d6a7bb331e740ad8963d63a34788f47"
# resp = requests.get(
#     f"http://127.0.0.1:12917/coupon/list?page=1&size=10000&state=ACCEPTED&merchantId={merchant_id}",
#     headers={"userId": user_id},

# )
# print()

import uuid

client = MongoClient(
    host="127.0.0.1", port=13691, username="shilai", password="a7cfd52194d326d5465a89192809aeef", 
    # host="************", port=13691, username="shilai", password="a7cfd52194d326d5465a89192809aeef", 
    # minPoolSize=10,maxPoolSize=1024, connect=False, w=3, readPreference='secondaryPreferred'
)

json_data = {'id': str(uuid.uuid4()), 'wechatProfile': {"province" : "Beijing",
        "qrSceneStr" : "",
        "city" : "kkk",
        "unionid" : "oASYU1T4OiGhJkSh4f4RvGN_fVu",
        "language" : ""}}
from pprint import pprint
pprint(json_data)


matcher = {"thirdPartyUniqId": json_data['wechatProfile']['unionid']}
user_id = json_data['id']
del json_data['id']
result = client.user_db.users.find_and_modify(matcher, {"$set": json_data, "$setOnInsert": {"id": user_id}}, upsert=True)
pprint('reuslt=', result)

# 存在，更新，返回老数据，id用老的userId

# 不存在，

# database_list = client.list_database_names()
# # 遍历每个数据库并获取其所有集合
# for db_name in database_list:
#     db = client[db_name]
#     collection_list = db.list_collection_names()
#     print(f"数据库名称：{db_name}", f"集合列表：{collection_list}")

# data = client["coupon_db"]["coupons"].find({'merchantId': '', 'userId': '', 'state': 'ACCEPTED'})
# data = client["coupon_db"]["coupons"].find().sort([('latestReminderTimestamp', -1)]).skip(0).limit(10)
# for row in data:
#     print(row)

# data = client["misc_db"]["merchant_table_qrcodes"].find({'merchantId': 'd3348a6659ac401486d53753ea58e8d2', "tableName": {"$ne": ""}}).skip(0).limit(20)
# for row in data:
#     print(row)

# merchantId = "10b320052feb4118a4438466351c275a"
# print("\n\n\\n=============")
# # data = client["business_entities_db"]["merchants"].find({"id": merchantId}).skip(0).limit(10)
# # cond = {"basicInfo.name": {"$ne": "", "$regex": "时来", "$exists": True}}
# cond = {"basicInfo.name": {"$ne": "", "$exists": True}}
# data = client["business_entities_db"]["merchants"].find(cond).skip(0).limit(10)
# for row in data:
#     print(row)
#     print()

# data = client["business_entities_db"]["merchants"].find({'status': {"$in": ['RUNNING', 'EDITING']}}).skip(0).limit(20)
# for row in data:
#     print()
#     print(row)

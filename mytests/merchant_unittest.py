# -*- coding: utf-8 -*-

from pprint import pprint

import unittest

from unittest_helper import Meta, myprint, TestHelper

class MerchantUnittest(unittest.TestCase, TestHelper, metaclass=Meta):

    def __init__(self, *args, **kargs):
        TestHelper.__init__(self)
        super(MerchantUnittest, self).__init__(*args, **kargs)

    def test_merchant_info(self):
        self.path = "/v1/merchant/{}/info".format(self.merchant_id)
        self._do_get()
        print(self.ret)

if __name__ == '__main__':
    unittest.main(warnings="ignore", failfast=True)

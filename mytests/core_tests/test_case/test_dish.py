# -*- encoding: utf-8 -*-
'''
@time        :2024/09/04 23:39:07
'''

from mytests.core_tests.base import BasicTests


class DishCatalogTestCase(BasicTests):

    def test_dish_catalog_feie(self):
        data = self._get("/merchant/dish_catalog/db4aaa401485499788d310c21ebcf9e5")
        print()

    def test_dish_catalog_shilai(self):
        data = self._get("/merchant/dish_catalog/ed98d6435a954161ace371d7849f66a5")
        print()


if __name__ == '__main__':
    import unittest

    unittest.main(warnings="ignore", failfast=True)

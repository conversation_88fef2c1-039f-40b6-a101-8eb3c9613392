# -*- encoding: utf-8 -*-

'''
@Time        :   2024/09/05 09:51:45
'''

import os
import sys
import unittest


_TEST_DIR = os.path.dirname(os.path.abspath(__file__))
_PROJECT_DIR = os.path.dirname(os.path.dirname(_TEST_DIR))


def source_env(**kwargs):
    env_var = {
        "VERSION": "v1.7",
        "SERVICE_NAME": "main-service",
        "DEPLOYMENT_ENV": "test"
    }
    env_var.update(kwargs)
    for k, v in env_var.items():
        os.environ[str(k).upper()] = str(v)
    sys.path.insert(0, _PROJECT_DIR)

def run_tests(deployment_env="test", **kwargs):
    source_env(deployment_env=deployment_env, **kwargs)
    loader = unittest.TestLoader()
    suite = loader.discover(_TEST_DIR)
    runner = unittest.TextTestRunner()
    runner.run(suite)


if __name__ == '__main__':
    run_tests()

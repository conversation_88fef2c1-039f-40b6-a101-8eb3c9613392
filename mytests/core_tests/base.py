# -*- encoding: utf-8 -*-
'''
@time        :2024/09/04 23:05:56
'''
import unittest
import json as _json

from service.main_service import app


class BasicTests(unittest.TestCase):

    def setUp(self):
        self.app = app.test_client()
        self.app.testing = True

    def _get(self, api, *args, status_code=200, error_code=0, **kwargs):
        resp = self.app.get(api, *args, **kwargs)
        self.assertEqual(resp.status_code, status_code)
        resp = resp.get_json()
        self.assertEqual(resp.get("errcode"), error_code)
        return resp.get("data")

    def _post(self, api, *args, json=None, data=None, status_code=200, error_code=0, **kwargs):
        content_type='application/json' if json else None
        if json:
            data=_json.dumps(json)
        resp = self.app.post(api, *args, data=data, content_type=content_type, status_code=status_code, **kwargs)
        self.assertEqual(resp.status_code, status_code)
        resp = resp.get_json()
        self.assertEqual(resp.get("errcode"), error_code)
        return resp.get("data")

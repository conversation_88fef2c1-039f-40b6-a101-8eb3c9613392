# -*- encoding: utf-8 -*-

'''
@Time        :   2024/01/18 18:16:59
'''

import json

import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning
from requests.exceptions import SSLError

from common.utils.requests_utils import retry

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class HttpResponseError(Exception):
    def __init__(self, **kwargs):
        self._error_msg = json.dumps(kwargs, ensure_ascii=False, indent=4)

    def __repr__(self):
        return f"http fetch error:\n{self._error_msg}"

    def __str__(self):
        return self.__repr__()


@retry(times=2, delay=0.5)
def _fetch(url: str, method: str, timeout=30, **kwargs):
    try:
        resp = getattr(requests, method.lower())(
            url,
            verify=True,
            timeout=timeout,
            **kwargs
        )
    except SSLError:
        resp = getattr(requests, method.lower())(
            url,
            verify=False,
            timeout=timeout,
            **kwargs
        )
    if resp.status_code != 200:
        raise HttpResponseError(
            url=url,
            method=method,
            **kwargs,
            status=resp.status_code,
            error_msg=resp.text,
            reason=resp.reason
        )
    return resp


def get(url, **kwargs):
    return _fetch(url, "get", **kwargs)


def post(url, **kwargs):
    return _fetch(url, "post", **kwargs)

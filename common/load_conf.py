# -*- encoding: utf-8 -*-

'''
@Time        :   2024/01/10 16:04:22
'''

import os
import sys
import re
import copy
import json
import argparse
import threading
from typing import Any, List

import yaml
from flask import g

_parse_prompt_regex = re.compile(r'\{([\w-]+?)\}')
_config = dict()


class Config(object):
    allow_reflush = False
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if not cls._instance:
                cls._instance = super(Config, cls).__new__(cls)
        return cls._instance

    def __init__(self, **kwargs):
        self._cache = {k.upper(): v for k, v in kwargs.items()}

    def __setattr__(self, name: str, value: Any):
        if not self.allow_reflush and name not in('_cache', '_attributes'):
            raise AttributeError(f"'{type(self).__name__}' object can not set attribute '{name}'")
        if name not in('_attributes', '_cache'):
            self._cache[name.upper()] = value
        else:
            super().__setattr__(name, value)

    def __getattr__(self, name: str):
        upper_name = name.upper()
        if upper_name in self._cache:
            try:
                if hasattr(g, 'version') and isinstance(g.version, str) and g.version == 'v1.6':
                    value = self.__adapt_v16(upper_name)
                    if value is not None:
                        return value
            except:
                pass
            return copy.deepcopy(self._cache[upper_name])
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __adapt_v16(self, name):
        if name == "WECHAT_MINIPROGRAM_APPID":
            return copy.deepcopy(self._cache["WECHAT_MINIPROGRAM_OLD_APPID"])
        if name == "WECHAT_MINIPROGRAM_SECRET":
            return copy.deepcopy(self._cache["WECHAT_MINIPROGRAM_OLD_SECRET"])

    def __getitem__(self, key):
        return self.__getattr__(key)

    def __setitem__(self, key, value):
        self.__setattr__(key, value)

    def __len__(self):
        return len(self._cache)

    def get(self, name):
        return self.__getattr__(name)

    def to_dict(self):
        return copy.deepcopy(self._cache)

    def to_json(self):
        return json.dumps(self._cache, ensure_ascii=False, indent=4)

    def to_env(self, upper=True):
        print('-' * 100)
        for k, v in self._cache.items():
            k = str(k)
            if upper:
                k = k.upper()
            print(f"set environ, {k}={str(v)}")
            os.environ[k] = str(v)

    def get_config_value(self, name, default=None):
        return os.environ.get(name, default)


def _parse_template_variable(template_variable):
    global _config
    if not template_variable:
        return
    result = dict()
    for item in template_variable.split():
        k, v = item.split('=')
        _config[k] = v
        result[k] = v
    return result


def import_path(path):
    if path.endswith("__pycache__"): 
        return
    if os.path.split(path)[-1].startswith("."):
        return
    if path not in sys.path and os.path.exists(path):
        sys.path.insert(0, path)
        return True


def auto_import(path: str, depth: int = None, current_depth: int = 0):
    if depth is not None and current_depth > depth:
        return
    if path.endswith("__pycache__"): 
        return
    if os.path.split(path)[-1].startswith("."):
        return
    if not import_path(path):
        return
    for file in os.listdir(path):
        file_path = os.path.join(path, file)
        if os.path.isdir(file_path):
            auto_import(file_path, depth, current_depth + 1)


def _reflush_env(template_variable, **conf):
    global _config
    _config.update(template_variable)
    template_variable.update({k: v for k, v in os.environ.copy().items() if k not in template_variable})
    for k, v in conf.items():
        if isinstance(v, str) and template_variable is not None:
            fields = _parse_prompt_regex.findall(v)
            if fields:
                formatted_outputs = {_k: _v for _k, _v in template_variable.items() if _k in fields}
                if not formatted_outputs:
                    error_msg = f'Configuration template variable definition error, name={fields}, template={v}'
                    print(error_msg)
                    raise ValueError(error_msg)
                v = v.format(**formatted_outputs)
                template_variable[k.upper()] = v
        _config[k] = v


def read_yaml(paths: List[str]):
    all_conf = dict()
    for path in paths:
        if not os.path.exists(path):
            print(f"{path} not found.")
            continue
        print(f"loading config file: {path}")
        with open(path, encoding='utf-8') as f:
            all_conf.update(yaml.load(f, Loader=yaml.FullLoader) or {})
    return all_conf


def reflush_conf_by_path(paths: List[str], upper=True, **template_variable):
    global _config
    all_conf = read_yaml(paths)
    if not all_conf:
        return
    _reflush_env(
        template_variable,
        conf_env=conf_env,
        **all_conf
    )
    config = Config(**_config)
    config.to_env(upper=upper)
    return config


def reflush_conf(conf_env: str = "test", upper=True, **template_variable):
    global _config
    conf_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "conf")
    sub_path = os.path.join(conf_path, conf_env)
    if not os.path.exists(sub_path):
        return
    paths = [os.path.join(conf_path, 'common.yaml')]
    for path in os.listdir(sub_path):
        if path.endswith(".yaml"):
            paths.append(os.path.join(sub_path, path))
    all_conf = read_yaml(paths)
    if not all_conf:
        return
    _reflush_env(
        template_variable,
        conf_env=conf_env,
        **all_conf
    )
    if not _config:
        return
    config = Config(**_config)
    config.to_env(upper=upper)
    print('The configuration has been successfully loaded.')
    return config


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--conf_env", dest='conf_env', type=str, default='test', required=False)
    parser.add_argument("--template_variable", dest='template_variable', type=str, required=True)
    return parser.parse_args()


def init_config(version=None, deployment_env=None):
    if version is None:
        version = os.environ.get("VERSION", "v1.6")
    if deployment_env is None:
        deployment_env = os.environ.get("DEPLOYMENT_ENV", "unkown")
    return reflush_conf(
        conf_env=deployment_env,
        **{"VERSION": version, "DEPLOYMENT_ENV": deployment_env}
    )


config = init_config()


if __name__ == "__main__":
    kwargs = vars(get_args())
    conf_env = kwargs.pop('conf_env')
    template_variable = _parse_template_variable(kwargs.pop('template_variable'))
    # config = reflush_conf(conf_env, **template_variable)
    # auto_import(base_path, depth=1)

# -*- encoding: utf-8 -*-
'''
@time        :2024/10/29 21:08:56
'''
import urllib3
import time
import logging
import json as _json
from types import MethodType
from urllib.parse import urlparse


logger = logging.getLogger(__name__)


class HttpResponseError(Exception):
    def __init__(self, **kwargs):
        self._error_msg = _json.dumps(kwargs, ensure_ascii=False, indent=4)

    def __repr__(self):
        return f"http fetch error:\n{self._error_msg}"

    def __str__(self):
        return self.__repr__()


def json_method(self):
    return _json.loads(self.text)


class Session(object):
    
    def __init__(self, retry=2, num_pools=100):
        session = urllib3.PoolManager(cert_reqs='CERT_NONE', num_pools=num_pools)
        self._session = session
        self._retry = retry

    def _fetch(self, url: str, method: str, record_logger=False, json=None, data=None, headers=None, **kwargs):
        if record_logger:
            api_start_time = time.time()
            cpu_start_time = time.process_time()
        if json is not None:
            data = _json.dumps(json)
            headers = {"Content-Type": "application/json", **(headers or {})}
        resp= self._session.request(method.upper(), url, body=data, headers=headers)
        if resp.status != 200:
            raise HttpResponseError(
                url=url,
                method=method,
                **kwargs,
                status=resp.status,
                error_msg=resp.data.decode(),
                reason=resp.reason
            )
        resp.ok = True
        resp.status_code = 200
        resp.text = resp.data.decode()
        resp.json = MethodType(json_method, resp)
        if record_logger:
            cpu_end_time = time.process_time()
            api_end_time = time.time()
            logger.info(
                f"请求记录, total_cost={(api_end_time - api_start_time) * 1000:.3f}ms, " \
                f"cpu_cost={(cpu_end_time - cpu_start_time) * 1000:.3f}ms, " \
                f"url={url}, method={method}, kwargs={kwargs}, response={resp.text}"
            )
        return resp

    def get(self, url, **kwargs):
        return self._fetch(url, "get", **kwargs)

    def post(self, url, **kwargs):
        return self._fetch(url, "post", **kwargs)

    def close(self):
        self._session.close()


_SESSION_CACHE = {}

def _get_session(url):
    global _SESSION_CACHE
    host = urlparse(url).hostname
    if host not in _SESSION_CACHE:
        _SESSION_CACHE[host] = Session(retry=5, num_pools=100)
    return _SESSION_CACHE[host]


def get(url, **kwargs):
    session = _get_session(url)
    return session.get(url, **kwargs)


def post(url, **kwargs):
    session = _get_session(url)
    return session.post(url, **kwargs)


if __name__ == "__main__":
    
    resp = get("https://www.baidu.com", record_logger=True)
    print(resp.text)
    
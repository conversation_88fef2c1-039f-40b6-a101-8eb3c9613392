# -*- coding: utf-8 -*-

# Filename: const.py
# 定义一个常量类实现常量的功能
#
# 该类定义了一个方法__setattr()__，和一个异常ConstError, ConstError类继承
# 自类TypeError. 通过调用类自带的字典__dict__, 判断定义的常量是否包含在字典
# 中。如果字典中包含此变量，将抛出异常，否则，给新创建的常量赋值。
# 最后两行代码的作用是把const类注册到sys.modules这个全局字典中。
import os


class _const(object):

    class ConstError(PermissionError):
        pass

    def __setattr__(self, name, value):
        if name in self.__dict__.keys():
            raise self.ConstError("Can't rebind const(%s)" % name)
        self.__dict__[name] = value

    def __delattr__(self, name):
        if name in self.__dict__:
            raise self.ConstError("Can't unbind const(%s)" % name)
        raise NameError(name)


const = _const()

# 主目录路径
# const.ROOT_PATH = os.path.abspath(sys.argv[0]).split("/shilai_common/")[0]
const.ROOT_PATH = '/tmp'

# 保存商户二维码路径
const.MERCHANT_ID_QRCODE_SAVE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/qrcode_images')
# 保存码牌二维码路径
const.CODE_PLATE_QRCODE_SAVE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/code_plate_qrcode')
# 保存宣发二维码路径
const.PUBLICITY_QRCODE_SAVE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/publicity_qrcode')
# 保存商户下用户小程序码路径
const.MERCHANT_CODE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/merchant_code_images')
const.SELF_DINING_MERCHNT_CODE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/self_dining_merchant_code_images')
# 保存商户下饭局入口的小程序码路径
const.GROUP_DINING_CODE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/group_dining_code_images')
# 商户桌台点餐二维码图片存储入口
# const.TABLE_CODE_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/merchant_table_code_images')
const.TABLE_CODE_DIR = "/Users/<USER>/code/qrcode-images"
# 保存上传的静态图片的路径
const.STATIC_IMAGES_DIR = os.path.join(const.ROOT_PATH, 'shilai_common/data/static_images')
const.ALIYUN_OSS_SHILAI_DOMAIN = "https://shilai-images.oss-cn-shenzhen.aliyuncs.com/"
const.ALIYUN_OSS_UPLOAD_URL = "http://oss-cn-shenzhen.aliyuncs.com"
const.ALIYUN_OSS_ACCESS_KEY = "LTAI4G1zvzqANmwFDS9zCSb7"
const.ALIYUN_OSS_ACCESS_KEY_SECRET = "******************************"
const.ALIYUN_OSS_DISH_IMAGE_BUCKET = "shilai-images"

# 第三方商户ID
const.GRANT_TYPE = 'authorization_code'

# 时间相关常量
const.ONE_DAY_IN_SECONDS = 60 * 60 * 24
const.STR_FORMAT = "%Y-%m-%d %H:%M:%S"

# 获取当前的环境信息

# ENVIRONMENT = os.getenv("SHILAI_ENVIRONMENT")

# ENVIRONMENT_DEV = 'dev'
# ENVIRONMENT_PRODUCE = 'produce'

# SERVICE_HOST = "**************"
# 商户助手服务的端口
# MERCHANT_ASSISTANT_SERVICE_PORT = 6002

from common.load_conf import config

if config is not None:
    const = config

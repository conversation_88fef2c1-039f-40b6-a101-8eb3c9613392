# -*- coding: utf-8 -*-

""" 时来其它服务返回结果的预处理类
"""


class ApiResult:

    SUCCESS = 0x01
    FAIL = 0x02
    # 不是时来pos机服务商户
    NOT_SHILAI_POS_MERCHANT = 0x03

    def __init__(self, result=None, parse_data_function=None, parse_data_function_kargs={}):
        """
        :params result: 其它的接口的返回内容
        :params parse_data_function: 处理data的函数
            第一个参数为函数
            第二个参数为除data外还需要的参数,必须以 key=value的形式给出
        """
        self.errcode = self.NOT_SHILAI_POS_MERCHANT
        self.errmsg = "NotSupport"
        self.data = None
        if result:
            self.errcode = result.get("errcode")
            self.errmsg = result.get("errmsg", "NotSupport")
            self.data = result.get("data")
        if parse_data_function is not None:
            self.data = parse_data_function(self.data, **parse_data_function_kargs)

    @property
    def flag(self):
        return self.errcode == 0

    def set_data(self, data):
        self.data = data

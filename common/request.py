# -*- encoding: utf-8 -*-
'''
@Time        :2024/05/10 00:14:36
'''
import logging

from flask import request

from service import errors, error_codes
from common.schema import AutoSnake, to_full, to_bar, AutoCamel


logger = logging.getLogger(__name__)


def get_headers(headers: dict = None) -> dict:
    if headers is None:
        headers = dict(request.headers)
    excluded_headers = {
        "X-Real-Ip", "X-Forwarded-For", "Host", "X-Nginx-Proxy", "Connection",
        "Content-Length", "Content-Type", "User-Agent", "Accept", "Message-Uuid",
        "Request-Timestamp", "Accept-Encoding", "Sec-Fetch-Site", "Sec-Fetch-Mode",
        "Sec-Fetch-Dest", "Referer", "Charset", "Accept-Language", "Session",
        "Cache-Control", "Sec-Ch-Ua", "", "Request-Timestamp-Cpu"
    }
    return dict(filter(lambda item: item[0] not in excluded_headers, headers.items()))


def check_body(
    field_map: dict = {},
    only_null=False,
    include_headers=True,
    snake=True
):
    body = {}
    if include_headers:
        body.update(get_headers())
    if request.args:
        body.update(request.args.to_dict() or {})
    if request.view_args:
        body.update(request.view_args or {})
    if request.values:
        body.update(request.values.to_dict() or {})
    if request.method.upper() == 'POST':
        if request.is_json:
            body.update(request.json or {})
        if request.form:
            body.update(request.form.to_dict() or {})
    if request.files:
        body["files"] = list(request.files.keys())
    if not body:
        raise errors.Error(
            errcode=error_codes.PARAMETER_NOT_ENOUGH,
            errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG
        )
    field_map = AutoSnake(field_map)
    body = AutoSnake(body)
    if only_null:
        return {body.get(k) for k in field_map.keys()}
    
    def _check_value(value, verifier_map: dict, key: str):
        required = verifier_map.get("required")
        if required and value is None:
            logger.error(
                f"{error_codes.PARAMETER_NOT_ENOUGH_MSG}, 参数{key}为必传字段"
            )
            raise errors.Error(
                errcode=error_codes.PARAMETER_NOT_ENOUGH,
                errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG
            )
        if value is not None:
            _type = verifier_map.get("type")
            if _type and not isinstance(value, _type):
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前类型为{type(value)}, 实际类型为{_type}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
            max_len = verifier_map.get("max_len")
            if max_len and isinstance(value, str) and len(value) > max_len:
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前值为{value}, 最大长度为{max_len}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
            min_len = verifier_map.get("min_len")
            if min_len and isinstance(value, str) and len(value) < min_len:
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前值为{value}, 最小长度为{min_len}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
            max_value = verifier_map.get("max_value")
            if max_value and isinstance(value, (int, float)) and value > max_value:
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前值为{value}, 最大值为{max_value}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
            min_value = verifier_map.get("min_value")
            if min_value and isinstance(value, (int, float)) and value < min_value:
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前值为{value}, 最小值为{min_value}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
            in_value = verifier_map.get("in")
            if in_value and not value in in_value:
                logger.error(
                    f"{error_codes.INVALID_REQUEST_DATA_MSG}, 参数{key}, 当前值为{value}, 不在{in_value}"
                )
                raise errors.Error(
                    errcode=error_codes.INVALID_REQUEST_DATA,
                    errmsg=error_codes.INVALID_REQUEST_DATA_MSG
                )
        custom_func = verifier_map.get("custom_func")
        if custom_func and callable(custom_func):
            value = custom_func(value)

        if isinstance(value, str):
            value = value.strip()

        default = verifier_map.get("default")
        if value is None and default:
            value = default

        return value 
    
    result = AutoSnake({}) if snake else AutoCamel({})
    for k, verifier_map in field_map.items():
        value = body.get(k)
        if value is None:
            value = body.get(to_full(k))
        if value is None:
            value = body.get(to_bar(k))
        value = _check_value(value, verifier_map, key=k)
        result[k] = value
    return result

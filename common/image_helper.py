# -*- coding: utf-8 -*-

import requests
from urllib.parse import urlparse

from collections import namedtuple
from PIL import Image
import math
import os

from common.aliyun_oss_helper import AliyunOSSHelper


class ImageHelper:

    def __init__(self, merchant_id):
        self.source_dir = "/tmp/{}/images".format(merchant_id)
        self.target_dir = "/tmp/{}/output".format(merchant_id)
        if not os.path.exists(self.source_dir):
            os.makedirs(self.source_dir)
        if not os.path.exists(self.target_dir):
            os.makedirs(self.target_dir)

    def download_image_local(self, image_url):
        o = urlparse(image_url)
        name = o.path.split("/")[-1]
        response = requests.get(image_url)
        filepath = "{}/{}".format(self.source_dir, name)
        file = open(filepath, "wb")
        file.write(response.content)
        file.close()

    def resize_image(self, threshold, image_url):
        o = urlparse(image_url)
        filename = o.path.split("/")[-1]
        src_filepath = "{}/{}".format(self.source_dir, filename)
        dist_filepath = "{}/{}".format(self.target_dir, filename)
        if os.path.exists(dist_filepath):
            return None
        aliyun_oss_helper = AliyunOSSHelper("shilai-images")
        vs = self.valid_suffix(filename)
        if vs.flag:
            filesize = os.path.getsize(src_filepath)
            with Image.open(src_filepath) as im:
                width, height = im.size
                new_size = self.compress_strategy(threshold, filesize, width, height, vs.suffix)
                if width >= height:
                    new_width = int(math.sqrt(new_size))
                    new_height = int(new_width * height * 1.0 / width)
                else:
                    new_height = int(math.sqrt(new_size))
                    new_width = int(new_height * width * 1.0 / height)
                resized_im = im.resize((new_width, new_height))
                print("保存到: {}".format(dist_filepath))
                try:
                    resized_im.save(dist_filepath)
                except OSError:
                    resized_im = resized_im.convert("RGB")
                    resized_im.save(dist_filepath)
                return aliyun_oss_helper.upload_image_local(dist_filepath)
        else:
            return aliyun_oss_helper.upload_image_network_stream(image_url)

    def resize_images(self, threshold):
        filenames = os.listdir(self.source_dir)
        aliyun_oss_helper = AliyunOSSHelper("shilai-images")
        for filename in filenames:
            if os.path.exists("{}/{}".format(self.target_dir, filename)):
                continue
            if "." not in filename:
                output_filename = filename.replace(self.source_dir, self.target_dir)
                command = 'cp {}/{} {}/{}'.format(self.source_dir, filename, self.target_dir, output_filename)
                os.system(command)
                continue
            filename = "{}/{}".format(self.source_dir, filename)
            filesize = os.path.getsize(filename)
            vs = self.valid_suffix(filename)
            if not vs.flag:
                continue
            with Image.open(filename) as im:
                width, height = im.size
                new_size = self.compress_strategy(threshold, filesize, width, height, vs.suffix)
                if width >= height:
                    new_width = int(math.sqrt(new_size))
                    new_height = int(new_width * height * 1.0 / width)
                else:
                    new_height = int(math.sqrt(new_size))
                    new_width = int(new_height * width * 1.0 / height)
                resized_im = im.resize((new_width, new_height))
                output_filename = filename.replace(self.source_dir, self.target_dir)
                try:
                    resized_im.save(output_filename)
                except OSError:
                    resized_im = resized_im.convert("RGB")
                    resized_im.save(output_filename)
                aliyun_oss_helper.upload_image_local(output_filename)

    def valid_suffix(self, filename):
        ValidSuffix = namedtuple("ValidSuffix", ["flag", "suffix"])
        suffix = filename.split(".")[-1]
        valid_suffix = ["png", "jpg", "jpeg", "PNG", "JPG", "JPEG"]
        if suffix.lower() in valid_suffix:
            return ValidSuffix(True, suffix)
        return ValidSuffix(False, "jpg")

    def compress_strategy(self, threshold, filesize, width, height, suffix):
        ONE_K = 1024
        ONE_M = 1024 * 1024
        factor = 1
        if suffix in ["png", "PNG"]:
            factor = 0.15
        if filesize <= 40 * ONE_K:
            # 图片小于等于40k则不做处理
            return threshold * 400 * factor
        if 40 * ONE_K < filesize <= 500 * ONE_K:
            # 40 到 300k
            return threshold * 350 * factor
        if 500 * ONE_K < filesize <= 1000 * ONE_K:
            return threshold * 300 * factor
        if 1000 * ONE_K < filesize <= 1500 * ONE_K:
            return threshold * 250 * factor
        if 1500 * ONE_K > filesize:
            return threshold * (filesize / float(ONE_M) * 200) * factor
        return threshold * (filesize / float(ONE_M) * 150) * factor

# -*- coding: utf-8 -*-

import hashlib


class CacheServerKeys:
    @staticmethod
    def order_coupon_package_union_pay_repeat_key(order):
        user_id = None
        if order.user_id:
            user_id = order.user_id
        elif order.order_user_id:
            user_id = order.user_id
        elif len(order.user_ids) > 0:
            user_id = order.user_ids[0]
        if user_id:
            key = "order_coupon_package_union_pay_repeat_key_{}_{}".format(user_id, order.merchant_id)
            return key
        return None

    @staticmethod
    def get_order_repeat_pay_key(order):
        """ 重复支付时用于去重的key
        """
        return "set_order_repeat_pay_{}".format(order.id)

    @staticmethod
    def get_order_no_pos_paid_key(order):
        """ 在小程序端支付了,收到第三回调时,用于去重处理的key
        """
        return "order_no_pos_paid_{}".format(order.id)

    @staticmethod
    def get_staff_assist_update_dish_key(merchant):
        """ 业务助手同步菜品时,去重用的key
        """
        return "staff_assist_update_dish_key_{}".format(merchant.id)

    @staticmethod
    def get_order_refund_redis_callback_key(order):
        """ 接收退货回调时用来去重的key
        """
        return "order_refund_repeat_key_{}".format(order.id)

    @staticmethod
    def get_check_repeat_prepay_key(user_id, merchant_id, paid_fee):
        """ 同一个用户在同一家店,短时间内唤起同样金额的prepay时,需要做提醒
        """
        return "check_repeat_prepay_key_{}_{}_{}".format(user_id, merchant_id, paid_fee)

    @staticmethod
    def get_check_repeat_prepay_order_key(order_id, merchant_id):
        return "check_repeat_prepay_order_key_{}_{}".format(order_id, merchant_id)

    @staticmethod
    def get_refund_callback_repeat_key(pay_transaction_id, business_transaction_id, refund_transaction_id):
        """ 对于某些支付方式,接收回调来做业务处理.回调会收到多次,需要做去重处理
        """
        s = "{}{}{}".format(pay_transaction_id, business_transaction_id, refund_transaction_id)
        s = s.encode("utf8")
        sha1 = hashlib.sha1()
        sha1.update(s)
        return "refund_callback_repeat_key_{}".format(sha1.hexdigest())

    @staticmethod
    def get_user_fanpiao_scan_qrcode_key(user_id):
        """ 用户当前饭票扫码支付的二维码
        """
        key = "{}_user_fanpiao_scan_qrcode".format(user_id)
        return key

    @staticmethod
    def get_dish_remain_quantity_key(dish_id):
        return "{}_remain_quantity".format(dish_id)

    @staticmethod
    def get_order_remain_quantity_key():
        return "order_remain_quantity"

    @staticmethod
    def get_table_shopping_card_key(table_id):
        key = "table_shopping_card_key_{}".format(table_id)
        return key

    @staticmethod
    def get_shopping_card_key(table_id):
        key = f"shopping_card_key_{table_id}"
        return key

    @staticmethod
    def get_user_phone_code_verify_key(user_id):
        return f"user_phone_code_verify_{user_id}"

    @staticmethod
    def get_fanpiao_count_key():
        """饭票总数"""
        return "fanpiao_count_key"

    @staticmethod
    def get_user_coupon_cache_key(user_id):
        """券对应数据"""
        return f'user_coupon_cache_{user_id}'

    @staticmethod
    def get_order_paying_cache_key(order_id):
        """设置标志,订单在短时间内不能加菜"""
        return f'order_paying_cannot_add_dish_{order_id}'

    @staticmethod
    def get_add_dish_key(order_id):
        return f'order_add_dish_{order_id}'
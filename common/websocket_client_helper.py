# -*- coding: utf-8 -*-

import json

from websocket import create_connection

from common import constants
from common.config import config

c_websocket_client = None # C端websocket客户端
b_websocket_client = None # B端websocket客户端

class CWebsocketClient():
    client = None
    def __init__(self):
        pass
    def send_message(self, data, retry=2):
        pass

class BWebsocketClient():
    client = None
    def __init__(self):
        pass

    def send_message(self, data, retry=2):
        pass

# -*- coding: utf-8 -*-

SHILAI_DEPLOYMENT_ENV_NAME = 'SHILAI_DEPLOYMENT'

# Web对外服务域名环境变量名称
SERVICE_DOMAIN_ENV_NAME = 'SERVICE_DOMAIN'
SERVICE_DOMAIN_WITHOUT_VERSION_ENV_NAME = 'SERVICE_DOMAIN_WITHOUT_VERSION'
H5_SERVICE_DOMAIN_ENV_NAME = 'H5_SERVICE_DOMAIN'

# 中控Access Token服务配置环境变量名称
ACCESS_TOKEN_SERVICE_ADDRESS_ENV_NAME = 'ACCESS_TOKEN_SERVICE_ADDRESS'
ACCESS_TOKEN_SERVICE_PORT_ENV_NAME = 'ACCESS_TOKEN_SERVICE_PORT'

SHILAI_ACCESS_TOKEN_SERVICE_ADDRESS_ENV_NAME = 'SHILAI_ACCESS_TOKEN_SERVICE_ADDRESS'
SHILAI_ACCESS_TOKEN_SERVICE_PORT_ENV_NAME = 'SHILAI_ACCESS_TOKEN_SERVICE_PORT'

# 微信事件消息接受服务端口环境变量名称
WECHAT_EVENT_HANDLING_SERVICE_ADDRESS_ENV_NAME = 'WECHAT_EVENT_HANDLING_SERVICE_ADDRESS'
WECHAT_EVENT_HANDLING_SERVICE_PORT_ENV_NAME = 'WECHAT_EVENT_HANDLING_SERVICE_PORT'

# 后端主体Web Service服务端口环境变量名称
MAIN_WEB_SERVICE_ADDRESS_ENV_NAME = 'MAIN_WEB_SERVICE_ADDRESS'
MAIN_WEB_SERVICE_PORT_ENV_NAME = 'MAIN_WEB_SERVICE_PORT'

# 平台账号相关环境变量名称
WECHAT_APP_ID_ENV_NAME = 'WECHAT_APP_ID_ENV_NAME'
WECHAT_APP_SECRET_ENV_NAME = 'WECHAT_APP_SECRET'

# Access Token中控服务器地址信息
ACCESS_TOKEN_SERVICE_ADDRESS = '**********'
ACCESS_TOKEN_SERVICE_PORT = 8886

# 部署环境
DEPLOYMENT_ENV_PROD = 'prod'
DEPLOYMENT_ENV_TEST = 'test'
DEPLOYMENT_ENV_LOCAL = 'local'

# 用户请求来源：来自用户小程序端
PLATFORM_USER = "user"
# 用户请求来源：来自商户小程序端
PLATFORM_MERCHANT = "merchant"
# 用户请求来源：来自时来业务员小程序端
PLATFORM_SHILAI_STAFF = "shilai-staff"
# 时易猫
PLATFORM_SHIYI_CAT = "shiyi-cat"

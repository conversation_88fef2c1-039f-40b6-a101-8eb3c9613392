import smtplib
import logging
from email.header import Header
from email.mime.text import MIMEText
from email.utils import formataddr
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication

logger = logging.getLogger(__name__)


class EmailHelper(object):

    def __init__(self):
        # self.account = "<EMAIL>"
        self.account = "<EMAIL>"
        # self.password = "oUNnHMvBZ2DVboQp"
        self.password = "fTk7dzt7XvS83dyk"

    def send_email(self, receiver, title, content, stream=None, file_name=""):
        """

        Args:
            receiver: 收件人
            title: 标题
            content: 文本内容
            stream: 附件的文件流
            file_name: 附件名称

        Returns:

        """
        # 用户信息
        # from_addr = '<EMAIL>'   # <EMAIL>
        # from_addr = '<EMAIL>'
        # password = 'oUNnHMvBZ2DVboQp'  # xcUSSPc5HESNa6Wi
        # password = 'xcUSSPc5HESNa6Wi'
        smtp_server = 'smtp.exmail.qq.com'  # 腾讯服务器地址
        # 内容初始化，定义内容格式（普通文本，html）
        # content = MIMEText('时来饭票营收数据报表', 'plain', 'utf-8')
        content = MIMEText(content, 'plain', 'utf-8')

        # 发件人收件人信息格式化 ，可防空
        # 固定用法不必纠结，我使用lambda表达式进行简单封装方便调用
        # 构造附件
        mmp = MIMEMultipart()
        mmp.attach(content)
        if stream and file_name:
            file_attach = MIMEApplication(stream)
            file_attach.add_header('Content-Disposition', 'attachment', filename=file_name or "附件")
            mmp.attach(file_attach)
        # 邮件标题
        lam_format_addr = lambda name, addr: formataddr((Header(name, 'utf-8').encode(), addr))
        mmp['Subject'] = Header(title, 'utf-8').encode()
        mmp['From'] = lam_format_addr('发件人昵称', self.account)  # 显示发件人信息
        # mmp['To'] = lam_format_addr('收件人昵称（服务商会自动替换成用户名）', receiver)  # 显示收件人信息
        # 服务端配置，账密登陆
        port = 465
        with smtplib.SMTP_SSL(smtp_server, port) as server:
            # 登陆服务器
            server.login(self.account, self.password)
            logger.info(f"SMTP server login chenggong: {self.account}")
            # 发送邮件及退出
            response = server.sendmail(self.account, [receiver], mmp.as_string())  # 发送地址需与登陆的邮箱一致
            logger.info(f"SMTP server response: {response}")
            return response

# -*- coding: utf-8 -*-

import logging

from functools import wraps

logger = logging.getLogger(__name__)


""" 一些不好定义的函数
"""

def safe_round(value):
    """ python3.x 的round的四舍五入采用的是近偶数
    如:
      round(3.5) = 4
      round(2.5) = 2
    当与其它服务对接时有可能会出现不一致的问题
    """
    if not isinstance(value, int):
        return None
    return int(value + 0.5)


def try_cache(fn):
    @wraps(fn)
    def inner(*args, **kargs):
        try:
            return fn(*args, **kargs)
        except Exception as ex:
            logger.exception(ex)
    return inner

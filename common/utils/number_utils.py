# -*- coding: utf-8 -*-


class NumberUtils:

    @staticmethod
    def safe_round(number, digit=0):
        if number is None:
            return 0
        if isinstance(number, int):
            return number
        if isinstance(number, float):
            return round(number, digit)
        if isinstance(number, str):
            if not number.isdigit():
                return 0
            number = float(number)
            return round(number, digit)
        return 0

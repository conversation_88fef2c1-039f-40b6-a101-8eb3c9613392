
def try_decode_bytes_to_utf8(buffer):
    """尝试将字节流转为UTF-8编码格式，若尝试失败，
    则尝试先以ISO-8859-1格式解码，然后再转换成UTF-8文本。
    """
    try:
        return buffer.decode('utf-8')
    except UnicodeDecodeError:
        return bytearray(buffer, 'iso-8859-1').decode('utf-8')

def try_convert_text_to_utf8(text):
    """尝试将文本从ISO-8859-1格式转为UTF-8编码格式，若尝试失败，则直接返回原始文本。
    """
    try:
        return bytearray(text, 'iso-8859-1').decode('utf-8')
    except UnicodeEncodeError:
        return text
# -*- coding: utf-8 -*-

import os
import shutil
from pathlib import Path

import tarfile
import zipfile


def compress(output_path, dir_path):
    """
        压缩文件夹到指定路径

        参数:
            output_path    string  是  输出压缩文件路径
            dir_path       string  是  输入压缩文件夹路径
    """
    zip = zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED)
    for dirpath, dirnames, filenames in os.walk(dir_path):
        # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
        fpath = dirpath.replace(dir_path, '')

        for filename in filenames:
            if filename != os.path.basename(output_path):
                zip.write(os.path.join(dirpath, filename),
                          os.path.join(fpath, filename))

    zip.close()


class Compress:

    @staticmethod
    def zip_compress(output_path, dir_path):
        """
        压缩文件夹到指定路径

        参数:
            output_path    string  是  输出压缩文件路径
            dir_path       string  是  输入压缩文件夹路径
        """
        zip = zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED)
        for dirpath, dirnames, filenames in os.walk(dir_path):
            # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
            fpath = dirpath.replace(dir_path, '')

            for filename in filenames:
                if filename != os.path.basename(output_path):
                    zip.write(os.path.join(dirpath, filename),
                              os.path.join(fpath, filename))
        zip.close()

    @staticmethod
    def tar_gz_compress(dir_path, output_path=None, rmtree=False):
        """ 把目录压缩成*.tar.gz
        @dir_path: 要压缩的目录
        @output_path: 被压缩后的文件所在的父目录,如果不传,则是dir_path的父目录
        @rmtree: 是否删除原目录
        """
        p = Path(dir_path)
        dirname = p.parts[-1]
        parent_path = p.parent
        if output_path is None:
            output_path = parent_path
        output_filename = "{}.tar.gz".format(dirname)
        compress_package_path = "{}/{}".format(output_path, output_filename)
        tar_file = tarfile.open(compress_package_path, "w:gz")
        tar_file.add(dir_path, arcname=dirname)
        tar_file.close()
        if rmtree:
            shutil.rmtree(dir_path)

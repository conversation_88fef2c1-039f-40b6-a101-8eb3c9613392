import functools
import time


def get_headers_info(request, headers_key):
    headers = request.headers
    if headers_key in headers:
        return headers.get(headers_key)
    else:
        return None

def get_platform(request):
    """ 用户、商户、时来业务员三端小程序，请求头带上 from-platform 作为来源标识：
       1. user: 用户端小程序
       2. merchant: 商户端小程序
       3. shilai-staff: 时来业务员小程序
    """
    return get_headers_info(request, 'from-platform')

def get_value_from_json(request_json, field_name, default_value=None):
    """ 从 request.get_json() 中获取指定值
    """
    return request_json[field_name] if field_name in request_json else default_value

def get_from_request(request, key, default=None):
    value = None
    if request.json:
        value = request.json.get(key)
    if request.args:
        value = request.args.get(key)
    if value is None:
        return default
    return value


def retry(times=1, exc_types=(Exception, ), callback=None, delay=None):
    def wrapper(func):
        @functools.wraps(func)
        def _exec(*args, **kwargs):
            i = 0
            while True:
                try:
                    rtn = func(*args, **kwargs)
                    i = 0
                    return rtn
                except exc_types:
                    if i >= times:
                        raise
                    if callback is not None:
                        args, kwargs = callback(i, *args, **kwargs)
                    if delay is not None:
                        time.sleep(delay)
                    i += 1
        return _exec
    return wrapper

import os
from PIL import Image, ImageDraw, ImageFont
from business_ops import constants
from common.utils import file_access_helper


def generate_merchant_qrcode(background_path, qrcode_path, number, filename):
    """自动生成带小程序码的商户宣传页（定制开发，只针对固定背景图进行小程序码和桌号嵌入）

    Args:
        background_path:背景图存放路径
        qrcode_path:小程序码存放路径
        number:桌号
        filename:保存商户页图片的名称

    Returns:
        商户宣传页图片存放路径
    """
    background = Image.open(background_path)
    qrcode = Image.open(qrcode_path)
    width = background.size[0]
    height = background.size[1]

    # 调整小程序码的尺寸，适应背景图
    qrcode_reshape = (1000, 1000)
    reqrcode = qrcode.resize(qrcode_reshape)

    # 计算粘贴小程序码的位置
    distance_left = 500
    distance_up = 670
    location_x = int(width / 2) - distance_left
    location_y = int(height / 2) - distance_up

    background.paste(reqrcode, (location_x, location_y), reqrcode)
    fontpath = os.path.join(constants.FONT_DIR, 'msyhbd.ttf')
    file_access_helper.ensure_directory_exists_for_filename(fontpath)

    # 根据桌号是否为2位数，调整字号大小和书写桌号位置
    box1 = (1050, 2980)
    box2 = (1125, 2900)
    text = str(number)
    if len(text) == 2:
        font_size = 300
        box = box1
    if len(text) == 1:
        font_size = 400
        box = box2
    font = ImageFont.truetype(fontpath, font_size)
    draw = ImageDraw.Draw(background)
    draw.text(box, text, (0, 0, 0), font=font)
    savepath = os.path.join(constants.MERCHANT_PAGE_SAVE_DIR, '{}.png'.format(filename))
    file_access_helper.ensure_directory_exists_for_filename(savepath)
    background.save(savepath)
    return savepath

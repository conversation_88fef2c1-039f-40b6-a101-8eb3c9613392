import base64

from Crypto.Cipher import AES


class AESCrypt():
    def __init__(self, key):
        self.key = key
        self.mode = AES.MODE_CBC

    def encrypt(self, text):
        cryptor = self.__create_cryptor()
        length = 16
        count = len(text.encode('utf-8'))
        if (count % length != 0):
            add = length - (count % length)
        else:
            add = 0
        text1 = text + ('\0' * add)
        try:
            self.ciphertext = cryptor.encrypt(text1)
        except:
            self.ciphertext = cryptor.encrypt(text1.encode('utf8'))
        cryptedStr = str(base64.b64encode(self.ciphertext), encoding='utf-8')
        return cryptedStr

    def decrypt(self, text):
        base_text = base64.b64decode(text)
        cryptor = self.__create_cryptor()
        plain_text = cryptor.decrypt(base_text)
        ne = plain_text.decode('utf-8').rstrip('\0')
        return ne

    def __create_cryptor(self):
        try:
            cryptor = AES.new(self.key, self.mode, self.key)
        except:
            cryptor = AES.new(self.key.encode('utf8'), self.mode, self.key.encode('utf8'))
        return cryptor

# -*- coding: utf-8 -*-

import logging
import io
import math
import os
import requests
from collections import namedtuple

from PIL import Image

from common.aliyun_oss_helper import AliyunOSSHelper

logger = logging.getLogger(__name__)


class ImageUtils:

    def __init__(self):
        self.oss_client = AliyunOSSHelper()

    def upload_image_from_url(self, url):
        name = os.path.basename(url)
        resp = requests.get(url)
        if resp.status_code != 200:
            return None
        binary = io.BytesIO(resp.content)
        compress_image = self.compress_image(binary, name)
        if not compress_image:
            return None
        return self.oss_client.upload_image_binary(compress_image.binary, compress_image.name)

    def compress_image(self, image_binary, filename, threshold=1024, fmt="png"):
        """ 把图片转换成指定的格式,并压缩
        @image_binary: 图片的二进制格式
        @fmt: 要转换成的格式
        """
        CompressImage = namedtuple("CompressImage", ["name", "binary"])
        filesize = image_binary.getbuffer().nbytes
        if filesize <= 1024 * 1024:
            # 原始图片小于等于1M时不做压缩
            return None
        vs = self.valid_suffix(filename)
        if not vs.flag:
            logger.info("图片格式不正确: {}".format(filename))
            return
        with Image.open(image_binary) as im:
            im_buf = io.BytesIO()
            im.save(im_buf, format=fmt)
            with Image.open(im_buf) as im_png:
                width, height = im_png.size
                new_size = self.compress_strategy(threshold, filesize, width, height, fmt)
                resized_im = im_png.resize((new_size.width, new_size.height))
                resized_im_buf = io.BytesIO()
                resized_im.save(resized_im_buf, format=fmt)
                resized_im_buf.seek(0)
                name = "{}.{}".format(vs.name, fmt)
                return CompressImage(name, resized_im_buf)
        return None

    def valid_suffix(self, filename):
        ValidSuffix = namedtuple("ValidSuffix", ["flag", "suffix", "name"])
        suffix = filename.split(".")[-1]
        name = filename.split(".")[0]
        valid_suffix = ["png", "jpg", "jpeg"]
        if suffix.lower() in valid_suffix:
            return ValidSuffix(True, suffix, name)
        return ValidSuffix(False, suffix, "")

    def compress_strategy(self, threshold, filesize, width, height, suffix):
        ONE_K = 1024
        ONE_M = 1024 * 1024
        factor = 1
        if suffix == "png":
            factor = 0.15
        if filesize <= 40 * ONE_K:
            # 图片小于等于40k则不做处理
            return self.convert_to_width_height(threshold * 400 * factor, filesize, width, height, suffix)
        if 40 * ONE_K < filesize <= 500 * ONE_K:
            return self.convert_to_width_height(threshold * 350 * factor, filesize, width, height, suffix)
        if 500 * ONE_K < filesize <= 1000 * ONE_K:
            return self.convert_to_width_height(threshold * 300 * factor, filesize, width, height, suffix)
        if 1000 * ONE_K < filesize <= 1500 * ONE_K:
            return self.convert_to_width_height(threshold * 250 * factor, filesize, width, height, suffix)
        if 1500 * ONE_K > filesize:
            return self.convert_to_width_height(threshold * (filesize / float(ONE_M) * 200) * factor, filesize, width, height, suffix)

        return self.convert_to_width_height(threshold * (filesize / float(ONE_M) * 150) * factor, filesize, width, height, suffix)

    def convert_to_width_height(self, new_size, filesize, width, height, suffix):
        Size = namedtuple("Size", ["width", "height"])
        if width >= height:
            new_width = int(math.sqrt(new_size))
            new_height = int(new_width * height * 1.0 / width)
        else:
            new_height = int(math.sqrt(new_size))
            new_width = int(new_height * width * 1.0 / height)
        return Size(new_width, new_height)

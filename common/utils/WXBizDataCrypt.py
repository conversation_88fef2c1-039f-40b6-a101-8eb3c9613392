import base64
import json
import logging
from Crypto.Cipher import AES
from common.utils import encoding_utils

logger = logging.getLogger(__name__)


class WXBizDataCrypt:
    def __init__(self, appId, sessionKey):
        self.appId = appId
        self.sessionKey = session<PERSON>ey

    def decrypt(self, encryptedData, iv):
        # base64 decode
        sessionKey = base64.b64decode(self.sessionKey)
        encryptedData = base64.b64decode(encryptedData)
        iv = base64.b64decode(iv)

        try:
            cipher = AES.new(sessionKey, AES.MODE_CBC, iv)
        except:
            cipher = AES.new(sessionKey.encode('utf8'), AES.MODE_CBC, iv.encode('utf8'))

        unpad_bytes = self._unpad(cipher.decrypt(encryptedData))
        logger.info(f'Decrypted Bytes: {unpad_bytes}')
        decrypted = json.loads(encoding_utils.try_decode_bytes_to_utf8(unpad_bytes))
        logger.info(f"decrypted: {decrypted}")
        if 'watermark' not in decrypted:
            return decrypted
        if decrypted['watermark']['appid'] != self.appId:
            raise Exception('Invalid Buffer')

        return decrypted

    def _unpad(self, s):
        return s[:-ord(s[len(s)-1:])]

# 管理微信的接口调用凭据
#  1. 时来公众号和小程序接口调用凭据统一由生产环境的中控服务提供
#  2. 时来开放平台接口调用凭据区分测试和生产两个环境
#  3. 时来开放平台授权方接口调用凭据区分测试和生产两个环境
#
# TODO:
#  1. 生产环境的中控服务增加接口调用权限检查
import os

import common.constants as constants
from common.config import config
from common import http

TOKEN_SERVICE_BASE_URL = 'http://{}:{}'.format("access-token.server", 8886)

# SHILAI_APP 为时来公众号和小程序的统称，目前生产和测试环境共用一套账号，统一由生产环境的中控服务提供
SHILAI_APP_TOKEN_SERVICE_BASE_URL = 'http://{}:{}'.format("access-token.server", 8886)


def get_component_token():
    """获取时来开放平台 access_token"""
    return _get_access_token('get_component_token')


def get_platform_token(platform):
    return _get_access_token('get_platform_token', headers={'from-platform': platform})


def add_authorizer_token(authorizer_token):
    url = '{}/authorizer_token'.format(TOKEN_SERVICE_BASE_URL)
    http.post(url, json=authorizer_token)


def get_authorizer_access_token(appid):
    """获取时来开放平台授权方 access_token
    """
    return _get_access_token('get_authorizer_token', appid=appid)


def add_authorizer_api_ticket(api_ticket):
    url = '{}/authorizer_api_ticket'.format(TOKEN_SERVICE_BASE_URL)
    http.post(url, json=api_ticket)


def get_authorizer_api_ticket(appid, ticket_type):
    """获取时来开放平台授权方 ticket

    Args:
        appid: (string) 时来公众号或小程序 appid
        ticket_type: (string) Ticket 类型，包括 WX_CARD 和 JS_API

    Return:
        (string) ticket
    """

    return _get_api_ticket(appid=appid, ticket_type=ticket_type)


def get_shilai_app_access_token(appid):
    """获取时来公众号或小程序 access_token

    生产和测试环境共用时来公众号和时来小程序账号，所以 access_token 统一由生产环境提供服务

    Args:
        appid: (string) 时来公众号或小程序 appid

    Return:
        (string) access_token
    """
    return _get_access_token('get_authorizer_token', service_base_url=SHILAI_APP_TOKEN_SERVICE_BASE_URL, appid=appid)


def get_shilai_app_api_ticket(appid, ticket_type):
    """获取时来公众号或小程序 ticket

    生产和测试环境共用时来公众号和时来小程序账号，所以 ticket 统一由生产环境提供服务

    Args:
        appid: (string) 时来公众号或小程序 appid
        ticket_type: (string) Ticket 类型，包括 WX_CARD 和 JS_API

    Return:
        (string) ticket
    """
    return _get_api_ticket(appid=appid,
                           ticket_type=ticket_type,
                           service_base_url=SHILAI_APP_TOKEN_SERVICE_BASE_URL)


def _get_access_token(token_type,
                      service_base_url=TOKEN_SERVICE_BASE_URL,
                      appid=None,
                      headers=None):
    """获取token

    Args:
        token_type: (string) token类型，包括时来开放平台、时来开放平台授权方、时来公众号和小程序号
        service_base_url: (string) 服务 Base URL
        appid: (string)
        headers: (Flask.Request.Headers)

    Return:
        (string) access_token
    """
    domain = os.environ.get("ACCESS_TOKEN_SERVICE_DOMAIN", "https://shilai.zhiyi.cn")
    if appid:
        url = f"{domain}/wxaccess_token/{token_type}/{appid}"
    else:
        url = f"{domain}/wxaccess_token/{token_type}"
    resp = http.get(url, headers=headers)
    resp_json = resp.json()
    return resp_json['access_token']


def _get_api_ticket(appid,
                    ticket_type,
                    service_base_url=TOKEN_SERVICE_BASE_URL):
    """获取 API Ticket

    Args:
        appid: (string)
        ticket_type: (string) Ticket 类型，包括 WX_CARD 和 JS_API
        service_base_url: (string) 服务 Base URL

    Return:
        (string) api ticket
    """
    url = '{}/get_wechat_api_ticket/{}/{}'.format(service_base_url, appid, ticket_type)
    resp = http.get(url)
    resp_json = resp.json()
    return resp_json['ticket']

# -*- coding: utf-8 -*-

from contextlib import contextmanager
import inspect
import logging
import time

from redlock import Redlock
from redis.exceptions import RedisError

from cache.redis_client import RedisClient
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


@contextmanager
def redislock(key, ttl, retry_count, retry_delay):
    # redis_server = [{
    #     "host": os.environ.get("REDIS_HOST"),
    #     "port": int(os.environ.get("REDIS_PORT")),
    #     "password": os.environ.get("REDIS_PASSWORD"),
    #     "db": 0
    # }]
    redis_server = [RedisClient().get_connection()]
    retry_delay = retry_delay / 1000
    rlk = Redlock(redis_server, retry_count = retry_count, retry_delay = retry_delay)
    lock = rlk.lock(key, ttl)
    try:
        yield lock
    finally:
        if lock:
            rlk.unlock(lock)


class WalletLock(object):
    def __init__(self, key=None, ttl=None, retry_count=5, retry_delay=200):
        self.key = "{}-wallet".format(key)
        self.ttl = ttl
        self.retry_count = retry_count
        self.retry_delay = retry_delay

    def __call__(self, func):
        def lock(*args, **kargs):
            ret = inspect.signature(func)
            key_args = {}
            for index, arg in enumerate(ret.parameters):
                if arg == 'self':
                    continue
                if index < len(args):
                    key_args[arg] = args[index]
                if kargs.get(arg):
                    key_args[arg] = kargs.get(arg)
            key_args.update(kargs)
            key = self.key.format(**key_args)
            with redislock(key, self.ttl, retry_count=self.retry_count, retry_delay=self.retry_delay) as rlock:
                if not rlock:
                    logger.info("transfer不能获取payer锁: {}".format(key))
                    raise errors.Error(err=error_codes.CANNOT_GET_WALLET_LOCK)
                logger.info("获取用户锁: {}成功".format(key))
                return func(*args, **kargs)
            raise errors.Error(err=error_codes.CANNOT_GET_WALLET_LOCK)
        return lock


class ExpireLock(object):
    def __init__(self, key: str, ttl: int = 1000):
        self.redis = RedisClient().get_connection()
        self._ttl = ttl  # 豪秒
        self._key = key

    def lock(self, nx=True) -> bool:
        try:
            return bool(self.redis.set(self._key, 1, nx=nx, px=self._ttl))
        except RedisError as e:
            logger.error(f"<{self.__class__.__name__}>, key={self._key}, 获取锁失败: {e}")
            return False

    def __enter__(self):
        return self.lock()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def release(self):
        pass


class AtomicDistributedLock(ExpireLock):

    def release(self):
        try:
            if self.locked:
                self.redis.delete(self._key)
        except RedisError as e:
            logger.error(f"<{self.__class__.__name__}>, key={self._key}, 释放锁失败: {e}")

    @property
    def locked(self) -> bool:
        return bool(self.redis.get(self._key) == b"1")

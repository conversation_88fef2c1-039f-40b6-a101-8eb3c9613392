from geopy import distance

from dao.merchant_da_helper import MerchantDataAccessHelper

def get_distance_in_meters(lat1, lng1, lat2, lng2):
    coords_1 = (lat1, lng1)
    coords_2 = (lat2, lng2)
    return int(distance.vincenty(coords_1, coords_2).meters)

def is_within_distance(lat1, lng1, lat2, lng2, distance_limit_in_meters):
    dis = get_distance_in_meters(lat1, lng1, lat2, lng2)
    return dis <= distance_limit_in_meters

def get_merchants_within_distance(lat, lng, merchants, distance_limit_in_meters):
    result = []
    for merchant in merchants:
        if merchant.stores and merchant.stores[0].poi and merchant.stores[0].poi.location:
            store_loc = merchant.stores[0].poi.location
            if is_within_distance(lat, lng, store_loc.latitude, store_loc.longitude,
                distance_limit_in_meters):
                result.append(merchant)
    return result

def get_group_dining_events_within_distance(lat, lng, dining_events, distance_limit_in_meters):
    result = []
    merchant_da = MerchantDataAccessHelper()
    for event in dining_events:
        merchant = merchant_da.get_merchant(event.merchant_id)
        if merchant.stores and merchant.stores[0].poi and merchant.stores[0].poi.location:
            store_loc = merchant.stores[0].poi.location
            if is_within_distance(lat, lng, store_loc.latitude, store_loc.longitude,
                distance_limit_in_meters):
                result.append(event)
    return result

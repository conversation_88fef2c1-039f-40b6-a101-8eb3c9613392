#!/usr/bin/python
# -*- coding: utf-8 -*-

import logging
import sys
import os
import inspect

class Logger:
    '''
    The logger module.

    Usage:

    - First, call `Logger.set_default()` to initialize logger.
    - Second, call 'get_logger()` to get logger.
    - Then you can use: `logger.info(msg)`, `logger.debug(msg)`, `logger.warning(msg)`, `logger.error(msg)`.

    The default logger's level is `INFO`, and output is `stdout`.
    You can set different logger for different modules:

    - Set environment variable `loglevel_modname`, where `modname` is the name of module to be logged.
    - The value of environment variable `loglevel_modname` should be (case insensitive):
      - 'info':logging.INFO,
      - 'warn':logging.WARNING,
      - 'warning':logging.WARNING,
      - 'debug':logging.DEBUG,
      - 'error':logging.ERROR,
      - 'unkown':logging.INFO
    '''
    _formatter = logging.Formatter('%(asctime)s file:%(filename)s lineno:%(lineno)d %(levelname)s: %(message)s') #logging format
    _logger_map = {} # log map: module name -> logger
    _ROOT_NAME = '__main__'
    _mod_map={
      'info':logging.INFO,
      'warn':logging.WARNING,
      'warning':logging.WARNING,
      'debug':logging.DEBUG,
      'error':logging.ERROR,
      'unkown':logging.INFO
    } # log level map: log level string -> log level
    _loglevel_prefix = 'loglevel_' # Environment variable prefix.


    @classmethod
    def set_logger(cls,file_name=None):
      '''
      Initialize the logger.Create the root logger.

      :param file_name: logger file. If is `None`,then use `stdout` .
      '''
      logging.getLogger().handlers = []  # delete all handler
      ### create handler
      if not file_name:
        _handler = logging.StreamHandler(sys.stdout)
        _handler.formatter = cls._formatter
      else:
        ## make sure path exist.
        _dir = os.sep.join(list(file_name.split(os.sep))[:-1])
        if not os.path.exists(_dir):
          os.makedirs(_dir)

        _handler = logging.FileHandler(file_name)
        _handler.setFormatter(cls._formatter)

      ### create logger
      logger = logging.getLogger(cls._ROOT_NAME)  # rootLogger
      logger.setLevel(logging.INFO)
      logger.addHandler(_handler)
      cls._logger_map[cls._ROOT_NAME] =  logger

    @classmethod
    def get_logger(cls):
      '''
      Get logger.

      :return:The logger for current module.
      '''
      ### make sure that the root logger is created.
      if cls._ROOT_NAME not in cls._logger_map:
        cls.set_logger()
      ### Get the module name
      frm = inspect.stack()[1]
      mod = inspect.getmodule(frm[0])
      mod_name = mod.__name__
      env_mod_name = cls._loglevel_prefix+mod_name
      ### Get logger
      if env_mod_name not in os.environ: # root logger
        return cls._logger_map[cls._ROOT_NAME]
      else:
        if mod_name in cls._logger_map:# module logger
          return cls._logger_map[mod_name]
        else:
          logger = logging.getLogger(mod_name)
          logger.setLevel(cls._mod_map.get(os.environ[env_mod_name].lower(), 'unkown'))
          cls._logger_map[mod_name] = logger
          logger.addHandler(cls._logger_map[cls._ROOT_NAME].handlers[0])
          return logger

# mongodb database
from common.config import *
from common.utils import date_utils
from common.client.mongodb_client import MongodbClient
from google.protobuf import json_format
import proto.merchant_rules_pb2 as merchant_rules_pb2
import proto.wechat_member_card_pb2 as wechat_member_card_pb2
import proto.staff_pb2 as staff_pb2
import proto.user_pb2 as user_pb2
import proto.authorizer_pb2 as authorizer_pb2
import proto.membership_pb2 as membership_pb2
import proto.coupons_pb2 as coupons_pb2

databases_name = 'shilai'

merchant_assistant_user_login_collection_name = 'log_user_login'
merchant_assistant_user_info_save_collection_name = 'log_user_info_save'

#
staff_collection_name = 'shilai_staff'

# 商户信息
merchant_collection_name = 'merchant'

# Ticket
# ComponentVerifyTicket
component_verify_ticket_collection_name = 'component_verify_ticket'

# 开发者 token
component_access_token_collection_name = 'component_access_token'
# 授权者 token
authorizer_access_token_collection_name = 'authorizer_access_token'

# 授权者 小程序信息
authorizer_info_collection_name = 'authorizer_info'

# 微信用户信息
wechat_user_collection_name = 'wechat_user'

# 优惠券
coupon_category_collection_name = 'coupon_category'
# 用户领取优惠券相关信息
coupon_collection_name = 'coupon'

# 会员卡相关表
# 用户领取的会员卡信息
member_card_collection_name = 'member_card'

# 门店创建的会员卡表信息
member_card_category_collection_name = 'member_card_category'
# 根据activate_ticket获取到用户填写的信息
member_card_activate_ticket_info_collection_name = 'member_card_activate_ticket_info'
# 根据CardID和Code查询会员信息
member_card_user_info_collection_name = 'member_card_user_info'
# 一键开卡 设置用户激活时填写的选项信息
member_card_activate_user_form_collection_name = 'member_card_activate_user_form'

# 二维码优惠券
qrcode_info_collection_name = 'qrcode_info'


# 分配给会员的单张会员卡


class DbUtils(object):
    def __init__(self):
        # try:
        #     db_conf = config.UTILS_MONGODB
        #     host = db_conf['host']
        #     port = db_conf['port']
        #     username = db_conf['username']
        #     password = db_conf['password']
        # except AttributeError:
        #     host = config.MONGODB_HOST
        #     port = config.MONGODB_PORT
        #     username = config.MONGODB_USERNAME
        #     password = config.MONGODB_PASSWORD
        # self.db_client = MongodbClient(host, port, username, password)
        self.db_client = None

    # 添加员工信息
    def set_staff(self, shilai_staff):
        print(shilai_staff)
        json_obj = json_format.MessageToDict(shilai_staff,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)
        data_filter = {'id': shilai_staff.id}
        self.db_client.set_one(databases_name,
                               staff_collection_name,
                               json_obj,
                               data_filter)

    # 获取员工信息
    def get_staff(self, user_id):
        data_filter = {'userId': user_id}
        cursor = self.db_client.find_one(databases_name,
                                         staff_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, staff_pb2.ShilaiStaff(),
                                         ignore_unknown_fields=True)
        return None

    # 获取员工信息
    def delete_staff(self, staff_id):
        data_filter = {'id': staff_id}
        cursor = self.db_client.find_one(databases_name,
                                         staff_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, staff_pb2.ShilaiStaff(),
                                         ignore_unknown_fields=True)
        return None

    # 添加商户信息
    def set_merchant(self, merchant_info):
        data_filter = {"id": merchant_info.id}
        json_obj = json_format.MessageToDict(merchant_info,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)
        self.db_client.set_one(databases_name,
                               merchant_collection_name,
                               json_obj, data_filter)

    # 查询商户信息
    def get_merchant(self, merchant_id=None, userid=None):
        if userid:
            data_filter = {"managerList": {
                "$elemMatch": {'userId': userid},
            }}
            print(data_filter)
            cursors = self.db_client.find(databases_name,
                                          merchant_collection_name,
                                          data_filter)
            merchant_list = []
            for cursor in cursors:
                merchant = json_format.ParseDict(cursor, merchant_rules_pb2.Merchant(),
                                                 ignore_unknown_fields=True)
                merchant_list.append(merchant)
            return merchant_list
        else:
            data_filter = {"id": merchant_id}

            cursor = self.db_client.find_one(databases_name,
                                             merchant_collection_name,
                                             data_filter)
            if cursor:
                return json_format.ParseDict(cursor, merchant_rules_pb2.Merchant(),
                                             ignore_unknown_fields=True)
        return None

    # 获取商户与员工对应关系
    def get_staff_merchant_list(self, staff_id):
        # data_filter = {"bindingStaffId": staff_id}
        data_filter = {"id": '50a59ea7b57b48308c8365ca6d9268e9'}
        cursors = self.db_client.find(databases_name,
                                      merchant_collection_name,
                                      data_filter)
        merchant_list = []
        for cursor in cursors:
            merchant = json_format.ParseDict(cursor, merchant_rules_pb2.Merchant(),
                                             ignore_unknown_fields=True)
            merchant_list.append(merchant)

        return merchant_list

    # 设置认证的token
    def set_authorizer_access_token(self, authorizer_token):
        json_obj = json_format.MessageToDict(authorizer_token,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)
        filter = {'appid': authorizer_token.appid}

        self.db_client.set_one(databases_name,
                               authorizer_access_token_collection_name,
                               json_obj, filter)

    # 获取认证的token
    def get_authorizer_access_token(self, appid=None):
        # AuthorizerToken
        if appid:
            query_cmd = {"appid": appid}
            cursor = self.db_client.find_one(databases_name,
                                             authorizer_access_token_collection_name,
                                             query_cmd)
            if cursor:
                return json_format.ParseDict(cursor, authorizer_pb2.AuthorizerToken(),
                                             ignore_unknown_fields=True)
        else:
            cursors = self.db_client.find(databases_name,
                                          authorizer_access_token_collection_name,
                                          {})
            if cursors:
                authorizer_token_list = []
                for cursor in cursors:
                    # print()
                    authorizer_token = json_format.ParseDict(cursor, authorizer_pb2.AuthorizerToken(),
                                                             ignore_unknown_fields=True)
                    authorizer_token_list.append(authorizer_token)
                return authorizer_token_list

        return None

    # 设置商户授权者小程序或者公众号信息
    def set_authorizer_info(self, authorize):
        data_filter = {"merchantId": authorize.merchant_id}
        json_obj = json_format.MessageToDict(authorize,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)
        self.db_client.set_one(databases_name,
                               authorizer_info_collection_name,
                               json_obj, data_filter)

    # 获取商户授权者小程序或者公众号信息
    def get_authorizer_info(self, id):
        """

        :param id:
        :return:
        """
        data_filter = {"merchantId": id}
        cursor = self.db_client.find_one(databases_name,
                                         authorizer_info_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, authorizer_pb2.Authorizer(),
                                         ignore_unknown_fields=True)
        return None

    # 设置会员卡信息
    def set_member_card_category(self, member_card_category):
        data_filter = {"id": member_card_category.id}
        json_obj = json_format.MessageToDict(member_card_category,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.set_one(databases_name,
                               member_card_category_collection_name,
                               json_obj, data_filter)

    # 获取会员卡信息
    def get_member_card_category(self, card_id=None, merchant_id=None):
        if card_id:
            data_filter = {"id": card_id}

            cursor = self.db_client.find_one(databases_name,
                                             member_card_category_collection_name,
                                             data_filter)
            if cursor:
                return json_format.ParseDict(cursor, membership_pb2.MemberCardCategory(),
                                             ignore_unknown_fields=True)
        elif merchant_id:
            data_filter = {"merchantId": merchant_id}

            cursors = self.db_client.find(databases_name,
                                          member_card_category_collection_name,
                                          data_filter)
            if cursors:
                member_card_category_list = []
                for cursor in cursors:
                    member_card_category = json_format.ParseDict(cursor, membership_pb2.MemberCardCategory(),
                                                                 ignore_unknown_fields=True)
                    member_card_category_list.append(member_card_category)
                return member_card_category_list

        return None

    # 设置根据activate_ticket获取到用户填写的信息
    def set_member_card_activate_ticket_info(self, activate_ticket_info):
        data_filter = {"code": activate_ticket_info.code}
        json_obj = json_format.MessageToDict(activate_ticket_info,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.insert_one(databases_name,
                                  member_card_activate_ticket_info_collection_name,
                                  json_obj, data_filter)

    # 获取根据activate_ticket获取到用户填写的信息
    def get_member_card_activate_ticket_info(self, code):
        data_filter = {"code": code}
        cursor = self.db_client.find_one(databases_name,
                                         member_card_activate_ticket_info_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, membership_pb2.MemberCardActivateTempInfo(),
                                         ignore_unknown_fields=True)
        return None

    # 根据CardID和Code查询会员信息
    def set_member_card_user_info(self, user_info):
        data_filter = {"code": user_info.code}
        json_obj = json_format.MessageToDict(user_info,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.insert_one(databases_name,
                                  member_card_user_info_collection_name,
                                  json_obj, data_filter)

    # 根据CardID和Code查询会员信息
    def get_member_card_user_info(self, code):
        data_filter = {"code": code}
        cursor = self.db_client.find_one(databases_name,
                                         member_card_user_info_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, wechat_member_card_pb2.MemberCardUserInfo(),
                                         ignore_unknown_fields=True)
        return None

    # 一键开卡 设置用户激活时填写的选项信息
    def set_member_card_activate_user_form(self, activate_user_form):
        data_filter = {"cardId": activate_user_form.card_id}
        json_obj = json_format.MessageToDict(activate_user_form,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.insert_one(databases_name,
                                  member_card_activate_user_form_collection_name,
                                  json_obj, data_filter)

    # 一键开卡 设置用户激活时填写的选项信息
    def get_member_card_activate_user_form(self, card_id):
        data_filter = {"cardId": card_id}
        cursor = self.db_client.find_one(databases_name,
                                         member_card_activate_user_form_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, wechat_member_card_pb2.MemberCardActivateUserForm(),
                                         ignore_unknown_fields=True)
        return None

    # # 设置微信用户信息
    # def set_wechat_user(self, user_info, unionid=None):
    #     if unionid:
    #         data_filter = {"unionid": unionid}
    #     else:
    #         data_filter = {"id": user_info.id}
    #     json_obj = json_format.MessageToDict(user_info, including_default_value_fields=True)
    #
    #     self.db_client.set_one(databases_name,
    #                            wechat_user_collection_name,
    #                            json_obj, data_filter, column={})
    #
    # # 获取微信用户信息
    # def get_wechat_user(self, user_id=None, unionid=None):
    #     if user_id:
    #         data_filter = {"id": user_id}
    #     else:
    #         data_filter = {"unionid": unionid}
    #     cursor = self.db_client.find_one(databases_name,
    #                               wechat_user_collection_name,
    #                               data_filter)
    #     if cursor:
    #         return json_format.ParseDict(cursor, user_pb2.User(),
    #                                      ignore_unknown_fields=True)
    #     return None

    # 设置微信用户信息
    def set_wechat_user(self, user_info):
        data_filter = {"id": user_info.id}

        json_obj = json_format.MessageToDict(user_info,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.set_one(databases_name,
                               wechat_user_collection_name,
                               json_obj, data_filter, column={})

    # 获取微信用户信息
    def get_wechat_user(self, id=None, openid=None):
        if openid:
            data_filter = {"wechatProfile.openid": openid}
        else:
            data_filter = {"id": id}

        cursor = self.db_client.find_one(databases_name,
                                         wechat_user_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, user_pb2.User(),
                                         ignore_unknown_fields=True)
        return None

    # 获取微信用户信息
    def get_all_user_openid(self, merchant_id):
        data_filter = {"merchantId": merchant_id}
        cursor = self.db_client.find(databases_name,
                                     wechat_user_collection_name,
                                     data_filter)
        if cursor:
            return json_format.ParseDict(cursor, user_pb2.User(),
                                         ignore_unknown_fields=True)
        return None

    # 设置 优惠券信息
    def set_coupon_category(self, coupon_class):
        data_filter = {"id": coupon_class.id}

        json_obj = json_format.MessageToDict(coupon_class,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.set_one(databases_name,
                               coupon_category_collection_name,
                               json_obj, data_filter, column={})

    # 获取 优惠券信息
    def get_coupon_class(self, id):
        data_filter = {"id": id}

        cursor = self.db_client.find_one(databases_name,
                                         coupon_category_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, coupons_pb2.CouponCategory(),
                                         ignore_unknown_fields=True)
        return None

    # 保存 qrcode 的信息
    def set_qrcode_info(self, qrcode_id, staff_id, merchant_id):
        data_filter = {
            "id": qrcode_id,
        }

        json_obj = {
            "id": qrcode_id,
            "staffId": staff_id,
            "merchantId": merchant_id,
            "expiresIn" : date_utils.timestamp_second(),
            "status" : True
        }
        print(json_obj)
        self.db_client.set_one(databases_name,
                               qrcode_info_collection_name,
                               json_obj, data_filter)

    # 获取 qrcode 的信息
    def get_qrcode_info(self, id=None, merchant_id=None):
        data_filter = {"id": id}
        if merchant_id:
            data_filter = {"merchantId": merchant_id}

        cursor = self.db_client.find_one(databases_name,
                                         qrcode_info_collection_name,
                                         data_filter)
        if cursor:
            cursor['status'] = False
            self.db_client.set_one(databases_name,
                                   qrcode_info_collection_name,
                                   cursor, data_filter)
            return cursor
        return None

    # 设置分配给会员的单张优惠券信息
    def set_coupon(self, coupon):

        data_filter = {"id": id}
        json_obj = json_format.MessageToDict(coupon,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.set_one(databases_name,
                               coupon_collection_name,
                               json_obj, data_filter, column={})

    # 获取分配给会员的单张优惠券信息
    def get_coupon(self, id, user_id, merchant_id, coupon_category_id):
        if id:
            data_filter = {"id": id}
        elif user_id:
            data_filter = {"userId": user_id}
        elif merchant_id:
            data_filter = {"merchantId": merchant_id}
        elif coupon_category_id:
            data_filter = {"coupon_category_id": coupon_category_id}

        cursor = self.db_client.find_one(databases_name,
                                         coupon_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, coupons_pb2.Coupon(),
                                         ignore_unknown_fields=True)
        return None

    # 设置会员的领取的会员卡信息
    def set_member_card(self, member_card):
        data_filter = {"id": member_card.id}
        json_obj = json_format.MessageToDict(member_card,
                                             including_default_value_fields=True,
                                             use_integers_for_enums=True)

        self.db_client.set_one(databases_name,
                               member_card_collection_name,
                               json_obj, data_filter)

    # 获取会员的领取的会员卡信息
    def get_member_card(self, id, user_id, merchant_id, card_category_id):
        if id:
            data_filter = {"id": id}
        elif user_id:
            data_filter = {"userId": user_id}
        elif merchant_id:
            data_filter = {"merchantId": merchant_id}
        elif card_category_id:
            data_filter = {"cardCategoryId": card_category_id}

        cursor = self.db_client.find_one(databases_name,
                                         member_card_collection_name,
                                         data_filter)
        if cursor:
            return json_format.ParseDict(cursor, membership_pb2.MemberCard(),
                                         ignore_unknown_fields=True)
        return None

    # 设置保存Ticket
    def set_component_verify_ticket(self, appid, component_verify_ticket):
        data_filter = {"appid": appid}
        json_obj = {
            "appid": appid,
            "componentVerifyTicket": component_verify_ticket
        }

        self.db_client.set_one(databases_name,
                               component_verify_ticket_collection_name,
                               json_obj, data_filter)

    # 获取会员Ticket
    def get_component_verify_ticket(self, appid):
        data_filter = {"appid": appid}

        cursor = self.db_client.find_one(databases_name,
                                         component_verify_ticket_collection_name,
                                         data_filter)
        if cursor:
            return cursor["componentVerifyTicket"]
        return None

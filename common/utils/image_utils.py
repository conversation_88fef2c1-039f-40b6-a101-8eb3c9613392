import os
from PIL import Image, ImageDraw, ImageFont
from business_ops import constants
from common.utils import file_access_helper

def generate_logo_picture(text, filename):
    '''为没有logo的用户生成标准文字logo图片，例如八品道牛肉火锅、智易科技有限公司

    Args:
        text:用户简称，例如八品道、智易
        filename:保存图片的名称，不要出现中文，建议名称首位大写字母，如BPD

    Returns:
        savepath:文件保存的路径
    '''
    # 字体格式
    fontpath = os.path.join(constants.CHARACTERS_STYLE_DIR, 'msyhbd.ttf')
    file_access_helper.ensure_directory_exists_for_filename(fontpath)
    font_size = 40

    font = ImageFont.truetype(fontpath, font_size)
    font_size = font.getsize(text)
    length = max(font_size) + 6

    # 生成背景图
    logo = Image.new("RGB", (length, length), '#F2A046')
    img_size = logo.size

    # 调整文字在图片的位置
    while font_size[0] > img_size[0] and font_size[1] > img_size[1]:
        font_size -= 5
        font = ImageFont.truetype(fontpath, font_size)
        font_size = font.getsize(text)
    x = (img_size[0] - font_size[0]) / 2
    y = (img_size[1] - font_size[1]) / 2
    draw = ImageDraw.Draw(logo)
    draw.text((x, y), text, (255, 255, 255), font=font)
    savepath = constants.LOGO_IMAGE_SAVE_DIR+'/{}.png'.format(filename)
    file_access_helper.ensure_directory_exists_for_filename(savepath)
    logo.save(savepath)
    return savepath
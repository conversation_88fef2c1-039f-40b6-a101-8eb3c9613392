# -*- coding: utf-8 -*-

import hashlib
import random
import uuid

def generate_message_id(sender_id, receiver_id):
  sha1 = hashlib.sha1()
  if sender_id > receiver_id:
    seed = "%s%s" % (sender_id, receiver_id)
  else:
    seed = "%s%s" % (receiver_id, sender_id)
  sha1.update(seed.encode('utf8'))
  return sha1.hexdigest()

def generate_common_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_common_uuid():
  return str(uuid.uuid4())

def generate_user_id():
    return str(uuid.uuid4())

def generate_merchant_id():
    return str(uuid.uuid4())

def generate_staff_id():
    return str(uuid.uuid4())

def generate_feedback_id():
    return str(uuid.uuid4())

def generate_qrcode_id(content=None):
    # 若二维码内容未提供，则默认生成新uuid
    if not content:
      return str(uuid.uuid4()).replace('-', '')
    else: # 若提供二维码内容，则根据内容生成哈希值作为ID
      return hashlib.md5(content.encode(encoding='UTF-8')).hexdigest()

def generate_nonce_str(length):
    remained_len = length
    result = ''
    while remained_len > 0:
        tmp_str = str(uuid.uuid4()).replace('-', '')
        if remained_len < len(tmp_str):
            tmp_str = tmp_str[0:remained_len]
        result += tmp_str
        remained_len -= len(tmp_str)

    return result

def generate_order_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_coupon_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_coupon_category_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_coupon_strategy_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_group_dining_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_red_packet_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_transaction_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_card_id():
  return str(uuid.uuid4()).replace('-', '')

def generate_number_string(digit=6):
  start = 10 ** (digit - 1)
  end = 10 ** digit
  code = str(random.randint(start, end))
  return code

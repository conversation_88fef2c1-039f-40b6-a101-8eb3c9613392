#!/usr/bin/python
# -*- coding: utf-8 -*-
import time
import logging

from flask import json

import common.utils.date_utils as date_utils
logger = logging.getLogger(__name__)


class LogUtils():
    def __init__(self, request):
        self.path = request.path

        self.method = request.method
        if self.method == 'POST':
            self.request_value = request.json if hasattr(request, 'json') else ''
        elif self.method == 'GET':
            self.request_value = request.values
        else:
            self.request_value = ''

        self.info_api_request(request)

    def print_log(self, type, value):
        log_info = "[{date}] : [{method}] : [{path}] : [{type}] : [{value}]". \
            format(method=self.method,
                   path=self.path,
                   type=type,
                   value=value)
        logger.info(log_info)

    def info_api_request(self, request):
        # print("\n***************************************")
        logger.info(f"[{self.method}] : [{self.path}] : [request] : [{'request': request, 'request_value': self.request_value, 'user_id': request.headers.get('userId')}]")
        # self.print_log('request', request)
        # self.print_log('request', self.request_value)
        # self.print_log('request:userId', request.headers.get("userId"))
        

    def info_api_responses(self, responses):

        # 减化返回日志输出
        # self.print_log('responses', json.loads(responses.get_data()))
        resp_data = json.loads(responses.get_data())
        value = {
            "errcode": resp_data.get("errcode"),
            "errmsg": resp_data.get("errmsg")
        }
        self.print_log('responses', value)
        # print("***************************************\n")

    def info(self, value):
        self.print_log('info', value)


def timeit(func):
    """
    How much time the function costs
    """
    def timed(*args, **kw):
        # ts = time.time()
        result = func(*args, **kw)
        # te = time.time()
        # logger.info(">" * 10)
        # logger.info('%r  %6.8f seconds' % (func.__name__, te - ts))
        # logger.info("<" * 10)
        return result
    return timed

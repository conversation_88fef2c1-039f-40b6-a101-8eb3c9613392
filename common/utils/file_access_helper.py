import os

def is_path_exists(path):
  return os.path.exists(path)

def ensure_directory_exists(dir):
  if not os.path.exists(dir):
    os.makedirs(dir)

def ensure_directory_exists_for_filename(filename):
  ensure_directory_exists(os.path.dirname(filename))

def write_file(filename, content):
    dir = os.path.dirname(filename)
    ensure_directory_exists(dir)
    with open(filename, 'wb') as file:
        file.write(content)

def read_file(filename):
    if not is_path_exists(filename):
        return None

    with open(filename, 'rb') as file:
        return file.read()

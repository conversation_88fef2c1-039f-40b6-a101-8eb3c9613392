# coding:utf8
import os
import warnings


class DefaultConfig(object):
    def get_config_value(self, config_name):
        if config_name in os.environ:
            return os.environ[config_name]
        else:
            return None

    # SERVICE_HOST = "**************"
    SERVICE_HOST = "**********"
    # 商户认证过程服务的端口
    AUTHORIZATION_PROCESS_SERVICE_PORT = 6001

    # 商户助手服务的端口
    MERCHANT_ASSISTANT_SERVICE_PORT = 6002

    # 商户助手服务的端口
    MEMBER_CARD_MANAGER_SERVICE_PORT = 6003

    # 商户助手服务的端口
    MERCHANT_ANALYSIS_SERVICE_PORT = 6004

    # 获取开发者TOKEN服务的url
    # GET_TOKEN_URL = 'http://{}:{}/get_token'.format(SERVICE_HOST, AUTHORIZATION_PROCESS_SERVICE_PORT)

    # MONGODB数据库相关信息
    # MONGODB_HOST = '*************'
    MONGODB_HOST = '********'
    # MONGODB_PORT = 32006
    MONGODB_PORT = 27017
    MONGODB_USERNAME = 'root'
    MONGODB_PASSWORD = 'zhiyi#mongodb#go!'

    # 商户端助手
    WECHAT_MINIPROGRAM_MERCHANT_APPID = 'wxb61d678466685a5a'
    WECHAT_MINIPROGRAM_MERCHANT_SECRET = 'e10f0cc827af5457c925da10afb325d9'

    # 业务助手
    WECHAT_MINIPROGRAM_STAFF_APPID = 'wxb481ce8d016fb2f3'
    WECHAT_MINIPROGRAM_STAFF_SECRET = '128ab4ce6e5a7cb7ff175c30afdc26a2'

    # C端小程序
    WECHAT_MINIPROGRAM_OLD_APPID = 'wxdd5cafec95f6cc46'  # 旧的
    WECHAT_MINIPROGRAM_APPID = 'wxaa3c47ef72452be7'  # 新的
    # WECHAT_MINIPROGRAM_SECRET = '95b044a926269962950d6084a49abd0c'
    WECHAT_MINIPROGRAM_SECRET = '8a478e5403c3d002dec32ca31abe889a'
    WECHAT_MINIPROGRAM_ORIGIN_ID = 'gh_369e154cc7ba'

    WECHAT_MINIPROGRAM_SHIYI_CAT_APPID = "wx4e3c563e105ae828"
    WECHAT_MINIPROGRAM_SHIYI_CAT_SECRET = "579250969b3cd3ddacb4a05f39330a2c"

    # 时来开放平台
    WECHAT_APP_ID = 'wx9b0a448bd837194c'
    WECHAT_APP_SECRET = '4ff28f100c4ab3896b3bd98de215402c'
    # 时来服务商号
    WECHAT_MERCHANT_ID = '1510708561'
    WECHAT_MERCHANT_KEY = '34861849ce3144fd82d804a9783ef99e'

    # 时来公众号APPID
    SHILAI_MP_APPID = 'wx56a5980f33bee8f9'
    # 时来公众号AppSecret
    SHILAI_MP_APP_SECRET = '4f3483f03a00f5e5592e458a07c7bed3'

    # 智易公众号APPID
    ZHIYI_MP_APPID = 'wx4f066f6e59f37cc9'

    # 获取平台component_verify_ticket
    WECHAT_MESSAGE_VERIFY_TOKEN = '9NO3h9CZ7v2Tba3fyuISeuhDZQoJ2pOW'
    WECHAT_MESSAGE_ENCODED_SYMMETRIC_KEY = 'CGlpqsDfvlZeW4nz2olvXwJiI91Rn4BSsqsx4qql8jO'

    # 腾讯位置服务 API KEY
    TENCENT_LBS_API_KEY = 'PUPBZ-W74WX-IRK4R-TODRO-XZ3OJ-F4BSI'

    # 时来在微信支付服务商下子商户
    SHILAI_SETTLEMENT_ACCOUNT = '**********'

    PLATFORM_USER = "user"
    PLATFORM_MERCHANT = "merchant"
    PLATFORM_SHILAI_STAFF = "shilai-staff"

    # request.header['from-platform'] = merchant | | shilai - staff | | user
    def _parse(self, kwargs):
        """
        根据字典kwargs 更新 config参数
        """
        for k, v in kwargs.items():
            if not hasattr(self, k):
                warnings.warn("Warning: opt has not attribut %s" % k)
            setattr(self, k, v)

        print('user config:')
        for k, v in self.__class__.__dict__.items():
            if not k.startswith('_'):
                print(k, getattr(self, k))


from common.load_conf import config

if config is None:
    config = DefaultConfig()

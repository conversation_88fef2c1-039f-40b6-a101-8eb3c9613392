# -*- encoding: utf-8 -*-
'''
@time        :2024/05/11 21:36:02
'''
import re
from typing import List, Dict, Any, Union


def to_camel(snake_name):
    tmp = snake_name.split("_")
    if len(tmp) > 1:
        return "".join((
            tmp[0].lower(),
            *list(map(lambda x: x.capitalize(), tmp[1:]))
        ))
    return snake_name


def to_snake(camel_name):
    camel_name = camel_name.replace("-", "")
    snake_name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_name)
    snake_name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', snake_name)
    return snake_name.lower()


def to_full(name):
    return to_snake(name).replace("_", "")


def to_bar(name):
    return to_snake(name).replace("_", "-")


def recursive_item(item, callback):
    if isinstance(item, dict):
        return callback(item)
    elif isinstance(item, list):
        return [recursive_item(row, callback) for row in item]
    return item


class SnakeDict(dict):
    def __init__(self, *args, **kwargs):
        super().__init__()
        self.update(*args, **kwargs)

    def __getattr__(self, key):
        try:
            return self[key]
        except KeyError:
            raise AttributeError(f"No such attribute: {key}")

    def __setattr__(self, key, value):
        if key.startswith('_'):
            super().__setattr__(key, value)
        else:
            self[key] = value

    def __delattr__(self, key):
        try:
            del self[key]
        except KeyError:
            raise AttributeError(f"No such attribute: {key}")

    def __getitem__(self, key):
        snake_key = to_snake(key)
        return super().__getitem__(snake_key)

    def __setitem__(self, key, value):
        super().__setitem__(
            to_snake(key),
            recursive_item(value, SnakeDict)
        )

    def __delitem__(self, key):
        snake_key = to_snake(key)
        super().__delitem__(snake_key)

    def update(self, *args, **kwargs):
        for k, v in dict(*args, **kwargs).items():
            self[k] = v


class CamelDict(dict):
    def __init__(self, *args, **kwargs):
        super().__init__()
        self.update(*args, **kwargs)

    def __getattr__(self, key):
        try:
            return self[key]
        except KeyError:
            raise AttributeError(f"No such attribute: {key}")

    def __setattr__(self, key, value):
        if key.startswith('_'):
            super().__setattr__(key, value)
        else:
            self[key] = value

    def __delattr__(self, key):
        try:
            del self[key]
        except KeyError:
            raise AttributeError(f"No such attribute: {key}")
        
    def __getitem__(self, key):
        camel_key = to_camel(key)
        return super().__getitem__(camel_key)

    def __setitem__(self, key, value):
        super().__setitem__(
            to_camel(key),
            recursive_item(value, CamelDict)
        )

    def __delitem__(self, key):
        camel_key = to_camel(key)
        super().__delitem__(camel_key)

    def update(self, *args, **kwargs):
        for k, v in dict(*args, **kwargs).items():
            self[k] = v


def AutoSnake(item: Union[Dict, List[Dict], Any]) -> Any:
    return recursive_item(item, SnakeDict)


def AutoCamel(item: Union[Dict, List[Dict], Any]) -> Any:
    return recursive_item(item, CamelDict)


if __name__ == "__main__":
    tmp = {
        "a_b": '11',
        "aName": '22',
        "A_kk": 33,
        "A_ll": 44
    }
    item = AutoCamel({"merchantId": 'xxx', "printerSn": 'xxx'})
    print(item)
    # item = CamelDict(tmp)
    # print(item)
    # item = SnakeDict(tmp)
    # print(item)
    # item = AutoSnake([tmp])
    # print(item)
    # item = AutoCamel([tmp])
    # print(item)
    # item = AutoSnake(tmp)
    # print(item)
    # item = AutoCamel(tmp)
    # print(item)
    # item = AutoCamel("asdfa_sdfGsdfgs")
    # print(item)

    # print(to_snake("asdfasf_asdf"))
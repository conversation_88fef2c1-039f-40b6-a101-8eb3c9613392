# -*- coding: utf-8 -*-

import base64
import os
import io
from collections import namedtuple

import oss2
import requests
from urllib.parse import urlparse
from PIL import Image

from common.constant import const


class AliyunOSSHelper:
    def __init__(self, bucket_name=None):
        self.access_key = const.ALIYUN_OSS_ACCESS_KEY
        self.access_key_secret = const.ALIYUN_OSS_ACCESS_KEY_SECRET
        self.access_domain = const.ALIYUN_OSS_SHILAI_DOMAIN
        self.upload_url = const.ALIYUN_OSS_UPLOAD_URL
        self.bucket_name = bucket_name
        self.region = const.ALIYUN_OSS_SHILAI_REGION
        if bucket_name is None:
            self.bucket_name = const.ALIYUN_OSS_DISH_IMAGE_BUCKET
        auth = oss2.AuthV4(self.access_key, self.access_key_secret)
        self.bucket = oss2.Bucket(auth, self.upload_url, self.bucket_name, region=self.region)
        self.ImageObj = namedtuple("ImageObj", ["url", "name"])

    def upload_image_network_stream(self, image_url, prefix=None, name=None):
        """上传网络流图片"""
        ImageObj = namedtuple("ImageObj", ["url", "name", "width", "height", "size"])
        if image_url == "":
            return None
        o = urlparse(image_url)
        if name is None:
            name = o.path.split("/")[-1]
        if prefix is not None:
            name = "{}/{}".format(prefix, name)
        input = requests.get(image_url)
        binary = io.BytesIO(input.content)
        with Image.open(binary) as im:
            width = im.size[0]
            height = im.size[1]
            size = binary.getbuffer().nbytes
            self.bucket.put_object(name, input)
            url = "{}{}".format(self.access_domain, name)
            return ImageObj(url, name, width, height, size)

    def convert_url_to_image_obj(self, image_url):
        ImageObj = namedtuple("ImageObj", ["url", "name", "width", "height", "size"])
        o = urlparse(image_url)
        name = o.path.split("/")[-1]
        input = requests.get(image_url)
        binary = io.BytesIO(input.content)
        with Image.open(binary) as im:
            width = im.size[0]
            height = im.size[1]
            size = binary.getbuffer().nbytes
            return ImageObj(image_url, name, width, height, size)

    def upload_image_local(self, filepath):
        """上传本地文件"""
        filename = os.path.split(filepath)[-1]
        self.bucket.put_object_from_file(filename, filepath)
        url = "{}{}".format(self.access_domain, filename)
        return self.ImageObj(url, filename)

    def upload_image_binary(self, image, name, directory=None):
        if directory is not None:
            name = "{}/{}".format(directory, name)
        self.bucket.put_object(name, image)
        url = "{}{}".format(self.access_domain, name)
        return self.ImageObj(url, name)

    def generate_image_url(self, name, directory=None):
        if directory is not None:
            name = "{}/{}".format(directory, name)
        return "{}{}".format(self.access_domain, name)

    def crop(self, src_name, target_name, w=100, h=100, x=100, y=100, r=1):
        # 裁剪
        style = 'image/crop,w_{},h_{},x_{},y_{},r_{}'.format(w, h, x, y, r)
        process = "{0}|sys/saveas,o_{1},b_{2}".format(
            style,
            oss2.compat.to_string(base64.urlsafe_b64encode(oss2.compat.to_bytes(target_name))),
            oss2.compat.to_string(base64.urlsafe_b64encode(oss2.compat.to_bytes(self.bucket_name))),
        )
        self.bucket.process_object(src_name, process)
        url = "{}{}".format(self.access_domain, target_name)
        return self.ImageObj(url, target_name)

    def resize(self, src_name, target_name, model="m_fixed", w=70, h=70):
        """aliyun oss 图片缩放的说明
        https://help.aliyun.com/document_detail/44688.html?spm=a2c4g.11186623.2.15.5db079abtcxOcq#concept-hxj-c4n-vdb
        """
        style = 'image/resize,{},w_{},h_{}'.format(model, w, h)
        process = "{0}|sys/saveas,o_{1},b_{2}".format(
            style,
            oss2.compat.to_string(base64.urlsafe_b64encode(oss2.compat.to_bytes(target_name))),
            oss2.compat.to_string(base64.urlsafe_b64encode(oss2.compat.to_bytes(self.bucket_name))),
        )
        self.bucket.process_object(src_name, process)
        url = "{}{}".format(self.access_domain, target_name)
        return self.ImageObj(url, target_name)

    def try_to_resize(self, image_obj, model="m_fixed", w=70, h=70):
        size_obj = self.resize_strategy(image_obj)
        if not size_obj:
            return None
        return self.resize(image_obj.name, image_obj.name, model=model, w=size_obj.width, h=size_obj.height)

    def resize_strategy(self, image_obj):
        Size = namedtuple("Size", ["width", "height"])
        ONE_M = 1024 * 1024
        if image_obj.size <= ONE_M:
            return None
        rate = float(ONE_M) / image_obj.size
        width = int(image_obj.width * rate)
        if width > 4096:
            width = 4096
        height = int(image_obj.height * rate)
        if height > 4096:
            height = 4096
        return Size(width, height)

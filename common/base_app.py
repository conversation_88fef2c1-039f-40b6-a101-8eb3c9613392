# -*- encoding: utf-8 -*-
'''
@time        :2024/06/29 11:16:34
'''
import logging
import time
import os
import sys
import json
import inspect
import functools
from typing import Dict, Optional, List

import psutil
from flask import request, g, current_app, Blueprint, Flask as _Flask
from flask.json import dumps
from flask._compat import text_type, reraise
from werkzeug.datastructures import Headers
from werkzeug.wrappers import BaseResponse

from service import errors, error_codes
from common.schema import AutoCamel
from common.utils import id_manager
from common.request import get_headers
from service import errors
from service import error_codes
from service import base_responses
from common.logger.syslog_util import Logger


logger = logging.getLogger(__name__)
syslogger = Logger()


def _jsonify(
    data=None,
    code=error_codes.SUCCESS,
    msg=None,
):
    content = {'errcode': code, 'errmsg': msg or error_codes.SUCCESS_MSG, 'data': data}
    json_data = dumps(content, ensure_ascii=False)
    return current_app.response_class(
        json_data + "\n",
        mimetype="application/json; charset=utf-8",
    )


class SuperFlask(_Flask):
    def make_response(self, rv):
        status = headers = None
        if isinstance(rv, tuple):
            len_rv = len(rv)
            if len_rv == 3:
                rv, status, headers = rv
            elif len_rv == 2:
                if isinstance(rv[1], (Headers, dict, tuple, list)):
                    rv, headers = rv
                else:
                    rv, status = rv
            else:
                raise TypeError(
                    "The view function did not return a valid response tuple."
                    " The tuple must have the form (body, status, headers),"
                    " (body, status), or (body, headers)."
                )

        if rv is None:
            rv = _jsonify()

        if not isinstance(rv, self.response_class):
            if isinstance(rv, set):
                rv = list(rv)
            if isinstance(rv, (text_type, bytes, bytearray)):
                rv = self.response_class(rv, status=status, headers=headers)
                status = headers = None
            elif isinstance(rv, (int, str, list, bool, dict)):
                rv = _jsonify(AutoCamel(rv))
            elif isinstance(rv, BaseResponse) or callable(rv):
                try:
                    rv = self.response_class.force_type(rv, request.environ)
                except TypeError as e:
                    new_error = TypeError(
                        "{e}\nThe view function did not return a valid"
                        " response. The return type must be a string, dict, tuple,"
                        " Response instance, or WSGI callable, but it was a"
                        " {rv.__class__.__name__}.".format(e=e, rv=rv)
                    )
                    reraise(TypeError, new_error, sys.exc_info()[2])
            else:
                raise TypeError(
                    "The view function did not return a valid"
                    " response. The return type must be a string, dict, tuple,"
                    " Response instance, or WSGI callable, but it was a"
                    " {rv.__class__.__name__}.".format(rv=rv)
                )

        if status is not None:
            if isinstance(status, (text_type, bytes, bytearray)):
                rv.status = status
            else:
                rv.status_code = status

        if headers:
            rv.headers.extend(headers)

        return rv


class RequestMiddleWare:
    def __init__(self, wsgi_app):
        self.wsgi_app = wsgi_app

    def __call__(self, environ, start_response):
        environ["HTTP_MESSAGE_UUID"] = id_manager.generate_common_id()
        environ["HTTP_REQUEST_TIMESTAMP"] = time.time()
        environ["HTTP_REQUEST_TIMESTAMP_CPU"] = time.process_time()
        return self.wsgi_app(environ, start_response)


def create_app(blueprints: List[Blueprint], app: _Flask = None):
    if app is None:
        app = SuperFlask(__name__)

    def _find_cumstom_field(names: list, suffix: Optional[Dict]):
        for name in names:
            value = request.headers.get(name)
            if value is None and suffix is not None:
                value = suffix.get("request_args", {}).get(name)
            if value is not None:
                break
        return value

    def _load_variable(suffix: Optional[Dict]):
        g.message_uuid = request.headers.get("Message-Uuid")
        g.merchant_id = _find_cumstom_field(["merchantId", "merchant_id"], suffix)
        g.from_platform = _find_cumstom_field(["from-platform", "from"], suffix)
        g.remote_addr = request.headers.get("X-Real-Ip", "unkown")
        g.version = request.headers.get("X-Version")
        g.user_id = _find_cumstom_field(["userId", "user_id"], suffix)

    @app.errorhandler(404)
    def error_404(e):
        response_obj = base_responses.create_responses_obj(error_codes.PAGE_NOT_FOUND, error_codes.PAGE_NOT_FOUND_MSG)
        return base_responses.make_json_response(response_obj)

    @app.errorhandler(Exception)
    def error_handler(e):
        if isinstance(e, errors.Error):
            logger.warning(e)
            response_obj = base_responses.create_responses_obj(e.errcode, e.errmsg)
        elif isinstance(e, errors.ShowError):
            logger.warning(e)
            response_obj = base_responses.create_responses_obj(error_codes.SHOW_ERROR_MESSAGE, e.message)
        else:
            logger.exception("unkown error", exc_info=e)
            response_obj = base_responses.create_responses_obj(error_codes.SERVER_ERROR, error_codes.SERVER_ERROR_MSG)
        return base_responses.make_json_response(response_obj)

    @app.before_request
    def before_request_handler():
        try:
            request_args = {}
            if request.args:
                request_args.update(request.args.to_dict() or {})
            if request.view_args:
                request_args.update(request.view_args or {})
            if request.values:
                request_args.update(request.values.to_dict() or {})
            if request.method.upper() == 'POST':
                if request.is_json:
                    request_args.update(request.json or {})
                if request.form:
                    request_args.update(request.form.to_dict() or {})
            if request.files:
                request_args["files"] = list(request.files.keys())
            suffix = {"request_args": request_args, "headers": get_headers()}
            _load_variable(suffix)
            record_machine_info(prefix="BeforeRequest =====> ", suffix=suffix)
        except Exception as e:
            logger.exception(f"before_request error", exc_info=e)

    @app.after_request
    def after_request_handler(response):
        try:
            suffix = {"status_code": response.status_code}
            if 'application/json' in response.content_type:
                resp = json.loads(response.get_data()) if response.get_data() else {}
                suffix["response"] = {"errcode": resp.get("errcode"), "errmsg": resp.get("errmsg")}
            request_timestamp = request.headers.get("Request-Timestamp")
            request_timestamp_cpu = request.headers.get("Request-Timestamp-Cpu")
            if request_timestamp_cpu:
                cpu_cost = int((time.process_time() - float(request_timestamp_cpu)) * 1000)
                suffix["cpu_cost"] = f"{cpu_cost}ms"
            if request_timestamp:
                interface_cost = int((time.time() - float(request_timestamp)) * 1000)
                suffix["interface_cost"] = f"{interface_cost}ms"
            record_meta = record_machine_info(prefix="AfterRequest =====> ", suffix=suffix)
            record_syslogger(record_meta)
        except:
            pass
        return response

    def record_machine_info(prefix, suffix: dict = None):
        cpu_percent = psutil.cpu_percent(percpu=True)
        memory_percent = psutil.virtual_memory().percent
        url_rule = request.url_rule
        data = {
            "method": request.method.upper(),
            "api_rule": url_rule.rule if url_rule else '',
            "api": request.path,
            "view_func": request.endpoint,
            "user_id": g.user_id,
            "merchant_id": g.merchant_id,
            "from_platform": g.from_platform,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
        }
        if suffix is not None:
            data.update(suffix)
        data = {k: v for k, v in data.items() if v}
        logger.info(f"""{prefix} {data}""")
        return data

    def record_syslogger(record_meta):
        now = time.time()
        request_timestamp = request.headers.get("Request-Timestamp")
        interface_cost = now - float(request_timestamp)
        user_id = record_meta.get("user_id")
        user_ip = request.headers.get("X-Real-Ip")
        merchant_id = record_meta.get("merchant_id")
        method = request.method
        url = request.url
        url_rule = request.url_rule
        syslogger.info(
            f'INTERFACE_COST|||{now}|||{request_timestamp}|||{interface_cost}|||'
            f'{user_id}|||{user_ip}|||{merchant_id}|||{method}|||{url}|||{url_rule}'
        )

    for blueprint in blueprints:
        app.register_blueprint(blueprint)

    app.wsgi_app = RequestMiddleWare(app.wsgi_app)

    return app


def print_route(app):
    with app.test_request_context():
        print('-' * 100)
        print("All routes with their view functions and source files:")
        for rule in app.url_map.iter_rules():
            view_func = app.view_functions[rule.endpoint]
            view_func_name = view_func.__name__
            if hasattr(view_func, '__module__'):
                module = inspect.getmodule(view_func)
                source_file = inspect.getsourcefile(module)
                relpath = os.path.relpath(source_file)
            else:
                relpath = 'unknown location'

            print(f"| api -> {rule} \t| view_func -> {view_func_name} \t| filename -> {relpath}")


def record_cpu_cost(func):
    @functools.wraps(func)
    def wrapper(*args, **kargs):
        start_time = time.process_time()
        result = func(*args, **kargs)
        end_time = time.process_time()
        logger.info(f"记录cpu耗时: {(end_time - start_time) * 1000:.3f}ms, func={func.__name__}")
        return result
    return wrapper


def record_cpu_cost_from_name(name=None):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kargs):
            start_time = time.process_time()
            result = func(*args, **kargs)
            end_time = time.process_time()
            logger.info(f"记录cpu耗时: {(end_time - start_time) * 1000:.3f}ms, func={name or func.__name__}")
            return result
        return wrapper
    return decorator

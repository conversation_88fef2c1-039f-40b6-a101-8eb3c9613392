# -*- coding: utf-8 -*-

import logging
import os
import msgpack

from common.singleton import SingletonMetaNoThreadSafe as SingletonMetaclass
from kafka import KafkaProducer
from kafka import KafkaConsumer

logger = logging.getLogger(__name__)


class KafkaTopicHelper:

    env = os.environ.get("DEPLOYMENT_ENV")

    @staticmethod
    def get_ordering_topic():
        return "{}_ordering".format(KafkaTopicHelper.env)

    @staticmethod
    def get_ordering_fail_topic():
        return "{}_ordering_fail".format(KafkaTopicHelper.env)

    @staticmethod
    def get_ordering_coupon_package_union_pay_fail_topic():
        return "{}_ordering_coupon_package_union_pay_fail".format(KafkaTopicHelper.env)

    @staticmethod
    def get_inexistence_transaction_topic():
        return "{}_inexistence_transaction".format(KafkaTopicHelper.env)

    @staticmethod
    def get_kry_callback_service_topic(path):
        path = path.replace("/", "_")
        return "{}_kry_callback_{}".format(KafkaTopicHelper.env, path)

    @staticmethod
    def get_wechat_create_order_info_sync_topic():
        return "{}_wechat_create_order_info_sync".format(KafkaTopicHelper.env)

    @staticmethod
    def get_wechat_pay_order_info_sync_topic():
        return "{}_wechat_pay_order_info_sync".format(KafkaTopicHelper.env)

    @staticmethod
    def get_ordering_paid_success_topic():
        return "{}_order_paid_success".format(KafkaTopicHelper.env)

class KafkaConsumerHelper(metaclass=SingletonMetaclass):
    def __init__(self, group_id=None, consumer_timeout_ms=None):
        host = os.environ.get("KAFKA_HOST")
        port = os.environ.get("KAFKA_PORT")
        logger.info("kafka consumer: {} {}".format(host, port))
        bootstrap_servers = "{}:{}".format(host, port)
        if consumer_timeout_ms is None:
            consumer_timeout_ms = float("inf")
        if group_id is not None:
            self.consumer = KafkaConsumer(
                group_id=group_id, bootstrap_servers=bootstrap_servers, consumer_timeout_ms=consumer_timeout_ms)
        else:
            self.consumer = KafkaConsumer(bootstrap_servers=bootstrap_servers, consumer_timeout_ms=consumer_timeout_ms)

    def close(self):
        if self.consumer:
            self.consumer.close()

class KafkaProducerHelper(metaclass=SingletonMetaclass):
    def __init__(self):
        host = os.environ.get("KAFKA_HOST")
        port = os.environ.get("KAFKA_PORT")
        bootstrap_servers = "{}:{}".format(host, port)
        self.producer = KafkaProducer(
            bootstrap_servers=bootstrap_servers, value_serializer=msgpack.dumps)

    def publish(self, topic, key=None, message=None):
        if message is None:
            return
        logger.info("send {}:{} to topic: {}".format(key, message, topic))
        if key is not None:
            if isinstance(key, str):
                key = key.encode("utf8")
            else:
                logger.info("key must be string not: {} {}".format(key, type(key)))
                return
        if message is None:
            return
        value = message
        if isinstance(message, str):
            value = message.encode("utf8")
        try:
            if key is not None:
                self.producer.send(topic, key=key, value=value)
            else:
                self.producer.send(topic, value=value)
            logger.info("kafka publish: {} {}".format(key, value))
        except Exception as ex:
            logger.exception("kafka send error: {}".format(ex))

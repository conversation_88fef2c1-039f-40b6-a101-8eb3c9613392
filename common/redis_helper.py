# -*- coding: utf-8 -*-

"""
deprecated use redis_client.py
"""

import logging
import os

from redis import StrictRedis
from redis import exceptions
from redis import ConnectionPool

from cache.redis_client import RedisClient

logger = logging.getLogger(__name__)


class RedisHelper(object):

    redis_connection_pool = None

    # def __new__(cls, *args, **kargs):
    #     if not RedisHelper.redis_connection_pool:
    #         RedisHelper.redis_connection_pool = ConnectionPool(
    #             host=os.environ.get("REDIS_HOST"),
    #             password=os.environ.get("REDIS_PASSWORD"),
    #             port=int(os.environ.get("REDIS_PORT")),
    #             socket_timeout=30, socket_keepalive=True, retry_on_timeout=True)
    #         return object.__new__(cls, *args, **kargs)

    def __init__(self):
        self.__connect()

    def publish(self, channel, message_json, retry_times=3):
        if retry_times == 0:
            return
        try:
            self.conn.publish(channel, message_json)
        except Exception as ex:
            logger.exception("redis publish exception: {}".format(ex))
            self.publish(channel, message_json, retry_times - 1)

    def _get_conn(self):
        conn = RedisClient().get_connection()
        self.redis_connection_pool = conn.connection_pool
        return conn

    def __connect(self):
        try:
            self.conn = self._get_conn()
            self.pubsub = self.conn.pubsub(ignore_subscribe_messages=True)
        except exceptions.ConnectionError:
            # Redis连接数超过max_connections的值的时候会报这个错误
            logger.error("Redis连接数过多")

    def get_connection(self):
        try:
            return self._get_conn()
        except Exception as e:
            logger.exception("get redis connection failed: %s" % (e))
            return None

RedisHelper()

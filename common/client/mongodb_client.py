# mongodb database
from pymongo import MongoClient


class MongodbClient(object):
    def __init__(self, server, port, username, root_password):
        self.conn = MongoClient(host=server, port=port,
                    username=username, password=root_password)

    def get_state(self, database):
        db = self.conn[database]
        return self.conn is not None and db is not None

    def set_one(self, database, collection, data, data_filter, column=None):
        if data_filter and self.find_one(database, collection, data_filter, column=column):
            return self.replace_one(database, collection, data_filter, data)
        return self.insert_one(database, collection, data)

    def insert_one(self, database, collection, data):
        db = self.conn[database]
        if self.get_state(database):
            ret = db[collection].insert_one(data)
            return ret.inserted_id
        else:
            return ""

    def insert_many(self, database, collection, data):
        db = self.conn[database]
        if self.get_state(database):
            ret = db[collection].insert_many(data)
            return ret.inserted_id
        else:
            return ""

    def update(self, database, collection, data):
        db = self.conn[database]
        # data format:
        # {key:[old_data,new_data]}
        data_filter = {}
        data_revised = {}
        for key in data.keys():
            data_filter[key] = data[key][0]
            data_revised[key] = data[key][1]
        if self.get_state(database):
            return db[collection].update_many(data_filter, {"$set": data_revised}).modified_count
        return 0

    def replace_one(self, database, collection, filter, data):
        db = self.conn[database]
        db[collection].replace_one(filter, data, upsert=True)

    def find(self, database, condition, filter, column=None):
        db = self.conn[database]
        if self.get_state(database):
            if column is None:
                return db[condition].find(filter)
            else:
                return db[condition].find(column, filter)
        else:
            return None

    def find_one(self, database, condition, filter, column=None):
        doc = self.find(database, condition, filter, column=column)
        if doc and doc.count() > 0:
            # print(doc[0])
            return doc[0]
        return None

    def delete(self, condition, filter):
        if self.get_state():
            return self.db[condition].delete_many(filter=filter).deleted_count
        return 0


if __name__ == '__main__':
    # unit test
    import time
    import random

    db = MongodbClient("192.168.31.12", 32006, 'root', 'zhiyi#mongodb#go!')
    # print(db.get_state())
    # # print(db.delete("ut", {}))
    # print(time.time())
    # start_time = int(time.time() * 1e6)
    # for i in range(100):
    #     t = int(time.time() * 1e6)
    #     db.insert_one("ut", {"username": str(t),
    #                          "timestamp": t,
    #                          "password": "aaaa",
    #                          "telephone": str(random.random() * 1000000)})
    #
    # # print("deleted count: ", db.delete("ut", {"timestamp": {"$gt": start_time + 500}}))
    # #
    # print(db.find("ut", {}).count())
    # print(db.update("ut", {"password": ["aaaa", "bbbb"]}))
    print(db.find('shilai', {}, "ut", {"password": 1, "username": 1}))

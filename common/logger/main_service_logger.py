import os
import logging
from logging.config import dictConfig

from common.logger import constants as const
from common.utils import file_access_helper
file_access_helper.ensure_directory_exists(const.MAIN_SERVICE_LOG_DIR)

version = os.environ.get("VERSION", "v1.0")
werkzeug_logger = logging.getLogger('werkzeug')
werkzeug_logger.setLevel(logging.ERROR)

# MainService Logger 配置
dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    "name": version,
    'formatters': {
        'standard': {
            'format': const.STANDARD_FORMAT
        },
        'custom': {
            'format': const.CUSTOM_FORMAT,
            '()': 'common.logger.logger_handler.RequestFormatter'
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'custom',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'custom',
            'filename': os.path.join(const.MAIN_SERVICE_LOG_DIR, 'error.log'),
            'when': 'D',
            'interval': 1,
            'backupCount': 30,
            'encoding': 'utf-8'
        },
    },
    'loggers': {
        '': {
            'handlers': ['error', 'console'],
            'level': 'INFO'
        }
    }
})


def get_logger(name):
    return logging.getLogger(name)

import os

# 标准格式
STANDARD_FORMAT = '%(asctime)s - %(name)s - %(lineno)s - %(levelname)s - %(message)s'
# 自定义格式
CUSTOM_FORMAT = '%(asctime)s | %(levelname)s | %(remote_addr)s | %(name)s(%(lineno)d) | user_id:%(user_id)s | merchant_id:%(merchant_id)s | message_uuid:%(message_uuid)s: %(message)s'

# 日志文件目录
LOG_DIR = format(os.environ["LOG_DIR"])
ACCESS_TOKEN_SERVICE_LOG_DIR = os.path.join(LOG_DIR, 'access_token_service')
EVENT_HANDLING_SERVICE_LOG_DIR = os.path.join(LOG_DIR, 'event_handling_service')
MAIN_SERVICE_LOG_DIR = os.path.join(LOG_DIR, 'main_service')
WEBSOCKET_SERVICE_LOG_DIR = os.path.join(LOG_DIR, 'websocket_service')
BATCH_JOBS_LOG_DIR = os.path.join(LOG_DIR, 'batch_jobs')
SHILAI_ASSIST_SERVICE_LOG_DIR = os.path.join(LOG_DIR, "shilai_assist_service")

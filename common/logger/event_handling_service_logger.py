import os
from logging.config import dictConfig

from common.logger import constants as const
from common.utils import file_access_helper

file_access_helper.ensure_directory_exists(const.EVENT_HANDLING_SERVICE_LOG_DIR)

# EventHandlingService Logger 配置
dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': const.STANDARD_FORMAT
        }
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'info': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'standard',
            'filename': os.path.join(const.EVENT_HANDLING_SERVICE_LOG_DIR, 'info.log'),
            'when': 'D',
            'encoding': 'utf-8'
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'standard',
            'filename': os.path.join(const.EVENT_HANDLING_SERVICE_LOG_DIR, 'error.log'),
            'when': 'D',
            'encoding': 'utf-8'
        }
    },
    'loggers': {
        '': {
            'handlers': ['error', 'info', 'console'],
            'level': 'INFO'
        }
    }
})

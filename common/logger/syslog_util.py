# -*- coding: utf-8 -*-

"""日志处理模块."""

import logging
import uuid
import socket
import os
from flask import request
from logging.handlers import SysLogHandler


class SysEnv:

    APPNAME = 'APPNAME'
    # console
    LOGGER_ENABLE_CONSOLE = "LOGGER_ENABLE_CONSOLE"
    # SYSLOG
    LOGGER_ENABLE_SYSLOG = "LOGGER_ENABLE_SYSLOG"
    LOGGER_SYSLOG_HOST = "LOGGER_SYSLOG_HOST"
    LOGGER_SYSLOG_PORT = "LOGGER_SYSLOG_PORT"
    LOGGER_SYSLOG_FACILITY = "LOGGER_SYSLOG_FACILITY"
    # FILE
    LOGGER_ENABLE_FILE = "LOGGER_ENABLE_FILE"
    LOGGER_FILE_DIRECTORY = "LOGGER_FILE_DIRECTORY"
    LOGGER_LEVEL = "LOGGER_LEVEL"

    @staticmethod
    def get_env(key):
        return os.environ.get(key)

    @staticmethod
    def set_env(key, value):
        os.environ[key] = value


class Logger(logging.Logger):
    """日志处理类."""

    def __init__(self):
        """日志处理类初始化函数."""
        # name = SysEnv.get_env(SysEnv.APPNAME)
        name = 'shilai-common'
        if name is None:
            name = "Default"
        self.name = name
        super().__init__(self.name)
        _formatter = f'{name}:|||%(asctime)s|||%(levelname)s|||%(message)s'
        self._syslog_config = eval(os.environ.get("SYSLOG_SERVICE"))
        self._formatter = logging.Formatter(_formatter)
        self.__set_level()
        self.__init_syslog_handler()
        self.__init_console_handler()

    def __wrap_message_with_uuid(self, message):
        message_uuid = request.headers.get("Message-Uuid")
        result = f"{message_uuid}|||{message}"
        return result

    def __name_to_level(self, name):
        return logging._nameToLevel[name]

    def debug(self, message):
        """记录debug日志."""
        message = self.__wrap_message_with_uuid(message)
        super().debug(message)

    def info(self, message):
        """记录info日志."""
        message = self.__wrap_message_with_uuid(message)
        super().info(message)

    def exception(self, message):
        """记录exception日志."""
        message = self.__wrap_message_with_uuid(message)
        super().exception(message)

    def error(self, message):
        """记录错误日志."""
        message = self.__wrap_message_with_uuid(message)
        super().error(message)

    def warning(self, message):
        """记录警告日志."""
        message = self.__wrap_message_with_uuid(message)
        self.logger.warning(message)

    def fatal(self, message):
        """记录致命日志."""
        message = self.__wrap_message_with_uuid(message)
        super().fatal(message)

    def __init_syslog_handler(self):
        """设置syslog日志."""
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == 'test':
            return
        # host = 'logger.server'
        host = self._syslog_config["host"]
        port = self._syslog_config["port"]
        facility = 'local7'
        # host = SysEnv.get_env(SysEnv.LOGGER_SYSLOG_HOST)
        # port = int(SysEnv.get_env(SysEnv.LOGGER_SYSLOG_PORT))
        # facility = SysEnv.get_env(SysEnv.LOGGER_SYSLOG_FACILITY)
        self.__create_syslog_handler(host, port, facility)

    def __create_syslog_handler(self, host, port, facility):
        handler = SysLogHandler(
            address=(host, port),
            facility=SysLogHandler.facility_names[facility],
            # socktype=socket.SOCK_STREAM # TCP
        )
        handler.setFormatter(self._formatter)
        handler.setLevel(self._level)
        self.addHandler(handler)

    def __init_console_handler(self):
        """设置终端日志."""
        # enable = SysEnv.get_env(SysEnv.LOGGER_ENABLE_CONSOLE)
        # if enable is None or enable == "false":
        #     return
        if not self._syslog_config.get("enable_console"):
            return
        handler = logging.StreamHandler()
        handler.setFormatter(self._formatter)
        self.addHandler(handler)

    def __set_level(self):
        level = self._syslog_config.get("level", 'info').upper()
        _level = logging.INFO
        if level == 'INFO':
            _level = logging.INFO
        elif level == 'DEBUG':
            _level = logging.DEBUG
        if level == 'ERROR':
            _level = logging.ERROR
        if level == 'FATAL':
            _level = logging.FATAL
        self.setLevel(_level)
        self._level = _level


if __name__ == '__main__':
    SysEnv.set_env(SysEnv.LOGGER_ENABLE_CONSOLE, 'true')
    SysEnv.set_env(SysEnv.LOGGER_ENABLE_SYSLOG, 'true')
    SysEnv.set_env(SysEnv.LOGGER_SYSLOG_HOST, 'logger.server')
    SysEnv.set_env(SysEnv.LOGGER_SYSLOG_PORT, '514')
    SysEnv.set_env(SysEnv.LOGGER_SYSLOG_FACILITY, 'local7')

    import random
    logger = Logger()
    logger.info(f"test-{random.randint(1, 1000)}")


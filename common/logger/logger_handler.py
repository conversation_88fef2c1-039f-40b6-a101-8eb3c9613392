# -*- encoding: utf-8 -*-

'''
@Time        :   2024/03/13 19:09:24
'''

import logging
import os

from flask import Flask, g, has_request_context


class RequestFormatter(logging.Formatter):
    def format(self, record):
        if has_request_context():
            record.message_uuid = getattr(g, 'message_uuid', 'unkown')
            record.user_id = getattr(g, 'user_id', 'unkown')
            record.merchant_id = getattr(g, 'merchant_id', 'unkown')
            record.remote_addr = getattr(g, 'remote_addr', 'unkown')
        else:
            record.message_uuid = None
            record.user_id = None
            record.merchant_id = None
            record.remote_addr = None
        return super().format(record)


def init_logger(app: Flask):
    logger = logging.Logger(__name__)
    console_handler = logging.StreamHandler()
    formatter = RequestFormatter(
        '%(asctime)s | %(levelname)s | %(user_ip)s | %(name)s:%(lineno)d ' \
        '| message_uuid:%(message_uuid)s | user_id:%(user_id)s ' \
        '| merchant_id:%(merchant_id)s : %(message)s'
    )
    console_handler.setFormatter(formatter)
    logger.root.handlers.clear()
    logger.addHandler(console_handler)
    logger_level = os.environ.get("LOGGER_LEVEL", "INFO")
    logger.setLevel(getattr(logging, logger_level))
    app.logger = logger

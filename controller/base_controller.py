# -*- coding: utf-8 -*-

"""所有controller的最终父类."""

from flask import request
from service import errors


class BaseController:

    def __init__(self, operation, *args, **kargs):
        self._operation = operation
        self._OP_FUNCS = {
            'get': self.get,
            'update': self.update,
            'delete': self.delete,
            'list': self.list,
            'create': self.create
        }
        if hasattr(self, "_cinit"):
            self._cinit(*args, **kargs)

    def get_json_param(self, key, default=None):
        """从request.json中获取参数."""
        value = request.json.get(key, default)
        return value

    def get_header_param(self, key):
        value = request.headers.get(key)
        return value

    def do_operate(self):
        if self._operation not in self._OP_FUNCS:
            raise errors.ShowError("方法未实现")
        return self._OP_FUNCS.get(self._operation)()

    def get(self):
        raise errors.ShowError('方法未实现')

    def update(self):
        raise errors.ShowError('方法未实现')

    def delete(self):
        raise errors.ShowError('方法未实现')

    def list(self):
        raise errors.ShowError('方法未实现')

    def create(self):
        raise errors.ShowError('方法未实现')

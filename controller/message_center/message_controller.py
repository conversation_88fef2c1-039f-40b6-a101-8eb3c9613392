# -*- coding: utf-8 -*-

import time

import proto.message_center.message_pb2 as message_pb
import proto.ui.message_center.message_pb2 as message_vo_pb

from controller.base_controller import BaseController
from business_ops.message_center.message_manager import MessageManager
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper


class MessageController(BaseController):
    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._OP_FUNCS.update(
            {
                "get_user_period_messages": self.get_user_period_messages,
            }
        )
        self.message_manager = MessageManager()
        self.merchant_da = MerchantDataAccessHelper()
        self.user = UserDataAccessHelper().get_user(user_id=self.get_header_param('userId'))

    def get_user_period_messages(self):
        latest_create_time = int(self.get_json_param("latestCreateTime", int(time.time())))
        start_time = latest_create_time - 24 * 60 * 60 * 365
        if not self.user:
            return None
        messages = self.message_manager.get_user_period_messages(
            self.user, create_time_periods=(start_time, latest_create_time)
        )
        if not messages or len(messages) == 0:
            return None
        vo = message_vo_pb.UserPeriodMessagesVO()
        for message in messages:
            mvo = vo.messages.add()
            mvo.id = message.id
            mvo.title = message.title
            mvo.text = message.text
            mvo.create_time = message.create_time
            self.__set_message_merchant_name(message, mvo)
        return vo

    def __set_message_merchant_name(self, message, vo):
        if not message.relative_info.merchant_id:
            return
        merchant = self.merchant_da.get_merchant(message.relative_info.merchant_id)
        vo.merchant_name = merchant.basic_info.display_name

# -*- coding: utf-8 -*-

from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from controller.base_controller import BaseController
from service import errors


class CouponTemplateController(BaseController):

    def _cinit(self, *args, **kargs):
        self.__coupon_template_manager = CouponTemplateManager()

    def create(self):
        coupon_template = self.__coupon_template_manager.create_coupon_template()
        self.__update(coupon_template)
        return coupon_template

    def get(self):
        coupon_template = self.__coupon_template_manager.get_coupon_template(
            id=self.get_json_param('couponTemplateId'))
        return coupon_template

    def update(self):
        coupon_template = self.get()
        if coupon_template is None:
            raise errors.ShowError('券模板不存在')
        self.__update(coupon_template)
        return coupon_template

    def list(self):
        coupon_template_list = self.__coupon_template_manager.get_coupon_template_list(
            ids=self.get_json_param('couponTemplateIds')
        )
        return coupon_template_list

    def __update(self, coupon_template):
        if coupon_template is None:
            return
        self.__coupon_template_manager.update_coupon_template(
            coupon_template,
            name=self.get_json_param('name'),
            promotion_type=self.get_json_param('promotionType'),
            fixed_reduce_amount=self.get_json_param('fixedReduceAmount'),
            fixed_discount=self.get_json_param('fixedDiscount'),
            random_reduce_amount=self.get_json_param('randomReduceAmount'),
            reduce_fee_type=self.get_json_param('reduceFeeType'),
            usage_limitation=self.get_json_param('usageLimitation')
        )
        self.__coupon_template_manager.add_or_update_coupon_template(coupon_template)

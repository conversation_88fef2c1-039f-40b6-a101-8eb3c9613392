# -*- coding: utf-8 -*-

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
import proto.page.coupon_list_pb2 as coupon_list_pb
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.promotion.group_purchase.group_purchase_invitation_manager import GroupPurchaseInvitationManager
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from common.utils import id_manager
from common.cache_server_keys import CacheServerKeys
from cache.redis_client import RedisClient
from controller.base_controller import BaseController
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service import errors


class CouponController(BaseController):
    def _cinit(self, *args, **kargs):
        self._OP_FUNCS.update({'user_coupon_list': self.get_user_coupon_list})
        self.__user_da = UserDataAccessHelper()
        self.user = self.__user_da.get_user(self.get_header_param('userId'))
        self.__coupon_manager = CouponManager(
            user=self.user,
            merchant_id=self.get_json_param('merchantId'),
        )
        self.__coupon_template_manager = CouponTemplateManager()
        self.redis_client = RedisClient().get_connection()

    def create(self):
        coupon_template = self.__coupon_template_manager.get_coupon_template(id=self.get_json_param('couponTemplateId'))
        if coupon_template is None:
            raise errors.ShowError('券模板不存在')
        coupon = self.__coupon_manager.create_coupon(coupon_template)
        self.__update(coupon)
        return coupon

    def get(self):
        coupon = self.__coupon_manager.get_coupon(id=self.get_json_param('couponId'))
        return coupon

    def update(self):
        coupon = self.get()
        if coupon is None:
            raise errors.ShowError('券不存在')
        self.__update(coupon)
        return coupon

    def list(self):
        coupon_list = self.__coupon_manager.get_coupon_list(ids=self.get_json_param('couponIds'))
        return coupon_list

    def __update(self, coupon):
        if coupon is None:
            return
        self.__coupon_manager.update_coupon(coupon, name=self.get_json_param('name'), status=self.get_json_param('status'))
        self.__coupon_manager.add_or_update_coupon(coupon)

    def __load_merchants(self, group_purchases=None):
        if group_purchases is None:
            group_purchases = self.group_purchases
        merchant_da = MerchantDataAccessHelper()
        merchant_id = self.get_json_param('merchantId')
        if merchant_id:
            merchants = [merchant_da.get_merchant_by_id(merchant_id)]
        else:
            merchant_ids = set([merchant_id])
            for g in group_purchases:
                merchant_ids.add(g.merchant_id)
            merchants = merchant_da.get_merchants_by_ids(list(merchant_ids))
        self.merchants = {m.id: m for m in merchants}

    def __load_user_group_purchases_as_leader(self):
        group_purchase_manager = GroupPurchaseManager(user=self.user, merchant_id=self.get_json_param('merchantId'))
        group_purchases = group_purchase_manager.get_group_purchase_list(with_leader=True, with_merchant=True)
        return group_purchases

    def __load_user_group_purchases_as_member(self):
        group_purchase_invitation_manager = GroupPurchaseInvitationManager(user=self.user)
        invitations = group_purchase_invitation_manager.get_group_purchase_invitation_list()
        group_purchase_ids = [i.join_group_id for i in invitations]
        group_purchase_manager = GroupPurchaseManager(user=self.user)
        group_purchases = group_purchase_manager.get_group_purchase_list(ids=group_purchase_ids)
        return group_purchases

    def get_user_coupon_list(self):
        coupon_list_vo = coupon_list_pb.CouponListVO()
        if self.user:
            self.group_purchases = []
            self.group_purchases.extend(self.__load_user_group_purchases_as_member())
            self.group_purchases.extend(self.__load_user_group_purchases_as_leader())
            self.__load_merchants()
            self.__get_group_purchase_member_coupons(coupon_list_vo)
            # 用户已经获得的券
            self.__set_user_coupons(coupon_list_vo)
        return coupon_list_vo

    def __get_group_purchase_member_coupons(self, coupon_list_vo):
        """我作为团员接受了邀请的团购的券."""
        merchant_id = self.get_json_param('merchantId')
        for group_purchase in self.group_purchases:
            if merchant_id is not None and merchant_id != group_purchase.merchant_id:
                continue
            if group_purchase.status not in [
                group_purchase_pb.GroupPurchase.ACTIVE,
            ]:
                continue
            if len(group_purchase.group_members) == group_purchase.member_coupon_count:
                continue
            if group_purchase.leader_id == self.user.id:
                # 团长
                self.__create_group_purchase_coupon_vo_as_leader(group_purchase, coupon_list_vo)
                pass
            else:
                # 团员
                self.__create_group_purchase_coupon_vo_as_member(group_purchase, coupon_list_vo)

    def __set_group_purchase_coupon(self, coupon_vo, group_purchase, coupon, coupon_template):
        coupon_template_manager = CouponTemplateManager()
        coupon_vo.least_cost = coupon_template_manager.get_coupon_template_min_bill_fee(coupon_template)
        coupon_vo.reduce_cost = coupon_template_manager.get_coupon_template_reduce_fee(coupon_vo.least_cost, coupon_template)
        coupon_vo.name = coupon_template.name
        coupon_vo.coupon_count = 1
        coupon_vo.can_refund = False
        coupon_vo.valid_period = group_purchase.valid_period
        coupon_vo.member_coupon_count = group_purchase.member_coupon_count
        coupon_vo.merchant_id = group_purchase.merchant_id
        merchant = self.merchants.get(group_purchase.merchant_id)
        coupon_vo.merchant_name = self.__get_merchant_name(merchant)
        coupon_vo.category_name = coupon_template.category_name
        coupon_vo.status = coupon_pb.Coupon.CouponStatus.Name(coupon_pb.Coupon.ACCEPTED)
        coupon_vo.coupon_type = coupon.coupon_type
        coupon_vo.is_group_purchase = True
        self.__set_coupon_vo_id(coupon_vo, group_purchase, coupon, coupon_template)

    def __set_coupon_vo_id(self, coupon_vo, group_purchase, coupon, coupon_template):
        key = CacheServerKeys.get_user_coupon_cache_key(self.user.id)
        value = self.redis_client.hget(key, group_purchase.id)
        if not value:
            id = id_manager.generate_common_id()
            coupon_type = coupon_pb.Coupon.CouponType.Name(coupon.coupon_type)
            value = f'{id}-{coupon_template.id}-{coupon_type}'
            self.redis_client.hset(
                key,
                key=group_purchase.id,
                value=value,
            )
        else:
            id = value.decode().split('-')[0]
        coupon_vo.id = id
        self.redis_client.expire(key, 3600)
        return id

    def __create_group_purchase_coupon_vo_as_leader(self, group_purchase, coupon_list_vo):
        # TODO: 在 2022-12-14 16:00 之后开的团,团长不能参与自己的团
        if group_purchase.create_time > 1671004800:
            return
        coupon_template_manager = CouponTemplateManager()
        coupon_template = coupon_template_manager.get_coupon_template(id=group_purchase.leader_coupon_template_id)
        if not coupon_template:
            return
        if group_purchase.status != group_purchase_pb.GroupPurchase.ACTIVE:
            return
        coupon_vo = coupon_list_vo.coupons.add()
        merchant = self.merchants.get(group_purchase.merchant_id)
        coupon_manager = CouponManager(user=self.user, merchant=merchant)
        coupon = coupon_manager.create_coupon(coupon_template)
        coupon.coupon_type = coupon_pb.Coupon.GROUP_PURCHASE_LEADER
        self.__set_group_purchase_coupon(coupon_vo, group_purchase, coupon, coupon_template)
        coupon_vo.name = "我的拼团券"

    def __create_group_purchase_coupon_vo_as_member(self, group_purchase, coupon_list_vo):
        coupon_template_manager = CouponTemplateManager()
        coupon_template = coupon_template_manager.get_coupon_template(id=group_purchase.member_coupon_template_id)
        if not coupon_template:
            return
        coupon_vo = coupon_list_vo.coupons.add()
        merchant = self.merchants.get(group_purchase.merchant_id)
        coupon_manager = CouponManager(user=self.user, merchant=merchant)
        coupon = coupon_manager.create_coupon(coupon_template)
        coupon.coupon_type = coupon_pb.Coupon.GROUP_PURCHASE_MEMBER
        self.__set_group_purchase_coupon(coupon_vo, group_purchase, coupon, coupon_template)
        if group_purchase.leader_nickname != "":
            coupon_vo.name = f"{coupon_vo.name}({group_purchase.leader_nickname}的团)"

    def __set_user_coupons(self, coupon_list_vo):
        coupon_manager = CouponManager(user=self.user)
        coupons = coupon_manager.get_coupon_list(status=coupon_pb.Coupon.ACCEPTED)
        coupon_template_manager = CouponTemplateManager()
        merchant_id = self.get_json_param('merchantId')
        for coupon in coupons:
            if merchant_id is not None and merchant_id != coupon.merchant_id:
                continue
            if coupon.status not in [
                coupon_pb.Coupon.ISSUED,
                coupon_pb.Coupon.ACCEPTED,
                coupon_pb.Coupon.USED,
                coupon_pb.Coupon.EXPIRED,
            ]:
                continue
            if coupon_manager.is_coupon_expired(coupon):
                continue
            coupon_vo = coupon_list_vo.coupons.add()
            coupon_vo.id = coupon.id
            coupon_vo.name = coupon.name
            coupon_vo.can_refund = False
            merchant = self.merchants.get(coupon.merchant_id)
            if not merchant:
                merchant = MerchantDataAccessHelper().get_merchant(merchant_id=coupon.merchant_id)
                self.merchants.update({coupon.merchant_id: merchant})
            coupon_template = coupon_template_manager.get_coupon_template(id=coupon.template_id)
            coupon_vo.merchant_id = merchant.id
            coupon_vo.merchant_name = self.__get_merchant_name(merchant)
            coupon_vo.category_name = coupon.category_name
            coupon_vo.coupon_type = coupon.coupon_type
            coupon_vo.status = coupon_pb.Coupon.CouponStatus.Name(coupon.status)
            coupon_vo.least_cost = coupon_template_manager.get_coupon_template_min_bill_fee(coupon_template)
            coupon_vo.reduce_cost = coupon_template_manager.get_coupon_template_reduce_fee(
                coupon_vo.least_cost, coupon_template
            )
            coupon_vo.valid_period = coupon.usage_limitation.time_limitation.valid_period
            coupon_vo.expired_time = coupon.usage_limitation.time_limitation.effective_end_time

    def __get_merchant_name(self, merchant):
        store = merchant.stores[0]
        if store.name:
            return store.name
        if merchant.basic_info.display_name:
            return merchant.basic_info.display_name
        if merchant.basic_info.name:
            return merchant.basic_info.name
        return ""

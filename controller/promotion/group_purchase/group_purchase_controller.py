# -*- coding: utf-8 -*-

"""团购模板控制器模块."""

from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
import proto.page.group_purchase_pb2 as page_group_purchase_pb
import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.promotion.group_purchase.group_purchase_invitation_manager import GroupPurchaseInvitationManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.message_center.message_manager import MessageManager
from controller.base_controller import BaseController
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service import errors


class GroupPurchaseController(BaseController):

    def _cinit(self, *args, **kargs):
        self._group_purchase_manager = GroupPurchaseManager(
            user_id=self.get_header_param('userId'),
            merchant_id=self.get_json_param('merchantId'),
        )
        self._group_purchase_template_manager = GroupPurchaseTemplateManager()
        self._group_purchase_invitation_manager = GroupPurchaseInvitationManager(
            user=self._group_purchase_manager.user,
            merchant=self._group_purchase_manager.merchant,
        )
        self._coupon_template_manager = CouponTemplateManager()
        self._coupon_manager = CouponManager(
            user=self._group_purchase_manager.user,
            merchant=self._group_purchase_manager.merchant,
        )
        self._OP_FUNCS.update({
            'join': self.join,
            'quote': self.quote,
            'my_list': self.my_list,
            'closest_finish_group_purchase': self.get_user_closest_finish_group_purchase,
            'my_recently': self.my_recently,
            'ongoing_group_purchase': self.my_ongoing_group_purchase,
        })

    def get(self):
        group_purchase = self._group_purchase_manager.get_group_purchase(
            id=self.get_json_param("groupPurchaseId")
        )
        return self.__convert_group_purchase_to_vo(group_purchase)

    def create(self):
        group_purchase_template = self._group_purchase_template_manager.get_group_purchase_template(
            id=self.get_json_param('groupPurchaseTemplateId')
        )
        if group_purchase_template is None:
            raise errors.ShowError("团购模板不存在")
        group_purchase = self._group_purchase_manager.create_group_purchase(
            group_purchase_template=group_purchase_template,
        )
        self.__update(group_purchase)
        return self.__convert_group_purchase_to_vo(group_purchase)

    def update(self):
        group_purchase = self.get()
        if group_purchase is None:
            raise errors.ShowError("团购不存在")
        self.__update(group_purchase)
        return self.__convert_group_purchase_to_vo(group_purchase)

    def list(self):
        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            ids=self.get_json_param('groupPurchaseIds'),
            with_leader=self.get_json_param('withLeader', None),
            with_merchant=self.get_json_param('withMerchant', None),
            status=self.get_json_param('status'),
        )
        return self.__convert_group_purchases_to_vo(group_purchases)

    def get_user_closest_finish_group_purchase(self):
        """获取用户最接近结束的团"""
        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            with_leader=self.get_json_param('withLeader', None),
            with_merchant=self.get_json_param('withMerchant', None),
            status=group_purchase_pb.GroupPurchase.ACTIVE
        )
        result_group_purchase = None
        member_count = 0
        for group_purchase in group_purchases:
            if len(group_purchase.group_members) > member_count or result_group_purchase is None:
                member_count = len(group_purchase.group_members)
                result_group_purchase = group_purchase
        return self.__convert_group_purchase_to_vo(result_group_purchase)

    def my_list(self):
        multi_status = [
            group_purchase_pb.GroupPurchase.ACTIVE,
            group_purchase_pb.GroupPurchase.FINISHED,
            group_purchase_pb.GroupPurchase.EXPIRED,
        ]
        multi_status = self.get_json_param('multiStatus', multi_status)
        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            with_leader=True,
            multi_status=multi_status,
        )
        return self.__convert_group_purchases_to_vo(group_purchases)

    def my_recently(self):
        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            with_leader=True,
            status=group_purchase_pb.GroupPurchase.ACTIVE
        )
        if len(group_purchases) == 0:
            return
        group_purchases.sort(key=lambda g: g.active_time)
        return self.__convert_group_purchase_to_vo(group_purchases[0])

    def my_ongoing_group_purchase(self):
        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            with_leader=True,
            status=group_purchase_pb.GroupPurchase.ACTIVE
        )
        vo = page_group_purchase_pb.OngoingGroupPurchaseVO()
        if len(group_purchases) == 0:
            return vo
        for g in group_purchases:
            coupon_template = self._coupon_template_manager.get_coupon_template(
                id=g.leader_coupon_template_id)
            if not coupon_template:
                continue
            vo.count += 1
            vo.saved_fee += self._coupon_template_manager.get_coupon_template_reduce_fee(
                0, # 目前只有立减券，所以能正确计算，如果有折扣券，就需要拿到实际订单来计算扣减金额
                coupon_template
            )

    def join(self):
        group_purchase_id = self.get_json_param('groupPurchaseId')
        group_purchase = self._group_purchase_manager.get_group_purchase(id=group_purchase_id)
        if group_purchase is None:
            raise errors.ShowError('团购不存在')
        group_purchase_invitation = self._group_purchase_invitation_manager.get_group_purchase_invitation(
            member_id=self.get_header_param('userId'),
            group_purchase_id=group_purchase_id
        )
        if group_purchase_invitation:
            raise errors.ShowError("您已经加入过此团")
        if group_purchase.leader_id == self.get_header_param('userId'):
            raise errors.ShowError("您不能接受自己的团邀请")
        if group_purchase.status != group_purchase_pb.GroupPurchase.ACTIVE:
            raise errors.ShowError("不能参与非活跃的团")
        group_purchase_invitation = self._group_purchase_invitation_manager.create_group_purchase_invitation(
            group_purchase
        )
        self._group_purchase_invitation_manager.add_or_update_group_purchase_invitation(
            group_purchase_invitation)
        user = UserDataAccessHelper().get_user(user_id=self.get_header_param("userId"))
        MessageManager().create_group_purhase_coupon_issued_message(self.user, group_purchase)
        return group_purchase_invitation

    def quote(self):
        """通过订单总金额询问用户是否能参团."""
        bill_fee = self.get_json_param('billFee')
        merchant_id = self.get_json_param('merchantId')
        user_id = self.get_header_param('userId')
        result = page_group_purchase_pb.GroupPurchaseQuote()
        result.user_id = user_id
        result.merchant_id = merchant_id
        result.can_join_group_purchase = False
        group_purchase_list = self._group_purchase_manager.get_group_purchase_list(
            status=group_purchase_pb.GroupPurchase.ACTIVE,
            with_merchant=True
        )
        for group_purchase in group_purchase_list:
            result.member_coupon_min_bill_fee = group_purchase.member_coupon_min_bill_fee
            if group_purchase.member_coupon_min_bill_fee <= bill_fee:
                result.can_join_group_purchase = True
            if group_purchase.leader_id == user_id:
                result.is_leader = True
                result.can_join_group_purchase = False # 团长不能参与自己的团
            if result.is_leader and result.can_join_group_purchase:
                break
        return result

    def __update(self, group_purchase):
        if group_purchase is None:
            return
        self._group_purchase_manager.update_group_purchase(
            group_purchase,
            name=self.get_json_param('name'),
            status=self.get_json_param('status'),
        )
        self._group_purchase_manager.add_or_update_group_purchase(group_purchase)

    def __create_opening_coupons(self, group_purchase_template):
        coupon_templates = self._coupon_template_manager.get_coupon_template_list(
            list(group_purchase_template.opening_coupon_template_ids)
        )
        result = []
        for coupon_template in coupon_templates:
            coupon = self._coupon_manager.create_coupon(coupon_template)
            result.append(coupon)
            self._coupon_manager.add_or_update_coupon(coupon)
        return result

    def __create_closing_coupons(self, group_purchase_template):
        coupon_templates = self._coupon_template_manager.get_coupon_template_list(
            list(group_purchase_template.closing_coupon_template_ids)
        )
        result = []
        for coupon_template in coupon_templates:
            coupon = self._coupon_manager.create_coupon(coupon_template)
            result.append(coupon)
            self._coupon_manager.add_or_update_coupon(coupon)
        return result

    def __convert_group_purchase_to_vo(self, group_purchase):
        if group_purchase is None:
            return None
        tmp = json_format.MessageToDict(group_purchase, including_default_value_fields=True)
        merchant_da = MerchantDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        merchant = merchant_da.get_merchant(merchant_id=group_purchase.merchant_id)
        group_purchase_vo = json_format.ParseDict(
            tmp,
            page_group_purchase_pb.GroupPurchaseVO(),
            ignore_unknown_fields=True)
        group_purchase_vo.merchant_name = merchant.basic_info.display_name
        success_count = 0
        for group_member in group_purchase.group_members:
            coupon_id = group_member.coupon_id
            order = ordering_da.get_order(new_coupon_id=coupon_id)
            if not order:
                continue
            if order.user_id == group_purchase.leader_id:
                continue
            if order.status == dish_pb.DishOrder.PAID:
                success_count += 1
        group_purchase_vo.successd_fee += success_count * group_purchase.member_coupon_min_bill_fee
        group_purchase_vo.refunded_fee += (group_purchase.member_coupon_count - success_count) * group_purchase.member_coupon_min_bill_fee
        for opening_coupon_id in group_purchase.opening_coupon_ids:
            coupon = self._coupon_manager.get_coupon(id=opening_coupon_id)
            group_purchase_vo.leader_reduce_fees.append(
                self._coupon_manager.get_coupon_reduce_fee(0, coupon)
            )
        for closing_coupon_id in group_purchase.closing_coupon_ids:
            coupon = self._coupon_manager.get_coupon(id=closing_coupon_id)
            group_purchase_vo.return_cash_fees.append(
                self._coupon_manager.get_coupon_reduce_fee(0, coupon)
            )
        return group_purchase_vo

    def __convert_group_purchases_to_vo(self, group_purchases):
        result = []
        for g in group_purchases:
            result.append(self.__convert_group_purchase_to_vo(g))
        return result

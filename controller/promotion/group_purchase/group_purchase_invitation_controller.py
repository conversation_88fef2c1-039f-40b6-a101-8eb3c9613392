# -*- coding: utf-8 -*-

"""团购模板控制器模块."""

import proto.page.group_purchase_pb2 as page_group_purchase_pb
import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from business_ops.promotion.group_purchase.group_purchase_invitation_manager import GroupPurchaseInvitationManager
from business_ops.message_center.message_manager import MessageManager
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from controller.base_controller import BaseController
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import errors


class GroupPurchaseInvitationController(BaseController):

    def _cinit(self, *args, **kargs):
        self._OP_FUNCS.update({
            "my_list": self.my_list
        })
        self._group_purchase_invitation_manager = GroupPurchaseInvitationManager(
            user_id=self.get_header_param('userId'),
            merchant_id=self.get_json_param('merchantId')
        )
        self.user = self._group_purchase_invitation_manager.user
        self.merchant = self._group_purchase_invitation_manager.merchant
        self._group_purchase_manager = GroupPurchaseManager(
            user=self.user,
            merchant=self.merchant
        )

    def my_list(self):
        invitation_list = self._group_purchase_invitation_manager.get_group_purchase_invitation_list()
        return self.__convert_group_purchase_invitations_to_vo(invitation_list)

    def join(self):
        group_purchase_id = self.get_json_param('groupPurchaseId')
        group_purchase = self._group_purchase_manager.get_group_purchase(id=group_purchase_id)
        if group_purchase is None:
            raise errors.ShowError('团购不存在')
        group_purchase_invitation = self._group_purchase_invitation_manager.create_group_purchase_invitation(
            group_purchase
        )
        MessageManager().create_group_purhase_coupon_issued_message(self.user, group_purchase)
        self.__update(group_purchase_invitation)

    def __update(self, group_purchase_invitation):
        if group_purchase_invitation is None:
            return
        self._group_purchase_invitation_manager.update_group_purchase_invitation(
            group_purchase_invitation
        )
        self._group_purchase_invitation_manager.add_or_update_group_purchase_invitation(
            group_purchase_invitation)

    def __convert_group_purchase_invitation_to_vo(self, group_purchase_invitation):
        group_purchase = self._group_purchase_manager.get_group_purchase(
            id=group_purchase_invitation.join_group_id
        )
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(group_purchase.merchant_id)
        vo = page_group_purchase_pb.GroupPurchaseInvitationVO()
        vo.id = group_purchase.id
        vo.name = f"{group_purchase.leader_nickname}的拼团"
        vo.merchant_name = merchant.basic_info.display_name
        vo.remaining_members = group_purchase.member_coupon_count - len(group_purchase.group_members)
        vo.active_time = group_purchase.active_time
        vo.status = group_purchase_pb.GroupPurchase.GroupPurchaseStatus.Name(group_purchase.status)
        return vo

    def __convert_group_purchase_invitations_to_vo(self, group_purchase_invitations):
        result = []
        for g in group_purchase_invitations:
            result.append(self.__convert_group_purchase_invitation_to_vo(g))
        return result

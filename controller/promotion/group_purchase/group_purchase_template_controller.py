# -*- coding: utf-8 -*-

"""团购模板控制器模块."""

from google.protobuf import json_format

import proto.page.group_purchase_pb2 as page_group_purchase_pb
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from controller.base_controller import BaseController
from service import errors


class GroupPurchaseTemplateController(BaseController):

    def _cinit(self, *args, **kargs):
        self._OP_FUNCS.update({
            "get_merchant_group_purchase_template_list": self.get_merchant_group_purchase_template_list
        })
        self._group_purchase_template_manager = GroupPurchaseTemplateManager(
            merchant_id=self.get_json_param('merchantId'))
        self._coupon_template_manager = CouponTemplateManager()

    def get(self):
        template = self._group_purchase_template_manager.get_group_purchase_template(
            id=self.get_json_param("groupPurchaseTemplateId")
        )
        return template

    def create(self):
        template = self._group_purchase_template_manager.create_group_purchase_template()
        self.__update(template)
        return template

    def update(self):
        template = self.get()
        if template is None:
            raise errors.ShowError("团购模板不存在")
        self.__update(template)
        return template

    def list(self):
        templates = self._group_purchase_template_manager.get_group_purchase_template_list(
            ids=self.get_json_param("groupPurchaseTemplateIds")
        )
        return self.convert_group_purchase_templates_to_vo(templates)

    def get_merchant_group_purchase_template_list(self):
        merchant = self._group_purchase_template_manager.merchant
        if not merchant:
            return []
        templates = self._group_purchase_template_manager.get_group_purchase_template_list(
            ids=list(merchant.group_purchase_template_ids)
        )
        return self.convert_group_purchase_templates_to_vo(templates)

    def convert_group_purchase_template_to_vo(self, group_purchase_template):
        template = json_format.MessageToDict(group_purchase_template)
        group_purchase_template_vo = json_format.ParseDict(
            template,
            page_group_purchase_pb.GroupPurchaseTemplateVO(),
            ignore_unknown_fields=True
        )
        for opening_coupon_template_id in group_purchase_template.opening_coupon_template_ids:
            coupon_template = self._coupon_template_manager.get_coupon_template(
                id=opening_coupon_template_id)
            group_purchase_template_vo.leader_reduce_fees.append(
                self._coupon_template_manager.get_coupon_template_reduce_fee(0, coupon_template)
            )
        for closing_coupon_template_id in group_purchase_template.closing_coupon_template_ids:
            coupon_template = self._coupon_template_manager.get_coupon_template(
                id=closing_coupon_template_id)
            group_purchase_template_vo.return_cash_fees.append(
                self._coupon_template_manager.get_coupon_template_reduce_fee(0, coupon_template)
            )
        return group_purchase_template_vo

    def convert_group_purchase_templates_to_vo(self, group_purchase_templates):
        result = []
        for group_purchase_template in group_purchase_templates:
            result.append(self.convert_group_purchase_template_to_vo(group_purchase_template))
        return result

    def __update(self, group_purchase_template):
        if group_purchase_template is None:
            return
        self._group_purchase_template_manager.update_group_purchase_template(
            group_purchase_template,
            name=self.get_json_param('name'),
            added_opening_coupon_template_ids=self.get_json_param('addedOpeningCouponTemplateIds'),
            removed_opening_coupon_template_ids=self.get_json_param('removedOpeningCouponTemplateIds'),
            opening_coupon_template_ids=self.get_json_param('openingCouponTemplateIds'),
            added_closing_coupon_template_ids=self.get_json_param('addedClosingCouponTemplateIds'),
            removed_closing_coupon_template_ids=self.get_json_param('removedClosingCouponTemplateIds'),
            closing_coupon_template_ids=self.get_json_param('closingCouponTemplateIds'),
            member_coupon_template_id=self.get_json_param('memberCouponTemplateId'),
            member_coupon_count=self.get_json_param('memberCouponCount'),
            member_coupon_min_bill_fee=self.get_json_param('memberCouponMinBillFee'),
            member_coupon_reduce_value=self.get_json_param('memberCouponReduceValue'),
            total_value=self.get_json_param('totalValue'),
            sell_price=self.get_json_param('sellPrice'),
            valid_period=self.get_json_param('validPeriod')
        )
        self._group_purchase_template_manager.add_or_update_group_purchase_template(
            group_purchase_template)

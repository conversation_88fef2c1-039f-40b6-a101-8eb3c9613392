import proto.page.account_pb2 as page_account_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.user_asset_account_manager import UserAssetAccountManager
from controller.base_controller import BaseController
from dao.transaction_da_helper import TransactionDataAccessHelper


class UserAssetAccountController(BaseController):

    def _cinit(self, *args, **kargs):
        self._user_asset_account_manager = UserAssetAccountManager(
            user_id=self.get_header_param('userId')
        )
        self.__transaction_da_helper = TransactionDataAccessHelper()

    def get(self):
        result = page_account_pb.UserAssetAccount()
        account = self._user_asset_account_manager.account
        result.owner_id = account.owner_id
        result.vip_membership.CopyFrom(account.vip_membership)
        result.wallet_balance = account.balance
        result.coin_balance = account.coin_balance
        result.pending_wallet_balance = self.__get_pending_wallet_balance(account)
        return result

    def __get_pending_wallet_balance(self, account):
        if account is None:
            return 0
        transactions = self.__transaction_da_helper.get_transactions(
            payer_id=account.owner_id,
            state=wallet_pb.Transaction.PENDING
        )
        balance = sum([t.paid_fee for t in transactions])
        return balance

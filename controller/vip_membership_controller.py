# -*- coding: utf-8 -*-

from flask import request

from business_ops.vip_membership_manager import VipMembershipManager
from controller.base_controller import BaseController


class  VipMembershipController(BaseController):

    def _cinit(self, *args, **kargs):
        self._OP_FUNCS.update({
            'subscribe': self.subscribe,
        })

    def subscribe(self):
        manager = VipMembershipManager(user_id=self.get_header_param("userId"))
        manager.subscribe()

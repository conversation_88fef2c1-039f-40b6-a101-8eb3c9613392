# -*- coding: utf-8 -*-

import time

from flask import request

from dao.merchant_da_helper import MerchantDataAccessHelper
import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from business_ops.vip_membership_manager import VipMembershipManager
from controller.base_controller import BaseController
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.wallet_da_helper import WalletDataAccessHelper


class WalletController(BaseController):
    def _cinit(self, *args, **kargs):
        self._OP_FUNCS.update(
            {'get_user_coin_details': self.get_user_coin_details, 'get_user_wallet_details': self.get_user_wallet_details}
        )
        self._coupon_manager = CouponManager(user_id=request.headers.get('userId'))
        self.user = self._coupon_manager.user
        self._ordering_da = OrderingServiceDataAccessHelper()
        self._transaction_da = TransactionDataAccessHelper()
        self._group_purchase_manager = GroupPurchaseManager(user=self.user)
        self._merchant_da = MerchantDataAccessHelper()
        self._merchant_map = {}

    def get_user_coin_details(self):
        """用户时运币明细"""
        if not self.user:
            return []
        result = []
        result.extend(self.__coin_deduction_details())
        result.extend(self.__coin_obtain_details())
        return result

    def get_user_wallet_details(self):
        transactions = self._transaction_da.query_list(
            matcher={
                '$or': [
                    {'payerId': self.user.id, 'state': 'ORDERED', 'createTime': {'$gt': '1672675200'}, 'type': 'CASH_WITHDRAW'},
                    {'$or': [{'payerId': self.user.id}, {'payeeId': self.user.id}], 'state': 'SUCCESS'},
                ]
            }
        )

        group_purchases = self._group_purchase_manager.get_group_purchase_list(
            with_leader=True, status=group_purchase_pb.GroupPurchase.FINISHED
        )
        result = []
        for transaction in transactions:
            self.__set_user_wallet_details_by_transaction(result, transaction)
        for group_purchase in group_purchases:
            self.__set_user_wallet_details_by_group_purchase(result, group_purchase)
        result.sort(key=lambda x: x.get("finishTime"), reverse=True)
        return result

    def __set_user_wallet_details_by_transaction(self, result, transaction):
        self.__set_user_wallet_cash_withdraw(result, transaction)
        self.__set_user_wallet_ordering(result, transaction)
        self.__set_user_wallet_vip_membership_recharge(result, transaction)
        self.__set_user_wallet_red_packet_deduction(result, transaction)
        self.__set_user_wallet_red_packet_deposit(result, transaction)
        self.__set_user_wallet_activity_bonus(result, transaction)
        self.__set_user_wallet_fanpiao_balance_withdraw(result, transaction)

    def __set_user_wallet_fanpiao_balance_withdraw(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(
            wallet_pb.Transaction.FANPIAO_BALANCE_WITHDRAW
        ) or transaction.get('payMethod') != wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WALLET):
            return
        result.append(
            {
                "id": transaction.get('id', ''),
                "finishTime": transaction.get('paidTime', 0),
                "fee": int(transaction.get('paidFee', 0)),
                "lockFee": 0,
                "desc": "饭票退款",
                "type": "FANPIAO_BALANCE_WITHDRAW",
            }
        )

    def __set_user_wallet_activity_bonus(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(
            wallet_pb.Transaction.ACTIVITY_BONUS
        ) or transaction.get('payMethod') != wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WALLET):
            return
        fee = int(transaction.get('billFee', 0))
        result.append(
            {
                "id": transaction.get('id', ''),
                "finishTime": transaction.get('paidTime', 0),
                "fee": fee,
                "lockFee": 0,
                "desc": "零钱存入" if fee >= 0 else "零钱扣除",
                "type": "ACTIVITY_BONUS" if fee >= 0 else 'DEDUCTION_BONUS',
            }
        )

    def __set_user_wallet_red_packet_deposit(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(
            wallet_pb.Transaction.RED_PACKET_DEPOSIT
        ) or transaction.get('payMethod') != wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WALLET):
            return
        merchant_name = self._merchant_map.get(transaction.get('payerId'))
        if not merchant_name:
            merchant = self._merchant_da.query_one(
                matcher={'id': transaction.get('payerId')}, project={'id': 1, 'basicInfo.name': 1}
            )
            merchant_name = merchant.get('basicInfo', {}).get('name')
            self._merchant_map.update({merchant.get('id'): merchant_name})
        result.append(
            {
                "id": transaction.get('id', ''),
                "finishTime": transaction.get('paidTime', 0),
                "fee": 0,
                "lockFee": abs(int(transaction.get('billFee', 0))),
                "desc": "订单返现红包",
                "type": "RED_PACKET_DEPOSIT",
                'merchantName': merchant_name,
            }
        )

    def __set_user_wallet_red_packet_deduction(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(
            wallet_pb.Transaction.RED_PACKET_DEDUCTION
        ) or transaction.get('payMethod') != wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WALLET):
            return
        merchant_name = self._merchant_map.get(transaction.get('payeeId'))
        if not merchant_name:
            merchant = self._merchant_da.query_one(
                matcher={'id': transaction.get('payeeId')}, project={'id': 1, 'basicInfo.name': 1}
            )
            merchant_name = merchant.get('basicInfo', {}).get('name')
            self._merchant_map.update({merchant.get('id'): merchant_name})
        result.append(
            {
                "id": transaction.get('id'),
                "finishTime": transaction.get('paidTime', 0),
                "fee": 0,
                "lockFee": -abs(int(transaction.get('billFee', 0))),
                "desc": "订单退款红包扣除",
                "type": "RED_PACKET_DEDUCTION",
                'merchantName': merchant_name,
            }
        )

    def __set_user_wallet_vip_membership_recharge(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE):
            return
        merchant_name = self._merchant_map.get(transaction.get('payeeId'))
        if not merchant_name:
            merchant = self._merchant_da.query_one(
                matcher={'id': transaction.get('payeeId')}, project={'id': 1, 'basicInfo.name': 1}
            )
            merchant_name = merchant.get('basicInfo', {}).get('name')
            self._merchant_map.update({merchant.get('id'): merchant_name})
        result.append(
            {
                "id": transaction.get('id', ''),
                "finishTime": transaction.get('paidTime', 0),
                "fee": abs(int(transaction.get('billFee', 0))),
                "lockFee": 0,
                "desc": "时享会员存钱",
                "type": "VIP_MEMBERSHIP_RECHARGE",
                "merchantName": merchant_name,
            }
        )

    def __set_user_wallet_cash_withdraw(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(wallet_pb.Transaction.CASH_WITHDRAW):
            return
        if transaction.get('paidFee', 0) == 0:
            return
        finish_time = transaction.get('paidTime', 0)
        if transaction.get('state') == wallet_pb.Transaction.TransactionState.Name(wallet_pb.Transaction.ORDERED):
            finish_time = transaction.get('createTime', 0)
        _result = {
            "id": transaction.get('id'),
            "finishTime": finish_time,
            "fee": abs(int(transaction.get('paidFee', 0))) * (-1),
            "lockFee": 0,
            "desc": "提现至微信零钱",
            "type": "CASH_WITHDRAW",
            "status": transaction.get('state'),
        }
        if transaction.get('payMethod') == wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.ALIPAY) or (
            transaction.get('payMethod') == wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.TIAN_QUE_PAY)
            and transaction.pay_channel == wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.ALIPAY_CHANNEL)
        ):
            _result.update({"desc": "提现至支付宝零钱"})
        result.append(_result)

    def __set_user_wallet_ordering(self, result, transaction):
        if transaction.get('type') != wallet_pb.Transaction.TransactionType.Name(wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT):
            return
        if int(transaction.get('paidFee', 0)) == 0:
            return
        if transaction.get('payMethod') != wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WALLET):
            return
        order = self._ordering_da.query_one(
            matcher={
                "transactionId": transaction.get('id'),
                'status': dish_pb.DishOrder.OrderStatus.Name(dish_pb.DishOrder.PAID),
            },
            project={'id': 1, 'lockFee': 1},
        )
        if not order:
            return
        merchant_name = self._merchant_map.get(transaction.get('payeeId'))
        if not merchant_name:
            merchant = self._merchant_da.query_one(
                matcher={'id': transaction.get('payeeId')}, project={'id': 1, 'basicInfo.name': 1}
            )
            merchant_name = merchant.get('basicInfo', {}).get('name')
            self._merchant_map.update({merchant.get('id'): merchant_name})
        result.append(
            {
                "id": transaction.get('id'),
                "finishTime": transaction.get('paidTime', 0),
                "fee": abs(int(transaction.get('paidFee'))) * -1,
                "lockFee": abs(int(order.get('lockFee', 0))) * -1,
                "desc": "零钱消费",
                "type": "WALLET_CONSUME",
                "merchantName": merchant_name,
            }
        )

    def __set_user_wallet_details_by_group_purchase(self, result, group_purchase):
        fee = 0
        for group_member in group_purchase.group_members:
            if group_member.user_id == self.user.id:
                continue
            fee += group_purchase.member_coupon_min_bill_fee
        result.append(
            {
                "id": group_purchase.id,
                "finishTime": group_purchase.finish_time,
                "fee": fee,
                "lockFee": 0,
                "desc": "拼团本金入账",
                "type": "GROUP_PURCHASE_FINISHED",
            }
        )
        return result

    def __coin_obtain_details(self):
        result = []
        coin_obtain_coupons = self._coupon_manager.get_coupon_list(
            coupon_type=coupon_pb.Coupon.CLOSING_GROUP_PURCHASE,
            status=coupon_pb.Coupon.USED,
            reduce_fee_type=coupon_pb.COIN_REFUND,
        )
        for coupon in coin_obtain_coupons:
            group_purchase_manager = GroupPurchaseManager(user=self.user)
            group_purchase = group_purchase_manager.get_group_purchase(closing_coupon_id=coupon.id)
            if not group_purchase:
                continue
            result.append(
                {
                    "memberCouponMinBillFee": group_purchase.member_coupon_min_bill_fee,
                    "memberCouponCount": group_purchase.member_coupon_count,
                    "value": self._coupon_manager.get_coupon_reduce_fee_from_coupon(coupon),
                    "useTime": group_purchase.finish_time,
                    "type": "GROUP_PURCHASE_COIN_BACK",
                    "groupPurchaseId": group_purchase.id,
                    "desc": "成团返现",
                    "id": group_purchase.id,
                }
            )
        return result

    def __coin_deduction_details(self):
        coin_deduction_coupons = self._coupon_manager.get_coupon_list(
            coupon_type=coupon_pb.Coupon.COIN_DEDUCTION, status=coupon_pb.Coupon.USED
        )
        result = []
        for coupon in coin_deduction_coupons:
            order = self._ordering_da.get_order(new_coupon_id=coupon.id)
            if not order:
                continue
            result.append(
                {
                    "id": order.id,
                    "orderId": order.transaction_id,
                    "value": self._coupon_manager.get_coupon_reduce_fee_from_coupon(coupon),
                    "useTime": order.paid_time,
                    "type": "SELF_DISH_ORDER_PAYMENT",
                    "desc": "订单抵扣",
                }
            )
        return result

[uwsgi]
set-placeholder = service_name=main_service
set-placeholder = version_dir=main_service
http = 0.0.0.0:12901

strict = true
chdir = $(HOME)/code/%(version_dir)/service
virtualenv = $(HOME)/code/env
module = %(service_name)
callable = app
processes = 32
master = true
threads = 1
daemonize = $(LOG_ROOT)/%(version_dir)/uwsgi.log
pidfile = $(LOG_ROOT)/%(version_dir)/uwsgi.pid
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1
listen = 2048

env = PYTHONPATH=$(HOME)/code/%(version_dir)
env = DEPLOYMENT_ENV=$(DEPLOYMENT_ENV)
env = LOG_DIR=$(LOG_ROOT)/%(version_dir)
env = VERSION=

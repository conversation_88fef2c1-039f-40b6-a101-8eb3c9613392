[uwsgi]
set-placeholder = service_name=merchant_assist_service
http = 0.0.0.0:7250

strict = true
chdir = $(HOME)/code/%(service_name)/service
virtualenv = $(HOME)/code/env
module = %(service_name)
callable = app
processes = 4
master = true
threads = 1
daemonize = $(LOG_ROOT)/%(service_name)/uwsgi.log
pidfile = $(LOG_ROOT)/%(service_name)/uwsgi.pid
vacuum = true
reload-mercy = 1
worker-reload-mercy = 1
listen = 2048

env = PYTHONPATH=$(HOME)/code/%(service_name)
env = DEPLOYMENT_ENV=$(DEPLOYMENT_ENV)
env = LOG_DIR=$(LOG_ROOT)/%(service_name)
env = VERSION=

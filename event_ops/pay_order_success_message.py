# -*- coding: utf-8 -*-

"""
Filename: pay_order_success_message.py
Date: 2020-07-10 16:28:30
Title: 扫码点餐支付成功通知
"""

from common.utils import access_token_helper
from event_ops.wx_template_send import WXThePublicTemplateSend


class PayOrderSuccessMessage(WXThePublicTemplateSend):
    def __init__(self, *args, **kargs):
        super(PayOrderSuccessMessage, self).__init__()

    def send_pay_order_success_message(self, touser, appid, pay, address, time, remark):
        template_id = "74RP5cBWAbY-uQEoBcZNpMwb_y1paIOvHYGyOdOB5aU"
        data = {
            "pay": pay,
            "address": address,
            "time": time,
            "remark": remark
        }
        params = self.build_params(touser, template_id, data, appid)
        self.send(params)

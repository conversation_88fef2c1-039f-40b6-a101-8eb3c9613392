from dao.auth_da_helper import AuthDataAccessHelper

import proto.authorizer_pb2 as authorizer_pb

# 处理授权推送事件
class AuthEventHandler():

    def handle_component_verify_ticket_info(self, decrypt_data):
        """
            接收 component_verify_ticket 推送
            在第三方平台创建审核通过后，微信服务器会向其“授权事件接收URL”每隔10分钟定时推送component_verify_ticket
        """
        appid = decrypt_data['AppId']
        component_verify_ticket = decrypt_data['ComponentVerifyTicket']

        pb_ticket = authorizer_pb.ComponentVerifyTicket()
        pb_ticket.appid = appid
        pb_ticket.component_verify_ticket = component_verify_ticket
        AuthDataAccessHelper().set_component_verify_ticket(pb_ticket)

    def handle_authorized_info(self, decrypt_data):
        """
            处理授权成功通知
        """

    def handle_unauthorized_info(self, decrypt_data):
        """
            处理取消授权通知
        """

    def handle_update_authorized_info(self, decrypt_data):
        """
            处理授权更新通知
        """

import json
import requests
import logging
import time

from common.utils import access_token_helper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common.config import config

# 上传素材,获得永久素材media_id
# curl -F media=@zjwc.jpg https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=ACCESS_TOKEN
# dB475RqAaV6jizemimqnlc9okAAlZWRYbobCT1qrFUE


logger = logging.getLogger(__name__)


class ScanCodeOrderHandler():

    def __init__(self, key, open_id):
        self.key = key
        self.open_id = open_id

    def handle_scan_code_event(self, decrypt_data):
        table = OrderingServiceDataAccessHelper().get_table(id=self.key)
        logger.info("self.key: {}, table: {}".format(self.key, table))
        if table:
            merchant_id = table.merchant_id
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
            appid = merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
            token = access_token_helper.get_authorizer_access_token(appid)
            thumb_media_id = merchant.shilai_platform_authorizer_info.authorization_info.thumb_media_id
            # thumb_media_id = 'dB475RqAaV6jizemimqnlc9okAAlZWRYbobCT1qrFUE'
            url = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={}'.format(token)

            datas = {
                "touser": self.open_id,
                "msgtype": "miniprogrampage",
                "miniprogrampage":
                {
                    "title": "自助点餐，优惠多多！点击开始下单 (桌号: {})".format(table.name),
                    # "appid": "wxdd5cafec95f6cc46",  # 时来饭票
                    # "appid": "wxaa3c47ef72452be7",  # 时来时享
                    "appid": config.wechat_miniprogram_appid,
                    "pagepath": "package-merchant/menu?scene={}&timestamp={}".format(table.id, int(time.time())),
                    "thumb_media_id": thumb_media_id
                }
            }
            data = json.dumps(datas, ensure_ascii=False).encode('utf-8')
            r = requests.post(url, data=data)
            logger.info("handle_scan_code_event: {}".format(r.text))
            logger.info("handle_scan_code_event: {}".format(datas))

    def handle_subscribe_event(self, decrypt_data):
        key = self.key.split('_')
        self.key = key[1]
        self.handle_scan_code_event(decrypt_data)

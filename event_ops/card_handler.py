from business_ops.merchant_manager import MerchantManager

import proto.merchant_rules_pb2 as merchant_rules_pb

# 处理卡券推送事件
class CardEventHandler():

    def handle_card_pass_event(self, decrypt_data):
        """
            处理卡券审核通过事件
        """

    def handle_card_not_pass_event(self, decrypt_data):
        """
            处理卡券审核未通过事件
        """

    def handle_user_get_card_event(self, decrypt_data):
        """
            处理用户领取卡券事件
        """

    def handle_user_gifting_card_event(self, decrypt_data):
        """
            处理用户转赠卡券事件
        """

    def handle_user_delete_card_event(self, decrypt_data):
        """
            处理用户删除卡券事件
        """

    def handle_user_consume_card_event(self, decrypt_data):
        """
            处理用户核销卡券事件
        """

    def handle_user_pay_from_pay_cell_event(self, decrypt_data):
        """
            处理用户买单事件
            通过调用买单接口设置，此 Card 必须配置了门店才能使用
        """

    def handle_user_view_card_event(self, decrypt_data):
        """
            处理用户访问会员卡事件
            需要设置卡券参数 need_push_on_view: true
        """

    def handle_user_enter_session_from_card(self, decrypt_data):
        """
            处理用户从卡券进入公众号会话事件
        """

    def handle_update_member_card_event(self, decrypt_data):
        """
            处理会员卡参数更新事件
        """

    def handle_card_sku_remind_event(self, decrypt_data):
        """
            处理库存警报事件
            初始库存数大于200且当前库存小于等于100时，用户尝试领券会触发发送事件给商户，事件每隔12h发送一次
        """

    def handle_card_pay_order_event(self, decrypt_data):
        """
            处理商户朋友的券券点发生变动事件
        """

    def handle_submit_membercard_user_info_event(self, decrypt_data):
        """
            处理会员卡激活事件
            当用户通过一键激活的方式提交信息并点击激活或者用户修改会员卡信息后，会收到该事件推送
        """

    def handle_card_submerchant_check_result_event(self, decrypt_data):
        """
            时来公众号代理子商户审核事件
        """
        submerchant_id = decrypt_data['MerchantId']
        is_pass = decrypt_data['IsPass']
        rejected_reason = decrypt_data['Reason'] if 'Reason' in decrypt_data else None

        if is_pass == 1:
            status = merchant_rules_pb.ShilaiMPSubmerchantInfo.APPROVED
        else:
            status = merchant_rules_pb.ShilaiMPSubmerchantInfo.REJECTED

        MerchantManager().update_shilai_mp_submerchant_audit_status(submerchant_id=submerchant_id,
                                                                    status=status,
                                                                    rejected_reason=rejected_reason)

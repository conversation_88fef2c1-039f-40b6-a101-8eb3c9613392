# -*- coding: utf-8 -*-

import requests
import json
import logging

"""
Filename: wx_template_send.py
Date: 2020-07-10 16:31:46
Title: 微信公众号模板消息
"""

"""
https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html
{
           "touser":"OPENID",
           "template_id":"ngqIpbwh8bUfcSsECmogfXcV14J0tQlEpBO27izEYtY",
           "url":"http://weixin.qq.com/download",
           "miniprogram":{
             "appid":"xiaochengxuappid12345",
             "pagepath":"index?foo=bar"
           },
           "data":{
                   "first": {
                       "value":"恭喜你购买成功！",
                       "color":"#173177"
                   },
                   "keyword1":{
                       "value":"巧克力",
                       "color":"#173177"
                   },
                   "keyword2": {
                       "value":"39.8元",
                       "color":"#173177"
                   },
                   "keyword3": {
                       "value":"2014年9月22日",
                       "color":"#173177"
                   },
                   "remark":{
                       "value":"欢迎再次购买！",
                       "color":"#173177"
                   }
           }
       }
"""

from common.utils import access_token_helper
from common.config import config

logger = logging.getLogger(__name__)


class WXThePublicTemplateSend:
    def __init__(self, *args, **kargs):
        self.appid = kargs.get("appid")
        if self.appid is None:
            self.appid = config.WECHAT_MINIPROGRAM_APPID

    def build_params(
            self, touser, template_id, data, appid=None, pagepath=None, url=None):
        params = {"data": data}
        params.update({
            "touser": touser,
            "template_id": template_id
        })
        if url is not None:
            params.update({"url": url})
        if appid is not None and pagepath is not None:
            if appid is None:
                appid = config.WECHAT_MINIPROGRAM_APPID
            params.update({
                "miniprogram": {
                    "appid": appid,
                    "pagepath": pagepath
                }
            })
        return params

    def send(self, params):
        access_token = access_token_helper.get_authorizer_access_token(self.appid)
        # url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={}".format(access_token)
        url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}".format(access_token)
        data = json.dumps(params, ensure_ascii=False).encode('utf-8')
        try:
            r = requests.post(url, data=data).json()
        except:
            return False
        logger.info("WXThePublicTemplateSend: {}".format(r))
        if r.get("errcode") == 0:
            return True
        return False

# -*- coding: utf-8 -*-

"""
Filename: pay_order_success_message.py
Date: 2020-07-10 16:28:30
Title: 扫码点餐支付成功通知
"""

from event_ops.wx_template_send import WXThePublicTemplateSend


class ActivityMessageSubscribe(WXThePublicTemplateSend):
    def __init__(self, *args, **kargs):
        super(ActivityMessageSubscribe, self).__init__(*args, **kargs)

    def send_fanpiao_snap_up_message(self, merchant, touser):
        template_id = "DkBqsWy6NfMX9NfMsZwClKXSerGHZwwONRy5eDEBhy8"
        data = {
            "thing1": {"value": "饭票秒杀大放「价」"},
            "date2": {"value": "2021-04-26 11:00:00"},
            "thing4": {"value": merchant.basic_info.display_name},
            "time5": {"value": "2021-04-30 24:00:00"},
            "thing6": {"value": "到店扫码点餐即可抢购超值饭票！"}
        }
        params = super(ActivityMessageSubscribe, self).build_params(
            touser=touser,
            template_id=template_id,
            data=data
        )
        return self.send(params)

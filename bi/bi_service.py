# -*- coding: UTF-8 -*-
import sys
import os
import time
from datetime import timedelta
from threading import Thread, Lock

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common
service_common.set_environment_var('prod')

from bs4 import BeautifulSoup
from urllib import parse
from flask import Flask
from flask import redirect
from flask import request
from flask import Markup

from bi import coupon_analyzer
from bi import group_dining_analyzer
from bi import payment_analyzer
from bi.ordering import order_analyzer
from common.utils import date_utils

app = Flask(__name__)

mutex = Lock()
REPORT_FILE = '/tmp/bi_report.txt'

@app.route('/merchants/daily_report', methods=['GET'])
def get_merchants_daily_report():
  mutex.acquire()
  try:
    with open(REPORT_FILE, 'r') as file:
      return Markup(file.read())
  finally:
    mutex.release()

def generate_report():
  now = date_utils.datetime_now_in_timezone()
  # now = now - timedelta(days=1)
  start_time = now.replace(hour=0, minute=0, second=0, microsecond=0).timestamp()
  start_time = int(start_time)
  report = '上次更新时间: {}\n\n\n'.format(now.strftime('%Y-%m-%d %H:%M:%S'))
  report += payment_analyzer.generate_platform_payment_report(start_time=start_time)
  report += '\n\n\n'
  report += order_analyzer.generate_platform_order_report(start_time=start_time)
  report += '\n\n\n'
  report += coupon_analyzer.generate_coupon_package_purchase_report(start_time=start_time)
#  report += group_dining_analyzer.generate_platform_group_dining_report(start_time=start_time)
  report += '\n\n\n'
  report += coupon_analyzer.generate_fanpiao_purchase_report(start_time=start_time)
  report += '\n\n\n'
  report += payment_analyzer.generate_active_merchant_payment_reports(start_time=start_time)
  report = '<text style="font-family: Arial, Helvetica, sans-serif; font-size:12px;">' + report.replace('\n', '<br>').replace(' ', '&nbsp') + '</text>'
  mutex.acquire()
  try:
    with open(REPORT_FILE, 'w') as file:
      file.write(report)
  finally:
    mutex.release()

def update_report():
  last_update_time = 0
  while True:
    current_time = date_utils.timestamp_second()
    if current_time - last_update_time > 180:
      last_update_time = date_utils.timestamp_second()
      try:
        generate_report()
      except Exception as e:
        print(e)
    else:
      time.sleep(1)


if __name__ == "__main__":
    t = Thread(target=update_report)
    t.start()

    app.config.update(DEBUG=True)
    app.run(host="0.0.0.0", port=8600)
import proto.coupons_pb2 as coupons_pb
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

WECHAT_MOMENT_ADS_MINIPROGRAM_ID = 1045
WECHAT_MOMENT_ADS_CHANNEL_ID = 1084

# TODO: 目前的漏斗分析非常粗糙，对于很多的转化路径流程及相关渠道来源的各种埋点信息，目前还很不完善，
#       未来有关漏斗分析的数据采集框架仍需不断迭代和改进。
def generate_funnel_analysis_report(coupon_category_id, join_method=WECHAT_MOMENT_ADS_CHANNEL_ID,
    start_time=None, end_time=None):
    issued_state_count = 0
    accepted_state_count = 0
    used_state_count = 0
    expired_state_count = 0

    users = UserDataAccessHelper().get_users(join_method=join_method, join_start_time=start_time,
        join_end_time=end_time)
    user_count = len(users)
    member_count = 0
    for user in users:
        if user.HasField('member_profile'):
            member_count += 1
        coupons = CouponDataAccessHelper().get_coupon_list(user_id=user.id,
            coupon_category_id=coupon_category_id)
        if coupons:
            # TODO: 目前数据信息还不能很好区分新老用户点击广告的行为以作出准确的统计分析，
            # 以及每张券被领取的渠道，所以只能先模糊处理
            coupon = coupons[-1]
            if coupon.state == coupons_pb.Coupon.ISSUED:
                issued_state_count += 1
            elif coupon.state == coupons_pb.Coupon.ACCEPTED:
                accepted_state_count += 1
            elif coupon.state == coupons_pb.Coupon.USED:
                used_state_count += 1
            elif coupon.state == coupons_pb.Coupon.EXPIRED:
                expired_state_count += 1

    report = '************ 朋友圈广告转化漏斗分析 ************\n'
    report += '进入小程序人数: {}\n'.format(user_count)
    report += '领取优惠券人数: {}\n'.format(accepted_state_count + used_state_count + expired_state_count)
    report += '注册会员人数:   {}\n'.format(member_count)
    report += '核销优惠券人数: {}\n'.format(used_state_count)

    return report

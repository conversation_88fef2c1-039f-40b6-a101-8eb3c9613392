import os
import sys
import time
from datetime import datetime

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

import proto.bi.common_pb2 as bi_common_pb
import proto.bi.group_dining_stats_pb2 as group_dining_stats_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.group_dining_pb2 as group_dining_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.merchant_rules_pb2 as merchant_pb
from bi import common_util
from common.config import config
from common.utils import date_utils
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper


def get_platform_group_dining_stats(start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    stats = group_dining_stats_pb.PlatformGroupDiningStats()
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()

    period_stats_dict = {}
    authentic_aa_dining_events = {}
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    transaction_da = TransactionDataAccessHelper()
    group_dining_events = GroupDiningDataAccessHelper().get_dining_events(create_start_time=start_time,
        create_end_time=end_time, state=group_dining_pb.GroupDiningEvent.PAID)
    for event in group_dining_events:
        # 忽略超级商家："智易科技"
        if event.merchant_id == '********************************':
            continue

        transaction = transaction_da.get_transaction_by_id(event.transaction_id)
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=event.id)
        if not (transaction and red_packet):
            continue
        stats.total_red_packet_count += len(red_packet.value_assignments)
        stats.total_drawn_red_packet_count += len(red_packet.drawn_users)

        period_time_str = date_utils.get_datetime_in_timezone(transaction.paid_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if not period_time_str in period_stats_dict:
            period_stats = group_dining_stats_pb.PeriodGroupDiningStats()
            period_stats.period_start_time = period_time_str
            period_stats_dict[period_time_str] = period_stats

        period_stats_dict[period_time_str].total_bill_fee += transaction.bill_fee
        period_stats_dict[period_time_str].total_paid_fee += transaction.paid_fee
        period_stats_dict[period_time_str].total_coupon_discount += transaction.bill_fee - transaction.paid_fee
        period_stats_dict[period_time_str].total_red_packet_fee += red_packet.total_value
        period_stats_dict[period_time_str].total_merchant_received_fee += transaction.paid_fee - red_packet.total_value
        period_stats_dict[period_time_str].group_dining_count += 1

        stats.group_dining_count += 1
        if event.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            stats.aa_group_dining_count += 1
            period_stats_dict[period_time_str].aa_group_dining_count += 1
        elif event.payment_rule == group_dining_pb.GroupDiningEvent.TREATMENT:
            stats.treatment_group_dining_count += 1
            period_stats_dict[period_time_str].treatment_group_dining_count += 1

        # 统计参与饭局用户复购数据
        accepted_invitations = InvitationDataAccessHelper().get_invitations(dining_id=event.id,
            state=group_dining_pb.Invitation.ACCEPTED)
        for invitation in accepted_invitations:
            if invitation.invitee_id in stats.user_group_dining_count_map:
                stats.user_group_dining_count_map[invitation.invitee_id] += 1
            else:
                stats.user_group_dining_count_map[invitation.invitee_id] = 1

            if invitation.inviter_id != invitation.invitee_id and invitation.transaction_id:
                authentic_aa_dining_events[event.id] = 1

        stats.total_bill_fee += transaction.bill_fee
        stats.total_paid_fee += transaction.paid_fee
        stats.total_coupon_discount += transaction.bill_fee - transaction.paid_fee
        stats.total_red_packet_fee += red_packet.total_value
        stats.total_merchant_received_fee += transaction.paid_fee - red_packet.total_value

    stats.authentic_aa_group_dining_count = len(authentic_aa_dining_events)

    if stats.group_dining_count > 0:
        stats.avg_bill_fee = int(stats.total_bill_fee / stats.group_dining_count)

    for time_str in sorted(period_stats_dict.keys()):
        stats.period_stats.add().CopyFrom(period_stats_dict[time_str])

    # 统计AA付款总额
    aa_transfers = transaction_da.get_transactions(type=wallet_pb.Transaction.GROUP_DINING_TRANSFER,
        state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time)
    for transfer in aa_transfers:
      stats.total_group_dining_transfer_fee += transfer.paid_fee
    # 统计提现总额
    cash_withdraws = transaction_da.get_transactions(type=wallet_pb.Transaction.CASH_WITHDRAW,
        state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time)
    for withdraw in cash_withdraws:
      stats.total_cash_withdraw_fee += withdraw.paid_fee

    return stats

def generate_report_from_platform_group_dining_stats(group_dining_stats):
    # 新会员红包统计
    # TODO: 新会员红包不属于组局约饭场景，放这里一起统计纯粹为了方便
    new_member_red_packets = RedPacketDataAccessHelper().get_red_packets(
        create_start_time=group_dining_stats.stats_start_time,
        create_end_time=group_dining_stats.stats_end_time,
        issue_scene=red_packet_pb.RedPacket.NEW_MEMBER)
    total_new_member_bill_fee = 0
    total_new_member_red_packet_value = 0
    total_drawn_count = 0
    for red_packet in new_member_red_packets:
        total_new_member_red_packet_value += red_packet.total_value
        transaction = TransactionDataAccessHelper().get_transaction_by_id(red_packet.new_member_transaction_id)
        total_new_member_bill_fee += transaction.bill_fee
        total_drawn_count += len(red_packet.drawn_users)

    report = '************ 时来平台饭局报表 ************\n'
    time_format = common_util.get_time_format_by_time_granularity(group_dining_stats.time_granularity)
    start_time_str = date_utils.get_datetime_in_timezone(group_dining_stats.stats_start_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    end_time_str = date_utils.get_datetime_in_timezone(group_dining_stats.stats_end_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    report += '统计起止时间:    {} ~ {}\n'.format(start_time_str, end_time_str)
    report += '总饭局数:        {}局\n'.format(group_dining_stats.group_dining_count)
    report += 'AA饭局数:        {}局\n'.format(group_dining_stats.aa_group_dining_count)
    report += '实际AA付款饭局数:  {}局\n'.format(group_dining_stats.authentic_aa_group_dining_count)
    report += '请客局数:        {}局\n'.format(group_dining_stats.treatment_group_dining_count)
    report += '总交易金额:      {}元\n'.format(group_dining_stats.total_bill_fee / 100)
    report += '平均客单价:      {}元\n'.format(group_dining_stats.avg_bill_fee / 100)
    report += '总优惠金额:      {}元\n'.format(group_dining_stats.total_coupon_discount / 100)
    report += '饭局红包金额:    {}元\n'.format(group_dining_stats.total_red_packet_fee / 100)
    report += '总饭局红包数:    {}个\n'.format(group_dining_stats.total_red_packet_count)
    report += '饭局红包抽取数:   {}个\n'.format(group_dining_stats.total_drawn_red_packet_count)
    report += '新会员总交易金额: {}元\n'.format(total_new_member_bill_fee / 100)
    report += '新会员红包金额:  {}元\n'.format(total_new_member_red_packet_value / 100)
    report += '新会员红包总数:  {}个\n'.format(len(new_member_red_packets))
    report += '新会员红包抽取数:  {}个\n'.format(total_drawn_count)
    report += '总实收金额:      {}元\n'.format(group_dining_stats.total_merchant_received_fee / 100)
    report += '总AA付款金额:    {}元\n'.format(group_dining_stats.total_group_dining_transfer_fee / 100)
    report += '总提现金额:      {}元\n'.format(group_dining_stats.total_cash_withdraw_fee / 100)
    report += '结余金额:        {}元\n'.format(
        (group_dining_stats.total_red_packet_fee + total_new_member_red_packet_value + \
         group_dining_stats.total_group_dining_transfer_fee - group_dining_stats.total_cash_withdraw_fee) / 100)

    report += '\n每日饭局统计:\n'
    report += '  {: <12}{: <8}{: <8}{: <8}{: <8}{: <8}\n'.format('日期',
        '总饭局数', 'AA饭局数', '请客局数', '总交易金额', '总红包金额')
    for daily_stats in group_dining_stats.period_stats:
        report += '  {: <15}{: <12}{: <11}{: <11}{: <13}{: <12}\n'.format(daily_stats.period_start_time,
            daily_stats.group_dining_count, daily_stats.aa_group_dining_count,
            daily_stats.treatment_group_dining_count, daily_stats.total_bill_fee / 100,
            daily_stats.total_red_packet_fee / 100)

    report += '\n用户参加饭局频率统计:\n'
    one_time_diner_count = 0
    for user_id in sorted(group_dining_stats.user_group_dining_count_map,
        key=group_dining_stats.user_group_dining_count_map.get, reverse=True):
        if group_dining_stats.user_group_dining_count_map[user_id] == 1:
            one_time_diner_count += 1
        else:
            report += '{: >3}次:   {} ({})\n'.format(group_dining_stats.user_group_dining_count_map[user_id],
                user_id, common_util.get_user_nickname(user_id))
    report += '\n  仅参加1次饭局用户数: {}\n'.format(one_time_diner_count)

    return report

def generate_platform_group_dining_report(start_time=None, end_time=None):
    stats = get_platform_group_dining_stats(start_time=start_time, end_time=end_time)
    return generate_report_from_platform_group_dining_stats(stats)

def get_merchant_group_dining_stats(merchant_id, start_time=None, end_time=None):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not merchant:
        return None
    if not merchant.stores[0].enable_group_dining:
        return None

    group_dining_events = GroupDiningDataAccessHelper().get_dining_events(merchant_id=merchant_id,
        create_start_time=start_time, create_end_time=end_time, state=group_dining_pb.GroupDiningEvent.PAID)

    group_dining_stats = group_dining_stats_pb.GroupDiningStats()
    if start_time:
        group_dining_stats.stats_start_time = start_time
    if end_time:
        group_dining_stats.stats_end_time = end_time
    group_dining_stats.merchant_id = merchant_id
    group_dining_stats.store_id = merchant.stores[0].id

    for event in group_dining_events:
        transaction = TransactionDataAccessHelper().get_transaction_by_id(event.transaction_id)
        if not transaction:
            print('$$$$$ 告警 $$$$$\n找不到与饭局[{}]相关的交易记录[{}]'.format(event.id, event.transaction_id))
            continue

        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=event.id)
        if not red_packet:
            print('$$$$$ 告警 $$$$$\n找不到与饭局[{}]相关的红包记录'.format(event.id))
            continue

        group_dining_stats.total_bill_fee += transaction.bill_fee
        group_dining_stats.total_paid_fee += transaction.paid_fee
        group_dining_stats.total_red_packet_fee += red_packet.total_value
        group_dining_stats.total_merchant_received_fee = transaction.paid_fee - red_packet.total_value
        group_dining_stats.group_dining_count += 1
        if event.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            group_dining_stats.aa_group_dining_count += 1
        else:
            group_dining_stats.treatment_group_dining_count += 1

    return group_dining_stats

def generate_report():
    print('{: <32}{: <8}{: <8}{: <8}{: <8}{: <8}\n'.format('商户',
        '总饭局数', 'AA饭局数', '请客局数', '总交易金额', '总红包金额'))
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if merchant.id == '********************************':
            continue
        if merchant.stores and not merchant.stores[0].enable_group_dining:
            continue
        if not (merchant.settlement_platform == merchant_pb.Merchant.SHILAI_ACCOUNT or \
            merchant.wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT or \
            merchant.stores[0].wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT):
            continue

        start_time = None
        end_time = None
        stats = get_merchant_group_dining_stats(merchant.id, start_time=start_time, end_time=end_time)
        if not stats:
            continue
        print('{: <32}{: <12}{: <11}{: <11}{: <13}{: <12}'.format(merchant.basic_info.display_name,
            stats.group_dining_count, stats.aa_group_dining_count,
            stats.treatment_group_dining_count, stats.total_bill_fee / 100,
            stats.total_red_packet_fee / 100))

def generate_report_from_merchant_group_dining_stats(group_dining_stats):
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(group_dining_stats.merchant_id)
    if not merchant:
        return None

    report = '************ {}饭局报表 ************\n'.format(merchant.basic_info.name)
    report += '总饭局数:   {}组   AA局: {}组    请客局: {}组\n'.format(group_dining_stats.group_dining_count,
        group_dining_stats.aa_group_dining_count, group_dining_stats.treatment_group_dining_count)
    report += '总账单金额:   {}元\n'.format(group_dining_stats.total_bill_fee / 100)
    report += '总优惠金额:   {}元\n'.format((group_dining_stats.total_bill_fee - group_dining_stats.total_paid_fee) / 100)
    report += '用户总实付金额:   {}元\n'.format(group_dining_stats.total_paid_fee / 100)
    report += '总红包金额:   {}元\n'.format(group_dining_stats.total_red_packet_fee / 100)
    report += '商家总实收金额:   {}元\n'.format((group_dining_stats.total_paid_fee - group_dining_stats.total_red_packet_fee) / 100)

    return report

def generate_merchant_group_dining_report(merchant_id, start_time=None, end_time=None):
    stats = get_merchant_group_dining_stats(merchant_id, start_time=start_time, end_time=end_time)
    if stats:
        return generate_report_from_merchant_group_dining_stats(stats)
    else:
        return ''

def get_group_dining_events():
    merchant_da = MerchantDataAccessHelper()
    transaction_da = TransactionDataAccessHelper()

    dining_events = GroupDiningDataAccessHelper().get_dining_events(
        state=group_dining_pb.GroupDiningEvent.PAID)
    for event in dining_events:
        # 忽略超级商家："智易科技"
        if event.merchant_id == '********************************':
            continue

        transaction = transaction_da.get_transaction_by_id(event.transaction_id)
        if not transaction or transaction.paid_fee < 100:
            continue
        merchant = merchant_da.get_merchant(event.merchant_id)
        if not merchant:
            continue

        print('{}    {}    {: <24}    {}    {: <18}    {}'.format(event.id,
            time.strftime("%D %H:%M", time.localtime(transaction.paid_time)), merchant.basic_info.display_name,
            transaction.paid_fee / 100, common_util.get_staff_nickname(merchant.binding_staff_id),
            common_util.get_user_nickname(event.initiator_id)) )

def generate_daily_group_dining_report():
    start_time = None
    end_time = None

    merchant_dining_events = {}
    merchant_da = MerchantDataAccessHelper()
    group_dining_transactions = TransactionDataAccessHelper().get_transactions(state=wallet_pb.Transaction.SUCCESS,
        start_time=start_time, end_time=end_time, type=wallet_pb.Transaction.GROUP_DINING_PAYMENT)
    for transaction in group_dining_transactions:
        # 忽略超级商家："智易科技"
        if transaction.payee_id == '********************************':
            continue

        merchant = merchant_da.get_merchant(transaction.payee_id)
        merchant_name = merchant.basic_info.display_name
        if merchant_name in merchant_dining_events:
            merchant_dining_events[merchant_name].append(transaction)
        else:
            merchant_dining_events[merchant_name] = [transaction]

    for merchant_name in merchant_dining_events.keys():
        print('********** {}饭局报表 **********\n'.format(merchant_name))
        print('时间            饭局交易金额        饭局发起人\n'.format(merchant_name))

        transactions = sorted(merchant_dining_events[merchant_name], key=lambda t: t.paid_time)
        for transaction in transactions:
            print('{}    {}    {}\n'.format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(transaction.paid_time)),
                transaction.paid_fee / 100, common_util.get_user_nickname(transaction.payer_id)))

        print('\n\n\n')

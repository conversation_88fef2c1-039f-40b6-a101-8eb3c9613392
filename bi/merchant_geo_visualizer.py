import os
import folium
import folium.plugins as plugins
import math
import random

import numpy as np
from pymongo import MongoClient

ALL_FOOD_CATEGORIES = {
  '25474': '农家菜',
  '107': '台湾菜',
  '132': '咖啡厅',
  '34055': '早茶',
  '311': '北京菜',
  '3243': '新疆菜',
  '207': '茶餐厅',
  '6743': '云贵菜',
  '2714': '水果生鲜',
  '219': '小龙虾',
  '250': '创意菜',
  '34014': '下午茶',
  '33759': '食品保健',
  '34032': '人气餐厅',
  '101': '本帮江浙菜',
  '102': '川菜',
  '103': '粤菜',
  '104': '湘菜',
  '508': '烧烤',
  '106': '东北菜',
  '1959': '粥粉面',
  '34284': '特色菜',
  '109': '素食',
  '110': '火锅',
  '111': '自助餐',
  '112': '小吃快餐',
  '113': '日本菜',
  '114': '韩国料理',
  '115': '东南亚菜',
  '116': '西餐',
  '117': '面包甜点',
  '118': '其他美食',
  '1783': '家常菜',
  '34059': '福建菜',
  '1338': '私房菜',
  '251': '江河湖海鲜',
  '34236': '饮品店',
  '26481': '西北菜',
  '252': '其他中餐',
  '32731': '潮汕牛肉火锅',
  '210': '快餐简餐',
  '224': '日本料理',
  '4473': '烤鱼',
  '33924': '寿司',
  '34245': '面包烘焙',
  '34243': '海鲜',
  '27823': '东北家常菜',
  '32708': '炸鸡炸串',
  '254': '牛排',
  '34278': '海鲜火锅',
  '34237': '茶饮果汁',
  '34257': '苏浙菜',
  '32728': '大排档',
  '1785': '烤鸭',
  '34285': '水果店',
  '34289': '更多食品保健',
  '32723': '韩式烤肉',
  '34246': '甜品',
  '34234': '陕西菜',
  '26484': '山西菜',
  '226': '日式简餐/快餐',
  '733': '客家菜',
  '4467': '川菜家常菜',
  '34283': '广州菜',
  '205': '粤菜馆',
  '34286': '生鲜',
  '34224': '京菜',
  '1947': '潮汕菜',
  '34279': '炭火锅',
  '4447': '融合烤肉',
  '4449': '烤串',
  '4569': '栗子/干果',
  '4317': '卤味鸭脖',
  '4465': '熟食熏酱',
  '3027': '鱼火锅',
  '32733': '重庆火锅',
  '223': '零食',
  '4509': '馄饨/饺子',
  '32729': '涮涮锅',
  '4583': '酸菜鱼/水煮鱼',
  '4481': '江湖菜',
  '4273': '汤锅',
  '241': '冰淇淋',
  '4305': '衢州菜',
  '32730': '猪肚鸡火锅',
  '232': '意大利菜',
  '2808': '顺德菜',
  '105': '贵州菜|黔菜',
  '3023': '四川火锅',
  '34282': '粤式茶点',
  '221': '麻辣烫',
  '4581': '南京菜',
  '225': '日式烧烤/烤肉',
  '4469': '干锅/香锅',
  '238': '西式简餐',
  '247': '江西菜',
  '2919': '烧腊',
  '246': '湖北菜',
  '217': '小吃',
  '34040': '日式面条',
  '227': '泰国菜',
  '3017': '串串香',
  '34240': '闽南菜',
  '203': '大闸蟹',
  '248': '云南菜|滇菜',
  '1797': '浙菜',
  '34065': '焖锅',
  '1805': '羊蝎子火锅',
  '4557': '生煎/锅贴',
  '25151': '日式自助',
  '34223': '南洋中菜',
  '34242': '肉蟹煲',
  '34238': '酸奶',
  '34273': '烤羊腿',
  '34062': '日韩火锅',
  '34041': '日式铁板烧',
  '1387': '广西菜',
  '3233': '江鲜',
  '206': '燕翅鲍',
  '4601': '炖菜',
  '4477': '小火锅',
  '231': '法国菜',
  '26483': '鲁菜',
  '34293': '花甲',
  '34295': '黄焖鸡',
  '201': '淮扬菜',
  '26482': '徽菜',
  '32712': '澳门豆捞',
  '34294': '桂林米粉',
  '33918': '韩式小吃',
  '1453': '内蒙菜',
  '4597': '骨头馆',
  '1881': '包子',
  '34244': '湖鲜',
  '2243': '苏帮菜',
  '34241': '闽西菜',
  '2920': '保健品',
  '4303': '温州菜',
  '253': '拉美烧烤',
  '208': '牛羊肉火锅',
  '1909': '无锡菜',
  '34277': '老北京火锅',
  '4351': '福州菜',
  '3031': '自贡盐帮菜',
  '34296': '烧鸡',
  '34063': '打边炉/港式火锅',
  '2821': '日式火锅',
  '2887': '本地鸡窝',
  '34233': '甘肃菜',
  '2823': '泰式火锅',
  '34275': '虾蟹火锅',
  '3095': '菌菇火锅',
  '34280': '云南火锅',
  '34255': '重庆鸡公煲',
  '4593': '烤翅',
  '32713': '芋儿鸡',
  '24340': '比萨',
  '2923': '牛杂',
  '4571': '湛江菜',
  '230': '越南菜',
  '4505': '其他东南亚菜',
  '34276': '地锅鸡',
  '32726': '腊排骨火锅',
  '32727': '黑山羊火锅',
  '2913': '炖品',
  '1817': '粉面馆',
  '1819': '饺子',
  '34256': '上海本帮菜',
  '1811': '老北京小吃',
  '213': '馄饨',
  '1787': '官府菜',
  '34274': '炙子烤肉',
}

INTERESTED_FOOD_CATEGORIES = {
  '219': '小龙虾',
  '34032': '人气餐厅',
  '102': '川菜',
  '104': '湘菜',
  '508': '烧烤',
  '34284': '特色菜',
  '110': '火锅',
  '112': '小吃快餐',
  '113': '日本菜',
  '114': '韩国料理',
  '116': '西餐',
  '251': '江河湖海鲜',
  '34236': '饮品店',
  '32731': '潮汕牛肉火锅',
  '210': '快餐简餐',
  '224' : '日本料理',
  '4473' : '烤鱼',
  '33924': '寿司',
  '34243': '海鲜',
  '32708': '炸鸡炸串',
  '254' : '牛排',
  '34278': '海鲜火锅',
  '34237': '茶饮果汁',
  '32723': '韩式烤肉',
  '34246': '甜品',
  '226' : '日式简餐/快餐',
  '34279': '炭火锅',
  '4447' : '融合烤肉',
  '4449' : '烤串',
  '3027' : '鱼火锅',
  '32733': '重庆火锅',
  '32729': '涮涮锅',
  '4583' : '酸菜鱼/水煮鱼',
  '4481' : '江湖菜',
  '4273' : '汤锅',
  '32730': '猪肚鸡火锅',
  '3023' : '四川火锅',
  '221'  : '麻辣烫',
  '225'  : '日式烧烤/烤肉',
  '4469' : '干锅/香锅',
  '238'  : '西式简餐',
  '3017' : '串串香',
  '34242': '肉蟹煲',
  '34062': '日韩火锅',
  '34041': '日式铁板烧',
  '34274': '炙子烤肉',
}

ASSIGNEES = [
  {'name': '罗单', 'color': 'red'},
  {'name': '江华堂', 'color': 'orange'},
  {'name': '左亚军', 'color': 'blue'},
  {'name': '易锦', 'color': 'purple'},
]

SHILAI_SCORES = [
  {'name': '高', 'group_name': '高价值商户', 'color': 'red'},
  {'name': '中', 'group_name': '中价值商户', 'color': 'blue'},
  {'name': '低', 'group_name': '低价值商户', 'color': 'gray'},
]

POPUP_HTML_TEMPLATE = """
    <h4>{merchant_name}</h4>
    <h5><strong>类别:</strong>&nbsp&nbsp {category}</h5>
    <h5><strong>人均:</strong>&nbsp&nbsp {avg_cost}元/人</h5>
    <h5><strong>总评:</strong>&nbsp&nbsp {stars}星</h5>
    <h5><strong>口味:</strong>&nbsp&nbsp {taste}</h5>
    <h5><strong>环境:</strong>&nbsp&nbsp {environment}</h5>
    <h5><strong>服务:</strong>&nbsp&nbsp {service}</h5>
    <h5><strong>评论条数:</strong>&nbsp&nbsp {comment_count}条</h5>
    <h5><strong>套餐数量:</strong>&nbsp&nbsp {combo_meal_count}</h5>
    <h5><strong>时来评级:</strong>&nbsp&nbsp {shilai_score}</h5>
    <h5><strong>时来委任:</strong>&nbsp&nbsp {assignee}</h5>
"""

DATABASE_NAME = 'S2_spider'
COLLECTION_NAME = 'merchant_detail'

def get_mongo_client():
    return MongoClient(host='*************', port=27017, username='all_ro', password='bi4321.')

def print_food_categories():
    DATABASE_NAME = 'S2_spider'
    COLLECTION_NAME = 'food_classifications'

    mongo_client = get_mongo_client()
    db = mongo_client[DATABASE_NAME]
    categories = db[COLLECTION_NAME].find()
    print('ID         Category')
    for cat in categories:
        print("{}: '{}',".format(cat['id'], cat['name']))


def transform_lat_lng(lat, lng):
    """ 国内地理API返回的经纬度通常有偏移，需要矫正。
    """
    # TODO: 这里只用了很粗糙的拟合，需改进
    return [lat + 0.0025, lng - 0.0045]

def get_shilai_merchant_score_index(merchant):
    total_score = 0
    # 评论数量1分
    if int(merchant['commentCount']) > 100:
        total_score += 1
    # 图片数量1分
    if int(merchant['picCount']) > 100:
        total_score += 1
    # 总评分2分
    if merchant['scores']['overall'] / 10 > 4.0:
        total_score += 2
    elif merchant['scores']['overall'] / 10 > 3.0:
        total_score += 1
    # 口味1分
    if merchant['scores']['taste'] / 10 > 7:
        total_score += 1
    # 环境1分
    if merchant['scores']['environment'] / 10 > 7:
        total_score += 1
    # 服务1分
    if merchant['scores']['service'] / 10 > 7:
        total_score += 1
    # TODO: 代金券1分

    # 套餐1分
    if len(merchant['dealId']) > 0:
        total_score += 1
    # 排行榜1分
    if len(merchant['rankings']) > 0:
        total_score += 1

    if total_score >= 7:
        return 0
    elif total_score >= 4:
        return 1
    else:
        return 2

def generate_summary_map():
    mongo_client = get_mongo_client()
    db = mongo_client[DATABASE_NAME]
    merchants = db[COLLECTION_NAME].find({
      # 'cityPinyin': 'shenzhen',
      'region': '罗湖区',
    })

    test_map = folium.Map(location=[22.5222301483154, 113.937103271484], zoom_start=13, tiles='OpenStreetMap')

    feature_groups = {}
    marker_clusters = {}
    for obj in SHILAI_SCORES:
        feature_groups[obj['name']] = folium.FeatureGroup(name=obj['group_name'])
        marker_clusters[obj['name']] = plugins.MarkerCluster()

    for merchant in merchants:
        if not 'poi' in merchant or not 'location' in merchant['poi']:
            continue

        category_id = merchant['category']
        if category_id not in ALL_FOOD_CATEGORIES:
             continue

        assignee_index = random.randint(0, len(ASSIGNEES) - 1)
        score_index = get_shilai_merchant_score_index(merchant)
        popup_html = POPUP_HTML_TEMPLATE.format(merchant_name=merchant['name'], category=ALL_FOOD_CATEGORIES[category_id],
            avg_cost=merchant['avgPrice'],
            stars=merchant['scores']['overall'] / 10, taste=merchant['scores']['taste'] / 10,
            environment=merchant['scores']['environment'] / 10, service=merchant['scores']['service'] / 10,
            comment_count=merchant['commentCount'], combo_meal_count=len(merchant['dealId']),
            shilai_score=SHILAI_SCORES[score_index]['name'], assignee=ASSIGNEES[assignee_index]['name'])

        lat_lng = transform_lat_lng(merchant['poi']['location']['lat'], merchant['poi']['location']['lng'])
        marker = folium.Marker(lat_lng, popup=folium.Popup(html=popup_html, max_width=450),
            icon = folium.Icon(color=SHILAI_SCORES[score_index]['color'],icon='ok-sign'))
        marker_clusters[SHILAI_SCORES[score_index]['name']].add_child(marker)

    for obj in SHILAI_SCORES:
      feature_groups[obj['name']].add_child(marker_clusters[obj['name']])
      test_map.add_child(feature_groups[obj['name']])

    test_map.add_child(folium.LayerControl())
    test_map.save('/Users/<USER>/Temp/summary_map.html')

def generate_category_map():
    mongo_client = get_mongo_client()
    db = mongo_client[DATABASE_NAME]
    merchants = db[COLLECTION_NAME].find({
      # 'cityPinyin': 'shenzhen',
      'region': '罗湖区',
    })

    test_map = folium.Map(location=[22.5222301483154, 113.937103271484], zoom_start=13, tiles='OpenStreetMap')

    feature_groups = {}
    marker_clusters = {}
    for key in INTERESTED_FOOD_CATEGORIES.keys():
        feature_groups[key] = folium.FeatureGroup(name=INTERESTED_FOOD_CATEGORIES[key])
        marker_clusters[key] = plugins.MarkerCluster()

    for merchant in merchants:
        if not 'poi' in merchant or not 'location' in merchant['poi']:
            continue

        category_id = merchant['category']
        if category_id not in INTERESTED_FOOD_CATEGORIES:
            continue

        assignee_index = random.randint(0, len(ASSIGNEES) - 1)
        score_index = get_shilai_merchant_score_index(merchant)
        popup_html = POPUP_HTML_TEMPLATE.format(merchant_name=merchant['name'], category=ALL_FOOD_CATEGORIES[category_id],
            avg_cost=merchant['avgPrice'],
            stars=merchant['scores']['overall'] / 10, taste=merchant['scores']['taste'] / 10,
            environment=merchant['scores']['environment'] / 10, service=merchant['scores']['service'] / 10,
            comment_count=merchant['commentCount'], combo_meal_count=len(merchant['dealId']),
            shilai_score=SHILAI_SCORES[score_index]['name'], assignee=ASSIGNEES[assignee_index]['name'])

        lat_lng = transform_lat_lng(merchant['poi']['location']['lat'], merchant['poi']['location']['lng'])
        marker = folium.Marker(lat_lng, popup=folium.Popup(html=popup_html, max_width=450),
            icon = folium.Icon(color=SHILAI_SCORES[score_index]['color'],icon='ok-sign'))
        marker_clusters[category_id].add_child(marker)

    for key in INTERESTED_FOOD_CATEGORIES.keys():
      #if marker_clusters[key]:
      feature_groups[key].add_child(marker_clusters[key])
      test_map.add_child(feature_groups[key])

    test_map.add_child(folium.LayerControl())
    test_map.save('/Users/<USER>/Temp/category_map.html')

def generate_assignee_map():
    mongo_client = get_mongo_client()
    db = mongo_client[DATABASE_NAME]
    merchants = db[COLLECTION_NAME].find({
      # 'cityPinyin': 'shenzhen',
      'region': '罗湖区',
    })

    test_map = folium.Map(location=[22.5222301483154, 113.937103271484], zoom_start=13, tiles='OpenStreetMap')
    # test_map.add_tile_layer()

    feature_groups = {}
    marker_clusters = {}
    for obj in ASSIGNEES:
        feature_groups[obj['name']] = folium.FeatureGroup(name=obj['name'])
        marker_clusters[obj['name']] = plugins.MarkerCluster()

    for merchant in merchants:
        if not 'poi' in merchant or not 'location' in merchant['poi']:
            continue

        category_id = merchant['category']
        if category_id not in ALL_FOOD_CATEGORIES:
             continue

        assignee_index = random.randint(0, len(ASSIGNEES) - 1)
        score_index = get_shilai_merchant_score_index(merchant)
        popup_html = POPUP_HTML_TEMPLATE.format(merchant_name=merchant['name'], category=ALL_FOOD_CATEGORIES[category_id],
            avg_cost=merchant['avgPrice'],
            stars=merchant['scores']['overall'] / 10, taste=merchant['scores']['taste'] / 10,
            environment=merchant['scores']['environment'] / 10, service=merchant['scores']['service'] / 10,
            comment_count=merchant['commentCount'], combo_meal_count=len(merchant['dealId']),
            shilai_score=SHILAI_SCORES[score_index]['name'], assignee=ASSIGNEES[assignee_index]['name'])

        lat_lng = transform_lat_lng(merchant['poi']['location']['lat'], merchant['poi']['location']['lng'])
        marker = folium.Marker(lat_lng, popup=folium.Popup(html=popup_html, max_width=450),
            icon = folium.Icon(color=ASSIGNEES[assignee_index]['color'],icon='ok-sign'))
        marker_clusters[ASSIGNEES[assignee_index]['name']].add_child(marker)

    for obj in ASSIGNEES:
      feature_groups[obj['name']].add_child(marker_clusters[obj['name']])
      test_map.add_child(feature_groups[obj['name']])

    test_map.add_child(folium.LayerControl())
    test_map.save('/Users/<USER>/Temp/assignee_map.html')


if __name__ == '__main__':
    generate_assignee_map()
    generate_category_map()
    generate_summary_map()

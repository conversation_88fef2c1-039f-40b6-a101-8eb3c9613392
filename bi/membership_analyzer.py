import operator
from datetime import datetime

import proto.bi.common_pb2 as bi_common_pb
import proto.bi.membership_stats_pb2 as membership_stats_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.finance.wallet_pb2 as wallet_pb
from bi import common_util
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

def get_platform_membership_stats(start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    """生成平台会员相关统计数据

    Args:
        start_time: (int64) 查询开始时间
        end_time: (int64) 查询结束时间
        time_granularity: (bi_common_pb.TimeGranularity) 时间细粒度

    Returns:
        membership_stats_pb.PlatformMembershipStats结构体
    """
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    period_new_members = {}
    subscription_stats = {}
    users = UserDataAccessHelper().get_users(join_start_time=start_time, join_end_time=end_time)
    total_member_count = 0
    membership_da = MembershipDataAccessHelper()
    for user in users:
        if not user.HasField('member_profile'):
            continue

        total_member_count += 1
        # 统计每周期平台新增会员数
        date_str = date_utils.get_datetime_in_timezone(user.joined_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if date_str in period_new_members:
            period_new_members[date_str] += 1
        else:
            period_new_members[date_str] = 1

        # 统计该用户一共加入了多少商家成为会员
        membercards = membership_da.get_member_cards(user_id=user.id)
        if not membercards:
            continue
        if len(membercards) in subscription_stats:
            subscription_stats[len(membercards)] += 1
        else:
            subscription_stats[len(membercards)] = 1

    stats = membership_stats_pb.PlatformMembershipStats()
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()

    stats.total_member_count = total_member_count
    for period_time in sorted(period_new_members.keys()):
        period_stats = stats.period_stats.add()
        period_stats.period_start_time = period_time
        period_stats.new_member_count = period_new_members[period_time]

    for merchant_count in sorted(subscription_stats.keys(), reverse=True):
        stats.subscription_stats[merchant_count] = subscription_stats[merchant_count]
    return stats

def generate_report_from_platform_membership_stats(platform_stats):
    report = '************ 时来平台会员运营报表 ************\n'
    time_format = common_util.get_time_format_by_time_granularity(platform_stats.time_granularity)
    start_time_str = date_utils.get_datetime_in_timezone(platform_stats.stats_start_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    end_time_str = date_utils.get_datetime_in_timezone(platform_stats.stats_end_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    report += '统计起止时间: {} ~ {}\n'.format(start_time_str, end_time_str)
    report += '平台会员总数: {}\n'.format(platform_stats.total_member_count)
    report += '每日新增会员数:\n'
    for period_stats in platform_stats.period_stats:
        report += '  {}  {: <5} {}\n'.format(period_stats.period_start_time,
            period_stats.new_member_count, '+' * int(period_stats.new_member_count / 10))
    report += '\n##### 用户注册商家数量统计 #####\n'
    for merchant_count in sorted(platform_stats.subscription_stats, reverse=True):
        report += '{}个商家: {}人\n'.format(merchant_count, platform_stats.subscription_stats[merchant_count])
    return report

def generate_platform_membership_report(start_time=None, end_time=None):
    platform_stats = get_platform_membership_stats(start_time=start_time, end_time=end_time)
    return generate_report_from_platform_membership_stats(platform_stats)

def get_merchant_membership_stats(merchant_id, start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    """获取商户会员数据

    Args:
        merchant_id: (string) 商户ID
        start_time: (int64) 查询开始时间
        end_time: (int64) 查询结束时间
        time_granularity: (bi_common_pb.TimeGranularity) 时间粒度
    """
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_id)
    if not merchant:
        return None

    membership_da = MembershipDataAccessHelper()
    member_cards = membership_da.get_member_cards(merchant_id=merchant_id,
                                   activate_start_time=start_time,
                                   activate_end_time=end_time)

    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    period_new_members = {}
    for card in member_cards:
        date_str = date_utils.get_datetime_in_timezone(card.initial_activate_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if date_str in period_new_members:
            period_new_members[date_str] += 1
        else:
            period_new_members[date_str] = 1

    stats = membership_stats_pb.MerchantMembershipStats()
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()
    stats.merchant_id = merchant_id
    stats.total_member_count = len(member_cards)
    for stat_date in sorted(period_new_members.keys()):
        period_stats = stats.period_stats.add()
        period_stats.period_start_time = stat_date
        period_stats.new_member_count = period_new_members[stat_date]
    return stats

def generate_report_from_merchant_membership_stats(membership_stats):
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(membership_stats.merchant_id)
    if not merchant:
        return None

    report = '************ {}会员报表 ************\n'.format(merchant.basic_info.name)
    report += '商家会员总数: {}\n'.format(membership_stats.total_member_count)
    report += '每日新增会员数:\n'
    for daily_stats in membership_stats.period_stats:
        report += '  {}  {: <5} {}\n'.format(daily_stats.period_start_time,
            daily_stats.new_member_count, '+' * daily_stats.new_member_count)

    return report

def generate_merchant_membership_report(merchant_id, start_time=None, end_time=None):
    merchant_stats = get_merchant_membership_stats(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time)
    return generate_report_from_merchant_membership_stats(merchant_stats)

def get_merchant_member_list(merchant_id, start_time=None, end_time=None):
    user_da = UserDataAccessHelper()
    type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT]
    transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
                state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time,
                type_list=type_list)

    total_member_count = 0
    phone_member_count = 0
    user_map = {}
    user_obj_map = {}
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
    print('正在统计{}会员手机号...\n'.format(merchant.basic_info.name))
    report = '********** {}会员手机号列表 **********\n'.format(merchant.basic_info.name)
    report += '用户ID                                手机号          复购次数      昵称\n'
    for transaction in transactions:
        user = user_da.get_user(transaction.payer_id)
        if not user:
            continue
        if user.id in user_map:
            user_map[user.id] += 1
            continue

        user_map[user.id] = 1
        user_obj_map[user.id] = user
        total_member_count += 1
        if not user.member_profile or not user.member_profile.mobile_phone:
            continue
        phone_member_count += 1

    for user_id, trans_count in sorted(user_map.items(), key=operator.itemgetter(1), reverse=True):
        user = user_obj_map[user_id]
        if not user.member_profile or not user.member_profile.mobile_phone:
            continue
        report += '{: <40}{: <16}{: <12}{}\n'.format(user.id, user.member_profile.mobile_phone,
            trans_count, user.wechat_profile.nickname)

    report += '总会员数: {}\n手机号会员数: {}\n'.format(total_member_count, phone_member_count)
    return report
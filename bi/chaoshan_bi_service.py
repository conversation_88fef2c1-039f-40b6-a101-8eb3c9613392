# -*- coding: utf-8 -*-

import sys
import os
import time
from datetime import timedelta
from threading import Thread, Lock

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common
service_common.set_environment_var('prod')

from bs4 import BeautifulSoup
from urllib import parse
from flask import Flask
from flask import redirect
from flask import request
from flask import Markup

import proto.merchant_rules_pb2 as merchant_pb
from bi import group_dining_analyzer
from bi import payment_analyzer
from bi.ordering import order_analyzer
from common.utils import date_utils
from dao.merchant_da_helper import MerchantDataAccessHelper

app = Flask(__name__)

mutex = Lock()
REPORT_FILE = '/tmp/chaoshan_bi_report.txt'
STAFF_LIST = [
  'd24eef88-330b-43d2-ae43-6ab550d6d279',
  'b818f6a2-088b-4724-9833-d35cfe016db5',
  '8a00d32a-8a6a-479d-a7a9-7259b0a57ed0',
  '49f9f91e-2e3c-4fd8-80ca-fba9cfbfefb2',
]

@app.route('/merchants/chaoshan_daily_report', methods=['GET'])
def get_merchants_daily_report():
  mutex.acquire()
  try:
    with open(REPORT_FILE, 'r') as file:
      return Markup(file.read())
  finally:
    mutex.release()

def generate_report():
  now = date_utils.datetime_now_in_timezone()
  # now = now - timedelta(days=1)
  start_time = now.replace(hour=0, minute=0, second=0, microsecond=0).timestamp()
  start_time = int(start_time)
  report = '********** 潮灿地区商户每日营业报表 **********\n'
  report += '上次更新时间: {}\n\n\n'.format(now.strftime('%Y-%m-%d %H:%M:%S'))

  merchants = []
  for staff_id in STAFF_LIST:
    res = MerchantDataAccessHelper().get_merchant_list_bound_to_staff(staff_id=staff_id, status=merchant_pb.RUNNING)
    if res:
      merchants.extend(res)

  for merchant in merchants:
    merchant_report = order_analyzer.get_merchant_order_report(merchant_id=merchant.id, start_time=start_time)
    if merchant_report:
      report += merchant_report
      report +='\n\n\n'

  report = '<text style="font-family: Arial, Helvetica, sans-serif; font-size:12px;">' + report.replace('\n', '<br>').replace(' ', '&nbsp') + '</text>'

  mutex.acquire()
  try:
    with open(REPORT_FILE, 'w') as file:
      file.write(report)
  finally:
    mutex.release()

def update_report():
  last_update_time = 0
  while True:
    current_time = date_utils.timestamp_second()
    if current_time - last_update_time > 180:
      last_update_time = date_utils.timestamp_second()
      try:
        generate_report()
      except Exception as e:
        print(e)
    else:
      time.sleep(1)


if __name__ == "__main__":
    t = Thread(target=update_report)
    t.start()

    app.config.update(DEBUG=True)
    app.run(host="0.0.0.0", port=8601)

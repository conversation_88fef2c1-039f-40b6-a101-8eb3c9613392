import time
import logging
from datetime import datetime
from datetime import timedelta
from multiprocessing import Pool

from google.protobuf import json_format

from bi import common_util
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from common.config import config
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.transfer_store_transaction_da_helper import TransferStoreTransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
import proto.bi.common_pb2 as bi_common_pb
import proto.bi.payment_stats_pb2 as payment_stats_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.coupons_pb2 as coupons_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb

logger = logging.getLogger(__name__)

def get_platform_payment_stats(start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    stats = payment_stats_pb.PlatformPaymentStats()
    period_paid_amounts = {}
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
        wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]
    payments = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
        start_time=start_time, end_time=end_time, type_list=type_list)
    for payment in payments:
        merchant = MerchantDataAccessHelper().get_merchant(payment.payee_id)
        if merchant.id == '********************************':
            continue

        stats.bill_count += 1
        stats.total_bill_fee += payment.bill_fee
        stats.total_paid_fee += payment.paid_fee
        stats.total_reduce_fee += payment.bill_fee - payment.paid_fee
        real_paid_fee = get_real_paid_fee(payment)
        stats.total_refund_fee += max(0, payment.paid_fee - real_paid_fee)
        if payment.use_coupon_id:
            stats.total_coupon_bill_count += 1
            stats.total_coupon_bill_fee += payment.bill_fee
            stats.total_coupon_paid_fee += payment.paid_fee
            stats.total_coupon_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        if payment.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            stats.total_fanpiao_bill_count += 1
            stats.total_fanpiao_bill_fee += payment.bill_fee
            stats.total_fanpiao_paid_fee += payment.paid_fee
            stats.total_fanpiao_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        period_time_str = date_utils.get_datetime_in_timezone(payment.paid_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if period_time_str in period_paid_amounts:
            period_paid_amounts[period_time_str].total_bill_fee += payment.bill_fee
            period_paid_amounts[period_time_str].total_paid_fee += payment.paid_fee
            period_paid_amounts[period_time_str].bill_count += 1
        else:
            period_stats = create_period_payment_stats(period_time_str, payment.bill_fee, payment.paid_fee)
            period_paid_amounts[period_time_str] = period_stats

    stats.total_discount_fee = stats.total_reduce_fee + stats.total_refund_fee
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()

    for period_str in sorted(period_paid_amounts.keys()):
        stats.period_stats.add().CopyFrom(period_paid_amounts[period_str])
    return stats

def generate_report_from_platform_payment_stats(platform_payment_stats):
    report = '************ 时来平台交易数据统计 ************\n'
    time_format = common_util.get_time_format_by_time_granularity(platform_payment_stats.time_granularity)
    start_time_str = date_utils.get_datetime_in_timezone(platform_payment_stats.stats_start_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    end_time_str = date_utils.get_datetime_in_timezone(platform_payment_stats.stats_end_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    report += '统计起止时间: {} ~ {}\n'.format(start_time_str, end_time_str)
    report += '总账单数:   {}笔\n'.format(platform_payment_stats.bill_count)
    report += '总账单金额: {}元\n'.format(platform_payment_stats.total_bill_fee / 100)
    report += '总实付金额: {}元\n'.format(platform_payment_stats.total_paid_fee / 100)
    report += '总立减金额: {}元\n'.format(platform_payment_stats.total_reduce_fee / 100)
    report += '总返现金额: {}元\n'.format(platform_payment_stats.total_refund_fee / 100)
    report += '折扣总额:   {}元\n'.format((platform_payment_stats.total_discount_fee) / 100)
    if platform_payment_stats.total_bill_fee > 0:
        report += '总折扣率:   {0:.2f}%\n'.format(platform_payment_stats.total_discount_fee * 100 / platform_payment_stats.total_bill_fee)
    report += '用券买单帐单数: {}笔\n'.format(platform_payment_stats.total_coupon_bill_count)
    report += '用券买单总金额: {}元\n'.format(platform_payment_stats.total_coupon_bill_fee / 100)
    report += '用券实付总金额: {}元\n'.format(platform_payment_stats.total_coupon_paid_fee / 100)
    report += '用券买单总补贴: {}元\n'.format(platform_payment_stats.total_coupon_subsidy_fee / 100)
    report += '饭票买单帐单数: {}笔\n'.format(platform_payment_stats.total_fanpiao_bill_count)
    report += '饭票买单总金额: {}元\n'.format(platform_payment_stats.total_fanpiao_bill_fee / 100)
    report += '饭票实付总金额: {}元\n'.format(platform_payment_stats.total_fanpiao_paid_fee / 100)
    report += '饭票买单总补贴: {}元\n'.format(platform_payment_stats.total_fanpiao_subsidy_fee / 100)
    report += '每日帐单统计:\n'
    report += '  日期         交易笔数   交易额   交易额柱图(单位:千)\n'
    for period_stats in platform_payment_stats.period_stats:
        report += '  {}    {: <8}  {: <8} {}\n'.format(period_stats.period_start_time,
            period_stats.bill_count,
            period_stats.total_paid_fee / 100,
            '+' * int(period_stats.total_paid_fee / 100000 + 0.5))
    return report

def generate_platform_payment_report(start_time=None, end_time=None):
    stats = get_platform_payment_stats(start_time=start_time, end_time=end_time)
    return generate_report_from_platform_payment_stats(stats)

def get_real_paid_fee_new(transaction):
    # paid_in_fee字段是在2020.06.02之后才添加的，在此之前需用原有的计算方式计算商家实收
    if (transaction.paid_time < **********):
        return get_real_paid_fee(transaction)

    order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
    if not order:
        return transaction.paid_fee
    else:
        return order.paid_in_fee

def get_real_paid_fee(transaction):
    """ 获取用户真实支付的金额
    Args:
        transaction: (wallet_pb.Transaction)交易关联的transaction
    Return:
        value: (int)金额
    """
    if transaction.type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
        dining = GroupDiningDataAccessHelper().get_dining_by_transaction_id(transaction.id)
        if dining:
            red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
            if red_packet:
                return transaction.paid_fee - red_packet.total_value
    if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
        red_packet = RedPacketDataAccessHelper().get_red_packet(transaction_id=transaction.id)
        if red_packet:
            return transaction.paid_fee - red_packet.total_value
    if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
        order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
        if not order:
            return transaction.paid_fee
        max_discount = order.max_discount
        if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            fee = max(int(order.enable_discount_fee * (100 - max_discount) / float(100) + (transaction.bill_fee - order.enable_discount_fee) + 0.5), transaction.paid_fee)
        elif transaction.use_coupon_id:
            coupon = CouponDataAccessHelper().get_coupon_by_id(transaction.use_coupon_id)
            coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
            if coupon_category.issue_scene == coupon_category_pb.CouponCategory.COUPON_PACKAGE:
                fee = max(int(order.enable_discount_fee * (100 - max_discount) / float(100) + (transaction.bill_fee - order.enable_discount_fee) + 0.5), transaction.paid_fee)
            else:
                fee = transaction.paid_fee
        else:
            red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
            fee = transaction.paid_fee
            if red_packet and red_packet.status != red_packet_pb.RedPacket.CANCELLED:
                fee -= red_packet.total_value
        return fee
    if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
        order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
        max_discount = order.max_discount
        if transaction.use_coupon_id == '':
            fee = transaction.paid_fee
        else:
            fee = max(int(order.enable_discount_fee * (100 - max_discount) / float(100) + (transaction.bill_fee - order.enable_discount_fee) + 0.5), transaction.paid_fee)
        return fee
    if transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
        order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
        if not order:
            return transaction.paid_fee
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        if red_packet:
            return transaction.paid_fee - red_packet.total_value
        else:
            print('找不到该点菜订单ID对应的red_packet: {}'.format(order.id))
    return transaction.paid_fee

def get_merchant_payment_distribution_report(start_time=None, end_time=None):
    report = '#################### 商家客单价分布统计 ####################\n'
    report += '商家名称,订单数,{}\n'.format(','.join(['{}%'.format(i + 1) for i in range(100)]))
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if not merchant.stores or not merchant.stores[0].enable_ordering_service:
            continue

        # report += '$$$$$ {} $$$$$\n'.format(merchant.basic_info.name)
        type_list=[wallet_pb.Transaction.GROUP_DINING_PAYMENT, wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]
        transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant.id,
                state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time, type_list=type_list)
        size = len(transactions)
        if size == 0:
            report += '无历史交易记录\n\n\n'
            continue

        report += '{},{}'.format(merchant.basic_info.name, size)
        transactions = sorted(transactions, key=lambda x: x.bill_fee)
        for i in range(100):
            pos = int(size * (i + 1) / 100)
            if pos >= size:
                pos = size - 1
            report += ',{}'.format(transactions[pos].bill_fee / 100)
        report += '\n'

        # report += '订单总数: {}\n\n'.format(size)
        # transactions = sorted(transactions, key=lambda x: x.bill_fee)
        # for i in range(100):
        #     pos = int(size * (i + 1) / 100)
        #     if pos >= size:
        #         pos = size - 1
        #     report += '{:2d}%: {}元\n'.format(i + 1, transactions[pos].bill_fee / 100)
        # report += '\n\n'

    return report

def get_merchant_payment_stats(merchant_id, store_id=None,
                               start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY,
                               type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
                                          wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.ORDERING_REFUND]):
    """获取商户账单数据

    Args:
        merchant_id: (string) 商户ID
        store_id: (string) 商户门店ID
        start_time: (int64) 查询开始时间
        end_time: (int64) 查询结束时间
        time_granularity: (bi_common_pb.TimeGranularity) 时间粒度
        type: (TransactionType) 交易类型
    """
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_id)
    if not merchant:
        return None

    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            payee_store_id=store_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=type_list)

    stats = payment_stats_pb.MerchantPaymentStats()
    period_paid_amounts = {}
    repurchase_map = {}
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    for payment in payments:
        flag = 1
        if payment.type == wallet_pb.Transaction.ORDERING_REFUND:
            flag = -1
        real_paid_fee = get_real_paid_fee_new(payment)
        if payment.type == wallet_pb.Transaction.ORDERING_REFUND:
            stats.bill_count -= 1
            order = OrderingServiceDataAccessHelper().get_order(refund_transaction_id=payment.id)
            if order:
                real_paid_fee += order.commission
        else:
            stats.bill_count += 1
            order = OrderingServiceDataAccessHelper().get_order(transaction_id=payment.id)
            if order:
                stats.total_diner_count += order.people_count
                real_paid_fee -= order.commission
        stats.total_bill_fee += payment.bill_fee
        stats.total_paid_fee += real_paid_fee
        stats.total_reduce_fee += payment.bill_fee - payment.paid_fee
        stats.total_refund_fee += max(0, payment.paid_fee - real_paid_fee)
        pay_method = wallet_pb.Transaction.PayMethod.Name(payment.pay_method)
        if pay_method in stats.total_paid_fee_by_channels:
            stats.total_paid_fee_by_channels[pay_method] += payment.paid_fee
        else:
            stats.total_paid_fee_by_channels[pay_method] = payment.paid_fee
        if payment.use_coupon_id:
            stats.total_coupon_bill_count += 1 * flag
            stats.total_coupon_bill_fee += payment.bill_fee
            stats.total_coupon_paid_fee += payment.paid_fee
            stats.total_coupon_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        if payment.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            stats.total_fanpiao_bill_count += 1 * flag
            stats.total_fanpiao_bill_fee += payment.bill_fee
            stats.total_fanpiao_paid_fee += payment.paid_fee
            stats.total_fanpiao_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        if payment.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            stats.total_recharge_member_bill_count += 1 * flag
            stats.total_recharge_member_bill_fee += payment.bill_fee
            stats.total_recharge_member_paid_fee += payment.paid_fee

        period_time_str = date_utils.get_datetime_in_timezone(payment.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if period_time_str in period_paid_amounts:
            period_paid_amounts[period_time_str].total_bill_fee += payment.bill_fee
            period_paid_amounts[period_time_str].total_paid_fee += real_paid_fee
            period_paid_amounts[period_time_str].bill_count += 1 * flag
        else:
            period_stats = create_period_payment_stats(period_time_str, payment.bill_fee, real_paid_fee)
            period_paid_amounts[period_time_str] = period_stats

        if payment.payer_id in repurchase_map:
            repurchase_map[payment.payer_id] += 1 * flag
        else:
            repurchase_map[payment.payer_id] = 1 * flag
    stats.merchant_id = merchant_id
    stats.total_discount_fee = stats.total_reduce_fee + stats.total_refund_fee
    if stats.bill_count > 0:
        stats.avg_bill_fee = int(stats.total_bill_fee / stats.bill_count)
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()

    for period_str in sorted(period_paid_amounts.keys()):
        stats.period_stats.add().CopyFrom(period_paid_amounts[period_str])

    stats.active_business_days = len(period_paid_amounts.keys())

    for user_id in repurchase_map.keys():
        if repurchase_map[user_id] in stats.repurchase_stats:
            stats.repurchase_stats[repurchase_map[user_id]] += 1
        else:
            stats.repurchase_stats[repurchase_map[user_id]] = 1

    # 特惠券包购买统计
    package_transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=[wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE])
    total_fee = 0
    buy_map = {}
    for transaction in package_transactions:
        total_fee += transaction.paid_fee
        if transaction.paid_fee in buy_map:
            buy_map[transaction.paid_fee] += 1
        else:
            buy_map[transaction.paid_fee] = 1

    stats.coupon_package_stats.total_buy_count = coupon_package_buy_count(package_transactions)
    stats.coupon_package_stats.total_paid_fee = total_fee
    for fee in sorted(buy_map.keys()):
        stats.coupon_package_stats.buy_map[fee] = buy_map[fee]

    # 饭票购买使用统计
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(merchant_id=merchant_id,
        buy_start_time=start_time, buy_end_time=end_time)
    for fanpiao in fanpiao_list:
        stats.total_fanpiao_purchase_value += fanpiao.total_value
        stats.fanpiao_stats.total_purchase_value += fanpiao.total_value
        stats.fanpiao_stats.buy_map[fanpiao.total_value] += 1
        stats.fanpiao_stats.remained_value_map[fanpiao.total_value] += (fanpiao.total_value - fanpiao.total_used_fee)
        if fanpiao.status == fanpiao_pb.Fanpiao.ACTIVE:
            stats.fanpiao_stats.total_buy_count += 1

    # 商家会员储值统计
    recharge_transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            payee_store_id=store_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=[wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE, wallet_pb.Transaction.MEMBER_CARD_RECHARGE_REFUND])
    total_fee = 0
    buy_map = {}
    for transaction in recharge_transactions:
        total_fee += transaction.paid_fee
        if transaction.paid_fee in buy_map:
            buy_map[transaction.paid_fee] += 1
        else:
            buy_map[transaction.paid_fee] = 1

    stats.member_recharge_stats.total_recharge_count = len(recharge_transactions)
    stats.member_recharge_stats.total_recharge_value = total_fee
    for fee in sorted(buy_map.keys()):
        stats.member_recharge_stats.buy_map[fee] = buy_map[fee]

    return stats


def get_merchant_payment_stats_new(merchant_id, store_id=None, start_time=None, end_time=None,
                               time_granularity=bi_common_pb.DAILY):
    """获取商户账单数据
    最近一个月的 transaction type:
        RED_PACKET_DEPOSIT,
        ORDERING_REFUND,
        COUPON_PACKAGE_REFUND,
        CASH_WITHDRAW,
        SHILAI_POS_ORDER_REFUND,
        SHILAI_MEMBER_CARD_RECHARGE,
        SELF_DINING_PAYMENT,
        SHILAI_POS_ORDER_PAYMENT,
        FANPIAO_PURCHASE,
        SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE,
        COUPON_PACKAGE_PURCHASE,
        FANPIAO_REFUND,
        SELF_DISH_ORDER_PAYMENT
    Args:
        merchant_id: (string) 商户ID
        store_id: (string) 商户门店ID
        start_time: (int64) 查询开始时间
        end_time: (int64) 查询结束时间
        time_granularity: (bi_common_pb.TimeGranularity) 时间粒度
        type: (TransactionType) 交易类型
    """
    ordering_dao = OrderingServiceDataAccessHelper()
    _paid_orders = ordering_dao.get_orders(
        merchant_id=merchant_id, start_paid_time=start_time, end_paid_time=end_time,
        status=dish_pb.DishOrder.PAID
    )

    refund_type_list = [
        wallet_pb.Transaction.ORDERING_REFUND,
        wallet_pb.Transaction.COUPON_PACKAGE_REFUND,
        wallet_pb.Transaction.SHILAI_POS_ORDER_REFUND,
        wallet_pb.Transaction.FANPIAO_REFUND,
    ]
    type_list = [
        wallet_pb.Transaction.SELF_DINING_PAYMENT,
        wallet_pb.Transaction.GROUP_DINING_PAYMENT,
        wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
        wallet_pb.Transaction.FANPIAO_PURCHASE,
        wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE,
        wallet_pb.Transaction.SHILAI_POS_ORDER_PAYMENT,
    ]
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_id)
    if not merchant:
        return None

    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            payee_store_id=store_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=type_list + refund_type_list)

    stats = payment_stats_pb.MerchantPaymentStats()
    period_paid_amounts = {}
    repurchase_map = {}
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    for payment in payments:
        flag = 1
        real_paid_fee = get_real_paid_fee_new(payment)
        if payment.type in refund_type_list:
            flag = -1
        stats.bill_count += flag
        if payment.type == wallet_pb.Transaction.ORDERING_REFUND:
            # 只有 ORDERING_REFUND 有 refund_transaction_id
            order = OrderingServiceDataAccessHelper().get_order(refund_transaction_id=payment.id)
            if order:
                real_paid_fee += order.commission
        else:
            order = OrderingServiceDataAccessHelper().get_order(transaction_id=payment.id)
            if order:
                stats.total_diner_count += order.people_count
                real_paid_fee -= order.commission
        stats.total_bill_fee += payment.bill_fee
        stats.total_paid_fee += real_paid_fee
        stats.total_reduce_fee += payment.bill_fee - payment.paid_fee
        stats.total_refund_fee += max(0, payment.paid_fee - real_paid_fee)
        pay_method = wallet_pb.Transaction.PayMethod.Name(payment.pay_method)
        if pay_method in stats.total_paid_fee_by_channels:
            stats.total_paid_fee_by_channels[pay_method] += payment.paid_fee
        else:
            stats.total_paid_fee_by_channels[pay_method] = payment.paid_fee
        if payment.use_coupon_id:
            stats.total_coupon_bill_count += 1 * flag
            stats.total_coupon_bill_fee += payment.bill_fee
            stats.total_coupon_paid_fee += payment.paid_fee
            stats.total_coupon_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        if payment.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            stats.total_fanpiao_bill_count += 1 * flag
            stats.total_fanpiao_bill_fee += payment.bill_fee
            stats.total_fanpiao_paid_fee += payment.paid_fee  # 实收是 order 的 paidInFee
            stats.total_fanpiao_subsidy_fee += max(0, real_paid_fee - payment.paid_fee)
        if payment.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            stats.total_recharge_member_bill_count += 1 * flag
            stats.total_recharge_member_bill_fee += payment.bill_fee
            stats.total_recharge_member_paid_fee += payment.paid_fee
        payment_json = json_format.MessageToDict(payment, including_default_value_fields=True)
        logger.info(f"[*] {payment.id} {payment_json['type']} Flag: {flag}")
        period_time_str = date_utils.get_datetime_in_timezone(payment.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if period_time_str in period_paid_amounts:
            period_paid_amounts[period_time_str].total_bill_fee += payment.bill_fee
            period_paid_amounts[period_time_str].total_paid_fee += real_paid_fee
            period_paid_amounts[period_time_str].bill_count += 1 * flag
        else:
            period_stats = create_period_payment_stats(period_time_str, payment.bill_fee, real_paid_fee)
            period_paid_amounts[period_time_str] = period_stats

        if payment.payer_id in repurchase_map:
            repurchase_map[payment.payer_id] += 1 * flag
        else:
            repurchase_map[payment.payer_id] = 1 * flag

    stats.merchant_id = merchant_id
    stats.total_discount_fee = stats.total_reduce_fee + stats.total_refund_fee
    if stats.bill_count > 0:
        stats.avg_bill_fee = int(stats.total_bill_fee / stats.bill_count)
    stats.time_granularity = time_granularity
    if start_time is not None:
        stats.stats_start_time = start_time
    if end_time is not None:
        stats.stats_end_time = end_time
    else:
        stats.stats_end_time = date_utils.timestamp_second()

    for period_str in sorted(period_paid_amounts.keys()):
        stats.period_stats.add().CopyFrom(period_paid_amounts[period_str])

    stats.active_business_days = len(period_paid_amounts.keys())

    for user_id in repurchase_map.keys():
        if repurchase_map[user_id] in stats.repurchase_stats:
            stats.repurchase_stats[repurchase_map[user_id]] += 1
        else:
            stats.repurchase_stats[repurchase_map[user_id]] = 1

    # 特惠券包购买统计
    package_transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=[wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE])
    total_fee = 0
    buy_map = {}
    for transaction in package_transactions:
        total_fee += transaction.paid_fee
        if transaction.paid_fee in buy_map:
            buy_map[transaction.paid_fee] += 1
        else:
            buy_map[transaction.paid_fee] = 1

    stats.coupon_package_stats.total_buy_count = coupon_package_buy_count(package_transactions)
    stats.coupon_package_stats.total_paid_fee = total_fee
    for fee in sorted(buy_map.keys()):
        stats.coupon_package_stats.buy_map[fee] = buy_map[fee]

    # 饭票购买使用统计
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(merchant_id=merchant_id,
        buy_start_time=start_time, buy_end_time=end_time)
    for fanpiao in fanpiao_list:
        stats.total_fanpiao_purchase_value += fanpiao.total_value
        stats.fanpiao_stats.total_purchase_value += fanpiao.total_value
        stats.fanpiao_stats.buy_map[fanpiao.total_value] += 1
        stats.fanpiao_stats.remained_value_map[fanpiao.total_value] += (fanpiao.total_value - fanpiao.total_used_fee)
        if fanpiao.status == fanpiao_pb.Fanpiao.ACTIVE:
            stats.fanpiao_stats.total_buy_count += 1

    # 商家会员储值统计
    recharge_transactions = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            payee_store_id=store_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=[wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE, wallet_pb.Transaction.MEMBER_CARD_RECHARGE_REFUND])
    total_fee = 0
    buy_map = {}
    for transaction in recharge_transactions:
        total_fee += transaction.paid_fee
        if transaction.paid_fee in buy_map:
            buy_map[transaction.paid_fee] += 1
        else:
            buy_map[transaction.paid_fee] = 1

    stats.member_recharge_stats.total_recharge_count = len(recharge_transactions)
    stats.member_recharge_stats.total_recharge_value = total_fee
    for fee in sorted(buy_map.keys()):
        stats.member_recharge_stats.buy_map[fee] = buy_map[fee]

    return stats


def coupon_package_buy_count(transactions):
    """
    获取 券包购买次数
    Args:
        transactions:

    Returns:

    """
    count = 0
    coupon_da = CouponDataAccessHelper()
    for transaction in transactions:
        coupon_package = coupon_da.get_coupon_package(transaction_id=transaction.id)
        if not coupon_package:
            continue
        if coupon_package.status == coupons_pb.CouponPackage.NORMAL:
            count += 1
    return count

def misc(merchant_id):
    transactions = TransactionDataAccessHelper().get_transactions(payee_id=merchant_id, type=wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
    state=wallet_pb.Transaction.SUCCESS)
    user_phone_map = {}
    user_transaction_map = {}
    for transaction in transactions:
        user = UserDataAccessHelper().get_user(transaction.payer_id)
        if not user:
            continue
        if (user.id not in user_phone_map) and user.member_profile and user.member_profile.mobile_phone:
            user_map[user.id] = True

        if user.id in user_transaction_map:
            user_transaction_map[user.id] += 1
        else:
            user_transaction_map[user.id] = 1

    count = 0
    for transaction in transactions:
        if not (transaction.paid_time > x and transaction.paid_time < y):
            continue
        if user_transaction_map[transaction.payer_id] > 1:
            count += 1

    print('手机会员数量: {}'.format(len(user_phone_map.keys())))
    print('领券人数: {}'.format(count))


def create_period_payment_stats(period_start_time, bill_fee, paid_fee):
    """初始化 PeriodPaymentStats
    """
    hourly_stats = payment_stats_pb.PeriodPaymentStats()
    hourly_stats.period_start_time = period_start_time
    hourly_stats.total_bill_fee = bill_fee
    hourly_stats.total_paid_fee = paid_fee
    hourly_stats.bill_count = 1
    return hourly_stats

def generate_report_from_merchant_payment_stats(merchant_payment_stats, add_csv_output=False):
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_payment_stats.merchant_id)
    if not merchant:
        return None

    report = '$$$$$$$$ {}销售报表 $$$$$$$$\n'.format(merchant.basic_info.name)
    report += '总账单数:   {}笔\n'.format(merchant_payment_stats.bill_count)
    report += '总账单金额: {}元\n'.format(merchant_payment_stats.total_bill_fee / 100)
    report += '总实收金额: {}元\n'.format(merchant_payment_stats.total_paid_fee / 100)
    report += '总立减金额: {}元\n'.format(merchant_payment_stats.total_reduce_fee / 100)
    report += '总返现金额: {}元\n'.format(merchant_payment_stats.total_refund_fee / 100)
    report += '折扣总额:   {}元\n'.format((merchant_payment_stats.total_discount_fee) / 100)
    if merchant_payment_stats.total_bill_fee > 0:
        report += '总折扣率:   {0:.2f}%\n'.format(merchant_payment_stats.total_discount_fee * 100 / merchant_payment_stats.total_bill_fee)
    report += '商家折扣底限: {}折\n'.format(100 - merchant.preferences.coupon_config.max_discount)
    report += '平均客单价: {}元\n'.format(merchant_payment_stats.avg_bill_fee / 100)
    report += '就餐总人数: {}\n'.format(merchant_payment_stats.total_diner_count)
    if merchant_payment_stats.active_business_days > 0:
        report += '日平均交易笔数: {0:.2f}笔\n'.format(merchant_payment_stats.bill_count / merchant_payment_stats.active_business_days)
        report += '日平均交易流水: {0:.2f}元\n'.format(merchant_payment_stats.total_bill_fee / merchant_payment_stats.active_business_days / 100)
    report += '每日帐单统计:\n'
    report += '  日期         交易笔数   交易额   交易额柱图(单位:百)\n'
    for daily_stats in merchant_payment_stats.period_stats:
        report += '  {}    {: <8}  {: <8} {}\n'.format(daily_stats.period_start_time,
            daily_stats.bill_count,
            daily_stats.total_bill_fee / 100,
            '+' * int(daily_stats.total_bill_fee / 10000 + 0.5))

    if merchant_payment_stats.bill_count == 0:
        return report

    report += '\n\n$$$$$ 复购次数统计 $$$$$\n'
    repurchase_count = 0
    for purchase_count in sorted(merchant_payment_stats.repurchase_stats.keys()):
        if purchase_count > 1:
            repurchase_count += purchase_count * merchant_payment_stats.repurchase_stats[purchase_count]
        report += '{}次复购: {}人\n'.format(purchase_count, merchant_payment_stats.repurchase_stats[purchase_count])
    report += '\n总订单数: {}\n'.format(merchant_payment_stats.bill_count)
    report += '总复购次数: {}\n'.format(repurchase_count)
    report += '总复购率: {0:.2f}%\n'.format(repurchase_count * 100 / merchant_payment_stats.bill_count)

    report += '\n\n$$$$$ 分支付渠道交易额统计 $$$$$\n'
    for channel in merchant_payment_stats.total_paid_fee_by_channels:
        report += '{}: {}元'.format(channel, merchant_payment_stats.total_paid_fee_by_channels[channel] / 100)

    report += '\n\n$$$$$ 特惠券包购买统计 $$$$$\n'
    report += '总券包购买数量: {}\n'.format(merchant_payment_stats.coupon_package_stats.total_buy_count)
    report += '总券包购买金额: {}元\n'.format(merchant_payment_stats.coupon_package_stats.total_paid_fee / 100)
    report += '用券买单帐单数: {}笔\n'.format(merchant_payment_stats.total_coupon_bill_count)
    report += '占总客单数比例: {0:.2f}%\n'.format(merchant_payment_stats.total_coupon_bill_count * 100 / merchant_payment_stats.bill_count)
    report += '用券买单总金额: {}元\n'.format(merchant_payment_stats.total_coupon_bill_fee / 100)
    report += '占门店营收比例: {0:.2f}%\n'.format(merchant_payment_stats.total_coupon_bill_fee * 100 / merchant_payment_stats.total_bill_fee)
    report += '用券买单总实付: {}元\n'.format(merchant_payment_stats.total_coupon_paid_fee / 100)
    report += '用券买单总补贴: {}元\n'.format(merchant_payment_stats.total_coupon_subsidy_fee / 100)
    report += '券包购买详情:\n'
    for fee in merchant_payment_stats.coupon_package_stats.buy_map:
        report += '  {}元券包: {}\n'.format(fee / 100, merchant_payment_stats.coupon_package_stats.buy_map[fee])

    report += '\n\n$$$$$ 饭票数据统计 $$$$$\n'
    report += '饭票购买总数量: {}张\n'.format(merchant_payment_stats.fanpiao_stats.total_buy_count)
    report += '饭票购买总额度: {}元\n'.format(merchant_payment_stats.total_fanpiao_purchase_value / 100)
    report += '饭票买单帐单数: {}笔\n'.format(merchant_payment_stats.total_fanpiao_bill_count)
    report += '占总客单数比例: {0:.2f}%\n'.format(merchant_payment_stats.total_fanpiao_bill_count * 100 / merchant_payment_stats.bill_count)
    report += '饭票买单总金额: {}元\n'.format(merchant_payment_stats.total_fanpiao_bill_fee / 100)
    report += '占门店营收比例: {0:.2f}%\n'.format(merchant_payment_stats.total_fanpiao_bill_fee * 100 / merchant_payment_stats.total_bill_fee)
    report += '饭票买单总实付: {}元\n'.format(merchant_payment_stats.total_fanpiao_paid_fee / 100)
    report += '饭票买单总补贴: {}元\n'.format(merchant_payment_stats.total_fanpiao_subsidy_fee / 100)
    report += '饭票购买使用详情:\n'
    for fee in merchant_payment_stats.fanpiao_stats.buy_map:
        report += '  {}元饭票: {}张    总额度: {}元    总剩余额度: {}元\n'.format(fee / 100,
            merchant_payment_stats.fanpiao_stats.buy_map[fee],
            fee / 100 * merchant_payment_stats.fanpiao_stats.buy_map[fee],
            merchant_payment_stats.fanpiao_stats.remained_value_map[fee] / 100)

    if add_csv_output:
        report +='\n\n日期,交易笔数,帐单原始金额(元),实收金额(元),打款金额(元)\n'
        for daily_stats in merchant_payment_stats.period_stats:
            report += '{},{},{},{},{}\n'.format(daily_stats.period_start_time, daily_stats.bill_count,
                daily_stats.total_bill_fee / 100, daily_stats.total_paid_fee / 100,
                int((daily_stats.total_paid_fee / 100 * 0.998 * 1000 + 5) / 10) / 100)

    return report

CSV_HEADER = '商家名称,总账单数,总账单金额,总实收金额,总立减金额,总返现金额,折扣总额,总折扣率,商家折扣底限,平均客单价,就餐总人数,' + \
             '日平均交易笔数,日平均交易流水,,总订单数,总复购次数,总复购率,,总券包购买数量,总券包购买金额,用券买单帐单数,' + \
             '占总客单数比例,用券买单总金额,占门店营收比例,用券买单总实付,用券买单总补贴,,饭票购买总数量,饭票购买总额度,' + \
             '饭票买单帐单数,占总客单数比例,饭票买单总金额,占门店营收比例,饭票买单总实付,饭票买单总补贴,'
def generate_csv_report_from_merchant_payment_stats(merchant_payment_stats):
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_payment_stats.merchant_id)
    if not merchant:
        return None

    # 商家名称
    report = '{},'.format(merchant.basic_info.name)
    # 总账单数
    report += '{},'.format(merchant_payment_stats.bill_count)
    # 总账单金额
    report += '{},'.format(merchant_payment_stats.total_bill_fee / 100)
    # 总实收金额
    report += '{},'.format(merchant_payment_stats.total_paid_fee / 100)
    # 总立减金额
    report += '{},'.format(merchant_payment_stats.total_reduce_fee / 100)
    # 总返现金额
    report += '{},'.format(merchant_payment_stats.total_refund_fee / 100)
    # 折扣总额
    report += '{},'.format((merchant_payment_stats.total_discount_fee) / 100)
    # 总折扣率
    if merchant_payment_stats.total_bill_fee > 0:
        report += '{}%,'.format(merchant_payment_stats.total_discount_fee * 100 / merchant_payment_stats.total_bill_fee)
    else:
        report += '0,'
    # 商家折扣底限
    report += '{},'.format(100 - merchant.preferences.coupon_config.max_discount)
    # 平均客单价
    report += '{},'.format(merchant_payment_stats.avg_bill_fee / 100)
    # 就餐总人数
    report += '{},'.format(merchant_payment_stats.total_diner_count)
    if merchant_payment_stats.active_business_days > 0:
        # 日平均交易笔数
        report += '{},'.format(merchant_payment_stats.bill_count / merchant_payment_stats.active_business_days)
        # 日平均交易流水
        report += '{},'.format(merchant_payment_stats.total_bill_fee / merchant_payment_stats.active_business_days / 100)
    else:
        report += '0,0,'

    if merchant_payment_stats.bill_count == 0:
        return report

    repurchase_count = 0
    for purchase_count in sorted(merchant_payment_stats.repurchase_stats.keys()):
        if purchase_count > 1:
            repurchase_count += purchase_count * merchant_payment_stats.repurchase_stats[purchase_count]
    report += ','
    # 总订单数
    report += '{},'.format(merchant_payment_stats.bill_count)
    # 总复购次数
    report += '{},'.format(repurchase_count)
    # 总复购率
    report += '{}%,'.format(repurchase_count * 100 / merchant_payment_stats.bill_count)

    report += ','
    # 总券包购买数量
    report += '{},'.format(merchant_payment_stats.coupon_package_stats.total_buy_count)
    # 总券包购买金额
    report += '{},'.format(merchant_payment_stats.coupon_package_stats.total_paid_fee / 100)
    # 用券买单帐单数
    report += '{},'.format(merchant_payment_stats.total_coupon_bill_count)
    # 占总客单数比例
    report += '{}%,'.format(merchant_payment_stats.total_coupon_bill_count * 100 / merchant_payment_stats.bill_count)
    # 用券买单总金额
    report += '{},'.format(merchant_payment_stats.total_coupon_bill_fee / 100)
    # 占门店营收比例
    report += '{}%,'.format(merchant_payment_stats.total_coupon_bill_fee * 100 / merchant_payment_stats.total_bill_fee)
    # 用券买单总实付
    report += '{},'.format(merchant_payment_stats.total_coupon_paid_fee / 100)
    # 用券买单总补贴
    report += '{},'.format(merchant_payment_stats.total_coupon_subsidy_fee / 100)

    report += ','
    # 饭票购买总数量
    report += '{},'.format(merchant_payment_stats.fanpiao_stats.total_buy_count)
    # 饭票购买总额度
    report += '{},'.format(merchant_payment_stats.total_fanpiao_purchase_value / 100)
    # 饭票买单帐单数
    report += '{},'.format(merchant_payment_stats.total_fanpiao_bill_count)
    # 占总客单数比例
    report += '{}%,'.format(merchant_payment_stats.total_fanpiao_bill_count * 100 / merchant_payment_stats.bill_count)
    # 饭票买单总金额
    report += '{},'.format(merchant_payment_stats.total_fanpiao_bill_fee / 100)
    # 占门店营收比例
    report += '{}%,'.format(merchant_payment_stats.total_fanpiao_bill_fee * 100 / merchant_payment_stats.total_bill_fee)
    # 饭票买单总实付
    report += '{},'.format(merchant_payment_stats.total_fanpiao_paid_fee / 100)
    # 饭票买单总补贴
    report += '{},'.format(merchant_payment_stats.total_fanpiao_subsidy_fee / 100)

    return report

def generate_merchant_payment_report(merchant_id, start_time=None, end_time=None, format='plain'):
    stats = get_merchant_payment_stats(merchant_id, start_time=start_time, end_time=end_time)
    if format == 'csv':
        return generate_csv_report_from_merchant_payment_stats(stats)
    else:
        return generate_report_from_merchant_payment_stats(stats)

def generate_merchant_monthly_report(merchant_id, year, month):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not merchant:
        return

    type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
                                          wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]

    daily_transactions = {}
    for day in range(1, 31):
        date_str = '{}-{:02}-{:02}'.format(year, month, day)
        start_time = date_utils.date_to_timestamp_second_tz(date_str, tz=date_utils.TIMEZONE_SHANGHAI, str_format='%Y-%m-%d')
        end_time = start_time + date_utils.ONE_DAY
        orders = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time, type_list=type_list)
        total_paid_fee = 0
        for order in orders:
            total_paid_fee += get_real_paid_fee(order)

        if total_paid_fee > 0:
            transactions = TransferStoreTransactionDataAccessHelper().get_transactions(store_id=merchant_id+'_0',
                success_time_start=start_time, success_time_end=end_time)
            store_transfer_fee = 0
            if transactions:
                store_transfer_fee = transactions[0].fee
            daily_transactions[date_str] = [total_paid_fee, store_transfer_fee]

    if not daily_transactions.keys():
        return

    report = '{}\n'.format(merchant.basic_info.display_name)
    report += '日期,当日营业金额(元),当日打款金额(T+1打款模式: 当日打款对应前N天营收。单位:元)\n'
    for date_str in sorted(daily_transactions.keys()):
        report += '{},{},{}\n'.format(date_str, daily_transactions[date_str][0] / 100, daily_transactions[date_str][1] / 100)

    return report

def generate_merchant_monthly_reports(year, month):
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    target_merchants = []
    for merchant in merchants:
        if '小田豆浆' in merchant.basic_info.display_name:
            target_merchants.append(merchant)

    with Pool(processes=8) as pool:
      pool.starmap(generate_single_merchant_monthly_report, [(merchant, year, month) for merchant in target_merchants])


def generate_single_merchant_monthly_report(merchant, year, month):
    print('Generating report for {}...'.format(merchant.basic_info.display_name))
    report = generate_merchant_monthly_report(merchant.id, year, month)
    with open('/tmp/{}_{}-{:02}.csv'.format(merchant.basic_info.display_name, year, month), 'w') as f:
        f.write(report)

def generate_active_merchant_payment_reports(start_time=None, end_time=None):
    """ 获取全平台在指定时间范围内的活跃商家(至少有1单交易)的报表。
    """
    report = '************ 时来营销(非扫码点餐)商户销售报表 ************\n\n'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    valid_merchants = []
    for merchant in merchants:
        # 不统计超级商户: 智易科技
        if merchant.id == '********************************':
            continue
        if not merchant.stores:
            continue
        # 本接口仅统计早期营销类商户数据，因此忽略扫码点餐商户
        if merchant.stores[0].enable_ordering_service:
            continue
        valid_merchants.append(merchant)

    with Pool(processes=16) as pool:
      results = pool.starmap(get_merchant_payment_stats, [(merchant.id, None, start_time, end_time) for merchant in valid_merchants])
      for stats in results:
        if not stats:
            continue
        if len(stats.period_stats) > 0:
            report += generate_report_from_merchant_payment_stats(stats)
            report += '\n\n'

    return report

def pick_merchants_eligible_for_cashback(start_time=None, end_time=None):
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if merchant.id == '********************************':
            continue
        if merchant.stores and merchant.stores[0].is_eligible_for_cashback:
            continue

        stats = get_merchant_payment_stats(merchant.id, start_time=start_time, end_time=end_time)
        for start_index in range(len(stats.period_stats)):
            end_index = min(start_index + 7, len(stats.period_stats))
            total_bills = sum([stat.bill_count for stat in stats.period_stats[start_index:end_index]])
            if total_bills > 50:
                print('{: <38}{: <25}{: <25}{: <32}'.format(merchant.id, total_bills,
                    stats.period_stats[start_index].period_start_time, merchant.basic_info.display_name))
                break

def get_merchant_transfer_fee(merchant, start_time=None, end_time=None):
    if merchant.id == '********************************':
        return 0
    # if not (merchant.settlement_platform == merchant_pb.Merchant.SHILAI_ACCOUNT or \
    #     merchant.wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT or \
    #     merchant.stores[0].wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT):
    #     return 0

    type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
        wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT,
        wallet_pb.Transaction.ORDERING_REFUND]
    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant.id,
        state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time,
        type_list=type_list)

    total_bill_fee = 0
    total_paid_fee = 0
    total_received_fee = 0
    for payment in payments:
        total_bill_fee += payment.bill_fee
        total_paid_fee += payment.paid_fee
        total_received_fee += get_real_paid_fee(payment)
        order = OrderingServiceDataAccessHelper().get_order(transaction_id=payment.id)
        if order:
            total_received_fee -= order.commission

    transfer_fee = 0
    if total_received_fee > 0:
        rate = merchant.payment_info.settlement_rate if merchant.payment_info.settlement_rate > 0.0 else 0.002
        transfer_fee = int((total_received_fee / 100 * (1 - rate) * 1000 + 5) / 10)
        print('{},{},{},{}'.format(merchant.id, merchant.basic_info.name, total_received_fee / 100, transfer_fee / 100))

    return transfer_fee

def generate_merchant_transfer_report(start_time=None, end_time=None):
    """生成商户转帐报表。
    """
    now = date_utils.datetime_now_in_timezone()
    sum_up_days = 1
    if now.weekday() == 0:
        sum_up_days = 3
    transaction_start_date = now - timedelta(days=sum_up_days)
    transaction_start_time = transaction_start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    transaction_end_date = now - timedelta(days=1)
    transaction_end_time = transaction_end_date.replace(hour=23, minute=59, second=59, microsecond=999)
    if start_time is None:
        start_time = transaction_start_time.timestamp() # int(time.mktime(yesterday_start_time.timetuple()))
    if end_time is None:
        end_time = transaction_end_time.timestamp()

    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    total_transfer_fee = 0
    print('############### 商家打款金额报表 ###############')
    with Pool(processes=16) as pool:
        results = pool.starmap(get_merchant_transfer_fee, [(merchant, start_time, end_time) for merchant in merchants])
        for transfer_fee in results:
            total_transfer_fee += transfer_fee

        print('\n\n商家打款总额: {}元\n'.format(total_transfer_fee / 100))

def generate_merchant_cashback_report(start_time=None, end_time=None):
    """生成商户转帐报表。
    """
    now = date_utils.datetime_now_in_timezone()
    sum_up_days = 1
    if now.weekday() == 0:
        sum_up_days = 7
    #transaction_start_date = date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI)
    transaction_start_date = now - timedelta(days=sum_up_days)
    transaction_start_time = transaction_start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    transaction_end_date = now - timedelta(days=1)
    transaction_end_time = transaction_end_date.replace(hour=23, minute=59, second=59, microsecond=999)
    if start_time is None:
        start_time = transaction_start_time.timestamp()
    if end_time is None:
        end_time = transaction_end_time.timestamp()

    group_dining_da = GroupDiningDataAccessHelper()
    red_packet_da = RedPacketDataAccessHelper()
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if merchant.id == '********************************':
            continue

        if not merchant.stores[0].is_eligible_for_cashback:
            continue

        if not (merchant.settlement_platform == merchant_pb.Merchant.SHILAI_ACCOUNT or \
            merchant.wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT or \
            merchant.stores[0].wechat_mch_id == config.SHILAI_SETTLEMENT_ACCOUNT):
            continue

        type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT]
        payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant.id,
            state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time,
            type_list=type_list)
        total_bill_fee = 0
        total_paid_fee = 0
        total_received_fee = 0
        for payment in payments:
            total_bill_fee += payment.bill_fee
            total_paid_fee += payment.paid_fee
            if payment.type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
                group_dining_event = group_dining_da.get_dining_by_transaction_id(payment.id)
                if not group_dining_event:
                    continue

                red_packet = red_packet_da.get_red_packet(dining_id=group_dining_event.id)
                if red_packet:
                    total_received_fee += payment.paid_fee - red_packet.total_value
                else:
                    total_received_fee += payment.paid_fee
            elif payment.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
                red_packet = red_packet_da.get_red_packet(transaction_id=payment.id)
                # 单人消费如果不是用拉新券买单(以是否有新会员红包判定)，则不属于有效帐单
                if not red_packet:
                    total_received_fee += 0  # payment.paid_fee
                elif red_packet.issue_scene == red_packet_pb.RedPacket.NEW_MEMBER:
                    total_received_fee += payment.paid_fee - red_packet.total_value
            else:
                total_received_fee += payment.paid_fee

        print('{: <38}{: <25}{: <32}'.format(merchant.id, int((total_received_fee / 100 * 0.05 * 0.998 * 1000 + 5) / 10) / 100,
            merchant.basic_info.display_name))

def generate_cash_flow_report(start_time=None, end_time=None):
    packets = RedPacketDataAccessHelper().get_red_packets(create_start_time=start_time, create_end_time=end_time)
    total_value = 0
    for packet in packets:
        total_value += packet.total_value

    outs = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
                start_time=start_time, end_time=end_time, type_list=[wallet_pb.Transaction.CASH_WITHDRAW])
    out_value = 0
    for out in outs:
        out_value += out.paid_fee

    type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
                                          wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]
    inners = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
                start_time=start_time, end_time=end_time, type_list=type_list, pay_method=wallet_pb.Transaction.WALLET)
    inner_value = 0
    for inner in inners:
        inner_value += inner.paid_fee

    pay_value = 0
    coupon_subsidy_value = 0
    payments = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
                start_time=start_time, end_time=end_time, type_list=type_list)
    for payment in payments:
        pay_value += payment.paid_fee
        if payment.use_coupon_id:
            coupon_subsidy_value += max(0, get_real_paid_fee(payment) - payment.paid_fee)

    rate_value = int(pay_value * 0.005)

    total_refund_value = 0
    trans_da = TransactionDataAccessHelper()
    refunded_orders = OrderingServiceDataAccessHelper().get_orders(start_time=start_time, end_time=end_time,
        status=dish_pb.DishOrder.POS_RETURN)
    for order in refunded_orders:
        transaction =  trans_da.get_transaction_by_id(order.transaction_id)
        total_refund_value += transaction.paid_fee
        pay_value -= transaction.paid_fee

    # 券包统计
    package_list = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
                start_time=start_time, end_time=end_time, type_list=[wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE])
    package_purchase_value = 0
    for package in package_list:
        package_purchase_value += package.paid_fee

    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(buy_start_time=start_time, buy_end_time=end_time)
    fanpiao_purchase_value = 0
    for fanpiao in fanpiao_list:
        fanpiao_purchase_value += fanpiao.total_value

    fanpiao_payments = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
                start_time=start_time, end_time=end_time, type_list=type_list, pay_method=wallet_pb.Transaction.FANPIAO_PAY)
    fanpiao_paid_value = 0
    for payment in fanpiao_payments:
        fanpiao_paid_value += get_real_paid_fee(payment)

    time_format = '%Y-%m-%d'
    if start_time is None:
        start_time = 0
    if end_time is None:
        end_time = date_utils.timestamp_second()
    start_time_str = date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    end_time_str = date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)

    report = '$$$$$ 现金流报告 $$$$$\n'
    report += '统计起止时间: {} ~ {}\n'.format(start_time_str, end_time_str)
    report += '总流水: {}元\n'.format(pay_value / 100)
    report += '流入: {}元\n'.format(total_value / 100)
    report += '流出: {}元\n'.format(out_value / 100)
    report += '内耗: {}元\n'.format(inner_value / 100)
    report += '费率支出(估): {}元\n'.format(rate_value / 100)
    report += '净流入: {}元\n'.format((total_value - out_value - inner_value - rate_value) / 100)
    report += '订单退款总额: {}元\n'.format(total_refund_value / 100)
    report += '券包购买总额: {}元\n'.format(package_purchase_value / 100)
    report += '券包补贴总额: {}元\n'.format(coupon_subsidy_value / 100)
    report += '券包剩余总额: {}元\n'.format((package_purchase_value - coupon_subsidy_value) / 100)
    report += '饭票购买总额: {}元\n'.format(fanpiao_purchase_value / 100)
    report += '饭票支付总额: {}元\n'.format(fanpiao_paid_value / 100)
    report += '饭票剩余总额: {}元\n'.format((fanpiao_purchase_value - fanpiao_paid_value) / 100)
    report += '总剩余: {}元\n'.format((total_value - out_value - inner_value - rate_value +
                package_purchase_value - coupon_subsidy_value + fanpiao_purchase_value - fanpiao_paid_value) / 100)

    return report

def generate_merchant_finance_report(merchant_id, start_time=None, end_time=None):
    report = '消费时间,消费门店,订单编号,支付方式,订单类型,订单状态,订单金额,优惠金额,退款金额,实收金额,手续费,结算金额,第三方订单号\n'
    merchant_da = MerchantDataAccessHelper()
    merchant = merchant_da.get_merchant(merchant_id)
    if not merchant:
        return None

    order_da = OrderingServiceDataAccessHelper()
    type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.ORDERING_REFUND]
    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=start_time, end_time=end_time,
            type_list=type_list)
    for payment in payments:
        real_paid_fee = get_real_paid_fee_new(payment)
        discount_fee = payment.bill_fee - real_paid_fee
        transfer_commission = int(real_paid_fee * merchant.payment_info.settlement_rate + 0.5)
        received_fee = real_paid_fee - transfer_commission
        paid_time_str = date_utils.get_datetime_in_timezone(payment.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
        order = order_da.get_order(transaction_id=payment.id)
        pay_method = '微信'
        if pay_method == 'ALIPAY':
            payment.pay_method = '支付宝'
        report += '{},{},{},{},{},{},{},{},{},{},{},{},{}\n'.format(
            paid_time_str, merchant.basic_info.display_name, order.id, pay_method, '时来扫码点餐', '已完成',
            payment.bill_fee / 100, discount_fee / 100, 0, real_paid_fee / 100,
            transfer_commission / 100, received_fee / 100, order.id)

    return report


from dao.membership_da_helper import MembershipDataAccessHelper
def tmp_test(merchant_id, start_time=None, end_time=None):
    type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.ORDERING_REFUND]
    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.SUCCESS, pay_method=wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY,
            start_time=start_time, end_time=end_time, type_list=type_list)
    report = '交易日期,用户昵称,手机号,帐单金额,可用余额\n'
    for payment in payments:
        date_str = date_utils.get_datetime_in_timezone(payment.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
        user = UserDataAccessHelper().get_user(payment.payer_id)
        member_cards = MembershipDataAccessHelper().get_member_cards(user_id=user.id,
            merchant_id=payment.payee_id)
        report += '{},{},{},{}元,{}元\n'.format(date_str, user.wechat_profile.nickname,
            user.member_profile.mobile_phone, payment.paid_fee / 100, member_cards[0].balance / 100)

    user_da = UserDataAccessHelper()

    report += '\n\n\n'
    report += '用户昵称,手机号,当前余额\n'
    member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id='4e8090f561874fee9fe76d9514eeed89')
    for member_card in member_cards:
        user = user_da.get_user(member_card.user_id)
        report = '{},{},{}元\n'.format(user.wechat_profile.nickname, user.member_profile.mobile_phone,
        member_card.balance / 100)

    report += '\n\n\n'
    report += '交易日期,用户昵称,手机号,储值金额\n'
    recharges = TransactionDataAccessHelper().get_transactions(payee_id=merchant_id, state=wallet_pb.Transaction.SUCCESS,
            type=wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE)
    for recharge in recharges:
        date_str = date_utils.get_datetime_in_timezone(recharge.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
        user = user_da.get_user(recharge.payer_id)
        report += '{},{},{},{}元\n'.format(date_str, user.wechat_profile.nickname, user.member_profile.mobile_phone,
            recharge.paid_fee / 100)

    return report


def misc_test(merchant_id, start_time=None, end_time=None):
    type_list=[wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE]
    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id,
            state=wallet_pb.Transaction.ORDERED, start_time=start_time, end_time=end_time, type_list=type_list)
    report = '交易日期,用户昵称,手机号,帐单金额\n'
    for payment in payments:
        date_str = date_utils.get_datetime_in_timezone(payment.paid_time, date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
        user = UserDataAccessHelper().get_user(payment.payer_id)
        report += '{},{},{},{}元\n'.format(date_str, user.wechat_profile.nickname,
            user.member_profile.mobile_phone, payment.paid_fee / 100)

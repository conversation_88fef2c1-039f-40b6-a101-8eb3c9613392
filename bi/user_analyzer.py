from datetime import datetime

import proto.bi.payment_stats_pb2 as payment_stats_pb
import proto.bi.user_stats_pb2 as user_stats_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.payment_pb2 as payment_pb
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.payment_da_helper import PaymentDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper


def generate_storyline_report_for_user(user_id, start_time=None, end_time=None):
    """为指定用户生成走心版的历史故事线报告。

    Args:
        user_id: (string) 用户ID
        ordered_user_events: (list of UserEvent) 按照时间线排序的用户事件列表

    Returns:
        (string) 用户故事线报告
    """
    user = UserDataAccessHelper().get_user(user_id)
    if not user:
        return ''

    join_time = date_utils.get_datetime_in_timezone(user.joined_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y年%m月%d日')
    past_days = (date_utils.datetime_now_in_timezone() - date_utils.get_datetime_in_timezone(
        user.joined_time, date_utils.TIMEZONE_SHANGHAI)).days

    report = 'Hi, {}:\n'.format(user.wechat_profile.nickname)
    report += '自从你{}加入时来平台，到现在已经过去了{}天。\n'.format(join_time, past_days)
    report += '让我们一起来回顾下你在平台的点点滴滴:\n'

    ordered_user_events = get_ordered_user_events(user_id, start_time=start_time, end_time=end_time)
    for event in ordered_user_events:
        event_report = generate_user_event_storyline(event)
        if event_report:
            report += '  '
            report += event_report

    member_cards = MembershipDataAccessHelper().get_member_cards_for_user(user_id)
    membership_count = len(member_cards)

    payments = PaymentDataAccessHelper().get_payments(user_id=user_id, status=payment_pb.Payment.SUCCESS)
    payment_count = len(payments)
    use_coupon_count = 0
    total_savings = 0
    merchant_stats = {}
    for payment in payments:
        if payment.merchant_id in merchant_stats:
            merchant_stats[payment.merchant_id] += 1
        else:
            merchant_stats[payment.merchant_id] = 1

        if payment.use_coupon_id:
            use_coupon_count += 1
            total_savings += int((payment.bill_fee - payment.paid_fee) / 100)

    max_count = 0
    favorite_merchant_id = ''
    for key in merchant_stats.keys():
        if merchant_stats[key] > max_count:
            max_count = merchant_stats[key]
            favorite_merchant_id = key

    favorite_merchant_name = ''
    if favorite_merchant_id:
        merchant = MerchantDataAccessHelper().get_merchant(favorite_merchant_id)
        favorite_merchant_name = merchant.basic_info.display_name

    report += '\n在{}个时来陪伴的日子里——\n'.format(past_days)
    report += '  你加入了{}个商家，成为了Ta们的忠实顾客；\n'.format(membership_count)
    report += '  你一共在平台上消费了{}次，其中{}次使用了时来为你准备的优惠券；\n'.format(payment_count, use_coupon_count)
    report += '  到目前为止，时来为你传递了价值{}元的优惠，未来仍将继续传递；\n'.format(total_savings)
    report += '  你最喜爱的商家是{}，一共光顾了{}次，这家店，时来也很喜欢。\n'.format(favorite_merchant_name, max_count)
    report += '\n未来，还有无数个或平凡或亮丽的日子，时来会一直在你的身边，\n'
    report += '传递价值，传递一份份小小的祝福与幸福。\n'
    report += '时来记得了你，希望你也记得时来。\n'

    return report

def generate_history_timeline_for_user(user_id, start_time=None, end_time=None):
    """为指定用户生成(用于debug的)历史时间线信息

    Args:
        user_id: (string) 用户ID
        ordered_user_events: (list of UserEvent) 按照时间线排序的用户事件列表

    Returns:
        (string) 用户历史时间线完整事件报告
    """
    report = ''
    ordered_user_events = get_ordered_user_events(user_id, start_time=start_time, end_time=end_time)
    for event in ordered_user_events:
        report += generate_user_event_report(event)

    return report

def generate_user_event_storyline(user_event):
    time_str = date_utils.get_datetime_in_timezone(user_event.timestamp,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y年%m月%d日%H时')
    if user_event.event_type == user_stats_pb.MEMBER_CARD_ACCEPTED:
        member_card = user_event.member_card
        merchant = MerchantDataAccessHelper().get_merchant(member_card.merchant_id)
        return '{}，你成为了{}的会员\n'.format(time_str, merchant.basic_info.display_name)
    elif user_event.event_type == user_stats_pb.PAYMENT:
        payment = user_event.payment
        if payment.status != payment_pb.Payment.SUCCESS:
            return ''

        merchant = MerchantDataAccessHelper().get_merchant(payment.merchant_id)
        if not payment.use_coupon_id:
            return '{}，你在{}消费{}元却未使用任何优惠券, 让时来感到心愧。\n'.format(time_str,
                merchant.basic_info.display_name, payment.paid_fee / 100)

        coupon = CouponDataAccessHelper().get_coupon_by_id(payment.use_coupon_id)
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
              coupon.coupon_category_id)
        return '{}，你在{}消费{}元并使用了“{}”优惠券。\n'.format(time_str,
            merchant.basic_info.display_name,
            payment.bill_fee / 100,
            coupon_category.wechat_cash_coupon.cash.base_info.title)

    return ''

def generate_user_event_report(user_event):
    time_str = date_utils.get_datetime_in_timezone(user_event.timestamp,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
    if user_event.event_type == user_stats_pb.MEMBER_CARD_ACCEPTED:
        member_card = user_event.member_card
        merchant = MerchantDataAccessHelper().get_merchant(member_card.merchant_id)
        return '{}    成为商户<{}(ID:{})>会员\n'.format(time_str,
            merchant.basic_info.display_name, member_card.merchant_id)
    elif user_event.event_type == user_stats_pb.COUPON_ISSUED:
        coupon = user_event.coupon
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
            coupon.coupon_category_id)
        merchant = MerchantDataAccessHelper().get_merchant(coupon.merchant_id)
        return '{}    获得商户<{}(ID:{})>投放的[{}(ID:{})]优惠券\n'.format(time_str,
            merchant.basic_info.display_name, merchant.id,
            coupon_category.wechat_cash_coupon.cash.base_info.title,
            coupon_category.id)
    elif user_event.event_type == user_stats_pb.COUPON_ACCEPTED:
        coupon = user_event.coupon
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
            coupon.coupon_category_id)
        merchant = MerchantDataAccessHelper().get_merchant(coupon.merchant_id)
        return '{}    领取商户<{}(ID:{})>投放的[{}(ID:{})]优惠券\n'.format(time_str,
            merchant.basic_info.display_name, merchant.id,
            coupon_category.wechat_cash_coupon.cash.base_info.title,
            coupon_category.id)
    elif user_event.event_type == user_stats_pb.COUPON_PUSHED:
        coupon = user_event.coupon
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
            coupon.coupon_category_id)
        merchant = MerchantDataAccessHelper().get_merchant(coupon.merchant_id)
        return '{}    被推送商户<{}(ID:{})>投放的[{}(ID:{})]优惠券消息\n'.format(time_str,
            merchant.basic_info.display_name, merchant.id,
            coupon_category.wechat_cash_coupon.cash.base_info.title,
            coupon_category.id)
    elif user_event.event_type == user_stats_pb.COUPON_USED:
        coupon = user_event.coupon
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
            coupon.coupon_category_id)
        merchant = MerchantDataAccessHelper().get_merchant(coupon.merchant_id)
        return '{}    使用商户<{}(ID:{})>投放的[{}(ID:{})]优惠券\n'.format(time_str,
            merchant.basic_info.display_name, merchant.id,
            coupon_category.wechat_cash_coupon.cash.base_info.title,
            coupon_category.id)
    elif user_event.event_type == user_stats_pb.PAYMENT:
        payment = user_event.payment
        merchant = MerchantDataAccessHelper().get_merchant(payment.merchant_id)
        if payment.status == payment_pb.Payment.PREPAY_ORDER:
            return '{}    在商户<{}(ID:{})>产生一笔未完成交易，金额为{}元\n'.format(time_str,
                merchant.basic_info.display_name, payment.merchant_id,
                payment.paid_fee / 100)

        if not payment.use_coupon_id:
            return '{}    在商户<{}(ID:{})>产生交易共付费{}元\n'.format(time_str,
                merchant.basic_info.display_name, payment.merchant_id,
                payment.paid_fee / 100)

        coupon = CouponDataAccessHelper().get_coupon_by_id(payment.use_coupon_id)
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
              coupon.coupon_category_id)
        return '{}    在商户<{}(ID:{})>使用[{}(ID:{})]优惠券 帐单金额:{}元  实付金额: {}元\n'.format(time_str,
            merchant.basic_info.display_name, payment.merchant_id,
            coupon_category.wechat_cash_coupon.cash.base_info.title,
            coupon_category.id, payment.bill_fee / 100, payment.paid_fee / 100)

    return ''

def generate_user_events_from_member_cards(member_cards):
    events = []
    for card in member_cards:
        event = user_stats_pb.UserEvent()
        event.timestamp = card.last_activate_time
        event.event_type = user_stats_pb.MEMBER_CARD_ACCEPTED
        event.member_card.CopyFrom(card)
        events.append(event)

    return events

def generate_user_events_from_coupons(coupons):
    events = []
    for coupon in coupons:
        event = user_stats_pb.UserEvent()
        event.timestamp = coupon.issue_time
        event.event_type = user_stats_pb.COUPON_ISSUED
        event.coupon.CopyFrom(coupon)
        events.append(event)

        if coupon.push_time > 0:
            event = user_stats_pb.UserEvent()
            event.timestamp = coupon.push_time
            event.event_type = user_stats_pb.COUPON_PUSHED
            event.coupon.CopyFrom(coupon)
            events.append(event)

        if coupon.accept_time > 0:
            event = user_stats_pb.UserEvent()
            event.timestamp = coupon.accept_time
            event.event_type = user_stats_pb.COUPON_ACCEPTED
            event.coupon.CopyFrom(coupon)
            events.append(event)

        if coupon.use_time > 0:
            event = user_stats_pb.UserEvent()
            event.timestamp = coupon.use_time
            event.event_type = user_stats_pb.COUPON_USED
            event.coupon.CopyFrom(coupon)
            events.append(event)

    return events

def generate_user_events_from_payments(payments):
    events = []
    for payment in payments:
        event = user_stats_pb.UserEvent()
        event.timestamp = payment.paid_time
        event.event_type = user_stats_pb.PAYMENT
        event.payment.CopyFrom(payment)
        events.append(event)

    return events

def get_ordered_user_events(user_id, start_time=None, end_time=None):
    member_cards = MembershipDataAccessHelper().get_member_cards_for_user(user_id)
    member_card_events = generate_user_events_from_member_cards(member_cards)

    coupons = CouponDataAccessHelper().get_coupon_list(user_id=user_id)
    coupon_events = generate_user_events_from_coupons(coupons)

    payments = PaymentDataAccessHelper().get_payments(user_id=user_id)
    payment_events = generate_user_events_from_payments(payments)

    events = member_card_events + coupon_events + payment_events
    events.sort(key=lambda x: x.timestamp)

    return events

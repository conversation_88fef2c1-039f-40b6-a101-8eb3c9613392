from multiprocessing import Pool

from bi import common_util
from common.utils import date_utils
from dao.comment_da_helper import CommentDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
import proto.bi.common_pb2 as bi_common_pb
import proto.bi.ordering.store_stats_pb2 as store_stats_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.user_pb2 as user_pb

def get_store_ordering_stats(merchant_id, start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id, start_time=start_time,
        end_time=end_time, status=dish_pb.DishOrder.PAID)

    store_stats = store_stats_pb.StoreOrderingStats()
    period_stats = {}
    dish_sale_counts = {}
    time_format = common_util.get_time_format_by_time_granularity(time_granularity)
    total_order_count = 0
    total_people_count = 0
    for order in orders:
        # 统计订单数
        total_order_count += 1
        # 统计客流量
        total_people_count += order.people_count
        if order.people_count in store_stats.diner_counts:
            store_stats.diner_counts[order.people_count] += 1
        else:
            store_stats.diner_counts[order.people_count] = 1
        period_time_str = date_utils.get_datetime_in_timezone(order.paid_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        if period_time_str in period_stats:
            period_stats[period_time_str].total_order_count += 1
            period_stats[period_time_str].total_diner_count += order.people_count
        else:
            period_stats[period_time_str] = store_stats_pb.PeriodStoreOrderingStats()
            period_stats[period_time_str].period_start_time = period_time_str
            period_stats[period_time_str].total_order_count += 1
            period_stats[period_time_str].total_diner_count += order.people_count
        # 统计菜品销量
        for dish in order.products:
            if dish.name in dish_sale_counts:
                dish_sale_counts[dish.name] += dish.quantity
            else:
                dish_sale_counts[dish.name] = dish.quantity

    store_stats.time_granularity = time_granularity
    if start_time is not None:
        store_stats.stats_start_time = start_time
    if end_time is not None:
        store_stats.stats_end_time = end_time
    else:
        store_stats.stats_end_time = date_utils.timestamp_second()

    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not merchant:
      return None

    store_stats.merchant_id = merchant_id
    store_stats.store_id = merchant.stores[0].id
    store_stats.total_order_count = total_order_count
    store_stats.total_diner_count = total_people_count
    store_stats.average_diner_per_order = total_people_count / total_order_count
    for dish_name in dish_sale_counts:
        store_stats.dish_sale_counts[dish_name] = dish_sale_counts[dish_name]

    for period_str in sorted(period_stats.keys()):
        # 还有平均每客单人数需要计算
        period_stats[period_str].average_diner_per_order = period_stats[period_str].total_diner_count / period_stats[period_str].total_order_count
        store_stats.period_stats.add().CopyFrom(period_stats[period_str])

    return store_stats

def get_dish_price_map(merchant_id):
    dishes = OrderingServiceDataAccessHelper().get_all_dishes(merchant_id=merchant_id)
    dish_price_map = {}
    for dish in dishes:
        dish_price_map[dish.name] = dish.price
    return dish_price_map

def generate_store_ordering_report(merchant_id, start_time=None, end_time=None, time_granularity=bi_common_pb.DAILY):
    store_stats = get_store_ordering_stats(merchant_id=merchant_id, start_time=start_time, end_time=end_time,
        time_granularity=time_granularity)
    if not store_stats:
      return ''

    report = '************ 时来商家客流数据统计 ************\n'
    report += '统计起止时间: {}\n'.format(common_util.get_start_end_time_string(start_time=start_time,
        end_time=end_time, time_granularity=time_granularity))
    report += '总订单数: {}单\n'.format(store_stats.total_order_count)
    report += '总客流数: {}人\n'.format(store_stats.total_diner_count)
    report += '平均客单人数: {0:.1f}人/单\n'.format(store_stats.total_diner_count / store_stats.total_order_count)
    report += '就餐人数统计:\n'
    for diner_count in sorted(store_stats.diner_counts.keys()):
        report += '{: <3}人就餐: {: <5}局\n'.format(diner_count, store_stats.diner_counts[diner_count])
    report += '\n'

    report += '菜品销量统计:\n'
    report += '  菜品名称                        价格            总销量\n'
    dish_price_map = get_dish_price_map(merchant_id)
    for dish_name in sorted(store_stats.dish_sale_counts, key=store_stats.dish_sale_counts.get, reverse=True):
        format_string = '  ' + common_util.format_string(dish_name, 28) + '    {: <16}{: <8}\n'
        report += format_string.format(dish_name, dish_price_map[dish_name] / 100, store_stats.dish_sale_counts[dish_name])
    report += '\n'

    report += '每日客流统计:\n'
    report += '  日期         客流数   客单数   平均每客单人数\n'
    for period_stats in store_stats.period_stats:
        report += '  {}    {: <8}  {: <8} {}\n'.format(period_stats.period_start_time,
            period_stats.total_diner_count,
            period_stats.total_order_count,
            period_stats.average_diner_per_order)
    return report

def misc_ops():
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        for manager in merchant.manager_list:
            merchant_user = MerchantUserDataAccessHelper().get_user(manager.user_id)
            found = False
            for m in merchant_user.merchants:
                if m.merchant_id == merchant.id:
                    found = True

            if not found:
                m = merchant_user.merchants.add()
                m.merchant_id = merchant.id
                m.store_id = merchant.stores[0].id
                m.role = manager.role
                m.status = user_pb.MerchantUser.NORMAL
                MerchantUserDataAccessHelper().update_or_create_user(merchant_user)

def generate_comments_report():
    report = '========== 商家评论汇总 ==========\n'
    comments = CommentDataAccessHelper().get_comments(limit=10000)
    merchant_da = MerchantDataAccessHelper()
    user_da = UserDataAccessHelper()
    comment_map = {}
    for comment in comments:
        merchant = merchant_da.get_merchant(comment.merchant_id)
        if merchant.basic_info.name in comment_map:
            comment_map[merchant.basic_info.name].append(comment)
        else:
            comment_map[merchant.basic_info.name] = [comment]

    for name in comment_map.keys():
        report += '$$$$$ {} $$$$$\n'.format(name)
        for comment in comment_map[name]:
            user = user_da.get_user(comment.user_id)
            report += '  {}\n'.format(user.wechat_profile.nickname)
            report += '  {}        {}\n'.format(date_utils.get_datetime_in_timezone(comment.create_time,
                    date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S'), comment.rating * '*')
            report += '  {}\n\n'.format(comment.comment)

        report += '\n\n'

    return report


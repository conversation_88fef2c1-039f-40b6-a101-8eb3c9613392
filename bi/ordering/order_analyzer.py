from multiprocessing import Pool

from bi import common_util
from common.utils import date_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
import proto.bi.common_pb2 as common_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.merchant_rules_pb2 as merchant_pb

def get_favorite_dishes(merchant_id):
    orders = OrderingServiceDataAccessHelper().get_order()

def get_merchant_commision_report(merchant_id, start_time=None, end_time=None):
    report = ''
    if merchant_id == '1e543376139b474e97d38d487fa9fbe8':
        return report

    merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
    if not merchant.stores[0].enable_ordering_service:
        return report

    # 外卖订单统计
    take_out_orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID, meal_type=dish_pb.DishOrder.TAKE_OUT)
    if not take_out_orders:
      take_out_orders = []

    report += '$$$$$$$$ {}外卖统计 $$$$$$$$\n'.format(merchant.basic_info.name)
    report += '订单时间                  订单金额(元)        佣金金额(元)\n'.format(merchant.basic_info.name)
    total_commission = 0
    take_out_total_fee = 0
    for order in take_out_orders:
        take_out_total_fee += order.total_fee
        time_format = '%Y-%m-%d %H:%M:%S'
        create_time_str = date_utils.get_datetime_in_timezone(order.create_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        report += '{: <26}{: <20}{: <20}\n'.format(create_time_str, order.total_fee / 100, order.commission / 100)
        total_commission += order.commission

    report += '\n外卖订单数: {}    平均客单价: {}元\n'.format(len(take_out_orders),
        int(take_out_total_fee / len(take_out_orders)) / 100)

    # 外带订单统计
    report += '\n\n\n'
    report += '$$$$$$$$ {}外带统计 $$$$$$$$\n'.format(merchant.basic_info.name)
    report += '订单时间                  订单金额(元)        佣金金额(元)\n'.format(merchant.basic_info.name)
    take_away_orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID, meal_type=dish_pb.DishOrder.TAKE_AWAY)
    if not take_away_orders:
      take_away_orders = []

    take_away_total_fee = 0
    for order in take_away_orders:
        take_away_total_fee += order.total_fee
        time_format = '%Y-%m-%d %H:%M:%S'
        create_time_str = date_utils.get_datetime_in_timezone(order.create_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        report += '{: <26}{: <20}{: <20}\n'.format(create_time_str, order.total_fee / 100, order.commission / 100)
        total_commission += order.commission

    report += '\n外带订单数: {}    平均客单价: {}元\n'.format(len(take_away_orders),
        int(take_away_total_fee / len(take_away_orders)) / 100)

    # 自提订单统计
    report += '\n\n\n'
    report += '$$$$$$$$ {}自提统计 $$$$$$$$\n'.format(merchant.basic_info.name)
    report += '订单时间                  预约自提时间              订单金额(元)        佣金金额(元)\n'.format(merchant.basic_info.name)
    pick_up_orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID, meal_type=dish_pb.DishOrder.SELF_PICK_UP)
    if not pick_up_orders:
      pick_up_orders = []

    pick_up_total_fee = 0
    appointment_order_count = 0
    for order in pick_up_orders:
        pick_up_total_fee += order.total_fee
        time_format = '%Y-%m-%d %H:%M:%S'
        create_time_str = date_utils.get_datetime_in_timezone(order.create_time,
            date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        appointment_time_str = ''
        if order.appointment_time > 0:
            appointment_order_count += 1
            appointment_time_str = date_utils.get_datetime_in_timezone(order.appointment_time,
                date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
        report += '{: <26}{: <26}{: <20}{: <20}\n'.format(create_time_str, appointment_time_str,
            order.total_fee / 100, order.commission / 100)
        total_commission += order.commission

    report += '\n自提订单数: {}\n'.format(len(pick_up_orders))
    report += '预约订单数: {}\n'.format(appointment_order_count)
    report += '平均客单价: {}元\n\n\n'.format(int(pick_up_total_fee / len(pick_up_orders)) / 100)

    # 堂食订单统计
    report += '\n\n\n'
    report += '$$$$$$$$ {}堂食统计 $$$$$$$$\n'.format(merchant.basic_info.name)
    eat_in_orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID, meal_type=dish_pb.DishOrder.EAT_IN)
    if not eat_in_orders:
      eat_in_orders = []

    eat_in_total_fee = 0
    for order in eat_in_orders:
        eat_in_total_fee += order.total_fee

    report += '\n堂食订单数: {}\n'.format(len(eat_in_orders))
    report += '平均客单价: {}元\n\n\n'.format(int(eat_in_total_fee / len(eat_in_orders)) / 100)

    report += '\n佣金总额: {}元\n'.format(total_commission / 100)
    return report

def get_merchant_order_report(merchant_id, start_time=None, end_time=None):
    report = ''
    if merchant_id == '1e543376139b474e97d38d487fa9fbe8':
        return report

    merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
    if not (merchant.stores and merchant.stores[0].enable_ordering_service):
        return report

    orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
        start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID)
    if not orders:
      return report

    report += '$$$$$$$$ {}扫码点餐今日报表 $$$$$$$$\n'.format(merchant.basic_info.name)
    total_bill_fee = 0
    total_discount_fee = 0
    total_received_fee = 0
    total_red_packet_value = 0
    for order in orders:
        total_bill_fee += order.bill_fee
        red_packet_value = 0
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=order.transaction_id)
        if red_packet:
            red_packet_value = red_packet.total_value
            total_red_packet_value += red_packet.total_value
        # discount = order.discount_amount + red_packet_value + order.coupon_fee - order.platform_discount_fee
        discount = order.bill_fee - order.paid_in_fee
        total_discount_fee += discount
        total_received_fee += order.paid_in_fee

    report += '总帐单数: {}笔\n'.format(len(orders))
    report += '总账单金额: {0:.2f}元\n'.format(total_bill_fee / 100)
    report += '商家总实收金额: {0:.2f}元\n'.format(total_received_fee / 100)
    report += '折扣总额: {0:.2f}元\n'.format(total_discount_fee / 100)
    report += '总折扣率: {0:.2f}%\n'.format(total_discount_fee / total_bill_fee * 100)
    report += '平均客单价: {0:.2f}元\n'.format(total_bill_fee / len(orders) /100)
    report += '返现金额: {0:.2f}元\n'.format(total_red_packet_value / 100)
    return report

def generate_platform_order_report(start_time=None, end_time=None):
    report = '************ 时来平台扫码点餐报表 ************\n\n'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    with Pool(processes=8) as pool:
      results = pool.starmap(get_merchant_order_report, [(merchant.id, start_time, end_time) for merchant in merchants])
      for res in results:
        if res:
          report += res
          report += '\n'

    report += '$$$$$$$$ 扫码点餐报表结束 $$$$$$$$\n\n\n'
    return report

def generate_staff_performance_report(start_time=None, end_time=None):
    report = '************ 时来BD关联流水报表 ************\n'
    if start_time is None:
        start_time = 0
    if end_time is None:
        end_time = date_utils.timestamp_second()
    time_format = common_util.get_time_format_by_time_granularity(common_pb.DAILY)
    start_time_str = date_utils.get_datetime_in_timezone(start_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    end_time_str = date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI).strftime(time_format)
    report += '统计起止时间: {} ~ {}\n\n'.format(start_time_str, end_time_str)

    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    staff_performance = {}
    for merchant in merchants:
        if merchant.id == '1e543376139b474e97d38d487fa9fbe8':
            continue

        if not merchant.stores[0].enable_ordering_service:
            continue

        staff_id = merchant.binding_staff_id
        staff = StaffDataAccessHelper().get_staff(staff_id)
        if not staff:
          continue

        merchant_total = 0
        orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant.id,
            start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID)
        for order in orders:
            merchant_total += order.paid_fee

        if not staff.wechat_profile.nickname in staff_performance:
            staff_performance[staff.wechat_profile.nickname] = merchant_total
        else:
            staff_performance[staff.wechat_profile.nickname] += merchant_total

    for staff_nickname in staff_performance:
        report += '{}:  {}元\n'.format(staff_nickname, staff_performance[staff_nickname] / 100)

    return report

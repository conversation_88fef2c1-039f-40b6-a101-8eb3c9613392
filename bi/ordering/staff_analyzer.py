import csv
from datetime import datetime
from datetime import timedelta

from bi import common_util
from bi import payment_analyzer
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
import proto.bi.common_pb2 as common_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.merchant_rules_pb2 as merchant_pb

STAFF_LIST = [
  'd021f417-9159-4cb8-be50-a3253ebcffb3',  # Jie
  '1c81b229-2ccb-4d03-ac4b-658426645de3',  # 翠 兒
  '5becf2ac-85eb-49fd-a12b-7035c8c229ce',  # 徐伟伦
  '4257db12-0030-41be-8ee0-70236006ee24',  # Yinglun
  'c61fa7c8-db70-4e18-af6e-4c387a148950',  # inmove
  '0e3ed7f7-d6f8-413b-b11d-e651c462d347',  # Jeslyn泳贤儿
  'ca62c02e-68c0-47df-b71e-5722984ffd15',  # uu
  '89afe2ca-fe5c-42d1-8346-21ca8198f7c5',  # 古 月🐯
  'd6611b62-0951-40fc-b4eb-bc2783922e29',  # 杨蕾
  '79eddc20-df86-4993-8379-d45661d6c2d0',  # Ben
  'a2159785-a34b-47eb-be04-8dd8a1883209',  # 阿竺
  '0ab43259-0f06-4d3c-9608-52b48f6bf661',  # Sha.
  '6aec41a2-75f5-4b87-8abe-98728db55dd2',  # Riva
  '90c0f6a8-bee2-4215-92aa-86fb2f1c25a5',  # 花弄影
  'eb6ad711-81c6-4238-8aa2-fa3797281546',  # 普鲁士蓝
  '2a9b3445-89e2-4adb-bb75-d89b4c46089a',  # 华堂
  '76341cf3-71c6-4dfc-a5f4-9fc8b55b3e85',  # 易锦
  'f9a697dc-617d-4443-ab83-d17623ff41b9',  # .       sκч    Снαиg  
  '31717213-5f82-430d-8386-dfbea9bf651d',  # 。
  'd24eef88-330b-43d2-ae43-6ab550d6d279',  # 黄俊群
  'b818f6a2-088b-4724-9833-d35cfe016db5',  # *欽永
  '8a00d32a-8a6a-479d-a7a9-7259b0a57ed0',  # 锥锥
  '49f9f91e-2e3c-4fd8-80ca-fba9cfbfefb2',  # 李潇彬
  '7816cade-9feb-4a34-a0a8-428ccd2c023d',  # 欣
  'ee910ca3-55e1-479d-a67d-0fb58724a7ea',  # Kris
]

def generate_merchant_template():
    report = '商家ID,商家名称,对接BD (e.g. "翠儿/泳贤/uu")'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
      if not (merchant.stores and merchant.stores[0].enable_ordering_service):
        continue
      report += '{},{},{}'.format(merchant.id, marchant.basic_info.display_name, '')
    return report

def generate_staff_reward_report(start_time=None, end_time=None):
    merchant_income_map = {}
    merchant_bd_map = {}
    bd_map = {}
    with open('', 'r') as file:
        reader = csv.reader(file)
        for row in reader:
            merchant_id = row[0]
            total_paid = 0
            merchant = MerchantDataAccessHelper()
            orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant_id,
                start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID)
            if not orders:
                continue
            total_paid = sum([order.paid_in_fee] for order in orders)
            merchant_income_map[merchant_id] = {
                'name': merchant.basic_info.display_name,
                'total': total_paid
            }

            bd_list = row[2].split('/')
            merchant_bd_map[merchant_id] = bd_list
            for bd in bd_list:
                if not bd in bd_map:
                    bd_map[bd] = [merchant_id]
                else:
                    bd_map[bd].append(merchant_id)

        report = '$$$$$$$$$$$$$$$ BD流水绩效统计 $$$$$$$$$$$$$$$\n'
        for bd in bd_map.keys():
            report += '===== {} =====\n'.format(bd)
            index = 1
            total_reward = 0
            for merchant_id in bd_map[bd]:
                whole_reward = int(merchant_income_map[merchant_id]['total'] * 0.005)
                personal_reward = int(whole_reward / len(merchant_bd_map[merchant_id]))
                report += '{}. {}\n    总流水: {}元\n    门店总绩效: {}元\n    绩效归属人: {}\n    个人绩效提成: {}元\n\n'.format(index,
                    merchant_income_map[merchant_id]['name'],
                    merchant_income_map[merchant_id]['total'] / 100,
                    whole_reward / 100,
                    ', '.join(merchant_bd_map[merchant_id]), personal_reward / 100)
                total_reward += personal_reward
                index += 1
            report += '\n总提成: {}元\n\n\n'.format(total_reward / 100)

    return report


def generate_staff_report(staff_id, start_time=None, end_time=None):
    staff = StaffDataAccessHelper().get_staff(staff_id)
    if not staff:
        return

    merchants = MerchantDataAccessHelper().get_merchant_list(binding_staff_id=staff_id)
    if not merchants:
        return

    report = '**************** {}业务流水报告 ****************\n\n'.format(staff.wechat_profile.nickname)
    total_paid_fee = 0
    for merchant in merchants:
        orders = OrderingServiceDataAccessHelper().get_orders(merchant_id=merchant.id,
            start_time=start_time, end_time=end_time, status=dish_pb.DishOrder.PAID)
        if not orders:
            continue

        orders.sort(key=lambda x: x.paid_time)
        start_index = 0
        while start_index < len(orders) and orders[start_index].paid_time == 0:
            start_index += 1

        if start_index == len(orders):
            continue

        start_date = date_utils.get_datetime_in_timezone(orders[start_index].paid_time,
                date_utils.TIMEZONE_SHANGHAI)
        if end_time is None:
            end_time = date_utils.timestamp_second()
        end_date = date_utils.get_datetime_in_timezone(end_time, date_utils.TIMEZONE_SHANGHAI)
        if end_date - start_date < timedelta(days=30):
            continue

        total_paid_fee_dict = {}
        merchant_total_paid_fee = 0
        for order in orders[start_index:]:
            order_date = date_utils.get_datetime_in_timezone(order.paid_time, date_utils.TIMEZONE_SHANGHAI)
            if order_date - start_date >= timedelta(days=30):
                break

            paid_fee = payment
            total_paid_fee += order.paid_fee
            merchant_total_paid_fee += order.paid_fee
            date_str = date_utils.get_datetime_in_timezone(order.paid_time,
                date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d')
            if date_str in total_paid_fee_dict:
                total_paid_fee_dict[date_str] += order.paid_fee
            else:
                total_paid_fee_dict[date_str] = order.paid_fee

        report += '$$$$$$$$ {}30天流水 $$$$$$$$\n'.format(merchant.basic_info.display_name)
        report += '  日期            当日流水\n'
        for date_str in sorted(total_paid_fee_dict.keys()):
            report += '  {}      {}元\n'.format(date_str, total_paid_fee_dict[date_str] / 100)

        report += '\n30天流水总额:  {}元\n\n'.format(merchant_total_paid_fee / 100)

    report += '本月结算总流水交易额: {}元'.format(total_paid_fee / 100)

    return report


def generate_staff_report_new(staff_id, start_time=None, end_time=None):
    staff = StaffDataAccessHelper().get_staff(staff_id)
    if not staff:
        return

    merchants = MerchantDataAccessHelper().get_merchant_list(binding_staff_id=staff_id, status=merchant_pb.RUNNING)
    if not merchants:
        return

    report = '**************** {}业务流水报告 ****************\n\n'.format(staff.wechat_profile.nickname)
    total_paid_fee = 0
    for merchant in merchants:
        type_list=[wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
                                          wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.ORDERING_REFUND]
        payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant.id,
            state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time, type_list=type_list)

        total_paid_fee_dict = {}
        merchant_total_paid_fee = 0
        for payment in payments:
            paid_fee = payment_analyzer.get_real_paid_fee(payment)
            total_paid_fee += paid_fee
            merchant_total_paid_fee += paid_fee
            date_str = date_utils.get_datetime_in_timezone(payment.paid_time,
                date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d')
            if date_str in total_paid_fee_dict:
                total_paid_fee_dict[date_str] += paid_fee
            else:
                total_paid_fee_dict[date_str] = paid_fee

        report += '$$$$$$$$ {}30天流水 $$$$$$$$\n'.format(merchant.basic_info.name)
        report += '  日期            当日流水\n'
        for date_str in sorted(total_paid_fee_dict.keys()):
            report += '  {}      {}元\n'.format(date_str, total_paid_fee_dict[date_str] / 100)

        report += '\n30天流水总额:  {}元\n\n'.format(merchant_total_paid_fee / 100)

    report += '本月结算总流水交易额: {}元'.format(total_paid_fee / 100)

    return report

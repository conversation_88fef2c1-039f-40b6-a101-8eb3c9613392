import os
import sys
from datetime import datetime

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

import proto.coupons_pb2 as coupons_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.merchant_rules_pb2 as merchant_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.payment_pb2 as payment_pb
from bi import common_util
from business_ops import coupon_category_helper
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

def generate_coupon_report(merchant_id, start_time=None, end_time=None):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    report = '************ {}优惠券报表 ************\n'.format(merchant.basic_info.name)

    coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id)
    if coupon_categories:
        report += '优惠券种类总数: {}\n'.format(len(coupon_categories))
    else:
        report += '优惠券种类总数: 0\n'
        return report

    coupon_user_dict = {}
    for index, category in enumerate(coupon_categories):
        # 饭局相关的数据在group_dining_analyzer中独立进行分析，这里跳过。
        if category.issue_scene == coupon_category_pb.CouponCategory.GROUP_DINING:
            continue

        issued_coupons = []
        accepted_coupons = []
        used_coupons = []
        expired_coupons = []

        coupons = CouponDataAccessHelper().get_coupon_list(coupon_category_id=category.id,
            issue_time_start=start_time, issue_time_end=end_time)
        for coupon in coupons:
            if coupon.state == coupons_pb.Coupon.ISSUED:
                issued_coupons.append(coupon)
            elif coupon.state == coupons_pb.Coupon.ACCEPTED:
                accepted_coupons.append(coupon)
            elif coupon.state == coupons_pb.Coupon.USED:
                used_coupons.append(coupon)
            elif coupon.state == coupons_pb.Coupon.EXPIRED:
                expired_coupons.append(coupon)

        for coupon in used_coupons:
            if coupon.user_id in coupon_user_dict:
                coupon_user_dict[coupon.user_id] += 1
            else:
                coupon_user_dict[coupon.user_id] = 1

        base_info = coupon_category_helper.get_coupon_category_base_info(category)
        report += '====== 优惠券{} ======\n'.format(index + 1)
        report += '  优惠券类型ID: {}\n'.format(category.id)
        report += '  优惠券类型名称: {}\n'.format(base_info.title)
        report += '  优惠券的投放场景: {}\n'.format(coupon_category_pb.CouponCategory.IssueScene.Name(category.issue_scene))
        report += '  总发放数: {}\n'.format(len(coupons))
        report += '  领取数量: {}\n'.format(len(coupons) - len(issued_coupons))
        report += '  使用数量: {}\n'.format(len(used_coupons))
        report += '  过期数量: {}\n'.format(len(expired_coupons))
        report += '\n'


    use_count_dict = {}
    for user_id in coupon_user_dict.keys():
        count = str(coupon_user_dict[user_id])
        if count in use_count_dict:
            use_count_dict[count] += 1
        else:
            use_count_dict[count] = 1

    report += '$$$$$$ 用户使用优惠券频率统计 $$$$$$\n'
    sorted_counts = sorted([int(key) for key in use_count_dict.keys()])
    for count in sorted_counts:
        report += '{}次用券: {}人\n'.format(count, use_count_dict[str(count)])

    return report

def generate_repurchase_report(merchant_id, store_id=None, start_time=None, end_time=None):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not merchant:
        return

    type_list = [wallet_pb.Transaction.SELF_DINING_PAYMENT, wallet_pb.Transaction.GROUP_DINING_PAYMENT,
        wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT]
    payments = TransactionManager().get_dining_payment_type_transactions(payee_id=merchant_id, payee_store_id=store_id,
        state=wallet_pb.Transaction.SUCCESS, start_time=start_time, end_time=end_time, type_list=type_list)
    user_dict = {}
    total_fee_dict = {}
    for payment in payments:
        if payment.payer_id in user_dict:
            user_dict[payment.payer_id] += 1
            total_fee_dict[payment.payer_id] += payment.bill_fee
        else:
            user_dict[payment.payer_id] = 1
            total_fee_dict[payment.payer_id] = payment.bill_fee

    purchase_count = {}
    purchase_total_fee = {}
    for user_id in user_dict.keys():
        if user_dict[user_id] in purchase_count:
            purchase_count[user_dict[user_id]] += 1
            purchase_total_fee[user_dict[user_id]] += total_fee_dict[user_id]
        else:
            purchase_count[user_dict[user_id]] = 1
            purchase_total_fee[user_dict[user_id]] = total_fee_dict[user_id]

    report = '************ {}用户复购报表 ************\n'.format(merchant.basic_info.name)
    repurchase_count = 0
    total_count = 0
    for count in sorted(purchase_count.keys()):
        total_count += count * purchase_count[count]
        if count > 1:
            repurchase_count += count * purchase_count[count]
        report += '{}次复购: {}人  共消费金额: {}元\n'.format(count, purchase_count[count], purchase_total_fee[count] / 100)

    repurchase_rate = 0.0
    if total_count > 0:
        repurchase_rate = repurchase_count / total_count * 100
    report += '复购局数: {}\n'.format(repurchase_count)
    report += '总局数: {}\n'.format(total_count)
    report += '总复购率: {0:.2f}%'.format(repurchase_rate)

    report += '\n用户复购统计:'
    for user_id in sorted(user_dict, key=user_dict.get, reverse=True):
        user = UserDataAccessHelper().get_user(user_id)
        report += '  {: <3}次: {}\n'.format(user_dict[user_id], user.wechat_profile.nickname)
    return report

def generate_coupon_package_refund_report(start_time=None, end_time=None):
    coupon_package_map = {}
    coupons = CouponDataAccessHelper().get_coupon_list(state=coupons_pb.Coupon.DELETED, issue_time_start=start_time,
        issue_time_end=end_time)
    for coupon in coupons:
        id = '{}_{}_{}'.format(coupon.user_id, coupon.coupon_category_id, coupon.issue_time)
        if id in coupon_package_map:
            continue
        merchant = MerchantDataAccessHelper().get_merchant(coupon.merchant_id)
        for coupon_package in merchant.preferences.coupon_config.coupon_packages:
            if coupon_package.coupon_category_id == coupon.coupon_category_id:
                buy_time_str = date_utils.get_datetime_in_timezone(coupon.issue_time,
                    date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S')
                coupon_package_map[id] = {
                    'userId': coupon.user_id,
                    'merchantId': merchant.id,
                    'merchantName': merchant.basic_info.name,
                    'buyTime': buy_time_str,
                    'value': coupon_package.coupon_package_spec.sell_price
                }

    refund_report = '################ 券包退款统计 ################\n'
    refund_report += '用户                                    购买时间                券包价格(元)      商家ID                                    商家名称\n'
    for val in coupon_package_map.values():
        refund_report += '{: <40}{: <24}{: <12}{: <40}{}\n'.format(val['userId'], val['buyTime'],
            val['value'] / 100, val['merchantId'], val['merchantName'])
    return refund_report

def list_merchant_coupon_packages():
    report = '#################### 商家特惠券包信息统计 ####################\n'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if not merchant.stores or not merchant.stores[0].enable_ordering_service:
            continue

        report += '$$$$$ {} $$$$$\n'.format(merchant.basic_info.name)
        if not merchant.preferences.coupon_config.coupon_packages:
            report += '无任何券包\n\n\n'
            continue

        for index, coupon_package in enumerate(merchant.preferences.coupon_config.coupon_packages):
            report += '== 券包{}: {} ==\n'.format(index + 1, coupon_package.name)
            report += '券包总价值: {}元\n'.format(coupon_package.coupon_package_spec.total_value / 100)
            report += '购买价格:   {}元\n'.format(coupon_package.coupon_package_spec.sell_price / 100)
            report += '优惠券数量: {}张\n'.format(coupon_package.coupon_package_spec.coupon_count)
            report += '使用门槛:   {}元\n'.format(coupon_package.coupon_package_spec.least_cost / 100)
            report += '减免金额:   {}元\n'.format(coupon_package.coupon_package_spec.reduce_cost / 100)
            report += '有效期:     {}天\n'.format(coupon_package.coupon_package_spec.coupon_category_spec.date_info.fixed_term)
            if coupon_package.state == coupon_category_pb.CouponCategory.ACTIVE:
                report += '券包状态:   售卖中\n'
            else:
                report += '券包状态:   已下架\n'
            report += '\n'

        report += '\n\n'

    return report

def generate_coupon_package_purchase_report(start_time=None, end_time=None):
    type_list = [wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE]
    payments = TransactionManager().get_dining_payment_type_transactions(state=wallet_pb.Transaction.SUCCESS,
        start_time=start_time, end_time=end_time, type_list=type_list)

    user_da = UserDataAccessHelper()
    merchant_da = MerchantDataAccessHelper()
    order_da = OrderingServiceDataAccessHelper()
    transaction_da = TransactionDataAccessHelper()
    payments.sort(key=lambda x: x.create_time)
    coupon_report = '################ 特惠券包购买统计 ################\n'
    coupon_report += '用户                                   购买时间            券包价格    券包总价值    优惠券数量    使用门槛    已用券数量    购买门店\n'
    use_coupon_orders = []
    total_fee = 0
    user_map = {}
    for payment in payments:
        merchant = merchant_da.get_merchant(payment.payee_id)
        # 不统计超级商户: 智易科技
        if merchant.id == '********************************':
            continue

        total_value = 0
        coupon_count = 0
        least_cost = 0
        for coupon_package in merchant.preferences.coupon_config.coupon_packages:
            if coupon_package.coupon_package_spec.sell_price == payment.bill_fee:
                total_value = coupon_package.coupon_package_spec.total_value
                coupon_count = coupon_package.coupon_package_spec.coupon_count
                least_cost = coupon_package.coupon_package_spec.least_cost

        total_fee += payment.bill_fee
        if payment.payer_id in user_map:
            coupon_report += '{: <40}{}      {: <12}{: <16}{: <16}{: <16}{}          {}\n'.format(payment.payer_id,
                date_utils.get_datetime_in_timezone(payment.create_time, date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S'),
                total_value / 100, coupon_count, least_cost / 100, 'N/A', merchant.basic_info.name)
            continue
        user_map[payment.payer_id] = True

        user = user_da.get_user(payment.payer_id)
        orders = order_da.get_orders(user_id=payment.payer_id, status=dish_pb.DishOrder.PAID)
        used_coupon_count = 0
        for order in orders:
            transaction = transaction_da.get_transaction_by_id(transaction_id=order.transaction_id)
            if transaction.use_coupon_id:
                used_coupon_count += 1
                use_coupon_orders.append(order)

        coupon_report += '{: <40}{}      {: <12}{: <16}{: <16}{: <16}{}          {}\n'.format(user.id, date_utils.get_datetime_in_timezone(payment.create_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S'),
            payment.bill_fee / 100, total_value / 100, coupon_count, least_cost / 100, used_coupon_count, merchant.basic_info.name)

    coupon_report += '\n特惠券包购买数量: {}\n总金额: {}元\n'.format(len(payments), total_fee / 100)

    order_report = '################ 用券交易统计 ################\n'
    order_report += '用户                                    交易时间              原价    用户实付    商家实收    平台补贴    券包价格    券包总价值    优惠券数量    使用门槛    购买门店\n'
    total_subsidy = 0
    use_coupon_orders.sort(key=lambda x: x.approve_time)
    coupon_da = CouponDataAccessHelper()
    coupon_category_da = CouponCategoryDataAccessHelper()
    for order in use_coupon_orders:
        merchant = merchant_da.get_merchant(order.merchant_id)
        # 不统计超级商户: 智易科技
        if merchant.id == '********************************':
            continue

        transaction = transaction_da.get_transaction_by_id(transaction_id=order.transaction_id)
        paid_time_str = date_utils.get_datetime_in_timezone(order.approve_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S')
        subsidy = max(0, order.paid_in_fee - transaction.paid_fee)
        total_subsidy += subsidy

        package_price = 0
        total_value = 0
        coupon_count = 0
        least_cost = 0
        coupon = coupon_da.get_coupon_by_id(transaction.use_coupon_id)
        coupon_category = coupon_category_da.get_coupon_category(id=coupon.coupon_category_id)
        for package in merchant.preferences.coupon_config.coupon_packages:
            if package.coupon_category_id == coupon_category.id:
                package_price = package.coupon_package_spec.sell_price
                total_value = package.coupon_package_spec.total_value
                coupon_count = package.coupon_package_spec.coupon_count
                least_cost = package.coupon_package_spec.least_cost
                break

        order_report += '{: <40}{: <22}{: <8}{: <12}{: <12}{: <12}{: <16}{: <16}{: <16}{}\n'.format(order.user_id, paid_time_str,
            order.bill_fee / 100, transaction.paid_fee / 100, order.paid_in_fee / 100,
            subsidy / 100, package_price / 100, total_value / 100, coupon_count, least_cost / 100, merchant.basic_info.name)

    order_report += '\n用券买单次数: {}\n总补贴金额: {}元\n'.format(len(use_coupon_orders), total_subsidy / 100)
    report = coupon_report + '\n\n\n' + order_report
    return report

def list_merchant_fanpiaos():
    report = '#################### 商家特惠券包信息统计 ####################\n'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if not merchant.stores or not merchant.stores[0].enable_ordering_service:
            continue

        report += '$$$$$ {} $$$$$\n'.format(merchant.basic_info.name)
        if not merchant.preferences.coupon_config.coupon_packages:
            report += '无任何券包\n\n\n'
            continue

        for index, coupon_package in enumerate(merchant.preferences.coupon_config.coupon_packages):
            report += '== 券包{}: {} ==\n'.format(index + 1, coupon_package.name)
            report += '券包总价值: {}元\n'.format(coupon_package.coupon_package_spec.total_value / 100)
            report += '购买价格:   {}元\n'.format(coupon_package.coupon_package_spec.sell_price / 100)
            report += '优惠券数量: {}张\n'.format(coupon_package.coupon_package_spec.coupon_count)
            report += '使用门槛:   {}元\n'.format(coupon_package.coupon_package_spec.least_cost / 100)
            report += '减免金额:   {}元\n'.format(coupon_package.coupon_package_spec.reduce_cost / 100)
            report += '有效期:     {}天\n'.format(coupon_package.coupon_package_spec.coupon_category_spec.date_info.fixed_term)
            if coupon_package.state == coupon_category_pb.CouponCategory.ACTIVE:
                report += '券包状态:   售卖中\n'
            else:
                report += '券包状态:   已下架\n'
            report += '\n'

        report += '\n\n'

    return report

def generate_fanpiao_refund_report(start_time=None, end_time=None):
    fanpiao_report = '################ 饭票退款统计 ################\n'
    fanpiao_report += '用户                                    购买时间                饭票面值(元)      商家ID                                    商家名称\n'
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(status=fanpiao_pb.Fanpiao.INACTIVE, buy_start_time=start_time, buy_end_time=end_time)
    for fanpiao in fanpiao_list:
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id=fanpiao.purchase_merchant_id)
        buy_time_str = date_utils.get_datetime_in_timezone(fanpiao.buy_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S')
        fanpiao_report += '{: <40}{: <24}{: <12}{: <40}{}\n'.format(fanpiao.user_id, buy_time_str,
            fanpiao.total_value / 100, merchant.id, merchant.basic_info.name)

    return fanpiao_report

def generate_fanpiao_purchase_report(start_time=None, end_time=None):
    fanpiao_report = '################ 饭票购买使用统计 ################\n'
    fanpiao_report += '用户                                    购买时间                饭票面值(元)      所在商家名称\n'
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(status=fanpiao_pb.Fanpiao.ACTIVE, buy_start_time=start_time, buy_end_time=end_time)
    total_value = 0
    total_used = 0
    transaction_map = {}
    for fanpiao in fanpiao_list:
        total_value += fanpiao.total_value
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id=fanpiao.purchase_merchant_id)
        buy_time_str = date_utils.get_datetime_in_timezone(fanpiao.buy_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
        fanpiao_report += '{: <40}{: <24}{: <16}{}\n'.format(fanpiao.user_id, buy_time_str,
            fanpiao.total_value / 100, merchant.basic_info.name)
        fanpiao_report += '  交易记录:\n'
        fanpiao_report += '    交易时间            帐单原价        帐单实付        饭票支出\n'
        # 注: 同一笔交易可能用多张饭票卡支付(余额不足的情况)，因此多张饭票的FanpiaoPaymentRecord可能指向同一个Transaction
        for payment in fanpiao.fanpiao_payment_records:
            transaction = TransactionDataAccessHelper().get_transaction_by_id(payment.transaction_id)
            real_paid_fee = payment.paid_fee if not payment.is_refund else 0
            total_used += real_paid_fee

            time_str = date_utils.get_datetime_in_timezone(transaction.paid_time,
                date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
            fanpiao_report += '    - {: <24}{: <12}{: <12}{: <12}{}\n'.format(time_str, transaction.bill_fee / 100,
                transaction.paid_fee / 100, real_paid_fee / 100, '(已退款)' if payment.is_refund else '')

        fanpiao_report += '  余额: {}元\n\n'.format((fanpiao.total_value - fanpiao.total_used_fee) / 100)

    fanpiao_report += '饭票购买总额：{}元\n已使用总额: {}元\n剩余总额: {}元\n'.format(total_value / 100,
        total_used / 100, (total_value - total_used) / 100)
    return fanpiao_report

def generate_merchant_fanpiao_report(merchant_id, start_time=None, end_time=None):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    fanpiao_report = '$$$$$ {}饭票买单记录 $$$$$\n'.format(merchant.basic_info.name)
    fanpiao_report += '用户ID                                  交易时间                帐单原价    帐单实付    饭票支出        饭票面额\n'
    total_fanpiao_value = 0
    total_remained_value = 0
    fanpiao_map = {}
    remained_values = {}
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(merchant_id=merchant_id,
        status=fanpiao_pb.Fanpiao.ACTIVE, buy_start_time=start_time, buy_end_time=end_time)
    for fanpiao in fanpiao_list:
        total_fanpiao_value += fanpiao.total_value
        total_remained_value += (fanpiao.total_value - fanpiao.total_used_fee)
        if fanpiao.total_value in fanpiao_map:
            fanpiao_map[fanpiao.total_value] += 1
            remained_values[fanpiao.total_value] += (fanpiao.total_value - fanpiao.total_used_fee)
        else:
            fanpiao_map[fanpiao.total_value] = 1
            remained_values[fanpiao.total_value] = (fanpiao.total_value - fanpiao.total_used_fee)

        # 注: 同一笔交易可能用多张饭票卡支付(余额不足的情况)，因此多张饭票的FanpiaoPaymentRecord可能指向同一个Transaction
        for payment in fanpiao.fanpiao_payment_records:
            transaction = TransactionDataAccessHelper().get_transaction_by_id(payment.transaction_id)
            real_paid_fee = payment.paid_fee if not payment.is_refund else 0

            time_str = date_utils.get_datetime_in_timezone(transaction.paid_time,
                date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d %H:%M:%S')
            fanpiao_report += '{: <40}{: <24}{: <12}{: <12}{: <8}{: <8}{: <12}\n'.format(fanpiao.user_id,
                time_str, transaction.bill_fee / 100, transaction.paid_fee / 100,
                real_paid_fee / 100, '(已退款)' if payment.is_refund else '', fanpiao.total_value / 100)

    fanpiao_report += '\n饭票购买总面值: {}元\n'.format(total_fanpiao_value / 100)
    fanpiao_report += '饭票剩余总额: {}元\n'.format(total_remained_value / 100)
    fanpiao_report += '饭票购买统计:\n'
    for total_value in sorted(fanpiao_map.keys()):
        fanpiao_report += '    {: >6}元面值: {: >4}张    总价值:{: >8}元    剩余总额:{: >8}元\n'.format(total_value / 100, fanpiao_map[total_value],
                fanpiao_map[total_value] * total_value / 100, remained_values[total_value] / 100)
    return fanpiao_report

def generate_fanpiao_report_for_merchants(start_time=None, end_time=None):
    fanpiao_report = '################ 饭票购买使用统计 ################\n'
    merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
    for merchant in merchants:
        if not merchant.stores or not merchant.stores[0].enable_ordering_service:
            continue

        fanpiao_report += generate_merchant_fanpiao_report(merchant_id=merchant.id,
            start_time=start_time, end_time=end_time)
        fanpiao_report += '\n\n\n'

    return fanpiao_report

def generate_platform_fanpiao_report(start_time=None, end_time=None, periods=None):
    fanpiao_purchase_report = '################ 饭票购买统计 ################\n'
    fanpiao_purchase_report += '用户                                    购买时间                饭票面值(元)      所在商家名称\n'
    total_value = 0
    total_used = 0
    fanpiao_list = FanpiaoDataAccessHelper().get_fanpiaos(status=fanpiao_pb.Fanpiao.ACTIVE,
        buy_start_time=start_time, buy_end_time=end_time)

    header = '用户ID                                  交易时间                订单号                                        帐单原价    商家实收    帐单实付    饭票支出        本单补贴    饭票面额        商家ID        商家名称\n'
    fanpiao_usage_reports = ['################ 饭票使用交易统计 ({}) ################\n{}'.format(
        common_util.get_start_end_time_string(start_time=period[0], end_time=period[1]), header) for period in periods]

    transaction_map = {}
    for fanpiao in fanpiao_list:
        total_value += fanpiao.total_value
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id=fanpiao.purchase_merchant_id)
        buy_time_str = date_utils.get_datetime_in_timezone(fanpiao.buy_time,
            date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S')
        fanpiao_purchase_report += '{: <40}{: <24}{: <16}{}\n'.format(fanpiao.user_id, buy_time_str,
            fanpiao.total_value / 100, merchant.basic_info.name)
        # 注: 同一笔交易可能用多张饭票卡支付(余额不足的情况)，因此多张饭票的FanpiaoPaymentRecord可能指向同一个Transaction
        for payment in fanpiao.fanpiao_payment_records:
            if payment.is_refund:
                continue

            total_used += payment.paid_fee
            transaction = TransactionDataAccessHelper().get_transaction_by_id(payment.transaction_id)
            order = OrderingServiceDataAccessHelper().get_order(transaction_id=transaction.id)
            if not order:
                continue
            subsidy = 'N/A' if transaction.id in transaction_map else max(0, order.paid_in_fee - transaction.paid_fee) / 100
            transaction_map[transaction.id] = True
            for index, period in enumerate(periods):
                if transaction.paid_time >= period[0] and transaction.paid_time <= period[1]:
                    time_str = date_utils.get_datetime_in_timezone(transaction.paid_time,
                        date_utils.TIMEZONE_SHANGHAI).strftime('%Y-%m-%d-%H:%M:%S')
                    fanpiao_usage_reports[index] += '{: <40}{: <24}{: <40}{: <12}{: <12}{: <12}{: <8}{: <12}{: <12}{: <40}{}\n'.format(
                        fanpiao.user_id, time_str, transaction.id, transaction.bill_fee / 100, order.paid_in_fee / 100, transaction.paid_fee / 100,
                        payment.paid_fee / 100, subsidy, fanpiao.total_value / 100, merchant.id, merchant.basic_info.name)

    fanpiao_usage_report = ''
    for report in fanpiao_usage_reports:
        fanpiao_usage_report += report + '\n\n\n'
    return fanpiao_purchase_report + '\n\n\n' + fanpiao_usage_report

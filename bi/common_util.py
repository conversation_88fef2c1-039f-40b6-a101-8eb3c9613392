import proto.bi.common_pb2 as bi_common_pb
from dao.staff_da_helper import StaffDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

def get_time_format_by_time_granularity(time_granularity):
    """根据时间粒度返回时间格式
    """
    if time_granularity == bi_common_pb.HOURLY:
        return '%Y-%m-%d %H:00'

    if time_granularity == bi_common_pb.DAILY:
        return '%Y-%m-%d'

    return None

def get_user_nickname(user_id):
    user = UserDataAccessHelper().get_user(user_id)
    if not user:
        return Non

    return user.member_profile.nickname

def get_user_name(user_id):
    user = UserDataAccessHelper().get_user(user_id)
    if not user:
        return None

    return user.member_profile.name

def get_staff_nickname(staff_id):
    staff = StaffDataAccessHelper().get_staff(staff_id)
    if not staff:
        return

    return staff.member_profile.nickname

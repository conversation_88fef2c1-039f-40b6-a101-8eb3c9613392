import os
import sys

parentPath = os.path.abspath("../")
if parentPath not in sys.path:
    sys.path.insert(0, parentPath)

from scripts.services import service_common
service_common.set_environment_var('test')

import proto.merchant_rules_pb2 as merchant_pb
from bi import coupon_analyzer
from bi import membership_analyzer
from bi import payment_analyzer
from dao.merchant_da_helper import MerchantDataAccessHelper

def generate_report_for_merchant(merchant, start_time=None, end_time=None):
    print('{}报表正在生成...\n\n'.format(merchant.basic_info.name))
    print(payment_analyzer.generate_merchant_payment_report(merchant.id, start_time=start_time, end_time=end_time), '\n')
    print(membership_analyzer.generate_merchant_membership_report(merchant.id, start_time=start_time, end_time=end_time), '\n')
    print(coupon_analyzer.generate_coupon_report(merchant.id, start_time=start_time, end_time=end_time), '\n')
    print(coupon_analyzer.generate_repurchase_report(merchant.id, start_time=start_time, end_time=end_time), '\n')


if __name__ == '__main__':
    if len(sys.argv) == 1:
        merchants = MerchantDataAccessHelper().get_merchant_list(status=merchant_pb.RUNNING)
        for merchant in merchants:
            # 忽略超级商家："智易科技"
            if merchant.id == '1e543376139b474e97d38d487fa9fbe8':
                continue
            generate_report_for_merchant(merchant)
    else:
        merchant_da = MerchantDataAccessHelper()
        for merchant_id in sys.argv[1:]:
            merchant = merchant_da.get_merchant(merchant_id)
            generate_report_for_merchant(merchant)

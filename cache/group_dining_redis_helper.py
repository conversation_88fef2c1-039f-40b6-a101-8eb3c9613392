import logging
import random
import sys

import maya

from cache.redis_client import RedisClient
from common.utils import date_utils
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.invitation_da_helper import Invitation<PERSON>ataAccessHelper
from proto.group_dining import group_dining_pb2 as group_dining_pb

logger = logging.getLogger()

version_info = sys.version_info

key = "group_dining_id_with_latlng"

def get_nearby_group_dinings(lng, lat, radius, visibility=None):
    redis_client = RedisClient().get_connection()
    if not redis_client.exists(key):
        update_nearby_group_dinings(lng, lat, radius, visibility)
    elif redis_client.ttl(key) <= date_utils.ONE_MINUTE * 5:
        randvalue = random.randint(1, 100)
        if randvalue >= 90:
            update_nearby_group_dinings(lng, lat, radius, visibility)

    group_dinings = redis_client.georadius(key, lng, lat, radius, unit='m', withdist=True,
                                           sort="ASC")
    return group_dinings

def update_nearby_group_dinings(lng, lat, radius, visibility=None):
    # 只返回半个小时前在进行的饭局和3天内进行的饭局
    redis_client = RedisClient().get_connection()
    start_time = maya.when("30 minutes ago").datetime().timestamp()
    end_time = maya.when("now").add(days=3).datetime().timestamp()
    state = group_dining_pb.GroupDiningEvent.SCHEDULED
    dinings = GroupDiningDataAccessHelper().get_dining_events(
        event_start_time=start_time, event_end_time=end_time, state=state, visibility=visibility)
    merchants = dict()
    for dining in dinings:
        merchant = merchants.get(dining.merchant_id)
        if not merchant:
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id=dining.merchant_id)
            merchants.update({dining.merchant_id: merchant})
        if len(merchant.stores) <= 0:
            continue
        store = merchant.stores[0]
        store_lng = store.poi.location.longitude
        store_lat = store.poi.location.latitude
        redis_client.geoadd(key, store_lng, store_lat, dining.id)
    redis_client.expire(key, date_utils.ONE_MINUTE * 40)

def add_group_dining_lng_lat_to_redis(dining_id, lng, lat):
    redis_client = RedisClient().get_connection()
    redis_client.geoadd(key, lng, lat, dining_id)

def remove_group_dining_lng_lat_from_redis(dining_id):
    redis_client = RedisClient().get_connection()
    redis_client.zrem(key, dining_id)

def update_user_invitations(user_id):
    key_invitation = "user_invitations_{user_id}".format(user_id=user_id)
    redis_client = RedisClient().get_connection()
    state = [
        group_dining_pb.Invitation.ACCEPTED,
        group_dining_pb.Invitation.PENDING_APPROVE
    ]
    monetary_state = [
        group_dining_pb.Invitation.PAYMENT_PENDING,
        group_dining_pb.Invitation.TRANSFER_PENDING,
        group_dining_pb.Invitation.RED_PACKET_PENDING
    ]
    if not redis_client.exists(key_invitation):
        least_accept_time = maya.when("30 days ago").epoch # 30天内接受的要请
        invitations = InvitationDataAccessHelper().get_invitations(invitee_id=user_id,
                                                                   state=state,
                                                                   monetary_state=monetary_state,
                                                                   accept_time=least_accept_time)
        for invitation in invitations:
            dining_id = invitation.dining_event_id
            dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
            one_day_ago = maya.when("1 days ago").epoch
            if dining.event_time < one_day_ago:
                continue
            redis_client.zadd(key_invitation, {"{}:{}".format(dining_id, invitation.accept_time): invitation.accept_time})

def get_user_invitations(user_id):
    redis_client = RedisClient().get_connection()
    key_invitation = "user_invitations_{user_id}".format(user_id=user_id)
    if not redis_client.exists(key_invitation):
        update_user_invitations(user_id)
    invitations = redis_client.zrange(key_invitation, 0, -1, withscores=True, score_cast_func=int)
    return invitations

def add_invitation(dining, invitation):
    redis_client = RedisClient().get_connection()
    key_invitation = "user_invitations_{user_id}".format(user_id=invitation.invitee_id)
    redis_client.zadd(key_invitation, {"{}:{}".format(dining.id, invitation.accept_time): invitation.accept_time})

def del_invitation(dining, invitation):
    redis_client = RedisClient().get_connection()
    key_invitation = "user_invitations_{user_id}".format(user_id=invitation.invitee_id)
    redis_client.zrem(key_invitation, "{}:{}".format(dining.id, invitation.accept_time))

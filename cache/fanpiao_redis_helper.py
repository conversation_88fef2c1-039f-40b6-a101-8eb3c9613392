import logging

from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys


logger = logging.getLogger()


def fanpiao_count_decorator(fn):
    def inner(*args, **kargs):
        try:
            redis_client = RedisClient().get_connection()
            key = CacheServerKeys.get_fanpiao_count_key()
            if redis_client.exists(key):
                result = int(redis_client.get(key))
                logger.info(f"从缓存获取已购买饭票总数: {result}")
                return result
            result = fn(*args, **kargs)
            logger.info(f"从数据库获取已购买饭票总数: {result}")
            redis_client.set(key, result)
            return result
        except Exception as ex:
            logger.info(f"获取饭票购买量报错: {ex}")
            return 816508
    return inner

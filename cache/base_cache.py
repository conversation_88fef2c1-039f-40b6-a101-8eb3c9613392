# -*- encoding: utf-8 -*-

'''
@Time        :   2024/09/14 13:57:51
基础通用缓存，适用于重量级不做数据修改的缓存，存储类型为string
'''
import json
import logging
import functools

from cache.redis_client import RedisClient


logger = logging.getLogger(__name__)


class StringBaseCache(object):

    _key: str = None
    _expire_gap: int = 15 * 60  # 默认15分钟
    _expire_refresh = False  # 每次cache时重置过期时间
    _allow_type: tuple = (int, str, tuple, list, dict)

    @classmethod
    def _get_client(cls):
        return RedisClient().get_connection()

    @classmethod
    def _get_format_key(cls, *args, **kargs) -> dict:
        raise NotImplementedError

    @classmethod
    def get_keys(cls, func, *args, **kargs) -> dict:
        if cls._key is None:
            raise ValueError(f"Cache error, _key of the {cls.__class__.__name__} class cannot be None.")
        key_map = cls._get_format_key(*args, **kargs)
        if key_map is None:
            logger.warning(f"Cache warning, no relevant key found in the {func.__name__} parameter, args={args}, kargs={kargs}")
            return
        if not isinstance(key_map, dict):
            raise ValueError(f"Cache error, the return value of the _get_format_key method must be of dict type.")
        return cls._key.format(**key_map)

    @classmethod
    def _update_callback(cls, redis_client, *args, **kargs):
        pass

    @classmethod
    def _cache_callback(cls, result, *args, **kargs):
        return result
         
    @classmethod
    def update(cls, func):
        @functools.wraps(func)
        def wrapper(*args, **kargs):
            redis_client = None
            try:
                redis_client = cls._get_client()
                key = cls.get_keys(func, *args, **kargs)
                if key is not None:
                    redis_client.delete(key)
                    cls._update_callback(redis_client, *args, **kargs)
                    logger.info(f"缓存操作成功(update)，client={redis_client}，func={func.__name__}, key={key}")
            except Exception as e:
                logger.exception(f"缓存操作失败(update)，client={redis_client}，func={func.__name__}，args={args}，kargs={kargs}", exc_info=e)
            return func(*args, **kargs)
        return wrapper

    @classmethod
    def cache(cls, func):
        @functools.wraps(func)
        def wrapper(*args, **kargs):
            redis_client = None
            try:
                redis_client = cls._get_client()
                key = cls.get_keys(func, *args, **kargs)
                result = cls._get_cache_from_redis(redis_client, key)
                if result is not None:
                    logger.info(f"缓存操作成功(cache)，client={redis_client}，func={func.__name__}，key={key}")
                    if cls._expire_refresh:
                        redis_client.expire(key, cls._expire_gap)
                    return cls._cache_callback(result, *args, **kargs)
                logger.info(f"缓存操作失效(cache)，client={redis_client}，func={func.__name__}，从业务层获取数据源，key={key}")
            except Exception as e:
                logger.exception(f"缓存操作失败(cache)，client={redis_client}，func={func.__name__}，args={args}，kargs={kargs}", exc_info=e)
            return cls._get_cache_from_func(func, redis_client, key, *args, **kargs)
        return wrapper

    @classmethod
    def _get_cache_from_func(cls, func, redis_client, key, *args, **kargs):
        """从func获取数据源"""
        result = func(*args, **kargs)
        if result is not None and isinstance(result, cls._allow_type):
            cls._set_cache_to_redis(redis_client, key, result)
            return cls._cache_callback(result, *args, **kargs)

    @classmethod
    def _set_cache_to_redis(cls, redis_client, key, result):
        redis_client.set(
            key,
            json.dumps({"cache_result": result}, ensure_ascii=False),
            ex=cls._expire_gap
        )

    @classmethod
    def _get_cache_from_redis(cls, redis_client, key):
        """从redis获取数据源"""
        if key is None:
            return
        result = redis_client.get(key)
        if result is None:
            return
        result = json.loads(result)
        return result.get("cache_result")

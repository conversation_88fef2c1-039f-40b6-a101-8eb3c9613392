# -*- encoding: utf-8 -*-

'''
@Time        :   2024/10/23 15:13:47
'''

from cache.base_cache import StringBaseCache


class PrinterDishCache(StringBaseCache):

    _key: str = "printer_cache:dish_bind_printer:{merchant_id}"
    _expire_gap: int = 60 * 60  # 60分钟

    @classmethod
    def _get_format_key(
        cls,
        *args,
        **kargs
    ) -> dict:
        for k, v in kargs.items():
            if k == 'merchant_id' and v is not None and isinstance(v, str):
                return {"merchant_id": v}

# -*- encoding: utf-8 -*-

'''
@Time        :   2024/09/18 17:58:20
'''
from cache.base_cache import StringBaseCache


class DishCatalogCache(StringBaseCache):

    _key: str = "dish_cache:dish_catalog:{merchant_id}"
    _expire_gap: int = 30 * 60  # 30分钟

    @classmethod
    def _get_format_key(
        cls,
        *args,
        **kargs
    ) -> dict:
        f = lambda x: {"merchant_id": x}

        for k in args:
            if hasattr(k, 'merchant_id'):
                merchant_id = k.merchant_id
                if merchant_id is not None and isinstance(merchant_id, str):
                    return f(merchant_id)
            if hasattr(k, 'id') and hasattr(k, 'basic_info') and hasattr(k, 'stores'):
                return f(k.id)
        
        for k, v in kargs.items():
            if k == 'merchant_id' and v is not None and isinstance(v, str):
                return f(v)
            if k == 'merchant' and hasattr(v, 'id'):
                return f(v.id)
            if isinstance(v, (list, tuple)) and len(v) > 0 and hasattr(v[0], 'merchant_id'):
                return f(v[0].merchant_id)
            if hasattr(v, 'merchant_id'):
                merchant_id = v.merchant_id
                if merchant_id is not None and isinstance(merchant_id, str):
                    return f(merchant_id)
            if hasattr(v, 'id') and hasattr(v, 'basic_info') and hasattr(v, 'stores'):
                return f(v.id)

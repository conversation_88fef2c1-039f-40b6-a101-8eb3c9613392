import json
import logging
import sys

from google.protobuf import json_format

from cache.redis_client import RedisClient
from common.utils import date_utils
from proto import user_pb2 as user_pb

logger = logging.getLogger()
version_info = sys.version_info


def get_user_redis(fn):
    def inner(*args, **kargs):
        try:
            user_id = kargs.get("user_id") or args[1]
        except:
            user_id = None
        if not user_id:
            return None
        redis_client = RedisClient().get_connection()
        if redis_client:
            cache_data = redis_client.get(user_id)
            if cache_data:
                if version_info.minor < 7:
                    cache_data = cache_data.decode('utf8')
                user_info = json.loads(cache_data)
                if user_info.get("memberProfile") and user_info.get("memberProfile").get("headImageUrl") != "":
                    user_info["wechatProfile"]["headimgurl"] = user_info.get("memberProfile").get("headImageUrl")
                if user_info.get("memberProfile") and user_info.get("memberProfile").get("nickname") != "":
                    user_info["wechatProfile"]["nickname"] = user_info.get("memberProfile").get("nickname")
                return json_format.ParseDict(user_info, user_pb.User(), ignore_unknown_fields=True)
        user_info = fn(*args, **kargs)
        if user_info:
            if user_info.member_profile.head_image_url != "":
                user_info.wechat_profile.headimgurl = user_info.member_profile.head_image_url
            if user_info.member_profile.nickname != "":
                user_info.wechat_profile.nickname = user_info.member_profile.nickname
            json_data = json_format.MessageToDict(user_info, including_default_value_fields=True)
            if redis_client:
                redis_client.setex(user_id, date_utils.ONE_HOUR, json.dumps(json_data))
        return user_info
    return inner


def update_user_redis(fn):
    def inner(*args, **kargs):
        try:
            user_id = None
            if kargs.get('user_id'):
                user_id = kargs.get('user_id')
            else:
                user = args[1]
                if isinstance(user, str):
                    user_id = user
                else:
                    user_id = user.id
        except:
            return None
        redis_client = RedisClient().get_connection()
        user = fn(*args, **kargs)
        user_id = user.id
        if redis_client:
            redis_client.delete(user_id)
        return user
    return inner

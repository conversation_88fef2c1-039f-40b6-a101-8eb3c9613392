# -*- coding: utf-8 -*-

import logging

from common.cache_server_keys import CacheServerKeys
from cache.redis_client import RedisClient

logger = logging.getLogger(__name__)


def check_order_can_update(fn):
    def inner(*args, no_check=False, **kargs):
        if no_check:
            return fn(*args, **kargs)
        order = kargs.get("order")
        if not order:
            return fn(*args, **kargs)
        key = CacheServerKeys.get_order_repeat_pay_key(order)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key):
            logger.info("订单: {} 处于支付状态中,不能更新订单信息".format(order.id))
            return None
        return fn(*args, **kargs)
    return inner

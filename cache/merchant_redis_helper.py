# import json

import logging
import sys
import os

from cache.redis_client import RedisClient
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto import merchant_rules_pb2 as merchant_pb

logger = logging.getLogger()

version_info = sys.version_info

key = "merchant_for_latlng_v2.2-1"


def get_nearby_stores(lng, lat, radius):
    redis_client = RedisClient().get_connection()
    if not redis_client.exists(key):
        no_shilai = True
        merchants = MerchantDataAccessHelper().get_merchants_for_latlng(status=merchant_pb.RUNNING)
        # 测试时用于开放时来门店
        dep_env = os.environ.get("DEPLOYMENT_ENV")
        if not no_shilai:
            shilai = MerchantDataAccessHelper().get_merchant("1e543376139b474e97d38d487fa9fbe8")
            merchants.append(shilai)

        for merchant in merchants:

            if len(merchant.stores) <= 0:
                continue
            store = merchant.stores[0]
            if not store.poi:
                continue
            if store.enable_ordering_service and dep_env == "prod":
                # 正式环境不放出扫码点餐的商户
                continue
            lng = store.poi.location.longitude
            lat = store.poi.location.latitude
            redis_client.geoadd(key, lng, lat, merchant.id)
            redis_client.expire(key, 60 * 60)
    return redis_client.georadius(key, lng, lat, radius, unit='m', withdist=True, sort="ASC")

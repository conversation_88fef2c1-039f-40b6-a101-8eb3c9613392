# -*- coding: utf-8 -*-

import time
import random
import json

from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
from business_ops.config_manager import ConfigManager
from cache.redis_client import RedisClient
from common.utils import date_utils


def get_dish_key(dish_id, merchant_id):
    return "dish_key_{}_{}".format(merchant_id, dish_id)


def get_dishes_key(merchant_id):
    return "dishes_key_{}".format(merchant_id)


def get_dish_key_exists(dish_id, merchant_id):
    return "dish_key_exists_{}_{}".format(merchant_id, dish_id)


def get_dishes_key_exists(merchant_id):
    return "dishes_key_exists_{}".format(merchant_id)


def get_dish(fn):
    def inner(*args, no_cache=False, **kargs):
        if no_cache:
            return fn(*args, **kargs)
        id = kargs.get("dish_id")
        merchant_id = kargs.get("merchant_id")
        if None in (id, merchant_id):
            return fn(*args, **kargs)
        key = get_dish_key(id, merchant_id)
        key_exists = get_dish_key_exists(id, merchant_id)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key_exists) and not redis_client.exists(key):
            return None
        cache_data = redis_client.get(key)
        if cache_data and redis_client.ttl(key) > 60:
            data = json.loads(cache_data)
            dish = json_format.ParseDict(data, dish_pb.Dish(), ignore_unknown_fields=True)
        else:
            dish = fn(*args, **kargs)
            if dish:
                json_data = json_format.MessageToJson(dish, including_default_value_fields=True)
                redis_client.setex(key, date_utils.ONE_DAY, json_data)
            redis_client.setex(key_exists, date_utils.ONE_MINUTE * 10, 1)
        return dish
    return inner


def update_dish(fn):
    def inner(*args, **kargs):
        dish = kargs.get("dish")
        if dish:
            id = dish.id
            merchant_id = dish.merchant_id
        else:
            id = kargs.get("id")
            merchant_id = kargs.get("merchant_id")
        key_exists = get_dish_key_exists(id, merchant_id)
        key = get_dish_key(id, merchant_id)
        redis_client = RedisClient().get_connection()
        redis_client.delete(key)
        redis_client.delete(key_exists)

        key = get_dishes_key(merchant_id)
        key_exists = get_dishes_key_exists(merchant_id)
        if redis_client.exists(key):
            dish_json = json_format.MessageToJson(dish, including_default_value_fields=True)
            redis_client.hset(key, dish.id, dish_json)
        config_manager = ConfigManager(merchant_id=merchant_id)
        now = int(time.time())
        config_manager.update_update_timestamp(dish_catalog_update_timestamp=now)
        return fn(*args, **kargs)
    return inner

def update_dishes(fn):
    def inner(*args, **kargs):
        dishes = kargs.get("dishes")
        redis_client = RedisClient().get_connection()
        pipeline = redis_client.pipeline()
        merchant_id = None
        if isinstance(dishes, dict):
            dishes = dishes.values()
            kargs.update({"dishes": dishes})
        for dish in dishes:
            id = dish.id
            merchant_id = dish.merchant_id
            key = get_dish_key(id, merchant_id)
            key_exists = get_dish_key_exists(id, merchant_id)
            pipeline.delete(key)
            pipeline.delete(key_exists)
            key = get_dishes_key(merchant_id)
            key_exists = get_dishes_key_exists(merchant_id)
            if redis_client.exists(key):
                dish_json = json_format.MessageToJson(dish, including_default_value_fields=True)
                # redis_client.hset(key, dish.id, dish_json)
                pipeline.hset(key, dish.id, dish_json)
        pipeline.execute()
        if merchant_id:
            config_manager = ConfigManager(merchant_id=merchant_id)
            now = int(time.time())
            config_manager.update_update_timestamp(dish_catalog_update_timestamp=now)
        return fn(*args, **kargs)
    return inner


def get_all_dishes(fn):
    def inner(*args, **kargs):
        merchant_id = kargs.get("merchant_id")
        key = get_dishes_key(merchant_id)
        key_exists = get_dishes_key_exists(merchant_id)
        redis_client = RedisClient().get_connection()
        pipeline = redis_client.pipeline()
        if kargs.get("nocache"):
            return fn(*args, **kargs)
        if not redis_client:
            return fn(*args, **kargs)
        random_value = random.randint(1, 100)
        if redis_client.ttl(key) < 10 * 60:
            # 如果key的过期还有10分钟
            # 1. 缓存中不存在
            # 2. 缓存存在但是已经快过期了
            if (not redis_client.exists(key) and not redis_client.exists(key_exists)) or random_value < 10:
                # 1. (not redis_client.exists(key) and not redis_client.exists(key_exists)): 缓存不存在,一定要在数据库中去取
                # 2. random_value > 10: 缓存存在,但是随机数大于30,从数据库去取数据并更新缓存中的数据.把缓存更新分散到10分钟,避免缓存过期时,大量请求到来时,使数据库压力过大
                dishes = fn(*args, **kargs)
                if len(dishes) <= 0:
                    redis_client.set(key_exists, 1, ex=date_utils.ONE_HOUR)
                    return []
                ret = []
                for dish in dishes:
                    dish_json = json_format.MessageToDict(dish, including_default_value_fields=True)
                    # redis_client.hset(key, dish.id, json.dumps(dish_json))
                    pipeline.hset(key, dish.id, json.dumps(dish_json))
                    ret.append(dish_json)
                ttl = date_utils.ONE_DAY * 7
                # redis_client.expire(key, ttl)
                # redis_client.expire(key_exists, ttl - 10)
                pipeline.expire(key, ttl)
                pipeline.expire(key_exists, ttl - 10)
                pipeline.execute()
                return ret

        if redis_client.exists(key):
            cache_data = redis_client.hgetall(key)
            dishes = []
            for _, dish in cache_data.items():
                dish = json.loads(dish)
                # 此处并不转换成protobuf是因为可能大部分菜品都不会用到,后面实际用到的时候才转换成protobuf
                # dish = json_format.ParseDict(dish, dish_pb.Dish(), ignore_unknown_fields=True)
                dishes.append(dish)
            ttl = date_utils.ONE_DAY * 7 # 菜单接口只要有访问,就延长7天
            redis_client.expire(key, ttl)
            return dishes
        return []
    return inner

# -*- coding: utf-8 -*-

import logging
import json

from google.protobuf import json_format

from cache.redis_client import RedisClient
from proto import merchant_rules_pb2 as merchant_pb

logger = logging.getLogger()


def get_merchant_key(merchant_id):
    return "merchant:{}".format(merchant_id)


def get_merchant(fn):
    def inner(*args, **kargs):
        merchant_id = kargs.get("merchant_id")
        if not merchant_id:
            return fn(*args, **kargs)
        key = get_merchant_key(merchant_id)
        redis_client = RedisClient().get_connection()
        if not redis_client:
            return fn(*args, **kargs)
        merchant = redis_client.get(key)
        if merchant:
            redis_client.set(key, merchant, ex=30 * 60)
            merchant = json.loads(merchant)
            return json_format.ParseDict(merchant, merchant_pb.Merchant(), ignore_unknown_fields=True)
        merchant = fn(*args, **kargs)
        merchant_json = json_format.MessageToDict(merchant, including_default_value_fields=True)
        merchant_json = json.dumps(merchant_json)
        redis_client.set(key, merchant_json, ex=30 * 60)
        return merchant
    return inner


def update_merchant(fn):
    def inner(*args, **kargs):
        merchant_id = kargs.get("merchant_id")
        merchant = kargs.get("merchant")
        if not merchant_id and not merchant:
            return fn(*args, **kargs)
        if not merchant_id:
            merchant_id = merchant.id
        redis_client = RedisClient().get_connection()
        if not redis_client:
            return fn(*args, **kargs)
        key = get_merchant_key(merchant_id)
        redis_client.delete(key)
        return fn(*args, **kargs)
    return inner

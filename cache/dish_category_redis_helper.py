# -*- coding: utf-8 -*-

import json
import time

from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
from business_ops.config_manager import ConfigManager
from cache.redis_client import RedisClient
from common.utils import date_utils


def get_dish_category_key(category_id, merchant_id):
    return "dish_category_key_{}_{}".format(merchant_id, category_id)


def get_dish_category_key_exists(category_id, merchant_id):
    return "dish_category_key_exists_{}_{}".format(merchant_id, category_id)


def get_dish_category(fn):
    def inner(*args, **kargs):
        no_cache = kargs.get("no_cache")
        if no_cache:
            del kargs["no_cache"]
            return fn(*args, **kargs)
        id = kargs.get("id")
        merchant_id = kargs.get("merchant_id")
        if id is None or merchant_id is None:
            return None
        key = get_dish_category_key(id, merchant_id)
        key_exists = get_dish_category_key_exists(id, merchant_id)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key_exists) and not redis_client.exists(key):
            return None
        cache_data = redis_client.get(key)
        if cache_data and redis_client.ttl(key) > 60:
            data = json.loads(cache_data)
            category = json_format.ParseDict(data, dish_pb.DishCategory(), ignore_unknown_fields=True)
        else:
            category = fn(*args, **kargs)
            if category:
                json_data = json_format.MessageToJson(category, including_default_value_fields=True)
                redis_client.setex(key, date_utils.ONE_DAY, json_data)
            redis_client.setex(key_exists, date_utils.ONE_DAY - 600, 1)
        return category
    return inner


def update_dish_category(fn):
    def inner(*args, **kargs):
        category = kargs.get("category")
        if category:
            id = category.id
            merchant_id = category.merchant_id
        else:
            id = kargs.get("id")
            merchant_id = kargs.get("merchant_id")
        key_exists = get_dish_category_key_exists(id, merchant_id)
        key = get_dish_category_key(id, merchant_id)
        redis_client = RedisClient().get_connection()
        redis_client.delete(key)
        redis_client.delete(key_exists)
        config_manager = ConfigManager(merchant_id=merchant_id)
        now = int(time.time())
        config_manager.update_update_timestamp(dish_catalog_update_timestamp=now)
        return fn(*args, **kargs)
    return inner

def update_dish_categories(fn):
    def inner(*args, **kargs):
        categories = kargs.get("categories")
        redis_client = RedisClient().get_connection()
        pipeline = redis_client.pipeline()
        merchant_id = None
        if isinstance(categories, dict):
            categories = categories.values()
            kargs.update({"categories": categories})
        for category in categories:
            id = category.id
            merchant_id = category.merchant_id
            key_exists = get_dish_category_key_exists(id, merchant_id)
            key = get_dish_category_key(id, merchant_id)
            pipeline.delete(key)
            pipeline.delete(key_exists)
        pipeline.execute()
        if merchant_id:
            config_manager = ConfigManager(merchant_id=merchant_id)
            now = int(time.time())
            config_manager.update_update_timestamp(dish_catalog_update_timestamp=now)
        return fn(*args, **kargs)
    return inner

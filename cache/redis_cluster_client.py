# -*- coding: utf-8 -*-

import os

from rediscluster import Cluster<PERSON>on<PERSON>ion<PERSON><PERSON>

from cache.cache_client import CacheClient


class RedisClusterClient(CacheClient):

    def __init__(self):
        nodes = self.get_cluster_nodes()
        self.collection_pool = ClusterConnectionPool(
            startup_nodes = nodes
        )

    def get_connection(self):
        return self.collection_pool.get_random_connection()

    def get_cluster_nodes(self):
        # redis.cluster.server:7001;redis.cluster.server:7002
        nodes = os.environ.get("REDIS_CLUSTER_HOST")
        if ";" in nodes:
            nodes = nodes.split(";")
        else:
            nodes = [nodes]
        startup_nodes = []
        for node in nodes:
            n = node.split(":")
            startup_nodes.append({
                "host": n[0],
                "port": int(n[1])
            })
        return startup_nodes

    def publish(self, channel, message_json):
        conn = self.get_connection()
        conn.publish(channel, message_json)

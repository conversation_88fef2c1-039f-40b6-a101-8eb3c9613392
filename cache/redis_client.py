# -*- coding: utf-8 -*-

import os
import time
import functools

from redis import ConnectionPool
from redis import StrictRedis, Redis
from redis.client import Pipeline

from cache.cache_client import CacheClient
from common.config import config


class SyncPipeline(Pipeline):
    def __init__(self, primary_client, secondary_client, transaction=True, shard_hint=None):
        super().__init__(
            primary_client.connection_pool,
            primary_client.response_callbacks,
            transaction=transaction,
            shard_hint=shard_hint
        )
        self.primary_client = primary_client
        self.secondary_client = secondary_client
        self.commands = []

    def execute_command(self, *args, **kwargs):
        self.commands.append((args, kwargs))
        return super().execute_command(*args, **kwargs)

    def execute(self):
        result = super().execute()

        secondary_pipeline = self.secondary_client.pipeline()
        for command in self.commands:
            args, kwargs = command
            func = args[0].lower()
            if func == "del":
                func = "delete"
            secondary_func = getattr(secondary_pipeline, func)
            secondary_func(*args[1:], **kwargs)
        secondary_pipeline.execute()

        self.commands.clear()
        return result


class RedisSyncClient(Redis):
    """
    Redis数据迁移
    """
    def __init__(self, *args, secondary_client: Redis = None, **kwargs):
        self.secondary_client = secondary_client
        super().__init__(*args, **kwargs)

    def pipeline(self, *args, **kwargs):
        return SyncPipeline(self, self.secondary_client, *args, **kwargs)

    def sync_with_secondary(method):
        """
        对涉及数据更新的操作进行同步处理。
        """
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            result = method(self, *args, **kwargs)
            if self.secondary_client is not None:
                sync_method = getattr(self.secondary_client, method.__name__)
                sync_method(*args, **kwargs)
            return result
        return wrapper

    def _sync_string(self, name):
        if self.secondary_client is None:
            return
        if not self.secondary_client.exists(name):
            value_on_secondary = super().get(name)
            if value_on_secondary:
                self.secondary_client.set(name, value_on_secondary)

    def _sync_list(self, name):
        if self.secondary_client is None:
            return
        if not self.secondary_client.exists(name):
            secondary_list = super().lrange(name, 0, -1)
            if secondary_list:
                self.secondary_client.rpush(name, *secondary_list)

    def _sync_set(self, name):
        if self.secondary_client is None:
            return
        if not self.secondary_client.exists(name):
            secondary_members = super().smembers(name)
            if secondary_members:
                self.secondary_client.sadd(name, *secondary_members)

    def _sync_zset(self, name):
        if self.secondary_client is None:
            return
        if not self.secondary_client.exists(name):
            secondary_zset = super().zrange(name, 0, -1, withscores=True)
            if secondary_zset:
                for member, score in secondary_zset:
                    self.secondary_client.zadd(name, {member: score})

    def _sync_hash(self, name):
        if self.secondary_client is None:
            return
        if not self.secondary_client.exists(name):
            secondary_hash = super().hgetall(name)
            if secondary_hash:
                self.secondary_client.hmset(name, secondary_hash)

    # 字符串
    @sync_with_secondary
    def set(self, name, *args, **kwargs):
        return super().set(name, *args, **kwargs)

    @sync_with_secondary
    def setex(self, name, *args, **kwargs):
        return super().setex(name, *args, **kwargs)

    @sync_with_secondary
    def setnx(self, name, *args, **kwargs):
        return super().setnx(name, *args, **kwargs)

    @sync_with_secondary
    def delete(self, *args, **kwargs):
        return super().delete(*args, **kwargs)

    @sync_with_secondary
    def incr(self, name, *args, **kwargs):
        self._sync_string(name)
        return super().incr(name, *args, **kwargs)

    @sync_with_secondary
    def expire(self, *args, **kwargs):
        return super().expire(*args, **kwargs)

    # 列表
    @sync_with_secondary
    def lpush(self, name,  *args, **kwargs):
        self._sync_list(name)
        return super().lpush(name,  *args, **kwargs)

    @sync_with_secondary
    def lpop(self, name, *args, **kwargs):
        self._sync_list(name)
        return super().lpop(name, *args, **kwargs)

    @sync_with_secondary
    def lrem(self, name, *args, **kwargs):
        return super().lrem(name, *args, **kwargs)

    # 集合
    @sync_with_secondary
    def sadd(self, name, *args, **kwargs):
        self._sync_set(name)
        return super().sadd(name, *args, **kwargs)

    @sync_with_secondary
    def spop(self, name, *args, **kwargs):
        self._sync_set(name)
        return super().spop(name, *args, **kwargs)

    @sync_with_secondary
    def srem(self, name, *args, **kwargs):
        return super().srem(name, *args, **kwargs)

    # 有序集合
    @sync_with_secondary
    def zadd(self, name, *args, **kwargs):
        self._sync_zset(name)
        return super().zadd(name, *args, **kwargs)

    @sync_with_secondary
    def zpopmin(self, name, *args, **kwargs):
        self._sync_zset(name)
        return super().zpopmin(name,  *args, **kwargs)
    
    @sync_with_secondary
    def zrem(self, name, *args, **kwargs):
        self._sync_zset(name)
        return super().zrem(name,  *args, **kwargs)

    # 哈希
    @sync_with_secondary
    def hset(self, name, *args, **kwargs):
        self._sync_hash(name)
        return super().hset(name, *args, **kwargs)

    @sync_with_secondary
    def hsetnx(self, name, *args, **kwargs):
        return super().hsetnx(name, *args, **kwargs)

    @sync_with_secondary
    def hincrby(self, name, *args, **kwargs):
        self._sync_hash(name)
        return super().hincrby(name, *args, **kwargs)

    @sync_with_secondary
    def hdel(self, name, *args, **kwargs):
        return super().hdel(name, *args, **kwargs)


class RedisClient(CacheClient):

    def __init__(self):
        self.redis_connection_pool = ConnectionPool(
            host=os.environ.get("REDIS_HOST"),
            password=os.environ.get("REDIS_PASSWORD"),
            port=int(os.environ.get("REDIS_PORT")),
            db=int(os.environ.get("REDIS_DB", '0')),
            socket_timeout=30,
            socket_keepalive=True,
            retry_on_timeout=True
        )
        self._conn_pool = ConnectionPool(**config.redis_service)

    def get_connection(self, use_new=True):
        if use_new and int(time.time()) > 1728714600:  # 2024-10-12 14:30:00
            return StrictRedis(connection_pool=self._conn_pool)
        return StrictRedis(connection_pool=self.redis_connection_pool)

    def publish(self, channel, message_json):
        conn = self.get_connection()
        return conn.publish(channel, message_json)

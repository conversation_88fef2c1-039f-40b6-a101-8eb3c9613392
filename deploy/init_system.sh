#!/bin/bash
# 设置系统时间Shanghai

USER_ID=$(id -u)

if [ "$USER_ID" -ne 0 ]; then
    echo "A root account is required!"
    exit 1
fi

CURRENT_DIR=$(dirname `readlink -f $0`)
BASE_DIR=$(dirname `readlink -f $CURRENT_DIR/`)
CODE_DIR=$(dirname `readlink -f $BASE_DIR/`)
DOWNLOAD_DIR="$HOME/download"
mkdir -p $DOWNLOAD_DIR

PYTHON_DIR=/usr/local/lib/python3.7.4
if [ ! -d "$PYTHON_DIR" ]; then
    apt-get update
    apt-get install -y build-essential zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libssl-dev libsqlite3-dev libreadline-dev libffi-dev wget git
    cp $CURRENT_DIR/Python-3.7.4.tgz $DOWNLOAD_DIR && tar -xvf $DOWNLOAD_DIR/Python-3.7.4.tgz -C $DOWNLOAD_DIR
    cd $DOWNLOAD_DIR/Python-3.7.4 && ./configure --prefix=$PYTHON_DIR --enable-optimizations && make && make install
    ln -s /usr/local/lib/python3.7.4/bin/python3.7 /usr/local/bin/python3.7
    ln -s /usr/local/lib/python3.7.4/bin/pip3.7 /usr/local/bin/pip3.7
fi

PY_ENV_DIR=$CODE_DIR/env
if [ ! -d "$PY_ENV_DIR" ]; then
    python3.7 -m venv $PY_ENV_DIR
fi

ALI_FONT="/data/fonts"
if [ ! -d $ALI_FONT ]; then
    cp $CURRENT_DIR/Alibaba-PuHuiTi-Regular.otf $ALI_FONT
fi

$PY_ENV_DIR/bin/pip3 install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/
$PY_ENV_DIR/bin/pip3 install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1
export PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION='python'
export LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libjemalloc.so.2 MALLOC_CONF='background_thread:true,dirty_decay_ms:0,muzzy_decay_ms:0'

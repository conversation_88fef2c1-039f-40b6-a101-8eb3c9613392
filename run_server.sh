#!/bin/bash

CURRENT_DIR=$(dirname `readlink -f $0`)
BASE_DIR=$(dirname `readlink -f $CURRENT_DIR/`)

# 可自定义配置路径
_backup_path=$BASE_DIR/backup
_py_env_dir="$HOME/code/shilai-common/env"
_system_python_dir="/usr/local/bin/python3.7"
_pip_source="https://pypi.tuna.tsinghua.edu.cn/simple"
# _pip_source="https://mirrors.aliyun.com/pypi/simple/"

if [ ! "$(ls -A $_backup_path)" ]; then
    _last_release="xxx"
else
    _last_release=$(ls -dt $_backup_path/*/ | head -n 1 | sed 's:/$::' | xargs basename)
fi

function usage() {
    echo -e "\nUsage: $0 [--start|--stop|--list|--reload|--install|--update|--backup|--list_backup|--rollback] [--service_name <arg>] [--version <arg>] [--branch <arg>] [--conf <arg>] [--rollback_version <arg>] [--env_path <arg>] [-h|--help]"
    echo -e "\t\t--install 安装依赖"
    echo -e "\t\t--start 启动服务"
    echo -e "\t\t--stop 停止服务"
    echo -e "\t\t--list 查看服务"
    echo -e "\t\t--reload 重载服务"
    echo -e "\t\t--update 更新服务"
    echo -e "\t\t--backup 备份项目"
    echo -e "\t\t--list_backup 查看备份"
    echo -e "\t\t--rollback 回滚服务【需要在项目真实路径操作，不能在备份目录操作】"
    echo -e "\t\t--rollback_version 回滚版本号"
    echo -e "\t\t--service_name 指定服务名，[ main-service|merchant-assist-service|staff-assist-service|access-token-service ]，默认main_service"
    echo -e "\t\t--version 指定版本号，默认v1.7"
    echo -e "\t\t--conf 指定配置环境test|prod，默认prod"
    echo -e "\t\t--env_path 指定Python环境路径，默认$HOME/code/shilai-common/env"
    echo -e "\t\t-h|--help 查看使用方式"
    echo -e "\tgit操作："
    echo -e "\t\t查看日志 git log"
    echo -e "\t\t代码更新 git pull && git submodule update --init --recursive"
    echo -e "\t\t代码回滚 git reset --hard HEAD^"
    echo -e "\t\t回滚到指定版本 git reset --hard commit_id"
    echo -e "\t案例："
    echo -e "\t\t安装服务 $0 --install --version v1.7 --conf prod"
    echo -e "\t\t启动服务 $0 --start --service_name main-service --version v1.7 --conf prod"
    echo -e "\t\t重载服务 $0 --reload --service_name main-service --version v1.7 --conf prod"
    echo -e "\t\t更新服务 $0 --update --service_name main-service --version v1.7 --branch dev --conf prod"
    echo -e "\t\t停止服务 $0 --stop --service_name main-service --version v1.7"
    echo -e "\t\t查看服务 $0 --list --service_name main-service"
    echo -e "\t\t备份项目 $0 --backup"
    echo -e "\t\t查看备份 $0 --list_backup"
    echo -e "\t\t回滚服务 $0 --rollback --service_name main-service --version v1.7 --conf prod --rollback_version $_last_release\n"
    echo -e "\t\t更新商家助手服务 $0 --update --service_name merchant-assist-service --version v1.6 --branch dev --env_path $BASE_DIR/env --conf prod"
    echo -e "\t\t更新业务助手服务 $0 --update --service_name staff-assist-service --version v1.6 --branch dev --env_path $BASE_DIR/env --conf prod"
    echo -e "\t\t更新access-token服务 $0 --update --service_name access-token-service --version v1.6 --branch dev --env_path $BASE_DIR/env --conf prod\n"
    exit 0
}
if [ $# == 0 ]; then
    usage
    exit 1
fi


# 解析参数 ##########################################################
_service_name='main-service'
_version='v1.7'
_branch=
_conf='prod'
_install=false
_action=
_backup=
_list_backup=
_rollback_version=

short_options="h"
long_options="start,stop,list,reload,install,update,backup,list_backup,rollback,rollback_version:,service_name:,version:,branch:,conf:,env_path:,help"
OPTS=$(getopt -o $short_options --long $long_options -n 'parse-options' -- "$@")
eval set -- "$OPTS"

while true; do
    case $1 in
        --install)
            _action="install"
            shift;;
        --start)
            _action="start"
            shift;;
        --stop)
            _action="stop"
            shift;;
        --list)
            _action="list"
            shift;;
        --reload)
            _action="reload"
            shift;;
        --update)
            _action="update"
            shift;;
        --backup)
            _action="backup"
            shift;;
        --list_backup)
            _action="list_backup"
            shift;;
        --rollback)
            _action="rollback"
            shift;;
        --service_name)
            _service_name=$2
            shift
            shift;;
        --version)
            _version=$2
            shift
            shift;;
        --rollback_version)
            _rollback_version=$2
            shift
            shift;;
        --branch)
            _branch=$2
            shift
            shift;;
        --conf)
            _conf=$2
            shift
            shift;;
        --env_path)
            _py_env_dir=$2
            shift
            shift;;
        -h | --help)
            usage
            shift;;
        *)
            break ;;
    esac
done

# 定义全局变量 ######################################################
_python_dir="$_py_env_dir/bin/python3.7"
_uwsgi_dir="$_py_env_dir/bin/uwsgi"
_project_dir=$CURRENT_DIR

_conf_dir="$_project_dir/conf/$_conf"
_cache_dir="$HOME/.$_service_name"

_uwsgi_pid_file="$_cache_dir/uwsgi_${_service_name}_${_version}.pid"
_uwsgi_conf_file="$_conf_dir/uwsgi_${_service_name}.ini"

_data_dir="/data"

_log_root="/data/logs/shilai/$_service_name"
if [ "$UID" -ne 0 ]; then
    _log_root="$HOME/logs/shilai/$_service_name"
fi
_log_dir="$_log_root/$_version"
_log_file="$_log_dir/uwsgi.log"

# 查看服务 #########################################################
if [[ $_action == 'list' ]]; then
    ps -ef|grep uwsgi_$_service_name |grep $USER |grep -v grep
    exit 0
elif [[ $_action == 'list_backup' ]]; then
    ls -lrth $_backup_path |tail -n 10
    exit 0
fi

# 检测Python环境 ###################################################
echo "=======================> 检测Python环境"
if [ ! -f "$_system_python_dir" ]; then
    bash $_project_dir/deploy/init_system.sh
fi
if [ ! -d "$_py_env_dir" ]; then
    $_system_python_dir -m venv $_py_env_dir
    $_python_dir -m pip install --upgrade pip -r $_project_dir/deploy/requirements.txt -i $_pip_source
fi

if [ ! -d "$_project_dir/conf/$_conf" ]; then
    echo "配置环境 $_conf 不存在!"
    exit 1
fi

# 检测Python配置 ########################################################
echo -e "\n=======================> 设置Python环境"
export SERVICE_NAME=$_service_name
export DEPLOYMENT_ENV=$_conf
export VERSION=$_version
export PYTHONPATH=$_project_dir

# 确认信息 #########################################################
function check_confirm() {
    printf $1
    read confirm
    if [[ $confirm != 'Y' ]] && [[ $confirm != 'y' ]];then
        exit 0
    fi

}
echo -e "\n服务名: $_service_name"
echo "版本号: $_version"
echo "本次操作: ***** $_action *****"
echo "配置环境: $_conf"
echo "项目路径: $_project_dir"
echo "uwsgi配置: $_uwsgi_conf_file"
echo "python环境: $_py_env_dir"
echo "日志文件: $_log_file"

check_confirm "\n请确认以上信息(Y/N): "

if [ ! -d "$_project_dir" ]; then
    echo "服务$_service_name ${_version}版本不存在，请先手动构建代码版本，将项目文件复制到目录：$_project_dir"
    exit 1
fi
if [ ! -f "$_uwsgi_conf_file" ]; then
    echo "服务$_service_name uwsgi配置文件不存在:${_uwsgi_conf_file}，请先手动构建配置！"
    exit 1
fi
mkdir -p $_cache_dir $_log_dir $_project_dir $_backup_path $_data_dir

# 函数定义 ########################################################
function clean_cache() {
    if [[ ! -d $1 ]]; then
        return
    fi
    find $1 -type f -name "*.pyc" | xargs rm -rf
    find $1 -type d -name "__pycache__" | xargs rm -rf
}

function proto_to_py() {
    if [ ! -d ${1} ]; then
        return
    fi
    include_path=("${_project_dir}/core")
    for d in `find ${1}`
    do
        if [[ $d = $1 ]];then
            continue
        elif [[ -f $d ]];then
            if [[ ${d##*.} = "proto" ]];then
                c="${_project_dir}/deploy/protoc $d --python_out=. -I$_project_dir"
                for inc_path in ${include_path[@]};do
                    if [ -d $inc_path ];then
                        c="$c -I${inc_path}"
                    fi
                done
                `$c`
                if [ $? != 0 ];then
                    exit -1
                    # echo "$c ==> $?"
                fi

            fi
        elif [[ -d $d ]];then
            proto_to_py $d
        fi
    done
}

function install() {
    echo -e "\n=======================> 安装相关依赖"
    echo "Installing configuration dependencies."
    sudo chown $USER:$USER -R $_data_dir
    rsync -av --ignore-existing $_project_dir/deploy/data/ $_data_dir/
    echo "Installing protobuf."
    clean_cache $_project_dir/proto && proto_to_py $_project_dir/proto
    clean_cache $_project_dir/core/proto && proto_to_py $_project_dir/core/proto
    echo "Installing pip package."
    $_python_dir -m pip install --upgrade pip -r $_project_dir/deploy/requirements.txt -i $_pip_source -q
}

function start() {
    check_confirm "\n请确认Nginx是否已经截流？(Y/N): "
    echo -e "\nuwsgi配置如下："
    cat $_uwsgi_conf_file
    check_confirm "\n请确认以上uwsgi配置，尤其是端口号、进程数量？(Y/N): "
    echo -e "\n=======================> 服务启动中"
    $_uwsgi_dir --ini $_uwsgi_conf_file --daemonize $_log_file --pidfile $_uwsgi_pid_file
    if [[ $? != 0 ]];then
        echo -e "服务${_service_name}启动失败 ....\n"
        exit 1
    fi
    sleep 1
    echo -e "服务${_service_name}启动成功！"
    echo -e "检测服务：grep -C 10 uWSGI $_log_file"
    echo -e "查看日志：tail -f $_log_file\n"
}

function stop() {
    echo -e "\n=======================> 服务停止中"
    $_uwsgi_dir --stop $_uwsgi_pid_file
    rm -f $_uwsgi_pid_file
    if [[ $? != 0 ]];then
        echo -e "服务${_service_name}停止失败 ....\n"
        exit 1
    fi
    sleep 1
    echo -e "服务${_service_name}已停止!\n"
}

function reload() {
    echo -e "\n=======================> 服务重载中"
    if [ ! -f "$_uwsgi_pid_file" ]; then
        echo "服务 $_service_name 的PID文件不存在: ${_uwsgi_pid_file}"
        exit 1
    fi
    $_uwsgi_dir --reload $_uwsgi_pid_file
    if [[ $? != 0 ]];then
        echo -e "服务${_service_name}重载失败 ....\n"
        exit 1
    fi
    sleep 1
    echo -e "服务${_service_name}重载成功！"
    echo -e "检测服务：grep -C 10 uWSGI $_log_file"
    echo -e "查看日志：tail -f $_log_file\n"
}

function backup() {
    echo -e "\n=======================> 项目备份中"
    timestamp=$(date "+%Y%m%d%H%M")
    local suffix="${1:-$timestamp}"
    release_name=$(basename $_project_dir)
    commit_id=$(git log -1 --format=format:%H)
    new_path=${_backup_path}/${release_name}_${commit_id}_${suffix}
    rm -rf $new_path
    echo "备份路径：$new_path"
    cp -r $_project_dir $new_path
}

function update() {
    check_confirm "\n请确认Nginx是否已经截流？(Y/N): "
    backup
    echo -e "\n=======================> 服务更新中"
    if [ -z $_branch ]; then
        echo "请输入代码分支参数，如：--branch dev"
        exit 1
    fi
    git pull origin $_branch && git submodule update --init --recursive
    if [[ $? -ne 0 ]];then
        echo "代码更新失败 ...."
        exit 1
    fi
    install
    echo -e "代码更新状态："
    git status
    echo -e "\nuwsgi配置如下："
    cat $_uwsgi_conf_file
    check_confirm "\n请确认以上uwsgi配置，尤其是端口号、进程数量？(Y/N): "
    reload
    if [[ $? -ne 0 ]];then
        echo "服务重载失败 ...."
        exit 1
    fi
    echo "服务已成功重载！"
    sleep 1
    echo -e "备份版本如下："
    ls -lrth $_backup_path |tail -n 5
    echo ""
}

function rollback() {
    echo -e "\n=======================> 服务回滚中"
    if [ -z $_rollback_version ];then
        echo "请输入--rollback_version ${_last_release}版本号，使用--list_backup查看需回滚的版本号"
        exit 1
    fi
    rollback_path=$_backup_path/$_rollback_version
    if [ ! -d $rollback_path ];then
        echo "回滚路径：${rollback_path}不存在，请重新输入参数rollback_version回滚版本号"
        exit 1
    fi

    echo -e "当前回滚的项目路径：$rollback_path"
    backup "question"
    find $_project_dir -mindepth 1 ! -path "$_project_dir/run_server.sh" -delete
    rsync -a --exclude="run_server.sh" $rollback_path/ $_project_dir/
    if [[ $? -ne 0 ]];then
        echo "代码回滚失败 ...."
        exit 1
    fi
    echo "代码回滚成功！"
    reload
}


# 程序入口 ########################################################
if [[ $_action == 'install' ]]; then
    install
elif [[ $_action == 'start' ]]; then
    start
elif [[ $_action == 'stop' ]]; then
    stop
elif [[ $_action == 'reload' ]]; then
    reload
elif [[ $_action == 'update' ]]; then
    update
elif [[ $_action == 'backup' ]]; then
    backup
elif [[ $_action == 'rollback' ]]; then
    rollback
fi

# sudo apt-get install openssl libssl-dev export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/lib/x86_64-linux-gnu

# 组局约饭相关系统消息
PRIVATE_DINING_EVENT_SCHEDULE_SUCCESS = '饭局"{event_title}"发起成功！进入饭局详情页可以实时查看约饭进展。'
PRIVATE_DINING_EVENT_SCHEDULE_FAILED = '因为有效时间内参与人数不足，饭局{event_title}自动流局。可以重新发起或者参加更多好玩好吃的饭局哦！'
PRIVATE_DINING_EVENT_NEW_PARTICIPANT = '饭友{nickname}申请加入{event_title}饭局，请及时审核。'
PRIVATE_DINING_EVENT_PARTICIPANT_QUIT = '饭友{nickname}已退出{event_title}饭局。'
DINING_EVENT_REMINDER = '距离饭局{event_title}开始还有{time_to_event}，目标组局人数{target_count}人，已加入人数{num_participants}人。'
SIGN_IN_REMINDER = '距离饭局{event_title}开始还有30分钟，到店后记得进入饭局页面点击签到，享受组局优惠！'
AA_PAYMENT_REMINDER = '恭喜您完成{event_title}饭局，已节省{total_saving}元，通过付款功能支付{paid_fee}元，平台已自动发起AA收款，进入饭局页面可查看账单详情。'
TREATMENT_PAYMENT_REMINDER = '恭喜您完成{event_title}饭局，已节省{total_saving}元，通过付款功能支付{paid_fee}元，进入饭局页面可查看账单详情。'

PRIVATE_DINING_EVENT_REQUEST_APPROVED = '恭喜！您已通过{event_title}饭局申请，赶紧进入饭局页面和饭友打声招呼吧！'
PRIVATE_DINING_EVENT_CANCELED = '局长已取消"{event_title}"饭局。可以去约饭页寻找更多好玩好吃的饭局哦！'
PRIVATE_DINING_EVENT_AA_PAYMENT_REMINDER = '恭喜您完成"{event_title}"饭局，进入饭局页查看待付款详情，AA付款后即可和饭友一起瓜分现金红包！'
PRIVATE_DINING_EVENT_TREATMENT_PAYMENT_REMINDER = '{event_title}饭局已由局长请客买单，马上进入饭局页和局长一起瓜分现金红包！'
NEW_COUPON = '您有一张{store_name}的代金券己到账,快去领取吧~'
PAY_GROUP_DINING = '饭局{title}己支付'
GROUP_DINING_TRANSFER = '您收到在{store_name}"{title}"饭局"{nickname}"的付款'
CREATE_GROUP_DINING = '您在{store_name}发起了"{title}"饭局'
AGREE_APPLY_FOR_GROUP_DINING = '{store_name}"{title}"饭局局长"{director_name}"同意你的申请,你己成功加入饭局'
JOIN_GROUP_DINING = '"{nickname}"加入了你在{store_name}发起的"{title}"的饭局'
CANCEL_GROUP_DINING = '您参与的{store_name}的"{title}"己取消'

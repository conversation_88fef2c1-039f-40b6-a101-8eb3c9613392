import random

import proto.coupons_pb2 as coupons_pb
import proto.coupon_category_pb2 as coupon_category_pb
from business_ops.coupon_manager import CouponManager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from strategy import coupon_strategy_for_user


def update_coupon_for_user(user_id, merchant_id):
    """ 针对指定商家，为指定用户生成新的优惠券投放策略，并进行相应操作。

    Args:
        user_id: (string) 指定用户ID
        merchant_id: (string) 指定商家ID
    """
    results = []
    coupon_manager = CouponManager()
    new_coupon_categories = coupon_strategy_for_user.check_for_new_coupons(user_id, merchant_id)
    for category in new_coupon_categories:
        coupon = coupon_manager.issue_coupon_to_user(category.id, user_id)
        results.append(coupon)

    return results

def is_member(user_id, merchant_id):
    member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id=merchant_id, user_id=user_id)
    if member_cards:
        return True
    else:
        return False

def should_issue_coupon(user_id, merchant_id, coupons):
    """ 根据用户在某一商家所拥有的整体优惠券状态，判断是否应该给用户发新券。

    Args:
        user_id: (string) 指定用户ID
        merchant_id: (string) 指定商家对象ID
        coupons: (list of Coupon) 用户在该商家拥有的全部优惠券
    """
    user = UserDataAccessHelper().get_user(user_id)
    if not is_member(user_id, merchant_id):
        return False

    issued_coupons = []
    accepted_coupons = []
    used_coupons = []
    expired_coupons = []

    for coupon in coupons:
        if coupon.state == coupons_pb.Coupon.ISSUED:
            issued_coupons.append(coupon)
        elif coupon.state == coupons_pb.Coupon.ACCEPTED:
            accepted_coupons.append(coupon)
        elif coupon.state == coupons_pb.Coupon.USED:
            used_coupons.append(coupon)
        elif coupon.state == coupons_pb.Coupon.EXPIRED:
            expired_coupons.append(coupon)

    if len(issued_coupons) == 0 and len(accepted_coupons) == 0:
        return True

    return False

def pick_coupon_category_for_user(user_id, merchant_id):
    categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id)
    candidates = []
    for category in categories:
        if category.issue_scene == coupon_category_pb.CouponCategory.NORMAL:
            candidates.append(category)

    # TODO: 目前策略为从可选项中随机选择一种优惠券进行投放
    if candidates:
        index = random.randint(0, len(candidates) - 1)
        return candidates[index]
    else:
        return None

def pick_num_coupons_for_user(user_id, target_category):
    # TODO: 目前固定为投放1张优惠券
    return 1

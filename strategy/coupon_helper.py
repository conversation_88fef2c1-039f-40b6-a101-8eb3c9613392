import sys

import proto.coupons_pb2 as coupons_pb
import proto.coupon_category_pb2 as coupon_category_pb
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper

def mean(numbers):
    return float(sum(numbers)) / max(len(numbers), 1)

def __get_coupons(user_id, merchant_id, state=None):
    return CouponDataAccessHelper().get_coupon_list(merchant_id=merchant_id, user_id=user_id, state=state)

def get_issued_coupons(user_id, merchant_id):
    return __get_coupons(user_id=user_id, merchant_id=merchant_id, state=coupons_pb.Coupon.ISSUED)

def get_accepted_coupons(user_id, merchant_id):
    return __get_coupons(user_id=user_id, merchant_id=merchant_id, state=coupons_pb.Coupon.ACCEPTED)

def get_used_coupons(user_id, merchant_id):
    return __get_coupons(user_id=user_id, merchant_id=merchant_id, state=coupons_pb.Coupon.USED)

def get_expired_coupons(user_id, merchant_id):
    return __get_coupons(user_id=user_id, merchant_id=merchant_id, state=coupons_pb.Coupon.EXPIRED)

def get_all_coupons(user_id, merchant_id):
    return __get_coupons(user_id=user_id, merchant_id=merchant_id)

def get_min_least_cost_coupon(coupons):
    coupon_category_da = CouponCategoryDataAccessHelper()
    min_least_cost = sys.maxsize
    result = None
    for coupon in coupons:
        category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
        if min_least_cost > category.cash_coupon_spec.least_cost:
            min_least_cost = category.cash_coupon_spec.least_cost
            result = coupon

    return result

def get_max_least_cost_coupon(coupons):
    coupon_category_da = CouponCategoryDataAccessHelper()
    max_least_cost = 0
    result = None
    for coupon in coupons:
        category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
        if max_least_cost < category.cash_coupon_spec.least_cost:
            max_least_cost = category.cash_coupon_spec.least_cost
            result = coupon

    return result

def get_coupons_average_least_cost(coupons):
    least_costs = []
    coupon_category_da = CouponCategoryDataAccessHelper()
    for coupon in coupons:
        coupon_category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            continue
        least_costs.append(coupon_category.cash_coupon_spec.least_cost)

    return mean(least_costs)

def get_coupons_average_reduce_cost(coupons):
    reduce_costs = []
    coupon_category_da = CouponCategoryDataAccessHelper()
    for coupon in coupons:
        coupon_category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            continue
        reduce_costs.append(coupon_category.cash_coupon_spec.reduce_cost)

    return mean(reduce_costs)

def include_new_member_coupon(coupons):
    coupon_category_da = CouponCategoryDataAccessHelper()
    for coupon in coupons:
        coupon_category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            continue
        if coupon_category.issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER:
            return True

    return False

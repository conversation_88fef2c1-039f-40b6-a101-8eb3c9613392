import proto.coupons_pb2 as coupons_pb
from business_ops.sms_manager import SmsManager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from strategy import shilai_brain


class SmsStrategy(object):
    def do_new_coupon_notification_for_merchant(self, merchant_id):
        """为指定商户进行常规性新优惠券发放的用户短信通知操作。

        Args:
            merchant_id: (string) 商户ID
        """
        TEMPLATE_CODE = 'SMS_170351148'

        member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id=merchant_id)
        if not member_cards:
            return

        sms_manager = SmsManager()
        user_da = UserDataAccessHelper()
        coupon_value_dict = {}
        for card in member_cards:
            user_id = card.user_id
            user = user_da.get_user(user_id)
            if not user or not user.HasField('member_profile'):
                continue

            should_notify, coupon_value = self.should_do_new_coupon_notification_for_user(merchant_id, user_id)
            if should_notify:
                phone_number = user.member_profile.mobile_phone
                if coupon_value in coupon_value_dict:
                    coupon_value_dict[coupon_value].append(phone_number)
                else:
                    coupon_value_dict[coupon_value] = [phone_number]

        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if not merchant:
            return
        merchant_name = merchant.basic_info.display_name
        for value in coupon_value_dict:
            params = str({
                'merchant': merchant_name,
                'price': int(value / 100)
            })
            SmsManager().send_batch_notifications(coupon_value_dict[value], TEMPLATE_CODE, template_params=params)

    def send_available_coupon_reminder_for_merchant(self, merchant_id):
        EXACT_VALUE_TEMPLATE_CODES = {
            5: 'SMS_168591697',
            6: 'SMS_168586884',
            8: 'SMS_168591706',
            10: 'SMS_168591699',
            15: 'SMS_169901898',
            20: 'SMS_168591700',
            30: 'SMS_168591701',
            32: 'SMS_168591707',
            40: 'SMS_168586627',
            50: 'SMS_168591703',
            60: 'SMS_168586630',
            70: 'SMS_168591704',
            80: 'SMS_168586631',
            90: 'SMS_168586632',
            100: 'SMS_168586634'
        }

        phone_dict = {}
        member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id=merchant_id)
        if not member_cards:
            return

        user_da = UserDataAccessHelper()
        for card in member_cards:
            user_id = card.user_id
            user = user_da.get_user(user_id)
            if not user or not user.HasField('member_profile'):
                continue

            results = self.__get_max_value_coupon_by_user(merchant_id, user_id)
            if not results:
                continue

            max_value, coupon = results
            value = int(max_value / 100)
            phone_number = user.member_profile.mobile_phone
            template = EXACT_VALUE_TEMPLATE_CODES[value]
            if template not in phone_dict:
                phone_dict[template] = []

            phone_dict[template].append(phone_number)

        # 向用户群发对应面值优惠券短信通知
        for template in phone_dict.keys():
            SmsManager().send_batch_notifications(phone_dict[template], template)

    def __get_max_value_coupon_by_user(self, merchant_id, user_id):
        coupons = []
        issued_coupons = CouponDataAccessHelper().get_coupon_list(merchant_id=merchant_id, user_id=user_id,
            state=coupons_pb.Coupon.ISSUED)
        if issued_coupons:
            coupons.extend(issued_coupons)
        accepted_coupons = CouponDataAccessHelper().get_coupon_list(merchant_id=merchant_id, user_id=user_id,
            state=coupons_pb.Coupon.ACCEPTED)
        if accepted_coupons:
            coupons.extend(accepted_coupons)
        if not coupons:
            return None

        max_value = 0
        result = None
        for coupon in coupons:
            coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
            if not coupon_category:
                continue

            value = coupon_category.wechat_cash_coupon.cash.reduce_cost
            if value > max_value:
                max_value = value
                result = coupon

        return max_value, result

    def do_sms_expiration_reminder_for_merchant(self, merchant_id):
        pass

    def should_do_new_coupon_notification_for_user(self, merchant_id, user_id):
        """判断商户是否需要给指定用户进行优惠券领取短信通知。

        Args:
            merchant_id: (string) 商户ID
            user_id: (string) 用户ID
        """
        issued_coupons = CouponDataAccessHelper().get_coupon_list(merchant_id=merchant_id,
            user_id=user_id, state=coupons_pb.Coupon.ISSUED)
        if issued_coupons:
            max_coupon_value = 0
            for coupon in issued_coupons:
                coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
                if not coupon_category:
                    continue
                if coupon_category.cash_coupon_spec.reduce_cost > max_coupon_value:
                    max_coupon_value = coupon_category.cash_coupon_spec.reduce_cost
            return max_coupon_value > 0, max_coupon_value

        return False, 0

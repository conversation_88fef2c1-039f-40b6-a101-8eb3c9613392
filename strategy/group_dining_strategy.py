import random

from proto.group_dining import group_dining_pb2 as group_dining_pb
from strategy import common

def assign_red_packet_values(total_value, num_participants):
    """基于随机算法为指定参与人数分配红包金额。

    Args:
      total_value: (int) 红包总金额，单位为分
      num_participants: (int) 参与抽取红包的人数

    Returns:
      (list of int) 每位参与者抽取红包份额的列表
    """
    # 先生成一组随机数
    random_values = [random.randint(1, 100) for i in range(num_participants)]
    random_sum = sum(random_values)
    # 将随机数组归一化后乘以红包总价值，即得到每个人的份额
    real_values = [int(value / random_sum * total_value) for value in random_values]
    # 需处理因小数取整后剩下的边角余料
    real_sum = sum(real_values)
    if total_value - real_sum > 0 :
      # 将边角余料赠送给第一位幸运用户
      real_values[0] += total_value - real_sum
    return real_values

def generate_coupon_policies(cost_per_person, total_discount):
    """根据商户提供的人均消费值和总折扣生成相应的饭局优惠组合。

    Args:
      cost_per_person: (int) 人均消费，单位为分
      total_discount: (int) 商家愿意提供的总折扣值，取值为1-100, 20为八折.

    Returns:
      (list of GroupDiningCouponPolicy) 适用该商家的优惠组合设计。
    """
    # 最多8人
    RATIOS = [0.8, 1.6, 2.4, 3.5, 4.5, 5.5, 6.5, 7.5]

    least_costs = [int(ratio * cost_per_person) for ratio in RATIOS]
    least_costs = [common.prettify_discount_value(cost) for cost in least_costs]

    total_discount_values = [cost_per_person * RATIOS[i] * total_discount / 100 for i in range(len(RATIOS))]

    reduce_cost_ratio = 0.5
    red_packet_ratio = 1 - reduce_cost_ratio
    reduce_costs = [int(discount_value * reduce_cost_ratio) for discount_value in total_discount_values]
    red_packet_values = [int(discount_value * red_packet_ratio) for discount_value in total_discount_values]

    if RATIOS[0] * cost_per_person * total_discount / 100 < 200:
      reduce_costs = [int((value + 5) / 10) * 10 for value in reduce_costs]
      red_packet_values = [int((value + 5) / 10) * 10 for value in red_packet_values]
    else:
      reduce_costs = [common.prettify_discount_value(cost) for cost in reduce_costs]
      red_packet_values = [common.prettify_discount_value(value) for value in red_packet_values]

    # 一个人时不享受抽红包福利
    red_packet_values[0] = 0

    result = []
    for i in range(len(RATIOS)):
      policy = group_dining_pb.GroupDiningCouponPolicy()
      policy.least_cost = least_costs[i]
      policy.reduce_cost = reduce_costs[i]
      policy.red_packet_value = red_packet_values[i]
      policy.target_diner_count = i + 1
      result.append(policy)

    return result

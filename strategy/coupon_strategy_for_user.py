import random

import proto.coupon_category_pb2 as coupon_category_pb
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from strategy import coupon_helper
from strategy import coupon_category_helper

def check_for_new_coupons(user_id, merchant_id):
    # TODO: 目前只基于简单的规则判断是否进行优惠券投放。规则的改进和演变是长期核心任务。
    issued_coupons = coupon_helper.get_issued_coupons(user_id, merchant_id)
    accepted_coupons = coupon_helper.get_accepted_coupons(user_id, merchant_id)

    valid_coupons = []
    if issued_coupons:
        valid_coupons.extend(issued_coupons)
    if accepted_coupons:
        valid_coupons.extend(accepted_coupons)

    target_categories = []
    # 当用户已有新会员券时，不增发普通复购券
    if coupon_helper.include_new_member_coupon(valid_coupons):
        return target_categories

    min_least_cost_category = coupon_category_helper.get_min_least_cost_coupon_category_for_merchant(
        merchant_id, issue_scene=coupon_category_pb.CouponCategory.NORMAL)
    if not min_least_cost_category:
        return target_categories

    min_least_cost_coupon_by_user = coupon_helper.get_min_least_cost_coupon(valid_coupons)
    if not min_least_cost_coupon_by_user:
        target_categories.append(min_least_cost_category)
    else:
        min_least_cost_category_by_user = CouponCategoryDataAccessHelper().get_coupon_category(
            min_least_cost_coupon_by_user.coupon_category_id)
        if not min_least_cost_category_by_user:
            return target_categories

        least_cost_by_user = min_least_cost_category_by_user.cash_coupon_spec.least_cost
        least_cost_by_merchant = min_least_cost_category.cash_coupon_spec.least_cost
        if least_cost_by_user > least_cost_by_merchant:
            target_categories.append(min_least_cost_category)

    # 目前策略为给每位用户发放一张最低门槛券，然后再加上一张随机抽送券
    while len(valid_coupons) + len(target_categories) < 2:
        category = pick_one_coupon_category_for_user(user_id, merchant_id)
        if category:
            target_categories.append(category)
        else:
            break

    return target_categories

def pick_one_coupon_category_for_user(user_id, merchant_id):
    candidates = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id,
        issue_scene=coupon_category_pb.CouponCategory.NORMAL)

    # TODO: 目前策略为从可选项中随机选择一种优惠券进行投放
    if candidates:
        index = random.randint(0, len(candidates) - 1)
        return candidates[index]
    else:
        return None

import proto.coupon_category_pb2 as coupon_category_pb
import proto.strategy_pb2 as strategy_pb
from bi import payment_analyzer
from business_ops.coupon_manager import CouponManager
from business_ops.merchant_manager import MerchantManager
from common.utils import id_manager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from strategy import common

class CouponStrategy(object):
    def update_coupon_categories_for_merchant(self, merchant_id):
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if not merchant:
            return

        if not self.should_add_new_coupon_category(merchant_id):
            return

        coupon_manager = CouponManager()
        new_coupon_strategies = self.pick_new_coupon_strategies_for_merchant(merchant_id)
        for strategy in new_coupon_strategies:
            coupon_manager.create_coupon_category(merchant, strategy)

    def should_add_new_coupon_category(self, merchant_id):
        coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id)
        exclude_new_member_categories = []
        for category in coupon_categories:
            if category.issue_scene == coupon_category_pb.CouponCategory.NORMAL:
                exclude_new_member_categories.append(category)

        # TODO: 目前只用最简单的逻辑判断是否需要制新券, 将来判断条件需要不断进化迭代。
        return len(exclude_new_member_categories) == 0

    def pick_new_coupon_strategies_for_merchant(self, merchant_id):
        payment_stats = payment_analyzer.get_merchant_payment_stats(merchant_id)
        if not payment_stats:
            return
        merchant = MerchantManager().get_valid_merchant(merchant_id)
        if not merchant:
            return

        # 满减金额门槛档次，单位为分
        LEAST_COST_LEVELS = [1000, 1500, 2000, 2500, 5000, 7500, 10000, 15000, 20000, 30000, 50000]

        # 获取商家复购券减免力度
        STANDARD_DISCOUNT = 0.2
        max_discount = STANDARD_DISCOUNT
        merchant_max_discount = merchant.preferences.coupon_config.max_discount / 100.0
        # 设置一条安全线，目前平台发复购券最大折扣力度不超过7折。当超过5折时，则认为无效，采用缺省折扣。
        if merchant_max_discount > 0 and merchant_max_discount <= 0.3:
            max_discount = merchant_max_discount
        # 如果商家上线了饭局功能，则单人用券场景不可超过饭局优惠力度
        if len(merchant.stores) > 0 and merchant.stores[0].enable_group_dining:
            group_dining_discount = merchant.stores[0].group_dining_discount / 100.0
            if max_discount > group_dining_discount:
                max_discount = group_dining_discount

        half_max_discount = max_discount * 0.5

        # 按照最大优惠力度的减免方案
        max_reduce_cost_levels = [int(max_discount * least_cost) for least_cost in LEAST_COST_LEVELS]
        # 美化一下减免数额(例如把44修正为45等)
        max_reduce_cost_levels = [common.prettify_discount_value(reduce_cost) for reduce_cost in max_reduce_cost_levels]

        # 按照最大优惠力度减半的减免方案
        half_reduce_cost_levels = [int(half_max_discount * least_cost) for least_cost in LEAST_COST_LEVELS]
        half_reduce_cost_levels = [common.prettify_discount_value(reduce_cost) for reduce_cost in half_reduce_cost_levels]

        high_range_index = len(LEAST_COST_LEVELS) - 1
        for i in range(len(LEAST_COST_LEVELS)):
            if payment_stats.avg_bill_fee < LEAST_COST_LEVELS[i]:
                high_range_index = i
                break

        indice = range(high_range_index + 1)
        if len(indice) > 4:
            indice = indice[-4:]

        strategies = []
        for index in indice:
            max_discount_coupon = self.__create_coupon_spec(LEAST_COST_LEVELS[index], max_reduce_cost_levels[index])
            strategies.append(max_discount_coupon)
            # TODO: 需要进一步评估及思考半折扣力度优惠券的投放，目前先暂停。
            #half_discount_coupon = self.__create_coupon_spec(LEAST_COST_LEVELS[index], half_reduce_cost_levels[index])
            #strategies.append(half_discount_coupon)

        return strategies

    def __create_coupon_spec(self, least_cost, reduce_cost):
        coupon_spec = strategy_pb.CouponCategorySpec()
        coupon_spec.id = id_manager.generate_coupon_strategy_id()
        coupon_spec.name = "消费分档代金券"
        coupon_spec.cash_coupon_spec.least_cost = least_cost
        coupon_spec.cash_coupon_spec.reduce_cost = reduce_cost
        coupon_spec.cash_coupon_spec.coupon_category_spec.issue_scene = coupon_category_pb.CouponCategory.NORMAL
        coupon_spec.cash_coupon_spec.coupon_category_spec.get_limit = 1000
        coupon_spec.cash_coupon_spec.coupon_category_spec.use_limit = 1000
        coupon_spec.cash_coupon_spec.coupon_category_spec.can_share = False
        coupon_spec.cash_coupon_spec.coupon_category_spec.can_give_friend = False

        coupon_spec.cash_coupon_spec.coupon_category_spec.date_info.type = 'DATE_TYPE_FIX_TERM'
        # TODO: 目前固定为领取即可使用
        coupon_spec.cash_coupon_spec.coupon_category_spec.date_info.fixed_begin_term = 0
        # TODO: 目前固定为7天
        coupon_spec.cash_coupon_spec.coupon_category_spec.date_info.fixed_term = 7

        return coupon_spec

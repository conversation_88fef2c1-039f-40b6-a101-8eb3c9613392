def prettify_discount_value(discount_value):
    """将不够齐整的折扣金额调整为比较齐整的数值。

    Args:
      discount_value: (int) 原始的折扣数值，单位为分

    Returns:
      经过调整后的折扣数值。
    """
    VALUE_ADJUST_DICT = {
        0: 0,
        100: -100,
        200: 0,
        300: -100,
        400: 100,
        500: 0,
        600: 0,
        700: 100,
        800: 0,
        900: 100
    }
    # 取整
    if discount_value == 0:
        return 100
    discount_value = int(discount_value)
    # 去掉零头，以元为最小单位
    discount_value = discount_value - discount_value % 100
    # 如果金额小于10元，则不做更多调整，直接返回
    if discount_value < 500:
      return discount_value

    # 做尾数调整，使其更贴近大众审美
    adjust_value = VALUE_ADJUST_DICT[discount_value % 1000]
    return discount_value + adjust_value

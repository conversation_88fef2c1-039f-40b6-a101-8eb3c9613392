# -*- coding: utf-8 -*-

import random
import time
import logging
from datetime import datetime

import proto.ordering.discount_pb2 as discount_pb
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
import proto.config_pb2 as config_pb
from common.utils.date_utils import get_current_time


logger = logging.getLogger(__name__)


def generate_discount_plan(merchant_id, user_id):
    """ 针对指定商家和用户，生成针对性的点菜折扣方案。

    Args:
        merchant_id: (string) 指定商家ID
        user_id: (string) 指定用户ID

    Returns:
        (StoreDiscountPlan) 对应的点菜折扣方案
    """
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not (merchant and merchant.stores):
        return
    discount_plan = discount_pb.StoreDiscountPlan()
    discount_plan.update_timestamp = int(time.time())
    if not user_id:
        return discount_plan

    # 初始化关键折扣参数
    dish_max_discount = 0
    dish_min_discount = 0
    red_packet_max_discount = 0
    red_packet_min_discount = 0
    max_discount = merchant.preferences.coupon_config.max_discount + \
        merchant.preferences.coupon_config.shilai_discount

    if merchant.preferences.coupon_config.is_fixed_discount:
        dish_max_discount = max_discount / 2
        dish_min_discount = dish_max_discount
        red_packet_max_discount = max_discount - dish_max_discount
        red_packet_min_discount = red_packet_max_discount
    elif max_discount <= 3:  # 折扣底限仅在97折或97折之上
        red_packet_max_discount = max_discount
        red_packet_min_discount = red_packet_max_discount
    elif max_discount <= 5:  # 折扣底限在95折~97折
        # dish_max_discount = 1.0
        # dish_min_discount = 1.0
        # red_packet_max_discount = max_discount - dish_max_discount
        # red_packet_min_discount = red_packet_max_discount - 0.5
        red_packet_max_discount = max_discount
        red_packet_min_discount = red_packet_max_discount - 0.5
    elif max_discount <= 12:  # 折扣底限在88折~95折
        dish_max_discount = 3.0
        dish_min_discount = 1.0
        red_packet_max_discount = max_discount - dish_max_discount
        red_packet_min_discount = red_packet_max_discount - 0.5
    else:
        dish_max_discount = max_discount / 2
        dish_min_discount = dish_max_discount / 2

        red_packet_max_discount = max_discount / 2
        red_packet_min_discount = red_packet_max_discount - 1
        if max_discount <= 5:
            dish_max_discount = int(max_discount / 2)
            red_packet_max_discount = max_discount - dish_max_discount
            red_packet_min_discount = red_packet_max_discount - 0.5

    dish_discount_rate = merchant.preferences.coupon_config.dish_discount_rate
    red_packet_discount_rate = merchant.preferences.coupon_config.red_packet_discount_rate
    if dish_discount_rate > 0 or red_packet_discount_rate > 0:
        dish_max_discount = round(max_discount * float(dish_discount_rate) / 100)
        dish_min_discount = dish_max_discount
        red_packet_max_discount = max_discount - dish_max_discount
        red_packet_min_discount = red_packet_max_discount

    # 如果门店仅允许顾客使用饭票或者券包买单时才能享受优惠，则立减和红包重置为0
    if merchant.stores[0].fanpiao_coupon_package_discount_only:
        dish_max_discount = 0
        dish_min_discount = 0
        red_packet_max_discount = 0
        red_packet_min_discount = 0

    discount_plan.merchant_id = merchant_id
    discount_plan.store_id = merchant.stores[0].id
    discount_plan.user_id = user_id

    discount_plan.dish_discounts.min_discount = dish_min_discount
    discount_plan.dish_discounts.max_discount = dish_max_discount
    discount_plan.dish_discounts.general_user_discount = int(100 - round(random.uniform(dish_min_discount, dish_max_discount), 2))

    discount_plan.red_packet_discount.min_red_packet_percentage = red_packet_min_discount
    discount_plan.red_packet_discount.max_red_packet_percentage = red_packet_max_discount
    # 目前单个红包最大限额为不超过18元
    discount_plan.red_packet_discount.max_value_cap = 1800

    ordering_da = OrderingServiceDataAccessHelper()
    registration_info = ordering_da.get_registration_info(merchant_id=merchant_id)

    # TODO: 需根据该商户的菜品列表生成相应的菜品折扣方案
    dishes = ordering_da.get_dishes(merchant_id=merchant_id)
    for dish in dishes:
        no_discount = False
        if registration_info.packaging_box_config.dish_id == dish.id:
            no_discount = True
        else:
            for category_id in dish.categories:
                category = ordering_da.get_category(id=category_id, merchant_id=merchant_id)
                if not category:
                    break
                no_discount = category.no_discount
                # 客如云一个菜有可能在多个分类中,只要有一个种类中这个菜不能满减,那么这个菜就不能满减
                if no_discount:
                    break
        if no_discount:
            discount_plan.dish_discounts.dish_discounts[dish.id] = 100
        else:
            price_discount = round(random.uniform(dish_min_discount, dish_max_discount), 2)
            discount_plan.dish_discounts.dish_discounts[dish.id] = int(100 - price_discount)
    return discount_plan


def get_sale_time_discount(merchant_id, timestamp=None):
    configs = ConfigDataAccessHelper().get_promotion_configs(
        merchant_id,
        type=config_pb.PromotionConfig.TypeEnum.SALE_TIME_DISCOUNT,
        return_proto=False
    )
    sale_time_discount_map = dict()
    if not configs:
        return sale_time_discount_map
    
    now_time = get_current_time(timestamp=timestamp)
    for res in configs:
        sale_time_schedule = res.get("saleTimeSchedule", {})
        weekdays = sale_time_schedule.get("weekdays", [])
        date_range = sale_time_schedule.get("dateRange", {})
        time_ranges = sale_time_schedule.get("timeRanges", [])
        if now_time.WEEK_NONE in weekdays:
            weekdays = now_time.WEEK_DAYS
        if now_time.week_day_str not in weekdays:
            continue
        start = date_range.get("start")
        if not start:
            continue
        end = date_range.get("end")
        if not end:
            continue
        if now_time.date_to_datetime(start) > now_time.now or now_time.date_to_datetime(end) < now_time.now:
            continue
        at_now = False
        for time_range in time_ranges:
            start = time_range.get("start")
            if not start:
                continue
            end = time_range.get("end")
            if not end:
                continue
            if now_time.hour_to_timestamp(start) > now_time.now_timestamp or now_time.hour_to_timestamp(end) < now_time.now_timestamp:
                continue
            at_now = True
            break
        if at_now:
            sale_time_price_discount = int(res.get("maxDiscount", 100))
            for category_id in res.get("dishCategoryIds", []):
                sale_time_discount_map[category_id] = sale_time_price_discount

    return sale_time_discount_map


def generate_discount_plan_v2(merchant, user_id, dishes, enbale_sale_time_discount=False, timestamp=None):
    """商家菜品折扣 数据预处理"""
    if not merchant:
        return
    if not merchant.stores:
        return
    discount_plan = discount_pb.StoreDiscountPlan()
    discount_plan.update_timestamp = int(time.time())
    if not user_id:
        return discount_plan

    merchant_id = merchant.id

    # max_discount（全场折扣） = dish_discount_rate（立减） + red_packet_discount（红包，已独立出去）
    # 优先级：fanpiao_coupon_package_discount_only > dish_discount_rate > max_discount
    # 如：立减=100 即全场折扣，0-100 即对max_discount进一步折扣，立减=0 即为不折扣（也不进行全场折扣）
    max_discount = merchant.preferences.coupon_config.max_discount
    dish_discount_rate = merchant.preferences.coupon_config.dish_discount_rate

    price_discount = round(max_discount * float(dish_discount_rate) / 100)
    # 饭票、券包买单不享受立减活动
    if merchant.stores[0].fanpiao_coupon_package_discount_only:
        price_discount = 0

    price_discount = int(100 - price_discount)
    discount_plan.merchant_id = merchant_id
    discount_plan.store_id = merchant.stores[0].id
    discount_plan.user_id = user_id
    discount_plan.dish_discounts.general_user_discount = price_discount

    ordering_da = OrderingServiceDataAccessHelper()
    if not dishes:
        dishes = ordering_da.get_dishes(merchant_id=merchant_id, return_proto=False)
        dishes = {dish['id']: dish for dish in dishes}
        if not dishes:
            from service import errors
            from service import error_codes
            raise errors.Error(err=error_codes.DISH_NOT_FOUND) 

    # 限时折扣配置
    sale_time_discount_map = get_sale_time_discount(merchant_id, timestamp=timestamp) if enbale_sale_time_discount else {}

    # 将折扣策略添加到每个菜品中
    categories = ordering_da.get_categories(merchant_id=merchant_id, return_proto=False)
    categories_map = {item.get('id'): item for item in categories}
    for dish in dishes.values():
        no_discount = False
        _price_discount = price_discount
        for category_id in dish.get("categories", []):
            category = categories_map.get(category_id)
            if not category:
                break
            _price_discount = sale_time_discount_map.get(category_id) or price_discount
            no_discount = category.get("noDiscount", False)
            # 客如云一个菜有可能在多个分类中,只要有一个种类中这个菜不能满减,那么这个菜就不能满减
            if no_discount:
                break
        dish_id = dish["id"]
        if no_discount:
            discount_plan.dish_discounts.dish_discounts[dish_id] = 100
        else:
            discount_plan.dish_discounts.dish_discounts[dish_id] = _price_discount
    return discount_plan

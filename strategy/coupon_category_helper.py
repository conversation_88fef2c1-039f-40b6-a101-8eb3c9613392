import sys

import proto.coupons_pb2 as coupons_pb
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper


def get_max_least_cost_coupon_category_for_merchant(merchant_id, issue_scene=None):
    coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id,
        issue_scene=issue_scene)
    max_least_cost = 0
    result = None
    for category in coupon_categories:
        if max_least_cost < category.cash_coupon_spec.least_cost:
            max_least_cost = category.cash_coupon_spec.least_cost
            result = category

    return result

def get_min_least_cost_coupon_category_for_merchant(merchant_id, issue_scene=None):
    coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id,
        issue_scene=issue_scene)
    min_least_cost = sys.maxsize
    result = None
    for category in coupon_categories:
        if min_least_cost > category.cash_coupon_spec.least_cost:
            min_least_cost = category.cash_coupon_spec.least_cost
            result = category

    return result

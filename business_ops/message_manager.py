import logging
import json
from collections import namedtuple

from google.protobuf import json_format

from dao.private_message_da_helper import PrivateMessageDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.apply_for_group_dining_message_da_helper import ApplyForGroupDiningMessageDataAccessHelper
from dao.system_notification_da_helper import SystemNotificationData<PERSON>ccessHelper
from dao.merchant_da_helper import Merchant<PERSON><PERSON><PERSON>cc<PERSON>H<PERSON>per
from dao.group_dining_da_helper import GroupDiningData<PERSON>ccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from cache.redis_client import RedisClient
from common.utils import date_utils
from common.utils import id_manager
from notification.messages import NEW_COUPON
from notification.messages import PAY_GROUP_DINING
from notification.messages import GROUP_DINING_TRANSFER
from notification.messages import CREATE_GROUP_DINING
from notification.messages import JOIN_GROUP_DINING
from notification.messages import CA<PERSON>EL_GROUP_DINING
from proto.page import message_center_pb2 as message_center_pb
from proto.websocket import common_pb2 as common_pb
from proto.sns import messaging_pb2 as messaging_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb


logger = logging.getLogger(__name__)

def tryexcept(fn):
    def inner(*args, **kargs):
        try:
            return fn(*args, **kargs)
        except Exception as ex:
            logger.exception(ex)
    return inner

class MessageManager():

    # 订单更新
    ORDER_UPDATE = 0x31
    # 订单已支付
    ORDER_PAID = 0x32
    # 订单创建
    ORDER_CREATE = 0x33

    # 购物车菜品更新
    SHOPPING_CARD_MODIFIED = 0x13
    # 购物车菜品清空
    SHOPPING_CARD_CLEAR = 0x14

    def publish_nominate_message(self, dining_id, invitee_id):
        invitations = InvitationDataAccessHelper().get_invitations(dining_id=dining_id)
        event = {
            "type": common_pb.MessageType.Name(common_pb.NOMINATE_DIRECTOR_MESSAGE)
        }
        for invitation in invitations:
            RedisClient().publish(invitation.invitee_id, event)

    def get_private_cnt(self, sender_id, receiver_id):
        """ 获取私聊消息未读总条数
        """
        id = id_manager.generate_message_id(sender_id, receiver_id)
        private_message_da = PrivateMessageDataAccessHelper()
        notify = private_message_da.get_private_message_last_viewed_time(id=id, user_id=receiver_id)
        if notify:
            count = private_message_da.count_unread(id=id, receiver_id=receiver_id,
                                                    last_viewed_time=notify.last_viewed_time)
        else:
            count = private_message_da.count_unread(id=id, receiver_id=receiver_id,
                                                    last_viewed_time=0)
        return count


    def get_private_message_notifies(self, user_id=None, page=None, size=None, last_viewed_time=None):
        private_message_da = PrivateMessageDataAccessHelper()

        notifies = private_message_da.get_private_message_notifies(user_id=user_id,
                                                                   page=page, size=size,
                                                                   orderby=[("lastViewedTime", -1)])
        result = []
        for notify in notifies:
            last_message = private_message_da.get_messages(id=notify.id, page=1, size=1,
                                                           orderby=[("sendTime", -1)])
            if len(last_message) > 0:
                last_message = last_message[0]
            else:
                continue
            message_vo = message_center_pb.PrivateMessage()
            chatter_id = last_message.sender_id
            if last_message.sender_id == user_id:
                chatter_id = last_message.receiver_id
            chatter = UserDataAccessHelper().get_user(chatter_id)
            message_vo.user_id = chatter_id
            message_vo.nickname = chatter.member_profile.nickname
            message_vo.headimgurl = chatter.member_profile.head_image_url
            message_vo.message = last_message.message
            message_vo.create_time = last_message.create_time
            message_vo.message_cnt = private_message_da.count_unread(id=notify.id, receiver_id=user_id,
                                                                     last_viewed_time=notify.last_viewed_time)
            message_vo.message_cnt = PrivateMessageDataAccessHelper().count_unread(id=notify.id,
                                                                                   receiver_id=notify.user_id,
                                                                                   last_viewed_time=notify.last_viewed_time)
            result.append(message_vo)
        return result

    def convert_message_type_to_text(self, type):
        if type == "SYSTEM_NOTIFICATION_NEW_COUPON_MESSAGE":
            return "新会员券领取消息"
        if type == "SYSTEM_NOTIFICATION_PAY_GROUP_DINING":
            return "饭局支付消息"
        if type == "SYSTEM_NOTIFICATION_CREATE_GROUP_DINING":
            return "饭局创建消息"
        if type == "SYSTEM_NOTIFICATION_CANCEL_GROUP_DINING":
            return "饭局取消饭局消息"
        if type == "SYSTEM_NOTIFICATION_APPLY_FOR_GROUP_DINING":
            return "饭局申请消息"
        if type == "SYSTEM_NOTIFICATION_JOIN_GROUP_DINING":
            return "饭局加入消息"
        if type == "SYSTEM_NOTIFICATION_GROUP_DINING_TRANSFER":
            return "饭局转账消息"
        if type == "AGREE_APPLY_FOR_GROUP_DINING":
            return "局长己同意"
        if type == "REJECT_APPLY_FOR_GROUP_DINING":
            return "局长己拒绝"
        return ""

    def get_last_system_notification(self, user_id):
        system_notification_da = SystemNotificationDataAccessHelper()
        last_viewed_time = system_notification_da.get_last_view_message(user_id)
        A = namedtuple("A", "count message messageDate")
        if last_viewed_time:
            count = system_notification_da.count_unread(user_id=user_id,
                                                        last_viewed_time=last_viewed_time.last_viewed_time)
            last_message = system_notification_da.get_messages(user_id=user_id, page=1, size=1,
                                                               orderby=[("createTime", -1)])
            if len(last_message) > 0:
                type = common_pb.MessageType.Name(last_message[0].type)
                message_date = last_message[0].create_time
                last_message = self.convert_message_type_to_text(type)
                return A(count=count, message=last_message, messageDate=message_date)
        return A(count=0, message="", messageDate="")

    def get_last_apply_for_message(self, user_id):
        apply_for_group_dining_mesesage_da = ApplyForGroupDiningMessageDataAccessHelper()
        last_viewed_time = apply_for_group_dining_mesesage_da.get_last_view_message(user_id=user_id)
        A = namedtuple("A", "count message messageDate")
        last_time = last_viewed_time.last_viewed_time if last_viewed_time else 0
        count = apply_for_group_dining_mesesage_da.count_unread(user_id=user_id,
                                                                last_viewed_time=last_time)
        last_message = apply_for_group_dining_mesesage_da.get_messages(director_id=user_id,
                                                                       page=1, size=1,
                                                                       orderby=[("applyTime", -1)])
        if len(last_message) > 0:
            type = "SYSTEM_NOTIFICATION_JOIN_GROUP_DINING"
            message_date = last_message[0].apply_time
            last_message = self.convert_message_type_to_text(type)
            return A(count=count, message=last_message, messageDate=message_date)
        return A(count=0, message="", messageDate="")

    @tryexcept
    def publish_apply_for_message(self, applicant_id, dining, invitation, apply_message):
        """ 发布申请加入饭局的消息到redis,接收人为局长
        """
        user_da = UserDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()

        merchant = merchant_da.get_merchant(dining.merchant_id)
        applicanter = user_da.get_user(applicant_id)
        director = user_da.get_user(dining.director_id)

        event = common_pb.EventMessage()
        message = messaging_pb.ApplyForGroupDiningMessage()
        message.applicant_id = applicant_id
        message.nickname = applicanter.member_profile.nickname
        message.headimgurl = applicanter.member_profile.head_image_url
        message.director_headimgurl = director.member_profile.head_image_url
        message.group_dining_event_id = dining.id
        message.title = dining.title
        message.state = invitation.state
        message.apply_time = invitation.apply_time
        message.store_name = merchant.stores[0].name
        message.director_nickname = director.member_profile.nickname
        message.director_id = dining.director_id

        m = message.messages.add()
        m.nickname = applicanter.member_profile.nickname
        m.message = apply_message
        m.user_id = applicant_id

        event.apply_for_group_dining.CopyFrom(message)
        event.type = common_pb.APPLY_FOR_GROUP_DINING_MESSAGE
        ApplyForGroupDiningMessageDataAccessHelper().add_message(message)
        RedisClient().publish(dining.director_id, json_format.MessageToJson(event))

        last_apply_for = self.get_last_apply_for_message(dining.director_id)
        last_apply_for = {
            "type": common_pb.MessageType.Name(common_pb.LAST_APPLY_FOR),
            "unreadCnt": last_apply_for.count,
            "message": last_apply_for.message,
            "messageDate": last_apply_for.messageDate
        }
        print(last_apply_for)
        RedisClient().publish(dining.director_id, json.dumps(last_apply_for))

    @tryexcept
    def publish_kick_user_message(self, user_ids, dining_id):
        event = common_pb.EventMessage()
        message = messaging_pb.KickUserFromGroupDiningMessage()
        for user_id in user_ids:
            message.user_ids.append(user_id)
        message.group_dining_event_id = dining_id
        event.kick_user_message.CopyFrom(message)
        RedisClient().publish(dining_id, json_format.MessageToJson(event))

    @tryexcept
    def publish_quit_group_dining_message(self, user_id, dining_id):
        """ 发送用户退出饭局通知
        """
        event = common_pb.EventMessage()
        event.type = common_pb.MessageType.QUIT_GROUP_DINING
        message = messaging_pb.QuitGroupDiningMessage()
        message.user_id = user_id
        message.group_dining_event_id = dining_id
        event.quit_group_dining.CopyFrom(message)
        # dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        logger.info('下发用户退出饭局的消息: {}'.format(event))
        RedisClient().publish(dining_id, json_format.MessageToJson(event))

    @tryexcept
    def publish_complete_group_dining_message(self, dining_id):
        """饭局完成消息
        """
        event = common_pb.EventMessage()
        message = messaging_pb.GroupDiningCompleteMessage()
        message.group_dining_event_id = dining_id
        event.group_dining_complete.CopyFrom(message)
        RedisClient().publish(dining_id, json_format.MessageToJson(event))

    @tryexcept
    def cancel_group_dining_event(self, dining_id, user_id, director_id):
        """取消饭局
        """
        event = common_pb.EventMessage()
        event.type = common_pb.MessageType.SYSTEM_NOTIFICATION_CANCEL_GROUP_DINING
        message = messaging_pb.CancelGroupDiningMessage()
        message.group_dining_event_id = dining_id
        event.cancel_group_dining.CopyFrom(message)
        logger.info('下发饭局取消的消息: {}'.format(event))
        RedisClient().publish(dining_id, json_format.MessageToJson(event))

    @tryexcept
    def publish_accept_invitation_message(self, dining_id, invitee_id, director_id):
        """被邀请人受邀消息
        """
        user = UserDataAccessHelper().get_user(invitee_id)
        event = common_pb.EventMessage()
        event.type = common_pb.MessageType.ACCEPT_GROUP_DINING_INVITATION

        message = messaging_pb.AcceptGroupDiningInvitationMessage()
        message.user_id = invitee_id
        message.nickname = user.member_profile.nickname
        message.headimgurl = user.member_profile.head_image_url
        message.group_dining_event_id = dining_id
        event.accept_group_dining_invitation.CopyFrom(message)
        logger.info('下发用户接受饭局邀请的消息: {}'.format(event))
        RedisClient().publish(dining_id, json_format.MessageToJson(event))

    @tryexcept
    def add_or_update_last_viewed_time(self, user_id, type, message_id=None, last_viewed_time=None, deleted=None):
        """ 更新用户最后一次读消息的时间
            1. 读系统消息
            2. 读申请消息
            3. 读私聊消息
        """
        system_notifications = [
            common_pb.SYSTEM_NOTIFICATION,
            common_pb.SYSTEM_NOTIFICATION_NEW_COUPON_MESSAGE,
            common_pb.SYSTEM_NOTIFICATION_PAY_GROUP_DINING,
            common_pb.SYSTEM_NOTIFICATION_GROUP_DINING_TRANSFER,
            common_pb.SYSTEM_NOTIFICATION_AGREE_APPLY_FOR_GROUP_DINING,
            common_pb.SYSTEM_NOTIFICATION_CREATE_GROUP_DINING,
            common_pb.SYSTEM_NOTIFICATION_JOIN_GROUP_DINING,
            common_pb.SYSTEM_NOTIFICATION_CANCEL_GROUP_DINING,
        ]
        private_messages = [
            common_pb.PRIVATE_MESSAGE
        ]
        apply_for_messages = [
            common_pb.APPLY_FOR_GROUP_DINING_MESSAGE
        ]

        # 如果notify不存在,那么就添加一个lastViewedTime为0的记录
        # 如果notify存在,且last_viewed_time大于0,那么就更新

        if type in system_notifications:
            # 更新系统消息的最后阅读时间
            notify = SystemNotificationDataAccessHelper().get_last_view_message(user_id)
            message = messaging_pb.NotificationLastViewedTime()
            message.user_id = user_id
            if not notify:
                message.last_viewed_time = 0
                SystemNotificationDataAccessHelper().add_or_update_last_view_time(message)
            elif last_viewed_time > 0:
                message.last_viewed_time = last_viewed_time
                SystemNotificationDataAccessHelper().add_or_update_last_view_time(message)
        elif type in private_messages:
            # 更新私聊消息的最后阅读时间
            notify = PrivateMessageDataAccessHelper().get_private_message_last_viewed_time(id=message_id,
                                                                                           user_id=user_id)
            if not notify:
                PrivateMessageDataAccessHelper().add_or_update_last_view_time(user_id=user_id, id=message_id,
                                                                              last_viewed_time=0,
                                                                              deleted=deleted)
            elif last_viewed_time > 0:
                PrivateMessageDataAccessHelper().add_or_update_last_view_time(user_id=user_id,
                                                                              id=message_id,
                                                                              last_viewed_time=last_viewed_time,
                                                                              deleted=deleted)
        elif type in apply_for_messages:
            # 更新申请消息的最后阅读时间
            notify = ApplyForGroupDiningMessageDataAccessHelper().get_last_view_message(user_id)
            message = messaging_pb.ApplyForLastViewedTime()
            message.user_id = user_id
            if not notify:
                message.last_viewed_time = 0
                ApplyForGroupDiningMessageDataAccessHelper().add_or_update_last_view_time(message)
            elif last_viewed_time > 0:
                message.last_viewed_time = last_viewed_time
                ApplyForGroupDiningMessageDataAccessHelper().add_or_update_last_view_time(message)

    @tryexcept
    def publish_agree_or_reject(self, receiver_id, user_id, dining_id, state):
        """ 局长同意或者拒绝的时候给申请者发消息
        """
        # 发消息给接收者
        message = ApplyForGroupDiningMessageDataAccessHelper().get_messages(applicant_id=receiver_id,
                                                                             group_dining_event_id=dining_id)[0]
        json_obj = json_format.MessageToDict(message)
        json_obj.update({
            "state": group_dining_pb.Invitation.InvitationState.Name(state)
        })
        ret = {
            "type": common_pb.MessageType.Name(common_pb.APPLICANT_APPLY_FOR_GROUP_DINING_MESSAGE),
            "applyForGroupDining": json_obj
        }
        RedisClient().publish(receiver_id, json.dumps(ret))

        # 发消息给局长
        message = ApplyForGroupDiningMessageDataAccessHelper().get_messages(director_id=user_id,
                                                                            group_dining_event_id=dining_id)[0]
        json_obj = json_format.MessageToDict(message)
        json_obj.update({
            "state": group_dining_pb.Invitation.InvitationState.Name(state)
        })
        ret = {
            "type": common_pb.MessageType.Name(common_pb.DIRECTOR_APPLY_FOR_GROUP_DINING_MESSAGE),
            "applyForGroupDining": json_obj
        }
        RedisClient().publish(user_id, json.dumps(ret))

    @tryexcept
    def publish_system_notification(self, receiver_id, user_id, type, group_dining_id=None,
                                    merchant_id=None):
        """ 发系统消息
        Args:
            receiver_id: (string)收消息的人
            user_id: (string)做出此动作的人
        """
        event = common_pb.EventMessage()
        event.receiver_id = receiver_id
        event.create_time = date_utils.timestamp_second()
        event.type = type
        if merchant_id:
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if group_dining_id:
            dining = GroupDiningDataAccessHelper().get_dining_by_id(group_dining_id)
        if type == common_pb.SYSTEM_NOTIFICATION_NEW_COUPON_MESSAGE:
            # 获得新优惠券
            message = messaging_pb.SystemNotificationNewCouponMessage()
            message.merchant_id = merchant_id
            message.store_name = merchant.stores[0].name
            event.sn_new_coupon.CopyFrom(message)
            event.notification = NEW_COUPON.format(store_name=merchant.stores[0].name)
            image = merchant.basic_info.logo_url
            id = merchant_id
        elif type == common_pb.SYSTEM_NOTIFICATION_PAY_GROUP_DINING:
            # 局长支付
            message = messaging_pb.SystemNotificationPayGroupDining()
            message.group_dining_event_id = group_dining_id
            message.title = dining.title
            message.merchant_id = merchant_id
            event.sn_pay_group_dining.CopyFrom(message)
            event.notification = PAY_GROUP_DINING.format(title=dining.title)
            image = merchant.basic_info.logo_url
            id = group_dining_id
        elif type == common_pb.SYSTEM_NOTIFICATION_GROUP_DINING_TRANSFER:
            # 饭局转账
            message = messaging_pb.SystemNotificationGroupDiningTransfer()
            message.group_dining_event_id = group_dining_id
            user = UserDataAccessHelper().get_user(user_id)
            message.nickname = user.member_profile.nickname
            message.store_name = merchant.stores[0].name
            message.user_id = user_id
            event.sn_group_dining_transfer.CopyFrom(message)
            event.notification = GROUP_DINING_TRANSFER.format(store_name=merchant.stores[0].name,
                                                              nickname=user.member_profile.nickname,
                                                              title=dining.title)
            image = user.member_profile.head_image_url
            id = group_dining_id
            logger.info('饭局转账: {}'.format(message))
        elif type == common_pb.SYSTEM_NOTIFICATION_CREATE_GROUP_DINING:
            # 创建饭局
            message = messaging_pb.SystemNotificationCreateGroupDining()
            message.group_dining_event_id = group_dining_id
            message.title = dining.title
            message.store_name = merchant.stores[0].name
            message.merchant_id = merchant_id
            event.sn_create_group_dining.CopyFrom(message)
            event.notification = CREATE_GROUP_DINING.format(store_name=merchant.stores[0].name,
                                                            title=dining.title)
            image = merchant.basic_info.logo_url
            id = group_dining_id
        elif type == common_pb.SYSTEM_NOTIFICATION_JOIN_GROUP_DINING:
            # 加入饭局
            message = messaging_pb.SystemNotificationJoinGroupDining()
            message.group_dining_event_id = group_dining_id
            message.store_name = merchant.stores[0].name
            message.title = dining.title
            message.merchant_id = merchant_id
            user = UserDataAccessHelper().get_user(user_id)
            event.sn_join_group_dining.CopyFrom(message)
            event.notification = JOIN_GROUP_DINING.format(nickname=user.member_profile.nickname,
                                                          store_name=merchant.stores[0].name,
                                                          title=dining.title)
            image = merchant.basic_info.logo_url
            id = group_dining_id
        elif type == common_pb.SYSTEM_NOTIFICATION_CANCEL_GROUP_DINING:
            # 取消饭局
            message = messaging_pb.SystemNotificationCancelGroupDining()
            message.store_name = merchant.stores[0].name
            message.merchant_id = merchant_id
            message.group_dining_event_id = group_dining_id
            event.sn_cancel_group_dining.CopyFrom(message)
            event.notification = CANCEL_GROUP_DINING.format(store_name=merchant.stores[0].name,
                                                            title=dining.title)
            image = merchant.basic_info.logo_url
            id = group_dining_id

        SystemNotificationDataAccessHelper().add_message(event)

        json_obj = {
            "image": image,
            "notification": event.notification,
            "id": id,
            "createTime": event.create_time,
            "type": common_pb.MessageType.Name(event.type),
            "isSystemNotification": True
        }
        RedisClient().publish(receiver_id, json.dumps(json_obj))

        last_notification = self.get_last_system_notification(receiver_id)
        last_system_notification = {
            "type": common_pb.MessageType.Name(common_pb.LAST_SYSTEM_NOTIFICATION),
            "unreadCnt": last_notification.count,
            "message": last_notification.message,
            "messageDate": last_notification.messageDate
        }
        RedisClient().publish(receiver_id, json.dumps(last_system_notification))


    def get_system_notification_list(self, user_id, page=None, size=None, create_time=None):
        messages = SystemNotificationDataAccessHelper().get_messages(user_id=user_id,
                                                                     page=page, size=size,
                                                                     create_time=create_time,
                                                                     orderby=[('createTime', -1)])
        result = []
        user_da = UserDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        for message in messages:
            type = message.type
            if type == common_pb.SYSTEM_NOTIFICATION_NEW_COUPON_MESSAGE:
                merchant = merchant_da.get_merchant(message.sn_new_coupon.merchant_id)
                image = merchant.basic_info.logo_url
                id = merchant.id
            elif type == common_pb.SYSTEM_NOTIFICATION_PAY_GROUP_DINING:
                merchant = merchant_da.get_merchant(message.sn_pay_group_dining.merchant_id)
                image = merchant.basic_info.logo_url
                id = message.sn_pay_group_dining.group_dining_event_id
            elif type == common_pb.SYSTEM_NOTIFICATION_GROUP_DINING_TRANSFER:
                user = user_da.get_user(message.sn_group_dining_transfer.user_id)
                image = user.member_profile.head_image_url
                id = message.sn_group_dining_transfer.group_dining_event_id
            elif type == common_pb.SYSTEM_NOTIFICATION_AGREE_APPLY_FOR_GROUP_DINING:
                # 同意加入饭局
                merchant = merchant_da.get_merchant(message.sn_agree_apply_for_group_dining.merchant_id)
                image = merchant.basic_info.logo_url
                id = message.sn_apply_for_group_dining.group_dining_event_id
            elif type == common_pb.SYSTEM_NOTIFICATION_CREATE_GROUP_DINING:
                # 创建饭局系统消息
                merchant = merchant_da.get_merchant(message.sn_create_group_dining.merchant_id)
                image = merchant.basic_info.logo_url
                id = message.sn_create_group_dining.group_dining_event_id
            elif type == common_pb.SYSTEM_NOTIFICATION_JOIN_GROUP_DINING:
                # 加入饭局系统消息
                merchant = merchant_da.get_merchant(message.sn_join_group_dining.merchant_id)
                image = merchant.basic_info.logo_url
                id = message.sn_join_group_dining.group_dining_event_id
            elif type == common_pb.SYSTEM_NOTIFICATION_CANCEL_GROUP_DINING:
                # 饭局取消系统消息
                merchant = merchant_da.get_merchant(message.sn_cancel_group_dining.merchant_id)
                image = merchant.basic_info.logo_url
                id = message.sn_cancel_group_dining.group_dining_event_id
            # message = json_format.MessageToDict(message, including_default_value_fields=True)
            result.append({
                "image": image,
                "notification": message.notification,
                "id": id,
                "createTime": message.create_time,
                "type": common_pb.MessageType.Name(message.type)
            })
        return result

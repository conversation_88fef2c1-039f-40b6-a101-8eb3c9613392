# -*- coding: utf-8 -*-

import logging
import time
import json
import uuid
from io import BytesIO
from datetime import datetime
from datetime import timedelta

import qrcode
from google.protobuf import json_format

from dao.message_center.operation_da_helper import OperationDataAccessHelper
import proto.finance.wallet_pb2 as wallet_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.page.fanpiao_pb2 as page_fanpiao_pb
import proto.fanpiao_websocket_pb2 as fanpiao_websocket_pb
import proto.config_pb2 as config_pb
import proto.promotion.coupon.coupon_pb2 as new_coupon_pb
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.merchant_manager import MerchantManager
from business_ops.fanpiao_helper import FanpiaoHelper
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.user_group.user_exp_manager import UserExpManager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from common.utils import distribute_lock, id_manager
from common.utils import date_utils
from common.aliyun_oss_helper import AliyunOSSHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from business_ops.config_manager import ConfigManager
from business_ops.ordering.order_manager import OrderManager
from dao.dao_helper import DaoORMHelper
from dao import constants
from service import errors
from service import error_codes


logger = logging.getLogger(__name__)


class FanpiaoManager:
    def __init__(
        self,
        user_id=None,
        pay_method=None,
        transaction_id=None,
        merchant_id=None,
        fanpiao_category_id=None,
        return_url=None,
        order_id=None,
        merchant=None,
        **kargs,
    ):
        self._fanpiao_da = None
        self._transaction_da = None
        self._merchant_da = None
        self._user_da = None
        self.merchant = None
        self.user = None
        self.user_platform_setting = None

        self.return_url = return_url
        self.order_id = order_id
        self.fanpiao_category = None
        if fanpiao_category_id is not None:
            self.fanpiao_category = self.fanpiao_da.get_fanpiao_category_by_id(fanpiao_category_id)
        if transaction_id is not None:
            self.transaction = self.transaction_da.get_transaction_by_id(transaction_id)
            user_id = self.transaction.payer_id
            pay_method = self.transaction.pay_method
            merchant_id = self.transaction.payee_id

        if merchant:
            self.merchant = merchant

        if self.merchant is None and merchant_id is not None:
            self.merchant = self.merchant_da.get_merchant(merchant_id)

        if user_id is not None:
            self.user = self.user_da.get_user(user_id)
            self.user_platform_setting = self.user_da.get_user_platform_setting(user_id=user_id)

        self.pay_method = pay_method
        # 如果self.fanpiao_category为空,那么就从接口获取fanpiao_category,并填上信息
        self.__fill_fanpiao_category(fanpiao_category_id)

        self._other_actions = kargs.get("other_actions", {})

    @property
    def fanpiao_da(self):
        if self._fanpiao_da:
            return self._fanpiao_da
        self._fanpiao_da = FanpiaoDataAccessHelper()
        return self._fanpiao_da

    @property
    def transaction_da(self):
        if self._transaction_da:
            return self._transaction_da
        self._transaction_da = TransactionDataAccessHelper()
        return self._transaction_da

    @property
    def merchant_da(self):
        if self._merchant_da:
            return self._merchant_da
        self._merchant_da = MerchantDataAccessHelper()
        return self._merchant_da

    @property
    def user_da(self):
        if self._user_da:
            return self._user_da
        self._user_da = UserDataAccessHelper()
        return self._user_da

    def fill_fanpiao_category(self, fanpiao_category_id):
        return self.__fill_fanpiao_category(fanpiao_category_id)

    def __fill_fanpiao_category(self, fanpiao_category_id):
        if self.fanpiao_category is not None:
            return
        if fanpiao_category_id is None:
            return
        exp_manager = UserExpManager(user_id=self.user.id, merchant=self.merchant)
        self.fanpiao_category = exp_manager.get_fanpiao_category_by_id(fanpiao_category_id)

    def get_fanpiao_categories(self):
        if not self.merchant or not self.user:
            return []
        if len(self.merchant.stores) > 0:
            store = self.merchant.stores[0]
            if store.disable_buy_fanpiao:
                return []
        # 如果为封禁用户, 则返回空值
        if self.__is_blocked_user():
            return []
        # 如果为风控用户, 则返回对应饭票类型
        if self.__is_risk_user():
            exp_manager = UserExpManager(user_id=self.user.id, merchant=self.merchant)
            return exp_manager.get_fanpiao_categories_for_risk_control_user(self.merchant.id)

        # 1. 优先返回智能营销策略生成的饭票类型(如适用)
        exp_manager = UserExpManager(user_id=self.user.id, merchant=self.merchant)
        exp_fanpiao_categories = exp_manager.get_fanpiao_categories_by_user_tags(self.merchant.id)
        if exp_fanpiao_categories:
            return exp_fanpiao_categories
        # 2. 智能营销策略未返回适用饭票类型，则返回普通饭票类型列表
        ret = []
        fanpiao_da = FanpiaoDataAccessHelper()
        status = fanpiao_pb.FanpiaoCategory.ACTIVE
        categories = fanpiao_da.get_fanpiao_categories(merchant_id=self.merchant.id, status=status)
        for category in categories:
            if not self.__is_display_for_user(category):
                continue
            quantity = fanpiao_da.count_fanpiao_selling_quantity(
                fanpiao_category_id=category.id, status=fanpiao_pb.Fanpiao.ACTIVE
            )
            category_vo = page_fanpiao_pb.MerchantFanpiaoCategoryVO()
            category_vo.id = category.id
            category_vo.name = category.name
            category_vo.discount = category.discount
            category_vo.merchant_id = category.merchant_id
            category_vo.sell_price = category.sell_price
            category_vo.total_value = category.total_value
            category_vo.selling_quantity = quantity + category.base_selling_quantity
            category_vo.display_scene = category.display_scene
            self.__set_fanpiao_bonus_coupon(category, category_vo)
            ret.append(category_vo)
        return ret

    def __set_fanpiao_bonus_coupon(self, category, category_vo):
        if len(category.bonus_coupon_template_ids) == 0:
            return
        coupon_template_manager = CouponTemplateManager()
        coupon_template = coupon_template_manager.get_coupon_template(id=category.bonus_coupon_template_ids[0])
        if not coupon_template:
            return
        category_vo.bonus_coupon_reduce_fee = coupon_template.fixed_reduce_amount.reduce_fee
        category_vo.bonus_coupon_count = len(category.bonus_coupon_template_ids)

    def __is_blocked_user(self):
        return self.user_platform_setting and self.user_platform_setting.fanpiao_risk_control.is_blocked

    def __is_risk_user(self):
        is_risk_user = (
            self.user_platform_setting is not None and self.user_platform_setting.fanpiao_risk_control.is_purchase_risk_control
        )
        return is_risk_user

    def __is_display_for_user(self, category):
        """这张饭票类型是否显示给用户看
        1. 如果用户是 普通用户 饭票 是 普通饭票 返回 True
        2. 如果用户是 普通用户 饭票 是 风险饭票 返回 False
        3. 如果用户是 风险用户 饭票 是 普通饭票 返回 False
        4. 如果用户是 风险用户 饭票 是 风险饭票
            1. 用户的风险 level == 饭票的风险 level 返回 True
            2. 用户的风险 level != 饭票的风险 level 返回 False
        """
        is_risk_fanpiao = category.is_for_risk_control_user
        is_risk_user = self.__is_risk_user()
        if not is_risk_user and not is_risk_fanpiao:  # 普通用户 x 普通饭票
            return True
        if not is_risk_user and is_risk_fanpiao:  # 普通用户 x 风险饭票
            return False
        if is_risk_user and not is_risk_fanpiao:  # 风险用户 x 普通饭票
            return False
        if is_risk_user and is_risk_fanpiao:  # 风险用户 x 风险饭票
            if self.__is_risk_user_and_risk_category_level_match(category):  # risk.level match
                return True
            else:
                return False
        return False

    def __is_risk_user_and_risk_category_level_match(self, category):
        if not self.user_platform_setting:
            return False
        if category.for_which_risk_user_level != self.user_platform_setting.fanpiao_risk_control.level:
            return False
        return True

    def prepay(self):
        """购买饭票"""
        if self.merchant and len(self.merchant.stores) > 0:
            store = self.merchant.stores[0]
            if not store.enable_fanpiao:
                raise errors.ShowError("该门店未开启购买卡包,请选用其它支付方式")
        if self.order_id is not None:
            order_manager = OrderManager(merchant=self.merchant, business_obj=self)
            order = OrderingServiceDataAccessHelper().get_order(id=self.order_id)
            order_manager.check_dish_remain_quantity(order)

        self.transaction = TransactionManager().get_fanpiao_purchase_transaction(
            user_id=self.user.id,
            pay_method=self.pay_method,
            bill_fee=self.fanpiao_category.total_value,
            paid_fee=self.fanpiao_category.sell_price,
            payee_id=self.merchant.id,
        )
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        # self.__can_buy()

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            result = payment_manager.prepay(
                transaction=self.transaction,
                merchant_id=self.merchant.id,
                fanpiao_category_id=self.fanpiao_category.id,
                order_id=self.order_id,
            )
            self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = payment_manager.prepay(
                transaction=self.transaction,
                merchant_id=self.merchant.id,
                fanpiao_category_id=self.fanpiao_category.id,
                order_id=self.order_id,
                return_url=self.return_url,
                user_id=self.user.id,
            )
        elif self.pay_method == wallet_pb.Transaction.NONE_EXISTS_PAY:
            result = payment_manager.prepay(transaction=self.transaction)
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") != error_codes.SUCCESS:
            raise errors.Error(err=error_codes.BUY_FANPIAO_FAILED)
        self.__set_transaction_other_actions()
        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            self.transaction.state = wallet_pb.Transaction.ORDERED
            TransactionManager().update_transaction(self.transaction)
            result.update({"transactionId": self.transaction.id})
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            self.transaction.state = wallet_pb.Transaction.ORDERED
            TransactionManager().update_transaction(self.transaction)
            result.update({"transactionId": self.transaction.id})
        return result

    def __can_buy(self):
        """用户如果被风控了,则有可能不能购买饭票"""
        if not self.user_platform_setting:
            return
        if not self.user_platform_setting.fanpiao_risk_control.is_purchase_risk_control:
            return
        if not self.fanpiao_category.is_for_risk_control_user:
            raise errors.ShowError("购买卡包过多,暂时无法购买")
        level = self.user_platform_setting.fanpiao_risk_control.level
        if self.fanpiao_category.for_which_risk_user_level != level:
            raise errors.ShowError("购买卡包过多,暂时无法购买")

    def __set_transaction_other_actions(self):
        """如果购买了饭票之后还有其它的动作,把相关的参数记录下来"""
        for other_action in self._other_actions:
            for action, params in other_action.items():
                self.transaction.other_actions.update({action: json.dumps(params)})

    def notification(self, decrease_dish_remain=True):
        """购买饭票,回调"""
        if self.transaction.type != wallet_pb.Transaction.FANPIAO_PURCHASE:
            return
        fanpiao = fanpiao_pb.Fanpiao()
        if not self.fanpiao_category:
            return

        fanpiao.id = id_manager.generate_common_id()
        fanpiao.user_id = self.user.id
        fanpiao.fanpiao_category_id = self.fanpiao_category.id
        fanpiao.purchase_merchant_id = self.merchant.id
        fanpiao.purchase_store_id = "{}_0".format(self.merchant.id)
        fanpiao.total_value = self.fanpiao_category.total_value
        fanpiao.name = self.fanpiao_category.name
        fanpiao.discount = self.fanpiao_category.discount
        fanpiao.buy_time = date_utils.timestamp_second()
        fanpiao.transaction_id = self.transaction.id
        fanpiao.status = fanpiao_pb.Fanpiao.ACTIVE
        fanpiao.sell_price = self.fanpiao_category.sell_price
        fanpiao.merchant_id = (
            self.merchant.brand_info.id
            if self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else self.merchant.id
        )
        fanpiao.max_discount = self.merchant.preferences.coupon_config.max_discount
        fanpiao.short_id = fanpiao.id[:6]
        extra_discount = self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        fanpiao.extra_discount = extra_discount
        if not self.transaction.ledgered:
            payment_manager = PaymentManager(wallet_pb.Transaction.TIAN_QUE_PAY, merchant=self.merchant)
            payment_manager.fanpiao_launch_ledger(self.transaction)
        transaction_manager = TransactionManager()
        self.transaction.paid_time = date_utils.timestamp_second()
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        transaction_manager.update_transaction(self.transaction)

        FanpiaoHelper().set_user_fanpiao_purchase_risk_control(user=self.user, merchant=self.merchant)
        # 智能营销策略可能根据饭票购买情况更新相关配置
        UserExpManager(user_id=self.user.id, merchant=self.merchant).post_process_fanpiao_purchase(fanpiao)
        try:
            redis_client = RedisClient().get_connection()
            fanpiao_count_key = CacheServerKeys.get_fanpiao_count_key()
            redis_client.incr(fanpiao_count_key, 1)
            logger.info(f"增加饭票购买量 1: {fanpiao.id}")
        except Exception as ex:
            logger.error(f"增加饭票购买量报错: {ex}")
        try:
            self.__issue_fanpiao_bonus_coupon(fanpiao)
        except Exception as ex:
            logger.error(f"给用户下发购买饭票优惠券失败: {ex}")
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_da.add_or_update_fanpiao(fanpiao)
        if decrease_dish_remain and self.order_id is not None:
            order_manager = OrderManager(merchant=self.merchant, business_obj=self)
            order = OrderingServiceDataAccessHelper().get_order(id=self.order_id)
            order_manager.decrease_dish_remain_quantity(order)
            order_manager.release_order_lock(order)

    def __issue_fanpiao_bonus_coupon(self, fanpiao):
        logger.info(f"{self.fanpiao_category.id} 配置的优惠券模板ID: {self.fanpiao_category.bonus_coupon_template_ids}")
        if len(self.fanpiao_category.bonus_coupon_template_ids) == 0:
            return
        coupon_template_manager = CouponTemplateManager()
        coupon_manager = CouponManager(merchant=self.merchant, user=self.user)
        for coupon_template_id in self.fanpiao_category.bonus_coupon_template_ids:
            coupon_template = coupon_template_manager.get_coupon_template(id=coupon_template_id)
            logger.info(f"{coupon_template_id} 配置的优惠券模板是否存在: {coupon_template is not None}")
            if not coupon_template:
                continue
            coupon = coupon_manager.create_coupon(coupon_template)
            coupon_manager.update_coupon(
                coupon, status=new_coupon_pb.Coupon.ACCEPTED, coupon_type=new_coupon_pb.Coupon.FANPIAO_PURCHASE_BONUS
            )
            coupon_manager.add_or_update_coupon(coupon)
            fanpiao.bonus_coupon_ids.append(coupon.id)

    def fanpiao_refund(self, transaction_id, transaction=None, reason=None, phone=None):
        """饭票退款
        1. 财付通退款通过回调来处理业务逻辑
        2. 其它则发起退款时直接处理业务逻辑
        """
        transaction_da = TransactionDataAccessHelper()
        fanpiao_da = FanpiaoDataAccessHelper()
        if transaction is None:
            transaction = transaction_da.get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.payer_id != self.user.id:
            raise errors.FanpiaoNotYours()

        today = datetime.today()
        today = today.replace(hour=0, minute=0, second=0)
        tomorrow = today + timedelta(1)
        user_today_refund_fanpiao_transactions = transaction_da.get_transactions(
            payer_id=self.user.id,
            start_create_time=(today.timestamp()),
            end_create_time=int(tomorrow.timestamp()),
            type=wallet_pb.Transaction.FANPIAO_REFUND,
            state=wallet_pb.Transaction.SUCCESS,
        )
        if len(user_today_refund_fanpiao_transactions) >= 2:
            logger.info(f"用户: {self.user.id} 申请退饭票超过2次")
            raise errors.ShowError("今日退卡包数过多")

        fanpiao = fanpiao_da.get_fanpiao(transaction_id=transaction.id)
        self.check_fanpiao_can_refund(fanpiao)
        payment_manager = PaymentManager(pay_method=transaction.pay_method, is_refund=True, merchant_id=transaction.payee_id)
        payment_manager.fanpiao_refund(transaction, reason=reason)

        if transaction.pay_method not in PaymentManager.get_refund_callback_pay_methods():
            self.refund_user_fanpiao_without_callback(fanpiao=fanpiao, reason=reason, phone=phone)
        else:
            fanpiao.status = fanpiao_pb.Fanpiao.REFUNDING
            fanpiao_da.add_or_update_fanpiao(fanpiao)
        FanpiaoHelper().set_user_fanpiao_purchase_risk_control(user=self.user, merchant_id=transaction.payee_id)

    def refund_user_fanpiao_without_callback(self, fanpiao, reason=None, phone=None):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao.status = fanpiao_pb.Fanpiao.REFUND
        if reason is not None:
            fanpiao.refund_reason = reason
        if phone is not None:
            fanpiao.refund_phone = phone
        fanpiao_da.add_or_update_fanpiao(fanpiao)
        self.__delete_fanpiao_bonus_coupons(fanpiao)

    def refund_user_fanpiao_with_callback(self, transaction):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao = fanpiao_da.get_fanpiao(transaction_id=transaction.id)
        fanpiao.status = fanpiao_pb.Fanpiao.REFUND
        fanpiao_da.add_or_update_fanpiao(fanpiao)
        self.__delete_fanpiao_bonus_coupons(fanpiao)

    def __delete_fanpiao_bonus_coupons(self, fanpiao):
        if len(fanpiao.bonus_coupon_ids) == 0:
            return
        coupon_manager = CouponManager(user=self.user, merchant=self.merchant)
        for coupon_id in fanpiao.bonus_coupon_ids:
            coupon = coupon_manager.get_coupon(id=coupon_id)
            if coupon.status != new_coupon_pb.Coupon.CouponStatus.ACCEPTED:
                continue
            coupon_manager.update_coupon(coupon, status=new_coupon_pb.Coupon.CouponStatus.DELETED)
            coupon_manager.add_or_update_coupon(coupon)

    def rollback_refund_user_fanpiao(self, transaction):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao = fanpiao_da.get_fanpiao(transaction_id=transaction.id)
        fanpiao.status = fanpiao_pb.Fanpiao.ACTIVE
        fanpiao_da.add_or_update_fanpiao(fanpiao)

    def check_fanpiao_can_refund(self, fanpiao):
        if fanpiao.total_used_fee > 0:
            raise errors.FanpiaoCannotRefund()
        if fanpiao.status not in [fanpiao_pb.Fanpiao.ACTIVE, fanpiao_pb.Fanpiao.REFUNDING]:
            raise errors.FanpiaoCannotRefund()
        if fanpiao.disable_refund:
            raise errors.FanpiaoCannotRefund()

    def get_fanpiao_usage(self, fanpiao_id):
        fanpiao_da = FanpiaoDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()
        fanpiao_usage_vo = page_fanpiao_pb.FanpiaoUsageVO()

        fanpiao = fanpiao_da.get_fanpiao(id=fanpiao_id)
        if not fanpiao:
            return None
        transaction = transaction_da.get_transaction_by_id(fanpiao.transaction_id)
        if not transaction:
            return None
        fanpiao_usage_vo.id = fanpiao.id
        fanpiao_usage_vo.transaction_id = transaction.id
        merchant_manager = MerchantManager(merchant_id=fanpiao.purchase_merchant_id)
        fanpiao_usage_vo.merchant_name = merchant_manager.get_merchant_name()
        fanpiao_usage_vo.merchant_id = merchant_manager.merchant.id
        fanpiao_usage_vo.name = fanpiao.name
        fanpiao_usage_vo.buy_time = fanpiao.buy_time
        fanpiao_usage_vo.balance = fanpiao.total_value - fanpiao.total_used_fee
        fanpiao_usage_vo.expire_time = fanpiao.expire_time
        for record in fanpiao.fanpiao_payment_records:
            transaction = transaction_da.get_transaction_by_id(record.transaction_id)
            if not transaction:
                continue
            if transaction.state != wallet_pb.Transaction.SUCCESS:
                continue
            if record.is_refund:
                continue
            detail_vo = fanpiao_usage_vo.fanpiao_usage_detail.add()
            detail_vo.transaction_id = transaction.id
            detail_vo.transaction_type = wallet_pb.Transaction.TransactionType.Name(transaction.type)
            detail_vo.paid_fee = record.paid_fee
            detail_vo.paid_time = transaction.paid_time
        return fanpiao_usage_vo

    def add_or_update_fanpiao_category(
        self,
        total_value,
        discount,
        name,
        sell_price,
        is_for_risk_control_user=False,
        fanpiao_risk_level=0,
        base_selling_quantity=None,
    ):
        if base_selling_quantity is None:
            base_selling_quantity = 0
        fanpiao_da = FanpiaoDataAccessHelper()
        category = fanpiao_pb.FanpiaoCategory()
        category.id = id_manager.generate_common_id()
        category.total_value = total_value
        category.create_time = int(time.time())
        category.discount = discount
        category.name = name
        category.base_selling_quantity = base_selling_quantity
        category.merchant_id = self.merchant.id
        category.sell_price = sell_price
        category.is_for_risk_control_user = is_for_risk_control_user
        category.for_which_risk_user_level = fanpiao_risk_level
        fanpiao_da.add_or_update_fanpiao_category(category)

    def get_platform_fanpiao_purchase_records(self):
        fanpiao_da = FanpiaoDataAccessHelper()
        user_da = UserDataAccessHelper()
        active = fanpiao_pb.FanpiaoCategory.ACTIVE
        purchase_count = fanpiao_da.count_fanpiaos(status=active)
        fanpiaos = fanpiao_da.get_fanpiaos(status=active, orderby=[("buyTime", -1)], limit=20)
        ret = []
        head_image_url_count = 0
        for fanpiao in fanpiaos:
            user_id = fanpiao.user_id
            user = user_da.get_user(user_id)
            if not user:
                continue
            if user.member_profile.head_image_url == "":
                continue
            ret.append(user.member_profile.head_image_url)
            head_image_url_count += 1
            if head_image_url_count == 100:
                break
        return {"headImageUrls": ret, "purchaseCount": purchase_count}

    def get_recently_buy_fanpiao_list(self):
        manager = TransactionManager()
        type = wallet_pb.Transaction.FANPIAO_PURCHASE
        now = int(time.time())
        before_timestamp = now - 24 * 60 * 60 * 30
        transactions = manager.get_recently_transactions(
            merchant=self.merchant, before_timestamp=before_timestamp, type=type, size=20
        )
        if not transactions:
            return []
        user_da = UserDataAccessHelper()
        fanpiao_da = FanpiaoDataAccessHelper()
        user_ids, transaction_ids = set(), list()
        for t in transactions:
            transaction_ids.append(t.id)
            user_ids.add(t.payer_id)
        status = fanpiao_pb.Fanpiao.ACTIVE
        fanpiaos = fanpiao_da.get_fanpiaos(transaction_ids=transaction_ids, status=status, return_proto=False)
        if not fanpiaos:
            return []
        fanpiaos = {f.get("transactionId"): f for f in fanpiaos}
        users = user_da.query_user_by_ids(list(user_ids))
        if not users:
            return []
        users = {u['id']: u for u in users if u}
        ret = []
        for transaction in transactions:
            fanpiao = fanpiaos.get(transaction.id)
            if not fanpiao:
                continue
            user_id = transaction.payer_id
            user = users.get(user_id)
            if not user:
                continue
            buy_fanpiao_vo = page_fanpiao_pb.RecentlyBuyFanpiaoVO()
            buy_fanpiao_vo.user_id = user.get("id")
            buy_fanpiao_vo.username = user.get("memberProfile", {}).get("nickname", "")
            buy_fanpiao_vo.fanpiao_category_name = fanpiao.get("name", "")
            buy_fanpiao_vo.head_image_url = user.get("memberProfile", {}).get("headImageUrl", "")
            ret.append(buy_fanpiao_vo)
        return ret

    def get_user_fanpiao_list(self, user_id, merchant_id=None, not_only_active=None):
        if not_only_active is None:
            not_only_active = False
        fanpiao_list_vo = page_fanpiao_pb.FanpiaoListVo()
        if user_id is None:
            return fanpiao_list_vo
        fanpiao_da = FanpiaoDataAccessHelper()
        status = [fanpiao_pb.Fanpiao.ACTIVE]
        if not_only_active:
            status.extend([fanpiao_pb.Fanpiao.EXPIRED, fanpiao_pb.Fanpiao.REFUND])
        fanpiaos = fanpiao_da.get_fanpiaos(
            user_id=user_id,
            merchant_id=merchant_id,
            brand_id=self.merchant.brand_info.id
            if merchant_id and self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else None,
            status=status,
        )
        if not fanpiaos or len(fanpiaos) <= 0:
            return fanpiao_list_vo
        # fanpiaos_refunded = fanpiao_da.get_fanpiaos(
        #     user_id=user_id, merchant_id=merchant_id, status=fanpiao_pb.Fanpiao.BALANCE_REFUNDED)
        # fanpiaos.extend(fanpiaos_refunded)
        fanpiaos.sort(key=lambda x: x.buy_time, reverse=True)

        fanpiaos_maps = {}
        for fanpiao in fanpiaos:
            if not fanpiaos_maps.get(fanpiao.purchase_merchant_id, {}).get("fanpiao_config"):
                merchant_da = MerchantDataAccessHelper()
                merchant = merchant_da.get_merchant(merchant_id=fanpiao.purchase_merchant_id)
                config_manager = ConfigManager(merchant=merchant)
                fanpiao_config = config_manager.get_fanpiao_config()
                fanpiaos_maps[fanpiao.purchase_merchant_id] = {"fanpiao_config": fanpiao_config, "merchant": merchant}

            fanpiao_config = fanpiaos_maps[fanpiao.purchase_merchant_id]["fanpiao_config"]
            merchant = fanpiaos_maps[fanpiao.purchase_merchant_id]["merchant"]

            fanpiao_vo = fanpiao_list_vo.fanpiaos.add()
            fanpiao_vo.id = fanpiao.id
            fanpiao_vo.name = fanpiao.name
            fanpiao_vo.total_value = fanpiao.total_value
            fanpiao_vo.sell_price = fanpiao.sell_price
            fanpiao_vo.buy_time = fanpiao.buy_time
            fanpiao_vo.present_value = fanpiao.total_value - fanpiao.total_used_fee
            fanpiao_vo.merchant_id = merchant.id
            fanpiao_vo.merchant_name = merchant.basic_info.display_name
            fanpiao_vo.merchant_logo_url = merchant.basic_info.logo_url
            fanpiao_vo.discount = fanpiao.discount
            fanpiao_vo.balance_refund_apply = fanpiao.balance_refund_apply
            fanpiao_vo.status = fanpiao.status
            fanpiao_vo.can_refund = self.can_fanpiao_refund(fanpiao)
            fanpiao_vo.transaction_id = fanpiao.transaction_id
            fanpiao_vo.expire_time = fanpiao.expire_time
            if fanpiao_config:
                fanpiao_vo.enable_fanpiao_balance_refund = fanpiao_config.enable_fanpiao_balance_refund
        return fanpiao_list_vo

    def check_user_ever_bought_fanpiao(self, vo, user_id, merchant_id):
        if merchant_id is None:
            return
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao = fanpiao_da.get_fanpiao(
            user_id=user_id,
            merchant_id=merchant_id,
            brand_id=self.merchant.brand_info.id
            if merchant_id and self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else None,
            status=fanpiao_pb.Fanpiao.ACTIVE,
        )
        vo.ever_bought_fanpiao = fanpiao is not None

    def can_fanpiao_refund(self, fanpiao):
        if fanpiao.disable_refund:
            return False
        if fanpiao.status == fanpiao_pb.Fanpiao.REFUND:
            return False
        if fanpiao.status == fanpiao_pb.Fanpiao.INACTIVE:
            return False
        if fanpiao.total_used_fee == 0:
            return True
        return False

    def get_user_fanpiaos(self, user_id, merchant_id=None):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos(
            user_id=user_id,
            merchant_id=merchant_id,
            brand_id=self.merchant.brand_info.id
            if merchant_id and self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else None,
            status=fanpiao_pb.Fanpiao.ACTIVE,
            orderby=[("buyTime", -1)],
        )
        return fanpiaos

    def get_user_fanpiao_qrcode(self, user_id=None, qrcode_id=None):
        """获取用户饭票支付二维码信息"""
        qrcode_obj = None
        if qrcode_id is not None:
            try:
                qrcode_obj = self.__get_fanpiao_scan_qrcode_obj_by_id(qrcode_id)
                return qrcode_obj
            except Exception as e:
                logger.exception("获取用户饭票支付二维码图片: {}".format(e))
        if user_id is not None:
            user_da = UserDataAccessHelper()
            user = user_da.get_user(user_id)
            qrcode_obj = self.__create_fanpiao_scan_qrcode_obj(user)
            image_url = self.__generate_user_fanpiao_scan_qrcode(qrcode_obj)
            qrcode_obj.image_url = image_url
            self.update_fanpiao_qrcode_obj(qrcode_obj)
            self.__update_fanpiao_qrcode_cache(qrcode_obj)
            return qrcode_obj
        if not qrcode_obj:
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_NOT_EXISTS)
        return qrcode_obj

    def __get_fanpiao_scan_qrcode_obj_by_id(self, id):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_scan_qrcode = fanpiao_da.get_fanpiao_scan_qrcode(id=id)
        if not fanpiao_scan_qrcode:
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_NOT_EXISTS)
        now = int(time.time())
        if fanpiao_scan_qrcode.expire_time < now:
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_EXPIRED)
        return fanpiao_scan_qrcode

    def __generate_user_fanpiao_scan_qrcode(self, qrcode_obj):
        """生成用户饭票支付二维码,并上传到oss"""
        oss_helper = AliyunOSSHelper()
        image_name = "{}.png".format(qrcode_obj.id)
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qrcode_obj.id)
        qr.make(fit=True)
        image = qr.make_image(fill='black', back_color='white')
        image_bytes = BytesIO()
        image.save(image_bytes, format="PNG")
        image_bytes.seek(0)
        image_obj = oss_helper.upload_image_binary(image_bytes, image_name, directory="fanpiao-qrcode")
        return image_obj.url

    def update_fanpiao_scan_qrcode_obj(self, qrcode_obj, **kargs):
        merchant_id = kargs.get("merchant_id")
        if merchant_id is not None:
            qrcode_obj.merchant_id = merchant_id
        bill_fee = kargs.get("bill_fee")
        if bill_fee is not None:
            qrcode_obj.bill_fee = bill_fee
        order_id = kargs.get("order_id")
        if order_id is not None:
            qrcode_obj.order_id = order_id
        paid_fee = kargs.get("paid_fee")
        if paid_fee is not None:
            qrcode_obj.paid_fee = paid_fee
        callback_url = kargs.get("callback_url")
        if callback_url is not None:
            qrcode_obj.callback_url = callback_url
        is_paid = kargs.get("is_paid")
        if is_paid:
            qrcode_obj.is_paid = is_paid
        self.__update_fanpiao_qrcode_cache(qrcode_obj)

    def publish_qrcode_info_complete(self, qrcode_obj):
        """通知小程序qrcode信息已更新"""
        conn = RedisClient().get_connection()
        message = fanpiao_websocket_pb.FanpiaoMessage()
        message.id = id_manager.generate_common_id()
        message.command = fanpiao_websocket_pb.FANPIAO_INFO_COMPLETE
        message.qrcode_id = qrcode_obj.id
        message_obj = json_format.MessageToJson(message, including_default_value_fields=True)
        conn.publish(qrcode_obj.user_id, message_obj)

    def __update_fanpiao_qrcode_cache(self, qrcode_obj):
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_user_fanpiao_scan_qrcode_key(qrcode_obj.user_id)
        qrcode_json = json_format.MessageToJson(qrcode_obj, including_default_value_fields=True)
        now = int(time.time())
        ttl = qrcode_obj.expire_time - now
        redis_client.set(key, qrcode_json, ex=ttl)

    def __create_fanpiao_scan_qrcode_obj(self, user, qrcode_obj=None):
        qrcode_obj = fanpiao_pb.FanpiaoScanQrcode()
        now = int(time.time())
        qrcode_obj.id = id_manager.generate_common_id()
        qrcode_obj.user_id = user.id
        qrcode_obj.phone = user.member_profile.mobile_phone
        qrcode_obj.create_time = now
        ttl = 10 * date_utils.ONE_MINUTE
        qrcode_obj.expire_time = ttl + now
        qrcode_obj.is_paid = "0"
        return qrcode_obj

    def update_fanpiao_qrcode_obj(self, qrcode_obj):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_da.add_or_update_fanpiao_scan_qrcode(qrcode_obj)

    def delete_fanpiao_qrcode_from_cache(self, qrcode_obj):
        redis_client = RedisClient().get_connection()
        user_id = qrcode_obj.user_id
        key = CacheServerKeys.get_user_fanpiao_scan_qrcode_key(user_id)
        redis_client.delete(key)

    def get_fanpiao_qrcode_obj(self, payment_transaction_id=None, fanpiao_qrcode_id=None, raise_error=True):
        fanpiao_da = FanpiaoDataAccessHelper()
        qrcode_obj = fanpiao_da.get_fanpiao_scan_qrcode(payment_transaction_id=payment_transaction_id, id=fanpiao_qrcode_id)
        if not qrcode_obj and raise_error:
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_NOT_EXISTS)
        if not qrcode_obj:
            return None
        return qrcode_obj

    def get_user_fanpiao_fee(self, user_id):
        """用户在该商户下的饭票余额"""
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos(
            user_id=user_id,
            status=fanpiao_pb.Fanpiao.ACTIVE,
            merchant_id=self.merchant.brand_info.id
            if self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else self.merchant.id,
        )
        remaining_value = 0
        for fanpiao in fanpiaos:
            remaining_value += fanpiao.total_value - fanpiao.total_used_fee
        return remaining_value

    def staff_perform_balance_refund_v2(self, fanpiao, is_refund_the_way, balance_refund_method, user_id):
        with distribute_lock.redislock(key=f"fanpiao-refund-{fanpiao.id}", ttl=3000, retry_count=0, retry_delay=0) as lock:
            if not lock:
                raise errors.Error(150014, f"饭票正在退款中，请稍后再试 - {fanpiao.id}")
            self.__do_balance_refund(
                fanpiao,
                config_pb.FanpiaoConfig(
                    merchant_id=fanpiao.purchase_merchant_id,
                    enable_fanpiao_balance_refund=True,
                    is_refund_the_way=is_refund_the_way,
                    balance_refund_method=config_pb.FanpiaoConfig.BalanceRefundMethod.Value(balance_refund_method),
                ),
                user_id,
                need_log=True,
            )

    def staff_perform_balance_refund(self, fanpiao, method, user_id=None):
        config_da = ConfigDataAccessHelper()
        # config = config_da.get_fanpiao_config(merchant_id=fanpiao.purchase_merchant_id)
        config = config_da.get_fanpiao_config(merchant_id=self.merchant.id)
        if not config:
            return
        if method is not None:
            if isinstance(method, str):
                method = config_pb.FanpiaoConfig.BalanceRefundMethod.Value(method)
            config.balance_refund_method = method
        with distribute_lock.redislock(key=f"fanpiao-refund-{fanpiao.id}", ttl=3000, retry_count=0, retry_delay=0) as lock:
            if not lock:
                logger.info("饭票正在退款中 - {}".format(fanpiao.id))
                raise errors.Error(150014, "饭票正在退款中")
            self.__do_balance_refund(fanpiao, config, user_id)

    def balance_refund_cronjob(self, fanpiao):
        config_da = ConfigDataAccessHelper()
        config = config_da.get_fanpiao_config(merchant_id=self.merchant.id)
        if not config:
            return
        if not config.enable_fanpiao_balance_refund:
            return
        self.__do_balance_refund(fanpiao, config)

    def balance_refund_fee_compute(self, fanpiao):
        """只计算一共要退多少钱,不做实际的打款操作"""
        config_da = ConfigDataAccessHelper()
        config = config_da.get_fanpiao_config(merchant_id=self.merchant.id)
        if not config:
            return
        if not config.enable_fanpiao_balance_refund:
            return
        fanpiao_manager = FanpiaoManager(merchant_id=fanpiao.purchase_merchant_id)
        fee = fanpiao_manager.compute_refund_fee(fanpiao, config.balance_refund_method)
        return fee

    def __do_balance_refund(self, fanpiao, config, user_id=None, need_log=False):
        fanpiao_manager = FanpiaoManager(merchant_id=fanpiao.purchase_merchant_id)
        fee = fanpiao_manager.compute_refund_fee(fanpiao, config.balance_refund_method)
        orig_fee = fanpiao.total_value - fanpiao.total_used_fee
        if fee <= 30 and config.balance_refund_method != config_pb.FanpiaoConfig.BalanceRefundMethod.BY_BALANCE:
            # 小于30分的钱,企业付款到零钱会失败
            fanpiao.balance_refund_apply = False
            self.fanpiao_da.add_or_update_fanpiao(fanpiao)
            return
        user = self.user_da.get_user(user_id=fanpiao.user_id)
        payment_manager = self.__get_payment_manager(user, config)
        ret = payment_manager.fanpiao_balance_withdraw(user, orig_fee, fee)
        msg = f"""
        饭票退款到: {wallet_pb.Transaction.PayMethod.Name(payment_manager.pay_method)}
        金额: {fee}分
        结果: {ret.flag}
        退款流水ID: {ret.transaction.id}
        """
        logger.info(msg)
        if ret.flag:
            self.__fanpiao_balance_withdraw_success(ret, fanpiao)
            need_log and OperationDataAccessHelper().add_operation(
                user_id,
                'FANPIAO_REFUND',
                {
                    "fanpiaoId": fanpiao.id,
                    "merchantId": fanpiao.purchase_merchant_id,
                    "payMethod": wallet_pb.Transaction.PayMethod.Name(payment_manager.pay_method),
                    "billFee": orig_fee,
                    "paidFee": fee,
                    "status": ret.flag,
                    "transactionId": ret.transaction.id,
                    "balanceRefundMethod": config_pb.FanpiaoConfig.BalanceRefundMethod.Name(config.balance_refund_method),
                },
            )

    def __get_payment_manager(self, user, config):
        if config.is_refund_the_way:
            if user.wechat_profile.openid:
                payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.WECHAT_PAY)
            else:
                payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.ALIPAY)
        else:
            payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.WALLET)
        return payment_manager

    def __fanpiao_balance_withdraw_success(self, ret, fanpiao):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao.status = fanpiao_pb.Fanpiao.BALANCE_REFUNDED
        fanpiao_payment = fanpiao.fanpiao_payment_records.add()
        fanpiao_payment.create_time = int(time.time())
        fanpiao_payment.transaction_id = ret.transaction.id
        fanpiao_payment.create_time = int(time.time())
        fanpiao.total_used_fee += ret.transaction.bill_fee
        self.__delete_fanpiao_bonus_coupons(fanpiao)
        fanpiao_da.add_or_update_fanpiao(fanpiao)

    def get_fanpiao_by_partial_id(self, partial_id):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiao_by_partial_id(
            self.merchant.brand_info.id
            if self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else self.merchant.id,
            partial_id,
        )
        results = []
        merchant_da = MerchantDataAccessHelper()
        for fanpiao in fanpiaos:
            merchant = merchant_da.get_merchant_by_id(fanpiao.purchase_merchant_id)
            fanpiao = json_format.MessageToDict(fanpiao, including_default_value_fields=True)
            fanpiao.update({"merchantName": merchant.basic_info.display_name})
            results.append(fanpiao)
        return results

    def get_fanpiao_by_short_id(self, short_id):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos_by_short_id(short_id)
        results = []
        merchant_da = MerchantDataAccessHelper()
        for fanpiao in fanpiaos:
            merchant = merchant_da.get_merchant_by_id(fanpiao.purchase_merchant_id)
            fanpiao = json_format.MessageToDict(fanpiao, including_default_value_fields=True)
            fanpiao.update({"merchantName": merchant.basic_info.display_name})
            results.append(fanpiao)
        return results

    def get_fanpiao_by_id(self, fanpiao_id):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao = fanpiao_da.get_fanpiao(id=fanpiao_id)
        return fanpiao

    def add_or_update_fanpiao(self, fanpiao):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_da.add_or_update_fanpiao(fanpiao)

    def add_fanpiao_record_log(self, logs):
        log_obj = fanpiao_pb.FanpiaoMonitoringLogs()
        log_obj.id = id_manager.generate_common_id()
        log_obj.create_time = int(time.time())
        source = logs.get("source")
        if source is None or source not in ["COMMUNITY", "PRIVATE_CHAT", "MATERIALS"]:
            raise errors.ShowError("渠道只能为COMMUNITY, PRIVATE_CHAT, MATERIALS")
        log_obj.source = source
        types = logs.get("type")
        if types is None or types not in ["SCAN_CODE", "BUY"]:
            raise errors.ShowError("扫码类型只能为SCAN_CODE, BUY")
        log_obj.type = types
        log_obj.merchant_id = logs.get("merchantId")
        log_obj.user_id = logs.get("userId", "")
        log_obj.nick_name = logs.get("nickName", "")
        log_obj.merchant_name = logs.get("merchantName", "")
        log_obj.meal_ticket_id = logs.get("mealTicketId", "")
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_da.add_or_update_monitor_logs(log_obj)

    def update_fanpiao(self, fanpiao, **kargs):
        if fanpiao is None:
            return
        status = kargs.get("status")
        self.__update_fanpiao_status(fanpiao, status)
        expire_time = kargs.get('expire_time', 0)
        if int(expire_time) > 0 and fanpiao.status == fanpiao_pb.Fanpiao.EXPIRED:
            fanpiao.status = fanpiao_pb.Fanpiao.Status.ACTIVE
            fanpiao.expire_time = int(expire_time)
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_da.add_or_update_fanpiao(fanpiao)

    def inactive_fanpiao(self, fanpiao):
        return self.__update_fanpiao_status(fanpiao, fanpiao_pb.Fanpiao.INACTIVE)

    def balance_refund_fanpiao(self, fanpiao):
        return self.__update_fanpiao_status(fanpiao, fanpiao_pb.Fanpiao.BALANCE_REFUNDED)

    def __update_fanpiao_status(self, fanpiao, status):
        if None in (fanpiao, status):
            return
        if isinstance(status, str):
            status = fanpiao_pb.Fanpiao.Status.Value(status)
        fanpiao.status = status

    def compute_refund_fee(self, fanpiao, compute_method):
        """计算饭票应退金额"""
        if not fanpiao:
            raise errors.ShowError("卡包不存在")
        if fanpiao.status != fanpiao_pb.Fanpiao.ACTIVE and fanpiao.status != fanpiao_pb.Fanpiao.EXPIRED:
            raise errors.ShowError("卡包状态错误")
        if isinstance(compute_method, str):
            compute_method = config_pb.FanpiaoConfig.BalanceRefundMethod.Value(compute_method)
        if compute_method == config_pb.FanpiaoConfig.BY_BALANCE:
            fee = self.__compute_by_balance(fanpiao)
        elif compute_method == config_pb.FanpiaoConfig.BY_ORIG_PRICE:
            fee = self.__compute_by_orig_price(fanpiao)
        if fee <= 0:
            fanpiao.balance_refund_apply = False
            self.add_or_update_fanpiao(fanpiao)
            raise errors.ShowError("可退金额小于0,请选择其它折算方式")
        logger.info(f"{self.merchant.basic_info.display_name} {fanpiao.id} 退款金额: {fee}")
        return fee

    def __compute_by_balance(self, fanpiao):
        """按余额退时应退的金额"""
        balance = fanpiao.total_value - fanpiao.total_used_fee
        return balance

    def __compute_by_orig_price(self, fanpiao):
        """按原价退时应退的金额"""
        discount = 100 - fanpiao.discount
        # 原价应该支付的金额
        used_fee = fanpiao.total_used_fee / discount * 100
        balance = int(fanpiao.total_value - used_fee + 0.5)
        calculate_formula = f"""
        int(总额({fanpiao.total_value}) - 已使用总金额({fanpiao.total_used_fee}) / 折扣({discount}) * 100 + 0.5)
        int({fanpiao.total_value} - {fanpiao.total_used_fee} / {discount} * 100 + 0.5) == {balance}
        """
        msg = f"""
        {fanpiao.id} {100 - fanpiao.discount}折
        余额: {fanpiao.total_value - fanpiao.total_used_fee}
        最终退回的余额: {balance}
        {calculate_formula}
        """
        logger.info(msg)
        return balance

    def check_refund_cronjob(self, fanpiao: fanpiao_pb.Fanpiao):
        balance = fanpiao.total_value - fanpiao.total_used_fee
        if balance <= 0:
            raise errors.Error(errcode=error_codes.FANPIAO_NOT_ENOUGH_MONEY, errmsg=error_codes.FANPIAO_NOT_ENOUGH_MONEY_MSG)
        if fanpiao.status not in [fanpiao_pb.Fanpiao.ACTIVE, fanpiao_pb.Fanpiao.EXPIRED]:
            raise errors.Error(err=error_codes.FANPIAO_REFUND_STATUS_ERROR)
        if fanpiao.disable_refund:
            raise errors.Error(err=error_codes.FANPIAO_LIMIT_REFUND_ERROR)

    def refund_cronjob(self, fanpiao: fanpiao_pb.Fanpiao, is_refund_the_way: bool, balance_refund_method: str):
        self.check_refund_cronjob(fanpiao)
        config_da = ConfigDataAccessHelper()
        config = config_da.get_fanpiao_config(merchant_id=fanpiao.purchase_merchant_id)
        if not config:
            return
        if isinstance(balance_refund_method, str):
            balance_refund_method = config_pb.FanpiaoConfig.BalanceRefundMethod.Value(balance_refund_method)
        config.balance_refund_method = balance_refund_method
        config.is_refund_the_way = is_refund_the_way
        self.__do_balance_refund(fanpiao, config)
        refund_transaction = fanpiao.fanpiao_payment_records[-1]
        return self.update_refund_cronjob(
            matcher={"fanpiao_id": fanpiao.id},
            status=fanpiao.status,
            balance=fanpiao.total_value - fanpiao.total_used_fee,
            refund_transaction_id=refund_transaction.transaction_id,
            refund_balance=refund_transaction.paid_fee,
            refund_pay_method=wallet_pb.Transaction.PayMethod.Name(refund_transaction.pay_method),
            refund_time=refund_transaction.create_time,
        )

    def _get_refund_record_dao(self):
        return DaoORMHelper(
            db=constants.MONGODB_FANPIAO_DATABASE_NAME,
            collection=constants.MONGODB_FANPIAO_REFUND_RECORDS,
            pb=fanpiao_pb.FanpiaoRefundRecord,
        )

    @staticmethod
    def _filter_none(item: dict):
        return {k: v for k, v in item.items() if v is not None}

    def add_refund_cronjob(self, **kwargs):
        dao = self._get_refund_record_dao()
        now = int(time.time())
        return dao.add_or_update({"id": str(uuid.uuid4()).replace("-", ""), **kwargs, "create_time": now, "update_time": now})

    def update_refund_cronjob(self, id: str = None, matcher: dict = None, **kwargs):
        dao = self._get_refund_record_dao()
        kwargs = self._filter_none(kwargs)
        if not kwargs:
            return
        matcher = matcher or {}
        if id is not None:
            matcher['id'] = id
        if not dao.get(matcher=matcher):
            raise errors.Error(errcode=error_codes.FANPIAO_NOT_EXISTS, errmsg=error_codes.FANPIAO_NOT_EXISTS_MSG)
        return dao.add_or_update(
            {**kwargs, "update_time": int(time.time())},
            matcher=matcher,
        )

    def query_refund_cronjob(self, **matcher):
        dao = self._get_refund_record_dao()
        matcher = self._filter_none(matcher)
        if not matcher:
            return []
        merchant_name = matcher.get("merchant_name")
        if merchant_name is not None:
            matcher["merchant_name"] = {"$regex": merchant_name}
        return dao.query(matcher=matcher, sort=[("createTime", -1)])

    def exists(self, fanpiao_id: str) -> bool:
        dao = self._get_refund_record_dao()
        return bool(dao.get(matcher={"fanpiao_id": fanpiao_id}))

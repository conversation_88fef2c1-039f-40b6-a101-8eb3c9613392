# -*- coding: utf-8 -*-

import hashlib
import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
from business_ops.merchant_manager import MerchantManager
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class BoshijieManager:
    app_key = "69dd008e3336a39982dd73bb4e59a2f1"
    PAY_TYPE_WECHAT = 1  # 微信: 播报内容: 微信收款成功x元
    PAY_TYPE_ALIPAY = 2  # 支付宝: 支付宝收款成功x元
    PAY_TYPE_FANPIAO = 3  # 饭票: 智能营销收款成功x元
    PAY_TYPE_DEFAULT = 4  # 时来: 时来收款成功

    def __init__(self):
        self.merchant_manager = MerchantManager()
        self.base_result = {'errcode': 0, 'errmsg': "成功"}

    def bind(self, sn, merchant_id):
        merchant = self.merchant_manager.get_merchant_by_handheld_pos_sn(sn)
        if merchant:
            raise errors.ShowError(f"{sn}已绑定{merchant.basic_info.name}")
        merchant = self.merchant_manager.get_merchant_by_id(merchant_id)
        if not merchant:
            raise errors.ShowError("商家不存在")
        for h in merchant.handheld_pos:
            if h.sn == sn:
                return merchant
        handheld_pos = merchant.handheld_pos.add()
        handheld_pos.sn = sn
        handheld_pos.type = merchant_rules_pb.HandheldPos.BOSHIJIE
        self.merchant_manager.add_or_update_merchant(merchant)
        return merchant

    def unbind(self, sn, merchant_id):
        merchant = self.merchant_manager.get_merchant_by_id(merchant_id)
        if not merchant:
            raise errors.ShowError("商家不存在")
        to_be_deleted = None
        for h in merchant.handheld_pos:
            if h.sn == sn:
                to_be_deleted = h
        if to_be_deleted is None:
            return
        merchant.handheld_pos.remove(to_be_deleted)
        self.merchant_manager.add_or_update_merchant(merchant)
        return merchant

    def query(self, sn):
        merchant = self.merchant_manager.get_merchant_by_handheld_pos_sn(sn)
        if not merchant:
            raise errors.ShowError("该sn码未绑定")
        return self.base_result

    @classmethod
    def parse_prepay_result(cls, result):
        if result.get("errcode") == error_codes.USER_PAYING:
            return {"errcode": 3001, "errmsg": "用户支付中", "data": {"status": "PAYING"}}
        else:
            return {"errcode": 3002, "errmsg": "支付失败"}

    @classmethod
    def parse_query_order_result(cls, order, transaction=None):
        if order is None:
            return {"errcode": 3003, "errmsg": "订单不存在"}
        pay_type = cls.PAY_TYPE_DEFAULT
        pay_method_name = "时来收款"
        paid_fee = 0
        if transaction is None and order.transaction_id != "":
            transaction_da = TransactionDataAccessHelper()
            transaction = transaction_da.get_transaction_by_id(order.transaction_id)
        if transaction is not None:
            if transaction.pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
                if transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                    pay_type = cls.PAY_TYPE_WECHAT
                    pay_method_name = "微信"
                elif transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                    pay_type = cls.PAY_TYPE_ALIPAY
                    pay_method_name = "支付宝"
            elif transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                pay_type = cls.PAY_TYPE_FANPIAO
                pay_method_name = "智能营销"
            paid_fee = "{:.2f}".format(transaction.paid_fee / float(100))
        if order.status == dish_pb.DishOrder.PAID:
            return {
                "errcode": 0,
                "errmsg": "成功",
                "data": {
                    'broadcast_msg': f'{pay_method_name}收款成功{paid_fee}元',
                    'pay_type': pay_type,
                    "status": dish_pb.DishOrder.OrderStatus.Name(order.status),
                },
            }
        if order.status == dish_pb.DishOrder.PAYING:
            return {
                "errcode": 3001,
                "errmsg": "用户支付中",
                "data": {
                    "status": "PAYING",
                },
            }
        if order.status == dish_pb.DishOrder.CANCELLED:
            return {
                "errcode": 3005,
                "errmsg": "已取消支付",
                "data": {
                    "status": "CANCELLED",
                },
            }
        return {"errcode": 3003, "errmsg": "订单不存在"}

    @classmethod
    def parse_merchant_result(cls, merchant):
        if merchant is None:
            result = {"errcode": 3006, "errmsg": "设备未绑定"}
        else:
            result = {"errcode": 0, "errmsg": "成功", "data": {"store_name": merchant.basic_info.display_name}}
        return result

    @classmethod
    def verify(cls, **kargs):
        sign = kargs.get("sign")
        del kargs['sign']
        keys = list(kargs.keys())
        keys.sort(key=lambda x: x)
        sign_values = []
        for key in keys:
            value = kargs.get(key)
            sign_values.append(f"{key}={value}")
        sign_values.append(cls.app_key)
        sign_value = "&".join(sign_values)
        md5 = hashlib.md5()
        md5.update(sign_value.encode('utf8'))
        result = md5.hexdigest().upper()
        logger.info(f"待签名字符串: {sign_value} -> 前端sign: {sign} 服务端sign: {result}")
        if result == sign:
            return True
        raise errors.ShowError("签名错误")

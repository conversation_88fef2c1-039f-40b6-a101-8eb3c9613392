# -*- coding: utf-8 -*-

###########################################################
# 所有manager类的父类
###########################################################

import logging
import math
import random

import proto.ordering.registration_pb2 as registration_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.red_packet_manager import RedPacketManager
from business_ops.coupon_manager import CouponManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.base_manager import BaseManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.promotion.coupon.coupon_manager import Coupon<PERSON>anager as NewCouponManager
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from business_ops.transaction_manager import TransactionManager
from business_ops.wallet_manager import WalletManager
from common.cache_server_keys import CacheServerKeys
from common.utils.distribute_lock import AtomicDistributedLock
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.dish_verification_code_da_helper import DishVerificationCodeDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class BusinessManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(BusinessManager, self).__init__(*args, **kargs)
        self.pay_method = kargs.get("pay_method")
        self.transaction_id = kargs.get("transaction_id")
        self.order_id = kargs.get("order_id")
        self.merchant_id = kargs.get("merchant_id")
        self.user_id = kargs.get("user_id")
        self.coupon_id = kargs.get("coupon_id")
        self.coupon_ids = kargs.get("coupon_ids")
        self.is_invoice = kargs.get("is_invoice")
        self.coupon_package_id = kargs.get("coupon_package_id")
        self.is_phone_member_pay = kargs.get("is_phone_member_pay")
        self.is_group_purchase = kargs.get('is_group_purchase')
        self.coin_deduction = kargs.get("coin_deduction")
        self.transaction_type = kargs.get("transaction_type")
        self.inc_leader_wallet_balance_transaction = None
        self.coupons_from_cache = []
        self.coupons = []
        self.group_purchase = None
        self.red_packet = None
        self.check_coupon = kargs.get("check_coupon")

        # 是否需要平台补贴的券的标志位
        self.is_platform_subsidies_coupon_category = False
        if self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            self.coupon_id = ""

        self.__clear_coupon_ids(self.transaction_id)
        self.preprocess_transaction()
        self.init_coupon_info_v2()
        self.preprocess_order()
        # 时运币券初始化
        self.__init_coin_deduction_coupon()
        self.init_coupon_info()
        self.init_coupon_package_info()

    def round_off(self, value):
        if value is None:
            return 0
        decimal = value - int(value)
        if decimal < 0.5:
            return int(value)
        else:
            return math.ceil(value)

    def __clear_coupon_ids(self, transaction_id):
        if not self.order:
            return
        if transaction_id is not None:  # 如果transaction_id不存在才是prepay
            return
        # 因为是在唤起支付的时候将coupon_ids设置到order中
        # 如果用户唤起又取消，再唤起时就需要将原coupon_ids清空
        while self.order.coupon_ids:
            self.order.coupon_ids.pop()

    def init_coupon_package_info(self):
        if self.coupon_package_id is None:
            return
        for package in self.merchant.preferences.coupon_config.coupon_packages:
            if package.id == self.coupon_package_id:
                self.coupon_package = package
                break
        if not self.coupon_package:
            raise errors.CouponPackageNotFound()

    def init_coupon_info_v2(self):
        if self.coupon_ids is None or len(self.coupon_ids) == 0:
            return
        self.order.coupon_ids.extend(self.coupon_ids)
        # 团购券的初始化
        self.__init_group_purchase_coupon_info()
        # NewCoupon: 普通券初始化
        self.__init_common_coupons()

    def check_coupon_fee_matched(self):
        """在用券的情况下,检查支付金额是否能匹配上."""
        if not (self.coupons and len(self.coupons) > 0):
            return False
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        reduce_fee = 0
        for coupon in self.coupons:
            fee = coupon_manager.get_coupon_reduce_fee(self.bill_fee, coupon)
            reduce_fee += fee
            logger.info(f"{self.user.id} {self.merchant.id} {coupon.id} 优惠金额为: {fee}")
        self.order.paid_fee = self.order.paid_fee - reduce_fee
        if self.order.paid_fee < 0:
            self.order.paid_fee = 0
        logger.info(f"用券金额: 服务端({self.order.bill_fee} {self.order.paid_fee}) 客户端: ({self.bill_fee} {self.paid_fee})")
        if self.order.bill_fee != self.bill_fee or self.order.paid_fee != self.paid_fee:
            raise errors.ShowError("支付金额不匹配")
        return True

    def check_coupon_fee_matched_direct(self):
        if not (self.coupons and len(self.coupons) > 0):
            return False
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        reduce_fee = 0
        self.coupon_ids = []
        for coupon in self.coupons:
            self.coupon_ids.append(coupon.id)
            fee = coupon_manager.get_coupon_reduce_fee(self.bill_fee, coupon)
            reduce_fee += fee
            logger.info(f"{self.user.id} {self.merchant.id} {coupon.id} 优惠金额为: {fee}")
        need_paid_fee = self.bill_fee - reduce_fee
        if need_paid_fee < 0:
            need_paid_fee = 0
        logger.info(f"用券金额: 服务端({self.bill_fee} {self.paid_fee}) 客户端: ({self.bill_fee} {need_paid_fee})")
        if need_paid_fee != self.paid_fee:
            raise errors.ShowError("支付金额不匹配")
        return True

    def join_group_purchase(self):
        if not self.is_group_purchase:
            return
        self.__join_group_purchase_with_invitation()
        self.__join_group_purchase_without_invitation()

    def __join_group_purchase_with_invitation(self):
        if not (self.coupons_from_cache and len(self.coupons_from_cache) > 0):
            return
        group_purchase_manager = GroupPurchaseManager(user=self.user, merchant=self.merchant)
        coupon_template_manager = CouponTemplateManager()
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        for coupon_from_cache in self.coupons_from_cache:
            coupon_id = coupon_from_cache.get('coupon_id')
            group_purchase_id = coupon_from_cache.get('group_purchase_id')
            coupon_template_id = coupon_from_cache.get('coupon_template_id')
            coupon_type = coupon_from_cache.get('coupon_type')
            coupon_template = coupon_template_manager.get_coupon_template(coupon_template_id)
            coupon = coupon_manager.create_coupon(coupon_template)
            coupon_manager.update_coupon(coupon, coupon_type=coupon_type)
            coupon.id = coupon_id
            coupon_manager.add_or_update_coupon(coupon)
            if coupon.coupon_type in [coupon_pb.Coupon.GROUP_PURCHASE_LEADER, coupon_pb.Coupon.GROUP_PURCHASE_MEMBER]:
                group_purchase = group_purchase_manager.get_group_purchase(id=group_purchase_id)
                if group_purchase.merchant_id != self.merchant.id:
                    continue
                try:
                    group_purchase = group_purchase_manager.join_group_purchase(group_purchase, coupon.id)
                except errors.GroupPurchaseFullError:
                    logger.info(f"{group_purchase.id} {group_purchase.name} 已满")
                    continue
                except Exception as ex:
                    raise ex
            self.coupons.append(coupon)
            break
        if len(self.coupons) == 0:
            raise errors.GroupPurchaseFullError()

    def __join_group_purchase_without_invitation(self):
        if self.coupons_from_cache and len(self.coupons_from_cache) > 0:
            return
        group_purchase_manager = GroupPurchaseManager(user=self.user, merchant=self.merchant)
        coupon_template_manager = CouponTemplateManager()
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        coupon_id = id_manager.generate_common_id()
        group_purchase = group_purchase_manager.join_random_group_purchase(coupon_id, self.order.bill_fee)
        if group_purchase is None:
            raise errors.GroupPurchaseFullError()
        coupon_template_id = group_purchase.member_coupon_template_id
        coupon_template = coupon_template_manager.get_coupon_template(id=coupon_template_id)
        coupon = coupon_manager.create_coupon(coupon_template)
        coupon.id = coupon_id
        coupon_manager.update_coupon(coupon, coupon_type=coupon_pb.Coupon.GROUP_PURCHASE_MEMBER)
        coupon_manager.add_or_update_coupon(coupon)
        self.coupons.append(coupon)
        self.order.coupon_ids.append(coupon.id)

    def preprocess_order(self):
        if not self.order:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        update = False
        if self.is_invoice is not None:
            self.order.is_invoice = self.is_invoice
            update = True
        if self.is_phone_member_pay is not None:
            self.order.is_phone_member_pay = self.is_phone_member_pay
            update = True
        if self.is_group_purchase:
            self.join_group_purchase()
            update = True
        if update:
            ordering_da.add_or_update_order(order=self.order)
        if self.order and self.pay_method in [wallet_pb.Transaction.FANPIAO_PAY, wallet_pb.Transaction.WALLET]:
            manager = OrderManager(merchant_id=self.order.merchant_id)
            manager._remove_order_discount_price(self.order)

    def preprocess_transaction(self, transaction=None):
        if not self.transaction:
            return
        # 在回调的接口中才会没有bill_fee, paid_fee, pay_method这些参数
        self.bill_fee = self.transaction.bill_fee
        self.paid_fee = self.transaction.paid_fee
        self.pay_method = self.transaction.pay_method
        self.no_discount_bill_fee = self.transaction.no_discount_bill_fee
        self.coupon_id = self.transaction.use_coupon_id
        red_packet_da = RedPacketDataAccessHelper()
        self.red_packet = red_packet_da.get_red_packet(new_transaction_id=self.transaction.id)

    def __init_coin_deduction_coupon(self):
        if self.coin_deduction is None or self.coin_deduction == 0:
            return
        if self.merchant.coin_deduction_coupon_template_id == "":
            raise errors.ShowError("该商家没有配置时运币券模板")
        while self.order.coupon_ids:
            self.order.coupon_ids.pop()
        coupon_template_manager = CouponTemplateManager()
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        coupon_template = coupon_template_manager.get_coupon_template(id=self.merchant.coin_deduction_coupon_template_id)
        if coupon_template is None:
            raise errors.ShowError("时运币券模板不存在")
        coupon = coupon_manager.create_coupon(coupon_template)
        coupon_manager.update_coupon(coupon, coupon_type=coupon_pb.Coupon.COIN_DEDUCTION)
        coupon_manager.add_or_update_coupon(coupon)
        self.order.coupon_ids.append(coupon.id)
        self.coupons.append(coupon)

    def __init_common_coupons(self):
        if self.is_group_purchase:
            return
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        for coupon_id in self.coupon_ids:
            coupon = coupon_manager.get_coupon(id=coupon_id)
            if not coupon:
                raise errors.ShowError("券不存在")
            if coupon.merchant_id != "" and coupon.merchant_id != self.merchant.id:
                raise errors.ShowError("该券必须在指定商家使用")
            if coupon.user_id != self.user.id:
                raise errors.ShowError("券使用者错误")
            self.coupons.append(coupon)

    def init_coupon_info_v2_single(self):
        if not self.coupon_id:
            return
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        coupon = coupon_manager.get_coupon(id=self.coupon_id)
        if not coupon:
            return
        if coupon.merchant_id != "" and coupon.merchant_id != self.merchant.id:
            raise errors.ShowError("该券必须在指定商家使用")
        if coupon.user_id != self.user.id:
            raise errors.ShowError("券使用者错误")
        if coupon_manager.is_coupon_expired(coupon):
            raise errors.ShowError("券已过期")
        if coupon.status == coupon_pb.Coupon.USED:
            raise errors.ShowError("券已使用")
        self.coupons.append(coupon)

    def __init_group_purchase_coupon_info(self):
        if not self.is_group_purchase:
            return
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_user_coupon_cache_key(self.user.id)
        coupon_from_cache = redis_client.hgetall(key)
        for group_purchase_id in coupon_from_cache:
            value = coupon_from_cache.get(group_purchase_id)
            group_purchase_id = group_purchase_id.decode()
            value = value.decode().split('-')
            coupon_id = value[0]
            if coupon_id not in self.coupon_ids:
                continue
            coupon_template_id = value[1]
            coupon_type = value[2]
            self.coupons_from_cache.append(
                {
                    'coupon_id': coupon_id,
                    'coupon_template_id': coupon_template_id,
                    'coupon_type': coupon_type,
                    'group_purchase_id': group_purchase_id,
                }
            )

    def init_coupon_info(self):
        if not hasattr(self, "coupon") and not hasattr(self, "coupon_id"):
            return
        if not hasattr(self, "coupon"):
            self.coupon = CouponDataAccessHelper().get_coupon_by_id(self.coupon_id)
        if not self.coupon:
            return
        if self.coupon.user_id != self.user.id:
            logger.info('使用不属于用户的优惠券: {}, {}'.format(self.coupon_id, self.user.id))
            raise errors.WrongCouponOwner()
        if self.check_coupon != False:
            coupon_manager = CouponManager()
            if not coupon_manager.check_coupon_can_consume(self.coupon):
                raise errors.ShowError("优惠券已过期或已使用")
        coupon_category_da = CouponCategoryDataAccessHelper()
        self.coupon_category = coupon_category_da.get_coupon_category(self.coupon.coupon_category_id)
        logger.info("init coupon info: {}".format(self.coupon_category.id))
        if self.coupon_category.issue_scene == coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            self.is_platform_subsidies_coupon_category = True
            self.coupon_fee = self.coupon_category.cash_coupon_spec.reduce_cost
        elif self.coupon_category.issue_scene == coupon_category_pb.CouponCategory.REGULAR_COUPONS:
            self.coupon_fee = self.coupon_category.cash_coupon_spec.reduce_cost
        elif self.coupon_category.issue_scene == coupon_category_pb.CouponCategory.DISH_VERIFICATION_CODE:
            self.coupon_fee = self.get_dish_verification_code_coupon_fee(self.coupon_category)
        elif self.coupon_category.issue_scene == coupon_category_pb.CouponCategory.INVITE_SHARE:
            self.coupon_fee = self.coupon_category.invite_share_coupon_spec.reduce_cost
        if self.check_coupon != False and self.bill_fee < self.coupon_category.cash_coupon_spec.least_cost:
            raise errors.DonnotMeetRequirements(self.coupon_category.cash_coupon_spec.least_cost)

    def get_dish_verification_code_coupon_fee(self, coupon_category):
        strategy = DishVerificationCodeDataAccessHelper().get_dish_verification_code_strategy(
            self.merchant.brand_info.id, coupon_category.dish_verification_code_coupon_spec.strategy_id
        )
        if not strategy:
            return 0
        if not self.is_dish_verification_code_matched(strategy.dish_id):
            return 0
        dish = OrderingServiceDataAccessHelper().get_dish(dish_id=strategy.dish_id, merchant_id=self.merchant.id)
        if not dish:
            return 0
        coupon_fee = round(dish.price * (100 - strategy.rate) / float(100))
        return coupon_fee

    def is_dish_verification_code_matched(self, dish_id):
        for product in self.order.products:
            if product.id == dish_id:
                return True
        for add_products in self.order.add_products:
            for product in add_products.products:
                if product.id == dish_id:
                    return True
        raise errors.DonnotChooseDishVerificationCodeDish()

    def consume_coupon(self):
        """核销优惠券"""
        coupon_manager = CouponManager()
        if self.transaction.use_coupon_id:
            coupon_manager.consume_coupon(self.transaction.use_coupon_id)

    def generate_red_packet_for_new_member_coupon(self):
        if not self.transaction.use_coupon_id:
            return
        # 如果是拉新券,并且门店有设置拉新券红包,就给用户发一个红包
        coupon = CouponDataAccessHelper().get_coupon_by_id(self.transaction.use_coupon_id)
        category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
        if category.issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER:
            if self.store.new_member_red_packet_value > 0:
                new_member_red_packet_value = self.store.new_member_red_packet_value
                # TODO: 临时需求变更
                if category.id == '51d92e5fd68e4b59b7a13c41fe717d89':
                    new_member_red_packet_value = 200
                self.generate_red_packet(
                    red_packet_value=new_member_red_packet_value,
                    issue_scene=red_packet_pb.RedPacket.NEW_MEMBER_RED_PACKET_VALUE,
                )

    def generate_red_packet(self, red_packet_value=None, issue_scene=None, status=None):
        coupon_id = self.transaction.use_coupon_id
        if self.order.platform_discount_fee > 0:
            logger.info("已经有补贴了,不生成红包: {} {}".format(self.transaction.id, self.order.id))
            return False
        if coupon_id is not None and coupon_id != "":
            logger.info("使用优惠券,不生成红包: {}, {}".format(coupon_id, self.transaction.id))
            return False
        if self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            logger.info("使用饭票支付,不生成红包: {}".format(self.transaction.id))
            return False
        if self.transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            logger.info("使用会员卡余额支付,不生成红包: {}".format(self.transaction.id))
            return False
        if self.order and self.order.is_invoice:
            logger.info("要开发票,不生成红包: {}".format(self.transaction.id))
            return False
        if self.order and self.order.is_pos_discount:
            logger.info("pos机上打折,不生成红包: {}".format(self.order.id))
            return False
        if self.order.is_phone_member_pay:
            logger.info("手机会员支付,不生成红包: {}".format(self.order.id))
            return False
        if self.transaction.pay_method == wallet_pb.Transaction.WALLET:
            logger.info("零钱支付,不生成红包: {}".format(self.transaction.id))
            return False
        if len(self.order.coupon_ids) > 0:
            logger.info(f"用券了,不生成红包: {self.order.id}")
            return False

        red_packet_block_rate = self.merchant.preferences.red_packet_config.block_rate
        random_num = random.randint(1, 100)
        if random_num > 100 - red_packet_block_rate:
            logger.info(f"红包拦截，随机数{str(random_num)}大于发放比例{100-red_packet_block_rate}%")
            return False

        if (
            self.transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT
            or self.transaction.type == wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT
            or self.transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT
        ):
            # red_packet_value = self._cal_red_packet_value()
            red_packet_value = int(
                self.merchant.preferences.red_packet_config.red_packet_discount
                * (
                    float(self.order.enable_discount_fee - self.coupon_fee)
                    if self.transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT
                    else float(self.order.bill_fee - self.coupon_fee)
                )
                / 100
            )

        if red_packet_value is not None and red_packet_value > 0:
            logger.info("生成红包: {}".format(self.transaction.id))
            self.red_packet = RedPacketManager().generate_red_packet(
                user_ids=[self.transaction.payer_id],
                total_value=round(red_packet_value),
                issue_scene=issue_scene,
                transaction_id=self.transaction.id,
                status=status,
            )
            red_packet_da = RedPacketDataAccessHelper()
            if self.red_packet:
                red_packet_da.add_or_update_red_packet(self.red_packet)
            return True
        return False

    def _cal_no_order_platform_subsidies(self):
        """非扫码点餐,直接支付,使用券包"""
        if not self.is_platform_subsidies_coupon_category:
            return 0
        bill_fee = self.transaction.bill_fee
        paid_fee = self.transaction.paid_fee
        max_discount = (
            int(
                self.merchant.preferences.coupon_config.max_discount
                * self.merchant.preferences.coupon_config.dish_discount_rate
                / float(100)
            )
            + self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        )
        least_paid_fee = bill_fee * (100 - max_discount) / float(100)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee
        if real_paid_fee >= least_paid_fee:
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        logger.info(
            "_cal_no_order_platform_subsidies: {}, {}, {}, {}".format(
                self.order.id, least_paid_fee, real_paid_fee, platform_discount_fee
            )
        )
        return round(platform_discount_fee)

    def _cal_no_order_fanpiao_platform_subsidies(self):
        """非扫码点餐,使用饭票支付"""
        if self.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            return 0
        bill_fee = self.transaction.bill_fee
        paid_fee = self.transaction.paid_fee
        max_discount = (
            int(
                self.merchant.preferences.coupon_config.max_discount
                * self.merchant.preferences.coupon_config.dish_discount_rate
                / float(100)
            )
            + self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        )
        least_paid_fee = bill_fee * (100 - max_discount) / float(100)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee
        if real_paid_fee >= least_paid_fee:
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        logger.info(
            "_cal_no_order_fanpiao_platform_subsidies: {}, {}, {}, {}".format(
                self.order.id, least_paid_fee, real_paid_fee, platform_discount_fee
            )
        )
        return round(platform_discount_fee)

    def _cal_fanpiao_platform_discount(self, registration_info=None):
        """如果用户使用的是饭票,计算需要做的平台补贴
        此函数已废弃:
            在fanpiao_pay_manager.py中,使用饭票时,会同时计算好每一张饭票应补贴的金额
            所以此函数废弃
        """
        if True:
            return 0
        if self.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            return 0
        if self.order.is_pos_discount:
            return 0
        bill_fee = self.order.enable_discount_fee
        paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        if self.order.giving_fee > 0:
            # 如果有赠送菜品
            bill_fee = self.transaction.bill_fee - self.order.giving_fee
            paid_fee = self.transaction.paid_fee - self.order.giving_fee
        max_discount = (
            self.merchant.preferences.coupon_config.max_discount
            + self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        )
        if registration_info is not None and registration_info.fanpiao_pay_commission_rate > 0:
            # 如果要收取饭票佣金,补贴需要补到 100%
            max_discount = 0
            self.order.max_discount = 0
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee + self.order.ifeedu_fee + self.order.giving_fee
        if real_paid_fee >= least_paid_fee:
            self.order.subsidies_overflow_fee = real_paid_fee - least_paid_fee
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        self.order.platform_discount_fee = platform_discount_fee
        logger.info(
            "_cal_fanpiao_platform_discount: {}, {}, {}, {}".format(
                self.order.id, least_paid_fee, real_paid_fee, platform_discount_fee
            )
        )
        return round(platform_discount_fee)

    def _cal_coupon_subsidy_fee(self, registration_info=None):
        """计算用券补贴金额"""
        if len(self.order.coupon_ids) == 0:
            logger.info(f"新版券的补贴逻辑 {self.order.id}: 没有用券")
            return 0
        bill_fee = self.order.enable_discount_fee
        paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        max_discount = int(
            self.merchant.preferences.coupon_config.max_discount
            * self.merchant.preferences.coupon_config.dish_discount_rate
            / float(100)
        )
        if registration_info and registration_info.coupon_commission_rate > 0:
            max_discount = 0
        self.order.max_discount = max_discount
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee
        if real_paid_fee >= least_paid_fee:
            return 0
        subsidy_fee = least_paid_fee - real_paid_fee
        self.order.platform_discount_fee = subsidy_fee
        logger.info(f"新版券补贴逻辑: {self.order.id}, {least_paid_fee}, {real_paid_fee}, {subsidy_fee}")
        return subsidy_fee

    def _cal_vip_membership_subsidy_fee(self, registration_info=None):
        """计算时享会员补贴"""
        if not WalletManager().is_vip_membership(self.user.id):
            return 0
        if self.pay_method != wallet_pb.Transaction.WALLET:
            return 0
        bill_fee = self.order.enable_discount_fee
        paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        max_discount = int(
            self.merchant.preferences.coupon_config.max_discount
            * self.merchant.preferences.coupon_config.dish_discount_rate
            / float(100)
        )
        if registration_info and registration_info.wallet_pay_commission_rate > 0:
            max_discount = 0
        self.order.max_discount = max_discount
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee
        if real_paid_fee >= least_paid_fee:
            return 0
        subsidy_fee = least_paid_fee - real_paid_fee
        self.order.platform_discount_fee = subsidy_fee
        logger.info(f"时享会员补贴逻辑: {self.order.id}, {least_paid_fee}, {real_paid_fee}, {subsidy_fee}")
        return subsidy_fee

    def _cal_platform_discount(self, transaction=None, registration_info=None):
        """计算平台优惠
        当用户使用优惠券时,有可能打折力度超过商户最大打折力度.
        这个时候时来平台会做补偿,补偿的部分则是平台优惠
        """
        if not self.transaction.use_coupon_id:
            logger.info("cal_platform_discount {}: 没有用券,没有平台补贴".format(self.order.id))
            return 0
        # 如果是不用平台补贴的券,直接返回0
        if not self.is_platform_subsidies_coupon_category:
            logger.info("cal_platform_discount {}: 使用的不是券包的券,没有平台补贴".format(self.order.id))
            return 0
        if self.order.is_pos_discount:
            logger.info("cal_platform_discount {}: 使用pos机打折,没有平台补贴".format(self.order.id))
            return 0
        if transaction is None:
            transaction = self.transaction
        bill_fee = self.order.enable_discount_fee
        paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        if self.order.giving_fee > 0:
            bill_fee = transaction.bill_fee - self.order.giving_fee
            paid_fee = transaction.paid_fee - self.order.giving_fee
        max_discount = (
            int(
                self.merchant.preferences.coupon_config.max_discount
                * self.merchant.preferences.coupon_config.dish_discount_rate
                / float(100)
            )
            + self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        )
        if registration_info and registration_info.coupon_package_pay_commission_rate > 0:
            max_discount = 0
            self.order.max_discount = 0
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee + self.order.ifeedu_fee
        if real_paid_fee >= least_paid_fee:
            self.order.subsidies_overflow_fee = real_paid_fee - least_paid_fee
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        self.order.platform_discount_fee = platform_discount_fee
        logger.info(
            "cal_platform_discount: {}, {}, {}, {}".format(self.order.id, least_paid_fee, real_paid_fee, platform_discount_fee)
        )
        return round(platform_discount_fee)

    def _cal_extra_platform_discount(self):
        """计算额外补贴,如商户A:
        maxDiscount = 10, 商户给的折扣为9折
        shilaiDiscount = 10, 时来额外再多给商户10个点的折扣
        用户一共拥有20个点
        20个点由 立减 + 红包 组成
        订单总金额为 100 元,商户应收金额为 90
        随机立减为 x 元,用户支付金额为 y 元, 红包金额为 z 元
        补贴金额为 90 - (y - z)
        """
        if (
            self.merchant.preferences.coupon_config.shilai_discount == 0
            and self.merchant.preferences.red_packet_config.red_packet_discount == 0
        ):
            # 没有额外的折扣
            return 0
        if self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            # 使用饭票的话,补贴不用再计算
            return 0
        if self.is_platform_subsidies_coupon_category:
            return 0
        if self.order.is_pos_discount:
            return 0
        if len(self.order.coupon_ids) > 0:
            # 使用新版券，补贴不用再计算
            return 0

        if self.red_packet and self.red_packet.status == red_packet_pb.RedPacket.CANCELLED:
            self.order.platform_discount_fee = 0
            return 0

        # bill_fee = self.order.enable_discount_fee
        # paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        # if self.order.giving_fee > 0:
        #     bill_fee = self.transaction.bill_fee - self.order.giving_fee
        #     paid_fee = self.transaction.paid_fee - self.order.giving_fee
        # max_discount = int(
        #     self.merchant.preferences.coupon_config.max_discount
        #     * self.merchant.preferences.coupon_config.dish_discount_rate
        #     / float(100)
        # )

        # 时来红包补贴，发了红包才会补贴
        if self.red_packet:
            # least_paid_fee = round(
            #     bill_fee
            #     * (
            #         100
            #         - self.merchant.preferences.red_packet_config.red_packet_discount
            #         + self.merchant.preferences.red_packet_config.shilai_extra_discount
            #     )
            #     / float(100)
            # )
            self.order.red_packet_shilai_discount = self.merchant.preferences.red_packet_config.shilai_extra_discount
            self.order.red_packet_discount = self.merchant.preferences.red_packet_config.red_packet_discount
            self.order.platform_discount_fee = round(self.merchant.preferences.red_packet_config.shilai_extra_discount / self.merchant.preferences.red_packet_config.red_packet_discount * self.red_packet.total_value) if self.merchant.preferences.red_packet_config.red_packet_discount > 0 else 0
            logger.info(f"时来额外补贴: {self.order.platform_discount_fee}")
        # else:
        #     least_paid_fee = round(bill_fee * (100 - max_discount) / float(100))
        # # 如果用户支付的钱大于这个数,我们就不用补偿
        # real_paid_fee = paid_fee + self.order.ifeedu_fee
        # if self.red_packet:
        #     real_paid_fee -= self.red_packet.total_value
        # if real_paid_fee >= least_paid_fee:
        #     self.order.subsidies_overflow_fee = real_paid_fee - least_paid_fee
        #     return 0
        # platform_discount_fee = least_paid_fee - real_paid_fee
        # logger.info("时来额外补贴: {}, {}, {}, {}".format(self.order.id, real_paid_fee, least_paid_fee, platform_discount_fee))
        # self.order.platform_discount_fee = platform_discount_fee

    def _cal_invite_share_coupon_platform_discount(self):
        if not self.coupon:
            return
        if not self.coupon_category:
            return
        if self.coupon_category.issue_scene != coupon_category_pb.CouponCategory.INVITE_SHARE:
            return
        bill_fee = self.order.enable_discount_fee
        paid_fee = self.transaction.paid_fee - (self.order.bill_fee - self.order.enable_discount_fee)
        if self.order.giving_fee > 0:
            bill_fee = self.transaction.bill_fee - self.order.giving_fee
            paid_fee = self.transaction.paid_fee - self.order.giving_fee
        max_discount = self.merchant.preferences.coupon_config.invite_share_max_discount
        coupon_category_spec = self.coupon_category.invite_share_coupon_spec
        if coupon_category_spec.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
            max_discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_new_user
        else:
            max_discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_old_user
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        # 如果用户支付的钱大于这个数,我们就不用补偿
        real_paid_fee = paid_fee + self.order.ifeedu_fee
        if real_paid_fee >= least_paid_fee:
            # self.order.subsidies_overflow_fee = real_paid_fee - least_paid_fee
            self.order.platform_discount_fee = 0
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        self.order.platform_discount_fee = platform_discount_fee
        logger.info(
            "_cal_invite_share_coupon_platform_discount: {}, {}, {}, {}".format(
                self.order.id, least_paid_fee, real_paid_fee, platform_discount_fee
            )
        )
        return round(platform_discount_fee)

    def _cal_red_packet_value(self):
        discount_plan = OrderingServiceDataAccessHelper().get_discount_plan(self.user.id, self.merchant.id)
        if discount_plan is None:
            return 0
        if discount_plan:
            max_value_cap = discount_plan.red_packet_discount.max_value_cap
            max_percentage = discount_plan.red_packet_discount.max_red_packet_percentage
            min_percentage = discount_plan.red_packet_discount.min_red_packet_percentage
        else:
            max_value_cap = 1800
            max_percentage = 9
            min_percentage = 1
        if self.transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            max_value = (
                float(self.order.enable_discount_fee - self.coupon_fee)
                * min(self.merchant.preferences.coupon_config.max_discount, max_percentage)
                / 100
            )
            min_value = (
                float(self.order.enable_discount_fee - self.coupon_fee)
                * min(self.merchant.preferences.coupon_config.max_discount, max_percentage)
                / 100
            )
        else:
            max_value = (
                float(self.transaction.bill_fee - self.coupon_fee)
                * min(self.merchant.preferences.coupon_config.max_discount, max_percentage)
                / 100
            )
            min_value = (
                float(self.transaction.bill_fee - self.coupon_fee)
                * min(self.merchant.preferences.coupon_config.max_discount, min_percentage)
                / 100
            )

        red_packet_value = int(random.uniform(min_value, max_value))
        # if red_packet_value > max_value_cap:
        #     red_packet_value = max_value_cap
        return red_packet_value

    def check_pay_method_valid(self):
        if self.order.is_pos_discount:
            if self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                raise errors.CannotUseCouponPackageWithPosDiscount()
            if self.coupon_id:
                raise errors.CannotUseCouponPackageWithPosDiscount()
        if self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            if self.coupon_id:
                raise errors.CannotUseFanpiaoWithCoupon()

        if self.pay_method == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_PAY:
            if self.coupon_id:
                raise errors.CannotUseKeruyunMemberCardWithCoupon()

        if self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            if self.coupon_id:
                raise errors.CannotUseShilaiMemberCardPayWithCoupon()

    def set_no_pos_paid(self):
        key = CacheServerKeys.get_order_no_pos_paid_key(self.order)
        self.redis_client.set(key, 1, ex=600, nx=True)

    def check_prepay_repeat(self):
        return super().add_order_lock()

    def consume_coupon_v2(self):
        if len(self.order.coupon_ids) == 0:
            return
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        for coupon_id in self.order.coupon_ids:
            coupon = coupon_manager.get_coupon(id=coupon_id)
            if coupon.coupon_type == coupon_pb.Coupon.COIN_DEDUCTION:
                coupon_manager.consume_coin_deduction_coupon(
                    coupon, coupon_manager.get_coupon_reduce_fee(self.order.bill_fee, coupon)
                )
            else:
                coupon_manager.consume_coupon(coupon)
            coupon_manager.add_or_update_coupon(coupon)
            self.coupons.append(coupon)

    def consume_coupon_v2_single(self):
        if not self.coupons:
            return
        coupon_manager = NewCouponManager(user=self.user, merchant=self.merchant)
        for coupon in self.coupons:
            if coupon.coupon_type == coupon_pb.Coupon.COIN_DEDUCTION:
                coupon_manager.consume_coin_deduction_coupon(
                    coupon, coupon_manager.get_coupon_reduce_fee(self.bill_fee, coupon)
                )
            else:
                coupon_manager.consume_coupon(coupon)
            coupon_manager.add_or_update_coupon(coupon)

    def deal_coupon_as_group_purchase(self):
        if len(self.coupons) == 0:
            return
        for coupon in self.coupons:
            group_purchase_manager = GroupPurchaseManager(user=self.user, merchant=self.merchant)
            group_purchase = group_purchase_manager.get_group_purchase(coupon_id=coupon.id)
            if coupon.coupon_type == coupon_pb.Coupon.GROUP_PURCHASE_LEADER:
                self.__deal_coupon_as_leader(group_purchase)
            elif coupon.coupon_type == coupon_pb.Coupon.GROUP_PURCHASE_MEMBER:
                self.__deal_coupon_as_member(group_purchase, coupon)
            self.__delete_group_purchase_coupon_rache(group_purchase)

    def __deal_coupon_as_leader(self, group_purchase):
        """如果是团长参团的处理逻辑."""

    def __deal_coupon_as_member(self, group_purchase, coupon):
        """如果是成员参团的处理逻辑."""
        self.try_incr_leader_account_balance(group_purchase)

    def __delete_group_purchase_coupon_rache(self, group_purchase):
        if group_purchase is None:  # 团购合并支付的时候不用删
            return
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_user_coupon_cache_key(self.user.id)
        result = redis_client.hdel(key, group_purchase.id)
        logger.info(f"从缓存删除coupon_id: {key} {group_purchase.id} {result}")

    def try_incr_leader_account_balance(self, group_purchase):
        """当团员完成订单的时候为团长增加一条零钱入账记录."""
        if not group_purchase:
            return
        if group_purchase.leader_id == self.user.id:
            return
        tm = TransactionManager()
        for group_member in group_purchase.group_members:
            if group_member.coupon_id not in self.order.coupon_ids:
                continue
            self.inc_leader_wallet_balance_transaction = tm.create_group_purchase_member_finish_leader_wallet_revenue(
                user_id=group_purchase.leader_id,
                paid_fee=group_purchase.member_coupon_min_bill_fee,
                transaction_id=group_member.leader_cash_refund_transaction_id,
            )

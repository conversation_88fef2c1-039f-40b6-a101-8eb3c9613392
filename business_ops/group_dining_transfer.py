# -*- coding: utf-8 -*-

"""
@deprecated
"""

import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.group_dining_pb2 as group_dining_pb
from business_ops.group_dining_manager import GroupDiningManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.wallet_manager import WalletManager
from business_ops.business_manager import BusinessManager
from common.utils import date_utils
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from service import errors
from service import error_codes
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

logger = logging.getLogger(__name__)


class GroupDiningTransferManager(BusinessManager):

    def __init__(self, merchant_id=None, user_id=None, pay_method=None, bill_fee=None, paid_fee=None,
                 order_id=None, transaction_id=None, user_cnt=0, dining_id=None, no_discount_bill_fee=0, coupon_id=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.user_cnt = user_cnt
        self.no_discount_bill_fee = no_discount_bill_fee

        super(GroupDiningTransferManager, self).__init__(
            merchant_id=merchant_id, user_id=user_id, pay_method=pay_method, order_id=order_id, transaction_id=transaction_id,
            dining_id=dining_id, coupon_id=coupon_id)

        self.paid_fee = self.round_off(self.paid_fee)
        self.bill_fee = self.round_off(self.bill_fee)
        self.no_discount_bill_fee = self.round_off(self.no_discount_bill_fee)

    def prepay(self):
        """ AA付款给买单人
        Args:
            user_id: (string)发起支付的用户ID
            dining: 饭局
            pay_method: (wallet_pb.Transaction.PayMethod)支付方式
        Return:
            create_responses_obj的返回值
        """
        transaction_da = TransactionDataAccessHelper()
        invitation_da = InvitationDataAccessHelper()

        # 检查饭局是否己支付
        if self.dining.state != group_dining_pb.GroupDiningEvent.PAID:
            logger.info("饭局 {} 未支付".format(self.dining_id))
            raise errors.DiningNotPaid()

        # 检查这个用户是否已经支付过了
        invitation = invitation_da.get_invitation(
            user_id=self.user_id, dining_id=self.dining_id, state=group_dining_pb.Invitation.ACCEPTED)
        invitation_transaction = transaction_da.get_transaction_by_id(invitation.transaction_id)
        if invitation_transaction and invitation_transaction.state == wallet_pb.Transaction.SUCCESS:
            logger.info("AA己支付 饭局: {}, user_id: {}".format(self.dining_id, self.user_id))
            raise errors.AlreadyTransfer()

        self.check_dish_remain_quantity(self.order)
        # 计算应该付多少钱
        dining_transaction = transaction_da.get_transaction_by_id(self.dining.transaction_id)
        bill_fee = GroupDiningManager.calculate_transfer_bill_fee(self.dining_id, dining_transaction)

        # 生成本次支付的transaction
        logger.info('生成饭局 AA transaction')
        self.transaction = TransactionManager().handle_group_dining_transfer_prepay(
            payer_id=self.user_id, dining=self.dining, pay_method=self.pay_method, bill_fee=bill_fee)

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 微信支付
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction)
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            # 从钱包转账到买单人
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction)
        else:
            logger.info("{},支付方式不支持".format(self.pay_method))
            raise errors.OrderPosReturn()

        if result.get('errcode') == error_codes.SUCCESS:
            invitation_da.update(user_id=self.user_id, dining_id=self.dining.id, transaction_id=self.transaction.id)
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
                logger.info("饭局: {},用户: {} AA支付微信prepay成功".format(self.dining_id, self.user_id))
            elif self.pay_method == wallet_pb.Transaction.WALLET:
                self.handle_group_dining_transfer_notification()
                result.update({"transactionId": self.transaction.id})
                logger.info("饭局: {},用户: {} AA支付成功".format(self.dining_id, self.user_id))
        return result

    def notification(self):
        """ AA付款给买单人的回调函数
        """
        if self.transaction.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 直接给买单人钱包里加钱
            WalletManager().increase_balance(self.dining.director_id, self.transaction.bill_fee)
        elif self.transaction.pay_method == wallet_pb.Transaction.WALLET:
            WalletManager().increase_balance(self.dining.director_id, self.transaction.bill_fee)

        # 1. 更新邀请相关的transaction_id
        # 2. 更新买单人的monetary_state为可领取红包或者为己完成
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=self.dining.transaction_id)
        if red_packet:
            monetary_state = group_dining_pb.Invitation.RED_PACKET_PENDING
        else:
            monetary_state = group_dining_pb.Invitation.COMPLETED
        InvitationDataAccessHelper().update(
            user_id=self.transaction.payer_id, dining_id=self.dining.id, transaction_id=self.transaction.id,
            monetary_state=monetary_state)

        # 1. 更新transaction的状态为成功
        # 2. 更新transaction的支付时间
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        TransactionManager().update_transaction(self.transaction)
        self.decrease_dish_remain_quantity(self.order)
        self.release_order_lock(self.order)
        MultiPartyDinningManager(order=self.order).payment_event()

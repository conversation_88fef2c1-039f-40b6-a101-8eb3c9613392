# -*- encoding: utf-8 -*-

'''
@Time        :   2024/05/09 17:43:06
'''
import time
import json
import hashlib

from proto import permission_pb2 as permission_pb
from dao.dao_helper import DaoORMHelper
from common.schema import SnakeDict


_get_timestamp = lambda: int(time.time())
_uuid = lambda obj: hashlib.md5(json.dumps(obj).encode("utf-8")).hexdigest()


class PermissionConstant:
    PERMISSION_DB = "permission_db"
    UI_PERMISSION_SET = "ui_permission_set"
    API_PERMISSION_SET = "api_permission_set"


class UIPermissionManager(DaoORMHelper):
    def __init__(self):
        super().__init__(
            db=PermissionConstant.PERMISSION_DB,
            collection=PermissionConstant.UI_PERMISSION_SET,
            pb=permission_pb.UIPermissionSet
        )

    def create_role_permission(
        self,
        name,
        role,
        permission_set,
    ):
        return SnakeDict(
            id=_uuid({
                'name': name,
                'role': role
            }),
            name=name,
            role=role,
            permission_set=permission_set,
            update_time=_get_timestamp()
        )

    def get_role(self, name, role, **kwargs):
        return super().get(
            matcher={
                "name": name,
                "role": role
            },
            **kwargs
        )


# class APIPermissionManager(DaoORMHelper):
#     def __init__(self):
#         super().__init__(
#             db=PermissionConstant.PERMISSION_DB,
#             collection=PermissionConstant.API_PERMISSION_SET,
#             pb=permission_pb.APIPermissionSet
#         )

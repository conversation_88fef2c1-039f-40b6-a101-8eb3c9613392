# -*- coding: utf-8 -*-


"""
扫码点餐与券包聚合支付
"""

import logging
import time

import proto.finance.wallet_pb2 as wallet_pb
import proto.coupons_pb2 as coupons_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.business_manager import BusinessManager
from business_ops import merchant_store_manager
from business_ops.payment_manager import PaymentManager
from business_ops.transaction_manager import TransactionManager
from business_ops.ordering.table_manager import TableManager
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.coupon_package_manager import CouponPackageManager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from common.utils.number_utils import NumberUtils
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors
from service import error_codes
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

logger = logging.getLogger(__name__)


class SelfDishOrderPaymentWithCouponPackageManager(BusinessManager):

    def __init__(self, transaction_id=None, user_id=None, merchant_id=None,
                 bill_fee=None, paid_fee=None, pay_method=None, coupon_package_id=None,
                 no_discount_fee=None, order_id=None, is_invoice=None, return_url=None,
                 coupon_fee=None, transaction=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.shipping_fee = 0
        self.return_url = return_url
        self.coupon_fee = coupon_fee
        self.no_discount_fee = 0

        super(SelfDishOrderPaymentWithCouponPackageManager, self).__init__(
            user_id=user_id, transaction_id=transaction_id, merchant_id=merchant_id, pay_method=pay_method,
            coupon_package_id=coupon_package_id, order_id=order_id, is_invoice=is_invoice, return_url=return_url,
            transaction=transaction)

        self.paid_fee = NumberUtils.safe_round(self.paid_fee)
        self.bill_fee = NumberUtils.safe_round(self.bill_fee)
        self.shipping_fee = NumberUtils.safe_round(self.shipping_fee)
        if self.coupon_package:
            self.coupon_fee = self.coupon_package.coupon_package_spec.reduce_cost
        self.redis_client = RedisClient().get_connection()

    def check_no_discount_time_ranges(self):
        ret = merchant_store_manager.is_no_discount_time_ranges(self.merchant)
        if ret.flag:
            raise errors.NoDiscountTimeRanges()

    def check_order_valid(self):
        if not self.order:
            raise errors.OrderNotFound()
        if self.order.status == dish_pb.DishOrder.PAID:
            raise errors.OrderAlreadyPaid()
        if self.order.status == dish_pb.DishOrder.POS_PAID:
            raise errors.OrderAlreadyPaid()
        if self.order.is_cannot_pay_order:
            raise errors.OrderCannotPay()
        coupon_package_sell_price = self.coupon_package.coupon_package_spec.sell_price
        backend_bill_fee = self.order.bill_fee + coupon_package_sell_price
        backend_paid_fee = self.order.paid_fee + coupon_package_sell_price

        backend_bill_fee += self.order.keruyun_take_out_packaging_fee
        backend_bill_fee += self.order.keruyun_take_out_shipping_fee
        # backend_bill_fee += self.order.package_fee
        backend_paid_fee += self.order.giving_fee
        backend_paid_fee -= self.coupon_fee
        if backend_paid_fee <= coupon_package_sell_price:
            backend_paid_fee = coupon_package_sell_price
        backend_paid_fee += self.order.keruyun_take_out_packaging_fee
        backend_paid_fee += self.order.keruyun_take_out_shipping_fee
        # backend_paid_fee += self.order.package_fee

        message = f"""
        扫码点餐prepay(单位: 分): {self.order.id}
        前端账单总金额: {self.bill_fee}
        前端账单总支付: {self.paid_fee}
        后端账单总金额: {backend_bill_fee}
        后端账单总支付: {backend_paid_fee}
        """
        logger.info(message)
        if self.bill_fee != backend_bill_fee:
            raise errors.PaidfeeBillfeeNotMatch()
        if self.paid_fee != backend_paid_fee:
            raise errors.PaidfeeBillfeeNotMatch()

    def prepay(self):
        self.check_no_discount_time_ranges()
        self.check_order_valid()
        self.check_prepay_repeat()
        manager = TableManager(merchant=self.merchant)
        manager.check_table_status(self.order.table_id)
        self.check_dish_remain_quantity(self.order)
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.handle_self_dining_payment_with_coupon_package_prepay(
            user_id=self.user.id, merchant_id=self.merchant.id, pay_method=self.pay_method, bill_fee=self.bill_fee,
            paid_fee=self.paid_fee)

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            result = payment_manager.prepay(
                transaction=self.transaction, order_id=self.order.id, coupon_package_id=self.coupon_package.id)
            logger.info("微信: 扫码点餐与券包聚合支付: {}".format(result))
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = payment_manager.prepay(
                transaction=self.transaction, order_id=self.order_id, return_url=self.return_url,
                coupon_package_id=self.coupon_package.id, user_id=self.user.id)
            logger.info("支付宝: 扫码点餐与券包聚合支付: {}".format(result))
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") == error_codes.SUCCESS:
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                transaction_manager.update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                transaction_manager.update_transaction(self.transaction)
            result["transactionId"] = self.transaction.id
        return result

    def notification(self, ordering_retry=True):
        if ordering_retry:
            self.check_order_repeat_pay()
        self.set_no_pos_paid()
        coupons = None
        try:
            coupons = self.buy_coupon_package_notification()
            self.ordering_notification(coupons)
        except Exception as e:
            logger.exception("券包扫码点餐合并支付出错: {}".format(e))
            # 删除券包
            if coupons is not None:
                self.delete_coupons(coupons)
            raise e
        self.deal_with_ordering_coupon_package_union_pay_transaction()
        self.deal_with_union_pay_transaction()
        self.clear_order_repeat_pay_key()
        self.decrease_dish_remain_quantity(self.order)
        self.release_order_lock(self.order)
        MultiPartyDinningManager(order=self.order).payment_event()

    def delete_coupons(self, coupons):
        coupon_da = CouponDataAccessHelper()
        coupon_package = coupon_da.get_coupon_package(self.buy_coupon_package_transaction.id)
        coupon_package.status = coupons_pb.CouponPackage.DELETED
        coupon_da.add_or_update_coupon_package(coupon_package)
        for coupon in coupons:
            coupon.state = coupons_pb.Coupon.DELETED
            coupon_da.update_or_create_coupon(coupon)

    def buy_coupon_package_notification(self):
        """ 购买券包
        """
        transaction_manager = TransactionManager()

        coupon_package_sell_price = self.coupon_package.coupon_package_spec.sell_price
        self.buy_coupon_package_transaction = transaction_manager.handle_buy_coupon_package_prepay(
            user_id=self.user.id, merchant_id=self.merchant.id, pay_method=self.transaction.pay_method,
            fee=coupon_package_sell_price)
        manager = CouponPackageManager(merchant=self.merchant, transaction=self.buy_coupon_package_transaction, coupon_package=self.coupon_package)
        manager.generate_coupon_package()
        coupons = manager.notification(union_pay=True)
        return coupons

    def ordering_notification(self, coupons):
        transaction_manager = TransactionManager()
        store_id = "{}_0".format(self.merchant.id)
        coupon_package_sell_price = self.coupon_package.coupon_package_spec.sell_price
        order_bill_fee = self.transaction.bill_fee - coupon_package_sell_price
        order_paid_fee = self.transaction.paid_fee - coupon_package_sell_price
        self.ordering_transaction = transaction_manager.handle_self_dining_ordering_payment_prepay(
            payer_id=self.user.id, payee_id=self.merchant.id, pay_method=self.transaction.pay_method,
            payee_store_id=store_id, bill_fee=order_bill_fee, paid_fee=order_paid_fee, coupon_id="")
        self.ordering_transaction.use_coupon_id = coupons[0].id
        manager = SelfDishOrderPaymentManager(
            transaction=self.ordering_transaction,
            merchant=self.merchant,
            order=self.order,
            coupon_id=coupons[0].id,
            bill_fee=self.ordering_transaction.bill_fee)
        manager.notification(union_pay=True, ordering_retry=False, decrease_dish_remain=False)

    def check_order_repeat_pay(self):
        """ 对于需要接收回调来处理逻辑的支付方式,根据orderId来做去重判断.
        如果已经有一个用户在支付了,则给后来支付的用户退款
        """
        check_pay_methods = [
            wallet_pb.Transaction.WECHAT_PAY,
            wallet_pb.Transaction.ALIPAY,
            wallet_pb.Transaction.TIAN_QUE_PAY
        ]
        key = CacheServerKeys.get_order_repeat_pay_key(self.order)
        if self.redis_client.set(key, 1, ex=6, nx=True):
            return
        else:
            if self.ordering_transaction.pay_method not in check_pay_methods:
                return
            raise errors.OrderAlreadyPaid()

    def clear_order_repeat_pay_key(self):
        key = CacheServerKeys.get_order_repeat_pay_key(self.order)
        self.redis_client.delete(key)

    def deal_with_ordering_coupon_package_union_pay_transaction(self):
        """ 保存 聚合支付,扫码点餐支付,券包支付 三个transactionId
        """
        transaction_da = TransactionDataAccessHelper()
        transaction = wallet_pb.OrderingCouponPackageUnionPayTransaction()
        transaction.transaction_id = self.transaction.id
        transaction.ordering_transaction_id = self.ordering_transaction.id
        transaction.buy_coupon_package_transaction_id = self.buy_coupon_package_transaction.id
        transaction_da.add_or_update_ordering_coupon_package_union_pay_transaction(transaction)

    def deal_with_union_pay_transaction(self):
        """ 执行分账
        """
        if self.transaction.ledgered:
            return
        transaction_manager = TransactionManager()
        payment_manager = PaymentManager(pay_method=self.transaction.pay_method, merchant=self.merchant)
        payment_manager.ordering_coupon_package_union_pay_launch_ledger(
            self.transaction, self.ordering_transaction, self.order, self.buy_coupon_package_transaction)
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = int(time.time())
        transaction_manager.update_transaction(self.transaction)

# -*- coding: utf-8 -*-

import logging
from collections import namedtuple
import proto.membership_pb2 as membership_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.shilai_ops_manager import ShilaiOPSManager
from business_ops.ordering.constants import ShilaiPosConstants
from common.api_result import ApiResult

logger = logging.getLogger(__name__)


class ShilaiPosMemberManager(ShilaiOPSManager):
    def __init__(self, *args, **kargs):
        super(ShilaiPosMemberManager, self).__init__(*args, **kargs)
        self.Ret = namedtuple("Ret", ["flag", "ret"])

    def increase_member_card_balance_by_balance(self, balance, phone, user_id=None):
        uri = ShilaiPosConstants.INCREASE_MEMBER_CARD_BALANCE
        url = self.generate_url(uri)
        params = {"phone": phone, "balance": balance, 'userId': user_id}
        logger.info(f"时来会员储值 {self.merchant.id} {params}")
        ret = self.do_post(url, params)
        return ApiResult(ret)

    def decrease_member_card_balance(self, transaction, user):
        """扣减余额"""
        if not user.member_profile.mobile_phone:
            return self.Ret(False, None)
        if not self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return self.Ret(False, None)
        uri = ShilaiPosConstants.DECREASE_MEMBER_CARD_BALANCE
        url = self.generate_url(uri)
        params = {"phone": user.member_profile.mobile_phone, "balance": abs(transaction.bill_fee) * -1}
        ret = self.do_post(url, params)
        return ApiResult(ret)

    def get_member_card_balance(self, user):
        """获取储值余额"""
        if not user.member_profile.mobile_phone:
            return ApiResult()
        if not self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return ApiResult()
        uri = ShilaiPosConstants.GET_MEMBER_CARD_BALANCE
        url = self.generate_url(uri)
        params = {"phone": user.member_profile.mobile_phone}
        ret = self.do_post(url, params)
        return ApiResult(ret)

    def get_member_card_balance_by_phone(self, phone):
        """获取储值余额"""
        if not self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return ApiResult()
        uri = ShilaiPosConstants.GET_MEMBER_CARD_BALANCE
        url = self.generate_url(uri)
        params = {"phone": phone}
        ret = self.do_post(url, params)
        return ApiResult(ret)

    def get_merchant_member_recharge_config_list(self):
        if not self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return ApiResult()
        uri = ShilaiPosConstants.GET_MEMBER_RECHARGE_CONFIG_LIST
        url = self.generate_url(uri)
        params = {}
        ret = self.do_post(url, params)
        if ret.get("data") and len(ret.get("data")) > 0:
            return ApiResult(ret, self.__parse_recharge_config_list)
        return ApiResult()

    def __parse_recharge_config_list(self, configs):
        ret = []
        for config in configs:
            c = membership_pb.MemberCardRechargeConfig()
            c.id = config.get("id")
            c.merchant_id = self.merchant.id
            c.amount = config.get("value") + config.get("givingFee")
            c.sell_price = config.get("value")
            ret.append(c)
        return ret

    def get_recharge_config(self, id):
        ret = self.get_merchant_member_recharge_config_list()
        configs = ret.data
        for config in configs:
            if id == config.id:
                return config
        return None

# -*- coding: utf-8 -*-

"""
邀请分享活动
"""

import logging
import time
import random
from collections import namedtuple
from datetime import datetime

import proto.activity.invite_share_pb2 as invite_share_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.config_pb2 as config_pb
import proto.page.invite_share_pb2 as page_invite_share_pb
import proto.coupons_pb2 as coupons_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.activity_manager import ActivityManager
from business_ops.coupon.coupon_category_manager import CouponCategoryManager
from business_ops.coupon_manager import CouponManager
from common.utils import id_manager
from common.utils import date_utils
from cache.redis_client import RedisClient
from dao.invite_share_da_helper import InviteShareDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import errors
from service import error_codes
from wechat_lib.subscribe_message import SubscribeMessage


logger = logging.getLogger(__name__)


class InviteShareManager(ActivityManager):

    def __init__(self, *args, **kargs):
        super(InviteShareManager, self).__init__(*args, **kargs)

        self.init_config()
        self.init_invite_share(**kargs)
        self.redis_client = RedisClient().get_connection()
        self.invitee_number_weight = 1
        self.cash_reward_amount_weight = 10000

    def init_config(self):
        if not self.merchant:
            return
        config_da = ConfigDataAccessHelper()
        self.activity_config = config_da.get_invite_share_config(merchant_id=self.merchant.id)

    def init_invite_share_by_id(self, id):
        invite_share_da = InviteShareDataAccessHelper()
        share = invite_share_da.get_invite_share(id=id, status=invite_share_pb.InviteShare.NORMAL)
        self.invite_share = share
        if not self.user:
            super(InviteShareManager, self).init_user(user_id=share.user_id)
        if not self.merchant:
            super(InviteShareManager, self).init_merchant(merchant_id=share.merchant_id)

    def init_invite_share(self, **kargs):
        self.invite_share = None
        invite_share = kargs.get("invite_share")
        if invite_share:
            self.invkte_share = invite_share
            return
        id = kargs.get("id")
        if id:
            self.init_invite_share_by_id(id)
            return
        if not self.user:
            return
        if not self.merchant:
            return
        invite_share_da = InviteShareDataAccessHelper()
        self.invite_share = invite_share_da.get_invite_share(
            user_id=self.user.id, merchant_id=self.merchant.id, status=invite_share_pb.InviteShare.NORMAL)

    def create_share(self):
        """ 生成分享,返回id给前端
        """
        if self.invite_share:
            return self.invite_share
        invite_share_da = InviteShareDataAccessHelper()

        share = invite_share_pb.InviteShare()
        share.id = id_manager.generate_common_id()
        share.user_id = self.user.id
        share.merchant_id = self.merchant.id
        share.create_time = int(time.time())
        share.effective_seconds = self.activity_config.effective_seconds
        self._set_invite_share_coupon_category(share)
        invite_share_da.add_or_update_invite_share(share)
        # self.update_inviter_income_rank_list(share)
        return share

    def _set_invite_share_coupon_category(self, share):
        issue_scene = coupon_category_pb.CouponCategory.INVITE_SHARE
        state = coupon_category_pb.CouponCategory.ACTIVE
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_categories = coupon_category_da.get_coupon_categories(
            issue_scene=issue_scene, merchant_id=self.merchant.id, state=state)
        if not coupon_categories:
            raise errors.Error(err=error_codes.SHARE_COUPON_CATEGORY_NOT_EXISTS)
        for coupon_category in coupon_categories:
            coupon_category_manager = CouponCategoryManager(merchant=self.merchant, coupon_category=coupon_category)
            coupon_category_spec = coupon_category_manager._manager.get_coupon_category_spec(coupon_category=coupon_category)
            if coupon_category_spec.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
                share.new_user_coupon_category_id = coupon_category.id
                self._calc_inviter_cash_reward_per_invitee(coupon_category, share)
            else:
                share.old_user_coupon_category_id = coupon_category.id
                self._calc_inviter_cash_reward_per_invitee(coupon_category, share)

    def _calc_inviter_cash_reward_per_invitee(self, coupon_category, share):
        """ 计算邀请者可获得的奖励金额
        1. 默认为固定值
        2. 根据配置的比率和商家的客单价计算得来的金额
        """
        coupon_category_manager = CouponCategoryManager(merchant=self.merchant, coupon_category=coupon_category)
        coupon_category_spec = coupon_category_manager._manager.get_coupon_category_spec()
        avg_cost_per_person = self.store.avg_cost_per_person
        if coupon_category_spec.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
            fee = self.activity_config.cash_reward_per_new_user
            rate = self.activity_config.cash_reward_rate_per_new_user
            if self.activity_config.type == config_pb.InviteShareConfig.FIX:
                share.cash_reward_per_new_user = fee
            else:
                discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_new_user
                share.cash_reward_per_new_user = int(avg_cost_per_person * float(discount / 100) * float(rate / 100))
        else:
            fee = self.activity_config.cash_reward_per_old_user
            rate = self.activity_config.cash_reward_rate_per_old_user
            if self.activity_config.type == config_pb.InviteShareConfig.FIX:
                share.cash_reward_per_old_user = fee
            else:
                discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_old_user
                share.cash_reward_per_old_user = int(avg_cost_per_person * float(discount / 100) * float(rate / 100))

    def is_new_user(self):
        if not self.user:
            return True
        transaction_da = TransactionDataAccessHelper()
        state = wallet_pb.Transaction.SUCCESS
        transactions = transaction_da.get_transactions(
            payee_id=self.merchant.id, payer_id=self.user.id, state=state)
        if len(transactions) == 0:
            return True
        return False

    def get_share(self, id):
        invite_share_da = InviteShareDataAccessHelper()
        coupon_category_da = CouponCategoryDataAccessHelper()
        user_da = UserDataAccessHelper()
        share_vo = page_invite_share_pb.InviteShareVO()
        share = invite_share_da.get_invite_share(id=id)
        self.check_share_validation(share)

        is_new_user = self.is_new_user()
        coupon_category_manager = None
        state = coupon_category_pb.CouponCategory.ACTIVE
        issue_scene = coupon_category_pb.CouponCategory.INVITE_SHARE
        invite_share_coupon_categories = coupon_category_da.get_coupon_categories(
            merchant_id=self.merchant.id, issue_scene=issue_scene, state=state)
        for coupon_category in invite_share_coupon_categories:
            user_type = coupon_category.invite_share_coupon_spec.user_type
            if user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER and is_new_user:
                coupon_category_manager = CouponCategoryManager(coupon_category=coupon_category)
                if share.new_user_coupon_category_id != coupon_category.id:
                    share.new_user_coupon_category_id = coupon_category.id
                    share.cash_reward_per_new_user = coupon_category.invite_share_coupon_spec.reduce_cost
            else:
                if share.old_user_coupon_category_id != coupon_category.id:
                    share.old_user_coupon_category_id = coupon_category.id
                    share.cash_reward_per_old_user = coupon_category.invite_share_coupon_spec.reduce_cost
                coupon_category_manager = CouponCategoryManager(coupon_category=coupon_category)
            invite_share_da.add_or_update_invite_share(share)

        invitor = user_da.get_user(user_id=share.user_id)
        share_vo.id = share.id
        share_vo.inviter_id = share.user_id
        share_vo.inviter_head_image_url = invitor.member_profile.head_image_url
        share_vo.inviter_username = invitor.member_profile.nickname
        coupon_category = coupon_category_manager._manager.coupon_category
        share_vo.reduce_cost = coupon_category.invite_share_coupon_spec.reduce_cost
        deadline_timestamp = coupon_category_manager.get_coupon_category_expire_time()
        share_vo.deadline = self._convert_deadline_to_str(deadline_timestamp)
        return share_vo

    def _convert_deadline_to_str(self, timestamp):
        if timestamp == -1:
            return "永久有效"
        date = datetime.fromtimestamp(timestamp)
        fmt = "%Y-%m-%d %H:%M:%S"
        return date.strftime(fmt)

    def check_share_validation(self, share):
        if not share:
            raise errors.Error(err=error_codes.SHARE_NOT_EXISTS)
        if share.status == invite_share_pb.InviteShare.EXPIRED:
            raise errors.Error(err=error_codes.SHARE_EXPIRED)
        # if share.effective_seconds > 0 and share.create_time + share.effective_seconds < now:
        #     raise errors.Error(err=error_codes.SHARE_EXPIRED)
        if share.status == invite_share_pb.InviteShare.INACTIVE:
            raise errors.Error(err=error_codes.SHARE_INACTIVE)

    def get_activity_config(self):
        config_da = ConfigDataAccessHelper()
        config = config_da.get_invite_share_config(merchant_id=self.merchant.id)
        if not config:
            return
        coupon_category_da = CouponCategoryDataAccessHelper()
        config_vo = page_invite_share_pb.ConfigVO()

        issue_scene = coupon_category_pb.CouponCategory.INVITE_SHARE
        state = coupon_category_pb.CouponCategory.ACTIVE
        coupon_categories = coupon_category_da.get_coupon_categories(
            merchant_id=self.merchant.id, issue_scene=issue_scene, state=state)
        for category in coupon_categories:
            coupon_category_spec = category.invite_share_coupon_spec
            if coupon_category_spec.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
                config_vo.new_user_least_cost = coupon_category_spec.least_cost
                config_vo.new_user_reduce_cost = coupon_category_spec.reduce_cost
            else:
                config_vo.old_user_least_cost = coupon_category_spec.least_cost
                config_vo.old_user_reduce_cost = coupon_category_spec.reduce_cost
        config_vo.cash_reward_per_new_user = config.cash_reward_per_new_user
        config_vo.cash_reward_per_old_user = config.cash_reward_per_old_user
        config_vo.new_user_max_discount = 100 - self.merchant.preferences.coupon_config.invite_share_max_discount_for_new_user
        config_vo.old_user_max_discount = 100 - self.merchant.preferences.coupon_config.invite_share_max_discount_for_old_user
        config_vo.cash_reward_uplimit = config.cash_reward_uplimit
        return config_vo

    def activity_config_init(
            self, cash_reward_uplimit, cash_reward_per_invitee, type, effective_days,
            receive_issue_coupon_seconds, rate=None, invitor_proportion=None,
            new_user_proportion=None, old_user_proportion=None, cash_reward_per_new_user=None,
            cash_reward_per_old_user=None, new_user_max_discount=None, old_user_max_discount=None):
        if self.activity_config is None:
            self.activity_config = config_pb.InviteShareConfig()
            self.activity_config.id = id_manager.generate_common_id()
            self.activity_config.create_time = int(time.time())
        if rate is None:
            rate = 50
        type = config_pb.InviteShareConfig.CashRewardType.Value(type)
        self.activity_config.merchant_id = self.merchant.id

        if type == config_pb.InviteShareConfig.CashRewardType.RATE:
            self.activity_config.cash_reward_per_new_user = self.invitor_red_packet_value_strategy(
                new_user_proportion, user_type=coupon_category_pb.InviteShareCouponCategory.NEW_USER)
            self.activity_config.cash_reward_per_old_user = self.invitor_red_packet_value_strategy(
                old_user_proportion, user_type=coupon_category_pb.InviteShareCouponCategory.OLD_USER)
        elif type == config_pb.InviteShareConfig.CashRewardType.FIX:
            if cash_reward_per_new_user is not None and cash_reward_per_old_user is not None:
                self.activity_config.cash_reward_per_new_user = cash_reward_per_new_user
                self.activity_config.cash_reward_per_old_user = cash_reward_per_old_user

        max_cash_reward = max(self.activity_config.cash_reward_per_new_user, self.activity_config.cash_reward_per_old_user)
        self.activity_config.cash_reward_uplimit = max_cash_reward
        if cash_reward_uplimit is not None:
            self.activity_config.cash_reward_uplimit = cash_reward_uplimit
        self.activity_config.type = type
        self.activity_config.receive_issue_coupon_seconds = receive_issue_coupon_seconds
        self.activity_config.cash_reward_rate_per_invitee = rate
        effective_seconds = effective_days * date_utils.ONE_DAY
        self.activity_config.effective_seconds = effective_seconds
        self.activity_config.update_time = int(time.time())
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_invite_share_config(self.activity_config)

        if new_user_max_discount is not None:
            self.merchant.preferences.coupon_config.invite_share_max_discount_for_new_user = 100 - new_user_max_discount
        if old_user_max_discount is not None:
            self.merchant.preferences.coupon_config.invite_share_max_discount_for_old_user = 100 - old_user_max_discount
        if new_user_max_discount is not None or old_user_max_discount is not None:
            merchant_da = MerchantDataAccessHelper()
            merchant_da.update_or_create_merchant(self.merchant)

    def invitor_red_packet_value_strategy(self, proportion, user_type=None):
        """ 生成邀请人获得的红包金额
        """
        avg_cost_per_person = self.store.avg_cost_per_person
        if user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
            discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_new_user
        else:
            discount = self.merchant.preferences.coupon_config.invite_share_max_discount_for_old_user
        value = int(avg_cost_per_person * float(discount / 100) * float(proportion / 100))
        return value

    def accept(self):
        coupon_category_fee = self.try_to_issue_invite_share_coupon_to_user()
        if self.user.id == self.invite_share.user_id:
            raise errors.Error(err=error_codes.CANNOT_ACCEPT_SELF)
        # self.update_inviter_income_rank_list(score=self.invitee_number_weight * 1)
        user_da = UserDataAccessHelper()
        user = user_da.get_user(self.invite_share.user_id)
        sm = SubscribeMessage()
        sm.invite_share_invite_success_subscribe_message(
            invitor=user, invitee=self.user, merchant_id=self.invite_share.merchant_id)
        return coupon_category_fee

    def try_to_issue_invite_share_coupon_to_user(self):
        if not self.check_user_issue_qualification():
            raise errors.Error(err=error_codes.RECEIVED_RECENTLY)
        if not self.invite_share:
            raise errors.Error(err=error_codes.SHARE_NOT_EXISTS)
        is_new_user = self.is_new_user()
        if is_new_user:
            coupon_category_id = self.invite_share.new_user_coupon_category_id
        else:
            coupon_category_id = self.invite_share.old_user_coupon_category_id
        coupon_manager = CouponManager()
        coupon = coupon_manager.issue_coupon_to_user(
            coupon_category_id, self.user.id, state=coupons_pb.Coupon.ACCEPTED)
        if not coupon:
            raise errors.Error(err=error_codes.SHARE_ISSUE_COUPON_FAIL)
        coupon_category_fee = self.save_invitee_share(coupon, coupon_category_id, is_new_user)
        # self.invite_share.invitee_number += 1
        # invite_share_da.add_or_update_invite_share(self.invite_share)
        return coupon_category_fee

    def cal_invitee_share_status(self, invitee_share):
        now = int(time.time())
        if invitee_share.effective_seconds > 0 and invitee_share.create_time + invitee_share.effective_seconds < now:
            invitee_share.status = invite_share_pb.InviteeShare.EXPIRED
            invite_share_da = InviteShareDataAccessHelper()
            invite_share_da.add_or_update_invitee_share(invitee_share)
        return invitee_share.status

    def save_invitee_share(self, coupon, coupon_category_id, is_new_user):
        """ 用户接受邀请时,保存接受记录
        """
        CouponCategoryFee = namedtuple("CouponCategoryFee", ["reduce_cost", "least_cost"])
        coupon_category_manager = CouponCategoryManager(coupon_category_id=coupon_category_id)
        coupon_category_spec = coupon_category_manager._manager.get_coupon_category_spec()
        invite_share_da = InviteShareDataAccessHelper()
        invitee_share = invite_share_pb.InviteeShare()
        invitee_share.id = id_manager.generate_common_id()
        invitee_share.invite_share_id = self.invite_share.id
        invitee_share.invitee_id = self.user.id
        invitee_share.invitor_id = self.invite_share.user_id
        invitee_share.create_time = int(time.time())
        invitee_share.coupon_id = coupon.id
        invitee_share.coupon_category_value = coupon_category_spec.reduce_cost
        invitee_share.user_type = coupon_category_spec.user_type
        invitee_share.merchant_id = self.merchant.id
        invite_share_da.add_or_update_invitee_share(invitee_share)
        self.update_invitee_share_record_list(invitee_share)
        return CouponCategoryFee(reduce_cost=coupon_category_spec.reduce_cost, least_cost=coupon_category_spec.least_cost)

    def can_receive_coupon(self):
        CanReceiveCoupon = namedtuple("CanReceiveCoupon", ["flag", "reduce_cost", "least_cost"])
        if self.user.id == self.invite_share.user_id:
            raise errors.Error(err=error_codes.CANNOT_ACCEPT_SELF)
        flag = self.check_user_issue_qualification()
        is_new_user = self.is_new_user()
        if is_new_user:
            coupon_category_id = self.invite_share.new_user_coupon_category_id
        else:
            coupon_category_id = self.invite_share.old_user_coupon_category_id
        coupon_category_manager = CouponCategoryManager(coupon_category_id=coupon_category_id)
        coupon_category_spec = coupon_category_manager._manager.get_coupon_category_spec()
        if not flag:
            return CanReceiveCoupon(flag=flag, reduce_cost=coupon_category_spec.reduce_cost, least_cost=coupon_category_spec.least_cost)
        return CanReceiveCoupon(flag=True, reduce_cost=0, least_cost=0)

    def check_user_issue_qualification(self):
        """ 检查用户是否有资格获得一张优惠券
        所有顾客首次参与活动均定义为新用户
        30天内未消费也定义为新用户
        顾客领完优惠券后，再有效期内再点链接会提示已领取
        顾客领完优惠券后，超出有效期内再点链接可再次领取
        邀请人在被邀请人消费30天后再度邀请，仍可赚取赏金
        """
        if self.user.id == self.invite_share.user_id:
            return False
        coupon_da = CouponDataAccessHelper()
        now = int(time.time())
        n_coupons = coupon_da.get_user_coupons(
            user_id=self.user.id, coupon_category_id=self.invite_share.new_user_coupon_category_id)
        o_coupons = coupon_da.get_user_coupons(
            user_id=self.user.id, coupon_category_id=self.invite_share.old_user_coupon_category_id)
        coupons = []
        coupons.extend(n_coupons)
        coupons.extend(o_coupons)
        if not coupons:
            return True
        coupon_manager = CouponManager()
        coupon_category_da = CouponCategoryDataAccessHelper()
        n_coupon_category = coupon_category_da.get_coupon_category(id=self.invite_share.new_user_coupon_category_id)
        o_coupon_category = coupon_category_da.get_coupon_category(id=self.invite_share.old_user_coupon_category_id)
        for coupon in coupons:
            if coupon_manager.check_coupon_can_consume(coupon, n_coupon_category):
                # 如果用户有一张可以使用的邀请券,则不再发放
                return False
            if coupon_manager.check_coupon_can_consume(coupon, o_coupon_category):
                # 如果用户有一张可以使用的邀请券,则不再发放
                return False
            if coupon.state == coupons_pb.Coupon.USED:
                # 如果用户在最近有领取过一张券,且已使用,则不再发放
                use_time = coupon.use_time
                if now - use_time < self.activity_config.receive_issue_coupon_seconds:
                    return False
        return True

    def user_record(self):
        invite_share_da = InviteShareDataAccessHelper()
        status = invite_share_pb.InviteShare.NORMAL
        invite_shares = invite_share_da.get_invite_shares(user_id=self.user.id, status=status)
        user_record_vo = page_invite_share_pb.UserRecordVO()
        for invite_share in invite_shares:
            user_record_vo.cash_reward_amount += invite_share.cash_reward_amount
            user_record_vo.on_the_way_amount = self.income_on_the_way()
            user_record_vo.invitee_number += invite_share.invitee_number
        return user_record_vo

    def stat_invitee_share(self, invite_share, user_record_vo):
        invite_share_da = InviteShareDataAccessHelper()
        invitee_shares = invite_share_da.get_invitee_shares(invite_share_id=invite_share.id)
        for invitee_share in invitee_shares:
            if invitee_share.status == invite_share_pb.InviteeShare.NORMAL:
                user_record_vo.on_the_way_amount += invite_share.cash_reward_per_invitee
            elif invitee_share.status == invite_share_pb.InviteeShare.REFUND_SETTLED:
                user_record_vo.on_the_way_amount += invite_share.cash_reward_per_invitee

    def get_user_income_rank(self, income_rank_vo, user_id=None):
        if user_id is None:
            user_id = self.user.id
            user = self.user
        else:
            user = UserDataAccessHelper().get_user(user_id)
        if not user:
            return
        key_obj = self.get_inviter_income_rank_list_key()
        my_ranking = self.redis_client.zrevrank(key_obj.key, user.id)
        my_score = self.redis_client.zscore(key_obj.key, user.id)
        income_rank_vo.mine.user_id = user.id
        if my_ranking is None or my_score is None:
            income_rank_vo.mine.ranking = -1
            income_rank_vo.mine.head_image_url = user.member_profile.head_image_url
            income_rank_vo.mine.nickname = user.member_profile.nickname
        else:
            income_rank_vo.mine.ranking = my_ranking + 1
            income_rank_vo.mine.head_image_url = user.member_profile.head_image_url
            income_amount = my_score // self.cash_reward_amount_weight
            income_rank_vo.mine.income_amount = int(income_amount)
            income_rank_vo.mine.nickname = user.member_profile.nickname
            logger.info("my_score: {}, {}".format(my_score, income_amount))
            invitee_number = int((my_score - income_amount * self.cash_reward_amount_weight) / self.invitee_number_weight)
            income_rank_vo.mine.invitee_number = invitee_number
            income_rank_vo.mine.income_on_the_way = self.income_on_the_way()

    def income_on_the_way(self):
        invite_share_da = InviteShareDataAccessHelper()
        invitee_shares = invite_share_da.get_invitee_shares(invitor_id=self.user.id)
        total = 0
        for invitee_share in invitee_shares:
            if invitee_share.status != invite_share_pb.InviteeShare.NORMAL:
                continue
            invite_share_id = invitee_share.invite_share_id
            invite_share = invite_share_da.get_invite_share(id=invite_share_id)
            if not invite_share:
                continue
            if invite_share.status != invite_share_pb.InviteShare.NORMAL:
                continue
            if invitee_share.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
                total += invite_share.cash_reward_per_new_user
            else:
                total += invite_share.cash_reward_per_old_user
        return total

    def get_inviter_income_rank_list(self, previous_ranking=0):
        income_rank_vo = page_invite_share_pb.IncomeRankVO()
        key_obj = self.get_inviter_income_rank_list_key()
        if not self.redis_client.exists(key_obj.key_exists):
            self.load_inviter_income_rank_list()
        if not self.redis_client.exists(key_obj.key):
            return income_rank_vo
        end = previous_ranking + 20
        ranking = previous_ranking + 1
        ranks = self.redis_client.zrevrange(key_obj.key, start=previous_ranking, end=end, withscores=True)
        user_da = UserDataAccessHelper()
        for rank in ranks:
            user_id, score = rank
            score = float(score)
            user_id = user_id.decode("utf8")
            user = user_da.get_user(user_id)
            if not user:
                continue
            income_amount = int(score // self.cash_reward_amount_weight)
            invitee_number = score - income_amount * self.cash_reward_amount_weight
            rank_vo = income_rank_vo.rank_list.add()
            rank_vo.ranking = ranking
            rank_vo.head_image_url = user.member_profile.head_image_url
            rank_vo.income_amount = int(income_amount)
            rank_vo.invitee_number = int(invitee_number)
            rank_vo.nickname = user.member_profile.nickname
            rank_vo.user_id = user_id
            ranking += 1
        self.get_user_income_rank(income_rank_vo)
        income_rank_vo.previous_ranking = ranking - 1
        pipeline = self.redis_client.pipeline()
        random_seconds = random.randint(100, 500)
        pipeline.expire(key_obj.key, date_utils.ONE_HOUR + random_seconds)
        pipeline.expire(key_obj.key_exists, date_utils.ONE_HOUR + random_seconds)
        return income_rank_vo

    def load_inviter_income_rank_list(self):
        key_obj = self.get_inviter_income_rank_list_key()
        status = invite_share_pb.InviteShare.NORMAL
        invite_share_da = InviteShareDataAccessHelper()
        if self.merchant:
            invite_shares = invite_share_da.get_invite_shares(status=status, merchant_id=self.merchant.id)
        else:
            invite_shares = invite_share_da.get_invite_shares(status=status)
        pipeline = self.redis_client.pipeline()
        for invite_share in invite_shares:
            user_id = invite_share.user_id
            score = self.cal_invite_share_income_score(invite_share)
            pipeline.zincrby(key_obj.key, score, user_id)
        random_seconds = random.randint(100, 500)
        pipeline.expire(key_obj.key, date_utils.ONE_HOUR + random_seconds)
        pipeline.set(key_obj.key_exists, 1, ex=date_utils.ONE_HOUR + random_seconds)
        pipeline.execute()

    def update_inviter_income_rank_list(self, invite_share=None, score=0):
        key_obj = self.get_inviter_income_rank_list_key()
        if not self.redis_client.exists(key_obj.key) and not self.redis_client.exists(key_obj.key_exists):
            return
        if invite_share is None:
            invite_share = self.invite_share
        score = self.cal_user_inviter_income_rank_list_score(invite_share)
        self.redis_client.zadd(key_obj.key, {invite_share.user_id: score})

    def cal_user_inviter_income_rank_list_score(self, invite_share=None):
        if invite_share is None:
            invite_share = self.invite_share
        if not invite_share:
            return
        invite_share_da = InviteShareDataAccessHelper()
        user_invite_shares = invite_share_da.get_invite_shares(user_id=invite_share.user_id)
        user_cnt, cash_reward_amount = 0, 0
        score = 0
        for user_invite_share in user_invite_shares:
            score += self.cal_invite_share_income_score(user_invite_share)
        score = cash_reward_amount * self.cash_reward_amount_weight + user_cnt * self.invitee_number_weight
        return score

    def cal_invite_share_income_score(self, invite_share=None):
        if invite_share is None:
            invite_share = self.invite_share
        if not invite_share:
            return 0
        cash_reward_amount = invite_share.cash_reward_amount
        user_cnt = invite_share.invitee_number
        score = cash_reward_amount * self.cash_reward_amount_weight + user_cnt * self.invitee_number_weight
        return score

    def get_invitee_record_list(self, previous_ranking=0):
        invitee_record_list_vo = page_invite_share_pb.InviteeRecordListVO()
        key_obj = self.get_invitee_record_list_key()
        if not self.redis_client.exists(key_obj.key_exists):
            self.load_invitee_record_list()
        if not self.redis_client.exists(key_obj.key):
            return invitee_record_list_vo
        end = previous_ranking + 20
        ranking = previous_ranking + 1
        record_list = self.redis_client.zrevrange(key_obj.key, start=previous_ranking, end=end, withscores=True)
        user_da = UserDataAccessHelper()
        invite_share_da = InviteShareDataAccessHelper()
        for record in record_list:
            invitee_id, score = record
            invitee_id = invitee_id.decode("utf8")
            invitee_share = invite_share_da.get_invitee_share(id=invitee_id)
            score = int(score)
            user = user_da.get_user(invitee_share.invitee_id)
            invitee_share_vo = invitee_record_list_vo.invitee_record_list.add()
            invitee_share_vo.head_image_url = user.member_profile.head_image_url
            invitee_share_vo.nickname = user.member_profile.nickname
            invitee_share_vo.ranking = ranking
            consumed = invitee_share.status == invite_share_pb.InviteeShare.SETTLED
            invitee_share_vo.consumed = consumed
            invitee_share_vo.status = self.cal_invitee_share_status(invitee_share=invitee_share)
            if invitee_share.user_type == coupon_category_pb.InviteShareCouponCategory.NEW_USER:
                invitee_share_vo.is_new = True
            else:
                invitee_share_vo.is_new = False
            invitee_share_vo.create_time = invitee_share.create_time
            invitee_share_vo.coupon_fee = invitee_share.coupon_category_value
            invitee_share_vo.invite_share_id = invitee_share.invite_share_id
            ranking += 1
        invitee_record_list_vo.previous_ranking = ranking - 1
        pipeline = self.redis_client.pipeline()
        random_seconds = random.randint(100, 500)
        pipeline.expire(key_obj.key, date_utils.ONE_HOUR + random_seconds)
        pipeline.expire(key_obj.key_exists, date_utils.ONE_HOUR + random_seconds)
        return invitee_record_list_vo

    def load_invitee_record_list(self):
        key_obj = self.get_invitee_record_list_key()
        if self.redis_client.exists(key_obj.key_exists):
            return
        invite_share_da = InviteShareDataAccessHelper()
        pipeline = self.redis_client.pipeline()

        if self.merchant:
            invitee_shares = invite_share_da.get_invitee_shares(invitor_id=self.user.id, merchant_id=self.merchant.id)
        else:
            invitee_shares = invite_share_da.get_invitee_shares(invitor_id=self.user.id)
        user_invitee_shares = {}

        for invitee_share in invitee_shares:
            user_id = invitee_share.invitee_id
            user_invitee_share = user_invitee_shares.get(user_id, {})
            score = user_invitee_share.get("score", 0)
            consumed = user_invitee_share.get("consumed", False)
            if consumed:
                continue
            score = invitee_share.create_time
            if invitee_share.status == invite_share_pb.InviteeShare.NORMAL:
                score *= 10
            consumed = invitee_share.status == invite_share_pb.InviteeShare.SETTLED
            user_invitee_share.update({
                "score": score,
                "consumed": consumed
            })
            user_invitee_shares.update({user_id: user_invitee_share})
            pipeline.zadd(key_obj.key, {invitee_share.id: score})
        random_seconds = random.randint(100, 500)
        pipeline.set(key_obj.key_exists, 1, ex=date_utils.ONE_HOUR + random_seconds)
        pipeline.expire(key_obj.key, date_utils.ONE_HOUR + random_seconds + 10)
        pipeline.execute()

    def update_invitee_share_record_list(self, invitee_share):
        """ 在消费或者退款时更新邀请记录排行榜
        """
        if not invitee_share:
            return
        key_obj = self.get_invitee_record_list_key(user_id=invitee_share.invitor_id)
        if not self.redis_client.exists(key_obj.key) and not self.redis_client.exists(key_obj.key_exists):
            return
        score = invitee_share.create_time
        pipeline = self.redis_client.pipeline()
        if invitee_share.status == invite_share_pb.InviteeShare.NORMAL:
            score *= 10
        pipeline.zadd(key_obj.key, {invitee_share.id: score})
        random_seconds = random.randint(100, 500)
        pipeline.set(key_obj.key_exists, 1, ex=date_utils.ONE_HOUR + random_seconds)
        pipeline.expire(key_obj.key, date_utils.ONE_HOUR + random_seconds + 10)
        pipeline.execute()

    def get_invitee_record_list_key(self, user_id=None):
        if user_id is None:
            user_id = self.user.id
        KeyObj = namedtuple("KeyObj", ["key", "key_exists"])
        if self.merchant:
            key = "invitee_record_list_{}_{}".format(user_id, self.merchant.id)
            key_exists = "invitee_record_list_exists_{}_{}".format(user_id, self.merchant.id)
        else:
            key = "invitee_record_list_{}".format(user_id)
            key_exists = "invitee_record_list_exists_{}".format(user_id)
        return KeyObj(key=key, key_exists=key_exists)

    def get_inviter_income_rank_list_key(self):
        KeyObj = namedtuple("KeyObj", ["key", "key_exists"])
        if self.merchant:
            key = "invite_share_inviter_income_rank_list_{}".format(self.merchant.id)
            key_exists = "invite_share_inviter_income_rank_list_exists_{}".format(self.merchant.id)
        else:
            key = "invite_share_inviter_income_rank_list"
            key_exists = "invite_share_inviter_income_rank_list_exists"
        return KeyObj(key=key, key_exists=key_exists)

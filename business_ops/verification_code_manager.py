# -*- coding: utf-8 -*-


"""
Filename: verification_code_manager.py
Date: 2020-06-08 11:34:44
Title: 核销码相关
"""

import logging
import random
import time

from google.protobuf import json_format

import proto.verification_code_pb2 as verification_pb
import proto.common.datetime_pb2 as datetime_pb
import proto.coupons_pb2 as coupons_pb
from business_ops import coupon_category_manager
from business_ops.coupon_manager import CouponManager
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.verification_code_da_helper import VerificationCodeDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper

logger = logging.getLogger(__name__)


class VerificationCodeManager:
    def __init__(self, merchant_id=None, user_id=None, brand_id=None, merchant=None):
        self.merchant = None
        if merchant:
            self.merchant = merchant
        if (merchant is None) and (merchant_id is not None):
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if self.merchant:
            self.registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id=self.merchant.id)
            # if not self.registration_info.enable_verification_code:
            #     logger.info("商户: {} 不能使用核销码功能".format(self.merchant.id))
            #     return
        if self.merchant:
            self.brand_id = self.merchant.brand_info.id
        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)
        if brand_id is not None:
            self.brand_id = brand_id
        self.split = "-||-"

    def generate_coupon_category(self, verification_code_strategy_id):
        verification_code_da = VerificationCodeDataAccessHelper()
        strategy = verification_code_da.get_verification_code_strategy(id=verification_code_strategy_id)
        if not strategy:
            logger.info("找不到verification_code_strategy: {}".format(verification_code_strategy_id))
            return
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(
            verification_code_strategy_id=verification_code_strategy_id, merchant_id=self.merchant.id)
        if coupon_category:
            logger.info("商户 {} 已经有该核销券,不用再生成".format(self.merchant.basic_info.name))
            return
        coupon_category_manager.create_coupon_category_by_verification_code_strategy(self.merchant, strategy)

    def create_code_verification_strategy(self, name, least_cost, reduce_cost, reduce_rate, day_of_month, daily_time_range):
        """ 生成核销券码策略
        """
        strategy = verification_pb.VerificationCodeStrategy()
        strategy.id = id_manager.generate_common_id()
        strategy.name = name
        strategy.least_cost = least_cost
        strategy.reduce_cost = reduce_cost
        strategy.reduce_rate = reduce_rate
        for day in day_of_month:
            day_of_month_p = strategy.day_of_month.add()
            day_of_month_v = json_format.ParseDict(day, datetime_pb.DayOfMonth(), ignore_unknown_fields=True)
            day_of_month_p.CopyFrom(day_of_month_v)
        for daily in daily_time_range:
            daily_p = strategy.daily_time_range.add()
            daily_v = json_format.ParseDict(daily, datetime_pb.DailyTimeRange(), ignore_unknown_fields=True)
            daily_p.CopyFrom(daily_v)
        verification_code_da = VerificationCodeDataAccessHelper()
        verification_code_da.add_or_update_verification_code_strategy(strategy)

    def generate_verification_code(self, verification_code_strategy_id, nums=100):
        """ 生成核销券码
        """
        verification_code_da = VerificationCodeDataAccessHelper()
        string_base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

        for i in range(nums):
            verification_code_p = verification_pb.VerificationCode()
            verification_code_p.id = id_manager.generate_common_id()
            verification_code_p.brand_id = self.brand_id
            verification_code_p.verification_code_strategy_id = verification_code_strategy_id
            random_index = [random.randint(0, len(string_base) - 1) for i in range(16)]
            show_message = "".join([string_base[index] for index in random_index])
            verification_code_p.show_message = show_message.upper()
            verification_code_da.add_or_update_verification_code(verification_code_p)

    def verify_verification_code(self, msg):
        """ 核销,通过verification_code给用户下发一张coupon
        """
        verification_code_da = VerificationCodeDataAccessHelper()
        verification_codes = verification_code_da.get_verification_codes(msg=msg, brand_id=self.brand_id)
        if not verification_codes or len(verification_codes) == 0:
            logger.info("找不到该券: {}".format(msg))
            return
        verification_code = None
        for v in verification_codes:
            if v.status == verification_pb.VerificationCode.NORMAL:
                verification_code = v
                break
        if not verification_code:
            logger.info("{} 指定的券已被核销".format(msg))
            return

        redis_client = RedisClient().get_connection()
        key = "verification_code_{}".format(verification_code.id)
        if not redis_client.setnx(key, 1):
            logger.info("{} 该券已核销".format(verification_code.id))
            return
        redis_client.expire(key, 30 * 60)  # 设置30分钟后过期

        now = int(time.time())
        verification_code.verification_time = now
        verification_code.user_id = self.user.id
        verification_code.status = verification_pb.VerificationCode.VERIFIED
        verification_code.merchant_id = self.merchant.id
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(verification_code_strategy_id=verification_code.verification_code_strategy_id)
        if not coupon_category:
            logger.info("找不到coupon_category: {}".format(verification_code.verification_code_strategy_id))
            return
        logger.info("用户: {} 核销券: {}".format(self.user.id, verification_code.id))
        CouponManager().issue_coupon_to_user(coupon_category.id, user_id=self.user.id, state=coupons_pb.Coupon.ACCEPTED)
        verification_code_da.add_or_update_verification_code(verification_code)

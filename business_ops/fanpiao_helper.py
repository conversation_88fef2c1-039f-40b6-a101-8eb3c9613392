# -*- coding: utf-8 -*-

from business_ops.transaction_manager import TransactionManager
from business_ops.user_manager import UserManager
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper


class FanpiaoHelper:

    # 不受风控的白名单用户。一般是测试人员
    # TODO: 存入数据库
    white_list = [
        '0fc5af1c-2726-4369-9aa2-891774f69d65',
        'ad7cd36e-f1b7-4d20-83f2-5115b2267376',
        '989713c8-8fcc-4bfa-9757-af53cdc603b7',
        'ebcbec8d-3b98-41f6-a3d5-c28cdf692b30'
    ]

    def set_user_fanpiao_purchase_risk_control(
            self, user=None, user_id=None, merchant=None, merchant_id=None):
        if merchant is None and merchant_id is None:
            return
        if merchant_id is None:
            merchant_id = merchant.id
        user_manager = UserManager()
        if user is None and user_id is None:
            return
        if user is None:
            user = user_manager.get_user(user_id)
        if not user:
            return
        if user.id in self.white_list:
            return
        fanpiao_da = FanpiaoDataAccessHelper()
        config = fanpiao_da.get_fanpiao_risk_control_config()
        if not config:
            return
        level = self.set_user_fanpiao_purchase_risk_control_buy(user, config, merchant_id) or \
            self.set_user_fanpiao_purchase_risk_control_use(user, config, merchant_id)
        if level:
            user_manager.set_user_fanpiao_purchase_risk_control(user, level)
        else:
            user_manager.clear_user_fanpiao_pruchase_risk_control(user)

    def set_user_fanpiao_purchase_risk_control_buy(self, user, config, merchant_id):
        """ 因为买饭票导致被风控
        """
        transaction_manager = TransactionManager()
        buy_count = transaction_manager.count_today_user_buy_fanpiao(user, merchant_id)
        level = self.__cal_risk_level(buy_count, config.buy_fanpiao)
        return level

    def set_user_fanpiao_purchase_risk_control_use(self, user, config, merchant_id):
        """ 因为使用饭票导致被风控
        """
        transaction_manager = TransactionManager()
        use_count = transaction_manager.count_today_user_use_fanpiao(user, merchant_id)
        level = self.__cal_risk_level(use_count, config.use_fanpiao)
        return level

    def __cal_risk_level(self, risk_count, config):
        _level = None
        for count, level in config.items():
            count = int(count)
            if risk_count < count:
                continue
            else:
                _level = level
        return _level

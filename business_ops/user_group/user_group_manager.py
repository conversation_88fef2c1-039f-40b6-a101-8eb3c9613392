"""
UserGroup
id
name
description


UserExp
id
name
description
user_groups: {
    id:
    name:
    description
    fanpiao_strategy: [
    ]
}

fanpiao_meta_categories
totalValue:
discount:

UserGrouping
user_id
user_groups: 顺序为分组的优先级
    {merchant_id: user_group_ids}

UserGroupFanpiaoCategory

"""
from dao.user_da_helper import UserDataAccessHelper


class UserGroupManager(object):

    def __init__(self, user_id):
        self.user_id = user_id
        mongo_client = UserDataAccessHelper().mongo_client
        self.collection = mongo_client['user_db']['user_groups']

    @classmethod
    def pick_up_user_group(cls, user_groups, merchant_id=None):
        """
        数字越大, 优先级最高
        """
        tag = ""
        merchant_groups = user_groups.get(merchant_id, {})
        for _tag, _priority in sorted(merchant_groups.items(), key=lambda x: x[1]):
            tag = _tag
        if tag:
            return tag
        platform_groups = user_groups.get('platform', {})
        for _tag, _priority in sorted(platform_groups.items(), key=lambda x: x[1]):
            tag = _tag
        return tag

    def get_groups_of_user(self):
        user_groups = self.collection.find_one({"userId": self.user_id})
        if user_groups:
            return user_groups


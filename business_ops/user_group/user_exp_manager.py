import logging
import pymongo
import random
import time

import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.page.fanpiao_pb2 as page_fanpiao_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.user_group.user_group_manager import UserGroupManager
from business_ops.app_components_config_manager import AppComponentConfigManager
from cache.redis_client import RedisClient
from dao.user_da_helper import UserDataAccessHelper
logger = logging.getLogger(__name__)


class UserExpManager(object):
    GLOBAL_TAG = 'global'
    # 用于首次购买饭票用户的拉新饭票
    FIRST_TIME_USER_FANPIAO_CATEGORIES = [
        # 面额:100元，折扣: 21 (79折)
        '0675589efc9a413c99dfb44f559bd18b'
    ]
    RECALL_USER_FANPIAO_CATEGORIES = [
        # 面额:100元，折扣: 14 (86折)
        '9392a753702d4984901667099ea7461f',
        # 面额:200元，折扣: 17 (83折)
        'c7b638f6aa114a54b2fd52348aca982a',
        # 面额:300元，折扣: 20 (80折)
        '8f6ffc14cca64b2abd6ff1e96170727c',
        # 面额:500元，折扣: 21 (79折)
        '5f86aea4fefb4ae588c310f0b43897b2'
    ]
    HIGH_FREQ_USER_FANPIAO_CATEGORIES = [
        # 面额:100元，折扣: 7 (93折)
        'b0b58d05c70f4e00bcdddc5b64e412d3',
        # 面额:200元，折扣: 8 (92折)
        '9cdf594e119242518f86c1722f063639',
        # 面额:300元，折扣: 9 (91折)
        'ef6229c51e874ceb937d057bfb2d9fd4',
        # 面额:500元，折扣: 11 (89折)
        '9ea4117561794221a42e0ada0eb797fd'
    ]

    MERCHANT_TAG_FANPIAO_CATEGORY_MAP = {
        # 拉新策略
        # 详见https://doc.weixin.qq.com/sheet/e3_AZsAcAZ2AAwIZGeM2FESmqgCGPz0k?scode=ADsAMAcHAC85hUJLpJAYkAUwZ-AEA
        'acquisition_1': [
            # 面额:100元，折扣: 17 (83折)
            '9c65695c6dec4dcda77ebbeb7d879dc4'
        ],
        'acquisition_2': [
            # 面额:300元，折扣: 22 (78折)
            '491f830389494d8283072e20b7d29885'
        ],
        'acquisition_3': [
            # 面额:100元，折扣: 32 (68折)
            '8944bc8a4cec4e4191c04a9ad204c3a3'
        ],
        'acquisition_4': [
            # 面额:100元，折扣: 15 (85折)
            '3bbd644ed0ad4224a5a10d6c86b4ef61',
            # 面额:200元，折扣: 18 (82折)
            'e99aa8f7539d407e9db410f252759ace',
            # 面额:300元，折扣: 21 (79折)
            '859b892107ef445e9b13e40665141e86',
            # 面额:500元，折扣: 22 (78折)
            '8be2c60bac944d8796154670de996d30'
        ],
        'acquisition_5': [
            # 面额:100元，折扣: 21 (79折)
            '89931fbb6ee441429dc553c50f8aae50',
        ],
        'acquisition_6': [
            # 面额:100元，折扣: 21 (79折)
            '3ca3c10783f14eb59d1be5d06f1bdfad',
            # 面额:200元，折扣: 31 (69折)
            '161d75e73fda4460b4ccbda067499fa8',
            # 面额:300元，折扣: 32 (68折)
            '2bd8c7c87d4a4536803c89ef70cf57d4',
        ],
        'acquisition_7': [
            # 面额:100元，折扣: 32 (68折)
            'f0016cebadba49c8adf67e607e691053',
        ],
        'acquisition_8': [
            # 面额:50元，折扣: 32 (68折)
            '9ee7f668db5443ffb6641e6fc4974124',
        ],
        'acquisition_9': [
            # 面额:50元，折扣: 21 (79折)
            'ec477777460c4fb394666737da964560',
        ],
        'acquisition_10': [
            # 面额:50元，折扣: 5 (95折)
            '9f4813d4faf14fb88b2d78a53377eea7',
        ],
        # 2022.10.27
        'acquisition_11': [
            # 面额:100元，折扣: 11 (89折)
            '40619a7961844193b47791eb785454e7',
            # 面额:200元，折扣: 18 (82折)
            'ab8e73af88b34b358b3b3a9085fb7cc5',
            # 面额:300元，折扣: 19 (81折)
            '722939d4253242a9897ae7b619adb44c',
        ],
        'acquisition_12': [
            # 面额:100元，折扣: 16 (84折)
            '00175a2dbb3b431b8f2b45c5744e85fb',
            # 面额:200元，折扣: 19 (81折)
            '373a9ededc50444888d9050d0526dde4',
            # 面额:300元，折扣: 21 (79折)
            'cbdf07cf0c954533b3257d56f9526a70',
        ],
        'acquisition_13': [
            # 面额:128元，折扣: 11 (89折)
            '4900bd409677420fa91284d35171c15f',
            # 面额:228元，折扣: 14 (86折)
            '03c1e63681af44d3bee39318bd2d5dc9',
            # 面额:328元，折扣: 17 (83折)
            '7d925fdf56774a4ca5e07b425844b77b',
            # 面额:528元，折扣: 18 (82折)
            '5247ffe982604ea3ad929360a400e582',
        ],
        'acquisition_14': [
            # 面额:99元，折扣: 11 (89折)
            '71a60777abba45f684cbbf6479c01880',
            # 面额:199元，折扣: 14 (86折)
            'a3d939306b014b5487e087234a9be5f9',
            # 面额:299元，折扣: 17 (83折)
            '44c72bfb7e044da7bf51c3fff1fbee54',
            # 面额:499元，折扣: 18 (82折)
            'bd98c1e51d1148b5a6151b3ebe28d181',
        ],
        'acquisition_15': [
            # 面额:119元，折扣: 11 (89折)
            '1975016db3ba43e6b29d0b845aa2aa6c',
            # 面额:199元，折扣: 14 (86折)
            '110d4924d8b34d4b9d0e2f064d1cffcf',
            # 面额:299元，折扣: 17 (83折)
            '08bcde9b7a554bb6a4bcc3c910b48184',
            # 面额:499元，折扣: 18 (82折)
            '425bcef694514e4e86da7f5fe15dc42d',
        ],
        'acquisition_16': [
            # 面额:119元，折扣: 21 (79折)
            '2b63dde1849340a2bcaa4eadcad7c58f',
        ],
        'acquisition_17': [
            # 面额:99元，折扣: 21 (79折)
            '7e295dc5645640bd8bbe348c199145e7',
        ],
        'acquisition_18': [
            # 面额:119元，折扣: 25 (75折)
            '5e4783060b674eb1b0b95be5f3334b99',
        ],
        'acquisition_19': [
            # 面额:99元，折扣: 25 (75折)
            '7762eb368274490c9192f11e862b37a4',
        ],
        'acquisition_20': [
            # 面额:119元，折扣: 11 (89折)
            'c71706b9543f45c6b558b2f39975f238',
        ],
        'acquisition_21': [
            # 面额:80元，折扣: 13 (87折)
            '2bdac1479f414206b6ccdfc26cf47c7a',
            # 面额:180元，折扣: 15 (85折)
            'eaf38bd620324560bb3c0bd10826d05f',
            # 面额:280元，折扣: 17 (83折)
            'a2f2e5b01f934d598e1215e5c83d3a3e',
            # 面额:480元，折扣: 18 (82折)
            'da3907854a1b48b19a9a39b1e7c785cc',
        ],
        'acquisition_22': [
            # 面额:88元，折扣: 12 (88折)
            '7bdd2c276fe34672a0acff48bc006a60',
            # 面额:188元，折扣: 15 (85折)
            'f3a858d6366644d1b793d48a6ec2c946',
            # 面额:288元，折扣: 17 (83折)
            'c600fc9338b945db8ff8c73985f8da5a',
            # 面额:388元，折扣: 18 (82折)
            '46af5b016ede49069b8a9376e1a1bdc6',
        ],

        # 2022.11.04
        'acquisition_23': [
            # 面额:100元，折扣: 14 (86折)
            'b0763f21347d47e099986f2541a685d1',
            # 面额:200元，折扣: 17 (83折)
            '30ecea9fee4b41cf94a5af7b63e0cd6a',
            # 面额:300元，折扣: 20 (80折)
            '1f5f6d32d49340f18f364b070e7b127e',
        ],
        'acquisition_24': [
            # 面额:99元，折扣: 14 (86折)
            'a918cbb010204768be4fcf383ba6274f',
            # 面额:199元，折扣: 17 (83折)
            '8183b9645af24dc6a7f5cd49a67f253e',
            # 面额:299元，折扣: 20 (80折)
            'ca54ed755b604aa18616e8cfe375864f',
        ],
        'acquisition_25': [
            # 面额:99元，折扣: 14 (86折)
            '65007818272548ccbea4c2b2d6122c61',
            # 面额:199元，折扣: 17 (83折)
            '0455b92b1a9d467cadfa87fc6942134e',
            # 面额:299元，折扣: 20 (80折)
            'b20a4ba2f94b48d99fa3758c570984e8',
            # 面额:499元，折扣: 21 (79折)
            '73be541a58bd4bcd94b30cc324956370',
        ],
        'acquisition_26': [
            # 面额:100元，折扣: 14 (86折)
            '7c34880958af416ba04b938d41b1a3f9',
            # 面额:188元，折扣: 17 (83折)
            'b331365c99844399acead3f96b0d6e0b',
            # 面额:280元，折扣: 20 (80折)
            '1c367f9de818487cbba5f6a1d6eda704',
            # 面额:480元，折扣: 21 (79折)
            '69bac75570d9467fad8b5eed6d9cce47',
        ],
        'acquisition_27': [
            # 面额:100元，折扣: 8 (92折)
            '9ce34632a0764c74b52ce877938ce895',
            # 面额:200元，折扣: 10 (90折)
            '4cfda74efaa0457196b937e33a249912',
            # 面额:300元，折扣: 11 (89折)
            '7c22505a214b4610b2e9c50c1380b397',
            # 面额:500元，折扣: 13 (87折)
            'b45c37c3e1d04c668fd2b325a6026416',
        ],
        'acquisition_28': [
            # 面额:99元，折扣: 8 (92折)
            '2f50dcf1d5844965aa40a4362a0f40bb',
            # 面额:199元，折扣: 10 (90折)
            '3446e2025c5a4c57954eb738823f8a2d',
            # 面额:299元，折扣: 11 (89折)
            'f3080bc12e344af88415bc66efd1effc',
            # 面额:499元，折扣: 13 (87折)
            '0b96c311a4ef4b28b2d6e92ca68acb49',
        ],
        'acquisition_29': [
            # 面额:99元，折扣: 11 (89折)
            '9cfd138e1d974422a3b3c3d9e14b17ee',
            # 面额:199元，折扣: 14 (86折)
            'c861fea4d5d64ed8a731c0d230b712c5',
            # 面额:299元，折扣: 17 (83折)
            '433a53e82dd54bc395e0fe804b5c849e',
            # 面额:499元，折扣: 18 (82折)
            '9435033f526b4219b40bebb4822a9f4a',
        ],
        # 常态化配置 - 对照组
        'acquisition_30': [
            # 面额:100元，折扣: 11 (89折)
            '1881aa7db8154d9b9451885a1df83bc1',
            # 面额:200元，折扣: 14 (86折)
            '87acbd9898fe4ff6b52524280ee4593d',
            # 面额:300元，折扣: 17 (83折)
            '4d69e1b95994453fba55d7af8e717745',
            # 面额:500元，折扣: 18 (82折)
            '8bcc42c54c8c483c83962e939b4433ba',
        ],
        'acquisition_31': [
            # 面额:100元，折扣: 10 (90折)
            '2d4d7f305e4a4713a001d58b4173d621',
            # 面额:200元，折扣: 11 (89折)
            '1015cbb4f54c40309cf07dbdb186e481',
            # 面额:300元，折扣: 13 (87折)
            'e8fddd49dfa44323a529aa8ae8358471',
            # 面额:500元，折扣: 15 (85折)
            '7a4c3921556948f2a37310b791220184',
        ],
        'acquisition_32': [
            # 面额:99元，折扣: 10 (90折)
            '46030ddbc18941588fdedebf524d1409',
            # 面额:199元，折扣: 13 (87折)
            '947e695192584127b214b49c0d9d020d',
            # 面额:299元，折扣: 16 (84折)
            '9383236dd7df41bb8aa2a9166af7b665',
            # 面额:499元，折扣: 17 (83折)
            'e57c731b84034e5eb43042ce34bc66f5',
        ],
        # 2022.12.21
        'acquisition_33': [
            # 面额:100元，折扣: 11 (89折)
            '162efea81d5d48d4b36eb3d9d4584980',
            # 面额:200元，折扣: 14 (86折)
            '93c35926dec04b68a2db8caa0b84f221',
            # 面额:300元，折扣: 17 (83折)
            'e981222949e746af935fe9a8eb1b44e1',
            # 面额:500元，折扣: 18 (82折)
            'd4e6abf683174f8191a3be7432e3f913',
        ],
        'acquisition_34': [
            # 面额:99元，折扣: 11 (89折)
            '007b68b782a94d3ab4698e1005f65811',
            # 面额:199元，折扣: 14 (86折)
            '7d7378f4eb424697b8c78b2d54f14f5a',
            # 面额:299元，折扣: 17 (83折)
            '67c6184af8c94f0fba63d33cc9c5b30a',
            # 面额:499元，折扣: 18 (82折)
            '61fee07f567b4c61b8b1e0619637845a',
        ],


        # 2022.12.07
        # 历史普通订单在4-6单
        'acquisition_4_5_6_orders': [
            # 2024.09.20
            # 面额:50元，折扣: 10 (90折)
            'eb8da1aeb635495292a0e635056835d3',
            # 面额:100元，折扣: 12 (88折)
            '14b7838b47a24d9d969fd3048a5a7325',
        ],
        # 历史普通订单大于或等于7单
        'acquisition_gte_7_orders': [
            # # 面额:99元，折扣: 17 (83折)
            # '00b490a6726844bb8e5f8015043e5965',
            # 2024.09.20
            # 面额:50元，折扣: 10 (90折)
            'eb8da1aeb635495292a0e635056835d3',
            # 面额:99元，折扣: 14 (86折)
            'ee3bd3096a384bd3a5cd9197399c42b6',
        ],
        # 2022.12.14
        # 历史普通订单在3-6单
        'acquisition_3_4_5_6_orders': [
            # 面额:99元，折扣: 17 (83折)
            '5998c8ef16374de3a8046b7c4ddf43bc', 
        ],

        # 召回策略
        'recall_1': [
            # 面额:200元，折扣: 21 (79折)
            '82b8049bf9a84d6ea0f2d2071e3ced0f'
        ],
        'recall_2': [
            # 面额:100元，折扣: 21 (79折)
            'a0bdfd7ef4be401a9fe853f6914d17a5'
        ],
        # 高频用户策略
        'high_freq_extreme': [
            # 面额:100元，折扣: 3 (97折)
            '642a3c16a6dc4a029f4aafad1254b4fb',
        ],
        # 2022.10.21
        'high_freq_extreme_2': [
            # 面额:100元，折扣: 2 (98折)
            'bc0b1bf7cce54cc4be61141a61ceadd7',
        ],
        'high_freq_aggressive': [
            # 面额:100元，折扣: 5 (95折)
            '63f19ff505ce4cb59a31695ed211d092',
            # 面额:200元，折扣: 6 (94折)
            '0c789f3218b64a75a1b02686370ea2c9',
            # 面额:300元，折扣: 7 (93折)
            'a4e646d5b9b9401d85ba553c04895b11'
        ],
        'high_freq_medium': [
            # 面额:100元，折扣: 7 (93折)
            '582922f3f82b464981893ef3af805268',
            # 面额:200元，折扣: 8 (92折)
            '244ac8d8e7254747b445d33a05705889',
            # 面额:300元，折扣: 9 (91折)
            '852cfcc56f434a25a8064337becb8e5f',
            # 面额:500元，折扣: 11 (89折)
            '21b08ac39ce84441b85ed2caf968e454',
        ],
        # 2022.10.21
        'high_freq_medium_2': [
            # 面额:100元，折扣: 7 (93折)
            '582922f3f82b464981893ef3af805268',
            # 面额:200元，折扣: 8 (92折)
            '244ac8d8e7254747b445d33a05705889',
            # 面额:300元，折扣: 9 (91折)
            '852cfcc56f434a25a8064337becb8e5f',
        ],
        'high_freq_mild': [
            # 面额:100元，折扣: 8 (92折)
            'e33aca35819b4a53ad2d873b510d5a34',
            # 面额:200元，折扣: 10 (90折)
            '3c438537b1194340be49226636448f91',
            # 面额:300元，折扣: 11 (89折)
            '36a81bd880cb4d7d9b059eb1a91419c3',
            # 面额:500元，折扣: 13 (87折)
            '75cc594865a34d0a94d1d3f4fe5efded',
        ],
        # 2022.11.30
        'high_freq_extreme_3': [
            # 面额:100元，折扣: 5 (95折)
            '41ff34877f4e4674a10cb27331be9d9a',
        ],
        # 2022.12.05
        'fanpiao_from_6': [
            # 面额:100元，折扣: 5 (95折)
            'f81a05e3acd640a79c11809fd6753215',
        ],
        # 2023.01.31
        'fanpiao_from_5_to_5': [
            # 面额:100元，折扣: 5 (95折)
            '0c071def72844961809e1dc14367c13e',
        ],
        # 2023.03.09
        'fanpiao_from_4_to_4': [
            # 面额:100元，折扣: 8 (92折)
            '46de271b11f9492bb7ce65fc16cb1af1',
            # 面额:200元，折扣: 11 (89折)
            '38d717894c16429c87a101bf5042a389',
            # 面额:300元，折扣: 12 (88折)
            '88f08646bd6b4feb8232db53ff6b52d5',
            # 面额:500元，折扣: 13 (87折)
            '3bc487ebfc2a43b5b966f0ef2b323c96',
        ],
        # 2023.04.10
        'fanpiao_from_3_to_3': [
            # 面额:100元，折扣: 10 (90折)
            '8819261e3c514ae4a2ef0ecb74436fae',
            # 面额:200元，折扣: 12 (88折)
            'a6424567d35b4f168f60b761a6d758fd',
            # 面额:300元，折扣: 14 (86折)
            '9d8304a4d83d4a8fb5873d3778a1c647',
            # 面额:500元，折扣: 15 (85折)
            '15174fb887d940f8a5c610075a76e0ba',
        ],
        # 2023.05.08
        'fanpiao_from_4_to_4_extreme': [
            # 面额:100元，折扣: 5 (95折)
            'f13c6b47115444afb464ce7c6c2fc286',
        ],
        'fanpiao_from_3_to_3_extreme': [
            # 面额:100元，折扣: 5 (95折)
            '2ad002fdd0284b5180a190749e99f9ff',
            # 面额:200元，折扣: 6 (94折)
            '24f8e4b6f1174de68c54d864a7d8f085',
            # 面额:300元，折扣: 7 (93折)
            '15a5066dd44b433c9c992d9128ed45a7'
        ],
        'fanpiao_from_2_to_2_extreme': [
            # 面额:100元，折扣: 8 (92折)
            'fab7c13a0f504e08bb5c7316d960a077',
            # 面额:200元，折扣: 10 (90折)
            'ef2694752eef4bdfb8df4197a0a9d6fe',
            # 面额:300元，折扣: 12 (88折)
            '5449013470b3487db803792d1e83485a'
        ],
        # 2024.03.27
        # 历史普通订单0单
        'new_user_0_order': [
            # 面额:50元，折扣: 5 (95折)
            'c4c6db3f796c4e40b49288ba33d1209a',
            # 面额:100元，折扣: 8 (92折)
            '8daac570d6a4466caff5df8c3fb48e9c',
            # 面额:200元，折扣: 11 (89折)
            'a91d4b814c01485b899723c470634fab', 
            # 面额:300元，折扣: 14 (86折)
            'dad63995bb52406ca3b5154b8b1d1ddb', 
            # 面额:500元，折扣: 15 (85折)
            'c237d0b6150d4a25876f802e34f106a5', 
        ],
        # 历史普通订单1单
        'new_user_1_order': [
            # # 面额:50元，折扣: 6 (94折)
            # 'c8afc67be51d4070bcb13f5c8952a99e',
            # # 面额:100元，折扣: 9 (91折)
            # 'e7615f6e50d64803a2c44f129022b905',
            # # 面额:200元，折扣: 12 (88折)
            # 'e634b903a21b43ce827f5d98ad872a1b',
            # # 面额:300元，折扣: 15 (85折)
            # '47e90a23de9d40bebc5e553be210f72f',
            # # 面额:500元，折扣: 16 (84折)
            # 'e8115403aa3747c89072ac8a665ccfb3',

            # 2024.09.23
            # 面额:50元，折扣: 7 (93折)
            'ce675afc3ed944cbaae58c79787f7741',
            # 面额:100元，折扣: 10 (90折)
            'f95b083c8dcd4dada191fea7feb19831',
            # 面额:200元，折扣: 13 (87折)
            '0bf85e2e76bb42c59c5281a51047fbd0',
            # 面额:300元，折扣: 16 (84折)
            '6a43acf2d99c43dd897b4fad95e62654',
            # 面额:500元，折扣: 17 (83折)
            'df98355b8ebd46e5b7c91facb1905219',
        ],
        # 历史普通订单2单
        'new_user_2_orders': [
            # 面额:50元，折扣: 7 (93折)
            'ce675afc3ed944cbaae58c79787f7741',
            # 面额:100元，折扣: 10 (90折)
            'f95b083c8dcd4dada191fea7feb19831',
            # 面额:200元，折扣: 13 (87折)
            '0bf85e2e76bb42c59c5281a51047fbd0', 
            # 面额:300元，折扣: 16 (84折)
            '6a43acf2d99c43dd897b4fad95e62654', 
            # 面额:500元，折扣: 17 (83折)
            'df98355b8ebd46e5b7c91facb1905219',
        ],
        'risk_control_user': [
            # 面额:100元，折扣: 5 (95折)
            '41ff34877f4e4674a10cb27331be9d9a',
        ],
        # 一般用户普通复购
        'repurchase_normal': [
            # 面额:100元，折扣: 11 (89折)
            '8a3ec158c6fd48ed9fd0dc0cdec24eab',
            # 面额:200元，折扣: 13 (87折)
            'ff6e10e8722c4b0c89979cd22ac5fe86',
            # 面额:300元，折扣: 15 (85折)
            '74447aaae4954554bd008bf07c5ebf8d',
            # 面额:500元，折扣: 16 (84折)
            'ceeacb3857d849bbb4cfe0db14ab8d6a',
        ],
        # 限制级用户复购
        'repurchase_strict': [
            # 面额:100元，折扣: 10 (90折)
            '706a3b9731494555878592c8926b7a56',
            # 面额:200元，折扣: 12 (88折)
            '35e6a72ab6c94b4ca62266d5cda5adff',
            # 面额:300元，折扣: 14 (86折)
            '598f6facecca489081fca37a7b7e71d9',
        ],
        # 极限用户复购
        'repurchase_extreme': [
            # # 面额:100元，折扣: 8 (92折)
            # 'f670416d3a7644598bd1054f67ddde65',
            # # 面额:200元，折扣: 10 (90折)
            # 'faa16d30a4704ea3becefbfff149a410',
            # # 面额:300元，折扣: 12 (88折)
            # '0b39e1d0ce3a4ad6b791338b92892adf',

            # 2024.11.04
            # 面额:100元，折扣: 5 (95折)
            'f0b8e2c3dcc8404da56ccd0ce1bf61be',
            # 面额:200元，折扣: 6 (94折)
            '841e5fa6545549e184bc29344c542d44',
            # 面额:300元，折扣: 7 (93折)
            '2fd99dfa711a468b87269dcd67551156',
        ],
        # 新用户普通购买
        'new_user_normal': [
            # 面额:50元，折扣: 8 (92折)
            '139108a3d46c4174ad7f41e0f8466e91',
            # 面额:100元，折扣: 11 (89折)
            '0e42df4b94934079af2e2ac645e9328b',
            # 面额:200元，折扣: 13 (87折)
            '3bc77f50d9c141b5a8923fc0c9a18453',
            # 面额:300元，折扣: 15 (85折)
            'dd22b97100fe449190a2eb1b81769d72',
            # 面额:500元，折扣: 16 (84折)
            '7a801fcf6cae47038eb6565090c01f18',
        ],
        # 2024-year-end调控
        'spring_2025': [
            # 面额:100元，折扣: 11 (89折)
            '501492d4d8714e049bfea7a8d8454422',
            # 面额:200元，折扣: 13 (87折)
            '8087709159cd4eaaa294ccfde742d14a',
            # 面额:300元，折扣: 15 (85折)
            '3085bdc52fd24f44a62671b7e81129a0',
        ],
    }

    # 饭票附赠优惠券策略全局开关
    ENABLE_COUPON_POLICY = True

    MERCHANT_TAG_FANPIAO_CATEGORIES = []
    for value in MERCHANT_TAG_FANPIAO_CATEGORY_MAP.values():
        MERCHANT_TAG_FANPIAO_CATEGORIES.extend(value)

    def __init__(self, user_id, merchant):
        self.user_id = user_id
        self.merchant = merchant
        self.mongo_client = UserDataAccessHelper().mongo_client
        self.collection = self.mongo_client['user_db']['user_fanpiao_exp']
        self.transaction_collection = self.mongo_client['transaction_db']['transaction']
        self.fanpiao_collection = self.mongo_client['fanpiao_db']['fanpiaos']
        self.promo_settings = self.get_merchant_promotion_settings(merchant.id)
        self._user_da = None
        self._app_component_config = AppComponentConfigManager().get_app_component_config_or_default(
            self.merchant.promotion_type)

    @property
    def user_group_manager(self):
        return UserGroupManager(user_id=self.user_id)

    @property
    def user_da(self):
        if not self._user_da:
            self._user_da = UserDataAccessHelper()
        return self._user_da

    @property
    def fanpiao_meta_categories(self):
        return self.mongo_client['fanpiao_db']['fanpiao_meta_categories']

    def get_user_exp(self, id):
        user_exp = self.collection.find_one({"id": id})
        return user_exp

    @classmethod
    def generate_fanpiao_name(cls, total_value, ticket_package_name=None):
        if ticket_package_name is None:
            ticket_package_name = "饭票"
        return f"{int(total_value / 100)}元超级{ticket_package_name}"

    def get_user_group(self, tag):
        user_exp = self.collection.find_one({"userGroups.tag": tag})
        if user_exp:
            for user_group in user_exp['userGroups']:
                if user_group['tag'] == tag:
                    return user_group

    def get_fanpiao_category_by_id(self, id=None):
        doc = self.fanpiao_meta_categories.find_one({'id': id})
        if doc:
            ticket_package_name = self._app_component_config.ticket_package_name
            fanpiao_category = fanpiao_pb.FanpiaoCategory()
            fanpiao_category.id = doc['id']
            fanpiao_category.discount = doc['discount']
            fanpiao_category.sell_price = doc['sellPrice']
            fanpiao_category.total_value = doc['totalValue']
            if self.ENABLE_COUPON_POLICY and 'bonusCouponTemplateIds' in doc and not self.is_no_coupon_policy():
                fanpiao_category.bonus_coupon_template_ids.extend(doc['bonusCouponTemplateIds'])
            if id in self.HIGH_FREQ_USER_FANPIAO_CATEGORIES:
                fanpiao_category.name = f"{int(doc['totalValue'] / 100)}元{ticket_package_name}"
            elif id in self.MERCHANT_TAG_FANPIAO_CATEGORIES:
                if id in self.MERCHANT_TAG_FANPIAO_CATEGORY_MAP['acquisition_5'] or \
                    id in self.MERCHANT_TAG_FANPIAO_CATEGORY_MAP['acquisition_6']:
                    fanpiao_category.name = f"{int(doc['totalValue'] / 100)}元新人专享{ticket_package_name}"
                elif doc['discount'] >= 15:
                    fanpiao_category.name = f"{int(doc['totalValue'] / 100)}元限时惊喜{ticket_package_name}"
                else:
                    fanpiao_category.name = f"{int(doc['totalValue'] / 100)}元{ticket_package_name}"
            else:
                fanpiao_category.name = self.generate_fanpiao_name(doc['totalValue'])
            return fanpiao_category

    def get_fanpiao_categories_by_user_old(self, merchant_id):
        fanpiao_categories = list()
        user_groups = self.user_group_manager.get_groups_of_user()
        tag = ""
        if user_groups:
            tag = self.user_group_manager.pick_up_user_group(user_groups.get("userGroups", {}), merchant_id)
        if not tag:  # 新用户
            logger.info(f"Use global tag for new user {self.user_id}: {user_groups}")
            tag = self.GLOBAL_TAG
        user_group = self.get_user_group(tag)
        logger.info(f"Get user_group by Tag: {user_group}")
        start_rand = 300
        for category in self.fanpiao_meta_categories.find({"id": {'$in': user_group['fanpiaoStrategy']}}).sort(
                [("totalValue", 1)]):
            category_vo = page_fanpiao_pb.MerchantFanpiaoCategoryVO()
            category_vo.id = category['id']
            category_vo.name = self.generate_fanpiao_name(category['totalValue'])
            category_vo.discount = category['discount']
            category_vo.merchant_id = merchant_id
            category_vo.sell_price = category['sellPrice']
            category_vo.total_value = category['totalValue']
            start_rand = random.randint(0, start_rand)
            category_vo.selling_quantity = start_rand
            fanpiao_categories.append(category_vo)
        return fanpiao_categories

    def _create_catogory_vo_from_template(self, merchant_id, category):
        category_vo = page_fanpiao_pb.MerchantFanpiaoCategoryVO()
        category_vo.id = category['id']
        category_vo.name = self.generate_fanpiao_name(category['totalValue'])
        category_vo.discount = category['discount']
        category_vo.merchant_id = merchant_id
        category_vo.sell_price = category['sellPrice']
        category_vo.total_value = category['totalValue']
        start_quantity = random.randint(101, 300)
        category_vo.selling_quantity = start_quantity
        if self.ENABLE_COUPON_POLICY and 'bonusCouponReduceFee' in category and 'bonusCouponCount' in category \
            and not self.is_no_coupon_policy():
                category_vo.bonus_coupon_reduce_fee = category['bonusCouponReduceFee']
                category_vo.bonus_coupon_count = category['bonusCouponCount']
        return category_vo

    def _get_merchant_tag_fanpiao_categories(self, merchant_id, tag):
        """返回与相应商家标签对应的饭票营销策略。"""
        customFanpiaoCategoryMap = self.get_value_by_name(self.promo_settings, 'fanpiaoSettings.customCategoryMap', default_value={})
        category_matcher = []
        if tag in customFanpiaoCategoryMap:
            category_matcher = customFanpiaoCategoryMap[tag]
        elif tag in self.MERCHANT_TAG_FANPIAO_CATEGORY_MAP:
            category_matcher = self.MERCHANT_TAG_FANPIAO_CATEGORY_MAP[tag]
        if not category_matcher:
            return []

        fanpiao_categories = []
        matcher = {"id": {'$in': category_matcher}}
        ticket_package_name = self._app_component_config.ticket_package_name
        for category in self.fanpiao_meta_categories.find(matcher).sort(
                [("totalValue", 1)]):
            category_vo = self._create_catogory_vo_from_template(merchant_id=merchant_id,
                        category=category)
            if tag == 'acquisition_5' or tag == 'acquisition_6':
                category_vo.name = f"{int(category['totalValue'] / 100)}元新人专享{ticket_package_name}"
            elif category['discount'] >= 15:
                category_vo.name = f"{int(category['totalValue'] / 100)}元限时惊喜{ticket_package_name}"
            else:
                category_vo.name = f"{int(category['totalValue'] / 100)}元{ticket_package_name}"
            fanpiao_categories.append(category_vo)
        return fanpiao_categories

    def _get_high_freq_user_fanpiao_categories(self, merchant_id):
        """返回高频消费用户对应的饭票营销策略。"""
        fanpiao_categories = []
        matcher = {"id": {'$in': self.HIGH_FREQ_USER_FANPIAO_CATEGORIES}}
        ticket_package_name = self._app_component_config.ticket_package_name
        for category in self.fanpiao_meta_categories.find(matcher).sort(
                [("totalValue", 1)]):
            category_vo = self._create_catogory_vo_from_template(merchant_id=merchant_id,
                        category=category)
            category_vo.name = f"{int(category['totalValue'] / 100)}元{ticket_package_name}"
            fanpiao_categories.append(category_vo)
        return fanpiao_categories

    def _get_new_user_fanpiao_categories(self, merchant_id):
        """返回拉新目标用户对应的饭票营销策略。"""
        fanpiao_categories = []
        matcher = {"id": {'$in': self.FIRST_TIME_USER_FANPIAO_CATEGORIES}}
        for category in self.fanpiao_meta_categories.find(matcher).sort(
                [("totalValue", 1)]):
            category_vo = self._create_catogory_vo_from_template(merchant_id=merchant_id,
                        category=category)
            fanpiao_categories.append(category_vo)
        return fanpiao_categories

    def _get_recall_user_fanpiao_categories(self, merchant_id):
        """返回召回目标用户对应的饭票营销策略。"""
        fanpiao_categories = []
        matcher = {"id": {'$in': self.RECALL_USER_FANPIAO_CATEGORIES}}
        for category in self.fanpiao_meta_categories.find(matcher).sort(
                [("totalValue", 1)]):
            category_vo = self._create_catogory_vo_from_template(merchant_id=merchant_id,
                        category=category)
            fanpiao_categories.append(category_vo)
        return fanpiao_categories

    def _get_platform_new_user_fanpiao_categories(self, merchant_id):
        matcher = {
            'payerId': self.user_id,
            'state': 'SUCCESS'
        }
        platform_transaction_count = self.transaction_collection.find(matcher).count()
        # 平台新用户策略
        if platform_transaction_count == 0:
            keys = [
                # 'promotion_strategy_acquisition_5_counter_20220929',
                # 'promotion_strategy_acquisition_6_counter_20220929',
                # 'promotion_strategy_acquisition_7_counter_20221019',
                # 'promotion_strategy_acquisition_8_counter_20221019',
                # 'promotion_strategy_acquisition_9_counter_20221019',
                # 'promotion_strategy_acquisition_10_counter_20221019',
                # 'promotion_strategy_acquisition_11_counter_20221027',
                # 'promotion_strategy_acquisition_12_counter_20221027',
                # 'promotion_strategy_acquisition_13_counter_20221027',
                # 'promotion_strategy_acquisition_14_counter_20221027',
                # 'promotion_strategy_acquisition_15_counter_20221027',
                # 'promotion_strategy_acquisition_16_counter_20221027',
                # 'promotion_strategy_acquisition_17_counter_20221027',
                # 'promotion_strategy_acquisition_18_counter_20221027',
                # 'promotion_strategy_acquisition_19_counter_20221027',
                # 'promotion_strategy_acquisition_20_counter_20221027',
                # 'promotion_strategy_acquisition_21_counter_20221027',
                # 'promotion_strategy_acquisition_22_counter_20221027',
                # 'promotion_strategy_acquisition_23_counter_20221104',
                # 'promotion_strategy_acquisition_24_counter_20221104',
                # 'promotion_strategy_acquisition_25_counter_20221104',
                # 'promotion_strategy_acquisition_26_counter_20221104',
                # 'promotion_strategy_acquisition_27_counter_20221104',
                # 'promotion_strategy_acquisition_28_counter_20221104',
                # 'promotion_strategy_acquisition_29_counter_20221104',
                # 'promotion_strategy_acquisition_30_counter_20221104',
                # 'promotion_strategy_acquisition_31_counter_20221104',
                # 'promotion_strategy_acquisition_32_counter_20221104',

                # TODO: 计划于2023.03进行实验
                # 'promotion_strategy_acquisition_33_counter_20221221',
                # 'promotion_strategy_acquisition_34_counter_20221221',
            ]
            # tags = [
            #         'acquisition_33', 'acquisition_34',
            #     ]
            # index = random.randint(0, 1)
            # key = keys[index]
            # tag = tags[index]
            # redis_client = RedisClient().get_connection()
            # impression_count = redis_client.incr(key)
            # # 仅限量推送10000次曝光 (5000人次)
            # if impression_count <= 10000:
            #     return self._get_merchant_tag_fanpiao_categories(
            #             merchant_id=merchant_id, tag=tag)
        return []

    def _is_high_freq_user(self, merchant_id):
        try:
            user_platform_setting = self.user_da.get_user_platform_setting(user_id=self.user_id)
            if not user_platform_setting:
                return False
            if not merchant_id in user_platform_setting.fanpiao_tags.high_freq_merchant_map:
                return False
            if not user_platform_setting.fanpiao_tags.high_freq_merchant_map[merchant_id]:
                return False
            return True
        except Exception as e:
            return False

    def _is_recent_new_user(self, merchant_id):
        RECENT_PERIOD_SECONDS = 5184000  # 60 Days
        start_time = int(time.time()) - RECENT_PERIOD_SECONDS
        matcher = {
            'payerId': self.user_id,
            'payeeId': merchant_id,
            'state': 'SUCCESS',
            'payMethod': 'FANPIAO_PAY',
            'paidTime': {'$gt': start_time}
        }
        transaction_count = self.transaction_collection.find(matcher).count()
        if transaction_count < 4:
            return True
        else:
            return False

    def _get_user_fanpiao_categories_for_extreme_merchant(self, merchant_id):
        def __get_normal_order_count():
            # 统计用户在指定商家最近消费的普通订单数
            matcher = {
                'payerId': self.user_id,
                'payeeId': merchant_id,
                'type': 'SELF_DISH_ORDER_PAYMENT',
                'state': 'SUCCESS'
            }
            transactions = self.transaction_collection.find(matcher).sort('paidTime', pymongo.DESCENDING).limit(5)
            transactions = sorted(transactions, key=lambda x: x['paidTime'], reverse=True)
            normal_order_count = 0
            for t in transactions:
                if 'payMethod' in t and t['payMethod'] == 'FANPIAO_PAY':
                    break
                normal_order_count += 1
            return normal_order_count


        matcher = {
            'userId': self.user_id,
            'purchaseMerchantId': merchant_id,
            'status': 'ACTIVE'
        }
        fanpiao_count = self.fanpiao_collection.find(matcher).count()
        # 有饭票购买历史用户
        if fanpiao_count >= 5:
            tag = 'fanpiao_from_6'
            if not self._is_high_freq_user(merchant_id):
                tag = 'repurchase_extreme'
            return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 4:
            tag = 'fanpiao_from_5_to_5'
            if not self._is_high_freq_user(merchant_id):
                tag = 'repurchase_extreme'
            return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 3:
            tag = 'repurchase_normal'
            if not self._is_high_freq_user(merchant_id):
                if self._is_recent_new_user(merchant_id):
                    tag = 'repurchase_strict'
                return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)

            # 若饭票用户最近已转为普通订单下单, 则返回梯度折扣饭票
            normal_order_count = __get_normal_order_count()
            if normal_order_count >= 3:
                return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
            elif normal_order_count >= 2:
                tag = 'fanpiao_from_2_to_2_extreme'
            elif normal_order_count >= 1:
                tag = 'fanpiao_from_3_to_3_extreme'
            else:
                tag = 'fanpiao_from_4_to_4_extreme'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 2:
            tag = 'repurchase_normal'
            if not self._is_high_freq_user(merchant_id):
                if self._is_recent_new_user(merchant_id):
                    tag = 'repurchase_strict'
                return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)

            # 若饭票用户最近已转为普通订单下单, 则返回梯度折扣饭票
            normal_order_count = __get_normal_order_count()
            if normal_order_count >= 3:
                return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
            elif normal_order_count >= 1:
                tag = 'fanpiao_from_2_to_2_extreme'
            else:
                tag = 'fanpiao_from_3_to_3_extreme'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 1:
            tag = 'repurchase_normal'
            if self._is_high_freq_user(merchant_id):
                normal_order_count = __get_normal_order_count()
                if normal_order_count == 0:
                    tag = 'fanpiao_from_2_to_2_extreme'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        # 无饭票购买历史用户
        elif fanpiao_count == 0:
            matcher = {
                'payerId': self.user_id,
                'payeeId': merchant_id,
                'type': 'SELF_DISH_ORDER_PAYMENT',
                'state': 'SUCCESS'
            }
            transaction_count = self.transaction_collection.find(matcher).count()
            if transaction_count <= 1:
                # GRACE_PERIOD_SECONDS = 12960000  # 150 Days
                # grace_begin_time = int(time.time()) - GRACE_PERIOD_SECONDS
                grace_begin_time = **********
                if (self.merchant.activate_timestamp == 0 or self.merchant.activate_timestamp > grace_begin_time):
                    return []
                tags_list = ['new_user_0_order', 'new_user_1_order']
                tag = tags_list[transaction_count]
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
            elif transaction_count <= 3:
                tag = 'new_user_normal'
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
            elif transaction_count >=4 and transaction_count <= 6:
                tag = 'acquisition_4_5_6_orders'
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
            elif transaction_count >= 7:
                tag = 'acquisition_gte_7_orders'
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
        return []
    
    def _get_user_fanpiao_categories(self, merchant_id):
        return self._get_user_fanpiao_categories_for_extreme_merchant(merchant_id=merchant_id)

        # TODO: To be deprecated
        matcher = {
            'userId': self.user_id,
            'purchaseMerchantId': merchant_id,
            'status': 'ACTIVE'
        }
        fanpiao_count = self.fanpiao_collection.find(matcher).count()
        # 有饭票购买历史用户
        if fanpiao_count >= 5:
            tag = 'fanpiao_from_6'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 4:
            tag = 'fanpiao_from_5_to_5'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 3:
            tag = 'fanpiao_from_4_to_4'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)
        elif fanpiao_count == 2:
            tag = 'fanpiao_from_3_to_3'
            return self._get_merchant_tag_fanpiao_categories(
                        merchant_id=merchant_id, tag=tag)

        # 无饭票购买历史用户
        elif fanpiao_count == 0:
            matcher = {
                'payerId': self.user_id,
                'payeeId': merchant_id,
                'type': 'SELF_DISH_ORDER_PAYMENT',
                'state': 'SUCCESS'
            }
            transaction_count = self.transaction_collection.find(matcher).count()
            if transaction_count >=4 and transaction_count <= 6:
                tag = 'acquisition_4_5_6_orders'
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
            elif transaction_count >= 7:
                tag = 'acquisition_gte_7_orders'
                return self._get_merchant_tag_fanpiao_categories(
                            merchant_id=merchant_id, tag=tag)
            elif transaction_count == 0:
                # 判断是否平台新用户, 如是则返回相应策略
                # return self._get_platform_new_user_fanpiao_categories(merchant_id=merchant_id)
                pass
        return []

    def get_merchant_promotion_settings(self, merchant_id):
        DEFAULT_PROMO_SETTINGS = {
            'merchantId': merchant_id,
            'fanpiaoSettings': {
                # 是否关闭买饭票附赠优惠券策略
                'disableCouponPolicy': False,
                # 是否固定饭票折扣方案 (不做千人千面动态推送)
                'isFixedDiscount': False,
                # 饭票最低面额值 (单位: 分)
                'minValue': 0,
                # 饭票最高面额值 (单位: 分)
                'maxValue': 200000,
            }
        }
        
        promo_settings_collection = self.mongo_client['promotion_db']['merchant_settings']
        matcher = {'merchantId': merchant_id}
        merchant_settings = promo_settings_collection.find_one(matcher)
        return merchant_settings if merchant_settings else DEFAULT_PROMO_SETTINGS
    
    def is_fixed_discount(self):
        return self.get_value_by_name(self.promo_settings, 'fanpiaoSettings.isFixedDiscount', default_value=False)
    
    def is_no_coupon_policy(self):
        return self.get_value_by_name(self.promo_settings, 'fanpiaoSettings.disableCouponPolicy', default_value=False)
    
    def get_value_by_name(self, dict_obj, dot_chained_name, default_value=None):
        parts = dot_chained_name.split('.')
        value = dict_obj
        for part in parts:
            if not isinstance(value, dict):
                return default_value
            if not part in value:
                return default_value
            value = value[part]
        return value

    def get_fanpiao_categories_by_user_tags(self, merchant_id):
        if not self.promo_settings:
            return []
        # 固定折扣商家不做动态调整
        if self.is_fixed_discount():
            return []
        # 根据饭票购买历史进行相应策略调整
        fanpiao_categories = self._get_user_fanpiao_categories(merchant_id=merchant_id)
        if fanpiao_categories:
            # 根据商家营销规则进行后续调整
            minValue = self.get_value_by_name(self.promo_settings, 'fanpiaoSettings.minValue', default_value=0)
            maxValue = self.get_value_by_name(self.promo_settings, 'fanpiaoSettings.maxValue', default_value=200000)
            results = []
            for category in fanpiao_categories:
                if category.total_value >= minValue and category.total_value <= maxValue:
                    results.append(category)
            return results

        # 根据用户标签进行相应策略调整
        # user_platform_setting = self.user_da.get_user_platform_setting(user_id=self.user_id)
        # if user_platform_setting:
        #     if merchant_id in user_platform_setting.merchant_tags:
        #         return self._get_merchant_tag_fanpiao_categories(merchant_id=merchant_id,
        #                     tag=user_platform_setting.merchant_tags[merchant_id])
        #     if merchant_id in user_platform_setting.frequency_tags.high_freq_merchants:
        #         return self._get_high_freq_user_fanpiao_categories(merchant_id=merchant_id)
        #     elif user_platform_setting.fanpiao_tags.is_new_user:
        #         return self._get_new_user_fanpiao_categories(merchant_id=merchant_id)
        #     elif merchant_id in user_platform_setting.fanpiao_tags.merchant_recall_map and \
        #                 user_platform_setting.fanpiao_tags.merchant_recall_map[merchant_id]:
        #         return self._get_recall_user_fanpiao_categories(merchant_id=merchant_id)

        # 无标签用户策略调整
        # new_user_categories = self._get_platform_new_user_fanpiao_categories(merchant_id=merchant_id)
        # if new_user_categories:
        #     return new_user_categories
        return []

    def get_fanpiao_categories_for_risk_control_user(self, merchant_id):
        tag = 'risk_control_user'
        return self._get_merchant_tag_fanpiao_categories(merchant_id=merchant_id, tag=tag)

    def post_process_fanpiao_purchase(self, fanpiao):
        if fanpiao.fanpiao_category_id in self.MERCHANT_TAG_FANPIAO_CATEGORIES:
            user_settings = self.user_da.get_user_platform_setting(user_id=fanpiao.user_id)
            if not user_settings:
                return
            if fanpiao.purchase_merchant_id in user_settings.merchant_tags and \
                'high_freq' not in user_settings.merchant_tags[fanpiao.purchase_merchant_id]:
                    # 非高频用户只享受一次特殊营销策略, 转化后需清除标签
                    del user_settings.merchant_tags[fanpiao.purchase_merchant_id]
                    self.user_da.add_or_update_user_platform_setting(user_settings)
        elif fanpiao.fanpiao_category_id in self.FIRST_TIME_USER_FANPIAO_CATEGORIES:
            user_settings = self.user_da.get_user_platform_setting(user_id=fanpiao.user_id)
            if not user_settings:
                return
            user_settings.fanpiao_tags.is_new_user = False
            self.user_da.add_or_update_user_platform_setting(user_settings)
        elif fanpiao.fanpiao_category_id in self.RECALL_USER_FANPIAO_CATEGORIES:
            user_settings = self.user_da.get_user_platform_setting(user_id=fanpiao.user_id)
            if not user_settings:
                return
            user_settings.fanpiao_tags.merchant_recall_map[fanpiao.purchase_merchant_id] = False
            self.user_da.add_or_update_user_platform_setting(user_settings)

# -*- coding: utf-8 -*-

import time

import proto.config_pb2 as config_pb
from dao.config_da_helper import ConfigDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper


class ConfigManager():

    def __init__(self, *args, **kargs):
        merchant = kargs.get("merchant")
        merchant_id = kargs.get("merchant_id")
        self.init_merchant(merchant=merchant, merchant_id=merchant_id)

    def init_merchant(self, merchant=None, merchant_id=None):
        if merchant is not None:
            self.merchant = merchant
        elif merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)

    def update_update_timestamp(self, **kargs):
        dish_catalog_update_timestamp = kargs.get("dish_catalog_update_timestamp", None)
        merchant_info_update_timestamp = kargs.get("merchant_info_update_timestamp", None)
        merchant_coupon_package_update_timestamp = kargs.get("merchant_coupon_package_update_timestamp", None)
        merchant_fanpiao_update_timestamp = kargs.get("merchant_fanpiao_update_timestamp", None)
        config_da = ConfigDataAccessHelper()
        config = config_da.get_update_timestamp(merchant_id=self.merchant.id)
        if not config:
            config = config_pb.UpdateTimestamp()
            config.merchant_id = self.merchant.id
        if dish_catalog_update_timestamp is not None:
            config.dish_catalog_update_timestamp = dish_catalog_update_timestamp
        if merchant_info_update_timestamp is not None:
            config.merchant_info_update_timestamp = merchant_info_update_timestamp
        if merchant_coupon_package_update_timestamp is not None:
            config.merchant_coupon_package_update_timestamp = merchant_coupon_package_update_timestamp
        if merchant_fanpiao_update_timestamp is not None:
            config.merchant_fanpiao_update_timestamp = merchant_fanpiao_update_timestamp
        config_da.add_or_update_update_timestamp(config)

    def get_merchant_activity_config(self):
        config_da = ConfigDataAccessHelper()
        merchant_activity_configs = config_da.get_merchant_activity_configs(merchant_id=self.merchant.id)
        if len(merchant_activity_configs) > 0:
            return merchant_activity_configs[0]
        return None

    def get_merchant_activity_configs(self, merchant_ids):
        config_da = ConfigDataAccessHelper()
        merchant_activity_configs = config_da.get_merchant_activity_configs(merchant_id=merchant_ids)
        return merchant_activity_configs

    def add_or_update_merchant_activity_config(self, activity_ids):
        config_da = ConfigDataAccessHelper()
        activity_configs = config_da.get_activity_configs(list(activity_ids))
        merchant_activity_config = self.get_or_create_merchant_activity_config()
        for activity_config in activity_configs:
            merchant_activity_config.activity_ids.append(activity_config.id)
        config_da.add_or_update_merchant_activity_config(merchant_activity_config)

    def get_or_create_merchant_activity_config(self):
        merchant_activity_config = self.get_merchant_activity_config()
        if merchant_activity_config:
            return merchant_activity_config
        merchant_activity_config = config_pb.MerchantActivityConfig()
        merchant_activity_config.merchant_id = self.merchant.id
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_merchant_activity_config(merchant_activity_config)
        return merchant_activity_config

    def add_or_update_activity_config(self, config):
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_activity_config(config)

    def get_activity_configs(self, activity_ids, no_timestamp_check=False):
        config_da = ConfigDataAccessHelper()
        configs = config_da.get_activity_configs(list(activity_ids))
        ret = []
        now = int(time.time())
        for config in configs:
            if config.disable_activity:
                continue
            if not no_timestamp_check and not (int(config.start_timestamp) < now < int(config.end_timestamp)):
                continue
            ret.append(config)
        return ret

    def get_fanpiao_config(self, merchant_id=None):
        if merchant_id is None:
            merchant_id = self.merchant.id
        config_da = ConfigDataAccessHelper()
        config = config_da.get_fanpiao_config(merchant_id=self.merchant.id)
        return config

# -*- coding: utf-8 -*-
import os
from urllib import parse

import proto.ordering.registration_pb2 as registration_pb
import proto.merchant_rules_pb2 as merchant_pb
from business_ops.base_manager import BaseManager
from business_ops.transaction_manager import TransactionManager
from business_ops.ordering.shilai_ops_manager import ShilaiPosMerchantManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from common import http


class MerchantAssistManager(BaseManager):
    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._merchant_da = None
        self._transaction_manager = None
        self._start_timestamp = kargs.get("start_timestamp")
        self._end_timestamp = kargs.get("end_timestamp")

    @property
    def merchant_da(self):
        if self._merchant_da is not None:
            return self._merchant_da
        self._merchant_da = MerchantDataAccessHelper()
        return self._merchant_da

    @property
    def transaction_manager(self):
        if self._transaction_manager is not None:
            return self._transaction_manager
        self._transaction_manager = TransactionManager()
        return self._transaction_manager

    @property
    def data_center_host(self):
        if self.is_test:
            return os.environ.get("POS_SERVICE_DOMAIN", "https://test.shilai-pos.zhiyi.cn")
        return os.environ.get("POS_SERVICE_DOMAIN", "https://shilai-pos.zhiyi.cn")

    @property
    def membership_deposit_list_url(self):
        host = self.data_center_host
        uri = f"{self.outer_version}/transaction/membership/deposit/list"
        url = parse.urljoin(host, uri)
        return url

    @property
    def revenue_stastics_url(self):
        host = self.data_center_host
        uri = f"{self.outer_version}/data-center/order/revenue-statistics"
        url = parse.urljoin(host, uri)
        return url

    def get_feie_printers(self, merchant=None):
        if merchant is None:
            merchant = self.merchant
        if not merchant:
            return {}
        ordering_da = OrderingServiceDataAccessHelper()
        category_printers = ordering_da.get_dish_category_printer_configs(merchant_id=merchant.id)
        ret = {}
        printer_sns = self.registration_info.printer_config.feie_printer.printer_sns
        for category_printer in category_printers:
            categories = ret.get("categories", {})
            categories.update({category_printer.dish_category_id: list(category_printer.printer_sns)})
            ret.update({"categories": categories})
        ret.update({"sns": list(printer_sns)})
        return ret

    def get_printer_categorys(self, merchant=None):
        if merchant is None:
            merchant = self.merchant
        if not merchant:
            return {}
        ordering_da = OrderingServiceDataAccessHelper()
        category_printers = ordering_da.get_dish_category_printer_configs(merchant_id=merchant.id)
        ret = {'sns': []}
        printer_sns = self.registration_info.printer_config.feie_printer.printer_sns
        for printer_sn in printer_sns:
            item = {'sn': printer_sn, 'categoryIds': []}
            for category_printer in category_printers:
                if printer_sn in category_printer.printer_sns:
                    item['categoryIds'].append(category_printer.dish_category_id)
            ret['sns'].append(item)
        return ret

    def __enable_shilai_member_card_recharge(self):
        flag = self.store.enable_shilai_member_card_recharge
        pos_type = self.registration_info.pos_type
        if pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return flag
        manager = ShilaiPosMerchantManager(merchant_id=self.merchant.id)
        setting = manager.get_store_setting()
        if not setting:
            return flag
        member_config = setting.get("storeMemberAccountRechargeConfig")
        if not member_config:
            return flag
        flag0 = member_config.get('enableMiniProgramRecharge', False)
        flag1 = member_config.get('enablePosRecharge', False)
        flag = flag0 | flag1
        return flag

    def sale_summary(self, merchantIds=[]):
        ret = self.__sale_summary(merchantIds)
        dinner_type = self.registration_info.ordering_config.dinner_type
        dinner_type = registration_pb.OrderingConfig.DinnerType.Name(dinner_type)
        ret.update(
            {
                "dinnerType": dinner_type,
                "logoUrl": self.merchant.basic_info.logo_url,
                "name": self.merchant.basic_info.display_name,
                "enableShilaiMemberCardRecharge": self.__enable_shilai_member_card_recharge(),
                "endTime": self._end_timestamp,
                "startTime": self._start_timestamp,
                "merchantId": self.merchant.id,
                "storeId": self.store.id,
                "type": "SELF_DEFINE",
            }
        )
        return ret

    def __sale_summary(self, merchantIds=[]):
        """商户助手营业数据"""
        membership_deposit = self.transaction_manager.get_membership_deposit_transactions(
            payee_id=self.merchant.id, start_timestamp=self._start_timestamp, end_timestamp=self._end_timestamp
        )
        pos_membership_deposit = self.__get_pos_membership_deposit_list()
        membership_recharge = {
            "count": membership_deposit.count + pos_membership_deposit.get("count", 0),
            "fee": pos_membership_deposit.get("fee", 0),
            "shilaiFee": membership_deposit.shilai_fee + pos_membership_deposit.get("shilaiFee", 0),
        }
        merchants = {}
        if merchantIds:
            for merchant in self.merchant_da.get_merchants_by_ids(merchantIds):
                merchants.update(
                    {
                        merchant.id: {
                            'settlement_rate': merchant.payment_info.settlement_rate,
                            'red_packet_shilai_discount': merchant.preferences.red_packet_config.shilai_extra_discount,
                            'red_packet_discount': merchant.preferences.red_packet_config.red_packet_discount,
                            'shareholders': [
                                {
                                    'merchant_id': m.merchant_id,
                                    'ledger_share_ratio': m.ledger_share_ratio,
                                    'ledger_type': merchant_pb.PaymentInfo.MerchantShareholder.LedgerType.Name(m.ledger_type),
                                    'start_time': m.start_time,
                                    'end_time': m.end_time,
                                }
                                for m in merchant.payment_info.shareholders
                            ],
                        }
                    }
                )
        else:
            merchants.update(
                {
                    self.merchant.id: {
                        'settlement_rate': self.merchant.payment_info.settlement_rate,
                        'red_packet_shilai_discount': self.merchant.preferences.red_packet_config.shilai_extra_discount,
                        'red_packet_discount': self.merchant.preferences.red_packet_config.red_packet_discount,
                        'shareholders': [
                            {
                                'merchant_id': m.merchant_id,
                                'ledger_share_ratio': m.ledger_share_ratio,
                                'ledger_type': merchant_pb.PaymentInfo.MerchantShareholder.LedgerType.Name(m.ledger_type),
                                'start_time': m.start_time,
                                'end_time': m.end_time,
                            }
                            for m in self.merchant.payment_info.shareholders
                        ],
                    }
                }
            )
        params = {
            "startTimestamp": self._start_timestamp,
            "endTimestamp": self._end_timestamp,
            "merchantId": self.merchant.id,
            "merchants": merchants,
            "storeId": self.store.id,
            "settlementRate": self.merchant.payment_info.settlement_rate,
            "membershipRecharge": membership_recharge,
        }
        data = self._fetch_post(self.revenue_stastics_url, json=params)
        return data.get('data', {})

    def __get_pos_membership_deposit_list(self):
        """获取收银机端的储值列表"""
        url = self.membership_deposit_list_url
        params = {
            "startTimestamp": self._start_timestamp,
            "endTimestamp": self._end_timestamp,
            "merchantId": self.merchant.id,
            "storeId": self.store.id,
        }
        data = self._fetch_post(url, json=params)
        return data.get("data", {})

    def _fetch_post(self, url, **kwargs):
        resp = http.post(url, **kwargs)
        data = resp.json()
        # errcode = data.get("errcode", 500)
        # if errcode != 0:
        #     raise http.HttpResponseError(
        #         url=url,
        #         errcode=errcode,
        #         errmsg=data.get('errmsg', ''),
        #         **kwargs
        #     )
        return data

# -*- coding: utf-8 -*-

"""
Filename: dish_coupon_manager.py
Date: 2020-06-15 11:12:16
Title: 菜品券
"""

import random
import time

import logging
from google.protobuf import json_format

import proto.verification_code_pb2 as verification_code_pb
import proto.coupons_pb2 as coupons_pb
import proto.wechat_common_pb2 as wechat_common_pb
import proto.page.coupon_list_pb2 as coupon_list_pb
import proto.page.dish_pb2 as dish_pb
import proto.coupon_category_pb2 as coupon_category_pb
from business_ops import coupon_category_manager
from business_ops.coupon_manager import CouponManager
from business_ops.ordering.dish_manager import DishManager
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.dish_verification_code_da_helper import DishVerificationCodeDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class DishVerificationCodeManager:
    def __init__(self, merchant_id=None, brand_id=None, merchant=None, user_id=None):
        self.merchant = None

        if merchant is not None:
            self.merchant = merchant
        if self.merchant is None and merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
            self.brand_id = self.merchant.brand_info.id

        if self.merchant:
            self.brand_id = self.merchant.brand_info.id

        if brand_id is not None:
            self.brand_id = brand_id

        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

    def create_coupon_category(self, strategy_id):
        coupon_category_da = CouponCategoryDataAccessHelper()
        dish_verification_code_da = DishVerificationCodeDataAccessHelper()

        strategy = dish_verification_code_da.get_dish_verification_code_strategy(self.brand_id, strategy_id)
        if not strategy:
            logger.info("brandId: {} 找不到菜品券策略: {}".format(self.brand_id, strategy_id))
            return
        coupon_category = coupon_category_da.get_coupon_category(merchant_id=self.merchant.id, strategy_id=strategy_id)
        if coupon_category:
            logger.info("商户 {} 已经有该菜品券,不用再生成".format(self.merchant.basic_info.name))
            return
        coupon_category_manager.create_coupon_category_by_dish_verification_code_category(self.merchant, strategy)

    def create_dish_coupon_strategy(self, dish_id, name, rate, date_info):
        strategy = verification_code_pb.DishVerificationCodeStrategy()
        strategy.id = id_manager.generate_common_id()
        strategy.name = name
        strategy.brand_id = self.brand_id
        strategy.dish_id = dish_id
        strategy.rate = rate
        date_info = json_format.ParseDict(date_info, wechat_common_pb.DateInfo(), ignore_unknown_fields=True)
        strategy.date_info.CopyFrom(date_info)
        DishVerificationCodeDataAccessHelper().add_or_update_strategy(strategy)
        return strategy.id

    def generate_dish_verification_codes(self, strategy_id, nums):
        """ 生成核销券码
        """
        dish_verification_code_da = DishVerificationCodeDataAccessHelper()
        string_base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

        for i in range(nums):
            dish_verification_code_p = verification_code_pb.DishVerificationCode()
            dish_verification_code_p.id = id_manager.generate_common_id()
            dish_verification_code_p.brand_id = self.brand_id
            dish_verification_code_p.strategy_id = strategy_id
            dish_verification_code_p.create_time = int(time.time())
            random_index = [random.randint(0, len(string_base) - 1) for i in range(16)]
            show_message = "".join([string_base[index] for index in random_index])
            dish_verification_code_p.msg = show_message.upper()
            dish_verification_code_da.add_or_update_dish_verification_code(dish_verification_code_p)

    def verify(self, msg):
        dish_verification_code_da = DishVerificationCodeDataAccessHelper()
        dish_verification_codes = dish_verification_code_da.get_dish_verification_codes(msg=msg, brand_id=self.brand_id)
        if not dish_verification_codes or len(dish_verification_codes) == 0:
            logger.info("找不到菜品券: {}".format(msg))
            raise errors.CannotFindDishVerificationCode()
        dish_verification_code = None
        for v in dish_verification_codes:
            if v.status == verification_code_pb.DishVerificationCode.NORMAL:
                dish_verification_code = v
                break
        if not dish_verification_code:
            logger.info("{} 指定的券已被核销".format(msg))
            raise errors.DishVerificationCodeVerified()

        redis_client = RedisClient().get_connection()
        key = "verification_code_{}".format(dish_verification_code.id)
        if not redis_client.setnx(key, 1):
            logger.info("{} 该券已核销".format(dish_verification_code.id))
            raise errors.DishVerificationCodeVerified()
        redis_client.expire(key, 30 * 60)  # 设置30分钟后过期

        now = int(time.time())
        dish_verification_code.verification_time = now
        dish_verification_code.user_id = self.user.id
        dish_verification_code.status = verification_code_pb.VerificationCode.VERIFIED
        dish_verification_code.merchant_id = self.merchant.id
        strategy_id = dish_verification_code.strategy_id
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(strategy_id=strategy_id)
        if not coupon_category:
            logger.info("找不到coupon_category: {}".format(dish_verification_code.strategy_id))
            raise errors.CouponCategorySpecNotFoundError()
        logger.info("用户: {} 核销券: {}".format(self.user.id, dish_verification_code.id))
        coupon = CouponManager().issue_coupon_to_user(coupon_category.id, user_id=self.user.id, state=coupons_pb.Coupon.ACCEPTED)
        dish_verification_code.coupon_id = coupon.id
        dish_verification_code_da.add_or_update_dish_verification_code(dish_verification_code)

        coupon_info = coupon_list_pb.OrderCouponList.OrderCouponVO()
        strategy = dish_verification_code_da.get_dish_verification_code_strategy(brand_id=self.brand_id, strategy_id=strategy_id)
        coupon_info.date_info.CopyFrom(coupon_category.dish_verification_code_coupon_spec.base_info.date_info)
        dish = OrderingServiceDataAccessHelper().get_dish(dish_id=strategy.dish_id, merchant_id=coupon_category.merchant_id)
        coupon_info.dish_name = dish.name
        if len(dish.images) > 0:
            coupon_info.dish_image_url = dish.images[0]
        coupon_info.dish_id = dish.id
        coupon_info.date_info.CopyFrom(coupon_category.dish_verification_code_coupon_spec.base_info.date_info)
        dish_vo = self.get_dish_vo(dish, self.user.id)
        return coupon_info, dish_vo

    def get_dish_coupon_info(self, coupon):
        """ 查询菜品券 菜品信息
        """
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
        if coupon_category and coupon_category.issue_scene == coupon_category_pb.CouponCategory.DISH_VERIFICATION_CODE:
            brand_id = self.merchant.brand_info.id
            strategy_id = coupon_category.dish_verification_code_coupon_spec.strategy_id
            dish_coupon_strategy = DishVerificationCodeDataAccessHelper().get_dish_verification_code_strategy(brand_id, strategy_id)
            dish_id = dish_coupon_strategy.dish_id
            dish = OrderingServiceDataAccessHelper().get_dish(merchant_id=self.merchant.id, dish_id=str(dish_id))
            return self.get_dish_vo(dish, coupon.user_id)
        return None

    def get_dish_vo(self, dish, user_id):
        manager = DishManager(merchant=self.merchant)
        manager.get_discount_plan(user_id)
        dish_vo = dish_pb.DishCatalog.Menu.Dish()
        manager._convert_dish_vo(dish, dish_vo)
        return dish_vo

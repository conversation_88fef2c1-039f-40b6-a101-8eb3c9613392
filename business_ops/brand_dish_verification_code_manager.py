# -*- coding: utf-8 -*-

"""
Filename: brand_dish_verification_code_manager.py
Date: 2020-07-02 17:48:33
Title: 品牌菜品券
"""

import logging
import time
import random
from collections import namedtuple

from google.protobuf import json_format

import proto.verification_code_pb2 as verification_code_pb
import proto.wechat_common_pb2 as wechat_common_pb
import proto.coupons_pb2 as coupons_pb
import proto.page.coupon_list_pb2 as coupon_list_pb
import proto.page.dish_pb2 as dish_pb
from business_ops import coupon_category_manager
from business_ops.coupon_manager import CouponManager
from business_ops.ordering.dish_manager import DishManager
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.brand_dish_verification_code_da_helper import BrandDishVerificationCodeDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class BrandDishVerificationCodeManager:
    def __init__(self, merchant_id=None, brand_id=None, user_id=None):
        self.brand_id = brand_id
        self.merchant = None
        if brand_id is not None:
            self.brand_id = brand_id
        if merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

    def get_user_new_member_brand_dish_coupon(self, user_id, merchant_id):
        Info = namedtuple("Info", ["dish_vo", "coupon_info"])
        registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id)
        if not registration_info:
            return None
        strategies = registration_info.new_member_dish_verification_code_strategies
        if not strategies or len(strategies) == 0:
            return None
        verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        codes = verification_code_da.get_brand_dish_verification_codes(
            strategy_ids=list(registration_info.new_member_dish_verification_code_strategies),
            status=verification_code_pb.BrandDishVerificationCode.VERIFIED, user_id=user_id)
        if not codes or len(codes) == 0:
            return None
        code = codes[0]
        strategy = verification_code_da.get_brand_dish_verification_code_strategy(strategy_id=code.strategy_id)
        if not strategy:
            return None
        # dish = OrderingServiceDataAccessHelper().get_dish(merchant_id=merchant_id, dish_brand_id=strategy.dish_brand_id)
        # dish_vo = self.get_dish_vo(dish, user_id)
        coupon = CouponDataAccessHelper().get_coupon_by_id(code.coupon_id)
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(id=coupon.coupon_category_id)
        # coupon_info = self.get_coupon_info(strategy.id, coupon_category)
        coupon_info, dish_vo = self.get_coupon_info(strategy.id, coupon_category)
        coupon_info.id = coupon.id
        coupon_info.reduce_cost = int(dish_vo.price)
        dish_vo.discount_price = dish_vo.price
        # dish_vo.is_verification_dish = True
        return Info(dish_vo=dish_vo, coupon_info=coupon_info)

    def issue_new_member_brand_dish_coupon(self, user_id, merchant_id, brand_id):
        registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id)
        if not registration_info:
            return
        strategies = registration_info.new_member_dish_verification_code_strategies
        if not strategies or len(strategies) == 0:
            return
        verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        codes = verification_code_da.get_brand_dish_verification_codes(strategy_ids=list(strategies), user_id=user_id)
        if len(codes) > 0:
            return
        strategy_id = random.choice(strategies)
        verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        codes = verification_code_da.get_brand_dish_verification_codes(
            strategy_id=strategy_id, brand_id=brand_id,
            status=verification_code_pb.BrandDishVerificationCode.NORMAL, size=30)
        # 如果有多个用户同时成为品牌会员,有可能拿到冲突的code
        # 给用户下发菜品券时,如果成功了就直接退出循环,否则就尝试核销下一个code
        for code in codes:
            try:
                self.verify(code.msg)
                break
            except:
                continue

    def create_brand_dish_coupon_strategy(self, dish_brand_id, name, rate, date_info):
        strategy = verification_code_pb.BrandDishVerificationCodeStrategy()
        strategy.id = id_manager.generate_common_id()
        strategy.name = name
        strategy.brand_id = self.brand_id
        strategy.dish_brand_id = dish_brand_id
        strategy.rate = rate
        date_info = json_format.ParseDict(date_info, wechat_common_pb.DateInfo(), ignore_unknown_fields=True)
        strategy.date_info.CopyFrom(date_info)
        BrandDishVerificationCodeDataAccessHelper().add_or_update_strategy(strategy)
        return strategy.id

    def create_coupon_category(self, strategy_id):
        coupon_category_da = CouponCategoryDataAccessHelper()
        brand_dish_verification_code_da = BrandDishVerificationCodeDataAccessHelper()

        strategy = brand_dish_verification_code_da.get_brand_dish_verification_code_strategy(self.brand_id, strategy_id)
        if not strategy:
            logger.info("brandId: {} 找不到菜品券策略: {}".format(self.brand_id, strategy_id))
            return
        coupon_category = coupon_category_da.get_coupon_category(brand_dish_verification_code_strategy_id=strategy_id, brand_id=strategy.brand_id)
        if coupon_category:
            logger.info("商户 {} 已经有该菜品券,不用再生成".format(self.merchant.basic_info.name))
            return
        coupon_category_manager.create_coupon_category_by_brand_dish_verification_code_category(strategy)

    def generate_brand_dish_verification_codes(self, strategy_id, nums):
        """ 生成核销券码
        """
        brand_dish_verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        string_base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

        for i in range(nums):
            dish_verification_code_p = verification_code_pb.BrandDishVerificationCode()
            dish_verification_code_p.id = id_manager.generate_common_id()
            dish_verification_code_p.brand_id = self.brand_id
            dish_verification_code_p.strategy_id = strategy_id
            dish_verification_code_p.create_time = int(time.time())
            random_index = [random.randint(0, len(string_base) - 1) for i in range(16)]
            show_message = "".join([string_base[index] for index in random_index])
            dish_verification_code_p.msg = show_message.upper()
            brand_dish_verification_code_da.add_or_update_brand_dish_verification_code(dish_verification_code_p)

    def verify(self, msg):
        brand_dish_verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        if self.brand_id:
            brand_dish_verification_codes = brand_dish_verification_code_da.get_brand_dish_verification_codes(
                msg=msg, brand_id=self.brand_id)
        else:
            brand_dish_verification_codes = brand_dish_verification_code_da.get_brand_dish_verification_codes(msg=msg)
        if not brand_dish_verification_codes or len(brand_dish_verification_codes) == 0:
            logger.info("找不到菜品券: {}".format(msg))
            raise errors.CannotFindDishVerificationCode()
        brand_dish_verification_code = None
        for v in brand_dish_verification_codes:
            if v.status == verification_code_pb.BrandDishVerificationCode.NORMAL:
                brand_dish_verification_code = v
                break
        if not brand_dish_verification_code:
            logger.info("{} 指定的券已被核销".format(msg))
            raise errors.DishVerificationCodeVerified()

        redis_client = RedisClient().get_connection()
        key = "brand_dish_verification_code_{}".format(brand_dish_verification_code.id)
        if not redis_client.setnx(key, 1):
            logger.info("{} 该券已核销".format(brand_dish_verification_code.id))
            raise errors.DishVerificationCodeVerified()
        redis_client.expire(key, 30 * 60)  # 设置30分钟后过期

        self.brand_id = brand_dish_verification_code.brand_id

        now = int(time.time())
        brand_dish_verification_code.verification_time = now
        brand_dish_verification_code.user_id = self.user.id
        brand_dish_verification_code.status = verification_code_pb.VerificationCode.VERIFIED
        if self.merchant:
            brand_dish_verification_code.merchant_id = self.merchant.id
        strategy_id = brand_dish_verification_code.strategy_id
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(brand_dish_verification_code_strategy_id=strategy_id)
        if not coupon_category:
            logger.info("找不到coupon_category: {}".format(brand_dish_verification_code.strategy_id))
            raise errors.CouponCategorySpecNotFoundError()
        logger.info("用户: {} 核销券: {}".format(self.user.id, brand_dish_verification_code.id))
        coupon = CouponManager().issue_coupon_to_user(coupon_category.id, user_id=self.user.id, state=coupons_pb.Coupon.ACCEPTED)
        brand_dish_verification_code.coupon_id = coupon.id
        brand_dish_verification_code_da.add_or_update_brand_dish_verification_code(brand_dish_verification_code)

        return self.get_coupon_info(strategy_id, coupon_category)

    def get_coupon_info(self, strategy_id, coupon_category):
        brand_dish_verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        coupon_info = coupon_list_pb.OrderCouponList.OrderCouponVO()
        strategy = brand_dish_verification_code_da.get_brand_dish_verification_code_strategy(
            brand_id=self.brand_id, strategy_id=strategy_id)
        coupon_info.date_info.CopyFrom(coupon_category.brand_dish_verification_code_coupon_spec.base_info.date_info)
        if self.merchant:
            dish = OrderingServiceDataAccessHelper().get_dish(dish_brand_id=strategy.dish_brand_id, merchant_id=self.merchant.id)
            coupon_info.dish_name = dish.name
            if len(dish.images) > 0:
                coupon_info.dish_image_url = dish.images[0]
            coupon_info.dish_id = dish.id
            coupon_info.date_info.CopyFrom(coupon_category.dish_verification_code_coupon_spec.base_info.date_info)
            dish_vo = self.get_dish_vo(dish, self.user.id)
            return coupon_info, dish_vo
        return coupon_info, None

    def get_dish_vo(self, dish, user_id):
        manager = DishManager(merchant=self.merchant)
        manager.get_discount_plan(user_id)
        dish_vo = dish_pb.DishCatalog.Menu.Dish()
        manager._convert_dish_vo(dish, dish_vo)
        return dish_vo

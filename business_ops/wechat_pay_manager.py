# -*- coding: utf-8 -*-

import base64
import collections
import hashlib
import logging
import requests
import xmltodict
import os
from dicttoxml import dicttoxml

from Crypto.Cipher import AES
from google.protobuf import json_format

import proto.wechat_pay_pb2 as wechat_pay_pb
from business_ops import constants
from business_ops.pay_manager import PayManager
from common.config import config
from common.utils import date_utils
from common.utils import id_manager
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import base_responses
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)
# 获取 dicttoxml 库的日志记录器
dicttoxml_logger = logging.getLogger('dicttoxml')

# 设置日志级别为 CRITICAL，这样只有严重错误才会被记录
dicttoxml_logger.setLevel(logging.CRITICAL)

class WechatPayManager(PayManager):
    def get_xml_request_with_sign(self, orig_obj):
        orig_obj['sign'] = self.generate_sign(orig_obj)
        return dicttoxml(orig_obj, attr_type=False, custom_root='xml').decode('utf-8')

    def get_json_request_with_pay_sign(self, orig_obj):
        orig_obj['paySign'] = self.generate_sign(orig_obj)
        return orig_obj

    def generate_sign(self, dict_obj):
        result = ''
        for key in sorted(dict_obj.keys()):
            result = '{}{}={}&'.format(result, key, dict_obj[key])

        result = result + 'key=' + config.WECHAT_MERCHANT_KEY
        return hashlib.md5(result.encode('utf-8')).hexdigest().upper()

    def _send_unified_order(self, unified_order):
        """向微信发送统一下单请求。

        Args:
            unified_order: (WechatUnifiedOrderRequest) 统一下单请求结构体

        Returns:
            (JSON) 微信返回的JSON格式响应信息
        """
        order_json = json_format.MessageToDict(unified_order, preserving_proto_field_name=True)
        xml_req = self.get_xml_request_with_sign(order_json)
        url = 'https://api.mch.weixin.qq.com/pay/unifiedorder'
        resp = requests.post(url=url, data=xml_req.encode("utf-8"), headers={'Content-Type': 'text/xml'})
        resp_xml = resp.content.decode('utf-8')
        return xmltodict.parse(resp_xml)

    def prepay(self, transaction, **kargs):
        """根据用户发起的支付请求，向微信发起下单请求"""
        # 2021-08-19 关掉微信的支付方式,转用天阙渠道
        raise errors.Error(errcode=error_codes.PAY_METHOD_NOT_SUPPORT, errmsg=error_codes.PAY_METHOD_NOT_SUPPORT_MSG)
        notify_url = self.generate_notify_url(transaction, payment_prefix="payment_notification", **kargs)

        user = UserDataAccessHelper().get_user(transaction.payer_id)
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        try:
            if merchant.basic_info.display_name:
                body = "{}消费".format(merchant.basic_info.display_name)
            elif merchant.stores[0].name:
                body = "{}消费".format(merchant.stores[0].name)
            else:
                body = "{}消费".format(transaction.payee_id)
        except Exception:
            body = "{}消费".format(transaction.payee_id)
        result = base_responses.create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
        if transaction.paid_fee == 0:
            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)

        # 微信支付接口要求支付金额大于 0
        if transaction.paid_fee > 0:
            # 创建统一下单请求
            unified_order = wechat_pay_pb.WechatUnifiedOrderSingleRequest()
            # 服务商ID，由微信分配的公众账号ID
            unified_order.appid = config.WECHAT_MINIPROGRAM_APPID  # config.SHILAI_MP_APPID
            # 微信支付分配的商户号
            unified_order.mch_id = config.WECHAT_MERCHANT_ID
            # 微信分配的子商户公众账号ID，如需在支付完成后获取sub_openid则此参数必传
            # unified_order.sub_appid = config.WECHAT_MINIPROGRAM_APPID
            # 微信支付分配的子商户号
            # unified_order.sub_mch_id = self.__get_wechat_mch_id(merchant)
            # 随机字符串，不长于32位。推荐随机数生成算法
            unified_order.nonce_str = id_manager.generate_nonce_str(16)
            # 商品描述交易字段
            unified_order.body = body or merchant.id
            # 商品详细描述，对于使用单品优惠的商户，该字段必须按照规范上传，详见“单品优惠参数说明”
            # 附加数据，在查询API和支付通知中原样返回
            # 商户系统内部订单号，要求32个字符内
            unified_order.out_trade_no = transaction.id
            unified_order.fee_type = 'CNY'
            unified_order.total_fee = transaction.paid_fee
            unified_order.spbill_create_ip = "127.0.0.1"
            unified_order.notify_url = notify_url
            unified_order.trade_type = 'JSAPI'
            # unified_order.limit_pay = ''
            unified_order.openid = user.wechat_profile.openid
            # unified_order.sub_openid = user.wechat_profile.openid
            unified_order.sign_type = 'MD5'

            # 向微信平台发送统一下单请求
            prepay_result = self._send_unified_order(unified_order)
            logger.info("prepay_result: {}".format(prepay_result))
            prepay_id = prepay_result['xml']['prepay_id']
            nonce = prepay_result['xml']['nonce_str']

            signData = {
                'appId': config.WECHAT_MINIPROGRAM_APPID,
                'timeStamp': date_utils.timestamp_second(),
                'nonceStr': nonce,
                'package': 'prepay_id={}'.format(prepay_id),
                'signType': "MD5",
            }

            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
            result['transactionId'] = transaction.id
            result['signData'] = self.get_json_request_with_pay_sign(signData)
            result['prepayId'] = prepay_id
        return result

    def handle_payment_success(self, trade_no):
        """支付成功"""
        pass

    def notification(self, notice_xml):
        """处理微信平台发来的支付结果通知。

        Args:
            notice_xml: (string) 请求主体(XML格式)

        Returns:
            (string) 响应主体(XML格式)
        """
        Callback = collections.namedtuple('Callback', ['resp_xml', 'success', 'wechat_transaction_id', 'transaction_id'])
        notification = xmltodict.parse(notice_xml)
        resp_xml = dicttoxml({'return_code': notification['xml']['return_code']}, attr_type=False, custom_root='xml')

        if notification['xml']['result_code'] == 'SUCCESS':  # 支付成功
            sign = notification['xml']['sign']
            params = notification['xml']
            del params['sign']
            my_sign = self.generate_sign(params)
            if sign == my_sign:
                wechat_transaction_id = notification['xml']['transaction_id']
                transaction_id = notification['xml']['out_trade_no']
                return Callback(resp_xml, True, wechat_transaction_id, transaction_id)
        return Callback(resp_xml, False, '', '')

    def __get_wechat_mch_id(self, merchant, store_id=None):
        """获取支付子商户号"""
        if not merchant:
            return constants.SHILAI_MCH_ID
        wechat_mch_id = merchant.wechat_mch_id
        store = merchant.stores[0]
        if store.wechat_mch_id:
            wechat_mch_id = store.wechat_mch_id
        if wechat_mch_id:
            return wechat_mch_id
        return constants.SHILAI_MCH_ID

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        """发起退款,然后在回调中处理业务逻辑
        关于退款时间的逻辑:
        财付通支付的话,会在第二天手动打款给商户,这个时间如果商户在pos机上发起退款,又会原路退回给用户.
        此处做了逻辑，如果在第二天12点之后还要退前一天的扫码点餐的订单,直接拒绝
        """
        # now = datetime.now()
        # paid_time = int(transaction.paid_time)
        # paid_date = datetime.fromtimestamp(paid_time)
        # paid_date_zero = paid_date.replace(hour=0, minute=0, second=0)
        # day_after_paid_date = paid_date_zero + timedelta(1)
        # day_after_paid_date_12 = day_after_paid_date.replace(hour=12)
        # if now.timestamp() > day_after_paid_date_12.timestamp():
        #     raise errors.Error(err=error_codes.TOO_EARLIER_TRANSACTION_CANNOT_REFUND)
        self.refund(transaction)

    def coupon_package_refund(self, transaction, reason=None):
        self.refund(transaction)

    def fanpiao_refund(self, transaction, reason):
        self.refund(transaction)

    def refund_data_decode(self, data):
        """微信退款回调消息解码: https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_16
        解密步骤如下：
            （1）对加密串A做base64解码，得到加密串B
            （2）对商户key做md5，得到32位小写key* ( key设置路径：微信商户平台(pay.weixin.qq.com)-->账户设置-->API安全-->密钥设置 )
            （3）用key*对加密串B做AES-256-ECB解密（PKCS7Padding）
        """
        # (1)
        req_info = data['req_info'].encode()
        req_info = base64.decodebytes(req_info)

        # (2)
        secret = config.WECHAT_MERCHANT_KEY
        hash = hashlib.md5()
        hash.update(secret.encode())
        key = hash.hexdigest().encode()

        # (3)
        unpad = lambda s: s[0 : -ord(s[-1])]
        cipher = AES.new(key, AES.MODE_ECB)
        decrypt_bytes = cipher.decrypt(req_info)
        decrypt_str = decrypt_bytes.decode()
        decrypt_str = unpad(decrypt_str)

        info = xmltodict.parse(decrypt_str)['root']
        data = dict(data, **info)
        data.pop('req_info')
        return data

    def refund_notification(self, notice_xml):
        RefundCallback = collections.namedtuple("RefundCallback", ["flag", "transaction_id", "refund_transaction_id"])
        notification = xmltodict.parse(notice_xml)
        if notification['xml']['return_code'] == 'SUCCESS':  # 支付成功
            data = self.refund_data_decode(notification["xml"])
            transaction_id = data.get("out_trade_no")
            refund_transaction_id = data.get("out_refund_no")
            return RefundCallback(flag=True, transaction_id=transaction_id, refund_transaction_id=refund_transaction_id)
        return RefundCallback(flag=False, transaction_id=None, refund_transaction_id=None)

    def refund(self, transaction):
        """微信退款,原路返回"""
        refund_transaction_id = id_manager.generate_common_id()
        pay_transaction = self.get_pay_transaction(transaction)

        notify_url = self.generate_refund_notify_url(
            pay_transaction, transaction.id, refund_transaction_id, prefix="wechat_pay/refund"
        )
        params = {
            "appid": config.WECHAT_MINIPROGRAM_APPID,
            "mch_id": config.WECHAT_MERCHANT_ID,
            "nonce_str": id_manager.generate_nonce_str(32),
            "out_trade_no": pay_transaction.id,
            "out_refund_no": refund_transaction_id,
            "total_fee": pay_transaction.paid_fee,
            "refund_fee": transaction.paid_fee,
            "notify_url": notify_url,
        }
        self._send_refund_request(params)

    def _send_refund_request(self, order_json, try_times=3):
        if try_times == 0:
            raise errors.Error(err=error_codes.REFUND_REQUEST_FAILED)
        xml_req = self.get_xml_request_with_sign(order_json)
        url = 'https://api.mch.weixin.qq.com/secapi/pay/refund'
        request_certs = (
            os.environ.get("REFUND_WECHAT_PAY_CLIENT_CERT_PATH"),
            os.environ.get("REFUND_WECHAT_PAY_CLIENT_KEY_PATH"),
        )
        headers = {"Content-Type": 'text/xml'}
        try:
            resp = requests.post(
                url=url, data=xml_req.encode("utf-8"), headers=headers, cert=request_certs, verify=True, timeout=15
            )
            resp_xml = resp.content.decode('utf-8')
            resp_dict = xmltodict.parse(resp_xml)
        except Exception as ex:
            logger.exception("向财付通发起退款申请出错: {}".format(ex))
            self._send_refund_request(order_json, try_times - 1)
        logger.info(resp_dict)
        resp_data = resp_dict.get("xml", {})
        return_code = resp_data.get("return_code")
        result_code = resp_data.get("result_code")
        if not (return_code == "SUCCESS" and result_code == "SUCCESS"):
            logger.info("财付通退款申请失败: {}".format(resp_data.get("err_code_des")))
            raise errors.Error(err=error_codes.REFUND_REQUEST_FAILED, reason=resp_data.get("err_code_des"))
        return resp_dict

    def transfer(self, user, transaction):
        url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers"
        openid = user.wechat_profile.openid
        params = {
            # 商户账号appid
            "mch_appid": config.WECHAT_MINIPROGRAM_APPID,
            # 微信支付分配的商户号
            "mchid": config.SHILAI_SETTLEMENT_ACCOUNT,
            # 随机字符串
            "nonce_str": id_manager.generate_common_id(),
            # 商户订单号
            "partner_trade_no": transaction.id,
            # 用户openid
            "openid": openid,
            # 付款金额
            "amount": transaction.paid_fee,
            # 备注
            "desc": "时来钱包提现",
            "check_name": "NO_CHECK",
        }
        signature = self.generate_sign(params)
        params.update({"sign": signature})
        request_xml = dicttoxml(params, attr_type=False, custom_root="xml").decode("utf-8")
        headers = {"Content-Type": "text/xml"}
        request_certs = (os.environ.get("WECHAT_PAY_CLIENT_CERT_PATH"), os.environ.get("WECHAT_PAY_CLIENT_KEY_PATH"))
        try:
            resp = requests.post(url=url, data=request_xml.encode("utf8"), headers=headers, cert=request_certs)
            resp_xml = resp.content.decode("utf-8")
            parsed_resp_xml = xmltodict.parse(resp_xml)
            resp_dict = parsed_resp_xml.get("xml")
        except Exception as ex:
            logger.exception("微信企业付款出错: {}".format(ex))
            raise errors.Error(err=error_codes.WECHAT_PROMOTION_TRANSFER_ERROR)
        logger.info("微信企业付款接口返回: {}".format(resp_dict))
        if resp_dict.get("return_code") == "SUCCESS" and resp_dict.get("result_code") == "SUCCESS":
            return True
        raise errors.Error(err=error_codes.WECHAT_PROMOTION_TRANSFER_FAILED)

    def fanpiao_balance_withdraw(self, user, transaction):
        return self.withdraw(user, transaction)

    def withdraw(self, user, transaction, appid=None, used_appids=[]):
        openid = user.wechat_profile.openid
        url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers"
        appid = appid or config.WECHAT_MINIPROGRAM_APPID
        request_json = {
            # 商户账号appid
            "mch_appid": appid,
            # 微信支付分配的商户号
            "mchid": config.SHILAI_SETTLEMENT_ACCOUNT,
            # 随机字符串
            "nonce_str": id_manager.generate_nonce_str(16),
            # 商户订单号
            "partner_trade_no": transaction.id,
            # 用户openid
            "openid": openid,
            # 付款金额
            "amount": transaction.paid_fee,
            # 备注
            "desc": "时来钱包提现",
            "check_name": "NO_CHECK",
        }

        signature = self.generate_sign(request_json)
        request_json["sign"] = signature

        request_xml = dicttoxml(request_json, attr_type=False, custom_root="xml").decode("utf-8")
        request_headers = {"Content-Type": "text/xml"}
        request_certs = (os.environ.get("WECHAT_PAY_CLIENT_CERT_PATH"), os.environ.get("WECHAT_PAY_CLIENT_KEY_PATH"))
        try:
            resp = requests.post(
                url=url, data=request_xml.encode("utf-8"), headers=request_headers, verify=True, cert=request_certs, timeout=30
            )
            resp_xml = resp.content.decode("utf-8")
        except UnicodeEncodeError as error:
            logger.error("企业付款到零钱解码出错，{}, 订单ID: {}".format(error, transaction.id))
            resp_xml = resp.content.decode("latin-1")
            raise errors.Error(err=error_codes.WALLET_WITHDRAW_FAILED)
        resp_data = xmltodict.parse(resp_xml).get("xml")
        logger.info("{} {} 微信提现返回: {}".format(user.id, transaction.paid_fee, dict(resp_data)))

        return_code = resp_data.get("return_code")
        result_code = resp_data.get("result_code")

        if return_code == "SUCCESS" and result_code == "SUCCESS":
            logger.info("企业付款到零钱成功，订单ID: {}".format(transaction.id))
            return True
        if resp_data.get('err_code') == 'OPENID_ERROR' and (
            config.WECHAT_MINIPROGRAM_OLD_APPID not in used_appids or config.WECHAT_MINIPROGRAM_APPID not in used_appids
        ):
            logger.info("{} {} 使用旧appid重试".format(user.id, transaction.paid_fee))
            used_appids.append(appid)
            return self.withdraw(
                user,
                transaction,
                config.WECHAT_MINIPROGRAM_OLD_APPID
                if appid == config.WECHAT_MINIPROGRAM_APPID
                else config.WECHAT_MINIPROGRAM_APPID,
                used_appids=used_appids,
            )
        raise errors.Error(err=(90012, resp_data.get('return_msg', '提现失败，请联系商家')))

# -*- coding: utf-8 -*-

import proto.finance.account_pb2 as account_pb
from dao.user_asset_account_da_helper import UserAssetAccountDAHelper
from business_ops.base_manager import BaseManager


class UserAssetAccountManager(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.__da_helper = UserAssetAccountDAHelper()
        self.account = self.__da_helper.get_user_asset_account(owner_id=self.user.id)
        if self.account is None:
            self.account = self.create_user_asset_account()

    def create_user_asset_account(self):
        obj = account_pb.UserAssetAccount()
        obj.owner_id = self.user.id
        return obj

    def incr_balance(self, balance):
        # TODO: Lock
        if balance <= 0:
            return
        self.account.balance += abs(balance)
        self.__add_or_update_user_asset_account()

    def decr_balance(self, balance):
        if balance >= 0:
            return
        self.account.balance -= abs(balance)
        self.__add_or_update_user_asset_account()

    def incr_coin_balance(self, balance):
        if balance <= 0:
            return
        self.account.coin_balance += abs(balance)
        self.__add_or_update_user_asset_account()

    def decr_coin_balance(self, balance):
        if balance >= 0:
            return
        self.account.coin_balance -= abs(balance)
        self.__add_or_update_user_asset_account()

    def __add_or_update_user_asset_account(self):
        self.__da_helper.add_or_update_user_asset_account(self.account)

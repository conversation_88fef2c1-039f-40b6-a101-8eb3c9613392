# -*- coding: utf-8 -*-

"""
Filename: redis_pubsub_manager.py
Date: 2020-07-13 16:33:10
Title: Redis消息发布程序
"""

import json

from google.protobuf import json_format

from common.pubsub_helper import PubsubHelper
from business_ops.pubsub_manager import PubsubManager


class RedisPubsubManager(PubsubManager):

    def __init__(self, *args, **kargs):
        super(RedisPubsubManager, self).__init__(*args, **kargs)

    def get_message_obj(self, path, **kargs):
        func = self.url_pathes.get(path)
        if func:
            return func(**kargs)
        return None

    def publish(self, path, **kargs):
        redis_client = PubsubHelper()
        obj = self.get_message_obj(path, **kargs)
        json_obj = json_format.MessageToDict(obj, including_default_value_fields=True)
        json_obj = json.dumps(json_obj)
        redis_client.publish(path, json_obj)

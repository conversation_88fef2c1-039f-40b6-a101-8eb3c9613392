# -*- coding: utf-8 -*-

import logging
import time

import proto.membership_pb2 as membership_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
from business_ops.transaction_manager import TransactionManager
from business_ops.shilai_pos_member_manager import ShilaiPosMemberManager
from business_ops.pay_manager import PayManager
from common.utils import distribute_lock
from common.utils import id_manager
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service import errors
from service.base_responses import success_responses_obj
from service.base_responses import fail_responses_obj


logger = logging.getLogger(__name__)


class ShilaiMemberCardPayManager(PayManager):
    def __init__(self, *args, **kargs):
        super(ShilaiMemberCardPayManager, self).__init__(*args, **kargs)
        if self.merchant:
            ordering_da = OrderingServiceDataAccessHelper()
            self.registration_info = ordering_da.get_registration_info(merchant_id=self.merchant.id)
            self.store = self.merchant.stores[0]

    def increase_pos_member_card_balance(self, transaction):
        user_da = UserDataAccessHelper()
        user = user_da.get_user(transaction.payer_id)
        manager = ShilaiPosMemberManager(merchant=self.merchant, store=self.store)
        manager.increase_member_card_balance_by_balance(
            balance=transaction.paid_fee, phone=user.member_profile.mobile_phone, user_id=user.id
        )

    def partial_refund(self, transaction=None, refund_transaction=None, **kargs):
        pos_type = self.registration_info.pos_type
        if pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            logger.info("时来收银机模式,在小程序端使用储值余额支付,需要在收银端退款: {refund_transaction.paid_fee}")
            self.increase_pos_member_card_balance(refund_transaction)
        else:
            membership_da = MembershipDataAccessHelper()
            merchant_da = MerchantDataAccessHelper()
            merchant = merchant_da.get_merchant(refund_transaction.payee_id)
            member_card = self.get_member_card(merchant=merchant, user_id=refund_transaction.payer_id)
            member_card.balance += refund_transaction.paid_fee
            member_card.used_amount -= refund_transaction.paid_fee
            membership_da.update_or_create_member_card(member_card)
        logger.info("会员卡余额支付退款成功: {}, {}".format(refund_transaction.payer_id, refund_transaction.bill_fee))
        return True

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        pos_type = self.registration_info.pos_type
        if pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            logger.info("时来收银机模式,在小程序端使用储值余额支付,需要在收银端退款: {transaction.paid_fee}")
            self.increase_pos_member_card_balance(transaction)
        else:
            membership_da = MembershipDataAccessHelper()
            merchant_da = MerchantDataAccessHelper()
            merchant = merchant_da.get_merchant(transaction.payee_id)
            member_card = self.get_member_card(merchant=merchant, user_id=transaction.payer_id)
            member_card.balance += transaction.paid_fee
            member_card.used_amount -= transaction.paid_fee
            membership_da.update_or_create_member_card(member_card)
        logger.info("会员卡余额支付退款成功: {}, {}".format(transaction.payer_id, transaction.paid_fee))

        refund_transaction_id = id_manager.generate_common_id()
        refund_transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            refund_transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                use_coupon_id=transaction.use_coupon_id,
                id=refund_transaction_id,
            )
        return refund_transaction

    def prepay(self, transaction, **kargs):
        order = kargs.get("order")
        key = "{}-shilai-membercard".format(transaction.payer_id)
        with distribute_lock.redislock(key=key, ttl=10000, retry_count=5, retry_delay=200) as lock:
            if not lock:
                logger.info("无法获取到用户会员卡锁 - {}".format(transaction.payer_id))
                raise errors.FanpiaoPayProcessing()
            if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
                manager = ShilaiPosMemberManager(merchant=self.merchant, store=self.store)
                user_da = UserDataAccessHelper()
                user = user_da.get_user(transaction.payer_id)
                manager.decrease_member_card_balance(transaction, user)
            else:
                self.decrease_member_card_balance(transaction, order)
            return success_responses_obj()
        return fail_responses_obj()

    def decrease_member_card_balance(self, transaction, order):
        membership_da = MembershipDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(transaction.payee_id)
        member_card = self.get_member_card(merchant=merchant, user_id=transaction.payer_id)
        if not (member_card.status == membership_pb.MemberCard.NORMAL):
            logger.info("会员卡不合法: {}, {}".format(transaction.payer_id, transaction.payee_id))
            raise errors.MemberCardInvalid()
        if member_card.balance < transaction.paid_fee:
            logger.info("会员卡余额不足: {}, {}, {}".format(order.id, member_card.balance, transaction.paid_fee))
            raise errors.MemberCardNotEnoughBalance()
        logger.info("使用会员卡余额支付: {}".format(member_card))
        member_card.balance -= transaction.paid_fee
        member_card.used_amount += transaction.paid_fee
        membership_da.update_or_create_member_card(member_card)
        logger.info("会员卡余额支付成功: {}, {}, {}".format(order.id, transaction.payer_id, transaction.paid_fee))

    def get_member_card(self, user=None, merchant=None, user_id=None, merchant_id=None, brand_id=None):
        member_card = self.get_member_card_with_balance(user=user, user_id=user_id)
        return member_card

    def pos_order_prepay(self, qrcode_info, **kargs):
        member_card = kargs.get("member_card")
        if not member_card:
            return False
        if not member_card.data:
            return False
        balance = member_card.data.get("balance")
        if balance < qrcode_info.bill_fee:
            return False
        merchant_id = qrcode_info.merchant_id
        bill_fee = qrcode_info.bill_fee
        user_id = qrcode_info.user_id
        transaction_manager = TransactionManager()
        transaction = transaction_manager.shilai_pos_scan_qrcode_pay(
            user_id, merchant_id, bill_fee, bill_fee, pay_method=wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY
        )
        now = int(time.time())
        transaction.paid_time = now
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction_manager.update_transaction(transaction)
        self.prepay(transaction, **kargs)
        qrcode_info.pay_method = fanpiao_pb.FanpiaoScanQrcode.MEMBER_ACCOUNT
        qrcode_info.user_member_card_balance = balance - bill_fee
        return transaction

    def pos_order_refund(self, transaction, qrcode_obj, **kargs):
        if not transaction:
            return False
        if transaction.pay_method != wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            return False
        manager = ShilaiPosMemberManager(merchant=self.merchant, store=self.store)
        user_da = UserDataAccessHelper()
        user = user_da.get_user(transaction.payer_id)
        manager.increase_member_card_balance_by_balance(transaction.bill_fee, user.member_profile.mobile_phone, user.id)
        return True

    def get_member_card_with_balance(self, user=None, user_id=None):
        """获取有余额的会员卡
        用户可能使用支付宝也能使用微信登陆小程序。
        此时只有一个user，当用户需要使用储值支付的时候，需要根据手机号关联储值余额
        """
        membership_da = MembershipDataAccessHelper()
        brand_id = self.merchant.brand_info.id
        user_da = UserDataAccessHelper()
        if user is None and user_id is not None:
            user = user_da.get_user(user_id=user_id)
        if not user:
            return None
        phone = user.member_profile.mobile_phone
        users = []
        if phone != "":
            users = user_da.get_users_by_condition(phone=phone)
        if len(users) == 0:
            users = [user]
        member_cards = []
        member_card = None
        if users:
            for user in users:
                if brand_id != "":
                    member_card = membership_da.get_member_card(user_id=user.id, brand_id=brand_id)
                if member_card is None or member_card.user_id != user.id:
                    member_card = membership_da.get_member_card(user_id=user.id, merchant_id=self.merchant.id)
                if member_card and member_card.balance > 0:
                    member_cards.append(member_card)
        if len(member_cards) == 0:
            return member_card
        elif len(member_cards) == 1:
            return member_cards[0]
        else:
            member_card = member_cards[0]
            for m in member_cards[1:]:
                logger.info(f"{phone} 把 {m.id}: {m.balance} 合并到 {member_card.id}: {member_card.balance} 中")
                member_card.balance += m.balance
                m.balance = 0
                m.used_amount = 0
                m.recharge_amount = 0
                membership_da.update_or_create_member_card(m)
            membership_da.update_or_create_member_card(member_card)
            return member_card
        return None

# -*- coding: utf-8 -*-

import logging

from business_ops.wallet_manager import WalletManager
from business_ops.transaction_manager import TransactionManager
from common.utils import distribute_lock
from common.utils import date_utils
from common.utils import id_manager
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto.group_dining import group_dining_pb2 as group_dining_pb
from proto.group_dining import red_packet_pb2 as red_packet_pb
from service.base_responses import create_responses_obj
from service.base_responses import error_responses
from service import error_codes
from strategy import group_dining_strategy

logger = logging.getLogger(__name__)


class RedPacketManager:
    def open_red_packet_2(self, transaction, red_packet=None):
        red_packet_da = RedPacketDataAccessHelper()
        if red_packet is None:
            red_packet = red_packet_da.get_red_packet(new_transaction_id=transaction.id)
        if not red_packet:
            return
        user_id = transaction.payer_id
        merchant_id = transaction.payee_id
        if red_packet.status == red_packet_pb.RedPacket.CANCELLED:
            logger.info('红包{}已退回'.format(red_packet.id))
            return
        value_assignments = red_packet.value_assignments
        drawn_users = red_packet.drawn_users
        if drawn_users.get(user_id):
            logger.info("红包{}己被领取".format(red_packet.id))
            return
        if value_assignments.get(user_id) is None:
            return
        key = "{}-red-packet".format(red_packet.id)
        with distribute_lock.redislock(key=key, ttl=1000, retry_count=5, retry_delay=200) as lock:
            if not lock:
                logger.info("开红包: 无法获取到锁 - {}".format(red_packet.id))
                return error_responses()

            red_packet.drawn_users.update({user_id: date_utils.timestamp_second()})
            value = red_packet.value_assignments.get(user_id)
            # 给用户钱包加钱
            WalletManager().increase_balance(user_id, 0, value, merchant_id)
            logger.info("领取红包成功 - {}, user_id: {}".format(red_packet.id, user_id))
            transaction_manager = TransactionManager()
            transaction_manager.handle_red_packet_deposit_prepay(user_id, value, merchant_id)
            return red_packet
        return

    def open_red_packet(self, red_packet_id, user_id, merchant_id=None):
        """开红包: 弃用"""
        key = "{}-red-packet".format(red_packet_id)
        with distribute_lock.redislock(key=key, ttl=1000, retry_count=5, retry_delay=200) as lock:
            if not lock:
                logger.info("开红包: 无法获取到锁 - {}".format(red_packet_id))
                return error_responses()
            red_packet_da = RedPacketDataAccessHelper()
            red_packet = red_packet_da.get_red_packet(id=red_packet_id)
            if not red_packet:
                logger.info("红包{}不存在".format(red_packet_id))
                return create_responses_obj(error_codes.RED_PACKET_NOT_EXISTS, error_codes.RED_PACKET_NOT_EXISTS_MSG)
            if red_packet.status == red_packet_pb.RedPacket.CANCELLED:
                logger.info('红包{}已退回'.format(red_packet_id))
                return create_responses_obj(error_codes.RED_PACKET_CANCELLED, error_codes.RED_PACKET_CANCELLED_MSG)
            value_assignments = red_packet.value_assignments
            drawn_users = red_packet.drawn_users
            if drawn_users.get(user_id):
                logger.info("红包{}己被领取".format(red_packet_id))
                return create_responses_obj(error_codes.RED_PACKET_DRAWN, error_codes.RED_PACKET_DRAWN_MSG)

            if value_assignments.get(user_id) is None:
                resp = create_responses_obj(error_codes.UNKNOWN_ERROR, error_codes.UNKNOWN_ERROR_MSG)
                return resp

            red_packet_da.update_red_packet_drawn_users(id=red_packet_id, user_id=user_id, date=date_utils.timestamp_second())
            value = red_packet.value_assignments.get(user_id)
            # 给用户钱包加钱
            WalletManager().increase_balance(user_id, 0, value, transaction.payee_id)
            logger.info("领取红包成功 - {}, user_id: {}".format(red_packet_id, user_id))
            resp = create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
            resp.update({"value": value})

            transaction_manager = TransactionManager()
            if red_packet.transaction_id:
                transaction = TransactionDataAccessHelper().get_transaction_by_id(red_packet.transaction_id)
                transaction_manager.handle_red_packet_deposit_prepay(user_id, value, transaction.payee_id)
            elif merchant_id is not None:
                transaction_manager.handle_red_packet_deposit_prepay(user_id, value, merchant_id)
            return resp
        return error_responses()

    def open_dining_red_packet(self, user_id, red_packet_id, dining_id):
        """开饭局红包"""
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        if not dining:
            resp = create_responses_obj(error_codes.DINING_NOT_EXISTS, error_codes.DINING_NOT_EXISTS_MSG)
            return resp

        invitation_da = InvitationDataAccessHelper()
        invitation = invitation_da.get_invitation(
            user_id=user_id, dining_id=dining.id, state=group_dining_pb.Invitation.ACCEPTED
        )
        if not invitation:
            logger.info("未被邀请,无法领取 - 饭局: {}, user_id: {}".format(dining.id, user_id))
            resp = create_responses_obj(error_codes.NOT_BEEN_INVITED, error_codes.NOT_BEEN_INVITED_MSG)
            return resp

        if dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            transaction_id = invitation.transaction_id
            transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
            if user_id != dining.director_id:
                # 不是买单人
                if (not transaction) or (not (transaction.state == wallet_pb.Transaction.SUCCESS)):
                    logger.info("未AA转帐,无法领取红包 - 饭局: {}, user_id: {}".format(dining.id, user_id))
                    resp = create_responses_obj(error_codes.NOT_PAID_TO_INITIATOR, error_codes.NOT_PAID_TO_INITIATOR_MSG)
                    return resp
        resp = self.open_red_packet(red_packet_id, user_id)
        if resp.get('code') == 0:
            invitation_da.update(dining_id=dining.id, user_id=user_id, monetary_state=group_dining_pb.Invitation.COMPLETED)
        return resp

    def can_open_red_packet(self, user_id, dining):
        """查询饭局某个用户能不能打开红包
            1. 饭局未结帐不能开
            2. 己开过了则不能再开了
            3. AA局未支付到付款人则不能开
        Args:
            user_id: 用户ID
            dining: 饭局
        Return:
            True/False
        """
        if not dining:
            return False
        red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=dining.id)
        if not red_packet:
            # 查看红包有没有生成
            return False
        # 如果状态字段不是NORMAL则不能开红包
        # 饭局红包是所有人共享整个红包的状态.所以不能以status来做判断
        # if red_packet.status != red_packet_pb.RedPacket.NORMAL:
        #     return False
        if dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED:
            # 饭局未付款
            return False
        if not dining.transaction_id:
            # 饭局的transaction不存在
            return False
        dining_transaction = TransactionDataAccessHelper().get_transaction_by_id(dining.transaction_id)
        if not dining_transaction:
            # 饭局的transaction不存在
            return False
        if not (dining_transaction.state == wallet_pb.Transaction.SUCCESS):
            # 饭局的transaction不是成功
            return False
        invitation = InvitationDataAccessHelper().get_invitation(
            user_id=user_id, dining_id=dining.id, state=group_dining_pb.Invitation.ACCEPTED
        )
        if not invitation:
            # 不在被邀请人之中
            return False
        if dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            # 如果是AA局,需要检查被邀请人是否有AA付款
            if not invitation.transaction_id:
                return False
            invitation_transaction = TransactionDataAccessHelper().get_transaction_by_id(invitation.transaction_id)
            if not invitation_transaction:
                return False
            if not (invitation_transaction.state == wallet_pb.Transaction.SUCCESS):
                return False
        drawn_users = red_packet.drawn_users
        if drawn_users.get(user_id):
            # 查看这个用户有没有领红包
            return False
        return True

    def generate_red_packet(
        self, user_ids, total_value, issue_scene, dining=None, transaction_id=None, order_id=None, status=None
    ):
        """生成红包池
            一共有两个场景生成红包:
            1. 饭局买单后
            2. 个人使用拉新券买单
        Args:
            user_ids: (字符串列表)对应于red_packet_pb.RedPacket的value_assignments的key
            total_value: (int)红包总金额
            issue_scene: (red_packet_pb.RedPacket.IssueScene)生成红包的场景
            dining: (GroupDiningEvent)如果是饭局的红包,则不为None.
            transaction_id: (string)流水id
        Return:
            None
        """
        red_packet_pool = group_dining_strategy.assign_red_packet_values(total_value, len(user_ids))
        red_packet = red_packet_pb.RedPacket()
        red_packet.id = id_manager.generate_red_packet_id()
        red_packet.create_time = date_utils.timestamp_second()
        red_packet.total_value = total_value
        red_packet.dining_event_id = dining.id if dining else ""
        # red_packet.status = red_packet_pb.RedPacket.BEFORE_NORMAL

        # TODO: transaction_id统一放到transcation_id字段中.
        if issue_scene == red_packet_pb.RedPacket.NEW_MEMBER:
            red_packet.new_member_transaction_id = transaction_id
        else:
            red_packet.new_member_transaction_id = ""

        if transaction_id is not None:
            red_packet.transaction_id = transaction_id
        if order_id:
            red_packet.order_id = order_id
        red_packet.issue_scene = issue_scene
        value_assignments = {}
        for index, value in enumerate(red_packet_pool):
            value_assignments.update({user_ids[index]: value})
        if status is not None:
            red_packet.status = status
        red_packet.value_assignments.update(value_assignments)
        if len(user_ids) > 0:
            red_packet.user_id = user_ids[0]
        return red_packet

# -*- coding: utf-8 -*-

import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
from business_ops.business_manager import BusinessManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.ordering.order_manager import OrderManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
from service import error_codes
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

logger = logging.getLogger(__name__)


class SelfDiningDiscountPaymentManager(BusinessManager):
    def __init__(self, transaction_id=None, user_id=None, merchant_id=None, order_id=None,
                 no_discount_bill_fee=0, bill_fee=None, paid_fee=None, pay_method=None, return_url=None,
                 coupon_id=None, discount_fee=None, is_zero_paid=None, fanpiao_id=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.return_url = return_url
        self.no_discount_bill_fee = no_discount_bill_fee
        self.shipping_fee = 0
        self.discount_fee = discount_fee

        super(SelfDiningDiscountPaymentManager, self).__init__(
            user_id=user_id, transaction_id=transaction_id, order_id=order_id, coupon_id=coupon_id, merchant_id=merchant_id)

        self.paid_fee = self.round_off(self.paid_fee)
        self.bill_fee = self.round_off(self.bill_fee)
        self.coupon_fee = self.round_off(self.coupon_fee)
        self.discount_fee = self.bill_fee - self.paid_fee - self.coupon_fee
        self.no_discount_bill_fee = self.round_off(self.no_discount_bill_fee)
        self.shipping_fee = self.round_off(self.shipping_fee)

        if is_zero_paid is not None:
            self.handle_self_dining_payment_prepay_order()

    def prepay(self):
        ''' 个人直接付款
        '''
        manager = DishManager(merchant=self.merchant)
        manager.get_discount_plan(self.user.id, enbale_sale_time_discount=True)
        discount_bill_fee = self.bill_fee - self.no_discount_bill_fee
        if manager.discount_plan.dish_discounts.general_user_discount:
            general_user_discount = manager.discount_plan.dish_discounts.general_user_discount
            server_paid_fee = self.round_off(float(discount_bill_fee * general_user_discount) / 100) + \
                self.no_discount_bill_fee - self.coupon_fee - self.order.ifeedu_fee
            args = [
                self.order.id,
                self.bill_fee,
                discount_bill_fee,
                self.no_discount_bill_fee,
                self.paid_fee,
                server_paid_fee,
                self.order.ifeedu_fee
            ]
            message = """
            个人直接买单prepay(单位: 分): {}
            总消费金额: {},
            参与打折金额: {},
            不参与打折金额: {}
            前端计算支付金额: {}
            服务端计算支付金额: {}
            使用投喂总金额: {}
            """.format(*args)
            logger.info(message)
            if self.paid_fee != server_paid_fee:
                raise errors.PaidfeeBillfeeNotMatch()
        self.check_dish_remain_quantity(self.order)
        self.transaction = TransactionManager().handle_scan_code_pay(
            self.user.id, self.merchant.id, self.store.id, self.bill_fee, self.paid_fee,
            self.no_discount_bill_fee, self.pay_method)
        return self.handle_prepay()

    def handle_prepay(self):
        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 采用微信支付
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, order_id=self.order.id)
            logger.info('wechat_pay self_dining_manager prepay result: {}'.format(result))
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, return_url=self.return_url, order_id=self.order.id)
            logger.info('alipay self_dining_manager prepay result: {}'.format(result))
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            key = self.order.id
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, key=key)
            logger.info('shilai wallet_pay self_dining_manager prepay result: {}'.format(result))
        elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            # 同一个orderId如果使用饭票支付的话需要限制1分钟只处理一次,避免网络延迟导致的重复支付
            key = self.order.id
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, key=key)
            logger.info("shilai fanpiao_pay self_dining_manager prepay result: {}".format(result))
        else:
            raise errors.PayMethodNotSupport()
        return self.handle_prepay_success(result)

    def handle_prepay_success(self, result):
        ordering_da_helper = OrderingServiceDataAccessHelper()
        if result.get("errcode") == error_codes.SUCCESS:
            logger.info('prepay success: {}, {}'.format(self.order.id, self.transaction.id))
            if self.pay_method == wallet_pb.Transaction.WALLET:
                # 时来钱包
                ordering_da_helper.update_order(id=self.order.id, transaction_id=self.transaction.id)
                self.order.transaction_id = self.transaction.id
                self.notification()
                result.update({"transactionId": self.transaction.id})
            elif self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                # 微信
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                # 支付宝
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
            result['transactionId'] = self.transaction.id
        return result

    def notification(self):
        if self.transaction.type != wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT:
            return
        manager = OrderManager(merchant=self.merchant)
        try:
            # 先生成红包
            self.generate_red_packet(issue_scene=red_packet_pb.RedPacket.SCAN_CODE_PAY)
            # 如果有使用券包,需要计算是否做平台补贴
            self._cal_platform_discount()
            self._cal_fanpiao_platform_discount()
            self.order.coupon_fee = self.coupon_fee
            logger.info("调用manager.pay_order: {}, {}".format(self.order.id, self.transaction.id))
            manager.pay_order(self.order, self.transaction)
        except Exception as e:
            PaymentManager(self.transaction.pay_method).ordering_refund(self.transaction, self.order)
            logger.info('下单失败,发起退款: {}'.format(self.transaction.id))
            raise e

        # 如果coupon_id不为空,且不是扫码点餐,就要处理优惠券
        self.consume_coupon()
        self.decrease_dish_remain_quantity(self.order)
        self.release_order_lock(self.order)
        MultiPartyDinningManager(order=self.order).payment_event()

from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper

def update_combo_meal_red_packet_values(merchant_id):
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not (merchant and merchant.stores):
        return

    store = merchant.stores[0]
    combo_meal_da = ComboMealDataAccessHelper()
    combo_meals = combo_meal_da.get_combo_meals(store_id=store.id)
    for meal in combo_meals:
      meal.red_packet_value = int(store.combo_meal_red_packet_rate / 100.0 * meal.discount_price)
      combo_meal_da.update_or_create_combo_meal(meal)

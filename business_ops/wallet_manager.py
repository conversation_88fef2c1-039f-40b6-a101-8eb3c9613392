# -*- coding: utf-8 -*-
import logging
import time
from datetime import datetime
from dao.merchant_da_helper import MerchantDataAccessHelper
import proto.page.wallet_pb2 as page_wallet_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.transaction_manager import TransactionManager
from common.utils import distribute_lock
from common.utils import date_utils
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.wallet_da_helper import WalletDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from service import error_codes
from service import errors
from service.base_responses import create_responses_obj
from service.base_responses import success_responses_obj

logger = logging.getLogger(__name__)


class WalletManager:
    # 提现最低门槛
    WITHDRAW_THRESHOLD_AMOUNT = 30
    # 单次提现最低限额
    WITHDRAW_LEAST_AMOUNT_PER_TIME = 30
    # 单日提现最高限额
    WITHDRAW_LIMIT_AMOUNT_PER_DAY = 200 * 100
    # 单日提现次数
    WITHDRAW_TIMES_PER_DAY = 10
    # 钱包转账,单日转出金额为20000
    TRANSFER_OUT_AMOUNT_PER_DAY = 20000
    # 钱包转账,单日转出次数为50次
    TRANSFER_OUT_TIMES_PER_DAY = 50

    @distribute_lock.WalletLock(key="{payer_id}", ttl=2000)
    def transfer(self, payer_id, payee_id, fee, transaction_type):
        """转帐
        Args:
            payer_id: (string)转出的用户ID
            payee_id: (string)转入的用户ID
            fee: (int)转帐数目(分)
        Return:
            response_obj
        """
        transaction_ma = TransactionManager()
        transaction = transaction_ma.handle_user_transfer_prepay(
            payer_id=payer_id, payee_id=payee_id, bill_fee=fee, transaction_type=transaction_type
        )
        # TODO: 使用事务支持操作的原子性
        wallet_da = WalletDataAccessHelper()
        payer_wallet = wallet_da.get_user_wallet(payer_id)
        if not payer_wallet:
            resp = create_responses_obj(error_codes.WALLET_BALANCE_NOT_ENOUGH, error_codes.WALLET_BALANCE_NOT_ENOUGH_MSG)
            return resp
        if payer_wallet.balance >= fee:
            wallet_da.decrease_balance(payer_id, abs(fee))
            wallet_da.increase_balance(payee_id, abs(fee))
            # 转账完成后设置transaction为成功
            transaction.paid_time = date_utils.timestamp_second()
            transaction.state = wallet_pb.Transaction.SUCCESS
            transaction_ma.update_transaction(transaction)
            logger.info("transfer payer:{} to payee:{}".format(payer_id, payee_id))
            return create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        else:
            resp = create_responses_obj(error_codes.WALLET_BALANCE_NOT_ENOUGH, error_codes.WALLET_BALANCE_NOT_ENOUGH_MSG)
            logger.info("transfer payer余额不足: {}".format(payer_id))
            return resp

    def fanpiao_balance_withdraw(self, user, transaction):
        """把饭票余额提现到时来钱包"""
        self.increase_balance(user.id, transaction.paid_fee)
        return True

    @distribute_lock.WalletLock(key="{user_id}", ttl=20000)
    def withdraw(self, user_id, amount, pay_method):
        """提现"""
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.Error(error_codes.UNAUTHORIZED, error_codes.UNAUTHORIZED_MSG)

        # amount单位为分,前端的数据因为精度问题有可能是带有尾巴的小数
        amount = int(amount)
        logger.info("用户{}发起提现请求，金额:{}元".format(user.id, amount / 100))

        # 单次提现最低金额
        if amount < WalletManager.WITHDRAW_LEAST_AMOUNT_PER_TIME:
            least_amount = self.WITHDRAW_LEAST_AMOUNT_PER_TIME / float(100)
            logger.error("用户{}本次提现金额({}元)，低于单次最低限制:{}元".format(user.id, amount / 100, least_amount))
            errmsg = error_codes.WALLET_WITHDRAW_LEAST_AMOUNT_MSG.format(least_amount)
            raise errors.Error(error_codes.WALLET_WITHDRAW_LEAST_AMOUNT, errmsg)

        today_datetime = date_utils.datetime_now_in_timezone()
        today_start_time = int(time.mktime(today_datetime.date().timetuple()))

        transaction_da = TransactionDataAccessHelper()
        state = wallet_pb.Transaction.SUCCESS
        type = wallet_pb.Transaction.CASH_WITHDRAW
        transactions = transaction_da.get_transactions(payer_id=user.id, type=type, start_time=today_start_time, state=state)
        # 单日提现次数
        today_withdraw_total_times = len(transactions)
        if today_withdraw_total_times >= WalletManager.WITHDRAW_TIMES_PER_DAY:
            logger.error(
                "用户{}今天累计提现次数({})，已到达单日累计次数上限{}".format(user.id, today_withdraw_total_times, self.WITHDRAW_TIMES_PER_DAY)
            )
            errmsg = error_codes.WALLET_WITHDRAW_TIMES_PER_TIME_MSG.format(self.WITHDRAW_TIMES_PER_DAY)
            raise errors.Error(error_codes.WALLET_WITHDRAW_TIMES_PER_TIME, errmsg)

        # 单日提现累计金额
        today_withdraw_total_amount = 0
        for transaction in transactions:
            today_withdraw_total_amount += transaction.paid_fee
        if today_withdraw_total_amount > WalletManager.WITHDRAW_LIMIT_AMOUNT_PER_DAY:
            withdraw_limit_amount_per_day = self.WITHDRAW_LIMIT_AMOUNT_PER_DAY / 100
            logger.error(
                "用户{}单日累计提现总金额({}元)，已达到上限额度({}元)".format(
                    user.id, today_withdraw_total_amount / 100, withdraw_limit_amount_per_day
                )
            )
            # 超出用户单日上限额度
            errmsg = error_codes.WALLET_WITHDRAW_LIMIT_PER_DAY_MSG.format(withdraw_limit_amount_per_day)
            raise errors.Error(error_codes.WALLET_WITHDRAW_LIMIT_PER_DAY, errmsg)

        wallet_da = WalletDataAccessHelper()
        wallet = wallet_da.get_user_wallet(user.id)

        # 余额的提现门槛
        if wallet.balance < WalletManager.WITHDRAW_THRESHOLD_AMOUNT:
            withdraw_threshold_amount = self.WITHDRAW_THRESHOLD_AMOUNT / 100
            logger.error("用户{}余额({}元)达不到提现的最低门槛({}元)".format(user.id, wallet.balance / 100, withdraw_threshold_amount))
            errmsg = error_codes.WALLET_WITHDRAW_THRESHOLD_MSG.format(withdraw_threshold_amount)
            raise errors.Error(error_codes.WALLET_WITHDRAW_THRESHOLD, errmsg)

        # 余额不满足提现金额
        if wallet.balance < amount:
            logger.error("用户{}钱包余额({}元)低于提现金额({}元)".format(user.id, wallet.balance / 100, amount / 100))
            # 钱包余额不足
            raise errors.Error(error_codes.WALLET_BALANCE_NOT_ENOUGH, error_codes.WALLET_BALANCE_NOT_ENOUGH_MSG)

        wallet_da.decrease_balance(user.id, amount)
        return True

    def __get_today_transfer_out_limit(self, payer_id):
        """返回用户单日转出限制:
            1. 用户今日转出总金额
            2. 用户今日转出总次数
        Args:
            payer_id: (string)转出人ID
        Return:
            total_amount: (int)总转出额
            total_times: (int)总转出次数
        """
        today_datetime = date_utils.datetime_now_in_timezone()
        today_start_time = int(time.mktime(today_datetime.date().timetuple()))
        transactions = TransactionDataAccessHelper().get_transactions(
            payer_id=payer_id,
            state=wallet_pb.Transaction.SUCCESS,
            start_time=today_start_time,
            type=wallet_pb.Transaction.USER_TRANSFER,
        )
        total_amount = 0
        for transaction in transactions:
            total_amount += transaction.paid_fee
        return total_amount, len(transactions)

    def __get_today_withdraw_total_amount(self, user):
        """获取用户今天累计提现的金额"""
        today_datetime = date_utils.datetime_now_in_timezone()
        today_start_time = int(time.mktime(today_datetime.date().timetuple()))

        transaction_da = TransactionDataAccessHelper()
        transactions = transaction_da.get_transactions(
            payer_id=user.id, type=wallet_pb.Transaction.CASH_WITHDRAW, start_time=today_start_time
        )
        total_amount = 0
        for transaction in transactions:
            total_amount += transaction.paid_fee

        return total_amount

    @distribute_lock.WalletLock(key="{user_id}", ttl=1000)
    def increase_balance(self, user_id, bill_fee, lock_fee=0, merchant_id=None):
        logger.info(f"increase {user_id} {merchant_id} balance {bill_fee} lock_balance {lock_fee}")
        WalletDataAccessHelper().increase_balance(user_id, abs(int(bill_fee)), lock_fee, merchant_id)

    @distribute_lock.WalletLock(key="{user_id}", ttl=1000)
    def increase_coin_balance(self, user_id, bill_fee):
        logger.info(f"increase {user_id} balance {bill_fee}")
        WalletDataAccessHelper().increase_coin_balance(user_id, abs(int(bill_fee)))

    @distribute_lock.WalletLock(key="{user_id}", ttl=1000)
    def decrease_balance(self, user_id, bill_fee, lock_fee=0, merchant_id=None):
        logger.info(f"decrease {user_id} {merchant_id} balance {bill_fee} lock_balance {lock_fee}")
        WalletDataAccessHelper().decrease_balance(user_id, abs(int(bill_fee)), lock_fee, merchant_id)

    @distribute_lock.WalletLock(key="{user_id}", ttl=1000)
    def decrease_coin_balance(self, user_id, bill_fee):
        logger.info(f"decrease {user_id} coin_balance {bill_fee}")
        WalletDataAccessHelper().decrease_coin_balance(user_id, abs(int(bill_fee)))

    @distribute_lock.WalletLock(key="{user_id}", ttl=1000)
    def wallet_pay(self, user_id, fee, **kargs):
        """使用时来钱包支付扣款"""
        wallet = WalletDataAccessHelper().get_user_wallet(user_id)
        if not wallet:
            wallet = wallet_pb.Wallet()
            wallet.owner_id = user_id
        if not wallet:
            return create_responses_obj(error_codes.WALLET_BALANCE_NOT_ENOUGH, error_codes.WALLET_BALANCE_NOT_ENOUGH_MSG)
        lock_fee = 0
        diff_fee = wallet.balance - fee
        if diff_fee < 0:
            if wallet.merchant_balance.get(kargs.get('order').merchant_id, 0) < abs(diff_fee):
                return create_responses_obj(error_codes.WALLET_BALANCE_NOT_ENOUGH, error_codes.WALLET_BALANCE_NOT_ENOUGH_MSG)
            lock_fee = abs(diff_fee)
        WalletDataAccessHelper().decrease_balance(user_id, fee - lock_fee, lock_fee, kargs.get('order').merchant_id)
        kargs.get('order').lock_fee = lock_fee
        return success_responses_obj()

    def handle_payment_success(self, trade_no):
        pass

    def prepay(self, transaction, **kargs):
        '''发起支付'''

        # 时来钱包支付需要做幂等处理
        # 因为网络等原因,有可能会多次请求被发送过来,对于同一单请求,只处理一次
        return self.wallet_pay(transaction.payer_id, transaction.paid_fee, **kargs)

    def notification(self, data):
        '''支付成功的回调'''
        pass

    def partial_refund(self, transaction=None, refund_transaction=None, order=None, **kargs):
        config_da = ConfigDataAccessHelper()
        merchant_vip_membership_config = config_da.get_vip_membership_config(transaction.payee_id)
        discount = merchant_vip_membership_config.vip_membership_discount if merchant_vip_membership_config else 0
        refund_fee = (
            transaction.paid_fee - round(transaction.bill_fee * (100 - discount) / 100 + 0.5)
            if not refund_transaction
            else refund_transaction.paid_fee
        )
        tmp_fee = refund_fee - order.lock_fee
        fee = 0 if tmp_fee < 0 else tmp_fee
        lock_fee = abs(tmp_fee) if tmp_fee < 0 else order.lock_fee
        order.lock_fee -= lock_fee
        self.increase_balance(transaction.payer_id, fee, lock_fee, transaction.payee_id)
        payer_id = transaction.payer_id
        payee_id = transaction.payee_id
        logger.info(f"{transaction.id} 用户{payer_id} 商户{payee_id} 部分退款成功 {refund_fee} {fee} + {lock_fee}")
        return True

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        '''退款, 直接把钱加到时来钱包'''
        self.increase_balance(
            transaction.payer_id, abs(transaction.paid_fee) - order.lock_fee, order.lock_fee, order.merchant_id
        )

        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        _transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.WALLET,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                use_coupon_id=transaction.use_coupon_id,
            )
        return _transaction

    def get_user_wallet(self, user_id, merchant_id=None):
        wallet_vo = page_wallet_pb.Wallet()
        if user_id is None:
            wallet_vo.wallet.balance = 0
            wallet_vo.data.red_packet_balance = 0
            wallet_vo.data.fanpiao_balance = 0
            wallet_vo.data.lock_balance = 0
            return wallet_vo
        wallet = WalletDataAccessHelper().get_user_wallet(user_id)
        now = datetime.now()
        if wallet:
            wallet_vo.wallet.balance = wallet.balance
            wallet_vo.wallet.owner_id = user_id
            wallet_vo.data.red_packet_balance = wallet.balance
            wallet_vo.wallet.coin_balance = wallet.coin_balance
            wallet_vo.data.balance = wallet.balance
            wallet_vo.data.coin_balance = wallet.coin_balance
            wallet_vo.data.lock_balance = wallet.lock_balance
            wallet_vo.data.vip_membership_subscribe_time = wallet.vip_membership.subscribe_time
            wallet_vo.data.vip_membership_expire_at = wallet.vip_membership.expire_at
            wallet_vo.data.is_vip_membership_valid = wallet.vip_membership.expire_at > int(now.timestamp())
            merchant_da = MerchantDataAccessHelper()
            for id, value in wallet.merchant_balance.items():
                if value == 0:
                    continue
                basic_info = merchant_da.query_one(matcher={'id': id}, project={'basicInfo': 1}).get('basicInfo', {})
                merchant_balance = wallet_vo.data.merchant_balance.add()
                merchant_balance.id = id
                merchant_balance.name = basic_info.get('name')
                merchant_balance.balance = value
                merchant_balance.logo_url = basic_info.get('logoUrl')
        wallet_vo.data.fanpiao_balance = self.get_user_fanpiao_balance(user_id)
        self.__set_on_the_way_balance(wallet_vo)
        return wallet_vo

    def __set_on_the_way_balance(self, wallet_vo):
        transaction_da = TransactionDataAccessHelper()
        transactions = transaction_da.get_transactions(
            payer_id=wallet_vo.wallet.owner_id,
            type=wallet_pb.Transaction.GROUP_PURCHASE_LEADER_WALLET_REVENUE,
            state=wallet_pb.Transaction.PENDING,
        )
        fee = 0
        for transaction in transactions:
            fee += transaction.paid_fee
        wallet_vo.wallet.one_the_way_balance = fee
        wallet_vo.data.one_the_way_balance = fee

    def get_user_fanpiao_balance(self, user_id):
        fanpiaos = FanpiaoDataAccessHelper().get_fanpiaos(user_id=user_id, status=fanpiao_pb.Fanpiao.ACTIVE)
        fanpiaos.sort(key=lambda x: x.total_value - x.total_used_fee)
        fanpiao_balance = 0
        for fanpiao in fanpiaos:
            fanpiao_balance += fanpiao.total_value - fanpiao.total_used_fee
        return fanpiao_balance

    def is_vip_membership(self, user_id):
        now = datetime.now()
        wallet_da = WalletDataAccessHelper()
        wallet = wallet_da.get_user_wallet(owner_id=user_id)
        if (
            not wallet
            or wallet.vip_membership.subscribe_time == 0
            or (wallet.vip_membership.expire_at > 0 and wallet.vip_membership.expire_at < int(now.timestamp()))
        ):
            if not wallet:
                wallet = wallet_pb.Wallet()
                wallet.owner_id = user_id
            wallet.vip_membership.subscribe_time = wallet.vip_membership.subscribe_time or int(now.timestamp())
            wallet.vip_membership.expire_at = 0
            wallet_da.add_or_update_wallet(wallet)
        return True

    def is_merchant_enable_vip_membership_payment(self, merchant, merchant_vip_membership_config=None):
        if merchant_vip_membership_config is None:
            config_da = ConfigDataAccessHelper()
            merchant_vip_membership_config = config_da.get_vip_membership_config(merchant_id=merchant.id)
        if merchant_vip_membership_config is None:
            return False
        return merchant_vip_membership_config.enable_vip_membership_payment

    def get_or_create_user_wallet(self, user):
        wallet_da = WalletDataAccessHelper()
        wallet = wallet_da.get_user_wallet(user.id)
        if not wallet:
            wallet = wallet_pb.Wallet()
            wallet.owner_id = user.id
        return wallet

import os
import requests
from io import BytesIO

from flask import Response
from PIL import Image
from PIL import ImageDraw
from PIL import ImageFont

from business_ops import constants
from common import constants as common_constants
from common.config import config
from common.utils import file_access_helper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from wechat_lib import miniprogram_code_api_helper


def _add_corners(img, rad):
    circle = Image.new('L', (rad * 2, rad * 2), 0)
    draw = ImageDraw.Draw(circle)
    draw.ellipse((0, 0, rad * 2, rad * 2), fill=255)
    alpha = Image.new('L', img.size, 255)
    w, h = img.size
    alpha.paste(circle.crop((0, 0, rad, rad)), (0, 0))
    alpha.paste(circle.crop((0, rad, rad, rad * 2)), (0, h - rad))
    alpha.paste(circle.crop((rad, 0, rad * 2, rad)), (w - rad, 0))
    alpha.paste(circle.crop((rad, rad, rad * 2, rad * 2)), (w - rad, h - rad))
    img.putalpha(alpha)
    return img


def _create_poster_qrcode(wish_list):
    code_content = miniprogram_code_api_helper.create_unlimited_code(
        config.WECHAT_MINIPROGRAM_APPID, scene=wish_list.id, page='package-merchant/feed/feed', is_hyaline=True)
    return code_content


BACKGROUND_IMAGE_FILE = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, 'background2.png')
XIAOTIAN_BACKGROUND_IMAGE_FILE = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, 'xiaotian_background.png')
FONT_FILE = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, 'ms_yahei.ttf')

def generate_poster(wish_list):
    user = UserDataAccessHelper().get_user(wish_list.user_id)
    nickname = user.wechat_profile.nickname

    bg_img = Image.open(BACKGROUND_IMAGE_FILE)

    qr_code_img = Image.open(BytesIO(_create_poster_qrcode(wish_list)))
    qr_code_img = qr_code_img.resize((200, 200))
    qr_code_img = qr_code_img.convert('RGBA')
    canvas = Image.new('RGBA', qr_code_img.size, (255, 255, 255, 255))
    canvas.paste(qr_code_img, mask=qr_code_img)
    qr_code_mask = Image.new("L", qr_code_img.size, 0)
    qr_code_draw = ImageDraw.Draw(qr_code_mask)
    qr_code_draw.ellipse((0, 0, 200, 200), fill=255)

    bg_img.paste(canvas, (470, 913), qr_code_mask)

    file_name = '{}_{}.jpg'.format(wish_list.user_id, wish_list.id)
    file_path = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, wish_list.merchant_id, file_name)
    file_access_helper.ensure_directory_exists_for_filename(file_path)
    bg_img = bg_img.convert('RGB')
    bg_img.save(file_path, quality=85)

    return get_poster_url(wish_list)


def generate_poster_v1(wish_list):
    user = UserDataAccessHelper().get_user(wish_list.user_id)
    nickname = user.wechat_profile.nickname

    bg_img = Image.open(XIAOTIAN_BACKGROUND_IMAGE_FILE)
    merchant = MerchantDataAccessHelper().get_merchant(wish_list.merchant_id)
    if '小田豆浆' not in merchant.basic_info.name:
        bg_img = Image.open(BACKGROUND_IMAGE_FILE)

    response = requests.get(user.wechat_profile.headimgurl)
    head_img = Image.open(BytesIO(response.content))
    head_img = head_img.resize((120, 120))
    head_img_mask = Image.new("L", head_img.size, 0)
    head_img_draw = ImageDraw.Draw(head_img_mask)
    head_img_draw.ellipse((10, 10, 112, 112), fill=255)

    qr_code_img = Image.open(BytesIO(_create_poster_qrcode(wish_list)))
    qr_code_img = qr_code_img.resize((170, 170))
    qr_code_img = qr_code_img.convert('RGBA')
    canvas = Image.new('RGBA', qr_code_img.size, (255, 255, 255, 255))
    canvas.paste(qr_code_img, mask=qr_code_img)
    qr_code_mask = Image.new("L", qr_code_img.size, 0)
    qr_code_draw = ImageDraw.Draw(qr_code_mask)
    qr_code_draw.ellipse((0, 0, 170, 170), fill=255)

    bg_img.paste(head_img, (14, 305), head_img_mask)
    bg_img.paste(canvas, (110, 910), qr_code_mask)

    # 添加用户昵称文本
    font = ImageFont.truetype(FONT_FILE, size=32)
    pos = (140, 320)
    color = 'rgb(66, 66, 66)'
    draw = ImageDraw.Draw(bg_img)
    draw.text(pos, nickname, fill=color, font=font)

    file_name = '{}_{}.png'.format(wish_list.user_id, wish_list.id)
    file_path = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, wish_list.merchant_id, file_name)
    file_access_helper.ensure_directory_exists_for_filename(file_path)
    bg_img.save(file_path)

    return get_poster_url(wish_list)


def get_poster(wish_list):
    qrcode_filename = '{}_{}.jpg'.format(wish_list.user_id, wish_list.id)
    qrcode_file = os.path.join(constants.IFEEDU_POSTER_IMAGES_DIR, wish_list.merchant_id, qrcode_filename)
    if not os.path.exists(qrcode_file):
        return None
    with open(qrcode_file, "rb") as f:
        image = f.read()
        filename_type = qrcode_filename.split(".")[-1]
        if filename_type == 'jpg':
            filename_type = 'jpeg'
        resp = Response(image, mimetype='image/{}'.format(filename_type))
        return resp
    return None


def get_poster_url(wish_list):
    domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
    poster_url = "{}/{}".format(domain, "ifeedu/{}/poster/{}".format(wish_list.user_id, wish_list.id))
    return poster_url

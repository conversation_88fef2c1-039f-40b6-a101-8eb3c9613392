# -*- encoding: utf-8 -*-

'''
@Time        :   2024/12/23 16:36:51
'''
import json
import time
import logging
from typing import Optional, Dict, List, Any

from common.config import config
from business_ops.base_manager import BaseManager
import proto.ordering.registration_pb2 as registration_pb
from dao.user_da_helper import UserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common import http
from cache.redis_client import RedisClient
from proto.ordering import dish_pb2 as dish_pb
from service import error_codes, errors
from business_ops.ordering.order_manager import OrderManager
from common.utils.distribute_lock import AtomicDistributedLock
from common.cache_server_keys import CacheServerKeys

logger = logging.getLogger(__name__)


class MultiPartyDinningManager(BaseManager):
    """多人点餐处理器"""
    redis = RedisClient().get_connection()

    _shopping_cart_event = "shoppingCart"
    _table_info_event = "tableInfo"
    _order_event = "order"
    _payment_event = "payment"
    _merchant_event = "merchant"

    USER_KEY = "multi_party_dinning:room_user:{room_id}"
    SHOPPING_CART_KEY = "multi_party_dinning:shopping_cart:{room_id}"
    TABLE_INFO_KEY = "multi_party_dinning:table_info:{room_id}"
    TABLE_INFO_LOCK_KEY = "multi_party_dinning:table_info_lock:{room_id}"
    
    def __init__(self, *args, **kargs):
        order = kargs.get("order")
        if order:
            kargs.update({
                "table_id": order.table_id,
                "merchant_id": order.merchant_id,
                "user_id": order.user_id,
            })
        self.order = order
        self.table_id = kargs.pop("table_id", None)
        self.merchant_id = kargs.get("merchant_id")
        super().__init__(*args, **kargs)
        self.room_id = f"{self.merchant_id}:{self.table_id}"
        self.user = UserDataAccessHelper().get_user(kargs.get('user_id'))
        self.user_info = {
            "id": self.user.id,
            "type": "CUSTOMER",
            "avatarLink": self.user.member_profile.head_image_url
        }

    def _get_cache(self, key, resp_json=True):
        value = self.redis.get(key)
        if value:
            value = value.decode()
            if not resp_json:
                return value
            return json.loads(value)

    def _set_cache(self, key, obj: dict, ex=None):
        return self.redis.set(key, json.dumps(obj), ex=ex)

    def _check_permission(self):
        return self.registration_info.ordering_config.enable_many_people_order

    def _is_pay_first(self):
        return self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST
    
    def _broadcast_room(self, event, room_id, data):
        if not self._check_permission():
            return
        json_data = {
            "event": event,
            "roomId": room_id,
            "data": data,
            "namespace": "/multi_party_dinning"
        }
        resp = http.post(
            f"{config.websocket_service_domain}/broadcast",
            json=json_data
        ).json()
        logger.info(f"多人点餐广播消息，内容：{json_data}，响应结果：{resp}")

    def _get_table_info(self):
        resp = http.post(
            f"{config.pos_service_domain_v2}/store/layout/table/get",
            json={
                "merchantId": self.merchant_id,
                "tableId": self.table_id,
            }
        ).json()
        if resp.get("errcode") != 0:
            raise Exception(f"获取桌台状态失败，错误码：{resp.get('errcode')}，错误信息：{resp.get('errmsg')}")
        return resp.get("data", {})

    def _is_empty_table(self, table_info: dict) -> bool:
        status = table_info.get("status", "")
        if status == "DISABLED":
            raise errors.Error(err=error_codes.TABLE_DISABLED)
        return status == "IDLE" or not table_info.get("currentOrderIds")

    def get_current_order(self, room_id):
        """获取当前桌台未支付的订单"""
        if not self._check_permission():
            return
        if self._is_pay_first():
            return
            # current_timestamp = int(time.time())
            # order_da = OrderingServiceDataAccessHelper()
            # current_timestamp -= 3 * 60 * 60
            # status = [dish_pb.DishOrder.ORDERED, dish_pb.DishOrder.PAYING]
            # return order_da.get_recently_order(
            #     merchant_id=self.merchant_id,
            #     table_id=self.table_id,
            #     before_time=current_timestamp,
            #     status=status
            # )
        table_info = self._get_table_info()
        if self._is_empty_table(table_info):
            return
        order = self.get_order(table_info.get("currentOrderIds", [])[-1])
        if order and order.status in [
            dish_pb.DishOrder.ORDERED,
            dish_pb.DishOrder.APPROVED,
            dish_pb.DishOrder.TO_BE_CONFIRMED,
            dish_pb.DishOrder.PAYING,
            dish_pb.DishOrder.PAID,
            dish_pb.DishOrder.POS_PAID,
            dish_pb.DishOrder.POS_SUCCESS,
        ]:
            return order

    def get_order(self, order_id):
        order = OrderingServiceDataAccessHelper().get_order(id=order_id)
        if order:
            return order
        return OrderingServiceDataAccessHelper().get_order(ordering_service_order_id=order_id)

    def _get_user_info(self, type="CUSTOMER"):
        return self.user_info

    def clear_shopping_cart(self, action_type="ORDER_CLEAR"):
        if not self._check_permission():
            return
        self._broadcast_room(
            self._shopping_cart_event,
            self.room_id,
            [{
                "roomId": self.room_id,
                "userInfo": self._get_user_info(),
                "type": "SHOPPING_CART",
                "shoppingCart": {"actionType": action_type}
            }]
        )

    def get_shopping_cart(self):
        if not self._check_permission():
            return
        return self._get_cache(self.SHOPPING_CART_KEY.format(room_id=self.room_id))

    def get_online_users(self):
        if not self._check_permission():
            return
        key = self.USER_KEY.format(room_id=self.room_id)
        users = self.redis.hgetall(key)
        if not users:
            return
        return {
            k.decode(): v.decode()
            for k, v in users.items()
        }
    
    def table_info_event(self, people_count=0, type="CLEAR"):
        if not self._check_permission():
            return
        self._broadcast_room(
            event=self._table_info_event,
            room_id=self.room_id,
            data={
                "roomId": self.room_id,
                "userInfo": self._get_user_info(),
                "type": "TABLE_INFO",
                "tableInfo": {
                    "actionType": type,
                    "tableId": self.table_id,
                    "peopleCount": people_count,
                    "merchantId": self.merchant_id,
                    "storeId": self.store.id
                }
            }
        )
    
    def create_order(self, order, save=True):
        if not self._check_permission():
            return

        if save:
            users = self.get_online_users()
            if users:
                while order.user_ids:
                    order.user_ids.pop()
                for user_id in users:
                    order.user_ids.append(user_id)
                OrderingServiceDataAccessHelper().add_or_update_order(order=order)
        if not self._is_pay_first():
            self.clear_shopping_cart()
        self.order_event(type="ORDERED", order=order)
    
    def payment_event(self, order=None, type="PAID"):
        if not self._check_permission():
            return
        if order is None:
            order = self.order
        self._broadcast_room(
            self._payment_event,
            self.room_id,
            {
                "roomId": self.room_id,
                "userInfo": self._get_user_info(),
                "type": "PAYMENT",
                "payment": {
                    "orderId": order.id,
                    "actionType": type,
                }
            }
        )
        if type == "PAID" and self._is_pay_first():
            self.clear_shopping_cart()
            self.table_info_event(type="CLEAR")

    def merchant_event(self, order=None, type="DISH_EVENT"):
        if not self._check_permission():
            return
        if order is None:
            order = self.order
        self._broadcast_room(
            self._merchant_event,
            self.room_id,
            {
                "roomId": self.room_id,
                "userInfo": self._get_user_info(),
                "type": "MERCHANT",
                "merchant": {
                    "orderId": order.id,
                    "actionType": type,
                }
            }
        )    

    def order_event(self, order=None, type="ORDERED", pos_config=None):
        if not self._check_permission():
            return
        if order is None:
            order = self.order
        order = {
            "orderId": order.id,
            "actionType": type,
        }
        if pos_config:
            order["posConfig"] = pos_config
        self._broadcast_room(
            self._order_event,
            self.room_id,
            {
                "roomId": self.room_id,
                "userInfo": self._get_user_info(),
                "type": "ORDER",
                "order": order
            }
        )


class MultiPartyDinningService:
    """多人点餐服务类"""
    
    def __init__(self, merchant_id: str = None, table_id: str = None, user_id: str = None, order = None):
        self.merchant_id = merchant_id
        self.table_id = table_id
        self.user_id = user_id
        self.order = order
        self.manager = MultiPartyDinningManager(
            merchant_id=merchant_id,
            table_id=table_id,
            user_id=user_id,
            order=order
        )

    def get_room_id(self) -> str:
        return f"{self.merchant_id}:{self.table_id}"

    def get_order_info(self, order_id: Optional[str] = None) -> Dict:
        try:
            if order_id:
                order = self.manager.get_order(order_id)
            else:
                order = self.manager.get_current_order(self.get_room_id())
            return self.manager.to_json(order)
        except Exception as e:
            logger.exception(f"获取订单信息失败")
            raise errors.ShowError(message="获取订单信息失败")

    def get_shopping_cart(self) -> Dict[str, Any]:
        try:
            room_id = self.get_room_id()
            shopping_cart = self.manager._get_cache(
                MultiPartyDinningManager.SHOPPING_CART_KEY.format(room_id=room_id)
            )
            table_info = self.manager._get_cache(
                MultiPartyDinningManager.TABLE_INFO_KEY.format(room_id=room_id)
            )
            return {
                "shoppingCart": shopping_cart,
                "tableInfo": table_info
            }
        except Exception as e:
            logger.error(f"获取购物车信息失败: {str(e)}")
            raise errors.ShowError(message="获取购物车信息失败")

    @staticmethod
    def validate_dish_list(dish_list: List[Dict]) -> None:
        if not dish_list:
            raise errors.Error(
                errcode=error_codes.PARAMETER_NOT_ENOUGH,
                errmsg=error_codes.PARAMETER_NOT_ENOUGH_MSG
            )

    def add_dishes_to_order(self, store_id: str, order_id: str, dish_list: List[Dict], remark: str) -> None:
        try:
            self.validate_dish_list(dish_list)
            
            ordering_da = OrderingServiceDataAccessHelper()
            order = ordering_da.get_order(id=order_id)
            if not order:
                raise errors.AddDishFailed()
                
            manager = OrderManager(merchant_id=order.merchant_id, order=order)

            if self._is_order_paying(order_id):
                raise errors.ShowError(message=error_codes.ORDER_PAYING_V2)

            # 添加菜品处理
            with self._get_add_dish_lock(order_id) as lock:
                if not lock:
                    raise errors.ShowError(message=error_codes.ORDER_ADDING_DISH)
                if remark is not None:
                    order.remark += f"&nbsp;{remark.strip()}"
                self._process_add_dishes(manager, dish_list, order, ordering_da)
                pos_config = self._get_pos_config(store_id)
                self._send_message_to_customer(order, pos_config)
        except Exception as e:
            logger.error(f"添加菜品失败: {str(e)}")
            raise

    def _is_order_paying(self, order_id: str) -> bool:
        return AtomicDistributedLock(
            key=CacheServerKeys.get_order_paying_cache_key(order_id),
            ttl=60 * 1000
        ).locked

    def _get_add_dish_lock(self, order_id: str) -> AtomicDistributedLock:
        return AtomicDistributedLock(
            key=CacheServerKeys.get_add_dish_key(order_id),
            ttl=60 * 1000
        )

    def _process_add_dishes(self, manager: OrderManager, dish_list: List[Dict], order: Any, ordering_da: OrderingServiceDataAccessHelper) -> None:
        manager.add_dish_to_shilai_order(dish_list, order, self.user_id)
        manager.add_dish_ordering_order(dish_list, order, self.user_id)
        ordering_da.add_or_update_order(order)

    def _get_pos_config(self, store_id: str) -> Dict[str, Any]:
        resp = http.post(
            f"{config.pos_service_domain_v2}/store/setting/query",
            json={
                "merchantId": self.merchant_id,
                "storeId": store_id,
            }
        ).json()
        
        if resp.get("errcode") != 0:
            raise Exception(f"获取收银机配置失败，错误码：{resp.get('errcode')}，错误信息：{resp.get('errmsg')}")
            
        return resp.get("data", {})

    def _send_message_to_customer(self, order: Any, pos_config: Dict[str, Any]) -> None:
        enable_dinner_auto_confirm = pos_config.get("orderSetting", {}).get("enableDinnerAutoConfirm", False)
        self.manager.order_event(
            order=order,
            type="POS_CONFIG",
            pos_config={"enableDinnerAutoConfirm": enable_dinner_auto_confirm}
        )
        self.manager.create_order(order=order)

# -*- coding: utf-8 -*-


import logging

from business_ops.pay_manager import PayManager
from service.errors import ShowError
from service.base_responses import success_responses_obj

logger = logging.getLogger(__name__)


class NoneExistsPayManager(PayManager):

    whitelist_merchant_id = set([
        "d3348a6659ac401486d53753ea58e8d2",
        "1e543376139b474e97d38d487fa9fbe8"
    ])

    def __init__(self, *args, **kargs):
        super(NoneExistsPayManager, self).__init__(*args, **kargs)
        merchant_id = kargs.get("merchant_id")
        merchant = kargs.get("merchant")
        if not merchant and not merchant_id:
            raise ShowError("支付方式错误")
        if not merchant_id:
            merchant_id = merchant.id
        if merchant_id not in self.whitelist_merchant_id:
            raise ShowError("支付方式错误")

    def prepay(self, transaction, **kargs):
        notify_url = self.generate_notify_url(transaction, payment_prefix="none_exists", **kargs)
        result = success_responses_obj()
        result.update({"notify_url": notify_url})
        return result

    def notification(self, data):
        return True

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        return True

    def coupon_package_refund(self, transaction, reason=None):
        return True

    def fanpiao_refund(self, transaction, reason=None):
        return True

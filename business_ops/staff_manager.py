# -*- coding: utf-8 -*-

import logging
import time
import os
import traceback
import re

import jieba
from whoosh.qparser import QueryParser
from whoosh.index import open_dir
from fuzzywuzzy import fuzz

from dao.membership_da_helper import MembershipDataAccessHelper
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.page.staff_merchant_pb2 as staff_merchant_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.config_pb2 as config_pb
import proto.staff_pb2 as staff_pb
import proto.permission_pb2 as permission_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.membership_pb2 as membership_pb
from business_ops.ordering.dish_manager import DishManager
from business_ops.base_manager import BaseManager
from business_ops.config_manager import ConfigManager
from business_ops.ordering.shilai_ops_manager import ShilaiPosMerchantManager
from business_ops.ordering.shop_manager import ShopManager
from business_ops.tian_que_pay_manager import TianQuePayManager
from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.constants import HualalaConstants
from business_ops.ordering.attr_manager import AttrManager
from business_ops.ordering.supply_condiment_manager import SupplyCondimentManager
from business_ops.printer.feie_print_format import FeiePrintFormat
from common.utils import date_utils, id_manager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from dao.staff_da_helper import StaffDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from service import errors
from google.protobuf import json_format
from common import http
from common.config import config

logger = logging.getLogger(__name__)


class StaffManager(BaseManager):
    def __init__(self, staff_id=None, check_permission=False, *args, **kargs):
        super(StaffManager, self).__init__(*args, **kargs)
        self.staff = None
        self.indexdir = "/data/whoosh/dish_images/indexdir"
        if staff_id is not None:
            self.staff = StaffDataAccessHelper().get_staff(staff_id=staff_id)
        if check_permission:
            self.__check_permission()
        self.redis_client = RedisClient().get_connection()
        self.init_config()

    def init_config(self):
        if not self.merchant:
            return
        config_da = ConfigDataAccessHelper()
        self.fanpiao_config = config_da.get_fanpiao_config(merchant_id=self.merchant.id)
        if self.fanpiao_config is None:
            self.fanpiao_config = config_pb.FanpiaoConfig()
            self.fanpiao_config.merchant_id = self.merchant.id
        self.coupon_package_config = config_da.get_coupon_package_config(merchant_id=self.merchant.id)
        if self.coupon_package_config is None:
            self.coupon_package_config = config_pb.CouponPackageConfig()
            self.coupon_package_config.merchant_id = self.merchant.id
        self.ordering_config = config_da.get_ordering_config(merchant_id=self.merchant.id)
        if self.ordering_config is None:
            self.ordering_config = config_pb.OrderingConfig()
            self.ordering_config.merchant_id = self.merchant.id
            self.ordering_config.disable_show_sold_number = True
            config_da.add_or_update_ordering_config(self.ordering_config)

    def get_fanpiao_categories(self):
        fanpiao_da = FanpiaoDataAccessHelper()
        categories = fanpiao_da.get_fanpiao_categories(status=fanpiao_pb.FanpiaoCategory.ACTIVE, merchant_id=self.merchant.id)
        categories += fanpiao_da.get_fanpiao_categories(
            status=fanpiao_pb.FanpiaoCategory.INACTIVE, merchant_id=self.merchant.id
        )
        return categories

    def sync_dish_with_one_button(self):
        key = CacheServerKeys.get_staff_assist_update_dish_key(self.merchant)
        # if not self.redis_client.setnx(key, 1):
        #     raise errors.Error(err=error_codes.SYNCING_DISH)
        self.redis_client.expire(key, date_utils.ONE_MINUTE * 10)
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        manager = DishManager(merchant=self.merchant, registration_info=registration_info)
        logger.info("同步菜品分类: {}".format(self.merchant.id))
        manager.async_categories()
        dishes = manager.get_all_dish()
        required_dishes = manager.get_required_dishes(self.merchant.id, self.store.id)
        logger.info("同步菜品: {}".format(self.merchant.id))
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            for category_dish in dishes:
                dish_brand_id = category_dish.get("brandDishId")
                manager.sync_dish(category_dish=category_dish, dish_brand_id=dish_brand_id)

        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            attr_manager = AttrManager(merchant=self.merchant)
            attr_manager.update_dishes_attr_group_hualala(dishes=dishes)
            supply_condiment_manager = SupplyCondimentManager(merchant=self.merchant)
            for dish in dishes:
                supply_condiment_manager.add_or_update_dish_supply_condiment(dish=dish)

        registration_info.latest_update_dish_timestamp = int(time.time())
        self.do_update_registration_info(registration_info)

        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            dishes = self.sync_shilai_dishes(dishes, required_dishes=required_dishes)

        self.redis_client.delete(key)

    def sync_shilai_dishes(self, data, required_dishes=None):
        dishes = data.get("dishes")
        categories = data.get("categories")
        ordering_da = OrderingServiceDataAccessHelper()
        for category in categories:
            ordering_da.add_or_update_category(category=category)
        for dish_id, dish in dishes.items():
            ordering_da.add_or_update_dish(dish=dish)

        if required_dishes:
            registration_info = ordering_da.get_registration_info(merchant_id=self.merchant.id, return_proto=False)
            _required_dishes = []
            for dish_id, item in required_dishes.items():
                dish = dishes[dish_id]
                _required_dishes.append({"name": dish.name, "price": dish.price, "type": item['type'], "id": dish_id})
            registration_info['orderingConfig']['requiredOrderItems'] = _required_dishes
            registration_info = json_format.ParseDict(
                registration_info, registration_pb.OrderingServiceRegistrationInfo(), ignore_unknown_fields=True
            )
            ordering_da.add_or_update_registration_info(registration_info)

    def update_fanpiao_category(self, fanpiao_category_id, state, **kargs):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_category = fanpiao_da.get_fanpiao_category_by_id(id=fanpiao_category_id)
        if not fanpiao_category:
            return
        base_selling_quantity = kargs.get("base_selling_quantity")
        if base_selling_quantity is not None:
            fanpiao_category.base_selling_quantity = base_selling_quantity
        display_scene = kargs.get("display_scene")
        if display_scene is not None:
            if isinstance(display_scene, str):
                display_scene = fanpiao_pb.FanpiaoCategory.DisplayScene.Value(display_scene)
            fanpiao_category.display_scene = display_scene
        if state is not None:
            if isinstance(state, str):
                state = fanpiao_pb.FanpiaoCategory.Status.Value(state)
            fanpiao_category.status = state
        now = int(time.time())
        fanpiao_category.update_time = now
        fanpiao_category.update_staff_id = self.staff.id
        fanpiao_da.add_or_update_fanpiao_category(fanpiao_category)

    def check_modify_discount_permission(self):
        if config.deployment_env != 'prod':
            return True
        if not self.staff:
            return False
        if self.staff.id not in [
            "84e70e46-5fbf-43b5-96a8-4bb97d6fd9cf",  # 叶安仪
            "c97cf5c1-407d-402c-b573-b90c3d88c353",  # 宋美丽
            "a5ade1d2-8ec9-45f0-ab32-28ecea28cb98",  # 孙文涛
            "3051be5e-97c2-4b19-9780-28fdab6d191f",  # 魏华夏
            "438a1b5a-4f89-4917-9114-9b6751ac79ce",  # 陈梦真
            "c543e71c-12cf-47e7-add4-96b7680362c3",  # 谢府
        ]:
            return False
        return True

    def _sync_promotion_to_pos(self):
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        if registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.PosType.SHILAI:
            return
        resp = http.post(
            f"{config.pos_service_domain_v2}/store/promotion/send_message_to_pos",
            json={"merhcantId": self.merchant.id, "storeId": self.store.id},
        )
        data = resp.json()
        if data.get("errcode", 500) != 0:
            logger.error(f"同步营销折扣到收银机失败，resp={data}")
        else:
            logger.info(f"同步营销折扣到收银机成功，resp={data}")

    def update_merchant_marketing_config(
        self, enable_fanpiao, enable_buy_fanpiao, max_discount, fanpiao_and_coupon_discount=None, **kargs
    ):
        if enable_fanpiao is not None:
            self.store.enable_fanpiao = enable_fanpiao
        if enable_buy_fanpiao is not None:
            self.store.disable_buy_fanpiao = not enable_buy_fanpiao
        if max_discount is not None:
            # if max_discount <= 50:
            #     raise errors.ShowError("折扣底线过低")
            if self.check_modify_discount_permission():
                self.merchant.preferences.coupon_config.max_discount = 100 - max_discount
                self.merchant.preferences.coupon_config.max_discount_update_timestamp = int(time.time())
                self._sync_promotion_to_pos()
        if fanpiao_and_coupon_discount is not None:
            fanpiao_and_coupon_extra_discount = (
                100 - self.merchant.preferences.coupon_config.max_discount - fanpiao_and_coupon_discount
            )
            if fanpiao_and_coupon_extra_discount < 0:
                raise errors.ShowError("饭票券包额外折扣底线过低")
            if self.check_modify_discount_permission():
                self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount = fanpiao_and_coupon_extra_discount
        fanpiao_pay_commission_rate = kargs.get("fanpiao_pay_commission_rate")
        if fanpiao_pay_commission_rate is not None:
            # TODO: 临时处理，饭票佣金比率与券包佣金比率,钱包佣金比率相同
            self.registration_info.fanpiao_pay_commission_rate = fanpiao_pay_commission_rate
            self.registration_info.coupon_package_pay_commission_rate = fanpiao_pay_commission_rate
            self.registration_info.wallet_pay_commission_rate = fanpiao_pay_commission_rate
            self.registration_info.coupon_commission_rate = fanpiao_pay_commission_rate
        is_demonstration_merchant = kargs.get("is_demonstration_merchant")
        if is_demonstration_merchant is not None:
            self.merchant.is_demonstration_merchant = is_demonstration_merchant
        self.do_update_merchant()
        self.do_update_registration_info()
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_info_update_timestamp=now, dish_catalog_update_timestamp=now)

    def get_merchant_marketing_config(self):
        marketing_config_vo = staff_merchant_pb.MarketingConfigVO()
        coupon_packages = self.merchant.preferences.coupon_config.coupon_packages
        self._set_coupon_packages(marketing_config_vo, coupon_packages)
        self._set_fanpiao_categories(marketing_config_vo)
        marketing_config_vo.max_discount = 100 - self.merchant.preferences.coupon_config.max_discount
        coupon_config = self.merchant.preferences.coupon_config
        fanpiao_and_coupon_discount = 100 - coupon_config.max_discount - coupon_config.fanpiao_and_coupon_extra_discount
        marketing_config_vo.fanpiao_and_coupon_discount = fanpiao_and_coupon_discount
        marketing_config_vo.fanpiao_pay_commission_rate = self.registration_info.fanpiao_pay_commission_rate
        marketing_config_vo.is_demonstration_merchant = self.merchant.is_demonstration_merchant
        return marketing_config_vo

    def _set_fanpiao_categories(self, marketing_config_vo):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiao_categories = fanpiao_da.get_fanpiao_categories(merchant_id=self.merchant.id)
        for category in fanpiao_categories:
            if category.status == fanpiao_pb.FanpiaoCategory.DELETED:
                continue
            category_vo = marketing_config_vo.fanpiao_categories.add()
            category_vo.id = category.id
            category_vo.total_value = category.total_value
            category_vo.sell_price = category.sell_price
            category_vo.discount = category.discount
            category_vo.name = category.name
            category_vo.state = category.status
            category_vo.base_selling_quantity = category.base_selling_quantity
            category_vo.real_selling_quantity = fanpiao_da.count_fanpiao_selling_quantity(
                fanpiao_category_id=category.id, status=fanpiao_pb.Fanpiao.ACTIVE
            )
            category_vo.display_scene = category.display_scene
        marketing_config_vo.enable_fanpiao = self.store.enable_fanpiao
        marketing_config_vo.enable_buy_fanpiao = not self.store.disable_buy_fanpiao

    def _set_coupon_packages(self, marketing_config_vo, coupon_packages):
        for coupon_package in coupon_packages:
            coupon_package_vo = marketing_config_vo.coupon_packages.add()
            coupon_package_vo.id = coupon_package.id
            coupon_package_vo.total_value = coupon_package.coupon_package_spec.total_value
            coupon_package_vo.sell_price = coupon_package.coupon_package_spec.sell_price
            coupon_package_vo.least_cost = coupon_package.coupon_package_spec.least_cost
            coupon_package_vo.reduce_cost = coupon_package.coupon_package_spec.reduce_cost
            coupon_package_vo.coupon_count = coupon_package.coupon_package_spec.coupon_count
            coupon_package_vo.fixed_term = coupon_package.coupon_package_spec.coupon_category_spec.date_info.fixed_term
            coupon_package_vo.state = coupon_package.state
            coupon_package_vo.name = coupon_package.name
            coupon_package_vo.display_scene = coupon_package.coupon_package_spec.coupon_category_spec.display_scene
            latest_on_sale_timestamp = coupon_package.latest_on_sale_timestamp
            coupon_package_vo.latest_on_sale_timestamp = latest_on_sale_timestamp

    def update_member_config(self, customize_info_vo):
        manager = ShilaiPosMerchantManager(merchant_id=self.merchant.id)
        member_config = None
        try:
            store = manager.get_store_setting()
            member_config = store.get("storeMemberAccountRechargeConfig")
            logger.info(f"member_config: {member_config}")
        except Exception as e:
            logger.error(traceback.format_exc())
        if member_config:
            customize_info_vo.enable_shilai_member_card_pay = member_config['enableMiniProgramMemberAccountPay']
            customize_info_vo.enable_shilai_member_card_recharge = member_config['enableMiniProgramRecharge']

    def get_customize_info(self):
        customize_info_vo = staff_merchant_pb.CustomizeInfoVO()
        customize_info_vo.splash_image_url = self.store.splash_image_url
        customize_info_vo.splash_mode = self.store.splash_mode
        customize_info_vo.banner_mode = self.store.banner_mode
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        if not registration_info:
            registration_info = registration_pb.OrderingServiceRegistrationInfo()
            registration_info.merchant_id = self.merchant.id
            registration_info.ordering_config.enable_eat_in = True
            registration_info.package_type = registration_pb.OrderingServiceRegistrationInfo.PackageType.TAKE_AWAY
            self.do_update_registration_info(registration_info)

        customize_info_vo.pay_type = registration_info.pay_type
        customize_info_vo.enable_payment_reminder = not self.store.disable_payment_reminder
        customize_info_vo.check_out_print = registration_info.printer_config.keruyun_printer.check_out_print
        customize_info_vo.enable_invoice = self.store.enable_invoice
        if registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            customize_info_vo.enable_shilai_member_card_pay = self.store.enable_shilai_member_card_pay
            customize_info_vo.enable_shilai_member_card_recharge = self.store.enable_shilai_member_card_recharge
        else:
            self.update_member_config(customize_info_vo)
        customize_info_vo.business_type = registration_info.ordering_config.dinner_type
        customize_info_vo.latest_update_dish_timestamp = registration_info.latest_update_dish_timestamp
        customize_info_vo.enable_shilai_promotion_splash = self.store.enable_shilai_promotion_splash
        customize_info_vo.enable_show_sold_out_dishes = self.store.enable_show_sold_out_dishes
        customize_info_vo.enable_invite_share = self.store.enable_invite_share
        customize_info_vo.dish_discount_rate = self.merchant.preferences.coupon_config.dish_discount_rate
        customize_info_vo.red_packet_discount_rate = self.merchant.preferences.coupon_config.red_packet_discount_rate
        customize_info_vo.enable_subsidies_overflow_fee = self.store.enable_subsidies_overflow_fee
        customize_info_vo.enable_ordering_coupon_package_union_pay = self.store.enable_ordering_coupon_package_union_pay
        customize_info_vo.disable_show_fanpiao_purchase_number = self.fanpiao_config.disable_show_fanpiao_purchase_number
        customize_info_vo.balance_refund_method = self.fanpiao_config.balance_refund_method
        customize_info_vo.enable_fanpiao_balance_refund = self.fanpiao_config.enable_fanpiao_balance_refund
        customize_info_vo.is_refund_the_way = self.fanpiao_config.is_refund_the_way
        customize_info_vo.disable_show_fanpiao_price = self.fanpiao_config.disable_show_fanpiao_price
        customize_info_vo.disable_show_coupon_package_purchase_number = (
            self.coupon_package_config.disable_show_coupon_package_purchase_number
        )
        customize_info_vo.enable_show_time_limit_sale = self.ordering_config.enable_show_time_limit_sale
        customize_info_vo.disable_show_sold_number = self.ordering_config.disable_show_sold_number
        customize_info_vo.enable_firstly_addon = self.ordering_config.enable_firstly_addon
        customize_info_vo.meal_code_base_value = registration_info.meal_code_base_value
        customize_info_vo.meal_code_max_value = registration_info.meal_code_max_value
        customize_info_vo.serial_number_base_value = registration_info.serial_number_base_value
        customize_info_vo.serial_number_max_value = registration_info.serial_number_max_value
        customize_info_vo.enterprise_wechat_number = self.merchant.enterprise_wechat_number
        customize_info_vo.enable_auto_recover_sold_out = registration_info.ordering_config.enable_auto_recover_sold_out
        customize_info_vo.enable_auto_recover_attr_sold_out = (
            registration_info.ordering_config.enable_auto_recover_attr_sold_out
        )
        customize_info_vo.enable_auto_recover_supply_condiments_sold_out = (
            registration_info.ordering_config.enable_auto_recover_supply_condiments_sold_out
        )

        customize_info_vo.enable_auto_recover_remain_quantity = (
            registration_info.ordering_config.enable_auto_recover_remain_quantity
        )
        customize_info_vo.enforce_phone_registration = self.store.enforce_phone_registration
        customize_info_vo.advertising_info.CopyFrom(self.merchant.advertising_info)
        customize_info_vo.display_advertising_info.CopyFrom(self.merchant.display_advertising_info)
        customize_info_vo.store_mp_image_url = self.merchant.store_mp_image_url
        customize_info_vo.enable_number_plate_pay_with_coupon_package = (
            self.merchant.enable_number_plate_pay_with_coupon_package
        )
        customize_info_vo.enable_number_plate_pay_with_marketing = self.merchant.enable_number_plate_pay_with_marketing
        customize_info_vo.enable_number_plate_splash = self.merchant.enable_number_plate_splash
        customize_info_vo.enable_number_plate_coupon_splash = self.merchant.enable_number_plate_coupon_splash
        customize_info_vo.enable_number_plate_pay_with_fanpiao = self.merchant.enable_number_plate_pay_with_fanpiao
        customize_info_vo.disable_number_plate_pay_fanpiao_pay = self.merchant.disable_number_plate_pay_fanpiao_pay
        customize_info_vo.disable_display_serial_number_after_ordering = (
            self.merchant.disable_display_serial_number_after_ordering
        )
        customize_info_vo.image_size = registration_info.ordering_config.image_size
        customize_info_vo.disable_show_ad = self.merchant.disable_show_ad
        customize_info_vo.red_packet_discount = self.merchant.preferences.red_packet_config.red_packet_discount
        customize_info_vo.shilai_extra_discount = self.merchant.preferences.red_packet_config.shilai_extra_discount
        customize_info_vo.block_rate = self.merchant.preferences.red_packet_config.block_rate
        customize_info_vo.package_type = registration_info.package_type
        customize_info_vo.operating_status = registration_info.operating_status
        customize_info_vo.is_pre_order = registration_info.is_pre_order
        customize_info_vo.is_pre_order_show_meal_code = registration_info.is_pre_order_show_meal_code
        customize_info_vo.enable_many_people_order = registration_info.ordering_config.enable_many_people_order
        customize_info_vo.enable_table_serial_number = self.merchant.enable_table_serial_number
        customize_info_vo.enable_show_membership = registration_info.ordering_config.enable_show_membership
        customize_info_vo.pay_success_ad_config.CopyFrom(self.store.pay_success_ad_config)
        return customize_info_vo

    def update_customize_info(
        self,
        business_type,
        pay_type,
        enable_payment_reminder,
        check_out_print,
        enable_invoice,
        enable_shilai_member_card_pay,
        enable_shilai_member_card_recharge,
        enable_ordering_coupon_package_union_pay,
        enable_coupon_package_refund,
        enable_fanpiao_refund,
        enable_shilai_promotion_splash,
        *args,
        **kargs,
    ):
        ordering_da = OrderingServiceDataAccessHelper()
        config_da = ConfigDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        self.update_business_type(registration_info, business_type)
        enable_many_people_order = kargs.get("enable_many_people_order")
        if enable_many_people_order is not None:
            registration_info.ordering_config.enable_many_people_order = enable_many_people_order
        if pay_type is not None:
            registration_info.pay_type = registration_pb.OrderingServiceRegistrationInfo.PayType.Value(pay_type)

        if enable_payment_reminder is not None:
            self.store.disable_payment_reminder = not enable_payment_reminder
        if check_out_print is not None:
            registration_info.printer_config.feie_printer.check_out_print = check_out_print
            registration_info.printer_config.keruyun_printer.check_out_print = check_out_print
        if enable_invoice is not None:
            self.store.enable_invoice = enable_invoice
        if enable_shilai_member_card_pay is not None:
            self.store.enable_shilai_member_card_pay = enable_shilai_member_card_pay
        if enable_shilai_member_card_recharge is not None:
            self.store.enable_shilai_member_card_recharge = enable_shilai_member_card_recharge
        if enable_ordering_coupon_package_union_pay is not None:
            self.store.enable_ordering_coupon_package_union_pay = enable_ordering_coupon_package_union_pay
        if enable_coupon_package_refund is not None:
            self.store.disable_coupon_package_refund = not enable_coupon_package_refund
        if enable_fanpiao_refund is not None:
            self.store.disable_fanpiao_refund = not enable_fanpiao_refund
        if enable_shilai_promotion_splash is not None:
            self.store.enable_shilai_promotion_splash = enable_shilai_promotion_splash

        enable_show_sold_out_dishes = kargs.get("enable_show_sold_out_dishes")
        if enable_show_sold_out_dishes is not None:
            self.store.enable_show_sold_out_dishes = enable_show_sold_out_dishes

        enable_invite_share = kargs.get("enable_invite_share")
        if enable_invite_share is not None:
            self.store.enable_invite_share = enable_invite_share

        now = int(time.time())
        enable_table_serial_number = kargs.get("enable_table_serial_number")
        if enable_table_serial_number is not None:
            self.merchant.enable_table_serial_number = enable_table_serial_number

        dish_discount_rate = kargs.get("dish_discount_rate")
        if dish_discount_rate is not None:
            self.merchant.preferences.coupon_config.dish_discount_rate = dish_discount_rate
            self.merchant.preferences.coupon_config.max_discount_update_timestamp = now

        red_packet_discount_rate = kargs.get("red_packet_discount_rate")
        if red_packet_discount_rate is not None:
            self.merchant.preferences.coupon_config.red_packet_discount_rate = red_packet_discount_rate
            self.merchant.preferences.coupon_config.max_discount_update_timestamp = now

        red_packet_discount = kargs.get('red_packet_discount')
        if red_packet_discount is not None:
            self.merchant.preferences.red_packet_config.red_packet_discount = red_packet_discount

        shilai_extra_discount = kargs.get('shilai_extra_discount')
        if shilai_extra_discount is not None:
            self.merchant.preferences.red_packet_config.shilai_extra_discount = shilai_extra_discount

        block_rate = kargs.get('block_rate')
        if block_rate is not None:
            self.merchant.preferences.red_packet_config.block_rate = block_rate

        enable_subsidies_overflow_fee = kargs.get("enable_subsidies_overflow_fee")
        if enable_subsidies_overflow_fee is not None:
            self.store.enable_subsidies_overflow_fee = enable_subsidies_overflow_fee

        disable_show_fanpiao_purchase_number = kargs.get("disable_show_fanpiao_purchase_number")
        if disable_show_fanpiao_purchase_number is not None:
            self.fanpiao_config.disable_show_fanpiao_purchase_number = disable_show_fanpiao_purchase_number

        disable_show_fanpiao_price = kargs.get('disable_show_fanpiao_price')
        if disable_show_fanpiao_price is not None:
            self.fanpiao_config.disable_show_fanpiao_price = disable_show_fanpiao_price

        disable_show_coupon_package_purchase_number = kargs.get("disable_show_coupon_package_purchase_number")
        if disable_show_coupon_package_purchase_number is not None:
            self.coupon_package_config.disable_show_coupon_package_purchase_number = disable_show_coupon_package_purchase_number

        fanpiao_sales_boost_factor = kargs.get("fanpiao_sales_boost_factor")
        if fanpiao_sales_boost_factor is not None:
            self.fanpiao_config.fanpiao_sales_boost_factor = fanpiao_sales_boost_factor

        enable_show_time_limit_sale = kargs.get("enable_show_time_limit_sale")
        if enable_show_time_limit_sale is not None:
            self.ordering_config.enable_show_time_limit_sale = enable_show_time_limit_sale

        enable_dish_incremental = kargs.get("enable_dish_incremental")
        if enable_dish_incremental is not None:
            self.ordering_config.enable_dish_incremental = enable_dish_incremental

        enable_firstly_addon = kargs.get("enable_firstly_addon")
        if enable_firstly_addon is not None:
            self.ordering_config.enable_firstly_addon = enable_firstly_addon

        banner_mode = kargs.get("banner_mode")
        if banner_mode is not None:
            self.store.banner_mode = merchant_rules_pb.Store.BannerMode.Value(banner_mode)

        disable_show_sold_number = kargs.get("disable_show_sold_number")
        if disable_show_sold_number is not None:
            self.ordering_config.disable_show_sold_number = disable_show_sold_number

        meal_code_base_value = kargs.get("meal_code_base_value")
        if meal_code_base_value is not None:
            registration_info.meal_code_base_value = meal_code_base_value

        serial_number_base_value = kargs.get("serial_number_base_value")
        if serial_number_base_value is not None:
            registration_info.serial_number_base_value = serial_number_base_value

        meal_code_max_value = kargs.get("meal_code_max_value")
        if meal_code_max_value is not None:
            registration_info.meal_code_max_value = meal_code_max_value

        serial_number_max_value = kargs.get("serial_number_max_value")
        if serial_number_max_value is not None:
            registration_info.serial_number_max_value = serial_number_max_value

        enterprise_wechat_number = kargs.get("enterprise_wechat_number")
        if enterprise_wechat_number is not None:
            self.merchant.enterprise_wechat_number = enterprise_wechat_number

        enable_auto_recover_remain_quantity = kargs.get("enable_auto_recover_remain_quantity")
        if enable_auto_recover_remain_quantity is not None:
            registration_info.ordering_config.enable_auto_recover_remain_quantity = enable_auto_recover_remain_quantity

        enable_auto_recover_sold_out = kargs.get("enable_auto_recover_sold_out")
        if enable_auto_recover_sold_out is not None:
            registration_info.ordering_config.enable_auto_recover_sold_out = enable_auto_recover_sold_out

        enable_auto_recover_attr_sold_out = kargs.get("enable_auto_recover_attr_sold_out")
        if enable_auto_recover_attr_sold_out is not None:
            registration_info.ordering_config.enable_auto_recover_attr_sold_out = enable_auto_recover_attr_sold_out

        enable_auto_recover_supply_condiments_sold_out = kargs.get("enable_auto_recover_supply_condiments_sold_out")
        if enable_auto_recover_supply_condiments_sold_out is not None:
            registration_info.ordering_config.enable_auto_recover_supply_condiments_sold_out = (
                enable_auto_recover_supply_condiments_sold_out
            )

        enforce_phone_registration = kargs.get("enforce_phone_registration")
        if enforce_phone_registration is not None:
            self.store.enforce_phone_registration = enforce_phone_registration

        advertising_info = kargs.get("advertising_info")
        if advertising_info is not None:
            self._update_merchant_advertising_info(advertising_info)

        pay_success_ad_config = kargs.get("pay_success_ad_config")
        if pay_success_ad_config is not None:
            type = pay_success_ad_config.get("type", "IMAGE")
            type = merchant_rules_pb.AdvertisingInfo.AdvertisingContent.Type.Value(type)
            jump_type = pay_success_ad_config.get("jumpType", "NONE")
            jump_type = merchant_rules_pb.AdvertisingInfo.AdvertisingContent.JumpType.Value(jump_type)
            jump_content = pay_success_ad_config.get("jumpContent")
            advertise_content = pay_success_ad_config.get("content")
            self.store.pay_success_ad_config.type = type
            self.store.pay_success_ad_config.jump_type = jump_type
            self.store.pay_success_ad_config.content = advertise_content
            self.store.pay_success_ad_config.jump_content = jump_content

        display_advertising_info = kargs.get("display_advertising_info")
        if display_advertising_info is not None:
            self._update_merchant_display_advertising_info(display_advertising_info)

        store_mp_image_url = kargs.get("store_mp_image_url")
        if store_mp_image_url is not None:
            self.merchant.store_mp_image_url = store_mp_image_url

        enable_number_plate_pay_with_marketing = kargs.get("enable_number_plate_pay_with_marketing")
        if enable_number_plate_pay_with_marketing is not None:
            self.merchant.enable_number_plate_pay_with_marketing = enable_number_plate_pay_with_marketing

        enable_number_plate_pay_with_fanpiao = kargs.get("enable_number_plate_pay_with_fanpiao")
        if enable_number_plate_pay_with_fanpiao is not None:
            self.merchant.enable_number_plate_pay_with_fanpiao = enable_number_plate_pay_with_fanpiao

        disable_number_plate_pay_fanpiao_pay = kargs.get("disable_number_plate_pay_fanpiao_pay")
        if disable_number_plate_pay_fanpiao_pay is not None:
            self.merchant.disable_number_plate_pay_fanpiao_pay = disable_number_plate_pay_fanpiao_pay

        enable_number_plate_splash = kargs.get("enable_number_plate_splash")
        if enable_number_plate_splash is not None:
            self.merchant.enable_number_plate_splash = enable_number_plate_splash

        enable_number_plate_coupon_splash = kargs.get("enable_number_plate_coupon_splash")
        if enable_number_plate_coupon_splash is not None:
            self.merchant.enable_number_plate_coupon_splash = enable_number_plate_coupon_splash

        enable_number_plate_pay_with_coupon_package = kargs.get("enable_number_plate_pay_with_coupon_package")
        if enable_number_plate_pay_with_coupon_package is not None:
            self.merchant.enable_number_plate_pay_with_coupon_package = enable_number_plate_pay_with_coupon_package

        enable_fanpiao_balance_refund = kargs.get("enable_fanpiao_balance_refund", None)
        if enable_fanpiao_balance_refund is not None:
            self.fanpiao_config.enable_fanpiao_balance_refund = enable_fanpiao_balance_refund

        disable_display_serial_number_after_ordering = kargs.get('disable_display_serial_number_after_ordering', None)
        if disable_display_serial_number_after_ordering is not None:
            self.merchant.disable_display_serial_number_after_ordering = disable_display_serial_number_after_ordering

        balance_refund_method = kargs.get("balance_refund_method", None)
        if balance_refund_method is not None:
            if isinstance(balance_refund_method, str):
                balance_refund_method = config_pb.FanpiaoConfig.BalanceRefundMethod.Value(balance_refund_method)
            self.fanpiao_config.balance_refund_method = balance_refund_method

        is_refund_the_way = kargs.get("is_refund_the_way", None)
        if is_refund_the_way is not None:
            self.fanpiao_config.is_refund_the_way = is_refund_the_way

        image_size = kargs.get('image_size')
        if image_size is not None:
            if image_size == registration_pb.OrderingConfig.ImageSize.Name(registration_pb.OrderingConfig.ImageSize.NORMAL):
                registration_info.ordering_config.image_size = registration_pb.OrderingConfig.ImageSize.NORMAL
            elif image_size == registration_pb.OrderingConfig.ImageSize.Name(registration_pb.OrderingConfig.ImageSize.LARGE):
                registration_info.ordering_config.image_size = registration_pb.OrderingConfig.ImageSize.LARGE
        disable_show_ad = kargs.get('disable_show_ad')
        if disable_show_ad is not None:
            self.merchant.disable_show_ad = disable_show_ad
        if kargs.get('package_type') is not None:
            registration_info.package_type = registration_pb.OrderingServiceRegistrationInfo.PackageType.Value(
                kargs.get('package_type')
            )

        operating_status = kargs.get('operating_status')
        if operating_status is not None:
            registration_info.operating_status = registration_pb.OrderingServiceRegistrationInfo.OperatingStatus.Value(
                operating_status
            )

        is_pre_order = kargs.get('is_pre_order')
        if is_pre_order is not None:
            registration_info.is_pre_order = is_pre_order

        is_pre_order_show_meal_code = kargs.get('is_pre_order_show_meal_code')
        if is_pre_order_show_meal_code is not None:
            registration_info.is_pre_order_show_meal_code = is_pre_order_show_meal_code

        enable_show_membership = kargs.get('enable_show_membership')
        if enable_show_membership is not None:
            registration_info.ordering_config.enable_show_membership = enable_show_membership

        self.do_update_registration_info(registration_info)
        self.do_update_merchant(registration_info if serial_number_base_value is not None else None)
        self.do_update_member_card_recharge_rule(kargs.get('member_card_recharge_rule', []))
        config_da.add_or_update_fanpiao_config(self.fanpiao_config)
        config_da.add_or_update_coupon_package_config(self.coupon_package_config)
        config_da.add_or_update_ordering_config(self.ordering_config)
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_fanpiao_update_timestamp=now)

    def do_update_member_card_recharge_rule(self, rules):
        membership_da = MembershipDataAccessHelper()
        if rules is None:
            return
        for rule in rules:
            config = json_format.ParseDict(rule, membership_pb.MemberCardRechargeConfig(), ignore_unknown_fields=True)
            config.id = config.id or id_manager.generate_common_id()
            config.merchant_id = config.merchant_id or self.merchant.id
            membership_da.add_or_update_member_card_recharge_config(config)

    def _update_merchant_display_advertising_info(self, display_advertising_info):
        if display_advertising_info is None:
            return
        display_advertising = display_advertising_info.get("displayAdvertising")
        if display_advertising is not None:
            self.merchant.display_advertising_info.display_advertising = display_advertising

        merchant_ids = display_advertising_info.get("merchantIds")
        if merchant_ids is not None:
            while self.merchant.display_advertising_info.merchant_ids:
                self.merchant.display_advertising_info.merchant_ids.pop()
            self.merchant.display_advertising_info.merchant_ids.extend(merchant_ids)

        max_distance = display_advertising_info.get("maxDistance")
        if max_distance is not None:
            self.merchant.display_advertising_info.max_distance = max_distance

    def update_business_type(self, registration_info=None, business_type=None):
        if business_type is None:
            return
        if registration_info is None:
            registration_info = self.registration_info
        if business_type == "FAST_FOOD":
            registration_info.ordering_config.dinner_type = registration_pb.OrderingConfig.FAST_FOOD
        elif business_type == "DINNER":
            registration_info.ordering_config.dinner_type = registration_pb.OrderingConfig.DINNER
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_fanpiao_update_timestamp=now)

    def update_splash_image(self, splash_image_url, splash_mode):
        if splash_mode is None:
            return
        if splash_image_url is not None:
            self.store.splash_image_url = splash_image_url
        self.store.splash_mode = merchant_rules_pb.Store.SplashMode.Value(splash_mode)
        self.do_update_merchant()

    def get_ordering_info(self):
        ordering_info_vo = staff_merchant_pb.OrderingInfoVO()
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        if not registration_info:
            registration_info = registration_pb.OrderingServiceRegistrationInfo()
            registration_info.merchant_id = self.merchant.id
            registration_info.ordering_config.enable_eat_in = True
            registration_info.package_type = registration_pb.OrderingServiceRegistrationInfo.PackageType.TAKE_AWAY
            self.do_update_registration_info(registration_info)

        ordering_info_vo.packaging_box_config.CopyFrom(registration_info.packaging_box_config)
        for item in registration_info.ordering_config.required_order_items:
            if item.name == "配送费":
                ordering_info_vo.shipping_fee = item.price
                ordering_info_vo.enable_shipping_fee = True
            item_vo = ordering_info_vo.required_order_items.add()
            item_vo.CopyFrom(item)

        ordering_info_vo.enable_eat_in = registration_info.ordering_config.enable_eat_in
        ordering_info_vo.enable_take_away = registration_info.ordering_config.enable_take_away
        ordering_info_vo.enable_self_pick_up = registration_info.ordering_config.enable_self_pick_up
        ordering_info_vo.enable_take_out = registration_info.ordering_config.enable_take_out
        ordering_info_vo.enable_people_count = not self.store.disable_people_count
        ordering_info_vo.minimal_bill_fee = registration_info.ordering_config.minimal_bill_fee
        ordering_info_vo.max_take_out_distance = registration_info.max_take_out_distance
        ordering_info_vo.take_out_commission_rate = registration_info.take_out_commission_rate
        ordering_info_vo.take_away_commission_rate = registration_info.take_away_commission_rate
        ordering_info_vo.self_pick_up_commission_rate = registration_info.self_pick_up_commission_rate
        ordering_info_vo.show_full_mobile_phone = registration_info.show_full_mobile_phone
        ordering_info_vo.package_box_type = self.store.package_box_type
        return ordering_info_vo

    def update_ordering_info(
        self,
        enable_eat_in,
        enable_take_away,
        enable_self_pick_up,
        enable_take_out,
        enable_people_count,
        required_order_items,
        packaging_box_config,
        shipping_fee,
        minimal_bill_fee,
        max_take_out_distance,
        take_out_commission_rate,
        take_away_commission_rate,
        self_pick_up_commission_rate,
        enable_shipping_fee,
        *args,
        **kargs,
    ):
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        self._set_required_order_items(required_order_items, registration_info)
        self._set_packaging_box_fees(packaging_box_config, registration_info)
        self._set_shipping_fee(enable_shipping_fee, registration_info)
        if enable_eat_in is not None:
            registration_info.ordering_config.enable_eat_in = enable_eat_in
        if enable_take_away is not None:
            registration_info.ordering_config.enable_take_away = enable_take_away
        if enable_self_pick_up is not None:
            registration_info.ordering_config.enable_self_pick_up = enable_self_pick_up
        if enable_take_out is not None:
            registration_info.ordering_config.enable_take_out = enable_take_out
        if enable_people_count is not None:
            self.store.disable_people_count = not enable_people_count
        if minimal_bill_fee is not None:
            registration_info.ordering_config.minimal_bill_fee = minimal_bill_fee
        if max_take_out_distance is not None:
            registration_info.max_take_out_distance = max_take_out_distance
        if take_out_commission_rate is not None:
            registration_info.take_out_commission_rate = take_out_commission_rate
        if take_away_commission_rate is not None:
            registration_info.take_away_commission_rate = take_away_commission_rate
        if self_pick_up_commission_rate is not None:
            registration_info.self_pick_up_commission_rate = self_pick_up_commission_rate
        if kargs.get('show_full_mobile_phone') is not None:
            registration_info.show_full_mobile_phone = bool(kargs.get('show_full_mobile_phone'))
        if kargs.get("package_box_type") is not None:
            self.store.package_box_type = merchant_rules_pb.Store.PackageBoxType.Value(kargs.get("package_box_type"))
        self.do_update_registration_info(registration_info)
        self.do_update_merchant()
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_fanpiao_update_timestamp=now)

    def _set_shipping_fee(self, enable_shipping_fee, registration_info):
        if enable_shipping_fee is None:
            return
        if not enable_shipping_fee:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        dish = ordering_da.get_dish_by_merchant_id_name(merchant_id=self.merchant.id, name="配送费")
        if not dish:
            return
        dish = dish[0]
        saved_required_order_items = registration_info.ordering_config.required_order_items
        for item in saved_required_order_items:
            if item.name == "配送费":
                return
        item_p = saved_required_order_items.add()
        item_p.name = dish.name
        item_p.price = int(dish.price)
        item_p.id = dish.id
        item_p.type = registration_pb.RequiredOrderItem.ONLY_ONE

    def _set_packaging_box_fees(self, packaging_box_config, registration_info):
        if packaging_box_config is None:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        saved_packaging_box_config = registration_info.packaging_box_config
        while saved_packaging_box_config:
            if len(saved_packaging_box_config.order_bill_fee) == 0:
                break
            saved_packaging_box_config.order_bill_fee.pop(0)
        box_fees = packaging_box_config.get("orderBillFee")
        if isinstance(box_fees, str):
            box_fees = box_fees.split(",")
        if len(box_fees) == 0:
            return
        # dish_name = packaging_box_config.get("name", "打包盒")
        dish = ordering_da.get_dish_by_merchant_id_name(merchant_id=self.merchant.id, name="打包盒")
        if not dish:
            return
        dish = dish[0]
        saved_packaging_box_config.dish_id = dish.id
        for box_fee in box_fees:
            if box_fee == "":
                continue
            registration_info.packaging_box_config.order_bill_fee.append(int(box_fee))

    def _set_required_order_items(self, required_order_items, registration_info):
        if required_order_items is None:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        saved_required_order_items = registration_info.ordering_config.required_order_items
        while len(saved_required_order_items) > 0:
            saved_required_order_items.pop(0)
        for item in required_order_items:
            name = item.get("name")
            dish = ordering_da.get_dish_by_merchant_id_name(name=name, merchant_id=self.merchant.id)
            if not dish:
                continue
            item_p = registration_info.ordering_config.required_order_items.add()
            dish = dish[0]
            item_p.name = dish.name
            item_p.price = int(dish.price)
            item_p.id = dish.id
            item_p.type = registration_pb.RequiredOrderItem.Type.Value(item.get("type"))

    def update_pos_info(self, **kargs):
        pos_type = kargs.get("pos_type")
        pos_type = registration_pb.OrderingServiceRegistrationInfo.PosType.Value(pos_type)
        kargs.update({"pos_type": pos_type})
        if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            self.update_keruyun_pos_info(**kargs)
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            self.update_hualala_pos_info(**kargs)
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            self.add_feie_pos_info(**kargs)
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            self.registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.SHILAI
            kargs.update({"registration_info": self.registration_info})
            self.update_shilai_pos_info(**kargs)
        self.update_ordering_config(**kargs)
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_fanpiao_update_timestamp=now)
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_ordering_config(self.ordering_config)

    def update_ordering_config(self, **kargs):
        if not self.ordering_config:
            return
        enable_x_sign_before_amount = kargs.get("enable_x_sign_before_amount")
        if enable_x_sign_before_amount is not None:
            self.ordering_config.enable_x_sign_before_amount = enable_x_sign_before_amount

    def get_pos_info(self):
        ordering_da = OrderingServiceDataAccessHelper()
        printer_config_da = PrinterConfigDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        pos_info_vo = staff_merchant_pb.PosInfoVO()
        if registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            pos_info_vo.shop_id = registration_info.keruyun_pos_info.shop_id
        elif registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            pos_info_vo.shop_id = registration_info.hualala_pos_info.shop_id
        pos_info_vo.long_password = registration_info.keruyun_pos_info.long_password
        pos_info_vo.short_password = registration_info.keruyun_pos_info.short_password
        pos_info_vo.account = registration_info.keruyun_pos_info.account
        pos_info_vo.merchant_print = registration_info.printer_config.feie_printer.merchant_print
        pos_info_vo.kitchen_print = registration_info.printer_config.feie_printer.kitchen_print
        pos_info_vo.one_dish_cut = registration_info.printer_config.feie_printer.one_dish_cut
        pos_info_vo.pos_type = registration_info.pos_type
        pos_info_vo.enable_dish_sort = registration_info.printer_config.feie_printer.enable_dish_sort
        for sn in registration_info.printer_config.feie_printer.printer_sns:
            config = printer_config_da.get_printer_config(printer_sn=sn)
            if not config:
                continue
            print_format_type = registration_pb.PrinterConfigByType.Type.Name(config.type)
            if registration_info.printer_config.feie_printer.enable_tag_print:
                if sn == registration_info.printer_config.feie_printer.tag_printer_sn:
                    pos_info_vo.feie_printer_sns.update({sn: "tag," + print_format_type})
                elif sn in registration_info.printer_config.feie_printer.tag_printer_sns:
                    pos_info_vo.feie_printer_sns.update({sn: "tag," + print_format_type})
                else:
                    pos_info_vo.feie_printer_sns.update({sn: "receipt," + print_format_type})
            else:
                pos_info_vo.feie_printer_sns.update({sn: "receipt," + print_format_type})
        if self.ordering_config:
            pos_info_vo.enable_x_sign_before_amount = self.ordering_config.enable_x_sign_before_amount
        return pos_info_vo

    def update_shilai_pos_info(self, **kargs):
        manager = ShopManager(merchant=self.merchant, registration_info=self.registration_info)
        registration_info = manager.register(**kargs)
        self.do_update_registration_info(registration_info)

    def add_feie_pos_info(self, **kargs):
        """云打印机的配置"""
        feie_printer_sns = kargs.get("feie_printer_sns", [])
        one_dish_cut = kargs.get("one_dish_cut")
        if one_dish_cut is not None:
            self.registration_info.printer_config.feie_printer.one_dish_cut = one_dish_cut
            self.registration_info.printer_config.feie_printer.cut = True
        kitchen_print = kargs.get("kitchen_print")
        if kitchen_print is not None:
            self.registration_info.printer_config.feie_printer.kitchen_print = kitchen_print
        enable_dish_sort = kargs.get("enable_dish_sort")
        if enable_dish_sort is not None:
            self.registration_info.printer_config.feie_printer.enable_dish_sort = enable_dish_sort
        merchant_print = kargs.get("merchant_print")

        if merchant_print is not None:
            self.registration_info.printer_config.feie_printer.merchant_print = merchant_print
        if feie_printer_sns and len(feie_printer_sns) > 0:
            self.registration_info.printer_config.feie_printer.printer_sn = feie_printer_sns[0].get("sn")
            """
            while self.registration_info.printer_config.feie_printer.printer_sns:
                self.registration_info.printer_config.feie_printer.printer_sns.pop()
            self.registration_info.printer_config.feie_printer.printer_sn_keys.clear()
            """
            for info in feie_printer_sns:
                sn = info.get("sn")
                key = info.get("key")
                print_format_type = info.get("printFormatType") if info.get("printFormatType") else "FEIE"
                printer_type = info.get("printerType")
                if sn == "":
                    continue

                # 查询打印机是否已经和系统中其他商户有绑定过（即非全新打印机）
                matcher = {'printerConfig.feiePrinter.printerSns': sn}
                ordering_da = OrderingServiceDataAccessHelper()
                registrations = ordering_da.get_registration_info_by_matcher(matcher)
                if registrations:
                    message = "打印机 sn={} 已绑定其他商户，请先解绑!".format(sn)
                    raise errors.Error(err=(220013, message))
                remarker = self.merchant.basic_info.name
                if print_format_type == "FEIE":
                    if key == "":
                        key = self.registration_info.printer_config.feie_printer.printer_sn_keys.get(key, "")
                    if key == "":
                        self.registration_info.printer_config.feie_printer.printer_sns.append(sn)
                        self.registration_info.printer_config.feie_printer.printer_sn_keys.update({sn: key})
                        continue
                    if key[-1] == "\n":
                        key = key[:-1]
                    if sn[-1] == "\n":
                        sn = sn[:-1]
                    ret = PrinterManager(registration_info=self.registration_info).add_feie_printer(sn, key, remarker)
                    logger.info("添加飞鹅打印机返回: {}".format(ret))
                    msg = ret.get("data", {}).get("no", [""])[0]
                    if len(msg) > 0:
                        if "错误" in msg and "已被添加过" not in msg:
                            m = re.search('(?<=错误：)(.*)', msg)
                            if not m:
                                raise errors.ShowError(msg)
                            m = m.group(0)
                            raise errors.ShowError(m)
                else:
                    ret = PrinterManager(registration_info=self.registration_info).add_xprinter(sn, remarker)
                    logger.info("添加芯烨打印机返回: {}".format(ret))
                    fail = ret.get("data", {}).get("fail")
                    if len(fail) > 0:
                        fail_msg = ret.get("data", {}).get("failMsg")[0]
                        if "1011" not in fail_msg:
                            if "1010" in fail_msg:
                                raise errors.ShowError("打印机设备编号无效")
                            raise errors.ShowError(fail_msg)

                if printer_type == "tag":
                    self.registration_info.printer_config.feie_printer.enable_tag_print = True
                    if sn not in self.registration_info.printer_config.feie_printer.tag_printer_sns:
                        self.registration_info.printer_config.feie_printer.tag_printer_sns.append(sn)
                if sn not in self.registration_info.printer_config.feie_printer.printer_sns:
                    self.registration_info.printer_config.feie_printer.printer_sns.append(sn)
                self.registration_info.printer_config.feie_printer.printer_sn_keys.update({sn: key})
                formater = FeiePrintFormat()
                formater.new_printer_config(sn, self.registration_info, print_format_type)
        self.registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.FEIE
        self.do_update_registration_info(self.registration_info)

    def amend_feie_pos_info(self, feie_printer_sns):
        # one_dish_cut = kargs.get("one_dish_cut")
        # if one_dish_cut is not None:
        #     self.registration_info.printer_config.feie_printer.one_dish_cut = one_dish_cut
        #     self.registration_info.printer_config.feie_printer.cut = True
        # kitchen_print = kargs.get("kitchen_print")
        # if kitchen_print is not None:
        #     self.registration_info.printer_config.feie_printer.kitchen_print = kitchen_print
        # merchant_print = kargs.get("merchant_print")
        # if merchant_print is not None:
        #     self.registration_info.printer_config.feie_printer.merchant_print = merchant_print
        formater = FeiePrintFormat()
        if feie_printer_sns is not None:
            for info in feie_printer_sns:
                sn = info.get("sn")
                print_format_type = info.get("printFormatType", "FEIE")
                printer_type = info.get("printerType")
                if printer_type == "tag":
                    if sn not in self.registration_info.printer_config.feie_printer.tag_printer_sns:
                        self.registration_info.printer_config.feie_printer.tag_printer_sns.append(sn)
                        self.registration_info.printer_config.feie_printer.enable_tag_print = True
                else:
                    if sn in self.registration_info.printer_config.feie_printer.tag_printer_sns:
                        self.registration_info.printer_config.feie_printer.tag_printer_sns.remove(sn)
                        if not self.registration_info.printer_config.feie_printer.tag_printer_sns:
                            self.registration_info.printer_config.feie_printer.enable_tag_print = False
                formater.new_printer_config(sn, self.registration_info, print_format_type)
            self.do_update_registration_info(self.registration_info)

    def update_feie_pos_info(self, **kargs):
        feie_printer_sns = kargs.get("feie_printer_sns", [])
        one_dish_cut = kargs.get("one_dish_cut")
        if one_dish_cut is not None:
            self.registration_info.printer_config.feie_printer.one_dish_cut = one_dish_cut
            self.registration_info.printer_config.feie_printer.cut = True
        kitchen_print = kargs.get("kitchen_print")
        if kitchen_print is not None:
            self.registration_info.printer_config.feie_printer.kitchen_print = kitchen_print
        merchant_print = kargs.get("merchant_print")
        if merchant_print is not None:
            self.registration_info.printer_config.feie_printer.merchant_print = merchant_print
        if feie_printer_sns and len(feie_printer_sns) > 0:
            self.registration_info.printer_config.feie_printer.printer_sn = feie_printer_sns[0].get("sn")
            while self.registration_info.printer_config.feie_printer.printer_sns:
                self.registration_info.printer_config.feie_printer.printer_sns.pop()
            self.registration_info.printer_config.feie_printer.printer_sn_keys.clear()
            for info in feie_printer_sns:
                sn = info.get("sn")
                key = info.get("key")
                printer_type = info.get("printerType")
                force_bond = info.get("forceBond", False)
                if sn == "":
                    continue
                if key == "":
                    key = self.registration_info.printer_config.feie_printer.printer_sn_keys.get(key, "")
                if key == "":
                    self.registration_info.printer_config.feie_printer.printer_sns.append(sn)
                    self.registration_info.printer_config.feie_printer.printer_sn_keys.update({sn: key})
                    continue
                if key[-1] == "\n":
                    key = key[:-1]
                if sn[-1] == "\n":
                    sn = sn[:-1]
                remarker = self.merchant.basic_info.name
                formater = FeiePrintFormat()
                if not force_bond:
                    ret = PrinterManager(registration_info=self.registration_info).add_feie_printer(sn, key, remarker)
                    # {'msg': 'ok', 'ret': 0, 'data': {'ok': [], 'no': ['570802397#2vrjh2d6#清华楼兰州牛肉面（高碑店店） （错误：已被添加过）']}, 'serverExecutedTime': 14}
                    logger.info("添加飞鹅打印机返回: {}".format(ret))
                    msg = ret.get("data", {}).get("no", [""])[0]
                    if len(msg) > 0:
                        if "错误" in msg and "已被添加过" not in msg:
                            m = re.search('(?<=错误：)(.*)', msg)
                            if not m:
                                raise errors.ShowError(msg)
                            m = m.group(0)
                            raise errors.ShowError(m)
                        # if "已被添加过" in msg:
                        #     raise errors.Error(err=error_codes.PRINTER_IS_BEEN_BOND)
                else:
                    matcher = {'printerConfig.feiePrinter.printerSns': sn}
                    ordering_da = OrderingServiceDataAccessHelper()
                    registrations = ordering_da.get_registration_info_by_matcher(matcher)
                    for registration in registrations:
                        if registration.printer_config.feie_printer.printer_sn == sn:
                            registration.printer_config.feie_printer.printer_sn = ""
                        if registration.printer_config.feie_printer.tag_printer_sn == sn:
                            registration.printer_config.feie_printer.tag_printer_sn = ""
                        if sn in registration.printer_config.feie_printer.tag_printer_sns:
                            registration.printer_config.feie_printer.tag_printer_sns.remove(sn)
                        if not registration.printer_config.feie_printer.tag_printer_sns:
                            registration.printer_config.feie_printer.enable_tag_print = False
                        registration.printer_config.feie_printer.printer_sns.remove(sn)
                        registration.printer_config.feie_printer.printer_sn_keys.pop(sn)
                        ordering_da.add_or_update_registration_info(registration)
                        formater.delete_printer_config(sn, registration)
                if printer_type == "tag":
                    self.registration_info.printer_config.feie_printer.enable_tag_print = True
                    if sn not in self.registration_info.printer_config.feie_printer.tag_printer_sns:
                        self.registration_info.printer_config.feie_printer.tag_printer_sns.append(sn)
                if sn not in self.registration_info.printer_config.feie_printer.printer_sns:
                    self.registration_info.printer_config.feie_printer.printer_sns.append(sn)
                self.registration_info.printer_config.feie_printer.printer_sn_keys.update({sn: key})

                formater.new_printer_config(sn, self.registration_info, kargs.get("print_format_type"))
        self.registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.FEIE
        self.do_update_registration_info(self.registration_info)

    def delete_feie_pos_info(self, printer_sn):
        matcher = {'printerConfig.feiePrinter.printerSns': printer_sn}
        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.delete_dish_category_printer(self.merchant.id, printer_sn)
        formater = FeiePrintFormat()
        registrations = ordering_da.get_registration_info_by_matcher(matcher)
        for registration in registrations:
            if registration.printer_config.feie_printer.printer_sn == printer_sn:
                registration.printer_config.feie_printer.printer_sn = ""
            if registration.printer_config.feie_printer.tag_printer_sn == printer_sn:
                registration.printer_config.feie_printer.tag_printer_sn = ""
            if printer_sn in registration.printer_config.feie_printer.tag_printer_sns:
                registration.printer_config.feie_printer.tag_printer_sns.remove(printer_sn)
            if not registration.printer_config.feie_printer.tag_printer_sns:
                registration.printer_config.feie_printer.enable_tag_print = False
            registration.printer_config.feie_printer.printer_sns.remove(printer_sn)
            registration.printer_config.feie_printer.printer_sn_keys.pop(printer_sn)
            ordering_da.add_or_update_registration_info(registration)
            formater.delete_printer_config(printer_sn, registration)

    def update_keruyun_pos_info(self, **kargs):
        shop_id = kargs.get("shop_id")
        short_password = kargs.get("short_password")
        long_password = kargs.get("long_password")
        account = kargs.get("account")
        pos_type = kargs.get("pos_type")
        ordering_da = OrderingServiceDataAccessHelper()

        registration_info = ordering_da.get_registration_info(self.merchant.id)
        registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.KERUYUN
        manager = ShopManager(merchant=self.merchant, registration_info=registration_info)
        registration_info = manager.register(shop_id=shop_id, pos_type=pos_type)
        registration_info.keruyun_pos_info.shop_id = shop_id
        registration_info.keruyun_pos_info.short_password = short_password
        registration_info.keruyun_pos_info.long_password = long_password
        registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.KERUYUN
        if account is not None:
            registration_info.keruyun_pos_info.account = account

        self.do_update_registration_info(registration_info)

    def update_hualala_pos_info(self, **kargs):
        group_id = kargs.get("group_id")
        if group_id is None:
            group_id = str(HualalaConstants.GROUP_ID)
        shop_id = kargs.get("shop_id")
        pos_type = registration_pb.OrderingServiceRegistrationInfo.HUALALA
        app_secret = kargs.get("app_secret")
        if app_secret is None:
            app_secret = HualalaConstants.APP_SECRET
        app_key = kargs.get("app_key")
        if app_key is None:
            app_key = HualalaConstants.APP_KEY
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(self.merchant.id)
        registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.HUALALA
        manager = ShopManager(
            merchant=self.merchant, registration_info=registration_info, app_key=app_key, app_secret=app_secret
        )
        registration_info = manager.register(
            group_id, shop_id, app_secret=app_secret, app_key=app_key, registration_info=registration_info, pos_type=pos_type
        )
        registration_info.pos_type = registration_pb.OrderingServiceRegistrationInfo.HUALALA
        self.do_update_registration_info(registration_info)

    def get_merchant_payment(self):
        payment_vo = staff_merchant_pb.PaymentInfoVO()
        for url in self.store.store_front_photo_urls:
            payment_vo.store_front_photo_urls.append(url)
        for url in self.store.store_photo_urls:
            payment_vo.store_photo_urls.append(url)
        payment_vo.license_photo_url = self.merchant.merchant_qualifications.license_photo_url
        payment_vo.food_distribution_permit_url = self.merchant.merchant_qualifications.food_distribution_permit_url
        payment_vo.id_front_photo = self.merchant.merchant_qualifications.id_front_photo
        payment_vo.id_back_photo = self.merchant.merchant_qualifications.id_back_photo
        payment_vo.bank_card_front_photo_url = self.merchant.payment_info.bank_card_front_photo_url
        payment_vo.bank_account_type = self.merchant.payment_info.bank_account_type
        payment_vo.bank_name = self.merchant.payment_info.bank_name
        payment_vo.account_number = self.merchant.payment_info.account_number
        payment_vo.account_name = self.merchant.payment_info.account_name
        payment_vo.settlement_rate = self.merchant.payment_info.settlement_rate
        payment_vo.bank_city = self.merchant.payment_info.bank_city
        payment_vo.ledger_info_url = self.merchant.payment_info.ledger_info_url
        tian_que_pay_manager = TianQuePayManager(merchant=self.merchant)
        if tian_que_pay_manager.tian_que_pay_info:
            payment_vo.mno = tian_que_pay_manager.tian_que_pay_info.mno
            payment_vo.prev_mno = tian_que_pay_manager.tian_que_pay_info.prev_mno
            payment_vo.next_mno = tian_que_pay_manager.tian_que_pay_info.next_mno
            self.set_payment_vo_tian_que_pay_message(tian_que_pay_manager, payment_vo)
        for photo in self.merchant.merchant_qualifications.id_front_photos:
            payment_vo.id_front_photos.append(photo)
        for photo in self.merchant.merchant_qualifications.id_back_photos:
            payment_vo.id_back_photos.append(photo)
        for photo in self.merchant.payment_info.bank_card_front_photo_urls:
            payment_vo.bank_card_front_photo_urls.append(photo)
        return payment_vo

    def set_payment_vo_tian_que_pay_message(self, tian_que_pay_manager, payment_vo):
        ret0 = tian_que_pay_manager.query_ledger_set_mno_array(mno=payment_vo.next_mno)
        ret1 = tian_que_pay_manager.query_sign_contract(mno=payment_vo.next_mno)
        messages = []
        if ret1.get("code") == "0000" and ret1.get("respData", {}).get("bizCode") == "0000":
            messages.append("分账签约成功")
        else:
            messages.append("分账签约失败")
        if ret0.get("code") == "0000" and ret0.get("respData", {}).get("bizCode") == "0000":
            messages.append("分账授权到时来成功")
        else:
            messages.append("分账授权到时来失败")
        messages.append(f"\n支付宝门店ID: {self.merchant.alipay_merchant_shop_info.id}")
        payment_vo.tian_que_pay_message = ";".join(messages)

    def update_merchant_payment(
        self,
        store_front_photo_urls,
        store_photo_urls,
        license_photo_url,
        food_distribution_permit_url,
        id_front_photo,
        id_back_photo,
        bank_card_front_photo_url,
        bank_account_type,
        bank_name,
        branch_name,
        account_number,
        account_name,
        settlement_rate,
        bank_city,
        *args,
        **kargs,
    ):
        self._set_store_front_photo_urls(store_front_photo_urls)
        self._set_store_photo_urls(store_photo_urls)
        if license_photo_url is not None:
            self.merchant.merchant_qualifications.license_photo_url = license_photo_url
        if food_distribution_permit_url is not None:
            self.merchant.merchant_qualifications.food_distribution_permit_url = food_distribution_permit_url
        if id_front_photo is not None:
            self.merchant.merchant_qualifications.id_front_photo = id_front_photo
        if id_back_photo is not None:
            self.merchant.merchant_qualifications.id_back_photo = id_back_photo
        if bank_card_front_photo_url is not None:
            self.merchant.payment_info.bank_card_front_photo_url = bank_card_front_photo_url
        if bank_account_type is not None:
            self.merchant.payment_info.bank_account_type = merchant_rules_pb.AccountType.Value(bank_account_type)
        if bank_name is not None:
            self.merchant.payment_info.bank_name = bank_name
        if branch_name is not None:
            self.merchant.payment_info.branch_name = branch_name
        if account_number is not None:
            self.merchant.payment_info.account_number = account_number
        if account_name is not None:
            self.merchant.payment_info.account_name = account_name
        if settlement_rate is not None:
            self.merchant.payment_info.settlement_rate = float(settlement_rate)
        if bank_city is not None:
            self.merchant.payment_info.bank_city = bank_city
        if kargs.get('ledger_info_url') is not None:
            self.merchant.payment_info.ledger_info_url = kargs.get('ledger_info_url')
        if kargs.get("id_front_photos") is not None:
            self._set_id_front_photos(kargs.get("id_front_photos"))
        if kargs.get("id_back_photos") is not None:
            self._set_id_back_photos(kargs.get("id_back_photos"))
        if kargs.get("bank_card_front_photo_urls") is not None:
            self._set_bank_card_front_photo_urls(kargs.get("bank_card_front_photo_urls"))
        self.do_update_merchant()
        self.upload_merchant_info(**kargs)

    def upload_merchant_info(self, **kargs):
        manager = ShopManager(merchant=self.merchant, registration_info=self.registration_info)
        manager.upload_merchant_info(**kargs)

    def _set_id_front_photos(self, photos):
        while self.merchant.merchant_qualifications.id_front_photos:
            self.merchant.merchant_qualifications.id_front_photos.pop()
        for photo in photos:
            self.merchant.merchant_qualifications.id_front_photos.append(photo)

    def _set_id_back_photos(self, photos):
        while self.merchant.merchant_qualifications.id_back_photos:
            self.merchant.merchant_qualifications.id_back_photos.pop()
        for photo in photos:
            self.merchant.merchant_qualifications.id_back_photos.append(photo)

    def _set_bank_card_front_photo_urls(self, photos):
        while self.merchant.payment_info.bank_card_front_photo_urls:
            self.merchant.payment_info.bank_card_front_photo_urls.pop()
        for photo in photos:
            self.merchant.payment_info.bank_card_front_photo_urls.append(photo)

    def _set_recommended_food_photo_urls(self, recommended_food_photo_urls):
        if recommended_food_photo_urls is None:
            return
        while self.store.recommended_food_photo_urls:
            self.store.recommended_food_photo_urls.pop()
        for photo_url in recommended_food_photo_urls:
            self.store.recommended_food_photo_urls.append(photo_url)

    def _set_store_photo_urls(self, store_photo_urls):
        if store_photo_urls is None:
            return
        saved_store_photo_urls = self.store.store_photo_urls
        while len(saved_store_photo_urls) > 0:
            saved_store_photo_urls.pop(0)
        for url in store_photo_urls:
            self.store.store_photo_urls.append(url)

    def _set_store_front_photo_urls(self, store_front_photo_urls):
        if store_front_photo_urls is None:
            return None
        saved_store_front_photo_urls = self.store.store_front_photo_urls
        while len(saved_store_front_photo_urls) > 0:
            saved_store_front_photo_urls.pop(0)
        for url in store_front_photo_urls:
            self.store.store_front_photo_urls.append(url)

    def get_merchant(self):
        merchant_vo = staff_merchant_pb.MerchantInfoVO()
        merchant_vo.logo_url = self.merchant.basic_info.logo_url
        merchant_vo.merchant_id = self.merchant.id
        merchant_vo.store_id = self.store.id
        for url in self.store.store_cover_photo_urls:
            merchant_vo.store_cover_photo_urls.append(url)
        merchant_vo.display_name = self.merchant.basic_info.display_name
        merchant_vo.catering_type = self.merchant.merchant_qualifications.catering_type
        merchant_vo.contact_name = self.merchant.basic_info.contact_name
        merchant_vo.contact_phone = self.merchant.basic_info.contact_phone
        merchant_vo.contact_email = self.merchant.basic_info.contact_email
        merchant_vo.address = self.store.address
        merchant_vo.province = self.store.poi.address_components.province
        merchant_vo.city = self.store.poi.address_components.city
        merchant_vo.district = self.store.poi.address_components.district
        merchant_vo.phone = self.store.phone
        merchant_vo.enable_ordering_service = self.store.enable_ordering_service
        if self.registration_info:
            merchant_vo.pos_type = self.registration_info.pos_type
        merchant_vo.system_message = self.merchant.system_message
        merchant_vo.opening_hours.daily_start_time = self.__convert_timestamp_to_str(self.store.opening_hours.daily_start_time)
        merchant_vo.opening_hours.daily_end_time = self.__convert_timestamp_to_str(self.store.opening_hours.daily_end_time)
        for day in self.store.opening_hours.open_days:
            merchant_vo.opening_hours.open_days.append(day)
        merchant_vo.avg_cost_per_person = self.store.avg_cost_per_person
        merchant_vo.main_staff_id = self.merchant.main_staff_id
        merchant_vo.activate_staff_id = self.merchant.activate_staff_id
        for assist_staff_id in self.merchant.assist_staff_ids:
            merchant_vo.assist_staff_ids.append(assist_staff_id)
        merchant_vo.is_ramen_joint_project = self.merchant.is_ramen_joint_project
        merchant_vo.is_shaxian_joint_project = self.merchant.is_shaxian_joint_project
        merchant_vo.status = self.merchant.status
        merchant_vo.enable_cancel_pay_broadcast = self.merchant.enable_cancel_pay_broadcast
        merchant_vo.promotion_type = self.merchant.promotion_type
        for handheld_pos in self.merchant.handheld_pos:
            handheld_pos_vo = merchant_vo.handheld_pos.add()
            handheld_pos_vo.CopyFrom(handheld_pos)
        return merchant_vo

    def __update_merchant_name(self, name):
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(name=name)
        if merchant is not None and merchant.id != self.merchant.id:
            raise errors.ShowError(message="商户名重复")
        self.merchant.basic_info.display_name = name
        self.store.name = name
        self.merchant.basic_info.name = name

    def update_merchant(
        self,
        store_cover_photo_urls,
        logo_url,
        display_name,
        catering_type,
        contact_name,
        contact_phone,
        contact_email,
        address,
        phone,
        opening_hours,
        avg_cost_per_person,
        main_staff_id,
        assist_staff_ids,
        **kargs,
    ):
        self._set_store_cover_photo_urls(store_cover_photo_urls)
        self._set_opening_hours(opening_hours)
        self._set_assist_staff_ids(assist_staff_ids)
        if logo_url is not None:
            if isinstance(logo_url, str):
                self.merchant.basic_info.logo_url = logo_url
            elif isinstance(logo_url, list) and len(logo_url) > 0:
                self.merchant.basic_info.logo_url = logo_url[0]
        if display_name is not None:
            self.__update_merchant_name(str(display_name).strip())
        if catering_type is not None:
            self.merchant.merchant_qualifications.catering_type = merchant_rules_pb.CateringType.Value(catering_type)
        if contact_name is not None:
            self.merchant.basic_info.contact_name = contact_name
        if contact_phone is not None:
            self.merchant.basic_info.contact_phone = contact_phone
        if contact_email is not None:
            self.merchant.basic_info.contact_email = contact_email
        if address is not None:
            self.store.address = address
        if phone is not None:
            self.store.phone = phone
        if avg_cost_per_person is not None:
            self.store.avg_cost_per_person = avg_cost_per_person
        if main_staff_id is not None:
            self.merchant.main_staff_id = main_staff_id

        system_message = kargs.get("system_message")
        if system_message is not None:
            self.merchant.system_message = system_message

        province = kargs.get("province")
        if province is not None:
            self.store.poi.address_components.province = province

        city = kargs.get("city")
        if city is not None:
            self.store.poi.address_components.city = city

        district = kargs.get("district")
        if district is not None:
            self.store.poi.address_components.district = district

        latitude = kargs.get("latitude")
        longitude = kargs.get("longitude")
        if latitude is not None and longitude is not None:
            self.store.poi.location.latitude = latitude
            self.store.poi.location.longitude = longitude

        enable_ordering_service = kargs.get("enable_ordering_service")
        if enable_ordering_service is not None:
            self.store.enable_ordering_service = enable_ordering_service

        is_ramen_joint_project = kargs.get("is_ramen_joint_project")
        if is_ramen_joint_project is not None:
            self.merchant.is_ramen_joint_project = is_ramen_joint_project

        is_shaxian_joint_project = kargs.get("is_shaxian_joint_project")
        if is_shaxian_joint_project is not None:
            self.merchant.is_shaxian_joint_project = is_shaxian_joint_project

        enable_cancel_pay_broadcast = kargs.get("enable_cancel_pay_broadcast")
        if enable_cancel_pay_broadcast is not None:
            self.merchant.enable_cancel_pay_broadcast = enable_cancel_pay_broadcast

        promotion_type = kargs.get("promotion_type")
        if promotion_type is not None:
            promotion_type = merchant_rules_pb.Merchant.PromotionType.Value(promotion_type)
            self.merchant.promotion_type = promotion_type

        self.do_update_merchant()
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(merchant_fanpiao_update_timestamp=now)

    def _update_merchant_advertising_info(self, advertising_info):
        if advertising_info is None:
            return
        has_advertising = advertising_info.get("hasAdvertising")
        if has_advertising is not None:
            self.merchant.advertising_info.has_advertising = has_advertising

        contents = advertising_info.get("contents")
        if contents is not None:
            while self.merchant.advertising_info.contents:
                self.merchant.advertising_info.contents.pop()
            for content in contents:
                type = content.get("type", "IMAGE")
                type = merchant_rules_pb.AdvertisingInfo.AdvertisingContent.Type.Value(type)
                jump_type = content.get("jumpType", "MINI_PROGRAM_INTERNAL")
                jump_type = merchant_rules_pb.AdvertisingInfo.AdvertisingContent.JumpType.Value(jump_type)
                jump_content = content.get("jumpContent")
                advertise_content = content.get("content")
                advertise_info_content = self.merchant.advertising_info.contents.add()
                advertise_info_content.type = type
                advertise_info_content.jump_type = jump_type
                advertise_info_content.content = advertise_content
                advertise_info_content.jump_content = jump_content

    def _set_assist_staff_ids(self, assist_staff_ids):
        if assist_staff_ids is None:
            return
        saved_assist_staff_ids = self.merchant.assist_staff_ids
        while len(saved_assist_staff_ids) > 0:
            saved_assist_staff_ids.pop(0)
        for assist_staff_id in assist_staff_ids:
            self.merchant.assist_staff_ids.append(assist_staff_id)

    def _set_store_cover_photo_urls(self, store_cover_photo_urls):
        if store_cover_photo_urls is None:
            return
        saved_store_cover_photo_urls = self.store.store_cover_photo_urls
        while len(saved_store_cover_photo_urls) > 0:
            saved_store_cover_photo_urls.pop(0)
        for url in store_cover_photo_urls:
            self.store.store_cover_photo_urls.append(url)

    def _set_opening_hours(self, opening_hours):
        if opening_hours is None:
            return
        daily_start_time = opening_hours.get("dailyStartTime")
        daily_end_time = opening_hours.get("dailyEndTime")
        open_days = opening_hours.get("openDays")
        saved_open_days = self.store.opening_hours.open_days
        while len(saved_open_days) > 0:
            saved_open_days.pop(0)
        for day in open_days:
            saved_open_days.append(day)
        self.store.opening_hours.daily_start_time = self.__convert_str_to_timestamp(daily_start_time)
        self.store.opening_hours.daily_end_time = self.__convert_str_to_timestamp(daily_end_time)

    def __convert_str_to_timestamp(self, date):
        date = date.split(":")
        hour = int(date[0])
        minutes = int(date[1])
        return hour * date_utils.ONE_HOUR + minutes * date_utils.ONE_MINUTE

    def __convert_timestamp_to_str(self, timestamp):
        minutes = int(timestamp / date_utils.ONE_MINUTE)
        hours = int(timestamp / date_utils.ONE_HOUR)
        minutes = int(minutes - hours * date_utils.ONE_MINUTE)
        return "{:0>2}:{:0>2}".format(hours, minutes)

    def __check_permission(self):
        return True

    def set_recommend_foods(self, recommended_food_photo_urls, recommended_dish_ids, enable_recommend_dish):
        self._set_recommended_food_photo_urls(recommended_food_photo_urls)
        if enable_recommend_dish is not None:
            self.registration_info.recommend_dish.enable_recommend_dish = enable_recommend_dish
        while self.registration_info.recommend_dish.dishes:
            self.registration_info.recommend_dish.dishes.pop()
        for dish_id in recommended_dish_ids:
            dish_vo = self.registration_info.recommend_dish.dishes.add()
            dish_vo.id = str(dish_id)
        self.do_update_registration_info(self.registration_info)

    def get_recommend_foods(self):
        recommend_dishes = self.registration_info.recommend_dish
        ordering_da = OrderingServiceDataAccessHelper()
        recommend_food_vo = staff_merchant_pb.RecommendedFood()
        for dish in recommend_dishes.dishes:
            id = dish.id
            dish = ordering_da.get_dish(dish_id=id, merchant_id=self.merchant.id)
            if not dish:
                continue
            dish_vo = recommend_food_vo.recommended_dishes.add()
            dish_vo.name = dish.name
            dish_vo.id = dish.id
        for photo_url in self.store.recommended_food_photo_urls:
            recommend_food_vo.store_recommended_food_photo_urls.append(photo_url)
        return recommend_food_vo

    def get_merchant_dishes(self):
        ordering_da = OrderingServiceDataAccessHelper()
        dish_list_vo = staff_merchant_pb.MerchantDishlist()
        status = [dish_pb.Dish.NORMAL, dish_pb.Dish.GUQING]
        dishes = ordering_da.get_dishes(status=status, merchant_id=self.merchant.id)
        for dish in dishes:
            dish_vo = dish_list_vo.dishes.add()
            dish_vo.id = str(dish.id)
            dish_vo.name = dish.name
        return dish_list_vo

    def fuzzy_match_dish_name(self, dish_name, most=3):
        if not os.path.exists(self.indexdir):
            return []
        ix = open_dir(self.indexdir)
        ret = []
        seg_list = jieba.cut_for_search(dish_name)
        dish_name_cut = " ".join(seg_list)
        with ix.searcher() as searcher:
            query = QueryParser("dish_name", ix.schema).parse(dish_name_cut)
            results = searcher.search(query, limit=most)
            for result in results:
                score = fuzz.ratio(dish_name, result.get("dish_name"))
                ret.append(
                    {"name": result.get("dish_name"), "imageUrl": result.get("image_url"), "score": result.score + score}
                )
            ret.sort(key=lambda x: x.get("score"), reverse=True)
        return ret

    def fuzzy_match_add_dish(self, dish_name, image_url):
        ix = open_dir(self.indexdir)
        writer = ix.writer()
        seg_list = jieba.cut_for_search(dish_name)
        name = " ".join(seg_list)
        writer.update_document(dish_name=name, image_url=image_url)
        writer.commit()

    def do_update_merchant(self, registration_info=None):
        now = int(time.time())
        self.merchant.update_timestamp = now
        if self.staff:
            self.merchant.update_staff_id = self.staff.id
        merchant_da = MerchantDataAccessHelper()
        shop_manager = ShopManager(merchant=self.merchant, store=self.store)
        shop_manager.add_or_update_merchant()
        shop_manager.add_or_update_store(registration_info)
        merchant_da.update_or_create_merchant(merchant=self.merchant)

    def do_update_registration_info(self, registration_info=None):
        if registration_info is None:
            registration_info = self.registration_info
        if not registration_info:
            return
        now = int(time.time())
        registration_info.update_timestamp = now
        if self.staff:
            registration_info.update_staff_id = self.staff.id
        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.add_or_update_registration_info(registration_info)

    def update_config(self, **configs):
        now = int(time.time())

        business_type = configs.get("businessType", None)
        if business_type is not None:
            self.update_business_type(business_type)

        pay_type = configs.get("payType", None)
        if pay_type is not None:
            self.registration_info.pay_type = registration_pb.OrderingServiceRegistrationInfo.PayType.Value(pay_type)

        enable_payment_reminder = configs.get("enablePaymentReminder", None)
        if enable_payment_reminder is not None:
            self.store.disable_payment_reminder = not enable_payment_reminder

        check_out_print = configs.get("checkOutPrint", None)
        if check_out_print is not None:
            self.registration_info.printer_config.feie_printer.check_out_print = check_out_print
            self.registration_info.printer_config.keruyun_printer.check_out_print = check_out_print

        enable_invoice = configs.get("enableInvoice", None)
        if enable_invoice is not None:
            self.store.enable_invoice = enable_invoice

        # todo: deprecated
        enable_shilai_member_card_pay = configs.get("enableShilaiMemberCardPay", None)
        if enable_shilai_member_card_pay is not None:
            self.store_enable_shilai_member_card_pay = enable_shilai_member_card_pay

        enable_shilai_member_card_recharge = configs.get("enableShilaiMemberCardRecharge", None)
        if enable_shilai_member_card_recharge is not None:
            self.store.enable_shilai_member_card_recharge = enable_shilai_member_card_recharge

        enable_ordering_coupon_package_union_pay = configs.get("enableOrderingCouponPackageUnionPay", None)
        if enable_ordering_coupon_package_union_pay is not None:
            self.store.enable_ordering_coupon_package_union_pay = enable_ordering_coupon_package_union_pay

        enable_coupon_package_refund = configs.get("enableCouponPackageRefund", None)
        if enable_coupon_package_refund is not None:
            self.store.enable_coupon_package_refund = enable_coupon_package_refund

        enable_fanpiao_refund = configs.get("enableFanpiaoRefund", None)
        if enable_fanpiao_refund is not None:
            self.store.enable_fanpiao_refund = enable_fanpiao_refund

        enable_fanpiao = configs.get("enableFanpiao", None)
        if enable_fanpiao is not None:
            self.store.enable_fanpiao = enable_fanpiao

        enable_buy_fanpiao = configs.get("enableBuyFanpiao", None)
        if enable_buy_fanpiao is not None:
            self.store.enable_buy_fanpiao = enable_buy_fanpiao

        enable_shilai_promotion_splash = configs.get("enableShilaiPromotionSplash", None)
        if enable_shilai_promotion_splash is not None:
            self.store.enable_shilai_promotion_splash = enable_shilai_promotion_splash

        enable_invite_share = configs.get("enableInviteShare", None)
        if enable_invite_share is not None:
            self.store.enable_invite_share = enable_invite_share

        dish_discount_rate = configs.get("dishDiscountRate", None)
        red_packet_discount_rate = configs.get("redPacketDiscountRate")
        if dish_discount_rate is not None and red_packet_discount_rate is not None:
            self.merchant.preferences.coupon_config.red_packet_discount_rate = red_packet_discount_rate
            self.merchant.preferences.coupon_config.dish_discount_rate = dish_discount_rate
            self.merchant.preferences.coupon_config.max_discount_update_timestamp = now

        enable_subsidies_overflow_fee = configs.get("enableSubsidiesOverflowFee", None)
        if enable_subsidies_overflow_fee is not None:
            self.store.enable_subsidies_overflow_fee = enable_subsidies_overflow_fee

        disable_show_fanpiao_purchase_number = configs.get("disableShowFanpiaoPurchaseNumber", None)
        if disable_show_fanpiao_purchase_number is not None:
            self.fanpiao_config.disable_show_fanpiao_purchase_number = disable_show_fanpiao_purchase_number

        disable_show_fanpiao_price = configs.get('disable_show_fanpiao_price', None)
        if disable_show_fanpiao_price is not None:
            self.fanpiao_config.disable_show_fanpiao_price = disable_show_fanpiao_price

        disable_show_coupon_package_purchase_number = configs.get("disableShowCouponPackagePurchaseNumber", None)
        if disable_show_coupon_package_purchase_number is not None:
            self.coupon_package_config.disable_show_coupon_package_purchase_number = disable_show_coupon_package_purchase_number

        fanpiao_sales_boost_factor = configs.get("fanpiaoSalesBoostFactor", None)
        if fanpiao_sales_boost_factor is not None:
            self.fanpiao_config.fanpiao_sales_boost_factor = fanpiao_sales_boost_factor

        enable_show_time_limit_sale = configs.get("enableShowTimeLimitSale", None)
        if enable_show_time_limit_sale is not None:
            self.ordering_config.enable_show_time_limit_sale = enable_show_time_limit_sale

        enable_dish_incremental = configs.get("enableDishIncremental", None)
        if enable_dish_incremental is not None:
            self.ordering_conifg.enable_dish_incremental = enable_dish_incremental

        banner_mode = configs.get("bannerMode", None)
        if banner_mode is not None:
            self.store.banner_mode = banner_mode

        disable_show_sold_number = configs.get("disableShowSoldNumber", None)
        if disable_show_sold_number is not None:
            self.ordering_config.disable_show_sold_number = disable_show_sold_number

        max_discount = configs.get("maxDiscount", None)
        if max_discount is not None:
            self.merchant.preferences.coupon_config.max_discount = max_discount
            self.merchant.preferences.coupon_config.max_discount_update_timestamp = now

        splash = configs.get("splash")
        if splash is not None:
            self.update_splash_image(splash.get("splashImageUrl"), splash.get("splashMode"))

        enable_eat_in = configs.get("enableEatIn", None)
        if enable_eat_in is not None:
            self.registration_info.ordering_config.enable_eat_in = enable_eat_in

        enable_take_away = configs.get("enableTakeAway", None)
        if enable_take_away is not None:
            self.registration_info.ordering_config.enable_take_away = enable_take_away

        enable_self_pick_up = configs.get("enableSelfPickUp", None)
        if enable_self_pick_up is not None:
            self.registration_info.ordering_config.enable_self_pick_up = enable_self_pick_up

        enable_take_out = configs.get("enableTakeOut", None)
        if enable_take_out is not None:
            self.registration_info.ordering_config.enable_take_out = enable_take_out

        enable_people_count = configs.get("enablePeopleCount", None)
        if enable_people_count is not None:
            self.store.disable_people_count = not enable_people_count

        minimal_bill_fee = configs.get("minimalBillFee", None)
        if minimal_bill_fee is not None:
            self.registration_info.ordering_config.minimal_bill_fee = minimal_bill_fee

        max_take_out_distance = configs.get("maxTakeOutDistance", None)
        if max_take_out_distance is not None:
            self.registration_info.max_take_out_distance = max_take_out_distance

        take_out_commission_rate = configs.get("takeOutCommissionRate", None)
        if take_out_commission_rate is not None:
            self.registration_info.take_out_commission_rate = take_out_commission_rate

        take_away_commission_rate = configs.get("takeAwayCommissionRate", None)
        if take_away_commission_rate is not None:
            self.registration_info.take_away_commission_rate = take_away_commission_rate

        self_pick_up_commission_rate = configs.get("selfPickUpCommissionRate", None)
        if self_pick_up_commission_rate is not None:
            self.registration_info.self_pick_up_commission_prate = self_pick_up_commission_rate

        package_box_type = configs.get("packageBoxType", None)
        if package_box_type is not None:
            self.store.package_box_type = merchant_rules_pb.Store.PackageBoxType.Value(package_box_type)

        pos_type = configs.get("posType", None)
        if pos_type is not None:
            pos_type = registration_pb.OrderingServiceRegistrationInfo.PosType.Value(pos_type)
            if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
                self.update_keruyun_pos_info(**configs)
            elif pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
                self.update_hualala_pos_info(**configs)

        store_front_photo_urls = configs.get("storeFrontPhotoUrls", None)
        if store_front_photo_urls is not None:
            self._set_store_front_photo_urls(store_front_photo_urls)

        store_photo_urls = configs.get("storePhotoUrls", None)
        if store_photo_urls is not None:
            self._set_store_photo_urls(store_photo_urls)

        license_photo_url = configs.get("licensePhotoUrl", None)
        if license_photo_url is not None:
            self.merchant.merchant_qualifications.license_photo_url = license_photo_url

        food_distribution_permit_url = configs.get("foodDistributionPermitUrl", None)
        if food_distribution_permit_url is not None:
            self.merchant.merchant_qualifications.food_distribution_permit_url = food_distribution_permit_url

        id_front_photo = configs.get("idFrontPhoto", None)
        if id_front_photo is not None:
            self.merchant.merchant_qualifications.id_front_photo = id_front_photo

        id_back_photo = configs.get("idBackPhoto", None)
        if id_back_photo is not None:
            self.merchant.merchant_qualifications.id_back_photo = id_back_photo

        bank_card_front_photo_url = configs.get("bankCardFrontPhotoUrl", None)
        if bank_card_front_photo_url is not None:
            self.merchant.payment_info.bank_card_front_photo_url = bank_card_front_photo_url

        bank_account_type = configs.get("bankAccountType", None)
        if bank_account_type is not None:
            self.merchant.payment_info.bank_account_type = merchant_rules_pb.AccountType.Value(bank_account_type)

        bank_name = configs.get("bankName", None)
        if bank_name is not None:
            self.merchant.payment_info.bank_name = bank_name

        branch_name = configs.get("branchName", None)
        if branch_name is not None:
            self.merchant.payment_info.branch_name = branch_name

        account_number = configs.get("accountNumber", None)
        if account_number is not None:
            self.merchant.payment_info.account_number = account_number

        settlement_rate = configs.get("settlementRate", None)
        if settlement_rate is not None:
            self.merchant.payment_info.settlement_rate = float(settlement_rate)

        bank_city = configs.get("bankCity", None)
        if bank_city is not None:
            self.merchant.payment_info.bank_city = bank_city

        id_front_photos = configs.get("idFrontPhotos", None)
        if id_front_photos is not None:
            self._set_id_front_photos(id_front_photos)

        id_back_photos = configs.get("idBackPhotos", None)
        if id_back_photos is not None:
            self._set_id_back_photos(id_back_photos)

        bank_card_front_photo_urls = configs.get("bankCardFrontPhotoUrls", None)
        if bank_card_front_photo_urls is not None:
            self._set_bank_card_front_photo_urls(bank_card_front_photo_urls)

        store_cover_photo_urls = configs.get("storeCoverPhotoUrls", None)
        if store_cover_photo_urls is not None:
            self._set_store_cover_photo_urls(store_cover_photo_urls)

        opening_hours = configs.get("openingHours", None)
        if opening_hours is not None:
            self._set_opening_hours(opening_hours)

        assist_staff_ids = configs.get("assistStaffIds", None)
        if assist_staff_ids is not None:
            self._set_assist_staff_ids(assist_staff_ids)

        logo_url = configs.get("logoUrl", None)
        if logo_url is not None:
            if isinstance(logo_url, str):
                self.merchant.basic_info.logo_url = logo_url
            elif isinstance(logo_url, list) and len(logo_url) > 0:
                self.merchant.basic_info.logo_url = logo_url[0]

        display_name = configs.get("displayName", None)
        if display_name is not None:
            display_name = str(display_name).strip()
            self.merchant.basic_info.display_name = display_name
            self.store.name = display_name
            self.merchant.basic_info.name = display_name

        catering_type = configs.get("cateringType", None)
        if catering_type is not None:
            self.merchant.merchant_qualifications.catering_type = merchant_rules_pb.CateringType.Value(catering_type)

        contact_name = configs.get("contactName", None)
        if contact_name is not None:
            self.merchant.basic_info.contact_name = contact_name

        contact_phone = configs.get("contactPhone", None)
        if contact_phone is not None:
            self.merchant.basic_info.contact_phone = contact_phone

        contact_email = configs.get("contactEmail", None)
        if contact_email is not None:
            self.merchant.basic_info.contact_email = contact_email

        address = configs.get("address", None)
        if address is not None:
            self.store.address = address

        longitude = configs.get("longitude", None)
        latitude = configs.get("latitude", None)
        if longitude is not None and latitude is not None:
            self.store.poi.location.longitude = longitude
            self.store.poi.location.latitude = latitude

        phone = configs.get("phone", None)
        if phone is not None:
            self.store.phone = phone

        avg_cost_per_person = configs.get("avgCostPerPerson", None)
        if avg_cost_per_person is not None:
            self.store.avg_cost_per_person = avg_cost_per_person

        main_staff_id = configs.get("mainStaffId", None)
        if main_staff_id is not None:
            self.merchant.main_staff_id = main_staff_id

        recommend_food_photo_urls = configs.get("recommendFoodPhotoUrls", None)
        if recommend_food_photo_urls is not None:
            self._set_recommended_food_photo_urls(recommend_food_photo_urls)

        enable_recommend_dish = configs.get("enableRecommendDish", None)
        if enable_recommend_dish is not None:
            self.registration_pb.recommend_dish.enable_recommend_dish = enable_recommend_dish

        recommended_dish_ids = configs.get("recommendedDishIds", None)
        if recommended_dish_ids is not None:
            for dish_id in recommended_dish_ids:
                dish_vo = self.registration_info.recommend_dish.dishes.add()
                dish_vo.id = str(dish_id)

        self.do_update_registration_info()
        self.do_update_merchant()
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_fanpiao_config(self.fanpiao_config)
        config_da.add_or_update_coupon_package_config(self.coupon_package_config)
        config_da.add_or_update_ordering_config(self.ordering_config)
        config_manager = ConfigManager(merchant=self.merchant)
        config_manager.update_update_timestamp(
            merchant_fanpiao_update_timestamp=now,
            merchant_coupon_package_update_timestamp=now,
            merchant_info_update_timestamp=now,
        )

    def get_bd_manager_roles(self):
        roles = [
            staff_pb.ShilaiStaff.MANAGER,
            staff_pb.ShilaiStaff.BD_GROUP_MANAGER,
            staff_pb.ShilaiStaff.BD_CITY_MANAGER,
            staff_pb.ShilaiStaff.BD_REGION_MANAGER,
            staff_pb.ShilaiStaff.AGENT_ADMIN,
        ]
        return roles

    def get_all_merchants_roles(self):
        roles = [
            permission_pb.RoleEnum.ADMIN,
            permission_pb.RoleEnum.PRODUCT_ADMIN,
            permission_pb.RoleEnum.PRODUCT_TESTER,
            permission_pb.RoleEnum.PRODUCT_TECHNICIAN,
            permission_pb.RoleEnum.BD_ADMIN,
            permission_pb.RoleEnum.OP_ADMIN,
            permission_pb.RoleEnum.OP_MANAGER,
            permission_pb.RoleEnum.OP_GROUP_MANAGER,
            permission_pb.RoleEnum.OP,
            permission_pb.RoleEnum.OP_CUSTOMER_SERVICE_GROUP_MANAGER,
            permission_pb.RoleEnum.OP_CUSTOMER_SERVICE,
            permission_pb.RoleEnum.OP_DEPLOYMENT_GROUP_MANAGER,
            permission_pb.RoleEnum.OP_DEPLOYMENT,
            permission_pb.RoleEnum.HR_ADMIN,
            permission_pb.RoleEnum.HR,
            permission_pb.RoleEnum.FINANCE_ADMIN,
            permission_pb.RoleEnum.FINANCE_PROCUREMENT,
            permission_pb.RoleEnum.FINANCE,
        ]
        return [permission_pb.RoleEnum.Name(role) for role in roles]

    def get_all_staff_ids(self, staffs, staff_ids=None):
        if staff_ids is None:
            staff_ids = []
        staff_da = StaffDataAccessHelper()
        for staff in staffs:
            if staff.role in self.get_bd_manager_roles() and staff.id not in staff_ids:
                stfs = staff_da.get_staff_list(manager_id=staff.id)
                self.get_all_staff_ids(stfs, staff_ids)
            if staff.id not in staff_ids:
                staff_ids.append(staff.id)
        return staff_ids

    def get_staff_merchant_list_with_specified_pos_type(
        self, staff_id, prev_create_timestamp, status, size, fuzzy_query_name=None, pos_type=None
    ):
        merchants = self.get_staff_merchant_list(staff_id, prev_create_timestamp, status, size, fuzzy_query_name)
        if pos_type is None:
            return merchants
        pos_type = registration_pb.OrderingServiceRegistrationInfo.PosType.Value(pos_type)
        ordering_da = OrderingServiceDataAccessHelper()
        result = []
        for merchant in merchants:
            r = ordering_da.get_registration_info(merchant_id=merchant.id)
            if not r:
                continue
            if pos_type != r.pos_type:
                continue
            result.append(merchant)
        return result

    def get_staff_merchant_list(self, staff_id, prev_create_timestamp, status, size, fuzzy_query_name=None):
        staff_da = StaffDataAccessHelper()
        staff = staff_da.get_staff(staff_id)
        if not staff.is_certified:
            return []
        merchant_list = []
        order_by = [("createTimestamp", -1)]
        merchant_da = MerchantDataAccessHelper()
        role_name = staff_pb.ShilaiStaff.Role.Name(staff.role)
        if staff.role in self.get_bd_manager_roles():
            staff_ids = self.get_all_staff_ids([staff])
            for staff_id in staff_ids:
                s = staff_da.get_staff(staff_id)
                logger.info("{} {} ------------>: {} {}".format(staff.name, staff.city, s.name, s.city))
                merchants = merchant_da.get_merchant_list(
                    main_staff_id=staff_id,
                    binding_staff_id=staff_id,
                    assist_staff_id=staff_id,
                    order_by=order_by,
                    prev_create_timestamp=prev_create_timestamp,
                    size=size,
                    fuzzy_query_name=fuzzy_query_name,
                    status=status,
                )
                if merchants:
                    merchant_list.extend(merchants)
            return merchant_list
        elif role_name in self.get_all_merchants_roles():
            merchant_list = merchant_da.get_merchant_list(
                order_by=order_by,
                prev_create_timestamp=prev_create_timestamp,
                fuzzy_query_name=fuzzy_query_name,
                size=size,
                status=status,
            )
            return merchant_list
        else:
            merchant_list = merchant_da.get_merchant_list(
                main_staff_id=staff_id,
                binding_staff_id=staff_id,
                assist_staff_id=staff_id,
                prev_create_timestamp=prev_create_timestamp,
                size=size,
                fuzzy_query_name=fuzzy_query_name,
                status=status,
                order_by=order_by,
            )
            return merchant_list

    def get_demonstration_merchant_list(self):
        merchant_da = MerchantDataAccessHelper()
        merchants = merchant_da.get_merchant_list(is_demonstration_merchant=True)
        return self.get_staff_merchants_info(merchants)

    def get_staff_merchants_info(self, merchants):
        ordering_da = OrderingServiceDataAccessHelper()
        ret = []
        for merchant in merchants:
            merchant_dict = {}
            registration_info = ordering_da.get_registration_info(merchant_id=merchant.id)
            if registration_info:
                pos_type = registration_pb.OrderingServiceRegistrationInfo.PosType.Name(registration_info.pos_type)
                merchant_dict.update({"posType": pos_type})
            if len(merchant.stores) > 0:
                store = merchant.stores[0]
                merchant_dict.update(
                    {
                        "province": store.poi.address_components.province,
                        "city": store.poi.address_components.city,
                        "district": store.poi.address_components.district,
                        "enableOrderingService": store.enable_ordering_service,
                    }
                )
            status = merchant_rules_pb.MerchantStatus.Name(merchant.status)
            merchant_dict.update(
                {
                    "displayName": merchant.basic_info.display_name,
                    "activateTimestamp": merchant.activate_timestamp,
                    "createTimestamp": merchant.create_timestamp,
                    "status": status,
                    "id": merchant.id,
                    "logoUrl": merchant.basic_info.logo_url,
                }
            )
            ret.append(merchant_dict)
        return ret

    def update_coupon_package_category(self, category_id, **kargs):
        category = None
        for category in self.merchant.preferences.coupon_config.coupon_packages:
            if category.id == category_id:
                break
        if category is None:
            raise errors.ShowError("coupon_category ID 错误")
        display_scene = kargs.get("display_scene")
        if display_scene is not None:
            if isinstance(display_scene, str):
                display_scene = coupon_category_pb.CouponCategory.DisplayScene.Value(display_scene)
            category.coupon_package_spec.coupon_category_spec.display_scene = display_scene
        merchant_da = MerchantDataAccessHelper()
        merchant_da.update_or_create_merchant(merchant=self.merchant)

    def get_staff_list(self):
        staff_da = StaffDataAccessHelper()
        staff_list = staff_da.get_staff_list()
        ret = {}
        for staff in staff_list:
            if staff.role == staff_pb.ShilaiStaff:
                continue
            if staff.name == "":
                continue
            if not staff.is_certified:
                continue
            if self.staff.role in [
                staff_pb.ShilaiStaff.Role.AGENT,
                staff_pb.ShilaiStaff.Role.AGENT_ADMIN,
            ] and staff.role not in [staff_pb.ShilaiStaff.Role.AGENT, staff_pb.ShilaiStaff.Role.AGENT_ADMIN]:
                continue

            ret.update({staff.id: staff.name})
        return ret

    def get_staff_list_v2(self, staff_id):
        staff_da = StaffDataAccessHelper()
        current_staff = staff_da.get_staff(staff_id)
        to_role_name = lambda x: staff_pb.ShilaiStaff.Role.Name(x)
        current_staff_role = to_role_name(current_staff.role)
        staff_list = staff_da.get_staff_list()
        ret = []
        for staff in staff_list:
            if staff.name == "":
                continue
            if not staff.is_certified:
                continue
            role = to_role_name(staff.role)
            if current_staff_role.startswith("AGENT") and not role.startswith("AGENT"):
                continue
            ret.append(
                {
                    "name": staff.name,
                    "phone": staff.phone,
                    "role": role,
                    "id": staff.id,
                    "headimgurl": staff.wechat_profile.headimgurl,
                }
            )
        return ret

    def update_staff(self, user_id, staff_id, **kargs):
        """更新员工信息
        Args:
            user_id: 操作员工ID
            staff_id: 被操作员工ID
        """
        staff_da = StaffDataAccessHelper()
        operator = staff_da.get_staff(user_id)
        operatee = staff_da.get_staff(staff_id)
        if None in (operator, operatee):
            raise errors.ShowError("操作者或被操作者错误")
        if operator.role not in [
            staff_pb.ShilaiStaff.Role.SUPER_ADMIN,
            staff_pb.ShilaiStaff.Role.ADMIN,
            staff_pb.ShilaiStaff.Role.HR_ADMIN,
        ]:
            raise errors.ShowError("操作者权限不足")
        is_certified = kargs.get("is_certified")
        if is_certified is not None:
            operatee.is_certified = is_certified

        role = kargs.get("role")
        if role is not None:
            role = staff_pb.ShilaiStaff.Role.Value(role)
            operatee.role = role

        staff_da.update_or_create_staff(operatee)

# -*- coding: utf-8 -*-

import json
from business_ops.direct_pay_manager import DirectPayManager


class Action:

    # 码牌饭票一次性支付
    DIRECT_PAY_WITH_FANPIAO_PURCHASE = "DIRECT_PAY_WITH_FANPIAO_PURCHASE"

    def __init__(self, transaction, **kargs):
        self._actions = {
            self.DIRECT_PAY_WITH_FANPIAO_PURCHASE: DirectPayManager
        }
        self._transaction = transaction
        self._base_params = {}
        self.__init_base_params()
        self._order_manager_v2 = kargs.get("order_manager_v2")

    @property
    def transaction(self):
        return self._transaction

    def __init_base_params(self):
        self._base_params = {
            "user_id": self._transaction.payer_id,
            "merchant_id": self._transaction.payee_id
        }

    def do_action(self):
        if self._transaction is None:
            return
        for action, params in self._transaction.other_actions.items():
            cls = self._actions.get(action)
            if not cls:
                continue
            params = json.loads(params)
            params.update(self._base_params)
            if action == self.DIRECT_PAY_WITH_FANPIAO_PURCHASE:
                self.__do_direct_pay_with_fanpiao_purchase(cls, params)

    def __do_direct_pay_with_fanpiao_purchase(self, cls, params):
        snake_params = {}
        for key, value in params.items():
            skey = self.camel_to_snake(key)
            snake_params.update({skey: value})
        snake_params.update({"order_manager_v2": self._order_manager_v2})
        obj = cls(**snake_params)
        obj.prepay()

    def camel_to_snake(self, s):
        return ''.join(['_'+c.lower() if c.isupper() else c for c in s]).lstrip('_')

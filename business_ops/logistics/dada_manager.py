import logging
import hashlib
import requests
import json

import maya

import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.logistics import constants
from common.config import config
from common import constants as common_constants
from common.utils import distribute_lock
from dao.logistics.dada_da_helper import DadaDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from proto.logistics import dada_pb2 as dada_pb
from service import errors

logger = logging.getLogger(__name__)


class DadaManager:
    CITY_CODE = {}
    def __init__(self):
        self.appkey = constants.DADA_LOGISTICS_PLATFORM_APPKEY
        self.app_secret = constants.DADA_LOGISTICS_PLATFORM_APP_SECRET
        self.gateway = "http://newopen.qa.imdada.cn"
        self.callback_domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        self.dada_data_access_helper = DadaDataAccessHelper()
        if not self.CITY_CODE:
            self.get_city_code()

    def _sign(self, **kargs):
        keys = kargs.keys()
        keys = sorted(keys)
        s = self.app_secret
        for key in keys:
            s += '{}{}'.format(key, kargs.get(key))
        s += self.app_secret
        md5 = hashlib.md5()
        md5.update(s.encode('utf-8'))
        signature = md5.hexdigest().upper()
        return signature

    def _do_post(self, api, args=''):
        if args:
            args = json.dumps(args)
        request_json = {
            'app_key': self.appkey,
            'timestamp': int(maya.when('now').epoch),
            'format': 'json',
            'v': '1.0',
            'source_id': 73753,
            'body': args
        }
        url = "{}{}".format(self.gateway, api)
        sign = self._sign(**request_json)
        request_json.update({'signature': sign})
        logger.info('达达接口请求参数: {}'.format(request_json))
        try:
            ret = requests.post(url, json=request_json, timeout=3).json()
            logger.info('达达接口返回: {}'.format(ret))
            return ret
        except Exception as ex:
            logger.error(ex)
            logger.info('达达调用接口失败: {}'.format(request_json))
            return False
        return True

    def _check_result(self, result):
        if isinstance(result, dict):
            logger.info('达达物流返回: {}, {}'.format(result.get('code'), result.get('msg')))
            if result.get('code') == 0:
                return True
        else:
            logger.info('达达物流返回格式出错: {}'.format(result))
        return False

    def update_order_status(self, client_id, order_id, order_status, cancel_reason, cancel_from, update_time, signature, dm_id, dm_name, dm_mobile):
        _status = dada_pb.OrderStatus()
        _status.client_id = client_id
        _status.order_id = order_id
        _status.order_status = order_status
        _status.cancel_reason = cancel_reason
        _status.cancel_from = cancel_from
        _status.update_time = update_time
        _status.signature = signature

        # 接单以后才会传以下三个参数
        if dm_id:
            _status.dm_id = dm_id
            _status.dm_name = dm_name
            _status.dm_mobile = dm_mobile

        # 如果是接单,需要获取锁,来改变状态
        key = "logistics-{}".format(order_id)
        with distribute_lock.redislock(key=key, ttl=1000, retry_count=5, retry_delay=200) as lock:
            if not lock:
                logger.info("接单: 无法获取到锁 - {}".format(order_id))
                raise errors.CannotGetOrderLock()
            self.dada_data_access_helper.add_or_update_order_status(_status)
            order = OrderingServiceDataAccessHelper().get_order(id=order_id)
            order.shipping_status = dish_pb.DishOrder.SHIPPING
            order.logistics_platform = registration_pb.LogisticsPlatform.DADA
            OrderingServiceDataAccessHelper().add_or_update_order(order)

    def get_city_code(self):
        api = "/api/cityCode/list"
        result = self._do_post(api)
        if self._check_result(result):
            cities = result.get('result')
            for city in cities:
                self.CITY_CODE.update({"{}市".format(city.get('cityName')): city.get('cityCode')})

    def get_cancel_reason(self):
        api = '/api/order/cancel/reasons'
        result = self._do_post(api)
        return result

    def create_order(self, shop_no, origin_id, city, cargo_price, receiver_name,
                     receiver_address, receiver_lat, receiver_lng, receiver_phone,
                     delay_publish_time='', cargo_num=1, cargo_weight=1, cargo_type=1, info='',
                     tips=0, is_prepay=0):
        city_code = self.CITY_CODE.get(city)
        api = "/api/order/addOrder"
        args = {
            'shop_no': shop_no,
            'origin_id': origin_id,
            'city_code': city_code,
            'cargo_price': cargo_price,
            'is_prepay': is_prepay,
            'receiver_name': receiver_name,
            'receiver_address': receiver_address,
            'receiver_lat': receiver_lat,
            'receiver_lng': receiver_lng,
            'callback': "{}/logistics/dada_order_callback/{}".format(self.callback_domain, origin_id),
            'receiver_phone': receiver_phone,
            'tips': tips,
            'cargo_type': cargo_type,
            'cargo_weight': cargo_weight,
            'cargo_num': cargo_num
        }
        if info:
            args.update({'info': info})
        result = self._do_post(api, args)
        if self._check_result(result):
            _order = dada_pb.Order()
            _order.shop_no = shop_no
            _order.origin_id = origin_id
            _order.city_code = city_code
            _order.cargo_price = cargo_price
            _order.is_prepay = is_prepay
            _order.receiver_name = receiver_name
            _order.receiver_address = receiver_address
            _order.receiver_lat = receiver_lat
            _order.receiver_lng = receiver_lng
            _order.receiver_phone = receiver_phone
            _order.callback = "{}/logistics/dada_order_callback/{}".format(self.callback_domain, origin_id)
            _order.info = info
            _order.tips = tips
            _order.cargo_type = cargo_type
            _order.cargo_weight = cargo_weight
            _order.cargo_num = cargo_num
            _order.invoice_title = ''
            _order.origin_mark_no = ''
            _order.is_use_insurance = 0
            _order.is_finish_code_needed = 0
            _order.delay_publish_time = 0
            _order.is_direct_delivery = 0
            self.dada_data_access_helper.add_or_update_order(_order)
            return True
        return False

    def cancel_order(self, order_id, reason):
        args = {
            'order_id': order_id,
            'cancel_reason': reason,
            'cancel_reason_id': 10000
        }
        api = '/api/order/formalCancel'
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def get_order(self, order_id):
        args = {
            'order_id': order_id
        }
        api = '/api/order/status/query'
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def re_add_order(self, shop_no, origin_id, city_code, cargo_price, receiver_name,
                     receiver_address, receiver_lat, receiver_lng, receiver_phone,
                     delay_publish_time, cargo_num=1, cargo_weight=1, cargo_type=1, info='',
                     tips=0, is_prepay=0):
        api = "/api/order/reAddOrder"
        args = {
            'shop_no': shop_no,
            'origin_id': origin_id,
            'city_code': city_code,
            'cargo_price': cargo_price,
            'is_prepay': is_prepay,
            'receiver_name': receiver_name,
            'receiver_address': receiver_address,
            'receiver_lat': receiver_lat,
            'receiver_lng': receiver_lng,
            'callback': "{}/logistics/dada_order_callback/{}".format(self.callback_domain, origin_id),
            'receiver_phone': receiver_phone,
            'tips': tips,
            'cargo_type': cargo_type,
            'cargo_weight': cargo_weight,
            'cargo_num': cargo_num
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    # 以下两个函数为订单预发布
    # 具体调用方式查看文档
    # https://newopen.imdada.cn/#/development/file/readyAdd?_k=9a6vnv
    def query_deliver_fee(self, shop_no, origin_id, city_code, cargo_price, receiver_name,
                     receiver_address, receiver_lat, receiver_lng, receiver_phone,
                     delay_publish_time, cargo_num=1, cargo_weight=1, cargo_type=1, info='',
                     tips=0, is_prepay=0):
        api = "/api/order/queryDeliverFee"
        args = {
            'shop_no': shop_no,
            'origin_id': origin_id,
            'city_code': city_code,
            'cargo_price': cargo_price,
            'is_prepay': is_prepay,
            'receiver_name': receiver_name,
            'receiver_address': receiver_address,
            'receiver_lat': receiver_lat,
            'receiver_lng': receiver_lng,
            'callback': "{}/logistics/dada_order_callback/{}".format(self.callback_domain, origin_id),
            'receiver_phone': receiver_phone,
            'tips': tips,
            'cargo_type': cargo_type,
            'cargo_weight': cargo_weight,
            'cargo_num': cargo_num
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return result.get('result').get('deliveryNo')
        return ''

    def appoint_exist_order(self, order_id, transporter_id, shop_no):
        """ 追加订单
        """
        api = "/api/order/appoint/exist"
        args = {
            "order_id": order_id, # 追加的第三方订单ID
            "transporter_id": transporter_id, # 追加的配送员ID
            "shop_no": shop_no # 追加订单的门店编码
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False


    def add_after_query(self, deliver_id):
        api = "/api/order/addAfterQuery"
        args = {
            'deliveryNo': deliver_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def confirm_goods(self, order_id):
        api = "/api/order/confirm/goods"
        args = {
            'order_id': order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False


    ##########以下函数为达达提供的测试接口##############
    # 达达文档地址 https://newopen.imdada.cn/#/development/file/order?_k=3m78nj
    def accept_order(self, order_id):
        """ 模拟接单
        """
        api = "/api/order/accept"
        args = {
            'order_id': order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def fetch_order(self, order_id):
        """ 模拟完成取货
        """
        api = "/api/order/fetch"
        args = {
            'order_id': order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def finish_order(self, order_id):
        """ 模拟完成订单
        """
        api = '/api/order/finish'
        args = {
            "order_id": order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def cancel_order(self, order_id, reason=None):
        """ 模拟取消订单
        """
        api = "/api/order/cancel"
        args = {
            'order_id': order_id
        }
        if reason is not None:
            args.update({'reason': reason})
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def expire_order(self, order_id):
        """ 模拟订单过期
        """
        api = '/api/order/expire'
        args = {
            'order_id': order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

    def delivery_abnormal_back_order(self, order_id):
        """ 模拟订单妥投异常返回中
        """
        api = "/api/order/delivery/abnormal/back"
        args = {
            'order_id': order_id
        }
        result = self._do_post(api, args)
        if self._check_result(result):
            return True
        return False

# -*- coding: utf-8 -*-

import logging
import math

import maya

from business_ops.message_manager import MessageManager
from business_ops.business_manager import BusinessManager
from cache.group_dining_redis_helper import del_invitation
from cache.group_dining_redis_helper import get_nearby_group_dinings
from common.utils import date_utils
from common.utils import geo_utils
from common.utils import distribute_lock
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.combo_meal_da_helper import ComboMealDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto.group_dining import group_dining_pb2 as group_dining_pb
from proto.finance import wallet_pb2 as wallet_pb
from proto.page import group_dining_store_list_pb2 as group_dining_store_list_pb
from proto.page import merchant_group_dining_list_pb2 as merchant_group_dining_list_pb
from service import error_codes
from service import errors
from service.base_responses import create_responses_obj
from strategy import group_dining_strategy

logger = logging.getLogger(__name__)


class GroupDiningManager(BusinessManager):

    def __init__(self, merchant_id=None, user_id=None, pay_method=None, bill_fee=None, paid_fee=None,
                 order_id=None, transaction_id=None, user_cnt=0, dining_id=None, no_discount_bill_fee=0, coupon_id=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.user_cnt = user_cnt
        self.coupon_fee = 0
        self.no_discount_bill_fee = no_discount_bill_fee

        super(GroupDiningManager, self).__init__(
            transaction_id=transaction_id, merchant_id=merchant_id, order_id=order_id, user_id=user_id, coupon_id=coupon_id, pay_method=pay_method)

        self.paid_fee = self.round_off(self.paid_fee)
        self.bill_fee = self.round_off(self.bill_fee)
        self.no_discount_bill_fee = self.round_off(self.no_discount_bill_fee)

    def nominate(self, invitee_id):
        GroupDiningDataAccessHelper().update(self.dining_id, director_id=invitee_id)
        MessageManager().publish_nominate_message(self.dining_id, invitee_id)

    def accept_invitation(self):
        """ 接受饭局邀请
        Args:
            dining: (GroupDiningEvent)饭局
            inviter_id: (inviter_id)邀请者ID
            invitee_id: (invitee_id)被邀请者ID
        Return:
            None
        """

        invitation_dao = InvitationDataAccessHelper()
        resp = create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        key = "{}-accept-invitation".format(self.dining.id)

        # # 如果用户不是这家商户的会员,就为他发一张会员卡
        # member_card_manager.MemberCardManager().create_member_card_for_user_if_not_member(self.user_id,
        #                                                                                   self.merchant_id)

        with distribute_lock.redislock(key=key, ttl=5000, retry_count=50, retry_delay=200) as lock:
            if not lock:
                logger.info("没有获取到饭局锁,加入失败: {}, {}".format(self.dining.id, self.user_id))
                raise errors.JoinGroupDiningFailed()
            invitations = invitation_dao.get_invitations(dining_id=self.dining.id, state=group_dining_pb.Invitation.ACCEPTED)
            count = len(invitations)
            if count >= self.dining.max_group_size:
                logger.info("饭局: {} 参加人数己满".format(self.dining.id))
                resp = create_responses_obj(error_codes.TOO_MANY_ACCEPTED, error_codes.TOO_MANY_ACCEPTED_MSG)
                return resp
            invitation = group_dining_pb.Invitation()
            invitation.dining_event_id = self.dining.id
            invitation.inviter_id = self.dining.director_id
            invitation.invitee_id = self.user_id
            invitation.accept_time = date_utils.timestamp_second()
            invitation.state = group_dining_pb.Invitation.ACCEPTED
            invitation.monetary_state = group_dining_pb.Invitation.PAYMENT_PENDING
            invitation.merchant_id = self.merchant.id

            start_time = int(maya.when('4 hours ago').epoch)
            end_time = int(maya.when('now').epoch)
            state = group_dining_pb.Invitation.ACCEPTED
            recently_invitation = invitation_dao.get_recently_accepted_group_dining(
                self.user_id, self.merchant.id, start_time, end_time, state)
            if recently_invitation:
                recently_dining = GroupDiningDataAccessHelper().get_dining_by_id(recently_invitation.dining_event_id)
                if recently_dining and self.dining.id != recently_invitation.dining_event_id:
                    logger.info('不是同一个饭局,退出旧有的饭局,并加入新的饭局')
                    if recently_dining.director_id == self.user_id:
                        if recently_dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED:
                            logger.info('{}局长加入{}饭局'.format(recently_invitation.dining_event_id, self.dining.id))
                            recently_invitation.state = group_dining_pb.Invitation.QUIT
                            invitation_dao.update(
                                dining_id=recently_invitation.dining_event_id, state=group_dining_pb.Invitation.QUIT)
                            GroupDiningManager(
                                dining_id=recently_dining.id, user_id=self.user_id).cancel_group_dining_event()
                            logger.info('解散饭局{}'.format(recently_invitation.dining_event_id))
                    else:
                        if recently_dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED:
                            logger.info('用户不是局长,用户退出饭局: {}'.format(recently_invitation.dining_event_id))
                            recently_invitation.state = group_dining_pb.Invitation.QUIT
                            invitation_dao.update(
                                dining_id=recently_invitation.dining_event_id, user_id=self.user_id,
                                state=group_dining_pb.Invitation.QUIT)
            # 通知所有人有人加入饭局
            MessageManager().publish_accept_invitation_message(self.dining.id, self.user_id, self.dining.director_id)

            # 设置用户加入饭局
            invitation_dao.add_or_update_invitation(invitation)
        return resp

    def create_group_dining_event(self, initiator_id, event_time, max_group_size, payment_rule, visibility):
        """ 创建饭局
        Args:
            group_dining_event_id: 饭局id
            merchant_id: 商户id
            store_id: 门店id
            initiator_id: 创建者id
            event_time: 开始时间
            max_group_size: 最大参与人数
            payment_rule: 支付方式
            combo_meal_id: 套餐id
        Returns:
            GroupDiningEvent结构体
        """
        dining = group_dining_pb.GroupDiningEvent()
        dining.merchant_id = self.merchant.id
        dining.store_id = self.store_id
        dining.initiator_id = initiator_id
        dining.director_id = initiator_id
        dining.open_invite_permission = True
        dining.event_time = event_time  # 饭局开始时间
        dining.max_group_size = max_group_size
        dining.id = self.dining_id
        dining.payment_rule = payment_rule
        dining.state = group_dining_pb.GroupDiningEvent.SCHEDULED  # 默认是未开始状态
        dining.create_time = date_utils.timestamp_second()  # 饭局创建时间
        dining.dish_order_mode = group_dining_pb.GroupDiningEvent.GENERAL

        policies = group_dining_strategy.generate_coupon_policies(
            cost_per_person=self.store.avg_cost_per_person, total_discount=self.store.group_dining_discount)
        for policy in policies:
            coupon_policy = dining.group_dining_coupon_policies.add()
            coupon_policy.CopyFrom(policy)

        self.dining = dining
        GroupDiningDataAccessHelper().add_group_dining_event(dining)

        return create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG), dining

    def cancel_group_dining_event(self):
        """取消饭局
        """

        invitations = InvitationDataAccessHelper().get_invitations(
            dining_id=self.dining_id, invitee_id=self.user_id)
        dining = GroupDiningDataAccessHelper().get_dining_by_id(self.dining_id)
        for invitation in invitations:
            del_invitation(dining, invitation)

        GroupDiningDataAccessHelper().update(dining_id=self.dining_id,
                                             state=group_dining_pb.GroupDiningEvent.CANCELED)
        MessageManager().cancel_group_dining_event(self.dining_id, self.user_id, self.dining.director_id)
        InvitationDataAccessHelper().update(dining_id=self.dining_id,
                                            state=group_dining_pb.Invitation.QUIT,
                                            monetary_state=group_dining_pb.Invitation.UNKNOWN)

    def quit_group_dining(self):
        """ 退出饭局
        """
        invitation = InvitationDataAccessHelper().get_invitation(dining_id=self.dining_id, invitee_id=self.user_id)
        if not (invitation.state == group_dining_pb.Invitation.ACCEPTED):
            return

        InvitationDataAccessHelper().update(
            user_id=[self.user_id], dining_id=self.dining_id, state=group_dining_pb.Invitation.QUIT)
        MessageManager().publish_quit_group_dining_message(self.user_id, self.dining_id)

    def kick_user(self, user_ids, dining_id):
        """ 局长踢人
        """
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        for user_id in user_ids:
            invitation = InvitationDataAccessHelper().get_invitation(dining_id=dining_id, invitee_id=user_id)
            del_invitation(dining, invitation)

        InvitationDataAccessHelper().update(user_id=user_ids, dining_id=dining_id, state=group_dining_pb.Invitation.KICKED)
        MessageManager().publish_kick_user_message(user_ids, dining_id)

    def apply_for_group_dining(self, dining_id, applicant_id, message):
        """ 申请加入饭局
        """
        invitation_da = InvitationDataAccessHelper()
        invitation = invitation_da.get_invitation(invitee_id=applicant_id, dining_id=dining_id)
        if invitation:
            return
        invitation = group_dining_pb.Invitation()
        dining = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
        invitation.dining_event_id = dining_id
        invitation.inviter_id = dining.director_id
        invitation.invitee_id = applicant_id
        invitation.state = group_dining_pb.Invitation.PENDING_APPROVE
        invitation.monetary_state = group_dining_pb.Invitation.PAYMENT_PENDING
        invitation.apply_time = date_utils.timestamp_second()
        invitation_da.add_or_update_invitation(invitation)

        # 发送加入申请的系统消息
        MessageManager().publish_apply_for_message(applicant_id, dining, invitation, message)

    def __calculate_policy(self):
        """ 根据支付的钱和用户数,计算应该使用哪个policy
        """
        if self.user_cnt <= 1:
            return None
        policies = self.dining.group_dining_coupon_policies
        for policy in reversed(policies):
            if policy.target_diner_count > 1 and \
               policy.least_cost <= self.bill_fee and \
               self.user_cnt >= policy.target_diner_count:
                return policy
        return None

    def __is_valid(self):
        if self.user_cnt > 1:
            frontend_policy = self.__calculate_policy()
            backend_policy = self.__calculate_policy()
            if frontend_policy and not backend_policy:
                # 如果前端的人数计算出来有policy,而后端计算的没有policy.则认为user_cnt异常
                logger.info('后端未计算出policy.前端计算出来的policy: {}'.format(frontend_policy))
                raise errors.GroupDiningFeeNotMatched()
            if frontend_policy and backend_policy:
                if frontend_policy.reduce_cost > backend_policy.reduce_cost:
                    # 防止被刷,前端传过来的人数计算出来的policy,不能优于后端计算出来的policy
                    logger.info('前端的满减金额大于后端的满减金额: frontend: {} \n backend: {}'.format(
                        frontend_policy.reduce_cost, backend_policy.reduce_cost))
                    raise errors.GroupDiningFeeNotMatched()
                if frontend_policy and (self.bill_fee - frontend_policy.reduce_cost) != self.paid_fee:
                    logger.info('满减金额错误: bill_fee: {}, reduce_cost: {}, paid_fee: {}'.format(
                        self.bill_fee,
                        frontend_policy.reduce_cost,
                        self.paid_fee))
                    raise errors.GroupDiningFeeNotMatched()
        else:
            if self.paid_fee != self.bill_fee:
                logger.info('人数为1,支付金额与总金额不同: bill_fee: {}, paid_fee: {}'.format(
                    self.bill_fee, self.paid_fee))
                raise errors.GroupDiningFeeNotMatched()
        return True

    def get_user_group_dining_monetary_state(self):
        """ 用户在饭局中付款状态
        """
        invitation_da = InvitationDataAccessHelper()
        invitation = invitation_da.get_invitation(user_id=self.user_id, dining_id=self.dining_id)
        if invitation and invitation.state == group_dining_pb.Invitation.ACCEPTED:
            if self.dining.state == group_dining_pb.GroupDiningEvent.SCHEDULED:
                return group_dining_pb.Invitation.PAYMENT_PENDING
            transaction = TransactionDataAccessHelper().get_transaction_by_id(invitation.transaction_id)
            if self.dining.payment_rule in (group_dining_pb.GroupDiningEvent.ALL_SHARING,
                                            group_dining_pb.GroupDiningEvent.SPELL_SPLIT):
                # 如果是AA或者是拼单局,需要参与者先付款
                if not transaction or not (transaction.state == wallet_pb.Transaction.SUCCESS):
                    return group_dining_pb.Invitation.TRANSFER_PENDING
            red_packet = RedPacketDataAccessHelper().get_red_packet(dining_id=self.dining_id)
            if red_packet:
                if red_packet.drawn_users.get(self.user_id) is None:
                    return group_dining_pb.Invitation.RED_PACKET_PENDING
                elif red_packet.drawn_users.get(self.user_id) is not None:
                    return group_dining_pb.Invitation.COMPLETED
        return group_dining_pb.Invitation.UNKNOWN

    def __set_invitation_info(self, has_red_packet):
        """ 设置局长invitation表中的状态
        Args:
            dining: 饭局
            transaction: 交易
        Return:
            无
        """
        invitation_da = InvitationDataAccessHelper()
        if has_red_packet:
            # 如果有生成红包
            if self.dining.payment_rule in [
                    group_dining_pb.GroupDiningEvent.ALL_SHARING,
                    group_dining_pb.GroupDiningEvent.SPELL_SPLIT]:
                # 如果是AA局或者拼单局
                # 更新饭局其它人的monetary_state为局长己买单,等待转账
                invitation_da.update(dining_id=self.dining_id,
                                     monetary_state=group_dining_pb.Invitation.TRANSFER_PENDING)
            else:
                if self.order:
                    # 1. 请客局,直接更新为等待开红包
                    invitation_da.update(dining_id=self.dining_id,
                                         monetary_state=group_dining_pb.Invitation.RED_PACKET_PENDING)
                else:
                    # 如果是扫码点餐的请客局,就只有局长可以领取红包
                    pass
            # 无论如何,买单人的monetary_state状态设置为可领红包
            invitation_da.update(dining_id=self.dining_id,
                                 user_id=self.dining.director_id,
                                 transaction_id=self.transaction.id,
                                 monetary_state=group_dining_pb.Invitation.RED_PACKET_PENDING)
        else:
            # 如果没有生成红包
            if self.dining.payment_rule in [group_dining_pb.GroupDiningEvent.ALL_SHARING,
                                            group_dining_pb.GroupDiningEvent.SPELL_SPLIT]:
                # AA或者拼单
                # 其它人设置为待转账
                invitation_da.update(dining_id=self.dining.id,
                                     monetary_state=group_dining_pb.Invitation.TRANSFER_PENDING)
            # 局长设置为己完成
            invitation_da.update(dining_id=self.dining.id,
                                 user_id=self.dining.director_id,
                                 transaction_id=self.transaction.id,
                                 monetary_state=group_dining_pb.Invitation.COMPLETED)

    def get_nearby_group_dining_events(self, lat, lng, user_id, max_distance=1000,
                                       last_event_time=None,
                                       page=None, size=None):
        """ 附近的饭局
        """
        visibility = group_dining_pb.GroupDiningEvent.PUBLIC
        group_dinings = get_nearby_group_dinings(lng, lat, max_distance, visibility=visibility)

        result = []
        for dining in group_dinings:

            dining_id = dining[0].decode('utf8')
            event = GroupDiningDataAccessHelper().get_dining_by_id(dining_id)
            if event.event_time < last_event_time:
                continue
            distance = int(dining[1])

            if event.merchant_id == "1e543376139b474e97d38d487fa9fbe8":
                continue
            invitation = InvitationDataAccessHelper().get_invitation(user_id=user_id,
                                                                     dining_id=event.id)
            merchant = MerchantDataAccessHelper().get_merchant(event.merchant_id)
            event_vo = group_dining_store_list_pb.NearbyGroupDiningEvent()
            event_vo.id = event.id
            event_vo.title = event.title
            event_vo.intro = event.intro
            user = UserDataAccessHelper().get_user(user_id=event.director_id)
            event_vo.headimgurl = user.member_profile.head_image_url
            event_vo.nickname = user.member_profile.nickname
            event_vo.sex = user.member_profile.sex
            event_vo.birth_year = user.member_profile.birth_year
            event_vo.birth_month = user.member_profile.birth_month
            event_vo.birth_day = user.member_profile.birth_day
            event_vo.distance = distance
            event_vo.store_name = merchant.stores[0].name
            event_vo.event_date = event.event_time
            event_vo.director_id = event.director_id
            if invitation:
                event_vo.invitation_state = invitation.state
            else:
                event_vo.invitation_state = group_dining_pb.Invitation.PENDING
            result.append(event_vo)
            if len(result) == size:
                break
        now = maya.when("now").datetime()

        def s(x):
            if x.event_date > now.timestamp() - 60 * 60:
                return x.event_date - now.timestamp() * 2
            elif x.event_date < now.timestamp() - 60 * 60:
                return now.timestamp() - x.event_date
        result.sort(key=s)
        skip = (page - 1) * size
        return result[skip:skip + size]

    def get_merchant_group_dining_list(self, merchant_id, user_id, page=None, size=None):
        """ 商户正在进行中的饭局
        """
        start_time = maya.when("30 minutes ago").datetime().timestamp()
        end_time = maya.when("now").add(days=3).datetime().timestamp()

        public = group_dining_pb.GroupDiningEvent.PUBLIC
        group_dinings = GroupDiningDataAccessHelper().get_dining_events(
            event_start_time=start_time, merchant_id=merchant_id, visibility=public, event_end_time=end_time, orderby=[("eventTime", -1)])
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        store = merchant.stores[0]
        store_vo = merchant_group_dining_list_pb.Store()
        store_vo.id = merchant.id
        store_vo.store_name = store.name
        store_vo.logo_url = merchant.basic_info.logo_url
        for group_dining in group_dinings:
            if group_dining.merchant_id == "1e543376139b474e97d38d487fa9fbe8":
                continue
            dining = store_vo.group_dining_events.add()
            dining.id = group_dining.id
            director = UserDataAccessHelper().get_user(group_dining.director_id)
            dining.headimgurl = director.member_profile.head_image_url
            dining.nickname = director.member_profile.nickname
            dining.sex = director.member_profile.sex
            dining.birth_year = director.member_profile.birth_year
            dining.birth_month = director.member_profile.birth_month
            dining.birth_day = director.member_profile.birth_day
            dining.title = group_dining.title
            dining.intro = group_dining.intro
            dining.max_group_size = group_dining.max_group_size
            dining.event_date = group_dining.event_time
            dining.dish_order_mode = group_dining.dish_order_mode
            if dining.dish_order_mode == group_dining_pb.GroupDiningEvent.COMBO_MEAL:
                combo = ComboMealDataAccessHelper().get_combo_meal_by_id(group_dining.combo_meal_id)
                dining.combo_meal_name = combo.name
            dining.payment_rule = group_dining.payment_rule
            invitation = InvitationDataAccessHelper().get_invitation(dining_id=dining.id, invitee_id=user_id)
            if invitation:
                dining.state = invitation.state
        return store_vo

    def get_nearby_group_dining_events_detail(self, lat, lng, user_id,
                                              max_distance=1000, page=None, size=None):
        start_time = maya.when("30 minutes ago").datetime().timestamp()
        end_time = maya.when("now").add(days=3).datetime().timestamp()

        state = group_dining_pb.GroupDiningEvent.SCHEDULED
        public = group_dining_pb.GroupDiningEvent.PUBLIC
        dining_events = GroupDiningDataAccessHelper().get_dining_events(
            event_start_time=start_time, event_end_time=end_time, visibility=public, state=state, orderby=[("eventTime", -1)])
        nearby_dining_events = geo_utils.get_group_dining_events_within_distance(lat, lng, dining_events, max_distance)
        result = []
        user_da = UserDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        invitation_da = InvitationDataAccessHelper()
        skip = (page - 1) * size
        for event in nearby_dining_events[skip:skip + size]:
            invitation = invitation_da.get_invitation(user_id=user_id, dining_id=event.id)
            merchant = merchant_da.get_merchant(event.merchant_id)
            user = user_da.get_user(user_id=event.director_id)
            event_vo = group_dining_store_list_pb.NearbyGroupDiningEventDetail()

            event_vo.id = event.id
            event_vo.title = event.title
            event_vo.intro = event.intro
            event_vo.headimgurl = user.member_profile.head_image_url
            event_vo.nickname = user.member_profile.nickname
            event_vo.sex = user.member_profile.sex
            for tag in user.member_profile.tags:
                event_vo.tags.append(tag)
            event_vo.logo = merchant.basic_info.logo_url
            event_vo.store_name = merchant.stores[0].name
            event_vo.address = merchant.stores[0].address
            event_vo.dish_order_mode = event.dish_order_mode
            combo_meal_id = event.combo_meal_id
            event_vo.combo_meal_name = ""
            if event_vo.dish_order_mode == group_dining_pb.GroupDiningEvent.COMBO_MEAL:
                combo_meal = ComboMealDataAccessHelper().get_combo_meal_by_id(combo_meal_id)
                event_vo.combo_meal_name = combo_meal.name
            event_vo.user_cnt = invitation_da.count_invitation(dining_id=event.id,
                                                               state=group_dining_pb.Invitation.ACCEPTED)
            event_vo.user_limit = event.max_group_size
            event_vo.payment_rule = event.payment_rule
            event_vo.event_date = event.event_time
            event_vo.birth_year = user.member_profile.birth_year
            event_vo.birth_month = user.member_profile.birth_month
            event_vo.birth_day = user.member_profile.birth_day
            event_vo.director_id = event.director_id
            if invitation:
                event_vo.invitation_state = invitation.state
            else:
                event_vo.invitation_state = group_dining_pb.Invitation.PENDING
            result.append(event_vo)
        return result

    @classmethod
    def calculate_transfer_bill_fee(cls, dining_id, transaction):
        invitees = InvitationDataAccessHelper().get_invitations(
            dining_id=dining_id, state=group_dining_pb.Invitation.ACCEPTED)
        count = len(invitees)
        bill_fee = int(math.ceil(float(transaction.paid_fee) / count))
        return bill_fee

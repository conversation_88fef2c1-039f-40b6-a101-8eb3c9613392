from dao.qrcode_da_helper import QrcodeDataAccessHelper
import proto.qrcode_pb2 as qrcode_pb

class QrcodeBusinessOpsHelper(object):
    def __init__(self):
      pass
  
    def add_merchant_auth_qrcode_info(qrcode_id, staff_id, merchant_id):
      qrcode = qrcode_pb.MerchantQrcode()
      qrcode.id = qrcode_id
      qrcode.merchant_id = merchant_id
      qrcode.base_info.staff_id = staff_id
      QrcodeDataAccessHelper().add_merchant_qrcode(qrcode)
import logging
from collections import defaultdict

import proto.finance.wallet_pb2 as wallet_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
from business_ops.payment_manager import PaymentManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.direct_pay_manager import DirectPayManager
from business_ops.handheld_pos.boshijie_manager import BoshijieManager
from dao.user_da_helper import UserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from business_ops.ordering.dish_manager import DishManager
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class DirectPayManagerHelper:

    def __init__(self, merchant=None):
        self.merchant = merchant
        self.user_id = None

    def fanpiao_qrcode_prepay(self, qrcode_obj, partial_refund=False, no_discount_fee=0):
        if qrcode_obj.bill_fee == 0:
            return
        fanpiao_pay_manager = FanpiaoPayManager(merchant_id=qrcode_obj.merchant_id)
        transaction = fanpiao_pay_manager.pos_order_prepay(qrcode_obj, no_discount_fee=no_discount_fee)
        qrcode_obj.transaction_id = transaction.id
        qrcode_obj.paid_fee = transaction.paid_fee
        platform_discount_fee = qrcode_obj.platform_discount_fee
        payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.WECHAT_PAY, merchant_id=qrcode_obj.merchant_id)
        order_manager = OrderManager(merchant_id=qrcode_obj.merchant_id)
        fanpiao_commission_fee = order_manager.cal_fanpiao_commission_fee(transaction, no_discount_fee=no_discount_fee)
        payment_manager.update_pos_order_merchant_transfer_fee(
            transaction, platform_discount_fee, fanpiao_commission_fee)
        total_receivable_fee = transaction.paid_fee + platform_discount_fee - fanpiao_commission_fee
        balance = qrcode_obj.balance
        user_da = UserDataAccessHelper()
        user = user_da.get_user(qrcode_obj.user_id)
        result = {
            "paidFee": transaction.paid_fee,
            "totalReceivableFee": total_receivable_fee,
            "totalSubsidyFee": platform_discount_fee,
            "fanpiaoCommissionFee": fanpiao_commission_fee,
            "userInfo": {
                "id": user.id,
                "nickname": user.member_profile.nickname,
                "phone": user.member_profile.mobile_phone
            },
            "payMethod": fanpiao_pb.FanpiaoScanQrcode.PayMethod.Name(qrcode_obj.pay_method),
            "balance": balance,
            "isPartialRefund": partial_refund
        }
        return result

    def handheld_pos_order_fanpiao_prepay(self, qrcode_obj, transaction, order):
        """手持pos机用饭票支付"""
        try:
            fanpiao_manager = FanpiaoManager()
            qrcode_obj.is_paid = '1'
            qrcode_obj.transaction_id = transaction.id
            qrcode_obj.order_id = order.id
            transaction.auth_code = qrcode_obj.id
            fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
            fanpiao_manager.delete_fanpiao_qrcode_from_cache(qrcode_obj)
        except Exception as ex:
            logger.info(f"卡包二维码支付: {qrcode_obj} {ex}")
            raise ex
        finally:
            fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)

    def pos_order_prepay(self, qrcode_obj, payment_transaction_id, no_discount_fee=0):
        fanpiao_manager = FanpiaoManager()
        try:
            result = self.fanpiao_qrcode_prepay(qrcode_obj, no_discount_fee=no_discount_fee)
            qrcode_obj.payment_transaction_id = payment_transaction_id
            qrcode_obj.is_paid = '1'
            fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
            fanpiao_manager.delete_fanpiao_qrcode_from_cache(qrcode_obj)
            return result
        except Exception as ex:
            logger.info(f"卡包二维码支付: {qrcode_obj} {ex}")
            raise ex
        finally:
            fanpiao_manager.update_fanpiao_qrcode_obj(qrcode_obj)
            fanpiao_manager.publish_qrcode_info_complete(qrcode_obj)

    def get_shilai_refund_order_no_discount_fee(self, order_id, merchant_id, refund_dishes):
        no_discount_fee = 0
        order_manager = OrderManager(merchant_id=merchant_id)
        order = order_manager.order_manager.sync_order_from_pos(order_id)
        ordering_da = OrderingServiceDataAccessHelper()
        categories = ordering_da.get_categories(merchant_id=merchant_id, return_proto=False)
        categories_map = {item.get('id'): item for item in categories}
        dish_manager = DishManager(merchant_id=merchant_id)

        order_dishes = self._del_refund_dish(order, refund_dishes)
        no_discount_fee = sum(
            self._get_shilai_dish_no_discount_fee(dish, dish_manager, categories_map)
            for dish in order_dishes
        )
        return int(no_discount_fee)
    
    def get_shilai_order_discount_status(self, order_id, qrcode_obj, refund_dishes, fanpiao):
        order_manager = OrderManager(merchant_id=qrcode_obj.merchant_id)
        order = order_manager.order_manager.sync_order_from_pos(order_id)
        ordering_da = OrderingServiceDataAccessHelper()
        categories = ordering_da.get_categories(merchant_id=qrcode_obj.merchant_id, return_proto=False)
        categories_map = {item.get('id'): item for item in categories}
        dish_manager = DishManager(merchant_id=qrcode_obj.merchant_id)

        order_dishes = self._del_refund_dish(order, refund_dishes)
        all_discount_dishes = defaultdict(list)
        has_no_discount = False
        for dish in order_dishes:
            dish_id = dish.get("dishId")
            no_discount_fee = self._get_shilai_dish_no_discount_fee(dish, dish_manager, categories_map)
            if no_discount_fee > 0:
                has_no_discount = True
            else:
                all_discount_dishes[dish_id].append(dish)

        # 只计算有折扣的菜品补贴
        dish_discount_map = {}
        for dish_id, dishes in all_discount_dishes.items():
            for dish in dishes:
                dish_discount = dish_discount_map.get(dish_id, 0)
                no_discount_fee = self._get_shilai_dish_no_discount_fee(dish, dish_manager, categories_map)
                if no_discount_fee > 0:
                    continue
                dish_discount += (dish.get("price", 0) * dish.get("quantity", 0)) * fanpiao.discount / 100
                dish_discount_map[dish_id] = dish_discount

        qrcode_obj.platform_discount_fee = int(sum(dish_discount_map.values())) 
        return has_no_discount

    def _del_refund_dish(self, order, refund_dishes):
        def flatten_dish(dishes):
            flatten_dishes = []
            for dish in dishes.copy():
                if dish.get("isWeighingDish"):
                    flatten_dishes.append(dish)
                else:
                    quantity = int(dish.get("quantity", 1))
                    if quantity > 1:
                        dish['quantity'] = 1
                        flatten_dishes.extend([dish] * quantity)
                    else:
                        flatten_dishes.append(dish)
            return flatten_dishes

        refund_dishes = flatten_dish(refund_dishes)
        order_dishes = flatten_dish(order.get("items", []))

        for refund_dish in (refund_dishes or []):
            refund_dish_id = refund_dish.get("uuid", "")
            for i in range(len(order_dishes)):
                if refund_dish_id == order_dishes[i].get("uuid", ""):
                    del order_dishes[i]
                    break
        return order_dishes

    def get_shilai_order_no_discount_fee(self, order_id, merchant_id):
        no_discount_fee = 0
        order_manager = OrderManager(merchant_id=merchant_id)
        order = order_manager.order_manager.sync_order_from_pos(order_id)
        if order is None:
            logger.error(f"pos端未查找到订单，order_id={order_id}")
            return no_discount_fee
        
        ordering_da = OrderingServiceDataAccessHelper()
        categories = ordering_da.get_categories(merchant_id=merchant_id, return_proto=False)
        categories_map = {item.get('id'): item for item in categories}
        dish_manager = DishManager(merchant_id=merchant_id)
        no_discount_fee = sum(
            self._get_shilai_dish_no_discount_fee(dish, dish_manager, categories_map)
            for dish in order.get("items", [])
        )
        return int(no_discount_fee)

    def _get_shilai_dish_no_discount_fee(self, dish, dish_manager, categories_map):
        dish_id = dish.get("dishId")
        if not dish_id:
            return 0
        db_dish = dish_manager.get_dish_from_cache(dish_id)
        no_discount = False
        for category_id in db_dish.categories:
            category = categories_map.get(category_id)
            if not category:
                break
            no_discount = category.get("noDiscount", False)
        if no_discount:
            return (dish.get("price", 0) * dish.get("quantity", 0))

        return 0

        # no_discount_fee = 0
        # prepay_strategy_params = dish.get("prepayStrategyParams", [])
        # for strategy in prepay_strategy_params:
        #     if strategy.get("name", "") == "itemSaleTimeDiscount" and strategy.get("value", 0) != "0":
        #         break
        #     no_discount_fee = (dish.get("price", 0) * dish.get("quantity", 0))
        # if not prepay_strategy_params:
        #     no_discount_fee = (dish.get("price", 0) * dish.get("quantity", 0))
        # return no_discount_fee

    def get_and_preprocess_qrcode_obj(self, qrcode_id, merchant_id, order_id, bill_fee, callback_url):
        fanpiao_manager = FanpiaoManager()
        qrcode_obj = fanpiao_manager.get_user_fanpiao_qrcode(qrcode_id=qrcode_id)
        if not qrcode_obj:
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_EXPIRED)
        if qrcode_obj.is_paid == "1":
            raise errors.Error(err=error_codes.FANPIAO_QRCODE_USED)
        fanpiao_manager.update_fanpiao_scan_qrcode_obj(
            qrcode_obj,
            merchant_id=merchant_id,
            bill_fee=bill_fee,
            order_id=order_id,
            callback_url=callback_url
        )
        return qrcode_obj

    def prepay(self, auth_no, order_amt, order_no):
        if len(auth_no) != 32:
            gen = self.wechat_or_alipay_prepay(auth_no)
        else:
            gen = self.fanpiao_prepay(auth_no, order_amt)
        pay_method = next(gen)
        ordering_da = OrderingServiceDataAccessHelper()
        manager = DirectPayManager(
            merchant=self.merchant,
            user_id=self.user_id,
            bill_fee=order_amt,
            paid_fee=order_amt,
            pay_method=pay_method
        )
        try:
            result = gen.send(manager)
        except StopIteration:
            pass
        manager.order.handheld_pos_order_no = order_no
        ordering_da.add_or_update_order(manager.order)
        self.order = manager.order
        self.transaction = manager.transaction

    def wechat_or_alipay_prepay(self, auth_no):
        if len(auth_no) == 32:
            return
        prefix = auth_no[0:2]
        if prefix == '28':
            pay_method = wallet_pb.Transaction.ALIPAY
            pay_channel = wallet_pb.Transaction.ALIPAY_CHANNEL
        elif prefix in ['10', '11', '12', '13', '14', '15']:
            pay_method = wallet_pb.Transaction.WECHAT_PAY
            pay_channel = wallet_pb.Transaction.WECHAT_CHANNEL
        manager = yield pay_method
        try:
            result = manager.prepay(auth_code=auth_no, pay_channel=pay_channel)
        except errors.QrcodeInvalid as ex:
            transaction_da = TransactionDataAccessHelper()
            ordering_da = OrderingServiceDataAccessHelper()
            transaction = transaction_da.get_transaction_by_auth_code(auth_no)
            if not transaction:
                raise ex
            order = ordering_da.get_order(transaction_id=transaction.id)
            if not order:
                raise ex

    def fanpiao_prepay(self, qrcode_id, bill_fee):
        if len(qrcode_id) != 32:
            return
        qrcode_obj = self.get_and_preprocess_qrcode_obj(
            qrcode_id=qrcode_id,
            bill_fee=bill_fee,
            merchant_id=self.merchant.id,
            callback_url=None,
            order_id=None
        )
        self.user_id = qrcode_obj.user_id
        pay_method = wallet_pb.Transaction.FANPIAO_PAY
        manager = yield pay_method
        manager.prepay()
        self.handheld_pos_order_fanpiao_prepay(qrcode_obj, manager.transaction, manager.order)

# -*- coding: utf-8 -*-

"""团购模板模块."""

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.promotion.group_purchase.group_purchase_template_da_helper import GroupPurchaseTemplateDAHelper


class GroupPurchaseTemplateManager(BaseManager):
    """团购模板类."""

    def __init__(self, *args, **kargs):
        """团购模板初始化."""
        super().__init__(*args, **kargs)
        self.__da_helper = GroupPurchaseTemplateDAHelper()

    def create_group_purchase_template(
            self
    ) -> group_purchase_pb.GroupPurchaseTemplate:
        """创建团购模板实体."""
        obj = group_purchase_pb.GroupPurchaseTemplate()
        obj.id = id_manager.generate_common_id()
        return obj

    def update_group_purchase_template(
            self,
            group_purchase_template: group_purchase_pb.GroupPurchaseTemplate,
            **kargs
    ):
        """更新团购模板."""
        if group_purchase_template is None:
            return
        self.__updated_group_purchase_template = group_purchase_template
        self.__update_name(kargs.get('name'))
        self.__add_opening_coupon_template_ids(
            kargs.get("added_opening_coupon_template_ids"))
        self.__remove_opening_coupon_template_ids(
            kargs.get("removed_opening_coupon_template_ids"))
        self.__replace_opening_coupon_template_ids(
            kargs.get("opening_coupon_template_ids"))
        self.__add_closing_coupon_template_ids(
            kargs.get("added_closing_coupon_template_ids"))
        self.__remove_closing_coupon_template_ids(
            kargs.get("removed_closing_coupon_template_ids"))
        self.__replace_closing_coupon_template_ids(
            kargs.get("closing_coupon_template_ids"))
        self.__update_member_coupon_template_id(
            kargs.get("member_coupon_template_id"))
        self.__update_member_coupon_count(
            kargs.get("member_coupon_count"))
        self.__update_member_coupon_min_bill_fee(
            kargs.get("member_coupon_min_bill_fee"))
        self.__update_member_coupon_reduce_fee(
            kargs.get("member_coupon_reduce_fee"))
        self.__update_total_value(kargs.get('total_value'))
        self.__update_sell_price(kargs.get('sell_price'))
        self.__update_valid_period(kargs.get('valid_period'))

    def __update_name(self, name):
        if name is None:
            return
        self.__updated_group_purchase_template.name = name

    def __add_opening_coupon_template_ids(self, coupon_template_ids):
        """新增到opening_coupon_template_ids字段."""
        if coupon_template_ids is None:
            return
        if len(coupon_template_ids) == 0:
            return
        for coupon_template_id in coupon_template_ids:
            if coupon_template_id in self.__updated_group_purchase_template.opening_coupon_template_ids:
                continue
            self.__updated_group_purchase_template.opening_coupon_template_ids.append(coupon_template_id)

    def __remove_opening_coupon_template_ids(self, coupon_template_ids):
        """从opening_coupon_template_ids中删除部分id."""
        if coupon_template_ids is None:
            return
        if len(coupon_template_ids) == 0:
            return
        for coupon_template_id in coupon_template_ids:
            if coupon_template_id not in self.__updated_group_purchase_template.opening_coupon_template_ids:
                continue
            self.__updated_group_purchase_template.opening_coupon_template_ids.remove(coupon_template_id)

    def __replace_opening_coupon_template_ids(self, coupon_template_ids):
        """用coupon_template_ids替换掉opening_coupon_template_ids字段.

        旧的opening_coupon_template_ids会被完全从template中删除
        """
        if coupon_template_ids is None:
            return
        while self.__updated_group_purchase_template.opening_coupon_template_ids:
            self.__updated_group_purchase_template.opening_coupon_template_ids.pop()
        for coupon_template_id in coupon_template_ids:
            self.__updated_group_purchase_template.opening_coupon_template_ids.append(coupon_template_id)

    def __add_closing_coupon_template_ids(self, coupon_template_ids):
        """新增到closing_coupon_template_ids字段."""
        if coupon_template_ids is None:
            return
        if len(coupon_template_ids) == 0:
            return
        for coupon_template_id in coupon_template_ids:
            if coupon_template_id in self.__updated_group_purchase_template.closing_coupon_template_ids:
                continue
            self.__updated_group_purchase_template.closing_coupon_template_ids.append(coupon_template_id)

    def __remove_closing_coupon_template_ids(self, coupon_template_ids):
        """从closing_coupon_template_ids中删除."""
        if coupon_template_ids is None:
            return
        if len(coupon_template_ids) == 0:
            return
        for coupon_template_id in coupon_template_ids:
            if coupon_template_id not in self.__updated_group_purchase_template.closing_coupon_template_ids:
                continue
            self.__updated_group_purchase_template.closing_coupon_template_ids.remove(coupon_template_id)

    def __replace_closing_coupon_template_ids(self, coupon_template_ids):
        """用coupon_template_ids替换掉closing_coupon_template_ids字段.

        旧的closing_coupon_template_ids会被完全从template中删除
        """
        if coupon_template_ids is None:
            return
        while self.__updated_group_purchase_template.closing_coupon_template_ids:
            self.__updated_group_purchase_template.closing_coupon_template_ids.pop()
        for coupon_template_id in coupon_template_ids:
            self.__updated_group_purchase_template.closing_coupon_template_ids.append(coupon_template_id)

    def __update_member_coupon_template_id(self, member_coupon_template_id):
        if member_coupon_template_id is None:
            return
        self.__updated_group_purchase_template.member_coupon_template_id = member_coupon_template_id

    def __update_member_coupon_count(self, member_coupon_count):
        if member_coupon_count is None:
            return
        self.__updated_group_purchase_template.member_coupon_count = member_coupon_count

    def __update_member_coupon_min_bill_fee(self, member_coupon_min_bill_fee):
        if member_coupon_min_bill_fee is None:
            return
        self.__updated_group_purchase_template.member_coupon_min_bill_fee = member_coupon_min_bill_fee

    def __update_member_coupon_reduce_fee(self, member_coupon_reduce_fee):
        if member_coupon_reduce_fee is None:
            return
        self.__updated_group_purchase_template.member_coupon_reduce_fee = member_coupon_reduce_fee

    def __update_total_value(self, total_value):
        if total_value is None:
            return
        self.__updated_group_purchase_template.total_value = total_value

    def __update_sell_price(self, sell_price):
        if sell_price is None:
            return
        self.__updated_group_purchase_template.sell_price = sell_price

    def __update_valid_period(self, valid_period):
        if valid_period is None:
            return
        self.__updated_group_purchase_template.valid_period = valid_period

    def add_or_update_group_purchase_template(self, group_purchase_template):
        """更新团购模板到数据库."""
        if group_purchase_template is None:
            return
        self.__da_helper.add_or_update_group_purchase_template(group_purchase_template)

    def get_group_purchase_template(self, id=None):
        """查找单个团购模板."""
        return self.__da_helper.get_group_purchase_template(id=id)

    def get_group_purchase_template_list(self, ids=None):
        """查找多个团购模板."""
        return self.__da_helper.get_group_purchase_template_list(ids=ids)

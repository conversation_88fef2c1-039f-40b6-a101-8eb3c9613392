# -*- coding: utf-8 -*-

"""团购模块."""

import time
import logging
import random
from datetime import datetime

import proto.finance.wallet_pb2 as wallet_pb
import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.base_manager import BaseManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.wallet_manager import WalletManager
from business_ops.message_center.message_manager import MessageManager
from common.utils import id_manager
from common.utils import distribute_lock
from common.utils import date_utils
from dao.promotion.group_purchase.group_purchase_da_helper import GroupPurchaseDAHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors
from service import error_codes
from wechat_lib.subscribe_message import SubscribeMessage

logger = logging.getLogger(__name__)


class GroupPurchaseManager(BaseManager):
    """团购类."""

    def __init__(self, *args, **kargs):
        """团购类初始化."""
        super().__init__(*args, **kargs)
        self.__da_helper = GroupPurchaseDAHelper()

        self.bill_fee = kargs.get('bill_fee')
        self.paid_fee = kargs.get('paid_fee')
        self.wallet_fee = kargs.get('wallet_fee') or 0
        self.pay_method = kargs.get('pay_method')
        self.group_purchase_template = kargs.get('group_purchase_template')

    def create_group_purchase(
            self,
            group_purchase_template,
    ) -> group_purchase_pb.GroupPurchase:
        """创建团购实体."""
        obj = group_purchase_pb.GroupPurchase()
        obj.id = id_manager.generate_common_id()
        obj.template_id = group_purchase_template.id
        obj.create_time = int(datetime.now().timestamp())
        obj.leader_id = self.user.id
        obj.leader_nickname = self.user.member_profile.nickname
        obj.leader_avatar_url = self.user.member_profile.head_image_url
        obj.member_coupon_template_id = group_purchase_template.member_coupon_template_id
        obj.leader_coupon_template_id = group_purchase_template.leader_coupon_template_id
        obj.member_coupon_count = group_purchase_template.member_coupon_count
        obj.member_coupon_min_bill_fee = group_purchase_template.member_coupon_min_bill_fee
        obj.member_coupon_reduce_fee = group_purchase_template.member_coupon_reduce_fee
        obj.total_value = group_purchase_template.total_value
        obj.sell_price = group_purchase_template.sell_price
        obj.valid_period = group_purchase_template.valid_period
        obj.name = group_purchase_template.name
        obj.merchant_id = self.merchant.id
        return obj

    def update_group_purchase(
            self,
            group_purchase: group_purchase_pb.GroupPurchase,
            **kargs
    ):
        """更新团购."""
        if group_purchase is None:
            return
        self.__group_purchase = group_purchase
        self.__update_status(kargs.get('status'))
        self.__update_name(kargs.get('name'))

    def __update_status(self, status):
        if status is None:
            return
        now = int(datetime.now().timestamp())
        if isinstance(status, str):
            status = group_purchase_pb.GroupPurchase.GroupPurchaseStatus.Value(status)
        if status == group_purchase_pb.GroupPurchase.FINISHED:
            self.__group_purchase.finish_time = now
        elif status == group_purchase_pb.GroupPurchase.EXPIRED:
            self.__group_purchase.expire_time = now
        elif status == group_purchase_pb.GroupPurchase.ACTIVE:
            self.__group_purchase.active_time = now
        self.__group_purchase.status = status

    def __update_name(self, name):
        if name is None:
            return
        self.__group_purchase.name = name

    def add_member(self, group_purchase, coupon_id):
        key = f'group_purchase_add_member_{group_purchase.id}'
        with distribute_lock.redislock(key, 10, 5000, 1000) as lock:
            if not lock:
                return False
            _group_purchase = self.get_group_purchase(id=group_purchase.id)
            if len(_group_purchase.group_members) == _group_purchase.member_coupon_count:
                return False
            if group_purchase.leader_id == self.user.id: # 团长不能参与自己的团
                # TODO: 在 2022-12-14 16:00 之后开的团,团长不能参与自己的团
                if group_purchase.create_time > 1671004800:
                    return False
            # *** 直接进行一次Copy会破坏掉调用此函数之前对group_purchase的修改
            # 所以不能使用此CopyFrom的方式
            # if group_purchase.update_time < _group_purchase.update_time:
            #     group_purchase.CopyFrom(_group_purchase)

            # 当A,B同时要求加入团
            # 获取到的团数据为G1,G2此时 G1与G2是相同的
            # A先获取到锁，处理完成之后，G1 > G2的
            # B再获取到锁，就需要将A的group_member添加到G2中
            len1 = len(_group_purchase.group_members)
            len2 = len(group_purchase.group_members)
            if len1 > len2:
                for _g in _group_purchase.group_members[len2:]:
                    g = group_purchase.group_members.add()
                    g.CopyFrom(_g)

            _member = group_purchase.group_members.add()
            _member.user_id = self.user.id
            _member.user_nickname = self.user.member_profile.nickname
            _member.user_avatar_url = self.user.member_profile.head_image_url
            _member.purchase_time = int(datetime.now().timestamp())
            _member.coupon_id = coupon_id
            _member.leader_cash_refund_transaction_id = id_manager.generate_common_id()
            logger.info(f"{_member.user_nickname} 加入团 {group_purchase.id}")
            self.add_or_update_group_purchase(group_purchase)
            return True

    def remove_members(self, group_purchase, members):
        key = f'group_purchase_add_member_{group_purchase.id}'
        with distribute_lock.redislock(key, 10, 5000, 1000) as lock:
            if not lock:
                return False
            for member in members:
                group_purchase.group_members.remove(member)
            self.add_or_update_group_purchase(group_purchase)
            return True

    def add_or_update_group_purchase(self, group_purchase):
        """更新或者新增团购."""
        if group_purchase is None:
            return
        group_purchase.update_time = int(datetime.now().timestamp())
        self.__da_helper.add_or_update_group_purchase(group_purchase)

    def get_group_purchase(self, id=None, transaction_id=None, coupon_id=None, closing_coupon_id=None):
        """获取单个团购."""
        return self.__da_helper.get_group_purchase(
            id=id,
            transaction_id=transaction_id,
            coupon_id=coupon_id,
            closing_coupon_id=closing_coupon_id
        )

    def join_group_purchase(self, group_purchase, coupon_id):
        result = False
        if group_purchase is not None:
            if group_purchase.status != group_purchase_pb.GroupPurchase.ACTIVE:
                raise errors.GroupPurchaseFullError()
            result = self.add_member(group_purchase, coupon_id)
            # 如果是团长无法加入此团,则直接返回出错
            if not result:
                raise errors.GroupPurchaseFullError()
        return group_purchase

    def join_random_group_purchase(self, coupon_id, bill_fee):
        """随机加入一个团购."""
        group_purchases = self.__da_helper.get_group_purchase_list(
            merchant_id=self.merchant.id,
            status=group_purchase_pb.GroupPurchase.ACTIVE
        )
        random.shuffle(group_purchases)
        for group_purchase in group_purchases:
            if bill_fee < group_purchase.member_coupon_min_bill_fee:
                continue
            # 随机加入的时候只能作为团员加入
            if group_purchase.leader_id == self.user.id:
                continue
            result = self.add_member(group_purchase, coupon_id)
            if result:
                return group_purchase
        raise errors.GroupPurchaseFullError()

    def get_group_purchase_list(
            self, ids=None, with_leader=None, with_merchant=None, status=None,
            start_update_time=None, multi_status=None
    ):
        """获取团购列表."""
        matcher = {
            'ids': ids,
            'status': status,
            'start_update_time': start_update_time,
            'multi_status': multi_status
        }
        if with_leader is None:
            with_leader = False
        if with_merchant is None:
            with_merchant = False
        if with_leader and self.user:
            matcher.update({'leader_id': self.user.id})
        if with_merchant and self.merchant:
            matcher.update({'merchant_id': self.merchant.id})
        if with_leader and not self.user:
            return []
        return self.__da_helper.get_group_purchase_list(**matcher)

    def prepay(self):
        if self.pay_method not in [
                wallet_pb.Transaction.WECHAT_PAY,
                wallet_pb.Transaction.ALIPAY
        ]:
            raise errors.ShowError("支付方式不支持")
        self.__check_active_group_purchase_uplimit()
        if self.wallet_fee == self.group_purchase_template.sell_price:
            self.pay_method = wallet_pb.Transaction.WALLET
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_open_group_purchase_transaction(
            self.merchant.id,
            self.user.id,
            self.group_purchase_template.total_value,
            self.group_purchase_template.sell_price,
            self.pay_method
        )
        if self.pay_method in [wallet_pb.Transaction.WECHAT_PAY, wallet_pb.Transaction.ALIPAY]:
            if self.wallet_fee == 0:
                result = self.__prepay_only_wa_pay()
            else:
                result = self.__prepay_wa_wallet_combine()
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            result = self.__prepay_only_wallet_pay()
        else:
            raise errors.ShowError("支付方式不支持")
        return result

    def check_active_group_purchase_uplimit(self):
        return self.__check_active_group_purchase_uplimit()

    def __check_active_group_purchase_uplimit(self):
        group_purchases = self.get_group_purchase_list(
            status=group_purchase_pb.GroupPurchase.ACTIVE,
            with_leader=True,
            with_merchant=True
        )
        if len(group_purchases) >= 3:
            raise errors.ShowError("亲～最多只能有3个进行中的团哦")

    def __prepay_wa_wallet_combine(self):
        """微信/支付宝 与时来钱包合并支付开团"""
        transaction_manager = TransactionManager()
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        self.transaction.paid_fee -= self.wallet_fee
        result = payment_manager.prepay(
            transaction=self.transaction, merchant_id=self.merchant.id)
        if result.get("errcode") != error_codes.SUCCESS:
            raise errors.ShowError("发起支付失败")
        group_purchase = self.create_group_purchase(self.group_purchase_template)
        group_purchase.transaction_id = self.transaction.id
        group_purchase.transaction_ids.append(self.transaction.id)
        if self.wallet_fee > 0:
            wallet = WalletManager().get_or_create_user_wallet(self.user)
            if wallet.balance < self.wallet_fee:
                raise errors.ShowError("钱包余额不足")
            wallet_pay_transaction = transaction_manager.create_open_group_purchase_transaction(
                self.merchant.id,
                self.user.id,
                self.group_purchase_template.total_value,
                self.wallet_fee,
                wallet_pb.Transaction.WALLET
            )
            group_purchase.transaction_ids.append(wallet_pay_transaction.id)
        self.add_or_update_group_purchase(group_purchase)
        result.update({'transactionId': self.transaction.id})
        transaction_manager.update_transaction(self.transaction)
        return result

    def __prepay_only_wallet_pay(self):
        """时来钱包支付开团"""
        wallet_manager = WalletManager()
        wallet = wallet_manager.get_or_create_user_wallet(self.user)
        if wallet.balance < self.transaction.paid_fee:
            raise errors.ShowError("钱包余额不足")
        group_purchase = self.create_group_purchase(self.group_purchase_template)
        group_purchase.transaction_id = self.transaction.id
        group_purchase.transaction_ids.append(self.transaction.id)
        self.notification(group_purchase)

    def __prepay_only_wa_pay(self):
        """微信/支付宝支付开团"""
        transaction_manager = TransactionManager()
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        result = payment_manager.prepay(
            transaction=self.transaction, merchant_id=self.merchant.id)
        if result.get("errcode") == error_codes.SUCCESS:
            group_purchase = self.create_group_purchase(self.group_purchase_template)
            group_purchase.transaction_id = self.transaction.id
            group_purchase.transaction_ids.append(self.transaction.id)
            self.add_or_update_group_purchase(group_purchase)
            result.update({'transactionId': self.transaction.id})
            transaction_manager.update_transaction(self.transaction)
        return result

    def create_group_purchase_union_pay(self, group_purchase_template, parent_transaction):
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_open_group_purchase_transaction(
            self.merchant.id,
            self.user.id,
            group_purchase_template.total_value,
            group_purchase_template.sell_price,
            parent_transaction.pay_method
        )
        group_purchase = self.create_group_purchase(group_purchase_template)
        group_purchase.transaction_id = self.transaction.id
        self.add_or_update_group_purchase(group_purchase)
        return group_purchase

    def notification(self, group_purchase=None):
        if group_purchase is None:
            group_purchase = self.get_group_purchase(transaction_id=self.transaction.id)
        self.update_group_purchase(group_purchase, status=group_purchase_pb.GroupPurchase.ACTIVE)
        self.__launch_ledger()
        self.__issue_leader_reduce_coupon(group_purchase)
        self.add_or_update_group_purchase(group_purchase)
        sm = SubscribeMessage()
        sm.create_group_purchase_subscribe_message(self.user, self.merchant, group_purchase)
        transaction_manager = TransactionManager()
        transaction_manager.set_transaction_success(self.transaction)
        self.__update_transactions(group_purchase)
        MessageManager().create_group_purchase_opened_message(self.user, group_purchase)

    def __update_transactions(self, group_purchase):
        transaction_manager = TransactionManager()
        transaction_da = TransactionDataAccessHelper()
        for transaction_id in group_purchase.transaction_ids:
            transaction = transaction_da.get_transaction_by_id(transaction_id)
            transaction_manager.set_transaction_success(transaction)
            self.__decrease_wallet_balance(transaction)

    def __decrease_wallet_balance(self, transaction):
        if transaction.pay_method != wallet_pb.Transaction.WALLET:
            return
        wallet_manager = WalletManager()
        wallet_manager.decrease_balance(transaction.payer_id, transaction.paid_fee)

    def group_purchase_union_pay_notification(self, group_purchase, transaction):
        self.transaction = transaction
        self.update_group_purchase(group_purchase, status=group_purchase_pb.GroupPurchase.ACTIVE)
        self.__issue_leader_reduce_coupon(group_purchase)
        self.add_or_update_group_purchase(group_purchase)
        sm = SubscribeMessage()
        sm.create_group_purchase_subscribe_message(self.user, self.merchant, group_purchase)
        transaction_manager = TransactionManager()
        transaction_manager.set_transaction_success(self.transaction)

    def __launch_ledger(self):
        if self.transaction.ledgered:
            return
        if self.transaction.pay_method == wallet_pb.Transaction.WALLET:
            return
        payment_manager = PaymentManager(wallet_pb.Transaction.TIAN_QUE_PAY, merchant=self.merchant)
        payment_manager.do_allocate_to_shilai(self.transaction)

    def __issue_leader_reduce_coupon(self, group_purchase):
        """为团长发开团券."""
        group_purchase_template_manager = GroupPurchaseTemplateManager()
        group_purchase_template = group_purchase_template_manager.get_group_purchase_template(id=group_purchase.template_id)
        coupon_template_manager = CouponTemplateManager()
        coupon_manager = CouponManager(user=self.user, merchant=self.merchant)
        for opening_coupon_template_id in group_purchase_template.opening_coupon_template_ids:
            coupon_template = coupon_template_manager.get_coupon_template(opening_coupon_template_id)
            coupon = coupon_manager.create_coupon(coupon_template)
            coupon_manager.update_coupon(
                coupon,
                status=coupon_pb.Coupon.ACCEPTED,
                coupon_type=coupon_pb.Coupon.OPENING_GROUP_PURCHASE
            )
            coupon_manager.add_or_update_coupon(coupon)
            group_purchase.opening_coupon_ids.append(coupon.id)

        for closing_coupon_template_id in group_purchase_template.closing_coupon_template_ids:
            coupon_template = coupon_template_manager.get_coupon_template(closing_coupon_template_id)
            coupon = coupon_manager.create_coupon(coupon_template)
            coupon_manager.update_coupon(
                coupon,
                coupon_type=coupon_pb.Coupon.CLOSING_GROUP_PURCHASE
            )
            coupon_manager.add_or_update_coupon(coupon)
            group_purchase.closing_coupon_ids.append(coupon.id)

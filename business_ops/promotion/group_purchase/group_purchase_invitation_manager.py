# -*- coding: utf-8 -*-

"""团购邀请模块."""

from datetime import datetime

import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.promotion.group_purchase.group_purchase_invitation_da_hepler import GroupPurchaseInvitationDAHelper


class GroupPurchaseInvitationManager(BaseManager):
    """团购邀请类."""

    def __init__(self, *args, **kargs):
        """团购邀请初始化."""
        super().__init__(*args, **kargs)
        self.__da_helper = GroupPurchaseInvitationDAHelper()

    def create_group_purchase_invitation(
            self,
            group_purchase,
    ) -> group_purchase_pb.GroupPurchaseInvitation:
        """创建团购邀请实体."""
        obj = group_purchase_pb.GroupPurchaseInvitation()
        obj.id = id_manager.generate_common_id()
        obj.member_id = self.user.id
        obj.member_avatar_url = self.user.member_profile.head_image_url
        obj.leader_id = group_purchase.leader_id
        obj.leader_avatar_url = group_purchase.leader_avatar_url
        obj.join_group_id = group_purchase.id
        obj.invite_time = int(datetime.now().timestamp())
        obj.member_coupon_min_bill_fee = group_purchase.member_coupon_min_bill_fee
        obj.member_coupon_reduce_fee = group_purchase.member_coupon_reduce_fee
        return obj

    def get_group_purchase_invitation(self, id=None, member_id=None, group_purchase_id=None):
        result = self.__da_helper.get_group_purchase_invitation(
            id=id,
            member_id=member_id,
            join_group_id=group_purchase_id
        )
        return result

    def get_group_purchase_invitation_list(self, ids=None, join_group_id=None):
        result = self.__da_helper.get_group_purchase_invitation_list(
            ids=ids,
            join_group_id=join_group_id,
            member_id=self.user.id
        )
        return result

    def update_group_purchase_invitation(
            self,
            group_purchase_invitation: group_purchase_pb.GroupPurchaseInvitation,
            **kargs
    ):
        """更新团购邀请."""
        if group_purchase_invitation is None:
            return
        self.__group_purchase_invitation = group_purchase_invitation
        self.__update_status(kargs.get('status'))

    def __update_status(self, status):
        if status is None:
            return
        if isinstance(status, str):
            status = group_purchase_pb.GroupPurchaseInvitation.GroupPurchaseInvitationStatus.Value(status)
        now = int(datetime.now().timestamp())
        if status == group_purchase_pb.GroupPurchaseInvitation.USED:
            self.__group_purchase_invitation.use_time = now
        elif status == group_purchase_pb.GroupPurchaseInvitation.EXPIRED:
            self.__group_purchase_invitation.expire_time = now
        elif status == group_purchase_pb.GroupPurchaseInvitation.DELETED:
            self.__group_purchase_invitation.delete_time = now
        self.__group_purchase_invitation.status = status

    def add_or_update_group_purchase_invitation(self, group_purchase_invitation):
        if group_purchase_invitation is None:
            return
        self.__da_helper.add_or_update_group_purchase_invitation(group_purchase_invitation)

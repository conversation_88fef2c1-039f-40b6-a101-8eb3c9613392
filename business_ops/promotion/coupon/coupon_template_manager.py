# -*- coding: utf-8 -*-

"""券模板管理模块."""

import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.promotion.coupon.coupon_template_da_hepler import CouponTemplateDAHelper
from service import errors


class CouponTemplateManager(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.__da_helper = CouponTemplateDAHelper()

    def create_coupon_template(self):
        obj = coupon_pb.CouponTemplate()
        obj.id = id_manager.generate_common_id()
        return obj

    def get_coupon_template(self, id=None):
        return self.__da_helper.get_coupon_template(id=id)

    def get_coupon_template_list(self, ids):
        return self.__da_helper.get_coupon_template_list(ids=ids)

    def get_coupon_template_reduce_fee(self, bill_fee, coupon_template):
        if coupon_template.promotion_type == coupon_pb.FIXED_REDUCE_AMOUNT:
            return self.__get_fixed_reduce_amount_reduce_fee(bill_fee, coupon_template)
        if coupon_template.promotion_type == coupon_pb.FIXED_DISCOUNT:
            return self.__get_fixed_discount_reduce_fee(bill_fee, coupon_template)
        if coupon_template.promotion_type == coupon_pb.RANDOM_REDUCE_AMOUNT:
            return self.__get_random_reduce_amount(bill_fee, coupon_template)

    def __get_fixed_reduce_amount_reduce_fee(self, bill_fee, coupon_template):
        return coupon_template.fixed_reduce_amount.reduce_fee

    def __get_fixed_discount_reduce_fee(self, bill_fee, coupon_template):
        fee = int(coupon_template.fixed_discount.discount * bill_fee / 100 + 0.5)
        return fee

    def __get_random_reduce_amount(self, bill_fee, coupon_template):
        # TODO: 该场景未明确
        return 0

    def get_coupon_template_min_bill_fee(self, coupon_template):
        if coupon_template.promotion_type == coupon_pb.FIXED_REDUCE_AMOUNT:
            return self.__get_fixed_reduce_amount_min_bill_fee(coupon_template)
        if coupon_template.promotion_type == coupon_pb.FIXED_DISCOUNT:
            return self.__get_fixed_discount_min_bill_fee(coupon_template)
        if coupon_template.promotion_type == coupon_pb.RANDOM_REDUCE_AMOUNT:
            return self.__get_random_min_bill_amount(coupon_template)

    def __get_fixed_reduce_amount_min_bill_fee(self, coupon_template):
        return coupon_template.fixed_reduce_amount.min_bill_fee

    def __get_fixed_discount_min_bill_fee(self, coupon_template):
        return 0

    def __get_random_min_bill_amount(self, coupon_template):
        # TODO: 该场景未明确
        return coupon_template.random_reduce_amount.min_bill_fee

    def update_coupon_template(self, coupon_template, **kargs):
        if coupon_template is None:
            return
        self.__coupon_template = coupon_template
        self.__update_name(kargs.get('name'))
        self.__update_promotion_type(kargs.get('promotion_type'))
        self.__update_reduce_fee_type(kargs.get('reduce_fee_type'))
        self.__update_usage_limitation(kargs.get('usage_limitation'))
        self.__update_promotion(kargs)

    def __update_name(self, name):
        if name is None:
            return
        self.__coupon_template.name = name

    def __update_promotion_type(self, promotion_type):
        if promotion_type is None:
            return
        if isinstance(promotion_type, str):
            promotion_type = coupon_pb.PromotionType.Value(promotion_type)
        self.__coupon_template.promotion_type = promotion_type

    def __update_reduce_fee_type(self, reduce_fee_type):
        if reduce_fee_type is None:
            return
        if isinstance(reduce_fee_type, str):
            reduce_fee_type = coupon_pb.ReduceFeeType.Value(reduce_fee_type)
        self.__coupon_template.reduce_fee_type = reduce_fee_type

    def __update_usage_limitation(self, usage_limitation):
        if usage_limitation is None:
            return
        self.__update_time_limitation(usage_limitation.get('timeLimitation'))

    def __update_time_limitation(self, time_limitation):
        if time_limitation is None:
            return
        self.__coupon_template.usage_limitation.time_limitation.valid_period = time_limitation.get('validPeriod')
        self.__coupon_template.usage_limitation.time_limitation.effective_start_time = time_limitation.get('effectiveStartTime')
        self.__coupon_template.usage_limitation.time_limitation.effective_end_time = time_limitation.get('effectiveEndTime')

    def __update_promotion(self, kargs):
        if not ('fixed_reduce_amount' in kargs \
                or 'fixed_discount' in kargs \
                or 'random_reduce_amount' in kargs):
            return
        if self.__coupon_template.HasField('fixed_reduce_amount'):
            self.__coupon_template.ClearField('fixed_reduce_amount')
        if self.__coupon_template.HasField('fixed_discount'):
            self.__coupon_template.ClearField('fixed_discount')
        if self.__coupon_template.HasField('random_reduce_amount'):
            self.__coupon_template.ClearField('random_reduce_amount')
        self.__update_fixed_reduce_amount(kargs.get('fixed_reduce_amount'))
        self.__update_fixed_discount(kargs.get('fixed_discount'))
        self.__update_random_reduce_amount(kargs.get('random_reduce_amount'))

    def __update_fixed_reduce_amount(self, fixed_reduce_amount):
        if fixed_reduce_amount is None:
            return
        if self.__coupon_template.promotion_type != coupon_pb.FIXED_REDUCE_AMOUNT:
            raise errors.ShowError("PromotionType参数错误")
        self.__coupon_template.fixed_reduce_amount.min_bill_fee = fixed_reduce_amount.get('minBillFee')
        self.__coupon_template.fixed_reduce_amount.max_bill_fee = fixed_reduce_amount.get('maxBillFee')
        self.__coupon_template.fixed_reduce_amount.reduce_fee = fixed_reduce_amount.get('reduceFee')

    def __update_fixed_discount(self, fixed_discount):
        if fixed_discount is None:
            return
        if self.__coupon_template.promotion_type != coupon_pb.FIXED_DISCOUNT:
            raise errors.ShowError("PromotionType参数错误")
        self.__coupon_template.fixed_discount.discount = fixed_discount.get('discount')

    def __update_random_reduce_amount(self, random_reduce_amount):
        if random_reduce_amount is None:
            return
        if self.__coupon_template.promotion_type != coupon_pb.RANDOM_REDUCE_AMOUNT:
            raise errors.ShowError("PromotionType参数错误")
        self.__coupon_template.random_reduce_amount.min_bill_fee = random_reduce_amount.get('minBillFee')
        self.__coupon_template.random_reduce_amount.mab_bill_fee = random_reduce_amount.get('mabBillFee')
        self.__coupon_template.random_reduce_amount.min_random_reduce_fee = random_reduce_amount.get('minRandomReduceFee')
        self.__coupon_template.random_reduce_amount.max_random_reduce_fee = random_reduce_amount.get('maxRandomReduceFee')

    def add_or_update_coupon_template(self, coupon_template):
        if coupon_template is None:
            return
        self.__da_helper.add_or_update_coupon_template(coupon_template)

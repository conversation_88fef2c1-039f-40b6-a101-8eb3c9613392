# -*- coding: utf-8 -*-

"""券管理模块."""

import logging
import time

import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.base_manager import BaseManager
from business_ops.wallet_manager import WalletManager
from common.utils import id_manager
from dao.promotion.coupon.coupon_da_hepler import CouponDAHelper
from dao.wallet_da_helper import WalletDataAccessHelper

logger = logging.getLogger(__name__)


class CouponManager(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.__da_helper = CouponDAHelper()

    def create_coupon(
            self,
            coupon_template: coupon_pb.CouponTemplate,
    ) -> coupon_pb.Coupon:
        """创建券实体."""
        obj = coupon_pb.Coupon()
        obj.id = id_manager.generate_common_id()
        obj.template_id = coupon_template.id
        obj.name = coupon_template.name
        obj.category_name = coupon_template.category_name
        obj.merchant_id = self.merchant.id
        obj.user_id = self.user.id
        obj.create_time = int(time.time())
        obj.promotion_type = coupon_template.promotion_type
        if coupon_template.promotion_type == coupon_pb.FIXED_REDUCE_AMOUNT:
            obj.fixed_reduce_amount.CopyFrom(coupon_template.fixed_reduce_amount)
        if coupon_template.promotion_type == coupon_pb.FIXED_DISCOUNT:
            obj.fixed_discount.CopyFrom(coupon_template.fixed_discount)
        if coupon_template.promotion_type == coupon_pb.RANDOM_REDUCE_AMOUNT:
            obj.random_reduce_amount.CopyFrom(coupon_template.random_reduce_amount)
        obj.reduce_fee_type = coupon_template.reduce_fee_type
        self.__set_time_limitation(obj, coupon_template)
        return obj

    def __set_time_limitation(self, coupon, coupon_template):
        coupon.usage_limitation.CopyFrom(coupon_template.usage_limitation)
        now = int(time.time())
        valid_period = coupon_template.usage_limitation.time_limitation.valid_period
        if coupon_template.usage_limitation.time_limitation.valid_period > 0:
            coupon.usage_limitation.time_limitation.effective_start_time = now
            coupon.usage_limitation.time_limitation.effective_end_time = now + valid_period
            coupon.usage_limitation.time_limitation.valid_period = 0

    def get_coupon(self, id=None):
        return self.__da_helper.get_coupon(id=id)

    def get_coupon_list(self, ids=None, status=None, coupon_type=None, reduce_fee_type=None):
        matcher = {
            'ids': ids,
            'status': status,
            'coupon_type': coupon_type,
            'reduce_fee_type': reduce_fee_type
        }
        if self.merchant:
            matcher.update({'merchant_id': self.merchant.id})
        if self.user:
            matcher.update({'user_id': self.user.id})
        result = self.__da_helper.get_coupon_list(**matcher)
        return result

    def update_coupon(self, coupon, **kargs):
        if coupon is None:
            return
        self.__coupon = coupon
        self.__update_name(kargs.get('name'))
        self.__update_status(kargs.get('status'))
        self.__update_coupon_type(kargs.get('coupon_type'))

    def consume_coupon(self, coupon):
        if coupon is None:
            logger.info(f"{self.user.id} {self.merchant.id} 核销 {coupon}")
            return
        logger.info(f"{self.user.id} {self.merchant.id} 核销 {coupon.id}")
        self.__coupon = coupon
        self.__update_status(coupon_pb.Coupon.USED)

    def consume_coin_deduction_coupon(self, coupon, balance):
        """核销时运币抵扣券"""
        if coupon.coupon_type != coupon_pb.Coupon.COIN_DEDUCTION:
            return
        self.consume_coupon(coupon)
        wallet_manager = WalletManager()
        logger.info(f"时运币抵扣 {self.user.id} {self.user.member_profile.nickname} {balance}")
        wallet_manager.decrease_coin_balance(self.user.id, balance)

    def consume_coin_refund_coupon(self, coupon):
        if coupon.reduce_fee_type != coupon_pb.COIN_REFUND:
            return
        self.consume_coupon(coupon)
        wallet_manager = WalletManager()
        balance = coupon.fixed_reduce_amount.reduce_fee
        logger.info(f"时运币增加 {self.user.id} {self.user.member_profile.nickname} {balance}")
        wallet_manager.increase_coin_balance(self.user.id, balance)

    def issue_coupon(self, coupon):
        if coupon is None:
            return
        self.__coupon = coupon
        self.__update_status(coupon_pb.Coupon.ISSUED)

    def get_coupon_reduce_fee_from_coupon(self, coupon):
        """券已使用之后,用此函数来获取券最终抵扣的金额"""
        if not coupon:
            return 0
        if coupon.promotion_type == coupon_pb.FIXED_REDUCE_AMOUNT:
            return coupon.fixed_reduce_amount.reduce_fee
        if coupon.promotion_type == coupon_pb.FIXED_DISCOUNT:
            return coupon.fixed_discount.reduce_fee
        if coupon.promotion_type == coupon_pb.RANDOM_REDUCE_AMOUNT:
            return coupon.random_reduce_amount.reduce_fee
        return 0

    def get_coupon_reduce_fee(self, bill_fee, coupon):
        """使用券时,查询券应抵扣的金额"""
        if not coupon:
            return 0
        if coupon.promotion_type == coupon_pb.FIXED_REDUCE_AMOUNT:
            return self.__get_fixed_reduce_amount_reduce_fee(bill_fee, coupon)
        if coupon.promotion_type == coupon_pb.FIXED_DISCOUNT:
            return self.__get_fixed_discount_reduce_fee(bill_fee, coupon)
        if coupon.promotion_type == coupon_pb.RANDOM_REDUCE_AMOUNT:
            return self.__get_random_reduce_amount(bill_fee, coupon)

    def __get_fixed_reduce_amount_reduce_fee(self, bill_fee, coupon):
        return coupon.fixed_reduce_amount.reduce_fee

    def __get_fixed_discount_reduce_fee(self, bill_fee, coupon):
        fee = int(coupon.fixed_discount.discount * bill_fee / 100 + 0.5)
        if coupon.coupon_type == coupon_pb.Coupon.COIN_DEDUCTION:
            wallet_da = WalletDataAccessHelper()
            wallet = wallet_da.get_user_wallet(owner_id=coupon.user_id)
            if wallet.coin_balance < fee:
                fee = wallet.coin_balance
        coupon.fixed_discount.reduce_fee = fee
        return fee

    def __get_random_reduce_amount(self, bill_fee, coupon):
        # TODO: 该场景未明确
        return 0

    def __update_name(self, name):
        if name is None:
            return
        self.__coupon.name = name

    def __update_status(self, status):
        if status is None:
            return
        if isinstance(status, str):
            status = coupon_pb.Coupon.CouponStatus.Value(status)
        now = int(time.time())
        if status == coupon_pb.Coupon.ISSUED:
            self.__coupon.issue_time = now
        elif status == coupon_pb.Coupon.ACCEPTED:
            self.__coupon.accept_time = now
        elif status == coupon_pb.Coupon.USED:
            self.__coupon.use_time = now
        elif status == coupon_pb.Coupon.EXPIRED:
            self.__coupon.expire_time = now
        elif status == coupon_pb.Coupon.DELETED:
            self.__coupon.delete_time = now
        self.__coupon.status = status

    def __update_coupon_type(self, coupon_type):
        if coupon_type is None:
            return
        if isinstance(coupon_type, str):
            coupon_type = coupon_pb.Coupon.CouponType.Value(coupon_type)
        self.__coupon.coupon_type = coupon_type

    def add_or_update_coupon(self, coupon):
        if coupon is None:
            return
        self.__da_helper.add_or_update_coupon(coupon)

    def is_coupon_expired(self, coupon):
        if coupon.status == coupon_pb.Coupon.EXPIRED:
            return True
        now = int(time.time())
        if coupon.usage_limitation.time_limitation.effective_end_time > 0 and coupon.usage_limitation.time_limitation.effective_end_time < now:
            return True
        return False

import base64
import binascii
import hmac
import hashlib
import logging
import math
import random
import os
from collections import namedtuple

import maya

from common import http
from common.config import config
from common import constants as common_constants
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

logger = logging.getLogger(__name__)

class TaotaoguManager:
    def __init__(self):
        self.appid = '8a81c1be6f19b113016f46bd018e002d'
        self.secret_key = '0f17a3b73bde48bb8f20c45966b5936d'
        self.wechat_pay_url = 'https://api-mop.chinaums.com/v1/netpay/wx/unified-order'
        self.token_url = 'https://api-mop.chinaums.com/v1/token/access'
        self.alipay_url = 'http://***********:29015/v1/netpay/trade/create'

        self.merchant_number = '898130148161866'
        self.terminal_id = '21526046'
        self.params = {}

    def prepay(self, transaction_id, return_url, order_id=None):
        transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)

        domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        notify_url = '{}/payment/payment_notification/{}'.format(domain, transaction_id)
        if order_id is not None:
            notify_url = '{}/{}'.format(notify_url, order_id)

        url = self.wechat_pay_url
        request_time = maya.when('now').datetime(to_timezone='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
        user = UserDataAccessHelper().get_user(transaction.payer_id)
        merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
        mer_order_id = '6712{}'.format(transaction_id[4:])
        domain = "http://test.shilai.zhiyi.cn"
        self.params = {
            'requestTimestamp': request_time,
            'merOrderId': mer_order_id, # 订单号必须以6712开头
            'mid': self.merchant_number, # 商户号
            'tid': self.terminal_id, # 终端号
            'instMid': 'MINIDEFAULT', # 机构商户号
            'orderDesc': '测试',
            'originalAmount': transaction.bill_fee, # 时来的bill_fee.单位分
            'totalAmount': transaction.paid_fee, # 时来paid_fee.单位分
            'divisionFlag': True, # 是否分账
            'platformAmount': self.round_up(transaction.paid_fee * 0.002),
            'subOrders': [
                {
                    'mid': merchant.wechat_mch_id, # 子商户号
                    'totalAmount': self.round_up(transaction.paid_fee * 0.998)
                }
            ],
            'notifyUrl': f'{domain}/payment/payment_notification/{transaction_id}',
            'returnUrl': f'{domain}/home', # 网页跳转地址
            'showUrl': f'{domain}/home', # 订单展示地址
            'subOpenId': user.wechat_profile.openid,
            'tradeType': 'MINI'
        }
        token = self.__get_token()
        if token:
            token = token.get('accessToken')
        headers = {'Authorization': token}
        try:
            ret = http.post(url, json=self.params, headers=headers)
            print(ret.text)
        except Exception as ex:
            logger.exception(ex)
        return {}

    def __get_token(self):
        signature = self.__sign()
        json_data = {
            'appId': self.appid,
            'appKey': self.secret_key,
            'nonce': signature.nonce,
            'signature': signature.signature,
            'timestamp': signature.timestamp
        }
        url = self.token_url
        try:
            ret = http.post(url, json=json_data)
            json_data = ret.json()
            return json_data
        except Exception as ex:
            logger.exception(ex)
        return None

    def __sign(self):
        Signature = namedtuple('Signature', ['nonce', 'signature', 'timestamp'])
        timestamp = maya.now().datetime(to_timezone='Asia/Shanghai').strftime('%Y%m%d%H%M%S')
        nonce = ''.join(str(random.choice(range(10))) for _ in range(18))
        message = '{}{}{}{}'.format(self.appid, timestamp, nonce, self.secret_key)
        message = bytes(message, encoding='utf-8')
        sha1 = hashlib.sha1()
        sha1.update(message)
        digest = sha1.digest()
        string_buffer = []
        for d in digest:
            l = int(d) & 255
            string_buffer.append('{:0x}'.format(l).zfill(2))
        signature = ''.join(string_buffer)
        return Signature(nonce, signature, timestamp)

    def __join_params(self):
        keys = self.params.keys()
        sorted_keys = sorted(keys, key=lambda x: x)
        keys = []
        for key in sorted_keys:
            value = self.params.get(key)
            if value == '':
                continue
            if isinstance(value, str):
                keys.append('{}={}'.format(key, value))
            elif isinstance(value, dict):
                keys.append('{}={}'.format(key, str(value)))
            elif isinstance(value, list):
                keys.append('{}={}'.format(key, str(value)))
        s = '&'.join(keys)
        return s

    def round_up(self, value):
        """ 四舍五入
        """
        decimal = value - int(value)
        if decimal < 0.5:
            return int(value)
        else:
            return math.ceil(value)


if __name__ == '__main__':
    TaotaoguManager().test()

# -*- coding: utf-8 -*-

""" 时来收银机使用饭票二维码买单
"""

import logging
import requests

from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

logger = logging.getLogger(__name__)


class FanpiaoPosOrderManager(FanpiaoManager):

    def __init__(self, user_id=None, pay_method=None, transaction_id=None, merchant_id=None,
                 fanpiao_category_id=None, return_url=None, fanpiao_qrcode_id=None):
        self.return_url = return_url
        self.qrcode_id = fanpiao_qrcode_id
        if fanpiao_category_id is not None:
            self.fanpiao_category = FanpiaoDataAccessHelper().get_fanpiao_category_by_id(fanpiao_category_id)
        if transaction_id is not None:
            self.transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
            user_id = self.transaction.payer_id
            pay_method = self.transaction.pay_method

        if merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

        self.pay_method = pay_method

    def notification(self):
        super(FanpiaoPosOrderManager, self).notification()
        fanpiao_manager = FanpiaoManager()
        fanpiao_pay_manager = FanpiaoPayManager(merchant=self.merchant)
        qrcode_obj = fanpiao_manager.get_user_fanpiao_qrcode(qrcode_id=self.qrcode_id)
        transaction = fanpiao_pay_manager.pos_fanpiao_prepay(qrcode_obj)
        if qrcode_obj.callback_url:
            requests.post(qrcode_obj)
        qrcode_obj.paid_fee = transaction.paid_fee
        qrcode_obj.is_paid = "1"
        fanpiao_manager.update_fanpiao_scan_qrcode_obj(qrcode_obj)

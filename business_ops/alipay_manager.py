# -*- coding: utf-8 -*-

import binascii
import base64
import collections
import logging
import json
from base64 import encodebytes
from urllib.parse import urlencode
from urllib.parse import quote_plus

from Cryptodome.Hash import SHA256 as CryptodomeSHA256
from Cryptodome.PublicKey import RSA as CryptodomeRSA
from Cryptodome.Signature import PKCS1_v1_5 as CryptodomePKCS1_v1_5
from Crypto.Cipher import AES
import maya
import requests

from business_ops import constants
from business_ops.transaction_manager import TransactionManager
from business_ops.pay_manager import PayManager
from common.utils import date_utils
from common.utils import id_manager
from dao.user_da_helper import <PERSON>r<PERSON>ata<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from proto import user_pb2 as user_pb
from proto.finance import wallet_pb2 as wallet_pb
from service import base_responses
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class AliPayManager(PayManager):
    def __init__(self, platform=None):
        self.appid = constants.ALIPAY_APPID
        if platform == "MINIPROGRAM":
            self.appid = constants.ALIPAY_MINIPROGRAM_APPID
        self.alipay_shilai_private_key_string = open(constants.ALIPAY_SHILAI_PRIVATE_KEY_FILE, 'r').read()
        self.alipay_public_key_string = open(constants.ALIPAY_PUBLIC_KEY_FILE, 'r').read()
        self.miniprogram_aes_secret_key = constants.ALIPAY_MINIPROGRAM_AES_SECRET_KEY

    def aes_decrypt_content(self, encrypted_content, charset="utf8"):
        encrypted_content = base64.b64decode(encrypted_content)
        iv = '\0' * AES.block_size
        key = self.miniprogram_aes_secret_key
        try:
            cryptor = AES.new(key, AES.MODE_CBC, iv)
        except:
            cryptor = AES.new(key.encode('utf8'), AES.MODE_CBC, iv.encode('utf8'))
        unpad = lambda s: s[:-ord(s[len(s) - 1:])]
        content = unpad(cryptor.decrypt(encrypted_content))
        return content

    def rsa_sign(self, plaintext):
        """RSA 数字签名"""
        signer = CryptodomePKCS1_v1_5.new(CryptodomeRSA.importKey(self.alipay_shilai_private_key_string))
        hash_value = CryptodomeSHA256.new(plaintext)
        signature = signer.sign(hash_value)
        sign = encodebytes(signature).decode("utf8").replace("\n", "")
        return sign

    def _verify(self, message, signature):
        key = self.alipay_public_key_string
        key = CryptodomeRSA.importKey(key)
        signer = CryptodomePKCS1_v1_5.new(key)
        digest = CryptodomeSHA256.new()
        digest.update(message.encode("utf8"))
        decodebytes = binascii.a2b_base64(signature.encode("utf8"))
        if signer.verify(digest, decodebytes):
            return True
        return False

    def rsa_verify(self, data, signature):
        if "sign_type" in data:
            data.pop("sign_type")
        unsigned_items = self._ordered_data(data)
        message = "&".join(u"{}={}".format(k, v) for k, v in unsigned_items)
        return self._verify(message, signature)

    def add_user(self, alipay_user):
        user = user_pb.User()
        user.id = id_manager.generate_user_id()
        now = int(maya.when('now', timezone=date_utils.TIMEZONE_SHANGHAI).epoch)
        user.joined_time = now
        alipay_profile = user_pb.AlipayUserProfile()
        alipay_profile.user_id = alipay_user.get('alipayUserId')
        if alipay_user.get('headimgurl'):
            alipay_profile.avatar = alipay_user.get('headimgurl')
        if alipay_user.get('province'):
            alipay_profile.province = alipay_user.get('province')
        if alipay_user.get('city'):
            alipay_profile.city = alipay_user.get('city')
        if alipay_user.get('nickname'):
            alipay_profile.nickname = alipay_user.get('nickname')
        alipay_profile.gender = alipay_user.get('gender', '男')
        user.alipay_profile.CopyFrom(alipay_profile)

        member_profile = user_pb.MemberProfile()
        if alipay_user.get('nickname'):
            member_profile.name = alipay_user.get('nickname')
        if alipay_user.get('sex'):
            member_profile.sex = alipay_user.get('sex')
        if alipay_user.get('headimgurl'):
            member_profile.head_image_url = alipay_user.get('headimgurl')
        if alipay_user.get('nickname'):
            member_profile.nickname = alipay_user.get('nickname')
        user.member_profile.CopyFrom(member_profile)
        UserDataAccessHelper().update_or_create_user(user)
        return user

    def __add_sign(self, params):
        params.pop('sign', None)
        keys = params.keys()
        sorted_keys = sorted(keys, key=lambda x: x)
        keys = []
        for key in sorted_keys:
            value = params.get(key)
            if value == '':
                continue
            if isinstance(value, str):
                keys.append('{}={}'.format(key, value))
            elif isinstance(value, dict):
                keys.append('{}={}'.format(key, str(value)))
        s = '&'.join(keys)
        sign = self.rsa_sign(s.encode(encoding='utf-8'))
        params['sign'] = sign

    def __build_params(self, params):
        tz = date_utils.TIMEZONE_SHANGHAI
        now = maya.when('now', timezone=tz).datetime(to_timezone=tz)
        now = now.strftime('%Y-%m-%d %H:%M:%S')
        common_params = {
            'app_id': self.appid,
            'charset': 'utf-8',
            'sign_type': 'RSA2',
            'timestamp': now,
            'version': '1.0'
        }
        params.update(common_params)
        logger.info('__build_params: {}'.format(params))

    def miniprogram_get_user_info(self, response):
        """ 前端获取用户私密信息之后,需要传到后端,后端通过AES解密出信息后保存到数据库,并返回到小程序
        """
        encrypt_content = response.get("response")
        signature = response.get("sign")
        decrypt_content = self.aes_decrypt_content(encrypt_content)
        decrypt_content = decrypt_content.decode("utf8")
        decrypt_content = json.loads(decrypt_content)
        if self.rsa_verify(response, signature):
            return decrypt_content
        logger.info("支付宝小程序获取用户手机信息失败: {}".format(decrypt_content))
        raise errors.Error(err=error_codes.ALIPAY_MINIPROGRAM_GET_USERINFO_ERROR)

    def get_user_info(self, auth_code, retry=3):
        if retry == 0:
            return None
        token = self.__get_alipay_access_token(auth_code)
        if not token:
            return self.get_user_info(auth_code, retry - 1)
        access_token = token.get('access_token')
        alipay_user_info = self.__get_alipay_user_info(access_token)
        if alipay_user_info is None and token.get("user_id"):
            alipay_user_info = {
                "alipayUserId": token.get("user_id")
            }
        return alipay_user_info

    def __get_alipay_user_info(self, access_token):
        ''' https://docs.open.alipay.com/api_2/alipay.user.info.share
        '''
        params = {
            'auth_token': access_token,
            'method': 'alipay.user.info.share'
        }
        self.__build_params(params)
        self.__add_sign(params)
        params = urlencode(params)
        url = '{}?{}'.format(constants.ALIPAY_GATEWAY, params)
        try:
            ret = requests.get(url)
            ret = ret.json()
            ret = ret.get('alipay_user_info_share_response')
            if ret.get("code") == "40006":
                # ISV权限不足
                return None
            user = {
                'alipayUserId': ret.get('user_id'),
                'headimgurl': ret.get('avatar'),
                'province': ret.get('province'),
                'city': ret.get('city'),
                'nickname': ret.get('nick_name'),
                'sex': '男' if ret.get('gender') == 'm' else '女',
                'gender': ret.get('gender')
            }
            return user
        except Exception:
            return None

    def __get_alipay_access_token(self, auth_code):
        ''' https://docs.open.alipay.com/api_9/alipay.system.oauth.token
        '''
        params = {
            'method': 'alipay.system.oauth.token',
            'grant_type': 'authorization_code',
            'code': auth_code
        }
        self.__build_params(params)
        self.__add_sign(params)
        url = "{}?charset=utf-8".format(constants.ALIPAY_GATEWAY)
        try:
            ret = requests.post(url, data=params)
            logger.info('__get_alipay_access_token: {}'.format(ret.text))
            ret = ret.json()
            return ret.get('alipay_system_oauth_token_response')
        except Exception:
            return None

    def notification(self, data):
        # sign must be poped out
        signature = data.pop("sign")
        verify = self.rsa_verify(data, signature)
        Callback = collections.namedtuple('Callback', ['resp_xml', 'success', 'trade_no', 'transaction_id'])
        # trade_status == 'TRADE_CLOSED'也会回调到这里.因为在其它地方已经处理了,所以这里就不再做处理了
        if verify and data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            return Callback('success', True, data['trade_no'], data['out_trade_no'])
        return Callback('fail', False, '', '')

    def refund(self, transaction):
        RefundCallback = collections.namedtuple("RefundCallback", ["flag", "refund_transaction_id"])
        refund_transaction_id = id_manager.generate_common_id()
        pay_transaction = self.get_pay_transaction(transaction)
        fee = transaction.paid_fee
        fee = "{:.2f}".format(fee / float(100))
        biz_content = {
            "out_trade_no": pay_transaction.id,
            "refund_amount": fee,
            "out_request_no": refund_transaction_id
        }
        params = {
            "method": "alipay.trade.refund",
            "biz_content": biz_content
        }
        self.__build_params(params)
        signed_string = self.__build_signed_string(params)
        try:
            url = constants.ALIPAY_GATEWAY + "?" + signed_string
            ret = requests.post(url, timeout=5)
            ret = ret.json()
            alipay_trade_refund_response = ret.get("alipay_trade_refund_response")
            code = alipay_trade_refund_response.get("code")
            msg = alipay_trade_refund_response.get("msg")
            msg = """
            支付宝退款接口返回:
            business_transaction_id: {},
            pay_transaction_id: {},
            退款金额: {},
            alipay_refund_return_code: {},
            alipay_refund_return_msg: {}
            """.format(transaction.id, pay_transaction.id, fee, code, msg)
            logger.info(msg)
            if alipay_trade_refund_response.get("code") == "10000":
                return RefundCallback(flag=True, refund_transaction_id=refund_transaction_id)
        except Exception as ex:
            logger.exception(ex)
            raise errors.AlipayPayError()
        return RefundCallback(flag=False)

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        '''退款到支付宝'''
        refund_callback = self.refund(transaction)
        if not refund_callback.flag:
            logger.info('transactionId: {} 退款失败'.format(transaction.id))
            return
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.ALIPAY,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee,
                use_coupon_id=transaction.use_coupon_id,
                refunded_transaction_id=transaction.id,
                id=refund_callback.refund_transaction_id)
            return _transaction
        return None

    def fanpiao_refund(self, transaction, reason=None):
        '''退款到支付宝'''
        refund_callback = self.refund(transaction)
        if not refund_callback.flag:
            logger.info("transactionId: {} 退款失败".format(transaction.id))
        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_fanpiao_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.ALIPAY,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                id=refund_callback.refund_transaction_id)
            return _transaction
        return None

    def coupon_package_refund(self, transaction, reason=None):
        '''退款到支付宝'''
        refund_callback = self.refund(transaction)
        if not refund_callback.flag:
            logger.info("transactionId: {} 退款失败".format(transaction.id))
        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_coupon_package_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id, pay_method=wallet_pb.Transaction.ALIPAY,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee)
            return _transaction
        return None

    def prepay(self, transaction, **kargs):
        user_id = kargs.get("user_id")
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.UserNotFound()
        if not user.HasField("alipay_profile"):
            raise errors.NotAlipayUser()
        paid_fee = float('{:.2f}'.format(float(transaction.paid_fee) / 100))  # 单位元
        notify_url = self.generate_notify_url(transaction, payment_notification="alipay", **kargs)
        biz_content = {
            "out_trade_no": transaction.id,
            "total_amount": paid_fee,
            "subject": "扫码点餐买单",
            "buyer_id": str(user.alipay_profile.user_id)
        }
        params = {
            "method": "alipay.trade.create",
            "notify_url": notify_url,
            "biz_content": biz_content
        }
        self.__build_params(params)
        signed_string = self.__build_signed_string(params)
        try:
            url = constants.ALIPAY_GATEWAY + "?" + signed_string
            ret = requests.get(url)
            logger.info("alipay_trade_create return: {}".format(ret.text))
            ret = ret.json()
            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
            result["tradeNo"] = ret.get("alipay_trade_create_response").get("trade_no")
        except Exception as ex:
            logger.exception(ex)
            raise errors.AlipayPayError()
        return result

    def _ordered_data(self, data):
        for k, v in data.items():
            if isinstance(v, dict):
                # 将字典类型的数据dump出来
                data[k] = json.dumps(v, separators=(',', ':'))
        return sorted(data.items())

    def __build_signed_string(self, params):
        ordered_items = self._ordered_data(params)
        raw_string = "&".join("{}={}".format(k, v) for k, v in ordered_items)
        sign = self.rsa_sign(raw_string.encode("utf-8"))
        unquoted_items = ordered_items + [('sign', sign)]
        signed_string = "&".join("{}={}".format(k, quote_plus(v)) for k, v in unquoted_items)
        return signed_string

    def transfer(self, transaction, user):
        fee = "{:.2f}".format(transaction.paid_fee / float(100))
        biz_content = {
            "out_biz_no": id_manager.generate_common_id(),
            "trans_amount": fee,
            "product_code": "TRANS_ACCOUNT_NO_PWD",
            "payee_info": {
                "identity": user.alipay_profile.user_id,
                "identity_type": "ALIPAY_USER_ID"
            }
        }
        params = {
            "method": "alipay.fund.trans.uni.transfer",
            "biz_content": biz_content
        }
        self.__build_params(params)
        signed_string = self.__build_signed_string(params)
        try:
            url = constants.ALIPAY_GATEWAY + "?" + signed_string
            ret = requests.get(url)
            logger.info("alipay fund trans uni transfer: {}".format(ret.text))
            ret = ret.json()
            return True
        except Exception as ex:
            logger.exception(ex)
            raise errors.Error(err=error_codes.ALIPAY_TRANSFER_ERROR)
        return False

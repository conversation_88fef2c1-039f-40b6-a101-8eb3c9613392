# -*- coding: utf-8 -*-

"""
Filename: shilai_member_manager.py
Date: 2020-06-29 13:48:01
Title: 时来会员充值
"""

import logging
import time

import proto.finance.wallet_pb2 as wallet_pb
import proto.membership_pb2 as membership_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.transaction_manager import TransactionManager
from business_ops.business_manager import BusinessManager
from business_ops.payment_manager import PaymentManager
from business_ops.shilai_pos_member_manager import ShilaiPosMemberManager
from business_ops.shilai_member_card_pay_manager import ShilaiMemberCardPayManager
from common.utils import id_manager
from common.utils import date_utils
from dao.membership_da_helper import MembershipDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class ShilaiMemberManager(BusinessManager):
    def __init__(self, user_id=None, merchant_id=None, pay_method=None, transaction_id=None, *args, **kargs):
        self.paid_fee = kargs.get("paid_fee", 0)
        self.bill_fee = kargs.get("bill_fee", 0)

        super(ShilaiMemberManager, self).__init__(
            user_id=user_id, merchant_id=merchant_id, pay_method=pay_method, transaction_id=transaction_id
        )
        self.__init_recharge_category(kargs.get("recharge_category_id"))

    def __init_recharge_category(self, recharge_category_id):
        """初始化储值配置
        1. 时来pos商家的储值配置从pos机端获取
        2. 其它商家储值列表从数据库获取
        """
        membership_da = MembershipDataAccessHelper()
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            manager = ShilaiPosMemberManager(merchant=self.merchant)
            self.recharge_category = manager.get_recharge_config(recharge_category_id)
        else:
            self.recharge_category = membership_da.get_member_card_recharge_config(
                merchant_id=self.merchant.id, recharge_category_id=recharge_category_id
            )

    def prepay(self):
        if not self.recharge_category:
            raise errors.RechargeCategoryNotExists()
        if self.recharge_category.amount != self.bill_fee:
            raise errors.RechargeCategoryAmountNotMatch()
        if self.recharge_category.sell_price != self.paid_fee:
            raise errors.RechargeCategorySellPriceNotMatch()
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI and (
            not self.user or not self.user.member_profile.mobile_phone
        ):
            raise errors.Error(50014, "手机号未授权，请先授权手机号")

        self.transaction = TransactionManager().get_shilai_member_card_recharge_transaction(
            self.user_id, self.merchant_id, self.pay_method, self.bill_fee, self.paid_fee
        )

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            result = PaymentManager(self.pay_method, merchant=self.merchant).prepay(transaction=self.transaction)
            logger.info("时来会员充值 prepay success: {}".format(result))
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") == error_codes.SUCCESS:
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)

        result['transactionId'] = self.transaction.id
        return result

    def notification(self):
        payment_manager = PaymentManager(self.transaction.pay_method, merchant=self.merchant)
        if not self.transaction.ledgered:
            payment_manager.recharge_launch_ledger(self.transaction)
        payment_manager.update_member_card_recharge_merchant_transfer_fee(self.transaction)

        if not self.increase_member_card_balance_to_pos():
            self.increase_member_card_balance()

        self.transaction.paid_time = int(time.time())
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        TransactionManager().update_transaction(self.transaction)

    def increase_member_card_balance_to_pos(self):
        if not self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return False
        shilai_pos_member_manager = ShilaiPosMemberManager(merchant=self.merchant, store=self.store)
        ret = shilai_pos_member_manager.increase_member_card_balance_by_balance(
            balance=self.transaction.bill_fee, phone=self.user.member_profile.mobile_phone, user_id=self.user.id
        )
        return ret.flag

    def increase_member_card_balance(self):
        balance = self.transaction.bill_fee
        manager = ShilaiMemberCardPayManager(merchant=self.merchant)
        member_card = manager.get_member_card_with_balance(user=self.user)
        if not member_card:
            member_card = self.create_member_card()
        if member_card.status == membership_pb.MemberCard.INVALID:
            raise errors.MemberCardInvalid()
        member_card.balance += balance
        member_card.recharge_amount += balance
        membership_da = MembershipDataAccessHelper()
        membership_da.update_or_create_member_card(member_card)

    def create_member_card(self, user_id, merchant_id):
        member_card = membership_pb.MemberCard()
        member_card.id = id_manager.generate_common_id()
        member_card.user_id = user_id
        member_card.merchant_id = merchant_id
        member_card.issue_time = date_utils.timestamp_second()
        member_card.brand_id = self.merchant.brand_info.id

    def refund(self, transaction, merchant):
        membership_da = MembershipDataAccessHelper()
        member_card = self.get_member_card(merchant=merchant, user_id=transaction.payer_id)
        if not member_card:
            raise errors.Error(err=error_codes.MEMBER_CARD_NOT_EXISTS)
        if member_card.balance < transaction.bill_fee:
            raise errors.MemberCardNotEnoughBalance()
        member_card.balance -= transaction.bill_fee
        member_card.recharge_amount -= transaction.bill_fee
        membership_da.update_or_create_member_card(member_card)

    def get_member_card(self, user=None, merchant=None, user_id=None, merchant_id=None, brand_id=None):
        manager = ShilaiMemberCardPayManager(merchant=merchant, merchant_id=merchant_id)
        member_card = manager.get_member_card_with_balance(user=user, user_id=user_id)
        return member_card

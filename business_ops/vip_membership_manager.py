# -*- coding: utf-8 -*-


"""时享会员."""

import logging
import time
from datetime import datetime

import proto.finance.wallet_pb2 as wallet_pb
from business_ops.business_manager import BusinessManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.wallet_manager import WalletManager
from business_ops.message_center.message_manager import MessageManager
from common.utils import date_utils
from dao.wallet_da_helper import WalletDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class VipMembershipManager(BusinessManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._config_da = ConfigDataAccessHelper()
        self.platform_global_config = self._config_da.get_platform_global_config()
        self._vip_membership_config = None
        self.merchant = MerchantDataAccessHelper().get_merchant("a5f60f71e44a49e9a711e768c76198eb")
        self.bill_fee = kargs.get("bill_fee")
        self.paid_fee = kargs.get("paid_fee")
        self.pay_method = kargs.get("pay_method")
        self.recharge_config_id = kargs.get("recharge_config_id")
        self.wallet = WalletManager().get_or_create_user_wallet(self.user)

    def prepay(self):
        if self.transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE:
            gen = self._vip_membership_subscribe_prepay()
        elif self.transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE:
            gen = self._vip_membership_recharge_prepay()
        elif self.transaction_type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE:
            gen = self._vip_membership_subscribe_recharge_combine_prepay()
        next(gen)
        if self.transaction.pay_method not in [
                wallet_pb.Transaction.WECHAT_PAY,
                wallet_pb.Transaction.ALIPAY,
        ]:
            raise errors.ShowError("支付方式不支持")
        payment_manager = PaymentManager(
            pay_method=self.transaction.pay_method,
            merchant=self.merchant
        )
        result = payment_manager.prepay(transaction=self.transaction, merchant=self.merchant)
        result.update({'transactionId': self.transaction.id})
        next(gen)
        if self.transaction_type in [
                wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE,
                wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE
        ]:
            MessageManager().create_shixiang_member_opened_message(
                self.user, self.transaction)
        return result

    def _vip_membership_subscribe_prepay(self):
        """时享会员订阅"""
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_vip_membership_subscribe_transaction(
            payer_id=self.user.id,
            payee_id=self.merchant.id,
            bill_fee=self.platform_global_config.vip_membership_global_config.vip_subscribe_price,
            paid_fee=self.platform_global_config.vip_membership_global_config.vip_subscribe_price,
            pay_method=self.pay_method
        )
        yield
        transaction_manager.update_transaction(self.transaction)
        yield

    def _vip_membership_recharge_prepay(self):
        """时享会员储值"""
        recharge_config = self.__get_recharge_config()
        if recharge_config is None:
            raise errors.ShowError("参数错误")
        if self.wallet.vip_membership.subscribe_time == 0:
            raise errors.ShowError("请先开通时享会员")
        if recharge_config.total_value != self.bill_fee or \
           recharge_config.sell_price != self.paid_fee:
            raise errors.ShowError("金额错误")
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_vip_membership_recharge_transaction(
            payer_id=self.user.id,
            payee_id=self.merchant.id,
            bill_fee=self.bill_fee,
            paid_fee=self.paid_fee,
            pay_method=self.pay_method
        )
        yield
        transaction_manager.update_transaction(self.transaction)
        yield

    def _vip_membership_subscribe_recharge_combine_prepay(self):
        subscribe_price = self.platform_global_config.vip_membership_global_config.vip_subscribe_price
        recharge_config = self.__get_recharge_config()
        if recharge_config is None:
            raise errors.ShowError("参数错误")
        if recharge_config.total_value + subscribe_price != self.bill_fee or \
           recharge_config.sell_price + subscribe_price != self.paid_fee:
            raise errors.ShowError("金额错误")
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_vip_membership_subscribe_recharge_combine_transaction(
            payer_id=self.user.id,
            payee_id=self.merchant.id,
            bill_fee=self.bill_fee,
            paid_fee=self.paid_fee,
            pay_method=self.pay_method
        )
        yield
        transaction_manager.update_transaction(self.transaction)
        yield

    def __get_recharge_config(self):
        if self.recharge_config_id in ("", None):
            return None
        for config in self.platform_global_config.vip_membership_global_config.recharge_config_list:
            if config.id == self.recharge_config_id:
                return config
        return None

    def notification(self):
        if self.transaction.state == wallet_pb.Transaction.SUCCESS:
            return
        if self.transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE:
            self._vip_membership_subscribe_notification()
        elif self.transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE:
            self._vip_membership_recharge_notification(self.transaction)
        elif self.transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE:
            self._vip_membership_subscribe_recharge_combine_notification()
        wallet_da = WalletDataAccessHelper()
        wallet_da.add_or_update_wallet(self.wallet)
        transaction_manager = TransactionManager()
        transaction_manager.set_transaction_success(self.transaction)

    def subscribe(self):
        self._vip_membership_subscribe_notification()
        wallet_da = WalletDataAccessHelper()
        wallet_da.add_or_update_wallet(self.wallet)

    def _vip_membership_subscribe_notification(self):
        now = int(time.time())
        fmt = "%Y-%m-%d %H:%M:%S"
        valid_period = self.platform_global_config.vip_membership_global_config.valid_period
        if self.wallet.vip_membership.subscribe_time == 0:
            self.wallet.vip_membership.subscribe_time = now
            self.wallet.vip_membership.expire_at = now + valid_period
        else:
            if self.wallet.vip_membership.expire_at > now: # 旧的还未过期
                self.wallet.vip_membership.expire_at += valid_period
            else: # 旧的已过期
                self.wallet.vip_membership.expire_at = now + valid_period
        expire_at = self.wallet.vip_membership.expire_at
        logger.info(f"{self.user.id} 订阅时间 {datetime.fromtimestamp(now).strftime(fmt)} 过期时间: {datetime.fromtimestamp(expire_at).strftime(fmt)}")

    def _vip_membership_recharge_notification(self, transaction):
        self.wallet.balance += transaction.bill_fee
        logger.info("{self.user.id} {self.user.member_profile.nickname} 储值成功 {transaction.id} {transaction.bill_fee}")

    def _vip_membership_subscribe_recharge_combine_notification(self):
        """时享会员开通和充值合并支付."""
        transaction_manager = TransactionManager()
        subscribe_price = self.platform_global_config.vip_membership_global_config.vip_subscribe_price
        union_transaction = transaction_manager.create_union_transaction(self.transaction)
        subscribe_transaction = transaction_manager.create_vip_membership_subscribe_transaction(
            payer_id=self.user.id,
            payee_id=self.merchant.id,
            bill_fee=subscribe_price,
            paid_fee=subscribe_price,
            pay_method=self.transaction.pay_method
        )
        recharge_transaction = transaction_manager.create_vip_membership_recharge_transaction(
            payer_id=self.user.id,
            payee_id=self.merchant.id,
            bill_fee=self.transaction.bill_fee - subscribe_price,
            paid_fee=self.transaction.paid_fee - subscribe_price,
            pay_method=self.transaction.pay_method
        )
        self._vip_membership_subscribe_notification()
        self._vip_membership_recharge_notification(recharge_transaction)
        transaction_manager.update_union_transaction(union_transaction)
        transaction_manager.set_transaction_success(subscribe_transaction)
        transaction_manager.set_transaction_success(recharge_transaction)

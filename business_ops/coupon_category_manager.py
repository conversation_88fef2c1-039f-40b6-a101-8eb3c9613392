# 商户优惠券类型的管理

import proto.strategy_pb2 as strategy_pb
from business_ops import coupon_category_helper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from service import errors
from proto import coupon_category_pb2 as coupon_category_pb

def create(merchant, coupon_category_spec, store_id=None):
    """ 创建优惠券类型

    Args:
        merchant: (Merchant) 商户信息
        coupon_category_spec: (CouponCategorySpec) 优惠券的设定
        store_id: (string) 适用的门店ID
    """
    if not merchant:
        raise errors.MerchantNotFoundError()

    coupon_category = None
    if coupon_category_spec.HasField("cash_coupon_spec"):
        cash_coupon_category_spec = coupon_category_spec.cash_coupon_spec
        coupon_category = coupon_category_helper.create_cash_coupon_category(
            merchant=merchant, cash_coupon_spec=cash_coupon_category_spec)
    if coupon_category_spec.HasField("coupon_package_spec"):
        cash_coupon_category_spec = coupon_category_spec.cash_coupon_spec
        coupon_category = coupon_category_helper.create_cash_coupon_category(
            merchant=merchant, cash_coupon_spec=cash_coupon_category_spec)
        coupon_category.issue_scene = coupon_category_pb.CouponCategory.COUPON_PACKAGE
        coupon_category.cash_coupon_spec.reduce_cost = coupon_category_spec.coupon_package_spec.reduce_cost
        coupon_category.cash_coupon_spec.least_cost = coupon_category_spec.coupon_package_spec.least_cost
        coupon_category.cash_coupon_spec.base_info.title = "满{}减{}元特惠券包".format(
            int(coupon_category_spec.coupon_package_spec.least_cost / 100),
            int(coupon_category_spec.coupon_package_spec.reduce_cost / 100))

        coupon_category.cash_coupon_spec.base_info.date_info.type = coupon_category_spec.coupon_package_spec.coupon_category_spec.date_info.type
        coupon_category.cash_coupon_spec.base_info.date_info.fixed_begin_term = 0
        coupon_category.cash_coupon_spec.base_info.date_info.fixed_term = coupon_category_spec.coupon_package_spec.coupon_category_spec.date_info.fixed_term

    if not coupon_category:
        raise errors.CouponCategoryNotSupportError()

    if store_id:
        coupon_category.store_id_list.append(store_id)

    CouponCategoryDataAccessHelper().add_coupon_category(coupon_category)
    return coupon_category


def create_coupon_category_by_verification_code_strategy(merchant, verification_code_strategy):
    coupon_category_spec = strategy_pb.CouponCategorySpec()
    verification_code_coupon_spec = coupon_category_spec.verification_code_coupon_spec
    coupon_category = coupon_category_helper.create_verification_code_coupon_category(
        merchant=merchant, verification_code_coupon_spec=verification_code_coupon_spec)
    coupon_category.issue_scene = coupon_category_pb.CouponCategory.VERIFICATION_CODE
    coupon_category.verification_code_coupon_spec.reduce_cost = verification_code_strategy.least_cost
    coupon_category.verification_code_coupon_spec.least_cost = verification_code_strategy.reduce_cost
    coupon_category.verification_code_coupon_spec.base_info.title = "满{}减{}元核销券".format(
        int(verification_code_strategy.least_cost / 100), int(verification_code_strategy.reduce_cost / 100))
    coupon_category.verification_code_coupon_spec.verification_code_strategy_id = verification_code_strategy.id
    coupon_category.verification_code_coupon_spec.base_info.date_info.type = "DATE_TYPE_PERMANENT"

    CouponCategoryDataAccessHelper().add_coupon_category(coupon_category)
    return coupon_category


def create_coupon_category_by_dish_verification_code_category(merchant, strategy):
    coupon_category_spec = strategy_pb.CouponCategorySpec()
    dish_coupon_spec = coupon_category_spec.dish_verification_code_coupon_spec
    coupon_category = coupon_category_helper.create_dish_coupon_category(merchant, dish_coupon_spec)
    coupon_category.issue_scene = coupon_category_pb.CouponCategory.DISH_VERIFICATION_CODE
    coupon_category.dish_verification_code_coupon_spec.base_info.title = "菜品核销券"
    coupon_category.dish_verification_code_coupon_spec.strategy_id = strategy.id
    coupon_category.dish_verification_code_coupon_spec.base_info.date_info.CopyFrom(strategy.date_info)
    CouponCategoryDataAccessHelper().add_coupon_category(coupon_category)
    return coupon_category


def create_coupon_category_by_brand_dish_verification_code_category(strategy):
    coupon_category_spec = strategy_pb.CouponCategorySpec()
    dish_coupon_spec = coupon_category_spec.dish_verification_code_coupon_spec
    coupon_category = coupon_category_helper.create_brand_dish_coupon_category(dish_coupon_spec, strategy.brand_id)
    coupon_category.issue_scene = coupon_category_pb.CouponCategory.BRAND_DISH_VERIFICATION_CODE
    coupon_category.brand_dish_verification_code_coupon_spec.base_info.title = "菜品核销券"
    coupon_category.brand_dish_verification_code_coupon_spec.strategy_id = strategy.id
    coupon_category.brand_dish_verification_code_coupon_spec.base_info.date_info.CopyFrom(strategy.date_info)
    CouponCategoryDataAccessHelper().add_coupon_category(coupon_category)
    return coupon_category

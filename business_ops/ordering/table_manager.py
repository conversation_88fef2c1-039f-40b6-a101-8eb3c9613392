# -*- coding: utf-8 -*-

import time
import inspect
import itertools

import proto.page.table_pb2 as table_pb
import proto.page.table_area_pb2 as page_table_area_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.page.pos_tables_pb2 as pos_tables_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.base_manager import BaseManager
from business_ops.ordering.keruyun_table_manager import KeruyunTableManager
from business_ops.ordering.shilai_table_manager import ShilaiTableManager
from business_ops.ordering.hualala_table_manager import HualalaTableManager
from business_ops.ordering.none_exists_table_manager import NoneExistsTableManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.table_area_da_helper import TableAreaDaHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from service import errors
from service import error_codes


class TableManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(<PERSON><PERSON>ana<PERSON>, self).__init__(*args, **kargs)
        self.init_table_manager(*args, **kargs)
        self.tables = []

    def init_table_manager(self, *args, **kargs):
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            self.table_manager = ShilaiTableManager(table_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            self.table_manager = KeruyunTableManager(table_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            self.table_manager = ShilaiTableManager(table_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            self.table_manager = HualalaTableManager(table_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.NONE_EXISTS:
            self.table_manager = NoneExistsTableManager(table_manager=self)
        else:
            raise errors.Error(err=error_codes.POS_NOT_SUPPORTED)

    def check_table_status(self, table_id=None):
        if table_id == "":
            return True
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.table_manager, fname):
            return self.table_manager.check_table_status(table_id=table_id)

    def get_tables(self):
        table_map = {}
        order_da = OrderingServiceDataAccessHelper()
        tables = order_da.get_tables(merchant_id=self.merchant.id)
        for table in tables:
            table_vo = table_pb.Table()
            table_vo.id = table.id
            table_vo.name = table.name
            if table.table_area.area_name in table_map:
                table_map[table.table_area.area_name].append(table_vo)
            else:
                table_map[table.table_area.area_name] = [table_vo]

        table_list = table_pb.TableList()
        table_list.merchant_id = self.merchant.id
        for group_name in table_map.keys():
            tables = table_map[group_name]
            tables = sorted(tables, key=lambda x: x.name)
            table_group = table_list.table_groups.add()
            table_group.name = group_name
            for table_vo in tables:
                table_group.tables.add().CopyFrom(table_vo)
        return table_list

    def get_ordering_tables(self):
        if hasattr(self.table_manager, "get_tables"):
            tables = self.table_manager.get_tables()
            return tables
        return None

    def sync_tables(self):
        if not hasattr(self.table_manager, "sync_tables"):
            return
        ordering_da = OrderingServiceDataAccessHelper()
        shilai_tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        shilai_tables = {table.name: table for table in shilai_tables}
        tables = self.table_manager.sync_tables()
        table_names = []
        for table in tables:
            name = table.get("name")
            shilai_table = shilai_tables.get(name)
            if not shilai_table:
                shilai_table = dish_pb.Table()
                shilai_table.id = table.get("id")
            # if shilai_table.status == -1:
            #     continue
            shilai_table.name = table.get("name")
            shilai_table.can_booking = table.get("can_booking", True)
            shilai_table.ordering_service_table_id = table.get("ordering_service_table_id")
            shilai_table.table_person_count = int(table.get("table_person_count", 1))
            shilai_table.table_area.area_code = table.get("area_code", "")
            shilai_table.table_area.area_name = table.get("area_name", "")
            shilai_table.table_area.area_id = str(table.get("area_id", ""))
            shilai_table.sort = int(table.get("sort", 1))
            shilai_table.merchant_id = self.merchant.id
            self.set_table_meal_type(shilai_table)
            table_names.append(name)
            ordering_da.add_or_update_table(shilai_table)
        table_names = ",".join(table_names)
        return table_names

    def batch_set_table(self, type):
        ordering_da = OrderingServiceDataAccessHelper()
        tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        for table in tables:
            if type == dish_pb.DishOrder.MealType.Name(dish_pb.DishOrder.SELF_PICK_UP):
                table.meal_type = dish_pb.DishOrder.SELF_PICK_UP
            else:
                self.set_table_meal_type(table)
            ordering_da.add_or_update_table(table)

    def set_table_meal_type(self, table):
        name = table.name
        if '外带' in name:
            table.is_take_away = True
            table.meal_type = dish_pb.DishOrder.TAKE_AWAY
        elif '自提' in name or '预点餐' in name:
            table.meal_type = dish_pb.DishOrder.SELF_PICK_UP
        elif '外卖' in name:
            table.meal_type = dish_pb.DishOrder.TAKE_OUT
        elif "支付" in name or "付款" in name:
            table.only_for_pay = True
        elif "选择桌台支付" == name:
            table.is_select_table_pay = True
        else:
            table.meal_type = dish_pb.DishOrder.EAT_IN

    def get_shilai_tables(self):
        ordering_da = OrderingServiceDataAccessHelper()
        tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        shilai_tables = {table.ordering_service_table_id: table for table in tables}
        keruyun_tables = self.table_manager.get_tables()
        table_list_vo = pos_tables_pb.PosTableList()
        tables_by_area = itertools.groupby(keruyun_tables, key=lambda table: table.get("tableArea", {}).get("areaName", "桌台"))

        for area_tables in tables_by_area:
            area_name, tables = area_tables
            area = table_list_vo.table_area_list.add()
            area.name = area_name
            for table in tables:
                shilai_table = shilai_tables.get(str(table.get("tableID")))
                if not shilai_table:
                    continue
                if shilai_table.only_for_pay:
                    continue
                if shilai_table.is_select_table_pay:
                    continue
                table_status = table.get("tableStatus")
                if table_status not in [
                    pos_tables_pb.PosTableList.Table.FREE,
                    pos_tables_pb.PosTableList.Table.DINING,
                    pos_tables_pb.PosTableList.Table.WAITING_FOR_CLEAN,
                    pos_tables_pb.PosTableList.Table.LOCKED,
                ]:
                    continue
                table_vo = area.table_list.add()
                table_vo.id = shilai_table.id
                table_vo.name = shilai_table.name
                table_vo.merchant_id = self.merchant.id
                table_vo.status = table.get("tableStatus")
                table_vo.sort = table.get("sort")
            area.table_list.sort(key=lambda x: x.sort)
        table_list_vo.merchant_id = self.merchant.id
        table_list_vo.logo_url = self.merchant.basic_info.logo_url
        table_list_vo.merchant_name = self.merchant.basic_info.display_name
        return table_list_vo

    def clear_table(self, table_id=None, keruyun_order_id=None, order=None):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.table_manager, fname):
            return self.table_manager.clear_table(table_id=table_id, keruyun_order_id=keruyun_order_id, order=order)

    def try_clear_table(self, table_id=None, keruyun_order_id=None, order=None):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.table_manager, fname):
            return self.table_manager.try_clear_table(order=order)

    def create_table(self, name, ordering_service_table_id, sort=None):
        if sort is None:
            sort = 0
        table_obj = dish_pb.Table()
        table_obj.id = id_manager.generate_common_id()
        table_obj.ordering_service_table_id = ordering_service_table_id
        table_obj.name = name
        table_obj.merchant_id = self.merchant.id
        table_obj.sort = sort
        if '外带' in name:
            table_obj.is_take_away = True
            table_obj.meal_type = dish_pb.DishOrder.TAKE_AWAY
        elif '自提' in name or '预点餐' in name:
            table_obj.meal_type = dish_pb.DishOrder.SELF_PICK_UP
        elif '外卖' in name:
            table_obj.meal_type = dish_pb.DishOrder.TAKE_OUT
        elif "支付" in name or "付款" in name:
            table_obj.only_for_pay = True
        else:
            table_obj.meal_type = dish_pb.DishOrder.EAT_IN
        OrderingServiceDataAccessHelper().add_or_update_table(table_obj)
        return table_obj

    def create_tables_without_pos(self, names):
        if names is None:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        names = names.split(",")
        if len(names) == 0:
            return
        shilai_tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        shilai_tables = {table.name: table for table in shilai_tables}
        sort = 0
        for name in names:
            sort += 1
            db_table = shilai_tables.get(name)
            if db_table:
                self.tables.append(db_table)
                db_table.status = 0
                db_table.sort = sort
                ordering_da.add_or_update_table(db_table)
                continue
            table = self.create_table(name, id_manager.generate_common_id(), sort=sort)
            self.tables.append(table)
        for _, shilai_table in shilai_tables.items():
            if shilai_table.name not in names:
                shilai_table.status = -1
                ordering_da.add_or_update_table(shilai_table)

    def async_tables(self, **kargs):
        ordering_da = OrderingServiceDataAccessHelper()

        shilai_tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        shilai_tables = {table.name: table for table in shilai_tables}
        tables = self.get_ordering_tables()
        if not tables:
            return
        for table in tables:
            name = table.get("tableName")
            db_table = shilai_tables.get(name)
            if not db_table:
                table = self.create_table(name, str(table.get("tableID")))
                self.tables.append(table)
                continue
            else:
                db_table_id = db_table.ordering_service_table_id
                keruyun_table_id = table.get("tableID")
                if db_table_id != keruyun_table_id:
                    db_table.ordering_service_table_id = str(keruyun_table_id)
                    ordering_da.add_or_update_table(db_table)
                self.tables.append(db_table)

    def get_table_by_id(self, table_id=None, ordering_service_table_id=None):
        table_da = OrderingServiceDataAccessHelper()
        table = table_da.get_table(
            merchant_id=self.merchant.id, id=table_id, ordering_service_table_id=ordering_service_table_id
        )
        return table

    def get_table_by_name(self, name):
        table_da = OrderingServiceDataAccessHelper()
        table = table_da.get_table(merchant_id=self.merchant.id, name=name)
        return table

    def create_table_area(self, area_name):
        area = dish_pb.TableArea()
        area.merchant_id = self.merchant.id
        area.id = id_manager.generate_common_id()
        area.area_name = area_name
        area.create_time = int(time.time())
        return area

    def list_tables(self):
        table_da = OrderingServiceDataAccessHelper()
        tables = table_da.get_tables(merchant_id=self.merchant.id)
        result = []
        for table in tables:
            vo = page_table_area_pb.TableVO()
            vo.id = table.id
            vo.name = table.name
            result.append(vo)
        return result

    def list_table_areas(self):
        tabl_area_da = TableAreaDaHelper()
        areas = tabl_area_da.list_table_areas(merchant_id=self.merchant.id, status=dish_pb.TableArea.ACTIVE)
        result = []
        for area in areas:
            area_vo = page_table_area_pb.TableAreaVO()
            area_vo.merchant_id = self.merchant.id
            area_vo.area_name = area.area_name
            area_vo.status = area.status
            area_vo.id = area.id
            self.__fill_tables(area, area_vo)
            self.__fill_printers(area, area_vo)
            result.append(area_vo)
        return result

    def __fill_tables(self, area, area_vo):
        table_da = OrderingServiceDataAccessHelper()
        for table_id in area.table_ids:
            table = table_da.get_table(id=table_id, merchant_id=self.merchant.id)
            if not table:
                continue
            table_vo = area_vo.tables.add()
            table_vo.id = table.id
            table_vo.name = table.name

    def __fill_printers(self, area, area_vo):
        printer_da = PrinterConfigDataAccessHelper()
        for printer_sn in area.printer_sns:
            printer = printer_da.get_printer_config(printer_sn, self.merchant.id)
            if not printer:
                continue
            printer_vo = area_vo.printers.add()
            printer_vo.sn = printer.printer_sn

    def update_table_area(self, area, **kargs):
        if not area:
            return
        if kargs.get("area_name"):
            area.area_name = kargs.get("area_name")
        if kargs.get("status"):
            status = dish_pb.TableArea.Status.Value(kargs.get("status"))
            area.status = status
        if kargs.get("table_ids") is not None:
            while area.table_ids:
                area.table_ids.pop()
            for table_id in kargs.get("table_ids"):
                area.table_ids.append(table_id)
        if kargs.get("printer_sns") is not None:
            while area.printer_sns:
                area.printer_sns.pop()
            for printer_sn in kargs.get("printer_sns"):
                area.printer_sns.append(printer_sn)

    def get_table_area(self, id):
        da = TableAreaDaHelper()
        table_area = da.get_table_area(id=id)
        return table_area

    def add_or_update_table_area(self, area):
        if not area:
            return
        da = TableAreaDaHelper()
        area.update_time = int(time.time())
        da.add_or_update_table_area(area)

# -*- coding: utf-8 -*-

""" 花啦啦正餐
"""

import logging
from datetime import datetime

import proto.ordering.registration_pb2 as registration_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.hualala_order_manager import HualalaOrderManager
from business_ops.ordering.constants import HualalaConstants
from business_ops.ordering.printer_manager import PrinterManager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class HualalaDinnerManager(HualalaOrderManager):
    def __init__(self, order_manager=None, *args, **kargs):
        self._manager = order_manager
        super(HualalaDinnerManager, self).__init__(order_manager=self._manager, *args, **kargs)
        self.printer_manager = PrinterManager(merchant=self._manager.merchant)

    def __add_dish_to_hualala(self, order, hualala_order):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id
        }
        route = HualalaConstants.ADD_DISH
        logger.info("加菜参数： {}".format(params))
        ret = self.try_post(route, params)
        if ret.flag:
            return True
        return False

    def __create_order(self, order, hualala_order):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id
        }
        self.__calculate_combo_meal_head_due_price(hualala_order)
        params.update(hualala_order)
        route = HualalaConstants.CREATE_ORDER
        ret = self.try_post(route, params)
        if ret.flag:
            ordering_da = OrderingServiceDataAccessHelper()
            third_party_order_id = ret.data.get("orderKey")
            if not third_party_order_id:
                raise errors.Error(err=error_codes.hualala_create_order_fail)
            order.ordering_service_order_id = third_party_order_id
            ordering_da.add_or_update_order(order)
        return False

    def __calculate_combo_meal_head_due_price(self, hualala_order):
        order_items = hualala_order.get("order").get("orderItem")
        combo_meal_header_price = {}
        for order_item in order_items:
            is_set_food = order_item.get("isSetFood")
            is_sf_detail = order_item.get("isSFDetail")
            sfm_unit_code = order_item.get("SFMUnitCode")
            due_price = order_item.get("duePrice")
            origin_price = float(order_item.get("originPrice"))
            if not sfm_unit_code:
                continue
            header_price = combo_meal_header_price.get(sfm_unit_code, {})
            header_price.update({
                "due_price": header_price.get("due_price", 0) + due_price,
                "origin_price": header_price.get("origin_price", 0) + origin_price
            })
            combo_meal_header_price.update({sfm_unit_code: header_price})
        for order_item in order_items:
            is_set_food = order_item.get("isSetFood")
            is_sf_detail = order_item.get("isSFDetail")
            sfm_unit_code = order_item.get("SFMUnitCode")
            due_price = float(order_item.get("duePrice"))
            origin_price = float(order_item.get("originPrice"))
            if not sfm_unit_code:
                continue
            header_price = combo_meal_header_price.get(sfm_unit_code)
            if is_set_food == 1 and is_sf_detail == 0:
                order_item.update({
                    "duePrice": round(header_price.get("due_price"), 2),
                    "originPrice": str(round(header_price.get("origin_price"), 2))
                })

    def __pay_order(self, order, hualala_order):
        total_fee = str(float(order.paid_in_fee) / 100)
        params = {
            "groupID": int(self.group_id),
            "shopID": int(self.shop_id),
            "orderKey": int(order.ordering_service_order_id),
            "payment": self.__get_pay_order_payment_info(),
            "orderSubType": 41,
            "isThirdPay": 2,
            "bankCode": "weChat",
            "checkoutType": 2,
            "isSentMsg": "1",
            "isPubPay": 0,
            "orderStatus": 20,
            "paidTotalAmount": total_fee,
            "channelKey": "399_weixin",
            "msgType": 240
        }
        route = HualalaConstants.PAY_ORDER
        ret = self.try_post(route, params)
        if ret.flag:
            return True
        return False

    def __get_pay_order_payment_info(self):
        paid_in_fee = self._manager.business_obj.order.paid_in_fee
        bill_fee = self._manager.business_obj.transaction.bill_fee
        # paid_fee = self._manager.business_obj.transaction.paid_fee
        discount_fee = bill_fee - paid_in_fee
        total_fee = str(float(paid_in_fee) / 100)
        discount_fee = str(float(discount_fee) / 100)
        payment_subject_id = self._manager.registration_info.hualala_pos_info.payment_subject_id
        payment_subject_name = self._manager.registration_info.hualala_pos_info.payment_subject_name
        d_payment_subject_id = self._manager.registration_info.hualala_pos_info.discount_payment_subject_id
        d_payment_subject_name = self._manager.registration_info.hualala_pos_info.discount_payment_subject_name
        payments = [
            {
                "paymentSubjectID": payment_subject_id,
                "paymentSubjectName": payment_subject_name,
                "dueAmount": total_fee,
                "paymentStatus": 20,
                "payWay": 70
            }, {
                "paymentSubjectID": d_payment_subject_id,
                "paymentSubjectName": d_payment_subject_name,
                "dueAmount": discount_fee,
                "paymentStatus": 20,
                "payWay": 70
            }
        ]
        return payments

    def pay_order(self, **kargs):
        order = self._manager.business_obj.order
        ordering_da = OrderingServiceDataAccessHelper()
        hualala_order = ordering_da.get_hualala_order(order)
        self._manager._cal_commission(order)
        self._manager._calculate_paid_in_fee(self._manager.business_obj)
        pay_type = self._manager.registration_info.pay_type
        if pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            self.__create_order(order, hualala_order)
            self.__pay_order(order, hualala_order)
        elif pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            self.__pay_order(order, hualala_order)

    def create_ordering_order(self, order):
        ordering_da = OrderingServiceDataAccessHelper()
        self._manager.dish_manager.get_discount_plan(order.user_id)
        hualala_order = {}
        self.__add_order_base_info(order, hualala_order)
        self.__add_products_info(order, hualala_order)
        pay_type = self._manager.registration_info.pay_type
        if pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            self.__create_order(order, hualala_order)
            ordering_da.add_or_update_hualala_order(order, hualala_order)
        elif pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            ordering_da.add_or_update_hualala_order(order, hualala_order)

    def __deal_with_combo_meal_product(self, hp, product, dish):
        if product.parent_uuid == "" and dish.type != dish_pb.Dish.COMBO_MEAL:
            return hp
        if dish.type == dish_pb.Dish.COMBO_MEAL:
            hp.update({
                "isSetFood": 1,
                "SFMUnitCode": product.uuid,
                "isSFDetail": 0,
                "isBatching": 0
            })
        else:
            if product.parent_uuid != "":
                hp.update({
                    "isSetFood": 1,
                    "SFMUnitCode": product.parent_uuid,
                    "isSFDetail": 1,
                    "isBatching": 0
                })
        return hp

    def __add_product_info(self, _order, product, dish):
        is_discount = 1
        if dish.no_discount:
            is_discount = 0
        order_item = _order.get("orderItem", [])
        hp = {
            "foodID": int(dish.third_party_dish_id),
            "foodName": dish.name,
            "foodUnit": dish.unit,
            "foodUnitID": dish.unit_key,
            "isSetFood": 0,
            "isBatching": 0,
            "foodCount": int(product.quantity),
            "originPrice": str(float(product.price) / 100),
            "takeoutPackagingFee": 0,
            "isDiscount": is_discount,
            "duePrice": str(float(product.price) / 100)
        }
        hp = self.__deal_with_combo_meal_product(hp, product, dish)
        self.__deal_with_attrs(hp, product, dish)
        self.__deal_with_supply_condiments(hp, product, dish)
        order_item.append(hp)
        _order.update({"orderItem": order_item})

    def __deal_with_attrs(self, hp, product, dish):
        attrs = product.attrs
        attr_reprice = 0
        due_price = hp.get("duePrice", 0)
        attr_names = []
        for attr in attrs:
            attr_reprice += float(attr.reprice) / 100
            attr_names.append(attr.name)
        due_price = str(float(due_price) + attr_reprice)
        remark = ",".join(attr_names)
        hp.update({
            "remark": remark,
            "duePrice": due_price
        })

    def __deal_with_supply_condiments(self, hp, product, dish):
        supply_condiments = product.supply_condiments
        s_names = []
        s_price = 0
        remark = hp.get("remark", "")
        for s in supply_condiments:
            s_names.append(s.name)
            s_price += s.market_price * s.quantity
        s_price = float(s_price) / 100
        due_price = float(hp.get("duePrice"))
        s_names = ",".join(s_names)
        if remark != "":
            remark = "{},{}".format(remark, s_names)
        hp.update({
            "duePrice": due_price + s_price,
            "remark": remark
        })

    def __add_products_info(self, order, hualala_order):
        ordering_da = OrderingServiceDataAccessHelper()
        products = order.products
        _order = hualala_order.get("order")
        for product in products:
            dish = ordering_da.get_dish(dish_id=product.id, merchant_id=self._manager.merchant.id)
            if not dish:
                continue
            self.__add_product_info(_order, product, dish)

    def __add_order_base_info(self, order, hualala_order):
        is_invoice = 0
        if order.is_invoice:
            is_invoice = 1
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(ordering_service_table_id=order.table_id)
        create_time = datetime.fromtimestamp(order.create_time).strftime("%Y%m%d%H%M")
        order_remark = self.printer_manager.build_order_remark(order, newline='\n')

        hualala_order.update({
            "groupID": int(self.group_id),
            "shopID": int(self.shop_id),
            "isCheackOut": is_invoice,
            "bankCode": "weChat",
            "isSentMsg": 1,
            "thirdOrderID": order.id,
            "msgType": 240,
            "order": {
                "orderSubType": 41,
                "orderStatus": 15,
                "discountTotalAmount": 0,
                "orderRemark": order_remark,
                "taxNo": "",
                "invoiceTitle": "",
                "tableName": table.name,
                "dinners": int(order.people_count),
                "orderTime": create_time,
                "deliveryAmount": '0',
                "channelKey": "399_weixin",
                "isAlreadyPaid": "0",
                "orderMode": 2
            }
        })

    def __add_product_info_add_dish(self, order, product, dish):
        is_discount = 1
        if dish.no_discount:
            is_discount = 0
        order_item = order.get("orderItem", [])
        hp  = {
            "foodID": int(dish.third_party_dish_id),
            "foodName": dish.name,
            "foodUnit": dish.unit,
            "foodUnitID": dish.unit_key,
            "isSetFood": 0,
            "isBatching": 0,
            "foodCount": int(product.quantity),
            "originPrice": str(float(product.price) / 100),
            "takeoutPackagingFee": '0',
            "isDiscount": is_discount,
            "duePrice": str(float(product.price) / 100),
            "batchNo": '1'
        }
        order_item.append(hp)
        order.update({"orderItem": order_item})

    def __add_products_info_add_dish(self, order, hualala_order):
        products = order.add_products[-1].products
        ordering_da = OrderingServiceDataAccessHelper()
        _order = hualala_order.get("order")
        for product in products:
            dish_id = product.id
            dish = ordering_da.get_dish(dish_id=dish_id, merchant_id=self._manager.merchant.id)
            if not dish:
                continue
            self.__add_product_info_add_dish(_order, product, dish)

    def __add_order_base_info_add_dish(self, order, hp):
        ordering_da = OrderingServiceDataAccessHelper()
        is_cheack_out = 0
        if order.is_invoice:
            is_cheack_out = 1
        table = ordering_da.get_table(ordering_service_table_id=order.table_id)
        create_time = datetime.fromtimestamp(order.create_time).strftime("%Y%m%d%H%M")
        hp.update({
            "isCheackOut": is_cheack_out,
            "isThirdPay": 2,
            "bankCode": "weChat",
            "isSentMsg": 1,
            # "thirdOrderID": order.id,
            "groupID": int(self.group_id),
            "shopID": int(self.shop_id),
            "msgType": 240,
            "order": {
                "orderSubType": 41,
                "orderStatus": 15,
                "orderKey": int(order.ordering_service_order_id),
                "discountTotalAmount": 0,
                "tableName": table.name,
                "dinners": order.people_count,
                "orderTime": create_time,
                "deliveryAmount": "0",
                "serviceAmount": "0",
                "channelKey": "399_weixin",
                "isAlreadyPaid": "0",
                "orderMode": 2
            }
        })

    def add_dish_ordering_order(self, dishes, order, user_id):
        ordering_da = OrderingServiceDataAccessHelper()
        hualala_order = ordering_da.get_hualala_order(order)
        self.__add_products_info(order, hualala_order)
        self.__add_dish_create_order(order)
        ordering_da.add_or_update_hualala_order(order, hualala_order)

    def __add_dish_create_order(self, order):
        self._manager.dish_manager.get_discount_plan(order.user_id)
        hp = {}
        self.__add_order_base_info_add_dish(order, hp)
        self.__add_products_info_add_dish(order, hp)
        self.__add_dish_to_hualala(order, hp)

    def initiate_ordering_refund(self, order, transaction_id=None, **kargs):
        if not order:
            raise errors.OrderNotFound()
        if order.status != dish_pb.DishOrder.PAID:
            raise errors.Error(err=error_codes.ORDER_NOT_PAID)
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id,
            "orderKey": order.ordering_service_order_id,
            "cancelOrderCause": "扫码点餐申请退款"
        }
        route = HualalaConstants.ORDER_REFUND
        ret = self.try_post(route, params)
        if ret.flag:
            return True
        raise errors.Error(err=error_codes.hualala_refund_order_fail)

    def order_status(self, **kargs):
        """
        15: 提交订单(未支付)
        20: 已付款
        40: 商家接单
        41: 商家已下单
        45: 制作完成,只自提
        46: 确认送出,只外卖
        50: 确认送达,只外卖
        60: 订单完成
        64: 退款中
        65: 退款完成
        2: 拒绝退款
        6: 同意部分退款
        5: 拒绝部分退款
        """
        body = kargs.get("body")
        order_state_code = body.get("orderStateCode")
        third_order_id = body.get("thirdOrderID")
        ordering_da = OrderingServiceDataAccessHelper()
        order = ordering_da.get_order(id=third_order_id)
        if not order:
            return
        redis_client = RedisClient().get_connection()
        if order_state_code == "65":
            key = CacheServerKeys.get_order_refund_redis_callback_key(order)
            if not redis_client.set(key, 1, ex=6000, nx=True):
                return
            logger.info("哗啦啦订单退款: {}".format(order.id))
            self._manager.pos_return_order(transaction_id=order.transaction_id)

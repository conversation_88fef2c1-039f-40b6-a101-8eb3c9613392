# -*- coding: utf-8 -*-

import logging

import proto.ordering.dish_pb2 as dish_pb
import proto.common.datetime_pb2 as datetime_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.base_dish_manager import BaseDishManager
from business_ops.ordering.shilai_ops_manager import <PERSON><PERSON><PERSON><PERSON>anager, <PERSON><PERSON>PosMerchantManager
from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.ordering.attr_manager import AttrManager
from business_ops.ordering.supply_condiment_manager import SupplyCondimentManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common.utils import id_manager
from common.aliyun_oss_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>elper
from service import errors

logger = logging.getLogger(__name__)


class ShilaiDishManager(BaseDishManager):
    # 增加菜品可售数量
    INCREASE_DISH_REMAIN_QUANTITY = 0x01
    # 减少菜品可售数量
    DECREASE_DISH_REMAIN_QUANTITY = 0x02
    # 检测菜品可售数量是否足够
    CHECK_DISH_REMAIN_QUANTITY = 0x03

    def __init__(self, dish_manager=None, *args, **kargs):
        self._manager = dish_manager
        super(ShilaiDishManager, self).__init__(*args, **kargs)
        self._ops = ShilaiOPSManager(merchant=self._manager.merchant, store=self._manager.store)

    def get_table_required_dishes(self, table):
        params = {"tableId": table.id}
        url = self._ops.generate_url(ShilaiPosConstants.GET_TABLE_REQUIRED_DISHES)
        ret = self._ops.do_post(url, params)
        return ret.get("data")

    def get_required_dishes(self, merchant_id, store_id):
        data = ShilaiPosMerchantManager(merchant_id).get_required_dishes(merchant_id, store_id)
        d_map = {"PER_PERSON": "EVERYONE", "PER_TABLE": "ONLY_ONE"}
        result = {}
        for d in data:
            if d['status'] != "ACTIVE":
                continue
            _type = d_map.get(d['type'])
            for dish_id in d.get("requiredDishIds", []):
                result[dish_id] = {"id": dish_id, "type": _type}
        return result

    def get_all_dish(self, **kargs):
        categories = self.__get_all_categories()
        dishes = self.__get_all_dishes()
        packaging_boxs = self.__get_packaging_boxs()
        dish_id_to_category_id = {}
        categories = self.generate_categories(categories, dish_id_to_category_id)
        packaging_boxs = self.generate_packaging_boxs(packaging_boxs)
        dishes = self.generate_dishes(dishes, dish_id_to_category_id, packaging_boxs)
        return {"dishes": dishes, "categories": categories, "packaging_boxs": packaging_boxs}

    def async_categories(self, names, dish_type_ids):
        __dish_categories = self.__get_all_categories()
        dish_categories = []
        for dish_category in __dish_categories:
            dish_categories.append(
                {
                    "id": dish_category.get("id"),
                    "third_party_category_id": dish_category.get("id"),
                    "name": dish_category.get("name"),
                    "sort": dish_category.get("rankInMerchant"),
                }
            )
        return dish_categories

    def generate_categories(self, categories, dish_id_to_category_id):
        ret = []
        ordering_da = OrderingServiceDataAccessHelper()
        for category in categories:
            _category = dish_pb.DishCategory()
            _category.id = category.get("id")
            db_category = ordering_da.get_category(id=_category.id, merchant_id=category.get("merchantId"))
            _category.name = category.get("name")
            _category.merchant_id = self._manager.merchant.id
            _category.sort = category.get("rankInMerchant", 0)
            for dish_id in category.get("dishIds"):
                c = dish_id_to_category_id.get(dish_id, set())
                c.add(_category.id)
                dish_id_to_category_id.update({dish_id: c})
            if db_category:
                _category.no_discount = db_category.no_discount
            ret.append(_category)
        return ret

    def generate_dishes(self, dishes, dish_id_to_category_id, packaging_boxs, db_dishes={}):
        ret = {}
        supply_condiments = self.__create_supply_condimetns(dishes)
        dish_id_child_dishes_map = {}
        attr_selection_dict = {}
        ordering_da = OrderingServiceDataAccessHelper()
        for dish in dishes:
            sale_channel = dish.get("saleChannel")
            status = dish.get("status")
            _dish = dish_pb.Dish()
            if status in ["INACTIVE", "DELETED"]:
                _dish.status = dish_pb.Dish.DISABLE
            elif status == "ON_SALE":
                _dish.status = dish_pb.Dish.NORMAL
            elif status == "SOLD_OUT":
                _dish.status = dish_pb.Dish.GUQING
            if "CODE" not in sale_channel:
                _dish.status = dish_pb.Dish.DISABLE
            _dish.id = dish.get("id")
            _dish.name = dish.get("name")
            _dish.price = dish.get("price")
            _dish.sort = dish.get("rankInCategory", 999)
            _dish.remain_quantity = dish.get("remainQuantity", 0)
            _dish.enable_quantity_setting = dish.get("enableQuantitySetting", False)
            _dish.min_order_num = dish.get("minOrderNum", 0)
            _dish.step_order_num = dish.get("stepOrderNum", 0)
            _dish.desc = dish.get('desc', '')
            _dish.supply_condiment_group_name = dish.get('supplyCondimentGroupName', '')
            # logger.info(f"同步收银机端菜品: {_dish.id} {_dish.name} {status}")
            while _dish.images:
                _dish.images.pop()
            for image in dish.get("imageUrls"):
                _dish.images.append(image)
                try:
                    self.set_dish_image(_dish, image)
                except:
                    pass
            _dish.merchant_id = self._manager.merchant.id
            category_ids = dish_id_to_category_id.get(_dish.id, set())
            for category_id in category_ids:
                _dish.categories.append(category_id)
            while _dish.supply_condiments:
                _dish.supply_condiments.pop()
            self.__set_dish_supply_condiments(_dish, dish, supply_condiments)
            self.__set_dish_sale_times(_dish, dish)
            while _dish.attrs:
                _dish.attrs.pop()
            if packaging_boxs and dish.get('packagingBoxDishId', ''):
                for pack_attr in packaging_boxs.attrs:
                    if dish.get('packagingBoxDishId', '') == pack_attr.id:
                        attr = _dish.attrs.add()
                        attr.id = pack_attr.id
                        attr.name = pack_attr.name
                        attr.reprice = pack_attr.reprice
                        attr.group_id = packaging_boxs.id
                        attr.group_name = packaging_boxs.name
                        attr.type = dish_pb.Attr.TAKE_AWAY
                        attr.sort = 0
            self.__set_dish_attrs(_dish, dish, attr_selection_dict)
            self.__set_dish_specs(_dish, dish)
            dish_groups = dish.get("combo", {}).get("dishGroups")
            if dish_groups:
                dish_id_child_dishes_map.update({_dish.id: dish_groups})
            ret.update({_dish.id: _dish})
            if _dish.id in db_dishes:
                db_dish = db_dishes.get(_dish.id)
            else:
                db_dish = ordering_da.get_dish(dish_id=_dish.id, merchant_id=self._manager.merchant.id)
            self.__set_db_dish(db_dish, _dish)
            if _dish.type == dish_pb.Dish.DishType.SINGLE:
                self.sync_combo_dish(_dish)
        self.__set_dish_child_dish_group(ret, dish_id_child_dishes_map)
        self.__process_dish_attrs(ret, attr_selection_dict)
        self.__process_dish_supply_condiments(ret)

        return ret

    def generate_packaging_boxs(self, packaging_boxs):
        if packaging_boxs:
            attr_manager = AttrManager(merchant=self._manager.merchant, store=self._manager.store)
            attrs = []
            for i, box in enumerate(packaging_boxs):
                attrs.append({'id': box.get('id'), 'name': box.get('name'), 'reprice': box.get('price'), 'sort': i})
            return attr_manager.add_or_update_attr_group(
                None, '打包盒', attrs, False, attr_group_type=dish_pb.Attr.AttrType.Name(dish_pb.Attr.AttrType.TAKE_AWAY)
            )

    def __set_db_dish(self, db_dish, _dish):
        if not db_dish:
            return
        _dish.sold_number = db_dish.sold_number

    def set_dish_image(self, dish_obj, image):
        aliyun_oss_helper = AliyunOSSHelper()
        image_obj = aliyun_oss_helper.convert_url_to_image_obj(image)
        if 'defaultDishImage' in image_obj.name:
            dish_obj.thumb_image = image
            return
        # 按比例把图片压缩到1M以内
        aliyun_oss_helper.try_to_resize(image_obj, model="m_lfit")
        target_name = "thumb-{}".format(image_obj.name)
        src_name = image_obj.name
        if "dishes" in image:
            src_name = f"dishes/{image_obj.name}"
        thumb_image_obj = aliyun_oss_helper.resize(src_name, target_name, model="m_lfit", w=480, h=480)
        dish_obj.thumb_image = thumb_image_obj.url

    def __set_dish_sale_times(self, dish, dish_dict):
        while dish.sale_times:
            dish.sale_times.pop()
        limited_sale_time = dish_dict.get("limitedSaleTime")
        if not limited_sale_time:
            return
        interval = limited_sale_time.get("interval")
        if not interval:
            return
        date_range_periods = interval.get("dateRangePeriods", [])
        if not date_range_periods:
            return

        for date_range_period in date_range_periods:
            day_periods = date_range_period.get("dayPeriods")
            date_range = date_range_period.get("dateRange")
            weekday_period = date_range_period.get("weekdayPeriod", {})
            monday_periods = weekday_period.get("mondayPeriods", {})
            tuesday_periods = weekday_period.get("tuesdayPeriods", {})
            wednesday_periods = weekday_period.get("wednesdayPeriods", {})
            thursday_periods = weekday_period.get("thursdayPeriods", {})
            friday_periods = weekday_period.get("fridayPeriods", {})
            saturday_periods = weekday_period.get("saturdayPeriods", {})
            sunday_periods = weekday_period.get("sundayPeriods", {})
            self.__set_dish_sale_time_day_period(
                dish.sale_times, monday_periods, datetime_pb.WEEKDAY_MONDAY, day_periods=day_periods, date_range=date_range
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times, tuesday_periods, datetime_pb.WEEKDAY_TUESDAY, day_periods=day_periods, date_range=date_range
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times,
                wednesday_periods,
                datetime_pb.WEEKDAY_WEDNESDAY,
                day_periods=day_periods,
                date_range=date_range,
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times, thursday_periods, datetime_pb.WEEKDAY_THURSDAY, day_periods=day_periods, date_range=date_range
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times, friday_periods, datetime_pb.WEEKDAY_FRIDAY, day_periods=day_periods, date_range=date_range
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times, saturday_periods, datetime_pb.WEEKDAY_SATURDAY, day_periods=day_periods, date_range=date_range
            )
            self.__set_dish_sale_time_day_period(
                dish.sale_times, sunday_periods, datetime_pb.WEEKDAY_SUNDAY, day_periods=day_periods, date_range=date_range
            )

    def __set_dish_sale_time_day_period(self, sale_times, weekday_periods, weekday, day_periods=None, date_range=None):
        if not day_periods and not weekday_periods:
            return
        if weekday_periods and not day_periods:
            day_periods = weekday_periods
        for day_period in day_periods:
            sale_time = sale_times.add()
            if date_range is not None:
                sale_time.start_date = date_range.get("startDate")
                sale_time.end_date = date_range.get("endDate")
            sale_time.start = day_period.get("startSecondOfDay")
            sale_time.end = day_period.get("endSecondOfDay")
            sale_time.weekday = weekday

    def __process_dish_supply_condiments(self, dishes):
        supply_condiments = {}
        for dish_id, dish in dishes.items():
            for sc in dish.supply_condiments:
                _sc = supply_condiments.get(sc.name)
                if not _sc:
                    supply_condiments.update({sc.name: sc})
                else:
                    sc.id = _sc.id
        supply_condiment_manager = SupplyCondimentManager(merchant=self._manager.merchant, store=self._manager.store)
        supply_condiment_manager.update_supply_condiments(supply_condiments)

    def __process_dish_attrs(self, dishes, attr_selection_dict):
        """把attr.id, attr.groupId统一"""
        groups = {}
        attrs = {}
        attrs_by_id = []
        for dish_id, dish in dishes.items():
            for attr in dish.attrs:
                groups.update({attr.group_name: attr.group_id})
                key = self.__get_attr_unique_key(attr)
                attrs.update({key: attr.id})

        for dish_id, dish in dishes.items():
            for attr in dish.attrs:
                attr.group_id = groups.get(attr.group_name)
                key = self.__get_attr_unique_key(attr)
                attr.id = attrs.get(key)
                attrs_by_id.append(attr)
        attr_manager = AttrManager(merchant=self._manager.merchant, store=self._manager.store)
        attr_manager.update_attr_groups(attrs_by_id, attr_selection_dict=attr_selection_dict)

    def __get_attr_unique_key(self, attr):
        return "{}-{}".format(attr.name, attr.reprice)

    def __set_dish_specs(self, dish, dish_dict):
        single = dish_dict.get("single")
        if not single:
            return
        dish_spec = single.get("dishSpec")
        if not dish_spec:
            return
        group_id = id_manager.generate_common_id()
        min_price = None
        for option in dish_spec.get("options"):
            if min_price is None:
                min_price = option.get("price", 0)
            else:
                min_price = min(option.get("price"), min_price)
        for option in dish_spec.get("options"):
            attr = dish.attrs.add()
            attr.id = id_manager.generate_common_id()
            attr.name = option.get("name")
            attr.reprice = option.get("price") - min_price
            attr.group_id = group_id
            attr.group_name = dish_spec.get("name")
            attr.type = dish_pb.Attr.SPECIFICATION
            status = option.get("status")
            if status == "SOLD_OUT":
                attr.status = dish_pb.Attr.SOLD_OUT
            elif status == "ON_SALE":
                attr.status = dish_pb.Attr.NORMAL
            elif status == "INACTIVE":
                attr.status = "DELETED"
        if min_price is not None:
            dish.price = min_price

    def __set_dish_child_dish_group(self, dishes, dish_groups):
        for dish_id, dish in dishes.items():
            dish_group = dish_groups.get(dish.id)
            if not dish_group:
                continue
            dish.type = dish_pb.Dish.COMBO_MEAL
            for group in dish_group:
                dish_group = dish.child_dish_groups.add()
                dish_group.id = id_manager.generate_common_id()
                dish_group.group_name = group.get("name")
                dish_group.order_min = group.get("minNumSelections")
                dish_group.order_max = group.get("maxNumSelections")
                dish_group.allow_duplicate = group.get("allowDuplicate")
                dish_group.is_fixed = group.get("isFixed")
                for child_dish in group.get("dishes"):
                    _child_dish_id = child_dish.get("dishId")
                    if dishes.get(_child_dish_id) is None:
                        continue
                    _child_dish = dish_group.child_dishes.add()
                    _child_dish.id = _child_dish_id
                    _child_dish.price = child_dish.get("addPrice")
                    _child_dish.name = dishes.get(_child_dish.id).name
                    _child_dish.is_must = child_dish.get("isRequired")
                    _child_dish.quantity_increment = child_dish.get("quantity")
                    _child_dish.market_price = child_dish.get("addPrice")

    def __set_dish_attrs(self, dish, dish_dict, attr_selection_dict):
        single = dish_dict.get("single")
        if not single:
            return
        attribute_groups = single.get("attributeGroups")
        if not attribute_groups:
            return
        for group in attribute_groups:
            group_id = id_manager.generate_common_id()
            if group.get("templateId"):
                group_id = group.get("templateId")
            for index, option in enumerate(group.get("options")):
                status = option.get("status")
                attr = dish.attrs.add()
                if option.get("optionId"):
                    attr.id = option.get("optionId")
                else:
                    attr.id = id_manager.generate_common_id()
                attr.name = option.get("name")
                attr.reprice = option.get("addPrice")
                attr.group_id = group_id
                attr.group_name = group.get("name")
                attr.sort = index
                if status == "INACTIVE":
                    attr.status = dish_pb.Attr.SOLD_OUT
                is_default = option.get("isDefault")
                if is_default:
                    attr.sort = -1
                attr_selection_dict.update(
                    {
                        attr.group_name: {
                            "max_num_selections": group.get("maxNumSelections"),
                            "min_num_selections": group.get("minNumSelections"),
                        }
                    }
                )
                attr.type = dish_pb.Attr.TASTE
            dish.attrs.sort(key=lambda x: x.sort)

    def __create_supply_condimetns(self, dishes):
        supply_condiments = {}
        ordering_da = OrderingServiceDataAccessHelper()
        for dish_dict in dishes:
            if dish_dict.get("type") != "ADDON":
                continue
            id = dish_dict.get("id")
            if id in supply_condiments:
                continue
            supply_condiment = dish_pb.SupplyCondiment()
            supply_condiment.id = id
            supply_condiment.market_price = dish_dict.get("price")
            supply_condiment.name = dish_dict.get("name")
            supply_condiments.update({id: supply_condiment})
            dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, dish_id=id)
            if dish and dish.type == dish_pb.Dish.SINGLE:
                dish.status = dish_pb.Dish.OFFLINE

        return supply_condiments

    def __set_dish_supply_condiments(self, dish, dish_dict, supply_condiments):
        max_num_addons = dish_dict.get("single", {}).get("maxNumAddons")
        min_num_addons = dish_dict.get("single", {}).get("minNumAddons")
        if max_num_addons is None or min_num_addons is None:
            pass
        elif max_num_addons == 10000 and min_num_addons == 0:
            dish.selection_type.lower_limit = 0
            dish.selection_type.upper_limit = 0
            dish.selection_type.type = dish_pb.SupplyCondimentSelectionType.UNLIMITED
        else:
            if max_num_addons == min_num_addons:
                dish.selection_type.type = dish_pb.SupplyCondimentSelectionType.NUMBER_REQUIRED
            else:
                dish.selection_type.type = dish_pb.SupplyCondimentSelectionType.NUMBER_RANGE
            dish.selection_type.lower_limit = min_num_addons
            dish.selection_type.upper_limit = max_num_addons
        if not dish_dict.get("single", {}).get("addonIds"):
            return
        for addon_id in dish_dict.get("single").get("addonIds"):
            s = supply_condiments.get(addon_id)
            if not s:
                continue
            dish_sc = dish.supply_condiments.add()
            dish_sc.CopyFrom(s)

    def __get_all_categories(self):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return []
        url = self._ops.generate_url(ShilaiPosConstants.CATEGORY_LIST)
        json_data = {}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret.get("data")

    def __get_all_dishes(self):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return []
        url = self._ops.generate_url(ShilaiPosConstants.DISH_LIST)
        json_data = {}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret.get("data")

    def __get_packaging_boxs(self):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return []
        url = self._ops.generate_url(ShilaiPosConstants.PACKAGING_BOXS)
        json_data = {}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret.get("data")

    def __get_dishes(self, dish_ids):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return []
        url = self._ops.generate_url(ShilaiPosConstants.DISH_INFO)
        json_data = {"dishIds": dish_ids}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret.get("data")

    def sync_dishes(self, db_dishes, dish_ids):
        """从pos端同步单个菜品信息"""
        categories = self.__get_all_categories()
        dishes = self.__get_dishes(dish_ids)
        packaging_boxs = self.__get_packaging_boxs()
        dish_id_to_category_id = {}
        categories = self.generate_categories(categories, dish_id_to_category_id)
        packaging_boxs = self.generate_packaging_boxs(packaging_boxs)
        dishes = self.generate_dishes(dishes, dish_id_to_category_id, packaging_boxs, db_dishes={x.id: x for x in db_dishes})
        return {"dishes": dishes, "categories": categories, "packaging_boxs": packaging_boxs}

    def sync_categories(self):
        """同步所有菜品分类信息"""
        categories = self.__get_all_categories()
        return categories

    def get_dishes_changeful_info(self):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return {}
        url = self._ops.generate_url(ShilaiPosConstants.DISH_CHANGEFUL_INFO)
        json_data = {}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret.get("data")

    def __update_dish(self, dishes, operate):
        url = self._ops.generate_url(ShilaiPosConstants.UPDATE_DISH)
        json_data = {"dishes": dishes, "operate": operate}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        return ret

    def _common_dish_remain(self, dishes, action, raise_error=False, **kwargs):
        ret = self.__update_dish(dishes, action)
        if raise_error and ret.get("errcode") in (1013, 1014):
            raise errors.ShowError(ret.get("errmsg"), errcode=ret.get("errcode"))

    def increase_dish_remain_quantity(self, dishes, **kwargs):
        self._common_dish_remain(dishes, self.INCREASE_DISH_REMAIN_QUANTITY, raise_error=False, **kwargs)

    def decrease_dish_remain_quantity(self, dishes, raise_error=False, **kwargs):
        self._common_dish_remain(dishes, self.DECREASE_DISH_REMAIN_QUANTITY, raise_error=False, **kwargs)

    def check_dish_remain_quantity(self, dishes: dict, **kwargs):
        self._common_dish_remain(dishes, self.CHECK_DISH_REMAIN_QUANTITY, raise_error=True, **kwargs)

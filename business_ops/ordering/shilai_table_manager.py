# -*- coding: utf-8 -*-

from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.ordering.base_table_manager import BaseTableManager
from business_ops.ordering.shilai_ops_manager import ShilaiOPSManager


class ShilaiTableManager(BaseTableManager):

    def __init__(self, table_manager=None, *args, **kargs):
        self._manager = table_manager
        super(ShilaiTableManager, self).__init__(*args, **kargs)
        self._ops = ShilaiOPSManager(
            merchant=table_manager.merchant, store=table_manager.store)

    def sync_tables(self):
        """ 同步脚本调用
        同步客如云桌台列表
        """
        uri = ShilaiPosConstants.LAYOUT_LIST
        url = self._ops.generate_url(uri)
        params = {
            "merchantId": self._manager.merchant.id,
            "storeId": self._manager.store.id
        }
        resp = self._ops.do_post(url, params)
        result = []
        if resp.get("errcode") != 0:
            return result

        areas = resp.get("data")
        if not areas:
            return result
        for area in areas:
            tables = area.get("tables")
            for table in tables:
                result.append({
                    "id": table.get("id"),
                    "name": table.get("name"),
                    "ordering_service_table_id": table.get("id"),
                    "area_name": area.get("name"),
                    "area_id": area.get("id")
                })
        return result

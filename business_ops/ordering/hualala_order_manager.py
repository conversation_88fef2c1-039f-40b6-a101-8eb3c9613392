# -*- coding: utf-8 -*-

import logging

import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.base_order_manager import BaseOrderManager
from business_ops.ordering.hualala_ops import HualalaOPSManager
from business_ops.ordering.constants import HualalaConstants
from business_ops.ordering.shilai_order_manager import ShilaiOrderManager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class HualalaOrderManager(HualalaOPSManager, BaseOrderManager):
    def __init__(self, order_manager=None, *args, **kargs):
        self._manager = order_manager
        self._shilai_order_manager = ShilaiOrderManager(
            order_manager)
        super(HualalaOrderManager, self).__init__(*args, **kargs)

    def create_ordering_order(self, order):
        pass

    def pay_order(self, **kargs):
        """ 支付成功后调用此函数
        """
        return True

    def add_dish_ordering_order(self, dishes, order, user_id):
        pass

    def initiate_ordering_refund(self, order=None, transaction_id=None, **kargs):
        if not order:
            raise errors.OrderNotFound()
        if order.status != dish_pb.DishOrder.PAID:
            raise errors.Error(err=error_codes.ORDER_NOT_PAID)
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id,
            "orderKey": order.ordering_service_order_id,
            "cancelOrderCause": "扫码点餐申请退款"
        }
        route = HualalaConstants.ORDER_REFUND
        ret = self.try_post(route, params)
        if ret.flag:
            return True
        raise errors.Error(err=error_codes.hualala_refund_order_fail)

    def order_status(self, **kargs):
        """
        15: 提交订单(未支付)
        20: 已付款
        40: 商家接单
        41: 商家已下单
        45: 制作完成,只自提
        46: 确认送出,只外卖
        50: 确认送达,只外卖
        60: 订单完成
        64: 退款中
        65: 退款完成
        2: 拒绝退款
        6: 同意部分退款
        5: 拒绝部分退款
        """
        body = kargs.get("body")
        order_state_code = body.get("orderStateCode")
        third_order_id = body.get("thirdOrderID")
        ordering_da = OrderingServiceDataAccessHelper()
        order = ordering_da.get_order(id=third_order_id)
        if not order:
            return
        redis_client = RedisClient().get_connection()
        if order_state_code == "65":
            key = CacheServerKeys.get_order_refund_redis_callback_key(order)
            if not redis_client.set(key, 1, ex=6000, nx=True):
                return
            logger.info("哗啦啦订单退款: {}".format(order.id))
            self._manager.pos_return_order(transaction_id=order.transaction_id)

    def sync_direct_pay_order(self, order, transaction):
        return self._shilai_order_manager.sync_direct_pay_order(order, transaction)

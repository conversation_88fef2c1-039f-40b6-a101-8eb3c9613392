# -*- coding: utf-8 -*-

import logging

import proto.ordering.supply_condiment_pb2 as supply_condiment_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.dish_supply_condiment_da_helper import DishSupplyCondimentDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class SupplyCondimentManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(SupplyCondimentManager, self).__init__(*args, **kargs)

    def generate_dishes_supply_condiment(self):
        """ 对于已有菜品,把所有加料取出来,放入加料库中
        """
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self.merchant.id)
        for dish in dishes:
            self.add_or_update_dish_supply_condiment(dish=dish)

    def add_or_update_supply_condiment(self, name, market_price, id=None, status=None):
        """ 新增或修改加料模板
        `name`: 加料名
        `market_price`: 加料价格,单位: 分
        `id`: 如果id不为空则表示会修改已有的加料模板
        `status`: 状态修改,新增模板时status为默认值,其它情况只有与id同时传时才有用
        """
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        supply_condiment = self.get_or_create_supply_condiment(id=id, name=name, market_price=market_price, status=status)
        if name is not None:
            supply_condiment.name = name
        if market_price is not None:
            supply_condiment.market_price = market_price
        if status is not None:
            status = dish_pb.SupplyCondiment.Status.Value(status)
            supply_condiment.status = status
        supply_condiment_da.add_or_update_supply_condiment(supply_condiment)
        if id is not None:
            dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, supply_condiment_id=id)
            self.update_dish_supply_condiment(dishes, supply_condiment)
        return supply_condiment

    def update_dish_supply_condiment(self, dishes, supply_condiment):
        """ supply_condiment 更新后 需要同时更新有这个加料的菜品的相关信息
        """
        ordering_da = OrderingServiceDataAccessHelper()
        for dish in dishes:
            for sc in dish.supply_condiments:
                if sc.id != supply_condiment.id:
                    continue
                sc.name = supply_condiment.name
                sc.market_price = supply_condiment.market_price
                sc.status = supply_condiment.status
                sc.sort = supply_condiment.sort
            dish.supply_condiments.sort(key=lambda s: s.sort)
            ordering_da.add_or_update_dish(dish=dish)

    def get_or_create_supply_condiment(self, id, name, market_price, status, update_dish=False):
        """ 根据ID从数据库查找 supply_condiment,如果没有找到则新建一个并返回
        """
        sc_da = DishSupplyCondimentDataAccessHelper()
        supply_condiment_by_id = sc_da.get_supply_condiment(merchant_id=self.merchant.id, id=id)
        if supply_condiment_by_id:
            return supply_condiment_by_id
        supply_condiment_by_name = sc_da.get_supply_condiment(merchant_id=self.merchant.id, name=name)
        if supply_condiment_by_name:
            return supply_condiment_by_name
        if supply_condiment_by_id and supply_condiment_by_name and supply_condiment_by_name != name:
            raise errors.ShowError("不能修改为已存在的加料名")
        supply_condiment = None
        if supply_condiment_by_id:
            supply_condiment = supply_condiment_by_id
        if supply_condiment_by_name:
            supply_condiment = supply_condiment_by_name
        if not supply_condiment:
            supply_condiment = self.create_supply_condiment(name=name, market_price=market_price)
        return supply_condiment

    def add_or_update_dish_supply_condiment(self, dish_id=None, dish=None):
        """ 增加或者修改加料
        `dish_id` `dish` 两者必须传一.
        主要用于
        1. 已有菜品的supply_condiment的设置
        2. 从第三方同步菜品时候设置
        3. 从脚本导入菜品时设置
        """
        ordering_da = OrderingServiceDataAccessHelper()
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        if dish is None:
            dish = ordering_da.get_dish(merchant_id=self.merchant.id, dish_id=dish_id)
        if not dish:
            return
        for supply_condiment in dish.supply_condiments:
            sc = self.get_or_create_supply_condiment(
                id=None, name=supply_condiment.name, market_price=supply_condiment.market_price, status=supply_condiment.status)
            sc.id = supply_condiment.id
            supply_condiment_da.add_or_update_supply_condiment(sc)

    def get_supply_condiments(self):
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        supply_condiments = supply_condiment_da.get_supply_condiments(merchant_id=self.merchant.id)
        supply_condiments.sort(key=lambda x: x.sort)
        return supply_condiments

    def reorder_supply_condiments(self, supply_condiment_ids):
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        supply_condiments = supply_condiment_da.get_supply_condiments(merchant_id=self.merchant.id)
        supply_condiments = {s.id: s for s in supply_condiments}
        for index, sc_id in enumerate(supply_condiment_ids):
            supply_condiment = supply_condiments.get(sc_id)
            if not supply_condiment:
                continue
            supply_condiment.sort = index + 1
        supply_condiment_da.reorder_supply_condiments(self.merchant.id, supply_condiments)

        ordering_da = OrderingServiceDataAccessHelper()
        for _, supply_condiment in supply_condiments.items():
            dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, supply_condiment_id=supply_condiment.id)
            self.update_dish_supply_condiment(dishes, supply_condiment)

    def create_supply_condiment(self, name, market_price, sort=None, status=None):
        supply_condiment = supply_condiment_pb.DishSupplyCondiment()
        supply_condiment.id = id_manager.generate_common_id()
        supply_condiment.name = name
        supply_condiment.market_price = market_price
        if sort is not None:
            supply_condiment.sort = sort
        if status is not None:
            supply_condiment.status = status
        supply_condiment.merchant_id = self.merchant.id
        return supply_condiment

    # ##########时来pos机
    def update_supply_condiments(self, supply_condiments):
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        # 数据库已存在的加料
        _supply_condiments = supply_condiment_da.get_supply_condiments(
            merchant_id=self.merchant.id)
        _supply_condiments = {s.id: s for s in _supply_condiments}
        # 本次新增的加料
        new_supply_condiments = []
        for id, _sc in _supply_condiments.items():
            if id not in supply_condiments:
                _sc.status = dish_pb.SupplyCondiment.OUT_OF_STOCK
        for id, sc in supply_condiments.items():
            if id not in _supply_condiments:
                _sc = self.create_supply_condiment(
                    name=sc.name, market_price=sc.market_price)
            _sc.market_price = sc.market_price
            _sc.sort = sc.sort
            _sc.id = sc.id
            new_supply_condiments.append(_sc)
        for sc in new_supply_condiments:
            supply_condiment_da.add_or_update_supply_condiment(sc)

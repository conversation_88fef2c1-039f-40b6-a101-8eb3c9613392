# -*- coding: utf-8 -*-

import time
from collections import namedtuple, defaultdict

import logging

from dao.config_da_helper import ConfigDataAccessHelper
import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.coupons_pb2 as coupons_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.order_operation_record_pb2 as order_operation_record_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.base_manager import BaseManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.wallet_manager import WalletManager
from common.utils import date_utils, distribute_lock
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from business_ops.promotion.coupon.coupon_manager import CouponManager as NewCouponManager
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class OrderManager(BaseManager):
    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._back_products = []
        self._transactions = kargs.get("transactions", [])
        # 一共要退的金额
        self._total_refund_fee = 0
        # 要退的这些菜品的可打折金额
        # self._total_refund_fee - self._enable_discount_fee就等于不打折金额
        self._enable_discount_fee = 0
        self._coupon_fee = 0

        self._coupon = None
        self._red_packet = None
        self._decrease_wallet_balance = 0
        self._partial_refund_record = None
        self._back_comment = kargs.get("back_comment", None)
        self._staff_name = kargs.get("staff_name")
        self._platform = kargs.get("platform", None)

        self._order_operation_record = None
        self._save_order_operation_record = False

    @property
    def platform(self):
        return self._platform

    @property
    def transactions(self):
        return self._transactions

    @transactions.setter
    def transactions(self, transactions):
        self._transactions = transactions

    @property
    def coupon(self):
        return self._coupon

    @property
    def red_packet(self):
        return self._red_packet

    @property
    def partial_refund_record(self):
        # 废弃
        if self._partial_refund_record is not None:
            return self._partial_refund_record
        ordering_da = OrderingServiceDataAccessHelper()
        self._partial_refund_record = ordering_da.get_order_partial_refund_record(id=self.order.id)
        return self._partial_refund_record

    @property
    def order_operation_record(self):
        if self._order_operation_record is not None:
            return self._order_operation_record
        ordering_da = OrderingServiceDataAccessHelper()
        self._order_operation_record = ordering_da.get_order_operation_record(id=self.order.id)
        return self._order_operation_record

    def __save_order_operation_record(self):
        logger.info(f"############## 保存 order_operation_record {self.order.id} #############")
        if not self._save_order_operation_record:
            return
        if not self.order_operation_record:
            return None
        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.add_or_update_order_operation_record(self.order_operation_record)

    def update_order_operation_record_pay_order(self, type):
        """订单支付时保存记录"""
        self.get_or_create_order_operation_record(creation=True)
        record = self.__create_record(type)
        self.__set_pay_record(record)
        self._save_order_operation_record = True
        self.__save_order_operation_record()

    def update_order_operation_record_partial_refund(self, type):
        """部分退款时保存记录"""
        self.get_or_create_order_operation_record(creation=True)
        record = self.__create_record(type)
        self.__set_partial_refund_record(record)
        self._save_order_operation_record = True
        self.__save_order_operation_record()

    def update_order_operation_record_refund(self, type):
        """全单退款时保存记录"""
        self.get_or_create_order_operation_record(creation=True)
        record = self.__create_record(type)
        self.__set_refund_record(record)
        self._save_order_operation_record = True
        self.__save_order_operation_record()

    def get_or_create_order_operation_record(self, creation=False):
        """创建或者获取一个order_operation_record"""
        if self.order_operation_record:
            return self.order_operation_record
        if creation is False:
            return None
        self.__create_order_operation_record()
        return self.order_operation_record

    def __create_order_operation_record(self):
        self._order_operation_record = order_operation_record_pb.OrderOperationRecord()
        self._order_operation_record.id = self.order.id

    def __create_record(self, type):
        """根据类型创建一个record
        type: Transaction的类型
        """
        record = None
        if type in [wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT, wallet_pb.Transaction.DIRECT_PAY]:
            record = self.order_operation_record.pay_records.add()
        elif type == wallet_pb.Transaction.ORDERING_REFUND:
            record = self.order_operation_record.refund_records.add()
        elif type == wallet_pb.Transaction.PARTIAL_REFUND:
            record = self.order_operation_record.partial_refund_records.add()
        if record is None:
            return
        self.__set_order_record_base_info(record, type)
        return record

    def __set_order_record_base_info(self, record, type):
        """所有record都会有信息"""
        record.type = type
        if self._staff_name is not None:
            record.staff_name = self._staff_name

    def __set_partial_refund_record(self, record):
        """部分退款record才会有的信息"""
        if record.type != wallet_pb.Transaction.PARTIAL_REFUND:
            return
        record.refund_time = int(time.time())
        if self._back_comment is not None:
            record.back_comment = self._back_comment
        self.__set_partial_refund_record_transactions(record)
        for product in self._back_products:
            item = record.items.add()
            item.CopyFrom(product)

    def __set_refund_record(self, record):
        """整单退款record才会有的信息"""
        if record.type != wallet_pb.Transaction.ORDERING_REFUND:
            return
        record.refund_time = int(time.time())
        if self._back_comment is not None:
            record.back_comment = self._back_comment
        self.__set_refund_record_transactions(record)

    def __set_pay_record(self, record):
        """支付时记录下的record信息"""
        if record.type != wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            return
        record.pay_time = int(time.time())
        self.__set_pay_record_transactions(record)

    def __set_pay_record_transactions(self, record):
        """支付时记录流水信息"""
        for transaction in self._transactions:
            t = record.transactions.add()
            t.CopyFrom(transaction)

    def __set_partial_refund_record_transactions(self, record):
        """部分退时记录流水信息"""
        for transaction in self._transactions:
            t = record.transactions.add()
            t.CopyFrom(transaction)

    def __set_refund_record_transactions(self, record):
        """退款时记录流水信息"""
        for transaction in self._transactions:
            t = record.transactions.add()
            t.CopyFrom(transaction)

    def __clear(self):
        if self._coupon:
            coupon_da = CouponDataAccessHelper()
            coupon_da.update_or_create_coupon(self._coupon)
        if self._red_packet:
            red_packet_da = RedPacketDataAccessHelper()
            red_packet_da.add_or_update_red_packet(self._red_packet)
        if self._decrease_wallet_balance:
            wallet_manager = WalletManager()
            wallet_manager.decrease_balance(
                self.transaction.payer_id, 0, self._decrease_wallet_balance, self.transaction.payee_id
            )
            transaction_ma = TransactionManager()
            transaction = transaction_ma.handle_user_transfer_prepay(
                payer_id=self.transaction.payer_id,
                payee_id=self.transaction.payee_id,
                bill_fee=self._decrease_wallet_balance,
                transaction_type=wallet_pb.Transaction.RED_PACKET_DEDUCTION,
            )
            transaction.paid_time = date_utils.timestamp_second()
            transaction.state = wallet_pb.Transaction.SUCCESS
            transaction_ma.update_transaction(transaction)
        if self.transaction:
            transaction_da = TransactionDataAccessHelper()
            self.transaction.bill_fee = self.order.bill_fee
            transaction_da.add_or_update_transaction(self.transaction)

        if self.transaction.pay_method == wallet_pb.Transaction.WALLET:
            self.order.wallet_commission_fee = 0
            self.order.platform_discount_fee = 0

    def __round(self, value):
        """1位小数的4舍5入"""
        return int(value + 0.5)

    def check_refund_fee(self):
        """多分类不同折扣，不支持按金额退款"""
        order_da = OrderingServiceDataAccessHelper()
        discount = set()
        products = []
        for product in self.order.products:
            products.append(product)
        for product in self.order.back_products:
            for p in product.products:
                products.append(p)
        for product in products:
            category_id = product.category_id
            category = order_da.get_category(id=category_id, merchant_id=self.merchant.id)
            if category.no_discount or product.price == 0:
                continue
            discount.add(product.discount_price / product.price)
        if len(discount) > 1:
            return False
        return True

    def partial_refund(self, uuids, back_fee=0, is_refund=False):
        key = f"{self.order.id}_partial_refund"
        outer_ex = None
        with distribute_lock.redislock(key, 3000, 0, 0) as lock:
            if not lock:
                raise errors.ShowError("订单退款中,请稍后再试")
            try:
                return self.partial_refund_without_lock(uuids, back_fee, is_refund)
            except Exception as ex:
                logger.exception(f"{self.order.id}发起部分退款失败: {ex}")
                outer_ex = ex
        raise outer_ex

    def partial_refund_without_lock(self, uuids, back_fee=0, is_refund=False):
        """订单部分退菜
        :uuids: 要退的菜的product.uuid字段
        """
        transaction_da = TransactionDataAccessHelper()
        self.transaction = transaction_da.get_transaction_by_id(self.order.transaction_id)
        before_paid_fee = self.transaction.paid_fee
        if self.transaction.paid_fee <= 0:
            raise errors.ShowError("订单可退金额为0")
        if back_fee > 0:
            self._total_refund_fee += back_fee
            self.order.bill_fee -= back_fee
            self.order.total_fee = self.order.bill_fee
            self.order.market_bill_fee = self.order.bill_fee
            self._enable_discount_fee = self.order.bill_fee
            self.order.enable_discount_fee = self._enable_discount_fee
        else:
            self.__collect_back_products(uuids)

        if self.order.bill_fee < 0:
            if not is_refund:
                raise errors.ShowError("交易金额超过可退金额")
            self.order.bill_fee = 0
            self.order.total_fee = self.order.bill_fee
            self.order.market_bill_fee = self.order.bill_fee
            self._total_refund_fee = self.transaction.paid_fee
        elif (
            self.transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY
            and self.transaction.pay_method != wallet_pb.Transaction.WALLET
        ):
            # 当前需退款金额：_total_refund_fee、订单剩余折扣金额：discount_amount
            # max_discount = int(
            #     self.merchant.preferences.coupon_config.max_discount
            #     * self.merchant.preferences.coupon_config.dish_discount_rate
            #     / float(100)
            # )
            # before_refund_fee = self._total_refund_fee
            # self._total_refund_fee = int(self._total_refund_fee * (100 - max_discount) / float(100))
            # if self.order.discount_amount > 0:
            #     self.order.discount_amount = max(self.order.discount_amount - (before_refund_fee - self._total_refund_fee), 0)

            # if self.order.discount_amount > 0:
            #     self.order.discount_amount = max((self.order.discount_amount - self._total_refund_fee), 0)
            if self.order.bill_fee == 0:
                self._total_refund_fee = self.transaction.paid_fee

        # raise
        # 做清理工作
        # 把红包与用券进行一个清除
        self.__record_clear_red_packet()
        self.__record_clear_coupon()
        self.__record_clear_new_coupon()
        # self.__fix_wallet()
        self.__partial_refund(is_refund)
        self.__clear()
        manager = SelfDishOrderPaymentManager(
            transaction_id=self.transaction.id,
            merchant=self.merchant,
            partial_refund=True,
            order=self.order,
            transaction=self.transaction,
            check_coupon=False,
        )
        self.order.is_partial_refund = True
        # 清空补贴转账金额
        # manager.clear_transfer_fee(self)
        if self.order.bill_fee > 0:
            # 前面对数据进行了清理,调用此函数重新处理订单
            manager.partial_refund_ordering(self)
        else:
            logger.info(f"通过退款退了全部菜品 {self.order.id}")
            self.clear_order_fees()
            self.clear_order_transaction()
        refund_paid_fee = before_paid_fee - self.transaction.paid_fee
        transaction_manager = TransactionManager()
        self._refund_transaction.paid_fee = refund_paid_fee
        transaction_manager.update_transaction(self._refund_transaction)

    def clear_order_transaction(self):
        self.transaction.state = wallet_pb.Transaction.REFUNDED
        transaction_manager = TransactionManager()
        transaction_manager.update_transaction(self.transaction)

    def clear_order_fees(self):
        self.order.paid_in_fee = 0
        self.order.paid_fee = 0
        self.order.fanpiao_commission_fee = 0
        self.order.red_packet_fee = 0
        self.order.total_fee = 0
        self.order.actual_total_receivable_fee = 0
        self.order.platform_discount_fee = 0
        self.order.coupon_commission_fee = 0
        self.order.discount_amount = 0
        self.order.status = dish_pb.DishOrder.POS_RETURN
        order_da = OrderingServiceDataAccessHelper()
        order_da.add_or_update_order(self.order)

    def __fix_wallet(self):
        if self.transaction.pay_method != wallet_pb.Transaction.WALLET:
            return
        if self.order.bill_fee == 0:
            self._total_refund_fee = self.transaction.paid_fee
            return
        config_da = ConfigDataAccessHelper()
        merchant_vip_membership_config = config_da.get_vip_membership_config(self.transaction.payee_id)
        discount = merchant_vip_membership_config.vip_membership_discount if merchant_vip_membership_config else 0
        self._total_refund_fee = round(self._total_refund_fee * (100 - discount) / 100)

    def __record_clear_coupon(self):
        if self.transaction.use_coupon_id == "":
            return
        coupon_da = CouponDataAccessHelper()
        self._coupon = coupon_da.get_coupon_by_id(self.transaction.use_coupon_id)
        if not self._coupon:
            return
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_category = coupon_category_da.get_coupon_category(id=self._coupon.coupon_category_id)
        if coupon_category:
            self.__can_partial_refund_coupon_order(coupon_category)
        if self._coupon.state == coupons_pb.Coupon.USED and self.order.bill_fee == 0:
            self._coupon.state = coupons_pb.Coupon.ACCEPTED
        # 如果还能继续使用这个券
        if self.transaction.use_coupon_id:
            self._coupon_fee = coupon_category.cash_coupon_spec.reduce_cost

    def __can_partial_refund_coupon_order(self, coupon_category):
        """判定一张用券的订单是否能部分退菜
        退用券支付的订单时将发生以下3种情况
        a. 订单总金额大于用券门槛
        b. 要退款的金额不足以抵扣券的面值
        1. 当 a = True 时,返回成功
        2. 当 a = False, b = True 时, 返回出错
        3. 当 a = False, b = False 时, 退款金额需要减去券的金额
        """
        # least_cost = coupon_category.cash_coupon_spec.least_cost  # 券使用门槛
        # reduce_cost = coupon_category.cash_coupon_spec.reduce_cost  # 用券优惠金额
        # msg = f"""
        # 订单{self.order.id} 部分退款后券是否能使用
        # 订单总金额({self.order.paid_fee}) >= 用券门槛({least_cost}) {self.order.paid_fee >= least_cost}
        # 退款金额({self._total_refund_fee}) >= 优惠金额({reduce_cost}) {self._total_refund_fee >= reduce_cost}
        # """
        # logger.info(msg)
        # if self.order.paid_fee >= least_cost:
        #     return
        # if self._total_refund_fee < reduce_cost:
        #     raise errors.ShowError("退款后达不到用券要求,请全部退款后重新下单")
        if self.order.bill_fee == 0:
            self._total_refund_fee = self.transaction.paid_fee
        else:
            self._total_refund_fee = self.transaction.paid_fee - int(
                self.transaction.paid_fee * float(self.order.bill_fee / self.transaction.bill_fee)
            )

        if self._total_refund_fee == self.transaction.paid_fee:
            self.order.bill_fee = 0
            self.transaction.use_coupon_id = ""
            self.order.platform_discount_fee = 0
            self.order.coupon_package_commission_fee = 0

    def __record_clear_red_packet(self):
        red_packet_da = RedPacketDataAccessHelper()
        self._red_packet = red_packet_da.get_red_packet(new_transaction_id=self.transaction.id)
        if not self._red_packet:
            return
        if self._red_packet.status != red_packet_pb.RedPacket.CANCELLED:
            self._decrease_wallet_balance = self._red_packet.total_value
        self._red_packet.status = red_packet_pb.RedPacket.CANCELLED

    def __record_clear_new_coupon(self):
        if not self.order.coupon_ids:
            return
        if self.order.bill_fee == 0:
            self._total_refund_fee = self.transaction.paid_fee
        if self._total_refund_fee == self.transaction.paid_fee:
            self.order.bill_fee = 0
            coupon_manager = NewCouponManager(user_id=self.order.user_id, merchant_id=self.order.merchant_id)
            for i, coupon_id in enumerate(self.order.coupon_ids):
                coupon = coupon_manager.get_coupon(id=coupon_id)
                if not coupon:
                    continue
                if coupon.coupon_type not in [
                    coupon_pb.Coupon.OPENING_GROUP_PURCHASE,
                    coupon_pb.Coupon.FANPIAO_PURCHASE_BONUS,
                ]:  # 当前只有团长立减券可以退
                    continue
                self.order.coupon_ids.pop(i)
                coupon_manager.update_coupon(coupon, status=coupon_pb.Coupon.ACCEPTED)
                coupon_manager.add_or_update_coupon(coupon)

    def __partial_refund(self, is_refund=False):
        if self._total_refund_fee == 0:
            logger.info(f"{self.order.id}发起部分退菜,待退款金额为0")
            raise errors.ShowError("订单可退金额为0")
        if self.transaction is None:
            raise errors.ShowError("交易流水不存在")
        if self.transaction.state != wallet_pb.Transaction.SUCCESS:
            raise errors.ShowError("交易状态错误,不能发起退款")
        if self.transaction.bill_fee < self._total_refund_fee:
            if not is_refund:
                raise errors.ShowError("交易金额超过可退金额")
            self._total_refund_fee = self.transaction.bill_fee
        payment_manager = PaymentManager(pay_method=self.transaction.pay_method, merchant=self.merchant)
        transaction_manager = TransactionManager()
        self._refund_transaction = transaction_manager.create_partial_refund_transaction(
            self.transaction, bill_fee=self._total_refund_fee, paid_fee=self._total_refund_fee
        )
        self.transaction.bill_fee = self.order.bill_fee
        ret = payment_manager.partial_refund(
            self.transaction, self._refund_transaction, order=self.order, enable_discount_fee=self._enable_discount_fee
        )
        if not ret:
            raise errors.ShowError("退款发起失败")
        if self.transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            self.transaction.paid_fee -= self._total_refund_fee
        self._refund_transaction.state = wallet_pb.Transaction.SUCCESS
        logger.info(f"订单{self.order.id}发起部分退款,退款金额{self._total_refund_fee} {self._enable_discount_fee} {ret}")
        self._transactions.append(self._refund_transaction)

    def _get_product_schema(self, order=None):
        if order is None:
            order = self.order
        schema = defaultdict(list)
        for product in order.products:
            if product.parent_uuid:
                schema[product.parent_uuid].append(product)
            else:
                schema[product.uuid].append(product)
        return schema

    def __collect_back_products(self, uuids):
        """根据前端传过来的uuid列表,把要退的菜的product对象找出来"""
        uuid_keys = set(uuids.keys())
        products = []
        order_back_products = self.order.back_products.add()
        for product in self.order.products:
            products.append(product)
        for product in products:
            if product.parent_uuid:
                continue
            if product.uuid not in uuid_keys:
                self.__cal_product_enable_discount_fee(product, product.quantity)
                continue
            self.__collect_back_product(self.order.products, order_back_products, product, uuid_keys, uuids, sub=True)
        for add_product in self.order.add_products:
            products = []
            for product in add_product.products:
                products.append(product)
            for product in products:
                if product.parent_uuid:
                    continue
                if product.uuid not in uuid_keys:
                    self.__cal_product_enable_discount_fee(product, product.quantity)
                    continue
                self.__collect_back_product(add_product.products, order_back_products, product, uuid_keys, uuids, sub=True)
        self._enable_discount_fee = max(min(self.order.bill_fee, self._enable_discount_fee), 0)
        self.order.enable_discount_fee = self._enable_discount_fee

    def __collect_back_product(self, products, order_back_products, product, uuid_keys, uuids, sub=True):
        if product.uuid not in uuid_keys:
            return
        quantity = uuids.get(product.uuid).get("quantity")
        product_obj = self.__split_product(product, quantity)
        refund_fee, total_fee = self._get_refund_fee(product_obj, quantity)
        self._total_refund_fee += refund_fee

        if product_obj.product is None:
            products.remove(product)
        if self.order.discount_amount > 0:
            self.order.discount_amount = max(int(self.order.discount_amount - (total_fee - refund_fee)), 0)
        self._back_products.append(product_obj.refund_product)
        self.__cal_product_enable_discount_fee(product_obj.product, quantity)
        self.order.bill_fee -= total_fee
        self.order.total_fee -= refund_fee
        self.order.market_bill_fee = self.order.bill_fee

        back_product = order_back_products.products.add()
        back_product.CopyFrom(product_obj.refund_product)

    def __split_product(self, product, quantity):
        """当一个product退部分时,把一个product分裂成两个
        :product: 原订单中的product
        :quantity: 要退的数量
        """
        Product = namedtuple("Product", ["product", "refund_product", "refund_order"])
        back_product = dish_pb.Product()
        back_product.CopyFrom(product)

        back_order = dish_pb.DishOrder()
        back_order.CopyFrom(self.order)

        if product.quantity == quantity:
            self._create_refund_product(back_product, quantity, order=back_order)
            return Product(None, back_product, back_order)

        self.__rebuild_product(product, product.quantity - quantity)
        self._create_refund_product(back_product, quantity, order=back_order)
        return Product(product, back_product, back_order)

    def __rebuild_product(self, product, quantity):
        """
        :quantity: product的最终数量
        """
        self.__rebuild_product_supply_condiments(product, quantity)
        product.total_fee = int(product.total_fee / product.quantity * quantity)
        product.quantity = quantity

    def __rebuild_product_supply_condiments(self, product, quantity, order=None):
        """
        :quantity: 新的数量
        """
        for sc in product.supply_condiments:
            if product.type != dish_pb.Dish.COMBO_MEAL:
                sc.quantity = int(sc.quantity / product.quantity * quantity)

        schema = self._get_product_schema(order=order)
        sub_product = schema.get(product.uuid, [])
        if len(sub_product) > 1:
            for sub in sub_product:
                for sc in sub.supply_condiments:
                    sc.quantity = int(sc.quantity / product.quantity * quantity)

    def _create_refund_product(self, product, quantity, order=None):
        self.__rebuild_product_supply_condiments(product, quantity, order=order)
        product.total_fee = int(product.total_fee / product.quantity * quantity)

    def _check_category_no_dicount(self, product):
        order_da = OrderingServiceDataAccessHelper()
        category = order_da.get_category(id=product.category_id, merchant_id=self.merchant.id)
        return category.no_discount

    def _get_refund_fee(self, product_obj, back_quantity) -> int:
        back_product = product_obj.refund_product
        back_order = product_obj.refund_order

        def cal_dicount_fee(rate, back_product, child_dish_fee, take_away_fee, sc_refund_fee, attr_refund_fee):
            if self._check_category_no_dicount(back_product):
                rate = 1
            total_fee = back_product.total_fee + attr_refund_fee + sc_refund_fee + take_away_fee + child_dish_fee
            return round(total_fee * rate), int(total_fee)

        child_dish_fee = self.__cal_child_dish_fee(back_product, order=back_order) * back_quantity
        sc_refund_fee, child_sc_fee = self.__cal_product_supply_condiments_fee(back_product, sub=True, order=back_order)
        attr_refund_fee, child_attr_fee = self.__cal_product_attrs_fee(back_product, sub=True, order=back_order)
        attr_refund_fee *= back_quantity
        child_attr_fee *= back_quantity
        take_away_fee, child_take_away_fee = self._get_take_away_fee(back_product, sub=True, order=back_order)
        take_away_fee *= back_quantity
        child_take_away_fee *= back_quantity

        # 饭票折扣
        if self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            fanpiao_da = FanpiaoDataAccessHelper()
            fanpiao = fanpiao_da.get_fanpiaos(record_transaction_id=self.transaction.id)[0]
            rate = (100 - fanpiao.discount) / 100
            return cal_dicount_fee(
                rate,
                back_product,
                child_dish_fee,
                child_take_away_fee,
                sc_refund_fee + child_sc_fee,
                child_attr_fee,
            )

        # 零钱折扣
        elif self.transaction.pay_method == wallet_pb.Transaction.WALLET:
            config_da = ConfigDataAccessHelper()
            config = config_da.get_vip_membership_config(self.merchant.id)
            rate = (100 - config.vip_membership_discount) / 100
            return cal_dicount_fee(
                rate,
                back_product,
                child_dish_fee,
                child_take_away_fee,
                sc_refund_fee + child_sc_fee,
                child_attr_fee,
            )

        if back_product.price > 0:
            rate = back_product.discount_price / back_product.price
        else:
            rate = 1

        if rate == 0:
            rate = 1

        total_fee = back_product.total_fee + child_take_away_fee + child_attr_fee + sc_refund_fee + child_sc_fee
        refund_fee = (
            (back_product.total_fee - attr_refund_fee - take_away_fee) * rate
            + take_away_fee
            + child_take_away_fee
            + sc_refund_fee
            + child_sc_fee
            + attr_refund_fee
            + child_attr_fee
            + child_dish_fee
        )
        return round(refund_fee), int(total_fee)

    def __cal_product_enable_discount_fee(self, product, quantity):
        """未被退的菜，且折扣总价"""
        if product is None:
            return 0
        if self._check_category_no_dicount(product):
            return 0
        child_dish_fee = self.__cal_child_dish_fee(product) * product.quantity
        sc_fee, child_sc_fee = self.__cal_product_supply_condiments_fee(product, sub=True)
        attr_fee, child_attr_fee = self.__cal_product_attrs_fee(product, sub=True)
        attr_fee *= product.quantity
        child_attr_fee *= product.quantity
        take_away_fee, child_take_away_fee = self._get_take_away_fee(product, sub=True)
        take_away_fee *= product.quantity
        child_take_away_fee *= product.quantity

        # 饭票 or 零钱
        if (
            self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY
            or self.transaction.pay_method == wallet_pb.Transaction.WALLET
        ):
            enable_discount_fee = (
                product.total_fee + sc_fee + child_sc_fee + child_attr_fee + child_take_away_fee + child_dish_fee
            )

        # 菜品分类折扣
        elif product.price != product.discount_price:
            enable_discount_fee = product.total_fee - attr_fee - take_away_fee
        else:
            enable_discount_fee = 0

        self._enable_discount_fee += int(enable_discount_fee)

    def __cal_child_dish_fee(self, product, order=None):
        """子菜加价单价"""
        fee = 0
        schema = self._get_product_schema(order=order)
        sub_product = schema.get(product.uuid, [])
        if len(sub_product) > 1:
            for sub in sub_product:
                if sub.parent_uuid and sub.child_dish_price > 0:
                    fee += sub.child_dish_price
        return round(fee)

    def __cal_product_supply_condiments_fee(self, product, sub=True, order=None):
        """计算product的加料总金额
        :product: 已经rebuild之后product
        """
        fee = 0
        child_fee = 0
        for sc in product.supply_condiments:
            fee += sc.market_price * sc.quantity

        if sub:
            schema = self._get_product_schema(order=order)
            sub_product = schema.get(product.uuid, [])
            if len(sub_product) > 1:
                for sub in sub_product:
                    for sc in sub.supply_condiments:
                        sc_fee = sc.market_price * sc.quantity
                        child_fee += sc_fee
        return round(fee), round(child_fee)

    def __cal_product_attrs_fee(self, product, sub=True, order=None):
        fee = 0
        child_fee = 0
        for attr in product.attrs:
            if attr.type != dish_pb.Attr.AttrType.TAKE_AWAY:
                fee += attr.reprice
        if sub:
            schema = self._get_product_schema(order=order)
            sub_product = schema.get(product.uuid, [])
            if len(sub_product) > 1:
                for sub in sub_product:
                    for attr in sub.attrs:
                        if attr.type != dish_pb.Attr.AttrType.TAKE_AWAY:
                            child_fee += attr.reprice
        return fee, child_fee

    def _get_take_away_fee(self, product, sub=True, order=None):
        fee = 0
        child_fee = 0
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.AttrType.TAKE_AWAY:
                fee += attr.reprice
        if sub:
            schema = self._get_product_schema(order=order)
            sub_product = schema.get(product.uuid, [])
            if len(sub_product) > 1:
                for sub in sub_product:
                    for attr in sub.attrs:
                        if attr.type == dish_pb.Attr.AttrType.TAKE_AWAY:
                            child_fee += attr.reprice
        return int(fee), int(child_fee)

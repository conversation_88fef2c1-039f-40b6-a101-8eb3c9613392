# -*- coding: utf-8 -*-


"""
Filename: keruyun_fast_food_manager.py
Date: 2020-07-14 16:14:50
Title: 客如云快餐
"""

import logging
import time
import requests

from google.protobuf import json_format

import proto.ordering.keruyun.order_pb2 as order_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.feie_printer_manager import FeiePrinterManager
from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.keruyun_order_manager import KeruyunOrderManager
from common.utils import date_utils
from common.utils import distribute_lock
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
from service import error_codes


logger = logging.getLogger(__name__)


class KeruyunFastFoodManager(KeruyunOrderManager):

    def __init__(self, *args, order_manager=None, **kargs):
        self._manager = order_manager
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>anager, self).__init__(*args, **kargs)
        self.printer_manager = PrinterManager(merchant=self._manager.merchant)
        self.feie_printer_manager = FeiePrinterManager(
            merchant=self._manager.merchant, registration_info=self._manager.registration_info)
        self.init_order()

    def init_order(self):
        ordering_service_da = OrderingServiceDataAccessHelper()
        self.keruyun_order = None
        if hasattr(self._manager, "order"):
            order = self._manager.order
            if order is not None:
                self.keruyun_order = ordering_service_da.get_keruyun_fast_food_order(tp_order_id=order.id)
                self.order = order
        if hasattr(self._manager.business_obj, "order"):
            order = self._manager.business_obj.order
            if order is not None:
                self.keruyun_order = ordering_service_da.get_keruyun_fast_food_order(tp_order_id=order.id)
                self.order = order

    def create_ordering_order(self, order):
        ordering_da = OrderingServiceDataAccessHelper()
        self.keruyun_order = order_pb.FastFoodOrder()
        self.order = order
        keruyun_pos_info = self._manager.registration_info.keruyun_pos_info

        self.keruyun_order.create_time = int(time.time())
        self.keruyun_order.tp_order_id = order.id
        self.keruyun_order.shop_identy = keruyun_pos_info.shop_id
        self.keruyun_order.shop_name = keruyun_pos_info.shop_name
        self.keruyun_order.people_count = order.people_count
        self.keruyun_order.total_price = order.bill_fee
        self.keruyun_order.status = 2
        self.keruyun_order.print = 1
        if self._manager.registration_info.printer_config.keruyun_printer.disable_fast_food_print:
            self.keruyun_order.print = 0

        # 桌台信息
        self.fill_table_info()

        # 商品信息
        self.fill_products_info()
        ordering_da.add_or_update_keruyun_fast_food_order(self.keruyun_order)

    def fill_table_info(self):
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(ordering_service_table_id=self.order.table_id)
        table_info = self.keruyun_order.tables.add()
        table_info.table_id = int(table.ordering_service_table_id)
        table_info.table_name = table.name

    def fill_discount_details(self):
        if self.order.discount_amount == 0:
            return
        discount_detail = self.keruyun_order.discount_details.add()
        discount_detail.discount_type = 3
        discount_detail.discount_fee = self.keruyun_order.payment.total_discount_fee

    def fill_snack_payment(self):
        self.keruyun_order.payment.total_fee = self._manager.business_obj.transaction.bill_fee
        self.keruyun_order.payment.shop_fee = int(self._manager._calculate_paid_in_fee(self._manager.business_obj))
        self.keruyun_order.payment.user_fee = self.keruyun_order.payment.shop_fee
        self.keruyun_order.payment.pay_type = 3

        msg = f"""
        客如云模式快餐: {self._manager.business_obj.order.id}
        订单总金额: {self._manager.business_obj.order.bill_fee}
        订单门店总金额: {self.keruyun_order.payment.shop_fee}
        """
        logger.info(msg)

        # TODO: 因为python3 round函数的四舍五入与众不同,有可能会造成不一致,在此做特殊处理
        discount_fee = self._manager.business_obj.order.bill_fee - self.keruyun_order.payment.shop_fee
        if abs(discount_fee) == 1:
            discount_fee = 0
            self.keruyun_order.payment.shop_fee = self._manager.business_obj.order.bill_fee
            self.keruyun_order.payment.user_fee = self.keruyun_order.payment.shop_fee

        self.keruyun_order.payment.total_discount_fee = discount_fee
        self.keruyun_order.payment.shop_discount_fee = discount_fee

    def fill_products_info(self):
        for product in self.order.products:
            self.add_product(product)

    def add_product(self, shilai_product):
        product = self.keruyun_order.products.add()
        product.tp_id = shilai_product.id
        product.id = int(shilai_product.id)
        product.parent_uuid = shilai_product.parent_uuid
        product.uuid = shilai_product.uuid
        product.type = shilai_product.type
        product.name = shilai_product.name
        product.unit = shilai_product.unit
        product.price = shilai_product.price
        product.total_fee = shilai_product.total_fee
        product.quantity = shilai_product.quantity

        self.set_attrs(shilai_product, product)
        self.set_supply_condiments(shilai_product, product)

    def set_attrs(self, shilai_product, product):
        for shilai_attr in shilai_product.attrs:
            attr = product.properties.add()
            attr.name = shilai_attr.name
            attr.id = int(shilai_attr.id)
            attr.type = shilai_attr.type
            attr.reprice = shilai_attr.reprice

    def set_supply_condiments(self, shilai_product, parent_product):
        for supply_condiment in shilai_product.supply_condiments:
            product = self.keruyun_order.products.add()
            product.name = supply_condiment.name
            product.price = supply_condiment.market_price
            product.quantity = supply_condiment.quantity
            product.parent_uuid = parent_product.uuid
            product.total_fee = int(product.price * product.quantity)
            product.uuid = id_manager.generate_common_id()
            product.type = 2

    def __create_order(self):
        order = self._manager.business_obj.order

        self.fill_snack_payment()
        # 优惠信息
        self.fill_discount_details()
        self.keruyun_order.remark = self.printer_manager.build_order_remark(order, newline="\n")
        json_body = json_format.MessageToDict(self.keruyun_order, including_default_value_fields=True)
        resp = self.__try_create_order(json_body, order)
        ordering_da = OrderingServiceDataAccessHelper()
        order.ordering_service_order_id = resp.get("result").get("orderId")
        order.ordering_service_trade_id = resp.get("result").get("tradeId")
        order.ordering_service_serial_number = resp.get("result").get("serialNumber")
        order.status = dish_pb.DishOrder.APPROVED
        order.approve_time = date_utils.timestamp_second()
        table = ordering_da.get_table(ordering_service_table_id=str(order.table_id))
        self.feie_printer_manager.kitchen_print_create_order(order, table)

    def __try_create_order(self, json_body, order, try_times=3):
        if try_times == 0:
            raise errors.KeruyunCreateOrderError()
        # 向客如云发起订单
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_FAST_FOOD_ORDER
        logger.info("向客如云发起创建订单: {}".format(json_body))
        try:
            resp = self.post(token=token, uri=uri, json_body=json_body)
            resp = resp.json()
        except requests.exception.ConnectTimeout:
            raise errors.KeruyunCreateOrderTimeout(error_codes.KERUYUN_CREATE_ORDER_TIMEOUT)
        logger.info("客如云创建订单接口返回: {}, {}".format(resp, order.id))
        if resp.get("code") == 0:
            return resp
        else:
            logger.info('创建客如云订单失败,重试: {}, resp.code: {}'.format(order.id, resp.get('code')))
            time.sleep(1)
            return self.__try_create_order(json_body, order, try_times=try_times - 1)
        return False

    def pay_order(self, **kargs):
        order = self._manager.business_obj.order
        with distribute_lock.redislock(key=order.table_id, ttl=5000, retry_count=50, retry_delay=200) as lock:
            if not lock:
                raise errors.KeruyunPayOrderError()
            return self.__create_order()
        logger.info('不能获取桌台锁: tableID: {}, order_id{}'.format(order.table_id, order.id))
        raise errors.KeruyunPayOrderError()

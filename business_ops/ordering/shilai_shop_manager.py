# -*- coding: utf-8 -*-

import logging

import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.ordering.shilai_ops_manager import Shi<PERSON>OPSManager
from dao.payment_da_helper import PaymentDataAccessHelper

logger = logging.getLogger(__name__)


class ShilaiShopManager:
    def __init__(self, shop_manager=None, *args, **kargs):
        self._manager = shop_manager
        self._ops = ShilaiOPSManager(merchant=shop_manager.merchant, store=shop_manager.store)

    def register(self, registration_info=None, *args, **kargs):
        if not registration_info:
            registration_info = registration_pb.OrderingServiceRegistrationInfo()
            registration_info.merchant_id = self._manager.merchant.id
            registration_info.package_type = registration_pb.OrderingServiceRegistrationInfo.PackageType.TAKE_AWAY
        self.add_or_update_merchant()
        self.add_or_update_store()
        return registration_info

    def add_or_update_merchant(self):
        uri = ShilaiPosConstants.ADD_OR_UPDATE_MERCHANT
        url = self._ops.generate_url(uri)
        json_data = {
            "brandName": self._manager.merchant.basic_info.name,
            "name": self._manager.merchant.basic_info.name,
            "migration": True,
        }
        json_data.update(self._ops.generate_base_params())
        resp = self._ops.do_post(url, json_data)
        logger.info(f"url={url}, json={json_data}, response={resp}")

    def add_or_update_store(self, registration_info=None):
        uri = ShilaiPosConstants.ADD_OR_UPDATE_STORE
        url = self._ops.generate_url(uri)
        json_data = {"name": self._manager.merchant.basic_info.name, "storeId": self._manager.store.id}
        if registration_info:
            json_data.update({'baseNumber': registration_info.serial_number_base_value})
        json_data.update(self._ops.generate_base_params())
        resp = self._ops.do_post(url, json_data)
        logger.info(f"url={url}, json={json_data}, response={resp}")

    def upload_merchant_info(self, **kargs):
        settlement_rate = self._manager.merchant.payment_info.settlement_rate
        uri = ShilaiPosConstants.ADD_OR_UPDATE_MERCHANT
        url = self._ops.generate_url(uri)
        json_data = {
            "settlementRate": settlement_rate,
            "shilaiServiceRate": self._manager.merchant.payment_info.shilai_service_rate,
        }
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        logger.info("update_settlement_rate: {}".format(ret))

        payment_da = PaymentDataAccessHelper()
        tian_que_pay_info = payment_da.get_tian_que_pay_info(merchant_id=self._manager.merchant.id)
        if tian_que_pay_info:
            uri = ShilaiPosConstants.UPDATE_TIAN_QUE_PAY_INFO
            url = self._ops.generate_payment_url(uri)
            json_data = {
                "merchantId": self._manager.merchant.id,
                "storeId": self._manager.store.id,
                "nextMno": tian_que_pay_info.next_mno,
                "mno": tian_que_pay_info.mno,
                "childNo": tian_que_pay_info.child_no,
            }
            ret = self._ops.do_post(url, json_data)
            logger.info("update tian_que_pay_info: {}".format(ret))

    def get_store_setting(self):
        uri = ShilaiPosConstants.GET_STORE_SETTING
        url = self._ops.generate_url(uri)
        json_data = {}
        json_data.update(self._ops.generate_base_params())
        ret = self._ops.do_post(url, json_data)
        ret = ret.get("data", {})
        return ret

# -*- coding: utf-8 -*-

"""
Filename: keruyun_member_manager.py
Date: 2020-06-17 15:17:45
Title: 客如去会员系统
"""

import logging

from google.protobuf import json_format

import proto.ordering.keruyun.member_pb2 as keruyun_member_pb
import proto.page.keruyun_member_pb2 as page_keruyun_member_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.payment_manager import PaymentManager
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from business_ops.ordering.constants import KeruyunConstants
from business_ops.transaction_manager import TransactionManager
from business_ops.business_manager import BusinessManager
from common.utils import date_utils
from dao.user_da_helper import UserDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class KeruyunMemberManager(KeruyunOPSManager, BusinessManager):
    def __init__(self, registration_info, pay_method=None, transaction_id=None, user_id=None, merchant_id=None, *args, **kargs):
        self._manager = self
        self.paid_fee = kargs.get("paid_fee", 0)
        self.bill_fee = kargs.get("bill_fee", 0)

        super(KeruyunMemberManager, self).__init__(pay_method=pay_method, transaction_id=transaction_id, user_id=user_id, merchant_id=merchant_id)

        self.registration_info = registration_info
        self.token = self.registration_info.keruyun_pos_info.token

    def login(self, phone):
        """ 会员登陆
        """
        json_body = {
            "loginId": phone,
            "loginType": 0
        }
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_MEMBER_LOGIN, json_body=json_body).json()
        logger.info("客如云会员登陆返回: {}".format(resp))
        if resp.get("code") == 0:
            member = json_format.ParseDict(resp.get("result"), keruyun_member_pb.CRMUserBaseInfoVO(), ignore_unknown_fields=True)
            return member
        return None

    def get_customer_by_phone(self, phone):
        customer = self.login(phone)
        return customer

    def get_customer_by_user_id(self, user_id):
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.UserNotFound()
        phone = user.member_profile.mobile_phone
        customer = self.login(phone)
        return customer

    def auto_login(self, phone):
        """ 获取免登陆token
        """
        json_body = {
            "phone": phone
        }
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_AUTOLOGIN, json_body=json_body).json()
        logger.info("客如云autoLogin返回: {}".format(resp))
        if resp.get("code") == 0:
            return resp.get("result").get("token")
        return None

    def create_customer(self, user_id, merchant_id):
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.UserNotFound()
        customer = keruyun_member_pb.CreateCustomerInfo()

        # 优先使用手机注册
        customer.login_id = user.member_profile.mobile_phone
        customer.login_type = '0'
        if not customer.login_id:
            # 没有用户手机号则使用用户微信注册
            customer.login_id = user.wechat_profile.openid
            customer.login_type = '1'
            customer.wx_icon_url = user.wechat_profile.headimgurl
        if not customer.login_id:
            raise errors.CannotCreateKeruyunCustomer()
        birth_year = user.member_profile.birth_year
        birth_month = user.member_profile.birth_month
        birth_day = user.member_profile.birth_day
        birthday = "{}-{}-{}".format(birth_year, birth_month, birth_day)
        birthday_timestamp = date_utils.string_to_timestamp(birthday, fmt="%Y-%m-%d")
        customer.birthday = int(birthday_timestamp * 1000)
        customer.name = user.member_profile.name
        customer.sex = -1
        if user.member_profile.sex == "男":
            customer.sex = 1
        elif user.member_profile.sex == "女":
            customer.sex = 0
        json_body = json_format.MessageToDict(customer, including_default_value_fields=False)
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_CREATE_CUSTOMER, json_body=json_body).json()
        logger.info("创建客如云会员返回: {}".format(resp))
        if resp.get("code") == 0:
            return customer
        return None

    def get_customer_info(self, user_id):
        """ 获取会员详情
        """
        customer = self.get_customer_by_user_id(user_id)
        if not customer:
            return None
        customer_id = customer.customer_id
        json_body = {
            "customerId": customer_id
        }
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_GET_CUSTOMER_DETAIL_BY_ID, json_body=json_body).json()
        logger.info("获取客如云会员详情返回: {}".format(resp))
        if resp.get("code") == 0:
            result = resp.get("result")
            customer_info = json_format.ParseDict(result, keruyun_member_pb.CustomerDetailInfo(), ignore_unknown_fields=True)
            return customer_info
        return None

    def get_customer_info_vo(self, user_id):
        customer_info = self.get_customer_info(user_id)
        if customer_info:
            customer_info_vo = page_keruyun_member_pb.CustomerInfoVO()
            customer_info_vo.user_id = user_id
            customer_info_vo.remain_value = int(customer_info.remain_value * 100)
            return customer_info_vo
        return None

    def get_customer_info_by_phone(self, phone):
        customer = self.get_customer_by_phone(phone)
        if not customer:
            return None
        customer_id = customer.customer_id
        json_body = {
            "customerId": customer_id
        }
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_GET_CUSTOMER_DETAIL_BY_ID, json_body=json_body).json()
        logger.info("获取客如云会员详情返回: {}".format(resp))
        if resp.get("code") == 0:
            result = resp.get("result")
            customer_info = json_format.ParseDict(result, keruyun_member_pb.CustomerDetailInfo(), ignore_unknown_fields=True)
            return customer_info
        return None

    def get_customer_info_vo_by_phone(self, phone):
        customer_info = self.get_customer_info_by_phone(phone)
        if customer_info:
            customer_info_vo = page_keruyun_member_pb.CustomerInfoVO()
            customer_info_vo.remain_value = int(customer_info.remain_value * 100)
            return customer_info_vo
        return None

    def point_add(self, user_id, points):
        customer = self.get_customer_by_user_id(user_id)
        if not customer:
            logger.info("客如云会员积分增加,会员不存在: {}".format(user_id))
            return None
        json_body = {
            "customerId": customer.customer_id,
            "points": points,
            "remark": "时来客如云积分增加",
            "businessType": 4  # 积分补录
        }
        logger.info("客如云积分增加接口请求参数: {}".format(json_body))
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_POINT_ADD, json_body=json_body).json()
        logger.info("客如云会员积分增加接口返回: {}".format(resp))
        if resp.get("code") == 0:
            return True
        return False

    def point_cut(self, user_id, points):
        customer = self.get_customer_by_user_id(user_id)
        if not customer:
            logger.info("客如云会员积分减少,会员不存在: {}".format(user_id))
            return None
        json_body = {
            "customerId": customer.customer_id,
            "points": points,
            "remark": "时来客如云积分扣减",
            "businessType": 4  # 积分扣除
        }
        logger.info("客如云积分扣除接口请求参数: {}".format(json_body))
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_POINT_CUT, json_body=json_body).json()
        logger.info("客如云会员积分扣除接口返回: {}".format(resp))
        if resp.get("code") == 0:
            return True
        return False

    def notification(self):
        customer = self.get_customer_by_user_id(self.transaction.payer_id)
        if not customer:
            logger.info("客如云会员充值接口,通过userId: {} 获取不到客如云会员信息".format(self.transaction.payer_id))
            raise errors.UserNotFound()
        json_body = {
            "customerId": customer.customer_id,
            "cardType": 2,  # 1: 虚拟账户 2: 会员卡 3: 匿名卡 4: 权益卡
            # "cardNum": "", # 匿名卡,权益卡必填  虚拟账户和会员卡不填
            "businessType": 1,  # 1:储值
            "amount": self.transaction.paid_fee,  # 充值金额,单位分
            "tpOrderId": self.transaction.id,
            "remark": "会员卡充值"
        }
        logger.info("客如云会员充值请求参数: {}".format(json_body))
        resp = self.post(token=self.token, uri=KeruyunConstants.KERUYUN_RECHARGE, json_body=json_body).json()
        logger.info("客如云会员充值返回参数: {}".format(resp))
        if resp.get("code") == 0:
            result = resp.get("result")
            logger.info("充值成功,账户余额: {}".format(result.get("balance")))
            return True
        return False

    def prepay(self):
        self.transaction = TransactionManager().get_keruyun_member_card_recharge_transaction(
            self.user_id, self.merchant_id, self.pay_method, self.bill_fee, self.paid_fee)
        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction)
            logger.info("客如云会员充值prepay success: {}".format(result))
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") == error_codes.SUCCESS:
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
        result["transactionId"] = self.transaction.id
        return result

# -*- coding: utf-8 -*-


import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.hualala_ops import HualalaOPSManager
from business_ops.ordering.constants import HualalaConstants


class HualalaShopManager(HualalaOPSManager):
    def __init__(self, shop_manager=None, *args, **kargs):
        self._manager = shop_manager
        super(HualalaShopManager, self).__init__(*args, **kargs)

    def get_group_info(self, group_id=None):
        if group_id is None:
            params = {
                "groupID": self.group_id
            }
            self.header.update({"groupID": self.group_id})
        else:
            params = {
                "groupID": group_id
            }
            params.update(self.must_params)
            self.header.update({"groupID": group_id})
        route = HualalaConstants.GET_GROUP_INFO
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data
        return None

    def get_shop_info(self, group_id, shop_id):
        params = {
            "groupID": group_id,
            "shopID": shop_id
        }
        self.header.update({
            "groupID": group_id
        })
        route = HualalaConstants.GET_SHOP_INFO
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data.get("shopBaseInfo")
        return None

    def register(self, group_id, shop_id, app_secret, app_key, registration_info=None, *args, **kargs):
        if not registration_info:
            registration_info = registration_pb.OrderingServiceRegistrationInfo()
            registration_info.merchant_id = self._manager.merchant.id
        shop_info = self.get_shop_info(group_id, shop_id)
        group_info = self.get_group_info(group_id)
        registration_info.hualala_pos_info.group_id = group_id
        registration_info.hualala_pos_info.shop_id = shop_id
        registration_info.hualala_pos_info.app_secret = HualalaConstants.APP_SECRET
        registration_info.hualala_pos_info.app_key = HualalaConstants.APP_KEY
        registration_info.hualala_pos_info.status = int(shop_info.get("status"))
        registration_info.hualala_pos_info.operation_mode = int(group_info.get("operationModel"))
        registration_info.hualala_pos_info.is_active = int(group_info.get("isActive"))
        registration_info.hualala_pos_info.is_online = int(group_info.get("isOnline"))
        registration_info.hualala_pos_info.group_login_name = group_info.get("groupLoginName")
        registration_info.hualala_pos_info.group_login_pwd = group_info.get("groupLoginPWD")
        registration_info.ordering_config.enable_eat_in = True
        self.set_pay_subject(registration_info)
        return registration_info

    def set_pay_subject(self, registration_info):
        group_id = registration_info.hualala_pos_info.group_id
        shop_id = registration_info.hualala_pos_info.shop_id
        pay_subjects = self.get_pay_subject(group_id=group_id, shop_id=shop_id)
        for pay_subject in pay_subjects:
            subject_code = pay_subject.get("subjectCode")
            subject_name = pay_subject.get("subjectName")
            if subject_name == "时来扫码点餐":
                registration_info.hualala_pos_info.payment_subject_id = subject_code
                registration_info.hualala_pos_info.payment_subject_name = subject_name
            if subject_name == "账单折扣":
                registration_info.hualala_pos_info.discount_payment_subject_id = subject_code
                registration_info.hualala_pos_info.discount_payment_subject_name = subject_name

    def get_pay_subject(self, group_id, shop_id):
        params = {
            "groupID": group_id,
            "shopID": shop_id
        }
        route = HualalaConstants.GET_PAY_SUBJECT
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data.get("paySubjectList")
        return None

    def get_group_pay_subject(self, group_id):
        params = {
            "groupID": group_id
        }
        route = HualalaConstants.GET_GROUP_PAY_SUBJECT
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data
        return None

# -*- coding: utf-8 -*-
import json
import time
import hashlib
import logging
import requests
import re
from datetime import datetime

import pytz

import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.constants import KitchenPrinter, XPRINTER
from business_ops.base_manager import BaseManager
from dao.user_da_helper import UserDataAccessHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper

logger = logging.getLogger(__name__)


class PrinterManager(BaseManager):

    __chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】《》]")

    def __init__(self, *args, **kargs):
        super(PrinterManager, self).__init__(*args, **kargs)
        self.printer_config = {}
        if self.merchant:
            printer_config_da = PrinterConfigDataAccessHelper()
            printer_configs = printer_config_da.get_printer_configs(merchant_id=self.merchant.id)
            self.printer_config = {c.printer_sn: c for c in printer_configs}

        self.bold_start_flag = "<BBB>"
        self.bold_end_flag = "</BBB>"

        self.goods_title_length = self.registration_info.printer_config.feie_printer.goods_title_length or 14
        self.quantity_title_length = self.registration_info.printer_config.feie_printer.quantity_title_length or 5
        self.price_title_length = self.registration_info.printer_config.feie_printer.price_title_length or 8
        self.goods_name_length = self.registration_info.printer_config.feie_printer.goods_name_length or 14
        self.goods_quantity_length = self.registration_info.printer_config.feie_printer.goods_quantity_length or 5
        self.goods_price_length = self.registration_info.printer_config.feie_printer.goods_price_length or 4
        self.attr_name_length = self.registration_info.printer_config.feie_printer.attr_name_length or 17
        self.attr_price_length = self.registration_info.printer_config.feie_printer.attr_price_length or 4
        self.supply_condiment_name_length = self.registration_info.printer_config.feie_printer.supply_condiment_name_length or 12
        self.supply_condiment_quantity_length = self.registration_info.printer_config.feie_printer.supply_condiment_quantity_length or 5
        self.supply_condiment_price_length = self.registration_info.printer_config.feie_printer.supply_condiment_price_length or 4
        self.guest_font_ratio = self.registration_info.printer_config.feie_printer.guest_font_ratio or 1
        self.printer_specification = self.registration_info.printer_config.feie_printer.printer_specification or "58mm"
        self.disable_kitchen_price_print = self.registration_info.printer_config.feie_printer.disable_kitchen_price_print
        if self.printer_specification == "80mm":
            self.guest_font_ratio = 2

    def build_order_remark(self, order, newline="<BR>"):
        remark = order.remark
        if order.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            if order.appointment_time:
                shanghai = pytz.timezone("Asia/Shanghai")
                appointment_time = datetime.fromtimestamp(order.appointment_time).astimezone(shanghai)
                appointment_time = appointment_time.strftime("%Y-%m-%d %H:%M")
                remark += '{} - 预约时间: {}'.format(newline, appointment_time)
            remark += '{} - 取餐码: {}'.format(newline, order.meal_code)
            remark += '{} - 用户手机号: {}'.format(newline, order.phone)
        elif order.meal_type in [
                dish_pb.DishOrder.TAKE_AWAY,
                dish_pb.DishOrder.KERUYUN_FAST_FOOD_TAKE_AWAY
        ]:
            remark += '{} - 取餐码: {}'.format(newline, order.meal_code)
            user = UserDataAccessHelper().get_user(order.user_id)
            if user.member_profile.mobile_phone:
                remark += '{} - 用户手机号: {}'.format(newline, user.member_profile.mobile_phone)
        elif order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            shipping_address = order.shipping_address
            remark += '{} - 送货地址:'.format(newline)
            remark += '{}{}({})'.format(newline, shipping_address.username, shipping_address.gender)
            remark += '{}{}'.format(newline, shipping_address.mobile_phone)
            remark += '{}{}{}'.format(newline, shipping_address.street, shipping_address.house_number)
        return remark

    def _get_table_info(self, table, header, order=None, enable_serial_number=False, enable_meal_code=True, bold_kitchen_serial_number=False):
        table_area = table.table_area.area_name
        name = table.name
        if table_area:
            name = "{}-{}<BR>".format(table_area, name)
        content = "-----{}-----------------<BR>".format(header)
        content += "<B>桌号:{}</B><BR>".format(name)
        if enable_meal_code and order is not None and order.meal_code != "":
            content += "<B>取餐码: {}   </B><BR>".format(order.meal_code)
        if order and order.serial_number:
            serial_number = order.serial_number
            if order.ordering_service_serial_number:
                serial_number = order.ordering_service_serial_number
            if not bold_kitchen_serial_number:
                content += "流水号: {}   <BR>".format(serial_number)
            else:
                content += "{}流水号: {}   {}<BR>".format(self.bold_start_flag, serial_number, self.bold_end_flag)
        if order and order.remark != "":
            content += "<B>整单备注: {}</B><BR>".format(order.remark)
        content += "--------------------------------<BR>"
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content += "{}<BR>".format(now)
        content += "--------------------------------<BR>"
        return content

    def _build_product_content(self, products, all_category=None):
        content = ""
        if self.registration_info.printer_config.feie_printer.enable_dish_sort:
            new_products = []
            for cate in all_category:
                for product in products:
                    if cate.id == product.category_id:
                        new_products.append(product)
            products = new_products
        for index, product in enumerate(products):
            name = product.name
            quantity = int(product.quantity)
            price = "{:.2f}".format(float(product.price) / 100)
            align_price = self.content_align(price, length=self.goods_price_length)
            index_name = "{}.{}".format(index + 1, name)
            align_name = self.content_align(index_name, length=self.goods_name_length)
            align_quantity = self.content_align("{}".format(quantity), length=self.goods_quantity_length)
            content += "{}{}{}{}{}<BR>".format(self.bold_start_flag, align_name, align_quantity, align_price, self.bold_end_flag)
            for attr in product.attrs:
                align_name = self.content_align(attr.name, length=self.attr_name_length)
                attr_price = "{:.2f}".format(float(attr.reprice) / 100)
                align_price = self.content_align(attr_price, length=self.attr_price_length)
                content += "{}{:2s}{}{}{}<BR>".format(self.bold_start_flag, "", align_name, align_price, self.bold_end_flag)
            for supply_condiment in product.supply_condiments:
                align_name = self.content_align(supply_condiment.name, length=self.supply_condiment_name_length)
                quantity = self.content_align(str(supply_condiment.quantity), length=self.supply_condiment_quantity_length)
                price = "{:.2f}".format(float(supply_condiment.market_price) / 100)
                align_price = self.content_align(price, self.supply_condiment_price_length)
                content += "{}{:2s}{}{}{}{}<BR>".format(self.bold_start_flag, "", align_name, quantity, align_price, self.bold_end_flag)
        return content

    def add_feie_printer(self, sn, key, remark):
        STIME = str(int(time.time()))
        signature = hashlib.sha1(str(KitchenPrinter.USER + KitchenPrinter.UKEY + STIME).encode('utf-8')).hexdigest()
        params = {
            "printerContent": "{}#{}#{}".format(sn, key, remark),
            'user': KitchenPrinter.USER,
            'sig': signature,
            'stime': STIME,
            'apiname': 'Open_printerAddlist'
        }
        ret = requests.post(KitchenPrinter.URL, data=params, timeout=3)
        logger.info("添加飞鹅打印机返回: {}".format(ret.json()))
        return ret.json()

    def add_xprinter(self, sn, remark):
        STIME = str(int(time.time()))
        signature = hashlib.sha1(str(XPRINTER.USER + XPRINTER.UKEY + STIME).encode('utf-8')).hexdigest()
        params = {
            "items": [{"sn":sn, "name": remark}],
            'user': XPRINTER.USER,
            'sign': signature,
            'timestamp': STIME,
        }
        ret = requests.post(XPRINTER.URL + 'addPrinters', data=json.dumps(params), timeout=3, headers={"Content-Type": "application/json;charset=UTF-8"})
        logger.info("添加芯烨打印机返回: {}".format(ret.json()))
        return ret.json()

    def entirety_bold_content(self, printer_sn, content):
        config = self.printer_config.get(printer_sn)
        if not config:
            content = content.replace(self.bold_start_flag, "")
            content = content.replace(self.bold_end_flag, "")
        else:
            if config.entirety_bold:
                content = content.replace(self.bold_start_flag, "<B>")
                content = content.replace(self.bold_end_flag, "</B>")
            else:
                content = content.replace(self.bold_start_flag, "")
                content = content.replace(self.bold_end_flag, "")
        return content

    def word_count(self, string):
        """ 计算字符的总个数
        """
        string = str(string)
        cn_count = self.chinese_count(string)
        total_count = len(string)
        en_count = total_count - cn_count
        return cn_count, en_count

    def chinese_count(self, string):
        """ 计算中文字符的个数
        """
        string = str(string)
        chinese_array = self.__chinese_re_pattern.findall(string)
        return len(chinese_array)

    def content_align(self, string, length=0, addin=' ', recursive=True, newline="<BR>"):
        cn_count, en_count = self.word_count(string)
        bytes_len = en_count + 2 * cn_count
        diff_length = length - bytes_len
        if diff_length == 0:
            return string
        if diff_length <= 0:
            if not recursive:
                return string
            strlen = len(string)
            split_index = int(strlen / 5 * 4 + 1)
            s0 = string[0:split_index]
            s1 = self.content_align(string[split_index:], length=length, recursive=False)
            s = "{}{}{}".format(s0, newline, s1)
            logger.info("too long: {}".format(s))
            return s
        else:
            s = string + addin * diff_length
            return s

# -*- coding: utf-8 -*-


import logging

from business_ops.ordering.base_order_manager import BaseOrderManager
from service.errors import ShowError

logger = logging.getLogger(__name__)


class NoneExistsOrderManager(BaseOrderManager):

    whitelist_merchant_id = set([
        "d3348a6659ac401486d53753ea58e8d2",
        "1e543376139b474e97d38d487fa9fbe8"
    ])

    def __init__(self, order_manager=None, *args, **kargs):
        self._manager = order_manager
        if self._manager.merchant.id not in self.whitelist_merchant_id:
            raise ShowError("POS type 错误")
        super(NoneExistsOrderManager, self).__init__(order_manager=self._manager, *args, **kargs)

    def create_ordering_order(self, *args, **kargs):
        return True

    def pay_order(self, *args, **kargs):
        return True

    def add_dish_ordering_order(self, *args, **kargs):
        return True

    def pos_order_bell(self, table, source, content, order=None):
        return True

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        return True

    def create_pos_order(self, table_id, dishes, people_count):
        return True

    def create_zero_order(self, order):
        return True

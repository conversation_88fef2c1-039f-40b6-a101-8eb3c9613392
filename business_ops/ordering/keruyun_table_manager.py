# -*- coding: utf-8 -*-

import logging

import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from business_ops.ordering.base_table_manager import BaseTableManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class KeruyunTableManager(KeruyunOPSManager, BaseTableManager):

    def __init__(self, *args, table_manager=None, **kargs):
        self._manager = table_manager
        super(KeruyunTableManager, self).__init__(*args, **kargs)

    def fetch_table(self, table_id):
        json_body = {
            "ids": [table_id]
        }
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_FETCH_TABLES
        ret = self.post(token=token, uri=uri, json_body=json_body).json()
        logger.info("客如云fetch table: {}".format(ret))
        if ret.get("code") == 0:
            return ret.get("result")[0]
        return None

    def check_table_status(self, table_id):
        keruyun_table = self.fetch_table(table_id)
        if keruyun_table:
            if keruyun_table.get("status") == 1:
                raise errors.KeruyunTableNotTakeEffort()
            if keruyun_table.get("status") == -1:
                raise errors.KeruyunTableCannotUsed()
            pay_type = self._manager.registration_info.pay_type
            if pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
                # 如果是先支付模式,就不能是就餐状态
                if keruyun_table.get("tableStatus") == 1:
                    raise errors.KeruyunTableInDining()
            if keruyun_table.get("tableStatus") == 3:
                raise errors.KeruyunTableLocked()
            if keruyun_table.get("tableStatus") == 2:
                if not self.clear_table(table_id=table_id):
                    raise errors.KeruyunClearTableFailed()
            return True
        raise errors.KeruyunTableNotFound()

    def clear_table(self, table_id=None, keruyun_order_id=None, order=None):
        if table_id  is None and keruyun_order_id is None and order is None:
            return False
        if order is not None:
            keruyun_order_id = order.ordering_service_order_id
            table_id = order.table_id
        if keruyun_order_id is None:
            latest_order = OrderingServiceDataAccessHelper().get_latest_keruyun_order(str(table_id))
            if not latest_order:
                return False
            keruyun_order_id = latest_order.ordering_service_order_id
        if not keruyun_order_id:
            return False
        json_body = {
            "operatorName": "工作人员",
            "orderId": keruyun_order_id,
            "tableId": int(table_id)
        }
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_CLEAR_TABLE
        try:
            ret = self.post(token=token, uri=uri, json_body=json_body).json()
        except Exception:
            return False
        logger.info("客如云清台: {}".format(ret))
        if ret.get("code") == 0:
            return True
        if ret.get("code") == 3001 and ret.get("message") == "订单桌台已被其他客户端更新":
            return True
        logger.info("客如云清台失败,找不到最近的客如云订单id")
        return False

    def try_clear_table(self, order):
        try:
            return self.clear_table(order=order)
        except Exception:
            return False

    def get_tables(self):
        """ 同步脚本调用
        同步客如云桌台列表
        """
        json_body = {}
        uri = KeruyunConstants.KERUYUN_FETCH_TABLES
        token = self._manager.registration_info.keruyun_pos_info.token
        if not token:
            token = self.get_token(self._manager.registration_info.keruyun_pos_info.shop_id)
        resp = self.post(token=token, uri=uri, json_body=json_body).json()
        result = []
        if resp.get("code") == 0:
            tables = resp.get("result")
            for table in tables:
                result.append(table)
        return result

    def sync_tables(self):
        tables = self.get_tables()
        if not tables:
            return None
        ret = []
        for table in tables:
            table_obj = {
                "id": id_manager.generate_common_id(),
                "ordering_service_table_id": table.get("tableID"),
                "name": table.get("tableName"),
                "table_person_count": table.get("tablePersonCount"),
                "area_name": table.get("tableArea", {}).get("areaName"),
                "area_code": table.get("tableArea", {}).get("areaCode"),
                "area_id": table.get("tableArea", {}).get("id"),
                "sort": table.get("sort"),
                "can_booking": table.get("canBooking") == 0
            }
            ret.append(table_obj)
        return ret

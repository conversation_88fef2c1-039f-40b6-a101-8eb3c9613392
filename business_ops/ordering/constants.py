import os


class KeruyunConstants:
    BASE_URL_SANDBOX = 'https://gldopenapi.keruyun.com'
    BASE_URL_PROD = 'https://openapi.keruyun.com'
    OAUTH_URL = 'https://open.keruyun.com'

    APP_KEY_SANDBOX = '********************************'
    APP_KEY_PROD = '29a822ecade1a77c19bfd43d7ea70808'
    SECRET_KEY = '44a289525d4229bc2c45d7dbe1c5e2a4'

    BASE_URL = BASE_URL_PROD
    APP_KEY = APP_KEY_PROD

    # SHOP_ID = '810094162' # Sandbox
    SHOP_ID = '810094162'
    TOKEN = '********************************'
    VERSION = '1.0'

    KERUYUN_TOKEN_URI = '/open/v1/token/get'  # 获取商户token
    KERUYUN_DISH_CATEGORY = '/open/v1/cater/dish/category'  # 获取菜品分类,不包括父分类
    KERUYUN_DISH_CATEGORY_ALL = '/open/v1/cater/dish/categoryAll'  # 获取菜品全量分类,包括父分类
    KERUYUN_DISH_MENU = '/open/v1/cater/dish/dishMenu'
    KERUYUN_DISH_DISH_NEW = '/open/v1/cater/dish/dishNew'  # 某类别菜品下的的所有菜品
    KERUYUN_DISH_DISH_MENU_BY_IDS = '/open/v1/cater/dish/dishMenuByIds'  # 根据菜品ID查询菜品
    KERUYUN_CREATE_ORDER = '/open/v1/dinner/order/create'  # 创建订单
    KERUYUN_FETCH_TABLES = '/open/v1/table/fetchTables'  # 获取桌台信息
    KERUYUN_CLEAR_TABLE = '/open/v1/dinner/order/clearTable'  # 清台
    KERUYUN_OPEN_TABLE = '/open/v1/dinner/order/table/open'  # 开台
    KERUYUN_SHOP_DETAILS = '/open/v1/shop/shopdetails'  # 门店详情信息
    KERUYUN_PAY_ORDER = '/open/v1/dinner/order/pay'  # 支付通知
    KERUYUN_APPLY_ADD_DISH = '/open/v1/dinner/order/applyAddDish'  # 加菜
    KERUYUN_ORDER_EXPORT = '/open/v1/data/order/export2'  # 订单列表查询
    KERUYUN_ORDER_EXPORT_DETAIL = '/open/v1/data/order/exportDetail'  # 订单详情查询
    KERUYUN_ORDER_DETAIL = '/open/v1/dinner/order/detail'  # 查询订单详情
    KERUYUN_FAST_FOOD_ORDER = '/open/v1/snack/order/create'  # 创建快餐
    KERUYUN_DISCOUNT_PAY = '/open/v1/dinner/order/discountPay'  # 优惠支付
    KERUYUN_CHANGE_TABLE = '/open/v1/dinner/order/changeTable'  # 换桌台

    KERUYUN_MEMBER_LOGIN = '/open/v1/crm/login'  # 会员登陆
    KERUYUN_RECHARGE = '/open/v1/crm/member/recharge'  # 会员充值
    KERUYUN_AUTOLOGIN = '/open/v1/crm/autoLogin'  # 获取免登陆token
    KERUYUN_CREATE_CUSTOMER = '/open/v1/crm/createCustomer'  # 创建会员
    KERUYUN_GET_CUSTOMER_DETAIL_BY_ID = '/open/v1/crm/getCustomerDetailById'  # 查询会员详情
    KERUYUN_CREATE_OR_UPDATE_MEMBER = "/open/v1/crm/createOrUpgradeMember"  # 创建或升级会员
    KERUYUN_POINT_ADD = "/open/v1/crm/point/add"  # 客如云会员积分增加
    KERUYUN_POINT_CUT = "/open/v1/crm/point/cut"  # 客如云积分扣减
    KERUYUN_TAKE_OUT_CREATE = "/open/v1/takeout/order/create"  # 外卖下单
    KERUYUN_TAKE_OUT_ORDER_GET = "/open/v1/takeout/order/status/get"  # 外卖订单状态查询
    KERUYUN_USER_CANCEL_ORDER = "/open/v1/takeout/order/cancel"  # 用户取消订单
    KERUYUN_GET_BATCHIDS = "/open/v1/dinner/order/getBatchIds"  # 加菜id列表
    KERUYUN_GET_BATCH_DETAIL = "/open/v1/dinner/order/batchDetail"  # 加菜详情
    KERUYUN_BELL = "/open/v1/dinner/order/bell"  # pos机服务铃
    KERUYUN_SURCHARGE = "/open/v1/shop/surcharge/query"  # 附加费


class HualalaConstants:
    BASE_URL = "https://www-openapi.hualala.com"
    APP_KEY = 1789
    APP_SECRET = "yw0KOZez"
    GROUP_ID = 293668
    GET_GROUP_INFO = "doc/getAllShop"  # 获取店铺列表
    GET_DISHES = "doc/getOpenFood"  # 获取所有菜品列表
    GET_SHOP_INFO = "doc/getBaseInfo"  # 获取店铺信息
    GET_DISH_CATEGORIES = "doc/getFoodClassCategory"  # 菜品分类列表
    GET_SHOP_TABLES = "doc/getShopTableAreas"  # 桌台信息
    CREATE_ORDER = "order/submitordernew"  # 下单
    GET_FOOD_SALE_TIME = "doc/queryFoodSalTime"  # 查询菜品售卖时间
    GET_PAY_SUBJECT = "doc/getPaySubject"  # 店铺科目列表
    GET_GROUP_PAY_SUBJECT = "v2/doc/queryGroupSubjectInfo"  # 集团科目列表
    PAY_ORDER = "order/payout"  # 订单已支付
    ADD_DISH = "order/createOrder"  # 加菜
    ORDER_REFUND = "order/applyRefund"  # 退款申请
    DISH_ACTIVATION = "v2/doc/setIsActive"  # 设置菜品状态
    CREATE_ORDER_AND_PAY = "order/thirdOrder"  # 下单并支付


class KitchenPrinter:
    URL = "http://api.feieyun.cn/Api/Open/"  # 不需要修改
    USER = "<EMAIL>"  # *必填*：登录管理后台的账号名
    UKEY = "CPLf5uYyNMInmInX"  # *必填*: 注册账号后生成的UKEY
    # SN = "920548507"#*必填*：打印机编号，必须要在管理后台里手动添加打印机或者通过API添加之后，才能调用API


class FeiePrinter:
    URL = "http://api.feieyun.cn/Api/Open/"  # 不需要修改
    USER = "<EMAIL>"  # *必填*：登录管理后台的账号名
    UKEY = "CPLf5uYyNMInmInX"  # *必填*: 注册账号后生成的UKEY


class XPRINTER:
    URL = "https://open.xpyun.net/api/openapi/xprinter/"  # 不需要修改
    USER = "<EMAIL>"  # *必填*：登录管理后台的账号名
    UKEY = "3d25861c299c4c2b94a15709f656a13a"  # *必填*: 注册账号后生成的UKEY


class FeieTagPrinter:
    URL = "http://api.feieyun.cn/Api/Open/"
    USER = "<EMAIL>"  # *必填*：登录管理后台的账号名
    UKEY = "CPLf5uYyNMInmInX"  # *必填*: 注册账号后生成的UKEY


class ShilaiMerchantConstants:
    TEST_URL = os.environ.get("POS_SERVICE_MERCHANT_DOMAIN", "https://test.shilai-pos.zhiyi.cn")
    BASE_URL = os.environ.get("POS_SERVICE_MERCHANT_DOMAIN", "https://shilai-pos.merchant.zhiyi.cn")


class ShilaiPosConstants:
    SECRET_KEY = "34eb54a233ad81c5aa860bbb1dbea81f"
    TEST_URL = os.environ.get("POS_SERVICE_DOMAIN", "https://test.shilai-pos.zhiyi.cn")
    BASE_URL = os.environ.get("POS_SERVICE_DOMAIN", "https://shilai-pos.zhiyi.cn")
    PAYMENT_BASE_URL = BASE_URL
    VERSION = "v1.5"
    # 创建或更新商家
    ADD_OR_UPDATE_MERCHANT = "merchant"
    # 创建或更新门店
    ADD_OR_UPDATE_STORE = "store"
    # 菜单
    QUERY_MENU = "store/menu/query"
    # 所有菜品分类
    CATEGORY_LIST = "merchant/dish-category/list"
    # 所有菜品
    DISH_LIST = "merchant/dish/all"
    # 单个菜品
    DISH_INFO = "merchant/dish/query"
    # 打包盒
    PACKAGING_BOXS = "merchant/dish/get_packaging_boxs"
    # 创建订单
    CREATE_ORDER = "store/order"
    # 订单已支付
    PAY_SUCCESS = "store/order/pay-success"
    # 桌台列表
    LAYOUT_LIST = "store/layout/area/list"
    # 同步到天阙支付信息到时来支付服务
    UPDATE_TIAN_QUE_PAY_INFO = "payment/tian-que-pay"
    # 增加会员储值
    INCREASE_MEMBER_CARD_BALANCE = "user/membership/account/increase/balance"
    # 减少会员储值
    DECREASE_MEMBER_CARD_BALANCE = "user/membership/account/decrease/balance"
    # 获取账户余额
    GET_MEMBER_CARD_BALANCE = "user/membership/account/balance"
    # 储值列表
    GET_MEMBER_RECHARGE_CONFIG_LIST = "store/membership/account/recharge-rule/list"
    # 菜品易变信息,需要时时从收银机服务那边获取
    DISH_CHANGEFUL_INFO = "merchant/dish/changeful-info"
    # 更新菜品信息: 不能用于更新菜品基本信息
    # 1. 同步菜品可售数量
    UPDATE_DISH = "merchant/dish/mini-program/update"
    # 直接付款的订单同步
    SYNC_DIRECT_PAY_ORDER = "store/order/sync/direct-pay"
    # 桌台必点菜品列表
    GET_TABLE_REQUIRED_DISHES = "store/layout/required-dishes/query"
    # 获取订单详情
    GET_ORDER = "store/order/query"
    # 获取商家配置
    GET_STORE_SETTING = "store/setting/query"
    # 语音播报
    VOICE_BROADCAST = "speaker/voice-broadcast"


class DataCenterConstants:
    TEST_URL = os.environ.get("POS_SERVICE_DOMAIN", "https://test.shilai-pos.zhiyi.cn")
    BASE_URL = os.environ.get("POS_SERVICE_DOMAIN", "https://shilai-pos.zhiyi.cn")
    VERSION = "v1.5"

    # 把订单导入到数据中心
    IMPORT_ORDER = "/data-center/order/import"
    # 获取订单列表
    GET_ORDER_LIST = "/data-center/order/list/by-user"
    # 获取订单详情
    GET_ORDER = "/data-center/order"

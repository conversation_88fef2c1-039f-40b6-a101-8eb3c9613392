# -*- coding: utf-8 -*-

"""
Filename: keruyun_take_out_manager.py
Date: 2020-06-19 13:39:28
Title: 客如云外卖
@deprecated
"""

import bisect
import logging

from google.protobuf import json_format

import proto.ordering.keruyun.keruyun_take_out_pb2 as keruyun_take_out_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.ordering.keruyun_member_manager import KeruyunMemberManager
from business_ops.ordering.constants import KeruyunConstants
from common.utils import date_utils
from common.utils import distribute_lock
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class KeruyunTakeOutManager(OrderManager):
    def __init__(self, registration_info, *args, **kargs):
        super(KeruyunTakeOutManager, self).__init__()
        self.registration_info = registration_info
        self.merchant_id = self.registration_info.merchant_id
        self.merchant = MerchantDataAccessHelper().get_merchant(self.merchant_id)

        self.keruyun_ops = KeruyunOPSManager(self.registration_info)

    def cancel_order(self, order_id):
        """ 由用户发起取消订单
        """
        order = OrderingServiceDataAccessHelper().get_order(id=order_id)
        if not order:
            return
        if order.status == dish_pb.DishOrder.PAID:
            logger.info("订单: {} 已支付无法取消".format(order.id))
            return
        json_body = {"orderId": order.ordering_service_order_id, "reason": "用户取消订单"}
        logger.info("用户取消订单: {}".format(order.id))
        resp = self.keruyun_ops.post(token=self.keruyun_ops.token, uri=KeruyunConstants.KERUYUN_USER_CANCEL_ORDER, json_body=json_body)
        if resp.get("code") == 0:
            return True
        return False

    def get_order(self, ordering_service_order_id):
        json_body = {"orderId": ordering_service_order_id}
        resp = self.keruyun_ops.post(token=self.keruyun_ops.token, uri=KeruyunConstants.KERUYUN_TAKE_OUT_ORDER_GET, json_body=json_body).json()
        logger.info("客如云查询外卖订单返回: {}, {}".format(resp, ordering_service_order_id))
        if resp.get("code") == 0:
            result = resp.get("result")
            order = json_format.ParseDict(result, keruyun_take_out_pb.KeruyunTakeOutOrderInfo(), ignore_unknown_fields=True)
            return order
        return None

    def create_keruyun_order(self, order):
        take_out_order = keruyun_take_out_pb.KeruyunTakeOutOrder()

        take_out_order.tp_order_id = order.id
        take_out_order.people_count = order.people_count
        take_out_order.create_time = date_utils.timestamp_second()
        take_out_order.remark = order.remark
        take_out_order.is_print = 1
        take_out_order.print_template_types.append(8)
        take_out_order.print_template_types.append(9)

        self.set_user_invoice_info(order, take_out_order)
        self.set_order_status(order, take_out_order)
        self.set_shop_info(order, take_out_order)
        self.set_products(order, take_out_order)
        self.set_delivery(order, take_out_order)

        OrderingServiceDataAccessHelper().add_or_update_keruyun_take_out_order(take_out_order)

    def set_user_invoice_info(self, order, take_out_order):
        take_out_order.need_invoice = 0

    def set_order_status(self, order, take_out_order):
        take_out_order.status = 2

    def set_shop_info(self, order, take_out_order):
        take_out_order.shop.shop_identy = self.registration_info.keruyun_pos_info.shop_id
        take_out_order.shop.shop_name = self.registration_info.keruyun_pos_info.shop_name
        take_out_order.shop.tp_shop_id = order.merchant_id

    def set_products(self, order, take_out_order):
        for product in order.products:
            to_product = take_out_order.products.add()
            to_product.name = product.name
            to_product.id = product.id
            to_product.type = product.type
            to_product.parent_uuid = product.parent_uuid
            to_product.uuid = product.uuid
            to_product.tp_id = product.id
            to_product.quantity = product.quantity
            to_product.unit = product.unit
            to_product.price = product.price
            to_product.total_fee = product.total_fee
            to_product.remark = product.remark

            to_product.package_price = self.registration_info.logistics_config.packaging_fee_per_product
            to_product.package_quantity = int(product.box_qty)
            take_out_order.payment.package_fee += to_product.package_price * to_product.package_quantity * int(product.quantity)

            take_out_order.payment.total_fee += to_product.package_price * to_product.package_quantity * int(product.quantity)

            for attr in product.attrs:
                self.set_attr(attr, to_product)

            for supply_condiment in product.supply_condiments:
                keruyun_product = take_out_order.products.add()
                self.set_supply_condiments(supply_condiment, keruyun_product, to_product, take_out_order)

        take_out_order.payment.total_fee += order.bill_fee

    def set_attr(self, shilai_attr, to_product):
        to_attr = to_product.properties.add()
        to_attr.name = shilai_attr.name
        to_attr.id = int(shilai_attr.id)
        to_attr.type = shilai_attr.type
        to_attr.reprice = shilai_attr.reprice
        # 客如云外卖订单的商户总价是由时来订单变化而来
        # 时来订单的商品总价已经加过属性的reprice了,所以此处不用再加一次
        # to_product.total_fee += int(shilai_attr.reprice * to_product.quantity)

    def set_supply_condiments(self, shilai_supply_condiment, to_product, parent_product, take_out_order):
        to_product.id = shilai_supply_condiment.id
        to_product.name = shilai_supply_condiment.name
        to_product.price = shilai_supply_condiment.market_price
        to_product.quantity = shilai_supply_condiment.quantity
        to_product.parent_uuid = parent_product.uuid
        to_product.total_fee = int(to_product.price * to_product.quantity)
        to_product.type = 2

        # take_out_order.payment.total_fee += to_product.quantity * to_product.price

    def set_delivery(self, order, take_out_order):
        take_out_order.delivery.expect_time = order.appointment_time
        take_out_order.delivery.delivery_party = 1
        take_out_order.delivery.receiver_name = order.shipping_address.username
        take_out_order.delivery.receiver_gender = -1
        if order.shipping_address.gender == "先生":
            take_out_order.delivery.receiver_gender = 1
        else:
            take_out_order.delivery.receiver_gender = 0
        take_out_order.delivery.receiver_phone = order.shipping_address.mobile_phone
        take_out_order.delivery.deliverer_name = ""
        take_out_order.delivery.deliverer_phone = ""
        take_out_order.delivery.deliverer_address = "{}-{}".format(order.shipping_address.street, order.shipping_address.house_number)
        take_out_order.delivery.coordinate_type = 1
        take_out_order.delivery.longitude = order.shipping_address.longitude
        take_out_order.delivery.latitude = order.shipping_address.latitude

    def get_distance(self, user_id, address_id):
        user = UserDataAccessHelper().get_user(user_id)
        if len(self.merchant.stores) == 0:
            logger.info("该商户没有stores: {}".format(self.merchant.id))
            raise errors.TooFarAwayToSend()
        store = self.merchant.stores[0]
        if not store.HasField("poi"):
            logger.info("该商户poi不正确: {}".format(self.merchant.id))
            raise errors.TooFarAwayToSend()
        location = store.poi.location
        for s in user.member_profile.shipping_address:
            if s.id != address_id:
                continue
            distance = self._get_distance(s.longitude, s.latitude, location.longitude, location.latitude)
            # 如果设置了最远配送距离就需要检查是不是超过最远配送距离.
            if distance > self.registration_info.max_take_out_distance:
                raise errors.TooFarAwayToSend()
            return distance
        logger.info("没有找到对应的address_id: {}".format(address_id))
        raise errors.TooFarAwayToSend()

    def calculate_delivery_fee(self, user_id, address_id):
        logistics_config = self.registration_info.logistics_config
        type = logistics_config.shipping_fee_type
        if type == registration_pb.LogisticsConfig.PER_KILOMETRE:
            kilometres = (self.get_distance(user_id, address_id) + 999) / 1000
            return kilometres * self.registration_info.logistics_config.shipping_fee
        elif type == registration_pb.LogisticsConfig.TOTAL_PRICE:
            return self.registration_info.logistics_config.shipping_fee
        elif type == registration_pb.LogisticsConfig.LADDER_PRICE:
            kilometres = logistics_config.kilometres
            shipping_fee_ladder = logistics_config.shipping_fee_ladder
            distance = (self.get_distance(user_id, address_id) + 999) / 1000
            distance_index = bisect.bisect_left(distance, kilometres)
            if distance_index > 0:
                distance_index -= 1
            return shipping_fee_ladder[distance_index]
        return 0

    def set_payment_member_info(self, take_out_order, transaction):
        if not transaction.pay_method == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_PAY:
            return
        member_manager = KeruyunMemberManager(self.registration_info)
        member = member_manager.get_customer_by_user_id(transaction.payer_id)
        take_out_order.member_id = member.customer_id

    def set_payment(self, order, take_out_order, transaction):
        take_out_order.payment.total_fee += take_out_order.payment.delivery_fee

        take_out_order.payment.pay_type = 2
        take_out_order.payment.shop_fee = int(self._calculate_paid_in_fee(order, transaction))
        take_out_order.payment.user_fee = int(take_out_order.payment.shop_fee)

    def set_discount_detail(self, order, take_out_order, transaction):
        # discount_detail = take_out_order.discount_details.add()
        # discount_detail.discount_type = 3
        # discount_detail.discount_fee = order.bill_fee - (take_out_order.payment.shop_fee - take_out_order.payment.package_fee - take_out_order.payment.delivery_fee)
        discount_fee = order.bill_fee - (take_out_order.payment.shop_fee - take_out_order.payment.package_fee - take_out_order.payment.delivery_fee)
        take_out_order.payment.discount_fee = discount_fee
        take_out_order.payment.shop_discount_fee = discount_fee

    def __create_order(self, order, transaction, coupon_fee, try_times=3):
        if try_times == 0:
            raise errors.KeruyunCreateOrderError()
        ordering_service_da = OrderingServiceDataAccessHelper()
        keruyun_order = ordering_service_da.get_keruyun_take_out_order(tp_order_id=order.id)
        keruyun_order.payment.delivery_fee = order.keruyun_take_out_shipping_fee

        self.set_payment(order, keruyun_order, transaction)
        self.set_discount_detail(order, keruyun_order, transaction)

        json_body = json_format.MessageToDict(keruyun_order, including_default_value_fields=True)
        # 向客如云发起订单
        logger.info("向客如云发起创建订单: {}".format(json_body))
        resp = self.keruyun_ops.post(token=self.keruyun_ops.token, uri=KeruyunConstants.KERUYUN_TAKE_OUT_CREATE, json_body=json_body).json()
        logger.info("客如云创建订单接口返回: {}, {}".format(resp, order.id))
        if resp.get("code") == 0:
            order.ordering_service_order_id = resp.get("result").get("orderId")
            order.ordering_service_trade_id = resp.get("result").get("tradeId")
            order.ordering_service_serial_number = resp.get("result").get("serialNumber")
            order.status = dish_pb.DishOrder.APPROVED
            order.approve_time = date_utils.timestamp_second()
            ordering_service_da.add_or_update_order(no_check=True, order=order)
            return True
        else:
            logger.info('创建客如云订单失败,重试: {}, resp.code: {}'.format(order.id, resp.get('code')))
            return self.__create_order(order, transaction, coupon_fee, try_times=try_times - 1)
        return False

    def pay_order(self, order, transaction, coupon_fee=0):
        with distribute_lock.redislock(key=order.table_id, ttl=5000, retry_count=50, retry_delay=200) as lock:
            if not lock:
                raise errors.KeruyunPayOrderError()
            return self.__create_order(order, transaction, coupon_fee)
        logger.info('不能获取桌台锁: tableID: {}, order_id{}'.format(order.table_id, order.id))
        raise errors.KeruyunPayOrderError()

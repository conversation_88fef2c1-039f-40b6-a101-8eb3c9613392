# -*- coding: utf-8 -*-

import proto.ordering.registration_pb2 as registration_pb
from business_ops.base_manager import BaseManager
from business_ops.ordering.keruyun_shop_manager import KeruyunShopManager
from business_ops.ordering.hualala_shop_manager import HualalaShopManager
from business_ops.ordering.shilai_shop_manager import ShilaiShopManager
from business_ops.ordering.feie_shop_manager import FeieShopManager
from service import error_codes
from service import errors


class ShopManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(ShopManager, self).__init__(*args, **kargs)
        self.init_shop_manager(**kargs)

    def init_shop_manager(self, **kargs):
        if not self.registration_info:
            return
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            self.shop_manager = KeruyunShopManager(shop_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            self.shop_manager = HualalaShopManager(shop_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            self.shop_manager = ShilaiShopManager(shop_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            self.shop_manager = FeieShopManager(shop_manager=self)
        else:
            raise errors.Error(err=error_codes.POS_NOT_SUPPORTED)

    def register(
        self, group_id=None, shop_id=None, app_secret=None, app_key=None, registration_info=None, pos_type=None, *args, **kargs
    ):
        if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            manager = KeruyunShopManager(shop_manager=self)
            registration_info = manager.register(shop_id=shop_id, registration_info=registration_info)
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            manager = HualalaShopManager(shop_manager=self, app_secret=app_secret, app_key=app_key)
            registration_info = manager.register(
                group_id=group_id, shop_id=shop_id, app_secret=app_secret, app_key=app_key, registration_info=registration_info
            )
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            manager = ShilaiShopManager(shop_manager=self)
            registration_info = manager.register(registration_info=registration_info)
        if registration_info:
            registration_info.ordering_config.enable_eat_in = True
        return registration_info

    def get_pay_subject(self):
        if hasattr(self.shop_manager, "get_pay_subject"):
            return self.shop_manager.get_pay_subject()

    def get_group_pay_subject(self):
        if hasattr(self.shop_manager, "get_group_pay_subject"):
            return self.shop_manager.get_group_pay_subject()

    def add_or_update_merchant(self):
        if hasattr(self.shop_manager, "add_or_update_merchant"):
            return self.shop_manager.add_or_update_merchant()

    def add_or_update_store(self, registration_info=None):
        if hasattr(self.shop_manager, "add_or_update_store"):
            return self.shop_manager.add_or_update_store(registration_info)

    def upload_merchant_info(self, **kargs):
        shop_manager = ShilaiShopManager(shop_manager=self)
        shop_manager.upload_merchant_info(**kargs)

    def get_store_setting(self):
        if hasattr(self.shop_manager, "get_store_setting"):
            return self.shop_manager.get_store_setting()

# -*- coding: utf-8 -*-

""" 菜品属性相关的操作
"""

import logging
import time

import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.dish_attr_pb2 as dish_attr_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.dish_attr_da_helper import DishAttrData<PERSON>ccessHelper
from service import errors

logger = logging.getLogger(__name__)


class DishAttrManager(BaseManager):
    # 新增属性组
    CREATE_ATTR_GROUP = "CREATE_ATTR_GROUP"
    # 获取属性组列表
    ACCESS_ATTR_GROUPS = "ACCESS_ATTR_GROUPS"
    # 更新属性组信息
    UPDATE_ATTR_GROUP = "UPDATE_ATTR_GROUP"
    # 删除属性组
    DELETE_ATTR_GROUP = "DELETE_ATTR_GROUP"
    # 下架属性组
    SOLD_OUT_ATTR_GROUP = "SOLD_OUT_ATTR_GROUP"
    # 上架属性组
    SALE_IN_ATTR_GROUP = "SALE_IN_ATTR_GROUP"

    # 新增属性
    CREATE_ATTR = "CREATE_ATTR"
    # 更新属性
    UPDATE_ATTR = "UPDATE_ATTR"
    # 删除属性
    DELETE_ATTR = "DELETE_ATTR"
    # 下架属性
    SOLD_OUT_ATTR = "SOLD_OUT_ATTR"
    # 上架属性
    SALE_IN_ATTR = "SALE_IN_ATTR"
    # 属性移动到另一个分组
    MOVE_ATTR_TO_ANOTHER_GROUP = "MOVE_ATTR_TO_ANOTHER_GROUP"

    # 把属性组全部添加到多个菜品中
    ADD_ATTR_GROUP_TO_DISHES = "ADD_ATTR_GROUP_TO_DISHES"
    # 把属性组从多个菜品中删除
    REMOVE_ATTR_GROUP_FROM_DISHES = "REMOVE_ATTR_GROUP_FROM_DISHES"
    # 在菜品中把属性组更新
    # 如果属性组里的某个属性在菜品中那么更新这个属性,否则什么也不做
    UPDATE_ATTR_GROUP_IN_DISHES = "UPDATE_ATTR_GROUP_IN_DISHES"

    # 把属性添加到多个菜品中
    ADD_ATTR_TO_DISHES = "ADD_ATTR_TO_DISHES"
    # 把属性从多个菜品中删除
    REMOVE_ATTR_FROM_DISHES = "REMOVE_ATTR_FROM_DISHES"
    # 更新菜品中的属性
    UPDATE_ATTR_IN_DISHES = "UPDATE_ATTR_IN_DISHES"

    # 属性分组排序
    REORDER_ATTR_GROUPS = "REORDER_ATTR_GROUPS"
    # 某个分组下的属性排序
    REORDER_ATTRS = "REORDER_ATTRS"

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._dishes = None
        self._order_da = None
        self._attr_da = None
        self._need_update_groups = dict()
        self._need_update_dishes = dict()
        # 本次操作的类型
        self._operation = kargs.get("operation")
        # 一个属性分组的个数应该不会超过200个
        self._group_maxize = 200

    @property
    def operation(self):
        return self._operation

    @property
    def dishes(self):
        if self._dishes:
            return self._dishes
        self._dishes = dict()
        dishes = self.order_da.get_all_dishes(merchant_id=self.merchant.id, nocache=True)
        for dish in dishes:
            self._dishes.update({dish.id: dish})
        return self._dishes

    @property
    def order_da(self):
        if self._order_da:
            return self._order_da
        self._order_da = OrderingServiceDataAccessHelper()
        return self._order_da

    @property
    def attr_da(self):
        if self._attr_da:
            return self._attr_da
        self._attr_da = DishAttrDataAccessHelper()
        return self._attr_da

    def get_dish(self, dish_id, raise_error=False):
        dish = self.dishes.get(dish_id)
        if dish is None and raise_error:
            raise errors.ShowError("菜品不存在")
        return dish

    def add_or_update_group(self, group):
        self.attr_da.add_or_update_attr_group(group)

    def add_or_update_dish(self, dish):
        self.order_da.add_or_update_dish(dish=dish)

    # 菜品属性 start ###########################################
    def update_attr_group_in_dishes(self, group_id, dish_ids, operation):
        group = self.get_attr_group(group_id=group_id, raise_error=True)
        dishes = {}
        for dish_id in dish_ids:
            dish = self.get_dish(dish_id)
            if not dish:
                continue
            dishes.update({dish.id: dish})
        self.__update_attr_group_in_dishes(group, dishes, operation)

    def __update_attr_group_in_dishes(self, group, dishes, operation):
        for dish_id, dish in dishes.items():
            dish_attr_map = {attr.id: attr for attr in dish.attrs}
            if operation == self.UPDATE_ATTR_GROUP_IN_DISHES:
                self.__update_attr_group_in_dish(group, dish_attr_map, dish)
            elif operation == self.ADD_ATTR_GROUP_TO_DISHES:
                self.__add_attr_group_to_dish(group, dish_attr_map, dish)
            elif operation == self.REMOVE_ATTR_GROUP_FROM_DISHES:
                self.__remove_attr_group_from_dish(group, dish_attr_map, dish)
            else:
                continue
            dish.attrs.sort(key=lambda x: x.sort)
            self.__update_need_update_dishes(dish)

    def __update_attr_group_in_dish(self, group, dish_attr_map, dish):
        for attr in group.attrs:
            dish_attr = dish_attr_map.get(attr.id)
            if dish_attr is None:
                continue
            msg = f"""
            更新菜品属性前: {dish.name} {dish.id}
            属性名: {dish_attr.name} {attr.name}
            排序: {dish_attr.sort} {attr.sort}
            价格: {dish_attr.reprice} {attr.reprice}
            组: {group.id} {dish_attr.group_id} {group.name} {dish_attr.group_name}
            状态: {dish_pb.Attr.Status.Name(dish_attr.status)} {dish_pb.Attr.Status.Name(attr.status)}
            """
            logger.info(msg)
            self.__update_dish_attr(group, attr, dish_attr)

    def __add_attr_group_to_dish(self, group, dish_attr_map, dish):
        for attr in group.attrs:
            dish_attr = dish_attr_map.get(attr.id)
            if dish_attr:
                self.__update_dish_attr(group, attr, dish_attr)
            else:
                self.__add_attr_to_dish(group, attr, dish)

    def __remove_attr_group_from_dish(self, group, dish_attr_map, dish):
        for attr in group.attrs:
            dish_attr = dish_attr_map.get(attr.id)
            if not dish_attr:
                continue
            dish.attrs.remove(dish_attr)

    def update_attr_in_dishes(self, group_id, attr_id, dish_ids, operation):
        group = self.get_attr_group(group_id, raise_error=True)
        attr = self.__get_attr_from_group(group, attr_id, raise_error=True)
        dishes = dict()
        for dish_id in dish_ids:
            dish = self.get_dish(dish_id, raise_error=True)
            dishes.update({dish.id: dish})
        self.__update_attr_in_dishes(group, attr, dishes, operation)

    def __update_attr_in_dishes(self, group, attr, dishes, operation):
        for dish_id, dish in dishes.items():
            if operation == self.UPDATE_ATTR_IN_DISHES:
                self.__update_attr_in_dish(group, attr, dish)
            elif operation == self.ADD_ATTR_TO_DISHES:
                self.__add_attr_to_dish(group, attr, dish)
            elif operation == self.REMOVE_ATTR_FROM_DISHES:
                self.__remove_attr_from_dish(group, attr, dish)
            else:
                continue
            self.__update_need_update_dishes(dish)

    def __update_attr_in_dish(self, group, attr, dish):
        for dish_attr in dish.attrs:
            if attr.id != dish_attr.id:
                continue
            self.__update_dish_attr(group, attr, dish_attr)
            break

    def __update_dish_attr(self, group, attr, dish_attr):
        dish_attr.name = attr.name
        dish_attr.reprice = attr.reprice
        dish_attr.status = attr.status
        dish_attr.sort = attr.sort
        dish_attr.group_id = group.id
        dish_attr.type = group.attr_group_type
        dish_attr.group_name = group.name
        dish_attr.id = attr.id

    def __remove_attr_from_dish(self, group, attr, dish):
        for dish_attr in dish.attrs:
            if attr.id != dish_attr.id:
                continue
            dish.attrs.remove(dish_attr)
            break

    def __add_attr_to_dish(self, group, attr, dish):
        dish_attr = dish.attrs.add()
        self.__update_dish_attr(group, attr, dish_attr)

    # 菜品属性 end #############################################

    # 属性相关 start ###########################################
    def __create_attr_obj(self):
        attr = dish_attr_pb.DishAttr()
        attr.id = id_manager.generate_common_id()
        return attr

    def __update_attr(self, group, attr, **kargs):
        if not attr:
            return
        name = kargs.get("name")
        if name is not None:
            attr.name = name
        status = kargs.get("status")
        if status is not None:
            attr.status = status
            if attr.status == dish_pb.Attr.DELETED:
                attr.name += f"(已删除-{int(time.time())})"
        sort = kargs.get("sort")
        if sort is not None:
            attr.sort = sort
        reprice = kargs.get("reprice")
        if reprice is not None:
            attr.reprice = reprice

        self.__attr_exists(group, attr, raise_error=True)

        new_group_id = kargs.get("new_group_id")
        if new_group_id:
            new_group = self.get_attr_group(group_id=new_group_id, raise_error=True)
            self.move_attr_to_another_group(attr, group, new_group)
            self.__update_need_update_groups(new_group)
        self.__update_need_update_groups(group)
        return attr

    def create_attr(self, group_id, **kargs):
        group = self.get_attr_group(group_id, raise_error=True)
        name = kargs.get("name")
        reprice = kargs.get("reprice")
        attr = self.__create_attr_obj()
        self.__update_attr(group, attr, name=name, reprice=reprice)
        new_attr = group.attrs.add()
        new_attr.CopyFrom(attr)
        return self.__update_attr(group, new_attr, **kargs)

    def update_attr(self, group_id, attr_id, **kargs):
        group = self.get_attr_group(group_id=group_id, raise_error=True)
        attr = self.__get_attr_from_group(group, attr_id)
        self.__update_attr(group, attr, **kargs)
        if self.operation in [self.UPDATE_ATTR, self.SOLD_OUT_ATTR, self.SALE_IN_ATTR]:
            dish_operation = self.UPDATE_ATTR_IN_DISHES
            self.__update_attr_in_dishes(group, attr, self.dishes, dish_operation)
        elif self.operation in [self.DELETE_ATTR]:
            dish_operation = self.REMOVE_ATTR_FROM_DISHES
            self.__update_attr_in_dishes(group, attr, self.dishes, dish_operation)
        return attr

    def delete_attr(self, group_id, attr_id):
        self.update_attr(group_id, attr_id, status=dish_pb.Attr.DELETED)

    def sold_out_attr(self, group_id, attr_id):
        self.update_attr(group_id, attr_id, status=dish_pb.Attr.SOLD_OUT)

    def sale_in_attr(self, group_id, attr_id):
        self.update_attr(group_id, attr_id, status=dish_pb.Attr.NORMAL)

    def move_attr_to_another_group(self, group_id, new_group_id, attr_id):
        group = self.get_attr_group(group_id, raise_error=True)
        new_group = self.get_attr_group(new_group_id, raise_error=True)
        attr = self.__get_attr_from_group(group, attr_id, raise_error=True)
        self.__move_attr_to_another_group(group, new_group, attr)

    def __move_attr_to_another_group(self, group, new_group, attr):
        if None in (group, new_group, attr):
            return
        group.attrs.remove(attr)
        new_attr = new_group.attrs.add()
        new_attr.CopyFrom(attr)
        self.__update_need_update_groups(group)
        self.__update_need_update_groups(new_group)
        dish_operation = self.UPDATE_ATTR_IN_DISHES
        self.__update_attr_in_dishes(new_group, attr, self.dishes, dish_operation)

    def reorder_attrs(self, group_id, sort_array):
        sort_index = {}
        group = self.get_attr_group(group_id=group_id, raise_error=True)
        for index, attr_id in enumerate(sort_array):
            sort_index.update({attr_id: index})
        for attr in group.attrs:
            attr.sort = sort_index.get(attr.id)
            attr.sort = self.__generate_attr_sort(group, attr)
        group.attrs.sort(key=lambda x: x.sort)
        self.__update_need_update_groups(group)
        self.__update_attr_group_in_dishes(group, self.dishes, self.UPDATE_ATTR_GROUP_IN_DISHES)

    def __get_attr_from_group(self, group, attr_id, raise_error=False):
        attr = None
        for a in group.attrs:
            if a.id != attr_id:
                continue
            attr = a
            break
        if attr is None and raise_error:
            raise errors.ShowError("属性不存在")
        return attr

    def __attr_exists(self, group, attr, raise_error=False):
        key0 = self.__attr_unique_key(attr)
        flag = False
        for a in group.attrs:
            if a.id == attr.id:
                continue
            key1 = self.__attr_unique_key(a)
            if key0 == key1:
                flag = True
            if flag:
                break
        if flag and raise_error:
            raise errors.ShowError(f"此属性已在此组存在: {attr.name} {attr.reprice}")
        return flag

    def __attr_unique_key(self, attr):
        key = f"{attr.name}{attr.reprice}"
        return key

    # 属性相关 end ###########################################

    # 属性组相关 start #######################################
    def __create_attr_group_obj(self):
        group = dish_attr_pb.DishAttrGroup()
        group.id = id_manager.generate_common_id()
        group.merchant_id = self.merchant.id
        group.selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE
        group.attr_group_type = dish_pb.Attr.TASTE
        self.__update_need_update_groups(group)
        return group

    def __update_attr_group(self, group, **kargs):
        if not group:
            return
        name = kargs.get("name")
        if name is not None:
            group.name = name
            self.__attr_group_exists(group, raise_error=True)
        selection_type = kargs.get("selection_type")
        if selection_type is not None:
            group.selection_type.type = selection_type
        attr_group_type = kargs.get("attr_group_type")
        if attr_group_type is not None:
            if isinstance(attr_group_type, str):
                attr_group_type = dish_pb.Attr.AttrType.Value(attr_group_type)
            group.attr_group_type = attr_group_type
        status = kargs.get("status")
        if status is not None:
            if isinstance(status, str):
                status = dish_pb.Attr.Status.Value(status)
            if status == dish_attr_pb.DishAttrGroup.DELETED:
                group.name += f'(已删除-{int(time.time())})'
            group.status = status
        sort = kargs.get("sort")
        if sort is not None:
            group.sort = sort
        self.__update_need_update_groups(group)
        return group

    def create_attr_group(self, **kargs):
        name = kargs.get("name")
        group = self.attr_da.get_attr_group(merchant_id=self.merchant.id, group_name=name)
        if group:
            if group.status in [dish_attr_pb.DishAttrGroup.NORMAL, dish_attr_pb.DishAttrGroup.SOLD_OUT]:
                raise errors.ShowError(f"'{name}'已存在")
            kargs.update({"status": dish_attr_pb.DishAttrGroup.NORMAL})
        else:
            group = self.__create_attr_group_obj()
        self.__update_attr_group(group, **kargs)
        return group

    def update_attr_group(self, group_id, **kargs):
        group = self.get_attr_group(group_id=group_id, raise_error=True)
        ret = self.__update_attr_group(group, **kargs)
        if self.operation in [self.UPDATE_ATTR_GROUP, self.SOLD_OUT_ATTR_GROUP, self.SALE_IN_ATTR_GROUP]:
            dish_operation = self.UPDATE_ATTR_GROUP_IN_DISHES
            self.__update_attr_group_in_dishes(group, self.dishes, dish_operation)
        elif self.operation == self.DELETE_ATTR_GROUP:
            dish_operation = self.REMOVE_ATTR_GROUP_FROM_DISHES
            self.__update_attr_group_in_dishes(group, self.dishes, dish_operation)
        return ret

    def get_attr_group(self, group_id, raise_error=False):
        group = self.attr_da.get_attr_group(merchant_id=self.merchant.id, id=group_id)
        if not group and raise_error:
            raise errors.ShowError("属性组不存在")
        return group

    def get_attr_groups(self):
        groups = self.attr_da.get_attr_groups(merchant_id=self.merchant.id)
        return groups

    def delete_attr_group(self, group_id):
        status = dish_attr_pb.DishAttrGroup.DELETED
        return self.update_attr_group(group_id, status=status)

    def sold_out_attr_group(self, group_id):
        status = dish_attr_pb.DishAttrGroup.SOLD_OUT
        return self.update_attr_group(group_id, status=status)

    def sale_in_attr_group(self, group_id):
        status = dish_attr_pb.DishAttrGroup.NORMAL
        return self.update_attr_group(group_id, status=status)

    def reorder_attr_groups(self, sort_array):
        sort_index = {}
        for index, group_id in enumerate(sort_array):
            sort_index.update({group_id: index})
        groups = self.get_attr_groups()
        spec_sort = -20
        for group in groups:
            sort = sort_index.get(group.id)
            # 把规格放在最前面
            if group.attr_group_type == dish_pb.Attr.SPECIFICATION:
                sort = spec_sort
                spec_sort += 1
            group.sort = sort
            for attr_index, attr in enumerate(group.attrs):
                attr.sort = attr_index
                attr.sort = self.__generate_attr_sort(group, attr)
            self.__update_need_update_groups(group)
            self.__update_attr_group_in_dishes(group, self.dishes, self.UPDATE_ATTR_GROUP_IN_DISHES)

    def __generate_attr_sort(self, group, attr):
        sort = group.sort * self._group_maxize + attr.sort
        return sort

    def __attr_group_exists(self, group, raise_error=False):
        exists_group = self.attr_da.get_attr_group(merchant_id=self.merchant.id, group_name=group.name)
        if exists_group is None:
            return False
        if exists_group and group.id != exists_group.id and raise_error:
            raise errors.ShowError(f"属性组'{group.name}'已存在")

    # 属性组相关 end #######################################

    def __update_need_update_dishes(self, dish):
        if dish.id not in self._need_update_dishes:
            self._need_update_dishes.update({dish.id: dish})

    def __update_need_update_groups(self, group):
        if group.id not in self._need_update_groups:
            self._need_update_groups.update({group.id: group})

    def operate(self, request):
        # 属性ID
        attr_id = request.json.get("attrId", None)
        # 属性名
        attr_name = request.json.get("attrName", None)
        # 属性变价
        attr_reprice = request.json.get("attrReprice", None)
        # 属性状态
        attr_status = request.json.get("attrStatus", None)
        # 属性排序时,传输属性列表
        attr_sort_array = request.json.get("attrSortArray", None)

        # 属性组ID
        group_id = request.json.get("groupId", None)
        # 属性组名
        group_name = request.json.get("groupName", None)
        # 属性组状态
        group_status = request.json.get("groupStatus", None)
        # 属性选择类型
        selection_type = request.json.get("selectionType", None)
        # 属性类型
        attr_group_type = request.json.get("attrGroupType", None)
        # 属性组排序时,传输属性组列表
        group_sort_array = request.json.get("groupSortArray", None)
        # 把属性从一个组移动到另一个组时,新的属性组ID
        new_group_id = request.json.get("newGroupId", None)

        # 为某部分菜品修改属性或者属性组时,传菜品ID列表
        dish_ids = request.json.get("dishIds", None)

        ret = None

        if self.operation == self.ACCESS_ATTR_GROUPS:
            ret = self.get_attr_groups()
        elif self.operation == self.CREATE_ATTR_GROUP:
            ret = self.create_attr_group(name=group_name, status=group_status, selection_type=selection_type)
        elif self.operation == self.UPDATE_ATTR_GROUP:
            self.update_attr_group(
                group_id, name=group_name, status=group_status, selection_type=selection_type, attr_group_type=attr_group_type
            )
        elif self.operation == self.DELETE_ATTR_GROUP:
            self.delete_attr_group(group_id)
        elif self.operation == self.SOLD_OUT_ATTR_GROUP:
            self.sold_out_attr_group(group_id)
        elif self.operation == self.SALE_IN_ATTR_GROUP:
            self.sale_in_attr_group(group_id)
        elif self.operation == self.REORDER_ATTR_GROUPS:
            self.reorder_attr_groups(group_sort_array)

        elif self.operation == self.CREATE_ATTR:
            ret = self.create_attr(group_id, name=attr_name, reprice=attr_reprice)
        elif self.operation == self.UPDATE_ATTR:
            self.update_attr(group_id, attr_id, name=attr_name, reprice=attr_reprice, status=attr_status)
        elif self.operation == self.DELETE_ATTR:
            self.delete_attr(group_id, attr_id)
        elif self.operation == self.SOLD_OUT_ATTR:
            self.sold_out_attr(group_id, attr_id)
        elif self.operation == self.SALE_IN_ATTR:
            self.sale_in_attr(group_id, attr_id)
        elif self.operation == self.ADD_ATTR_GROUP_TO_DISHES:
            self.update_attr_group_in_dishes(group_id, dish_ids, self.operation)
        elif self.operation == self.REMOVE_ATTR_GROUP_FROM_DISHES:
            self.update_attr_group_in_dishes(group_id, dish_ids, self.operation)
        elif self.operation == self.UPDATE_ATTR_GROUP_IN_DISHES:
            self.update_attr_group_in_dishes(group_id, dish_ids, self.operation)
        elif self.operation == self.REORDER_ATTRS:
            self.reorder_attrs(group_id, attr_sort_array)
        elif self.operate == self.MOVE_ATTR_TO_ANOTHER_GROUP:
            self.move_attr_to_another_group(group_id, new_group_id, attr_id)

        elif self.operation == self.ADD_ATTR_TO_DISHES:
            self.update_attr_in_dishes(group_id, attr_id, dish_ids, self.operation)
        elif self.operation == self.REMOVE_ATTR_FROM_DISHES:
            self.update_attr_in_dishes(group_id, attr_id, dish_ids, self.operation)
        elif self.operation == self.UPDATE_ATTR_IN_DISHES:
            self.update_attr_in_dishes(group_id, attr_id, dish_ids, self.operation)
        else:
            return None
        for _, group in self._need_update_groups.items():
            self.add_or_update_group(group)
        for _, dish in self._need_update_dishes.items():
            self.add_or_update_dish(dish)
        return ret

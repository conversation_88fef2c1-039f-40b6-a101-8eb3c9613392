# -*- coding: utf-8 -*-

import logging
from collections import defaultdict

import proto.page.dish_category_pb2 as page_dish_category_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors


logger = logging.getLogger(__name__)


class DishCategoryManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(DishCategoryManager, self).__init__(**kargs)

    def get_dish_category_list(self):
        """菜品分类列表"""
        ordering_da = OrderingServiceDataAccessHelper()
        categories = ordering_da.get_categories(merchant_id=self.merchant.id)
        dish_category_list_vo = page_dish_category_pb.DishCategoryListVO()
        for category in categories:
            if category.status == dish_pb.DishCategory.DELETE:
                continue
            dish_category_vo = dish_category_list_vo.dish_categories.add()
            dish_category_vo.id = category.id
            dish_category_vo.name = category.name
            dish_category_vo.status = category.status
            dish_category_vo.no_discount = category.no_discount
            dish_category_vo.sort = category.sort
            dish_category_vo.required = category.required
        dish_category_list_vo.dish_categories.sort(key=lambda x: x.sort)
        return dish_category_list_vo

    def remove_dish_category(self, category_id):
        """永久性的删除菜品分类
        `category_id`: 菜品分类ID
        """
        if not self.is_test:
            raise errors.ShowError("不能执行此操作")
        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.delete_category(id=category_id, merchant_id=self.merchant.id)

    def update_dish_category(self, **category_dict):
        """更新菜品分类
        `category`: dict
            id: string 分类ID
            name: string 分类名
            sort: int 排序,越小越在前
            noDiscount: boolean 是否不打折
            status: enum 状态
                ACTIVE: 启用
                DELETE: 删除(在点餐小程序上隐藏)
                INACTIVE: 停用
        """
        ordering_da = OrderingServiceDataAccessHelper()
        category_id = category_dict.get("id")

        name = category_dict.get("name")
        category = self.get_or_create_category(category_id, name)
        if name is not None:
            category.name = category_dict.get("name")

        no_discount = category_dict.get("no_discount")
        if no_discount is not None:
            category.no_discount = no_discount

        status = category_dict.get("status")
        if status is not None:
            category.status = dish_pb.DishCategory.Status.Value(status)

        sort = category_dict.get("sort")
        if sort is not None and isinstance(sort, int):
            category.sort = sort

        sns = category_dict.get("sns")
        self.update_category_printer_config(ordering_da, category, sns)

        required = category_dict.get('required')
        if required is not None and isinstance(required, bool):
            category.required = required

        ordering_da.add_or_update_category(category=category)
        return category

    def update_printer_category(self, sns):
        ordering_da = OrderingServiceDataAccessHelper()
        category_map = {}
        for item in sns:
            printers = ordering_da.get_category_printers(merchant_id=self.merchant.id, printer_sn=item.get('sn'))
            for category_printer in printers:
                while category_printer.printer_sns:
                    category_printer.printer_sns.pop()
                ordering_da.set_dish_category_printer(category_printer)
            for category_id in item.get('categoryIds', []):
                tmp = category_map.get(category_id, [])
                tmp.append(item.get('sn'))
                category_map.update({category_id: tmp})
        for category_id, sns in category_map.items():
            category = ordering_da.get_category(merchant_id=self.merchant.id, id=category_id)
            if not category:
                logger.warning(f'{category_id} 分类不存在')
                continue
            self.update_category_printer_config(ordering_da, category, sns)

    def update_category_printer_config(self, ordering_da, category, sns):
        if sns is None:
            return
        category_printer = ordering_da.get_dish_category_printer(merchant_id=self.merchant.id, dish_category_id=category.id)
        if not category_printer:
            category_printer = dish_pb.DishCategoryPrinter()
        category_printer.merchant_id = self.merchant.id
        category_printer.dish_category_id = category.id
        while category_printer.printer_sns:
            category_printer.printer_sns.pop()
        for sn in sns:
            category_printer.printer_sns.append(sn)
        ordering_da.set_dish_category_printer(category_printer)

    def get_or_create_category(self, category_id, name):
        ordering_da = OrderingServiceDataAccessHelper()
        category = ordering_da.get_category(merchant_id=self.merchant.id, id=category_id)
        if not category:
            category = ordering_da.get_category(merchant_id=self.merchant.id, name=name, no_cache=True)
            if category and name and category.name == name:
                if category.status == dish_pb.DishCategory.DELETE:
                    category.status = dish_pb.DishCategory.ACTIVE
                return category
            elif category and category.status != dish_pb.DishCategory.DELETE:
                raise errors.ShowError("不能创建同名的菜品分类")
            if category and category.status == dish_pb.DishCategory.DELETE:
                category.status = dish_pb.DishCategory.ACTIVE
                return category
        else:
            if name is not None:
                # 如果根据ID查到了分类,但是name和category.name不同,则说明是要改分类名
                name_category = ordering_da.get_category(merchant_id=self.merchant.id, name=name, no_cache=True)
                if name_category and name_category.id != category_id:
                    raise errors.ShowError("修改分类时不能与已有的名字重复")
        if not category:
            if name == "":
                raise errors.ShowError("分类名不能为空")
            category = dish_pb.DishCategory()
            category.name = name
            category.merchant_id = self.merchant.id
            category.id = id_manager.generate_common_id()
        return category

    def reorder_dish_category(self, category_ids):
        """菜品分类排序
        `categories`:
            菜品分类ID列表,(下标+1)为分类sort值.如,[id1,id2],更新后 id1-category.sort=1, id2-category.sort=2
        """
        ordering_da = OrderingServiceDataAccessHelper()
        db_categories = ordering_da.get_categories(merchant_id=self.merchant.id)
        db_categories = {c.id: c for c in db_categories}
        for index, category_id in enumerate(category_ids):
            db_category = db_categories.get(category_id)
            db_category.sort = index + 1
        ordering_da.add_or_update_categories(categories=db_categories, merchant_id=self.merchant.id)

# -*- coding: utf-8 -*-

from business_ops.ordering.base_table_manager import BaseTableManager
from service.errors import ShowError


class NoneExistsTableManager(BaseTableManager):

    whitelist_merchant_id = set([
        "d3348a6659ac401486d53753ea58e8d2",
        "1e543376139b474e97d38d487fa9fbe8"
    ])

    def __init__(self, table_manager=None, *args, **kargs):
        self._manager = table_manager
        if self._manager.merchant.id not in self.whitelist_merchant_id:
            raise ShowError("POS type 错误")
        super(NoneExistsTableManager, self).__init__(*args, **kargs)

    def get_tables(self):
        return []

    def sync_tables(self):
        return []

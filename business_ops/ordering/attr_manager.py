# -*- coding: utf-8 -*-

import logging

import proto.ordering.dish_attr_pb2 as dish_attr_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.dish_attr_da_helper import DishAttrDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class AttrManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(AttrManager, self).__init__(*args, **kargs)

    def generate_dishes_attr_groups(self):
        """把当前数据库的所有菜品的属性进行一次处理,把相同名字的属性组合并"""
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self.merchant.id)
        groups = {}
        group_attrs = {}
        for dish in dishes:
            if dish.status not in [dish_pb.Dish.NORMAL, dish_pb.Dish.GUQING]:
                continue
            for attr in dish.attrs:
                group_name = attr.group_name
                group = groups.get(group_name)
                if not group:
                    group = self.create_group(group_name, is_multi_select=attr.is_multi_select, attr_group_type=attr.type)
                    group.id = attr.group_id
                    groups.update({group_name: group})
                attrs = group_attrs.get(group_name, set())
                o_len = len(attrs)
                attrs.add(attr.name)
                n_len = len(attrs)
                if n_len > o_len:
                    a_attr = group.attrs.add()
                    a_attr.name = attr.name
                    a_attr.id = attr.id
                    a_attr.reprice = attr.reprice
                    a_attr.sort = attr.sort
                    group_attrs.update({group_name: attrs})
        dish_attr_da = DishAttrDataAccessHelper()
        for _, group in groups.items():
            dish_attr_da.add_or_update_attr_group(group)

        self.update_dishes_attr_group(dishes=dishes)

    def update_dishes_attr_group(self, dishes):
        """当有新的菜品从第三方系统同步下来的时候,需要把新的菜品属性相关的信息同步到attr_group中"""
        attr_da = DishAttrDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        groups = attr_da.get_attr_groups(merchant_id=self.merchant.id)
        groups = {group.id: group for group in groups}
        group_id_attrs = {}
        for _, group in groups.items():
            for attr in group.attrs:
                # {"groupId0": {"attrId00": attr00, "attrId01": attr01}}
                # {"groupId1": {"attrId10": attr10, "attrId11": attr11}}
                group_attrs = group_id_attrs.get(group.id, {})
                group_attrs.update({attr.id: attr})

        for dish in dishes:
            for attr in dish.attrs:
                group_id = attr.group_id
                group_name = attr.group_name
                db_group = groups.get(group_id)
                if db_group:
                    group_attrs = group_id_attrs.get(group_id, {})
                    if not group_attrs.get(attr.id):
                        self.add_attr_to_group(db_group, attr.name, attr.reprice, type=attr.type, dish_attr=attr)
                else:
                    db_group = self.create_group(group_name=group_name, attr_group_type=attr.type)
                    db_group.id = attr.group_id
                    self.add_attr_to_group(db_group, attr.name, attr.reprice, type=attr.type, dish_attr=attr)
                    groups.update({db_group.id: db_group})
                    group_id_attrs.update({db_group.id: {attr.id: attr}})
                attr.is_multi_select = db_group.is_multi_select

        for _, group in groups.items():
            attr_da.add_or_update_attr_group(group)

        for dish in dishes:
            ordering_da.add_or_update_dish(dish=dish)

    def update_dishes_attr_group_hualala(self, dishes):
        attr_da = DishAttrDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        groups = attr_da.get_attr_groups(merchant_id=self.merchant.id)
        attrs_dict = {}
        for group in groups:
            for attr in group.attrs:
                key = "{}-{}".format(attr.name, attr.reprice)
                attrs_dict.update(
                    {key: {"group_name": group.name, "group_id": group.id, "type": group.attr_group_type, "id": attr.id}}
                )
        groups = {group.name: group for group in groups}

        for dish in dishes:
            for attr in dish.attrs:
                group_name = attr.group_name
                db_group = groups.get(group_name)
                key = "{}-{}".format(attr.name, attr.reprice)
                group_attr = attrs_dict.get(key)
                if group_attr:
                    attr.id = group_attr.get("id")
                    attr.group_id = group_attr.get("group_id")
                    attr.type = group_attr.get("type")
                    attr.group_name = group_attr.get("group_name")
                    if db_group:
                        attr.is_multi_select = db_group.is_multi_select
                    continue
                if db_group:
                    self.add_attr_to_group(db_group, attr.name, attr.reprice, type=attr.type, dish_attr=attr)
                    attrs_dict.update(
                        {
                            key: {
                                "group_name": db_group.name,
                                "group_id": db_group.id,
                                "type": db_group.attr_group_type,
                                "id": attr.id,
                            }
                        }
                    )
                else:
                    db_group = self.create_group(group_name=group_name, attr_group_type=attr.type)
                    self.add_attr_to_group(db_group, attr.name, attr.reprice, type=attr.type, dish_attr=attr)
                    groups.update({db_group.name: db_group})
                    attrs_dict.update(
                        {
                            key: {
                                "group_name": db_group.name,
                                "group_id": db_group.id,
                                "type": db_group.attr_group_type,
                                "id": attr.id,
                            }
                        }
                    )
        for _, group in groups.items():
            attr_da.add_or_update_attr_group(group)

        for dish in dishes:
            ordering_da.add_or_update_dish(dish=dish)

    def create_group(self, group_name, is_multi_select=None, attr_group_type=None, sort=None):
        group = dish_attr_pb.DishAttrGroup()
        group.id = id_manager.generate_common_id()
        group.merchant_id = self.merchant.id
        if group_name is not None:
            group.name = group_name
        if sort is not None:
            group.sort = sort
        if attr_group_type is not None:
            group.attr_group_type = attr_group_type
        else:
            group.attr_group_type = dish_pb.Attr.TASTE
        if is_multi_select is not None:
            group.is_multi_select = is_multi_select
        else:
            group.is_multi_select = True
        if attr_group_type == dish_pb.Attr.SPECIFICATION:
            group.is_multi_select = False
        return group

    def add_attr_to_group(
        self, group, attr_name, attr_reprice, attr_sort=None, selection_type=None, type=None, dish_attr=None, attr_id=None
    ):
        for attr in group.attrs:
            if attr.name == attr_name and attr.reprice == attr_reprice:
                if dish_attr is not None and dish_attr.id != attr.id:
                    attr.id = dish_attr.id
                return attr
        attr = group.attrs.add()
        attr.id = attr_id or id_manager.generate_common_id()
        attr.name = attr_name
        attr.reprice = attr_reprice
        if dish_attr is not None and attr.name == dish_attr.name and attr.reprice == dish_attr.reprice:
            attr.id = dish_attr.id
        if attr_sort is not None:
            attr.sort = attr_sort
        return attr

    def check_attrs_repeated(self, attrs):
        """更新属性组的时候需要判断 属性名+价格 是惟一的"""
        attr_repeated = set()
        if attrs is None:
            return
        for attr in attrs:
            key = "{}_{}".format(attr.get("name"), attr.get("reprice"))
            if key in attr_repeated:
                raise errors.ShowError("属性名字+价格 不能重复")
            attr_repeated.add(key)

    def add_or_update_attr_group(
        self, group_id, group_name, attrs, is_multi_select=None, update_dish=True, selection_type=None, **kargs
    ):
        """新建或修改一个属性组
        `group_id`: 可以不传,不传的时候根据group_name,attrs,is_multi_select来新建属性组
        `group_name`: 属性组名
        `attrs`: 属性
        `is_multi_select`: 是否支持多选
        `update_dish`: 是否同步更新菜品
        """
        if group_id is None and group_name is None:
            return
        # self.check_attrs_repeated(attrs)
        dish_attr_da = DishAttrDataAccessHelper()
        group = self.get_or_create_attr_group(group_id, group_name)
        self.specification_attr_group_repeated(group)
        if group_name is not None:
            group.name = group_name
        if selection_type is not None:
            group.selection_type.type = dish_attr_pb.AttrSelectionType.Type.Value(selection_type)
            if group.selection_type.type in [
                dish_attr_pb.AttrSelectionType.MULTI_MUST,
                dish_attr_pb.AttrSelectionType.MULTI_NOT_MUST,
            ]:
                group.is_multi_select = True
            elif group.selection_type.type in [dish_attr_pb.AttrSelectionType.SINGLE, dish_attr_pb.AttrSelectionType.NONE]:
                group.is_multi_select = False

        if is_multi_select is not None:
            group.is_multi_select = is_multi_select
        attr_group_type = kargs.get("attr_group_type")
        if attr_group_type is not None:
            attr_group_type = dish_pb.Attr.AttrType.Value(attr_group_type)
            group.attr_group_type = attr_group_type
        attr_status = kargs.get("attr_status", {})
        if attr_status:
            for attr in group.attrs:
                if attr.id in attr_status:
                    attr.status = dish_pb.Attr.Status.Value(attr_status.get(attr.id))

        if attrs is not None:
            _attrs = {a.name: a for a in group.attrs}
            while group.attrs:
                attr = group.attrs.pop()
                key = "{}_{}".format(attr.name, attr.reprice)
                _attrs.update({key: attr})
                if attr_status and attr.id in attr_status:
                    attr.status = dish_pb.Attr.Status.Value(attr_status.get(attr.id))
            for index, attr in enumerate(attrs):
                key = "{}_{}".format(attr.get("name"), attr.get("reprice"))
                name = attr.get("name")
                _attr = _attrs.get(key)
                new_attr = self.add_attr_to_group(
                    group, name, attr.get("reprice", 0), index + 1, selection_type=selection_type, attr_id=attr.get('id')
                )
                if _attr:
                    new_attr.id = _attr.id
                    new_attr.status = dish_pb.Attr.Status.Value(attr.get('status')) if 'status' in attr else _attr.status
        dish_attr_da.add_or_update_attr_group(group)

        if update_dish and group_id:
            ordering_da = OrderingServiceDataAccessHelper()
            dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, attr_group_id=group_id)
            self.update_dishes_attrs(dishes, group_id)

        return group

    def specification_attr_group_repeated(self, group):
        """检查规格属性组是不是重复的"""
        if group.attr_group_type != dish_pb.Attr.SPECIFICATION:
            return
        group_attr_da = DishAttrDataAccessHelper()
        g = group_attr_da.get_attr_group(merchant_id=self.merchant.id, id=group.id)
        if g:
            return
        type = dish_pb.Attr.SPECIFICATION
        g = group_attr_da.get_attr_group(merchant_id=self.merchant.id, type=type)
        if not g:
            return
        raise errors.ShowError("规格属性组只能有一个")

    def update_dish_attr_group(self, dish, attr_group):
        """更新菜品某一个属性组的相关信息
        主要用于某个属性组的被修改之后,需要把相关的所有菜品的属性组的信息都做一次修改
        """
        dish_attrs = set([attr.id for attr in dish.attrs])
        ordering_da = OrderingServiceDataAccessHelper()
        attrs = {a.id: a for a in attr_group.attrs}
        original_attrs = []
        while dish.attrs:
            o_attr = dish.attrs.pop()
            if o_attr.group_id != attr_group.id:
                original_attrs.append(o_attr)
        for o_attr in original_attrs:
            attr = dish.attrs.add()
            attr.CopyFrom(o_attr)
        update = False
        for ag_attr in attr_group.attrs:
            if ag_attr.id not in dish_attrs:
                continue
            update = True
            attr = dish.attrs.add()
            reprice = attrs.get(ag_attr.id).reprice
            sort = attrs.get(ag_attr.id).sort
            attr.reprice = reprice
            attr.sort = sort
            attr.is_multi_select = attr_group.is_multi_select
            attr.type = attr_group.attr_group_type
            attr.id = ag_attr.id
            attr.name = ag_attr.name
            attr.group_id = attr_group.id
            attr.group_name = attr_group.name
            attr.status = ag_attr.status
        if update:
            dish.attrs.sort(key=lambda k: k.sort)
            ordering_da.add_or_update_dish(dish=dish)

    def update_dishes_attrs(self, dishes=None, group_id=None):
        """更新菜品所有属性
        `dishes`: message Dish 列表
        此函数主要用于更新了属性组之后把所有相关菜品的属性相关的内容都进行一次更新
        """
        dish_attr_da = DishAttrDataAccessHelper()
        if not dishes:
            return
        attr_group = dish_attr_da.get_attr_group(merchant_id=self.merchant.id, id=group_id)
        for dish in dishes:
            self.update_dish_attr_group(dish, attr_group)

    def get_or_create_attr_group(self, group_id, group_name, group_type=None):
        """从数据库查询一个attr_group或者创建一个.不保存数据库,需要调用函数自行保存到数据库
        `group_id`: 属性组ID
        `group_name`: 属性组名称
        """
        dish_attr_da = DishAttrDataAccessHelper()
        attr_group = dish_attr_da.get_attr_group(merchant_id=self.merchant.id, id=group_id)
        if attr_group:
            return attr_group
        attr_group = dish_attr_da.get_attr_group(merchant_id=self.merchant.id, group_name=group_name)
        if attr_group:
            return attr_group
        attr_group = self.create_group(group_name, attr_group_type=group_type, is_multi_select=True)
        if group_id is not None:
            attr_group.id = group_id
        return attr_group

    def reorder_attr_groups(self, attr_group_ids):
        """更新属性组模板的排序"""
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_all_dishes(nocache=True, merchant_id=self.merchant.id)

        dish_attr_da = DishAttrDataAccessHelper()
        attr_groups = dish_attr_da.get_attr_groups(merchant_id=self.merchant.id)
        attr_groups = {t.id: t for t in attr_groups}
        for index, attr_group_id in enumerate(attr_group_ids):
            attr_group = attr_groups.get(attr_group_id)
            if not attr_group:
                continue
            attr_group.sort = index + 1
            if attr_group.attr_group_type == dish_pb.Attr.SPECIFICATION:
                attr_group.sort = 0
            for dish in dishes:
                self.update_dish_attr_group(dish, attr_group)

        dish_attr_da.add_or_update_attrs(self.merchant.id, attr_groups)

    def reorder_attrs(self, attr_ids, group_id):
        """属性组下的属性进行排序
        `attr_ids`: 属组ID列表
        `group_id`: 属性组ID
        """
        dish_attr_da = DishAttrDataAccessHelper()
        attr_group = dish_attr_da.get_attr_group(merchant_id=self.merchant.id, id=group_id)
        if not attr_group:
            raise errors.ShowError("属性组不存在")
        attrs = {attr.id: attr for attr in attr_group.attrs}
        for index, attr_id in enumerate(attr_ids):
            attr = attrs.get(attr_id)
            if not attr:
                continue
            attr.sort = index + 1 + attr_group.sort * 1000
        attr_group.attrs.sort(key=lambda k: k.sort)
        dish_attr_da.add_or_update_attr_group(attr_group)

        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_all_dishes(nocache=True, merchant_id=self.merchant.id)
        for dish in dishes:
            self.update_dish_attr_group(dish, attr_group)

    def get_attr_groups(self):
        """获取属性组"""
        dish_attr_da = DishAttrDataAccessHelper()
        attr_groups = dish_attr_da.get_attr_groups(merchant_id=self.merchant.id)
        attr_groups.sort(key=lambda x: x.sort)
        return attr_groups

    # ######时来pos机###########
    def update_attr_groups(self, attrs, attr_selection_dict):
        dish_attr_da = DishAttrDataAccessHelper()
        attr_groups = dish_attr_da.get_attr_groups(merchant_id=self.merchant.id)
        for group in attr_groups:
            while group.attrs:
                group.attrs.pop()
        attr_groups = {group.name: group for group in attr_groups}
        attr_id_set = set()
        is_default = len(list(filter(lambda x: x.sort == -1, attrs))) > 0
        for attr in attrs:
            group = attr_groups.get(attr.group_name)
            if not group:
                group = self.create_attr_group_obj(attr.group_name, attr.group_id)
                attr_groups.update({group.name: group})
            attr.group_id = group.id
            if attr.id in attr_id_set:
                continue
            attr_id_set.add(attr.id)
            selection_type = attr_selection_dict.get(attr.group_name)
            max_num_selections = 1
            min_num_selections = 1
            if selection_type:
                max_num_selections = selection_type.get("max_num_selections", 1)
                min_num_selections = selection_type.get("min_num_selections", 1)
            if max_num_selections == 10000 and min_num_selections == 0:
                group.selection_type.type = dish_attr_pb.AttrSelectionType.MULTI_NOT_MUST
            elif max_num_selections == 1 and min_num_selections == 1 and is_default:
                group.selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE
            elif max_num_selections == 1 and min_num_selections == 0:
                group.selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE_NOT_MUST
            elif max_num_selections == 1 and min_num_selections == 1 and not is_default:
                group.selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE_AND_CHECK
            elif max_num_selections == 10000 and min_num_selections == 1:
                group.selection_type.type = dish_attr_pb.AttrSelectionType.MULTI_MUST
            group.attr_group_type = attr.type
            group_attr = group.attrs.add()
            group_attr.name = attr.name
            group_attr.id = attr.id
            group_attr.reprice = attr.reprice
            group_attr.sort = attr.sort
        for _, group in attr_groups.items():
            dish_attr_da.add_or_update_attr_group(group)

    def create_attr_group_obj(self, group_name, group_id):
        group = dish_attr_pb.DishAttrGroup()
        group.id = group_id
        group.name = group_name
        group.merchant_id = self.merchant.id
        return group

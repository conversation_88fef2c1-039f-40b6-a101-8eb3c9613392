# -*- coding: utf-8 -*-

import logging

import proto.ordering.registration_pb2 as registration_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.ordering.feie_printer_manager import FeiePrinterManager
from business_ops.printer.feie_printer_manager import FeiePrinterManager as NewFeiePrinterManager
from business_ops.ordering.feie_tag_printer_manager import FeieTagPrinterManager
from business_ops.ordering.base_order_manager import BaseOrderManager
from business_ops.ordering.shilai_ops_manager import ShilaiOPSManager
from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.transaction_manager import TransactionManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
from service import error_codes


logger = logging.getLogger(__name__)
printer_manager_map = {
    "v1": FeiePrinterManager,
    "v2": NewFeiePrinterManager
}
printer_version_map = {
    "1": "v1",
    "2": "v2"
}

def get_printer_version(manager):
    version = manager.registration_info.printer_config.feie_printer.print_format_version
    return printer_version_map.get(version, "v2")


def get_printer_manager(manager):
    version = get_printer_version(manager)
    return printer_manager_map.get(version)(
        merchant=manager.merchant,
        registration_info=manager.registration_info
    )


class FeieOrderManager(BaseOrderManager):
    def __init__(self, order_manager, *args, **kargs):
        self._manager = order_manager
        self._partial_refund = kargs.get("partial_refund", False)
        self._shilai_pos_ops = ShilaiOPSManager(
            merchant=order_manager.merchant,
            store=order_manager.store
        )
        self.version = get_printer_version(self._manager)
        self.printer_manager = get_printer_manager(self._manager)
        self.tag_printer_manager = FeieTagPrinterManager(
            merchant=self._manager.merchant, registration_info=self._manager.registration_info)

    def patch_kitchen_print(self, order, table):
        self.printer_manager.kitchen_print_create_order(order, table)
        self.printer_manager.print()

    def patch_checkout_print(self, order, table):
        transaction = TransactionManager().get_transaction_by_id(order.transaction_id)
        self.printer_manager.reception_print_create_order(transaction, order, table)
        self.printer_manager.print()

    def create_ordering_order(self, order):
        pass

    def pay_order(self, **kargs):
        """ 支付成功后调用此函数
        """
        self._partial_refund = kargs.get("partial_refund", self._partial_refund)
        order = self._manager.business_obj.order
        transaction = self._manager.business_obj.transaction
        table = OrderingServiceDataAccessHelper().get_table(ordering_service_table_id=str(order.table_id))
        try:
            self._manager._cal_commission(order=order, transaction=transaction)
            logger.info("云打印计算佣金成功: {} {}".format(order.fanpiao_commission_fee, order.coupon_package_commission_fee))
        except Exception as ex:
            logger.info("云打印计算佣金错误: {}".format(ex))

        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            if not self._partial_refund:
                self.printer_manager.kitchen_print_create_order(order, table)
                if self.version == "v2":
                    self.printer_manager.print(choice_type="kitchen")
                    self.printer_manager.contents = []
            self._manager._calculate_paid_in_fee(self._manager.business_obj)
            self._manager.cal_wallet_pay_commission_fee(order, transaction)
            self._manager.cal_coupon_commission_fee(order, transaction)
            self.printer_manager.reception_print_create_order(transaction, order, table)
            if not self._partial_refund:
                self.tag_printer_manager.pay_success(order, transaction, table)
            self.sync_ordering_order(order, transaction)
        elif self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            self._manager._calculate_paid_in_fee(self._manager.business_obj)
            self._manager.cal_wallet_pay_commission_fee(order, transaction)
            self._manager.cal_coupon_commission_fee(order, transaction)
            FeiePrinterManager(merchant=self._manager.merchant, registration_info=self._manager.registration_info).pay_success(order, transaction, table)
            if order.status == dish_pb.DishOrder.PAID:
                if not self._partial_refund:
                    self.tag_printer_manager.pay_success(order, transaction, table)

        # 部分退菜时不用出单
        if not self._partial_refund:
            self.printer_manager.print(choice_type="checkout")
            self.tag_printer_manager.print()
        return True

    def add_dish_ordering_order(self, dishes, order, user_id):
        products = order.add_products[-1].products
        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            order_da = OrderingServiceDataAccessHelper()
            table = order_da.get_table(ordering_service_table_id=str(order.table_id))
            self.printer_manager.kitchen_print_add_product(order, products, table)
            self.printer_manager.reception_print_add_product(order, products, table)
            order_da.add_or_update_order(order=order)

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        if not order:
            raise errors.OrderNotFound()
        if order.status == dish_pb.DishOrder.POS_RETURN:
            raise errors.Error(err=error_codes.ORDER_POS_RETURN)
        if order.status not in [
                dish_pb.DishOrder.PAID,
                dish_pb.DishOrder.REFUNDING
        ]:
            raise errors.Error(err=error_codes.ORDER_NOT_PAID)
        refund_transaction = self._manager.pos_return_order(transaction_id=order.transaction_id)
        if kargs.get("refund_print"):
            order_da = OrderingServiceDataAccessHelper()
            table = order_da.get_table(ordering_service_table_id=str(order.table_id))
            self.printer_manager.is_refund = True
            self.printer_manager.kitchen_print_create_order(order, table)
            self.printer_manager.print(
                choice_type="kitchen",
                print_times=1,
                refund_print=True
            )
        return refund_transaction

    def __sync_order_to_pos(self, order, transaction, source, pay_source):
        uri = ShilaiPosConstants.SYNC_DIRECT_PAY_ORDER
        url = self._shilai_pos_ops.generate_url(uri)
        pay_method_name = TransactionManager.get_transaction_pay_method_name(transaction)
        speak_fee = order.paid_in_fee + order.fanpiao_commission_fee + \
            order.coupon_package_commission_fee
        params = {
            "order": {
                "merchantId": order.merchant_id,
                "storeId": order.store_id,
                "billFee": order.bill_fee,
                "paidFee": order.paid_fee,
                "paidTime": order.paid_time,
                "id": order.id,
                "source": source,
                "paySource": pay_source,
                "payMethodName": pay_method_name,
                "posType": "FEIE",
                "totalReceivableFee": order.paid_in_fee
            },
            "speakFee": speak_fee
        }
        self._shilai_pos_ops.do_post(url, params)
        prefix = self.__get_voice_broadcast_prefix(transaction)
        if order.meal_type == dish_pb.DishOrder.DIRECT_PAY:
            self._shilai_pos_ops.voice_broadcast(
                fee=speak_fee, type=self._shilai_pos_ops.PAY_SUCCESS, prefix=prefix)
        return None

    def __get_voice_broadcast_prefix(self, transaction):
        if transaction.pay_method in [
                wallet_pb.Transaction.FANPIAO_PAY,
                wallet_pb.Transaction.WALLET
        ]:
            return "智能营销收款"
        if transaction.use_coupon_id != "":
            return "智能营销收款"
        return None

    def sync_ordering_order(self, order, transaction):
        source = "MINI_PROGRAM"
        pay_source = "MINI_PROGRAM"
        return self.__sync_order_to_pos(order, transaction, source, pay_source)

    def sync_direct_pay_order(self, order, transaction):
        source = "NUMBER_PLATE_PAY"
        pay_source = "NUMBER_PLATE_PAY"
        return self.__sync_order_to_pos(order, transaction, source, pay_source)

# -*- coding: utf-8 -*-

import logging
import time
from urllib.parse import urlparse

import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.base_dish_manager import BaseDishManager
from common.aliyun_oss_helper import AliyunOSSHelper
from common.constant import const
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper

logger = logging.getLogger(__name__)


class KeruyunDishManager(KeruyunOPSManager, BaseDishManager):

    def __init__(self, dish_manager, *args, **kargs):
        self._manager = dish_manager
        super(KeruyunDishManager, self).__init__(*args, **kargs)

    def get_saved_dish(self, dish_id=None, dish_brand_id=None):
        ordering_da = OrderingServiceDataAccessHelper()
        dish = None
        if dish_id is not None:
            dish_id = str(dish_id)
            dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, dish_id=dish_id, no_cache=True)
            if not dish:
                return None
        if dish_brand_id is not None:
            dish_brand_id = str(dish_brand_id)
            dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, dish_brand_id=dish_brand_id, no_cache=True)
            if not dish:
                return None
        return dish

    def create_dish_obj(self, keruyun_dish, saved_dish):
        dish_obj = dish_pb.Dish()
        self._manager.set_dish_default_value(dish_obj, saved_dish)
        dish_obj.dish_brand_id = str(keruyun_dish.get("brandDishId"))
        dish_obj.id = str(keruyun_dish.get("id"))
        dish_obj.name = keruyun_dish.get("name")
        dish_obj.merchant_id = self._manager.merchant.id
        dish_obj.price = keruyun_dish.get("price")
        dish_obj.dish_brand_id = str(keruyun_dish.get("brandDishId"))
        dish_obj.min_order_num = keruyun_dish.get("minOrderNum")
        dish_obj.sort = keruyun_dish.get("rank")
        dish_obj.unit = keruyun_dish.get("unit")
        dish_obj.uuid = keruyun_dish.get("uuid")
        dish_obj.sale_type = int(keruyun_dish.get("saleType"))
        dish_obj.box_qty = int(keruyun_dish.get("boxQty"))

        for category in keruyun_dish.get("categorys"):
            dish_obj.categories.append(str(category.get("categoryId")))
        return dish_obj

    def set_dish_image(self, dish_obj, saved_dish, images):
        if images != "":
            images = images.split(",")
            image = images[0]
            i = image.split("?")
            image = i[0]
        else:
            return

        if saved_dish and len(saved_dish.images) > 0:
            old_image = saved_dish.images[0]
            old_image_url_parser = urlparse(old_image)
            old_filename = old_image_url_parser.path.split("/")[-1]
            image_url_parser = urlparse(image)
            filename = image_url_parser.path.split("/")[-1]
            if image_url_parser.netloc == const.ALIYUN_OSS_SHILAI_DOMAIN and filename == old_filename:
                dish_obj.images.append(old_image)
                # return
        aliyun_oss_helper = AliyunOSSHelper()
        try:
            image_obj = aliyun_oss_helper.upload_image_network_stream(image)
        except:
            return
        # 按比例把图片压缩到1M以内
        aliyun_oss_helper.try_to_resize(image_obj, model="m_lfit")
        target_name = "thumb-{}".format(image_obj.name)
        thumb_image_obj = aliyun_oss_helper.resize(image_obj.name, target_name, model="m_lfit", w=160, h=160)
        dish_obj.images.append(image_obj.url)
        dish_obj.thumb_image = thumb_image_obj.url

    def discount_dish(self, saved_dish, dish_obj):
        name = dish_obj.name
        if "-" in name:
            n = name.split("-")
        elif "－" in name:
            n = name.split("－")
        else:
            return
        if len(n) == 2 and n[1] == "N折":
            dish_obj.is_discount_dish = True
            dish_obj.discount_rate = 100 - int(n[0])

    def deal_with_combo_meal(self, saved_dish, category_dish, dish_obj):
        dish_setmeal_groups = category_dish.get("dishSetmealGroups")
        if not dish_setmeal_groups or len(dish_setmeal_groups) == 0:
            return
        for dish_group in dish_setmeal_groups:
            dish_group_vo = dish_obj.child_dish_groups.add()
            dish_group_vo.group_name = dish_group.get("name")
            dish_group_vo.order_min = dish_group.get("orderMin")
            dish_group_vo.order_max = dish_group.get("orderMax")
            dish_group_vo.sort = int(dish_group.get("sort"))
            dish_group_vo.id = str(dish_group.get("id"))
            child_dishes = dish_group.get("dishSetmealGroupChildInfos")
            if not child_dishes or len(child_dishes) == 0:
                continue
            for child_dish in child_dishes:
                child_dish_vo = dish_group_vo.child_dishes.add()
                child_dish_vo.id = str(child_dish.get("id"))
                child_dish_vo.name = child_dish.get("name")
                child_dish_vo.price = child_dish.get("price") * 100
                child_dish_vo.sort = child_dish.get("sort")
                child_dish_vo.is_must = True if child_dish.get("isReplace", 2) == 1 else False
            dish_group_vo.child_dishes.sort(key=lambda k: k.sort)

    def set_attrs(self, dish_obj, attrs, saved_dish):
        if not attrs:
            return
        attr_groups = {}
        attr_dict = {}
        if saved_dish:
            attr_dict = {attr.id: attr for attr in saved_dish.attrs}
            for attr in saved_dish.attrs:
                if attr.is_multi_select:
                    attr_groups.update({attr.group_id: True})
                else:
                    attr_groups.update({attr.group_id: False})
        for index, attr in enumerate(attrs):
            attr_vo = dish_obj.attrs.add()
            attr_vo.id = str(attr.get("id"))
            attr_vo.name = attr.get("name")
            attr_vo.reprice = attr.get("reprice")
            attr_vo.type = attr.get("type")
            group_name = attr.get("groupName")
            if group_name is not None:
                attr_vo.group_name = attr.get("groupName")
            group_id = attr.get("groupId")
            if group_id is not None:
                attr_vo.group_id = str(attr.get("groupId"))
            if attr_vo.type == dish_pb.Attr.REMARK:
                attr_vo.group_name = "备注"
                attr_vo.group_id = '{}-{}-{}'.format(self._manager.merchant.id, "dish", "remark")
            if attr_vo.type == dish_pb.Attr.TAG:
                attr_vo.group_name = "标签"
                attr_vo.group_id = '{}-{}-{}'.format(self._manager.merchant.id, "dish", "tag")
            attr_vo.sort = index + 1
            dish_attr = attr_dict.get(attr_vo.id, None)
            if dish_attr:
                attr_vo.status = dish_attr.status
            attr_vo.is_multi_select = attr_groups.get(attr_vo.group_id, True)

    def set_supply_condiments(self, dish_obj, supply_condiments, saved_dish):
        if not supply_condiments:
            return
        supply_condiment_dict = {}
        if saved_dish:
            supply_condiment_dict = {s.id: s for s in saved_dish.supply_condiments}
        for supply_condiment in supply_condiments:
            supply_condiment_vo = dish_obj.supply_condiments.add()
            supply_condiment_vo.id = str(supply_condiment.get("id"))
            supply_condiment_vo.market_price = supply_condiment.get("marketPrice")
            supply_condiment_vo.name = supply_condiment.get("name")
            dish_supply_condiment = supply_condiment_dict.get(supply_condiment_vo.id)
            if dish_supply_condiment:
                supply_condiment_vo.status = dish_supply_condiment.status

    def set_sale_times(self, dish_obj, sale_times):
        for sale_time in sale_times:
            start = sale_time.get("start").split(":")
            end = sale_time.get("end").split(":")
            start = int(start[0]) * 60 * 60 + int(start[1]) * 60 + int(start[2])
            end = int(end[0]) * 60 * 60 + int(end[1]) * 60 + int(end[2])
            if start == end:
                continue
            if start > end:
                sale_time = dish_obj.sale_times.add()
                sale_time.start = start
                sale_time.end = 86399
                sale_time_2 = dish_obj.sale_times.add()
                sale_time_2.start = 0
                sale_time_2.end = end
            else:
                sale_time = dish_obj.sale_times.add()
                sale_time.start = start
                sale_time.end = end

    def deal_with_single_meal(self, saved_dish, keruyun_dish, dish_obj):
        self.set_attrs(dish_obj, keruyun_dish.get("attrs"), saved_dish)
        self.set_supply_condiments(dish_obj, keruyun_dish.get("supplyCondiments"), saved_dish)
        if keruyun_dish.get("clearStatus") == 1:
            dish_obj.status = dish_pb.Dish.NORMAL
        else:
            dish_obj.status = dish_pb.Dish.GUQING

    def ensure_dish_id(self, saved_dish, dish_id, dish_brand_id, category_dish):
        if saved_dish:
            return saved_dish.id
        if category_dish:
            dish_id = category_dish.get("id")
        if dish_id is not None:
            return dish_id
        saved_dish = self.get_saved_dish(dish_brand_id=dish_brand_id)
        if saved_dish:
            return saved_dish.id
        return None

    def sync_dish(self, saved_dish=None, dish_id=None, dish_brand_id=None, category_dish=None):
        """
        从客如云同步菜品
        """
        dish_id = self.ensure_dish_id(saved_dish, dish_id, dish_brand_id, category_dish)
        if not saved_dish:
            saved_dish = self.get_saved_dish(dish_brand_id=dish_brand_id, dish_id=dish_id)
        if saved_dish and saved_dish.status == dish_pb.Dish.OFFLINE:
            logger.info("菜品 {} 处于OFFLINE状态,不更新".format(saved_dish.name))
            return
        keruyun_dish = self.get_dish_by_id_from_keruyun(dish_id)
        if not keruyun_dish:
            logger.info("菜品不存在: {}".format(dish_id))
            return None
        keruyun_dish = keruyun_dish[0]
        dish_obj = self.create_dish_obj(keruyun_dish, saved_dish)
        self.set_dish_image(dish_obj, saved_dish, keruyun_dish.get("imgUrl"))
        self.discount_dish(saved_dish, dish_obj)
        dish_obj.type = int(keruyun_dish.get("type"))
        if dish_obj.type == dish_pb.Dish.COMBO_MEAL:
            self.deal_with_combo_meal(saved_dish, category_dish, dish_obj)
        elif dish_obj.type == dish_pb.Dish.SINGLE:
            self.deal_with_single_meal(saved_dish, keruyun_dish, dish_obj)
        else:
            logger.info("菜品 '{}' 不符合要求".format(dish_obj.name))
            return None
        self.set_sale_times(dish_obj, keruyun_dish.get("saleTimes"))

        OrderingServiceDataAccessHelper().add_or_update_dish(dish=dish_obj)
        return dish_obj

    def async_categories(self, names=None, dish_type_ids=None):
        categories = self.get_all_dish_categories()
        ret = []
        for category in categories:
            category_template = self.create_shilai_dish_category_template()
            category_template.update({
                "name": category.get("name"),
                "id": str(category.get("id")),
                "sort": int(category.get("sort")),
                "parent_id": category.get("parentId"),
                "level": int(category.get("level")),
                "third_party_category_id": str(category.get("id"))
            })
            ret.append(category_template)
        return ret

    def get_category_dishes(self, category_id):
        json_body = {"dishTypeId": category_id}
        uri = KeruyunConstants.KERUYUN_DISH_DISH_NEW
        token = self._manager.registration_info.keruyun_pos_info.token
        resp = self.post(token=token, uri=uri, json_body=json_body).json()
        if resp.get('code') == 0:
            return resp.get("result")
        return None

    def get_all_dish(self):
        """ 同步脚本调用
        返回所有菜品
        """
        categories = self.get_all_dish_categories()
        all_dish = []
        for category in categories:
            dishes = self.get_category_dishes(category.get("id"))
            dish_list = dishes.get("dishList", [])
            if dish_list:
                all_dish.extend(dish_list)
        return all_dish

    def get_all_dish_categories(self):
        """ 同步脚本调用
        所有菜品分类,包括父分类
        """
        uri = KeruyunConstants.KERUYUN_DISH_CATEGORY_ALL
        token = self._manager.registration_info.keruyun_pos_info.token
        resp = self.post(token=token, uri=uri).json()
        result = []
        if resp.get("code") == 0:
            categories = resp.get("result")
            return categories
        return result

    def change_dish_category_status(self, category_id, status):
        ordering_da = OrderingServiceDataAccessHelper()
        db_category = ordering_da.get_category(merchant_id=self._manager.merchant.id, id=str(category_id))
        if db_category and db_category.status in [dish_pb.DishCategory.INACTIVE, dish_pb.DishCategory.DELETE]:
            status = dish_pb.DishCategory.Status.Name(db_category.status)
            logger.info("'{}: {}' 该分类状态为 {},不用同步".format(db_category.id, db_category.name, status))
            return
        ordering_da.update_category(id=str(category_id), status=status, merchant_id=self._manager.merchant.id)

    def try_to_update_dish_supply_condiments(self, dish_name=None, status=None):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = []
        if dish_name is not None:
            dishes = ordering_da.get_dishes(merchant_id=self._manager.merchant.id, supply_condiment_name=dish_name)
        if len(dishes) == 0:
            return
        ret = []
        for dish in dishes:
            dish = self._manager.get_dish_from_cache(dish.id)
            for supply_condiment in dish.supply_condiments:
                if dish_name is not None and supply_condiment.name == dish_name:
                    logger.info("修改菜品配料状态为: {}, {}, {}".format(
                        dish.name, supply_condiment.name, dish_pb.SupplyCondiment.Status.Name(status)))
                    supply_condiment.status = status
            ret.append(dish)
        ordering_da.add_or_update_dishes(dishes=ret, merchant_id=self._manager.merchant.id)

    def try_to_update_dish_attrs(self, dish_name=None, status=None):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = []
        if dish_name is not None:
            dishes = ordering_da.get_dishes(merchant_id=self._manager.merchant.id, attr_name=dish_name)
        if len(dishes) == 0:
            return
        ret = []
        for dish in dishes:
            for attr in dish.attrs:
                if attr.name == dish_name:
                    logger.info("修改菜品 {} 属性 {} 状态为 {}".format(
                        dish.name, attr.name, dish_pb.Attr.Status.Name(status)))
                    attr.status = status
            ret.append(dish)
        ordering_da.add_or_update_dishes(dishes=ret, merchant_id=self._manager.merchant.id)

    def change_dish_status(self, dish_id, status):
        self._manager.load_dish()
        dish = self._manager.get_dish_from_cache(dish_id)
        if not dish:
            logger.info("菜品不存在,直接返回: {}".format(dish_id))
            return False
        if status == dish_pb.Dish.GUQING:
            self.try_to_update_dish_supply_condiments(dish_name=dish.name, status=dish_pb.SupplyCondiment.SOLD_OUT)
            self.try_to_update_dish_attrs(dish_name=dish.name, status=dish_pb.Attr.SOLD_OUT)
        elif status == dish_pb.Dish.NORMAL:
            self.try_to_update_dish_supply_condiments(dish_name=dish.name, status=dish_pb.SupplyCondiment.NORMAL)
            self.try_to_update_dish_attrs(dish_name=dish.name, status=dish_pb.Attr.NORMAL)
        if dish.status == dish_pb.Dish.OFFLINE:
            logger.info("菜品是下线状态,不做处理: {}".format(dish_id))
            return False
        from_status = dish_pb.Dish.Status.Name(dish.status)
        to_status = dish_pb.Dish.Status.Name(status)
        logger.info('修改"{}"状态: from {} to {}, 商户ID: {}'.format(
            dish.name, from_status, to_status, self._manager.merchant.id))
        dish.status = status
        OrderingServiceDataAccessHelper().add_or_update_dish(dish=dish)

    def change_brand_dish_status(self, dish_brand_id, status):
        ordering_da = OrderingServiceDataAccessHelper()
        dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, dish_brand_id=str(dish_brand_id))
        if not dish:
            logger.info("菜品不存在,直接返回: {}".format(dish_brand_id))
            return False
        if dish.status == dish_pb.Dish.OFFLINE:
            logger.info("菜品是下线状态,不做处理: {}".format(dish_brand_id))
            return False
        from_status = dish_pb.Dish.Status.Name(dish.status)
        to_status = dish_pb.Dish.Status.Name(status)
        logger.info("修改'{}'状态 from {} to {}".format(dish.name, from_status, to_status))
        dish.status = status
        ordering_da.add_or_update_dish(dish=dish)

    def get_dish_by_id_from_keruyun(self, dish_id):
        """ 同步脚本调用
        从客如云获取所有菜品
        """
        start = time.time()
        shop_id = self._manager.registration_info.keruyun_pos_info.shop_id
        json_body = {
            "shopIdenty": shop_id,
            "ids": [dish_id]
        }
        uri = KeruyunConstants.KERUYUN_DISH_DISH_MENU_BY_IDS
        token = self._manager.registration_info.keruyun_pos_info.token
        resp = self.post(token=token, uri=uri, json_body=json_body).json()
        logger.info("get dish from keruyun: {}".format(time.time() - start))
        if resp.get('code') == 0:
            dish = resp.get("result")
            return dish
        return None

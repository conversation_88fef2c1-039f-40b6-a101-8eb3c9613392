# -*- coding: utf-8 -*-

from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.ordering.shilai_ops_manager import ShilaiOPSManager


class FeieShopManager:
    def __init__(self, shop_manager=None, *args, **kargs):
        self._manager = shop_manager
        self._ops = ShilaiOPSManager(merchant=shop_manager.merchant, store=shop_manager.store)

    def add_or_update_merchant(self):
        uri = ShilaiPosConstants.ADD_OR_UPDATE_MERCHANT
        url = self._ops.generate_url(uri)
        json_data = {
            "brandName": self._manager.merchant.basic_info.name,
            "name": self._manager.merchant.basic_info.name,
            "migration": True,
        }
        json_data.update(self._ops.generate_base_params())
        self._ops.do_post(url, json_data)

    def add_or_update_store(self, registration_info=None):
        uri = ShilaiPosConstants.ADD_OR_UPDATE_STORE
        url = self._ops.generate_url(uri)
        json_data = {"name": self._manager.merchant.basic_info.name, "storeId": self._manager.store.id}
        if registration_info:
            json_data.update({'baseNumber': registration_info.serial_number_base_value})
        json_data.update(self._ops.generate_base_params())
        self._ops.do_post(url, json_data)

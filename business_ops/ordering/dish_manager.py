# -*- coding: utf-8 -*-


"""
时来菜品类,该类应该持有其它收银系统菜品类
"""

import inspect
import logging
import os
import time
import urllib.request
import random
import ssl
import hashlib
from collections import namedtuple, defaultdict
from datetime import datetime
from datetime import timedelta

from pytz import timezone
from google.protobuf import json_format
import requests

import proto.config_pb2 as config_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.dish_attr_pb2 as dish_attr_pb
import proto.page.dish_pb2 as page_dish_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.common.datetime_pb2 as datetime_pb
from business_ops.ordering.base_dish_manager import BaseDishManager
from business_ops.config_manager import ConfigManager
from business_ops.ordering.keruyun_dish_manager import KeruyunDishManager
from business_ops.ordering.shilai_dish_manager import ShilaiDishManager
from business_ops.ordering.hualala_dish_manager import HualalaDishManager
from business_ops.ordering.feie_dish_manager import FeieDishManager
from business_ops.ordering.none_exists_dish_manager import NoneExistsDishManager
from business_ops.ordering.attr_manager import AttrManager
from business_ops.ordering.supply_condiment_manager import SupplyCondimentManager
from business_ops.printer.printer_config_manager import PrinterConfigManager
from business_ops import constants
from common.aliyun_oss_helper import AliyunOSSHelper
from common.utils import date_utils
from common.utils import id_manager
from common.utils.zip_helper import Compress
from common.config import config
from common.cache_server_keys import CacheServerKeys
from common.protobuf_transformer import copy_obj_from_map
from cache.redis_client import RedisClient
from common import constants as common_constants
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from dao.ordering.dish_attr_da_helper import DishAttrDataAccessHelper
from dao.ordering.dish_supply_condiment_da_helper import DishSupplyCondimentDataAccessHelper
from strategy import order_discount_strategy
from service import errors
from service import error_codes
from cache.dish_cache import DishCatalogCache

logger = logging.getLogger(__name__)


class DishManager(BaseDishManager):
    MON = 1 << 0
    TUE = 1 << 1
    WED = 1 << 2
    THU = 1 << 3
    FRI = 1 << 4
    SAT = 1 << 5
    SUN = 1 << 6

    def __init__(self, *args, **kargs):
        super(DishManager, self).__init__(*args, **kargs)
        self.set_dish_manager()
        self.discount_plan = None
        self.dishes = {}
        self.dish_attrs_set_flag = False
        self.dish_supply_condiments_set_flag = False
        self.dish_attr_groups = {}
        self.dish_attrs = {}
        self.dish_supply_condiments = {}

    def set_dish_manager(self):
        if not self.store.enable_ordering_service:
            return
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            self.dish_manager = ShilaiDishManager(dish_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            self.dish_manager = KeruyunDishManager(dish_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            self.dish_manager = FeieDishManager(dish_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            self.dish_manager = HualalaDishManager(dish_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.NONE_EXISTS:
            self.dish_manager = NoneExistsDishManager(dish_manager=self)
        else:
            raise errors.Error(err=error_codes.POS_NOT_SUPPORTED)

    def set_dish_attrs(self):
        if self.dish_attrs_set_flag:
            return
        attr_da = DishAttrDataAccessHelper()
        attr_groups = attr_da.get_attr_groups(merchant_id=self.merchant.id)
        for attr_group in attr_groups:
            self.dish_attr_groups.update({attr_group.id: attr_group})
            for attr in attr_group.attrs:
                self.dish_attrs.update({attr.id: attr})
        self.dish_attrs_set_flag = True

    def set_dish_supply_condiments(self):
        if self.dish_supply_condiments_set_flag:
            return
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        supply_condiments = supply_condiment_da.get_supply_condiments(merchant_id=self.merchant.id)
        for supply_condiment in supply_condiments:
            self.dish_supply_condiments.update({supply_condiment.id: supply_condiment})
        self.dish_supply_condiments_set_flag = True

    def load_dish(self):
        if self.dishes:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_all_dishes(merchant_id=self.merchant.id)
        self.dishes.update({dish.get("id"): dish for dish in dishes})

    def get_discount_plan(self, user_id, enbale_sale_time_discount=False):
        if self.discount_plan:
            return
        if self.registration_info:
            dinner_type = self.registration_info.ordering_config.dinner_type
            pos_type = self.registration_info.pos_type
            if (
                pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI
                and dinner_type == registration_pb.OrderingConfig.DINNER
            ):
                return
        self.discount_plan = order_discount_strategy.generate_discount_plan_v2(
            self.merchant, user_id, self.dishes, enbale_sale_time_discount=enbale_sale_time_discount
        )

    def get_category_of_merchant(self):
        dishes = OrderingServiceDataAccessHelper().get_dishes(merchant_id=self.merchant.id)
        category_mapping = {
            c.id: c.name
            for c in OrderingServiceDataAccessHelper().get_categories(
                merchant_id=self.merchant.id, status=dish_pb.DishCategory.ACTIVE
            )
        }
        result = list()
        for dish in dishes:
            for category in dish.categories:
                result.append({'dishId': dish.id, 'categoryId': category, 'categoryName': category_mapping.get(category) or ''})
        return result

    def get_dish_remain_quantity_map(self):
        def from_feie_cache(dish):
            remain_quantity = dish.get("remainQuantity", 0)
            enable_quantity_setting = dish.get("enableQuantitySetting", False)
            available_quantity = dish.get("availableQuantity", 0)
            if not enable_quantity_setting:
                return {"remainQuantity": 0, "enableQuantitySetting": False, "availableQuantity": 0}
            client = RedisClient().get_connection()
            key = CacheServerKeys.get_dish_remain_quantity_key(dish["id"])
            cache_remain_quantity = client.get(key)
            if cache_remain_quantity:
                remain_quantity = int(cache_remain_quantity)
            else:
                client.setnx(key, remain_quantity)
            return {
                "remainQuantity": remain_quantity,
                "enableQuantitySetting": enable_quantity_setting,
                "availableQuantity": available_quantity,
            }

        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            if hasattr(self.dish_manager, "get_dishes_changeful_info"):
                return self.dish_manager.get_dishes_changeful_info() or {}
            return {}

        dishes = OrderingServiceDataAccessHelper().get_all_dishes(merchant_id=self.merchant.id)
        if not dishes:
            return {}
        return {dish['id']: from_feie_cache(dish) for dish in dishes}

    def reflush_dish_remain_quantity(self, dish, dish_remain_map: dict):
        for group in dish.get("childDishGroups", []):
            for child_dish in group.get("childDishes", []):
                self.reflush_dish_remain_quantity(child_dish, dish_remain_map)
        remain_quantity = dish_remain_map.get(dish["id"], {}).get("remainQuantity", 0)
        enable_quantity_setting = dish_remain_map.get(dish['id'], {}).get("enableQuantitySetting", False)
        if enable_quantity_setting and remain_quantity <= 0:
            dish['status'] = dish_pb.Dish.Status.Name(dish_pb.Dish.Status.GUQING)
        dish["enableQuantitySetting"] = enable_quantity_setting
        dish["remainQuantity"] = remain_quantity

    @DishCatalogCache.cache
    def get_dish_catalog(self, *args, **kargs):
        merchant_id = kargs.pop("merchant_id", None)
        if merchant_id is None:
            raise errors.Error(errcode=error_codes.INVALID_REQUEST_DATA, errmsg=error_codes.INVALID_REQUEST_DATA_MSG)
        dishes = self.dish_catalog(*args, **kargs)
        return json_format.MessageToDict(dishes, including_default_value_fields=True)

    def dish_catalog(self, user_id, meal_type, show_all=False, table_id=None, enable_sale_time=False):
        if not self.store.enable_ordering_service:
            raise errors.MerchantNotEnableDishOrder()
        dishes_changeful_info = {}
        if hasattr(self.dish_manager, "get_dishes_changeful_info"):
            dishes_changeful_info = self.dish_manager.get_dishes_changeful_info()
        self.load_dish()
        ordering_da = OrderingServiceDataAccessHelper()
        self.get_discount_plan(user_id, enbale_sale_time_discount=True)
        status = dish_pb.DishCategory.ACTIVE
        categories = ordering_da.get_categories(merchant_id=self.merchant.id, status=status)
        categories = {category.id: category for category in categories}
        catalog_vo = page_dish_pb.DishCatalog()
        table = self._set_table_info(catalog_vo, meal_type)
        if table_id is not None:
            table = ordering_da.get_table(id=table_id)
        dishes = self._set_catalog_dishes(
            catalog_vo,
            categories,
            meal_type,
            show_all=show_all,
            dishes_changeful_info=dishes_changeful_info,
            table=table,
            enable_sale_time=enable_sale_time,
        )
        self._groupify_dishes(dishes, categories, catalog_vo)
        self._set_red_packet_discount_config(catalog_vo)
        self._set_packaging_box_info(catalog_vo)
        self._sort_dish_catalog(catalog_vo)
        self._set_catalog_base_info(catalog_vo)
        self._set_promotion_config(catalog_vo)
        return catalog_vo

    def _set_promotion_config(self, catalog_vo):
        # 限时折扣
        promotion_configs = ConfigDataAccessHelper().get_promotion_configs(
            merchant_id=self.merchant.id, type=config_pb.PromotionConfig.TypeEnum.SALE_TIME_DISCOUNT
        )
        if not promotion_configs:
            return
        for config in promotion_configs:
            promotion_config_vo = catalog_vo.sale_time_promotion_configs.add()
            promotion_config_vo.CopyFrom(config)

    def _set_catalog_base_info(self, catalog_vo):
        if self.user:
            catalog_vo.phone = self.user.member_profile.mobile_phone
        catalog_vo.enable_recommend_dish = self.registration_info.recommend_dish.enable_recommend_dish
        catalog_vo.splash_image_url = self.store.splash_image_url
        catalog_vo.minimal_bill_fee = self.registration_info.ordering_config.minimal_bill_fee
        catalog_vo.enable_eat_in = self.registration_info.ordering_config.enable_eat_in
        catalog_vo.enable_take_away = self.registration_info.ordering_config.enable_take_away
        catalog_vo.enable_group_dining = self.registration_info.ordering_config.enable_group_dining
        catalog_vo.diner_uplimit = self.registration_info.ordering_config.diner_uplimit or 16

    def _sort_dish_catalog(self, catalog_vo):
        # 先根据菜品分类排序,再在每个分类下面根据菜品排序
        catalog_vo.dishes.sort(key=lambda x: x.category.sort)
        for dishes_vo in catalog_vo.dishes:
            dishes_vo.dish_list.sort(key=lambda x: x.sort)

    def _set_catalog_dishes(
        self, catalog_vo, categories, meal_type, show_all=False, dishes_changeful_info=None, table=None, enable_sale_time=False
    ):
        dishes = []
        # ordering_config = ConfigDataAccessHelper().get_ordering_config(merchant_id=self.merchant.id)
        redis_client = RedisClient().get_connection()
        for dish_id, dish in self.dishes.items():
            dish = self._dish_parse_dict(dish)
            if len(dish.categories) == 0:
                continue
            category_id = dish.categories[0]
            if category_id not in categories:
                continue
            category = categories.get(category_id)
            if category.status != dish_pb.DishCategory.ACTIVE:
                continue
            self.set_dish_status_as_pos(dish, dishes_changeful_info=dishes_changeful_info)
            self.try_to_set_dish_sold_out(redis_client, dish, dishes_changeful_info=dishes_changeful_info)
            if dish.status not in [dish_pb.Dish.NORMAL, dish_pb.Dish.GUQING] and show_all is False:
                continue
            # TODO: 如果余量已经为0则把菜品设置为估清
            if not self.store.enable_show_sold_out_dishes and dish.status == dish_pb.Dish.GUQING and show_all is False:
                continue
            dish_vo = self._convert_dish_vo(dish, category=category, dishes_changeful_info=dishes_changeful_info)
            on_sale = self._check_dish_sale_time(dish, dish_vo)
            # if not enable_sale_time and not on_sale and show_all is False:  # 如果不在售卖时间段
            #     if not ordering_config:
            #         # 如果没有ordering_config的配置,则直接不返回这个菜品
            #         continue
            #     if ordering_config and not ordering_config.enable_show_time_limit_sale:
            #         # 如果有配置ordernig_config,则根据配置来决定是否返回这个菜品
            #         continue
            if str(dish_vo.category_id) in self.registration_info.ordering_config.reminder_categories:
                catalog_vo.reminder_categories.update({category.id: category.name})
            dishes.append(dish_vo)

        self._required_dish_config(catalog_vo, meal_type, table=table)
        return dishes

    def set_dish_status_as_pos(self, dish, dishes_changeful_info, is_child_dish=False):
        """因为收银机端菜品状态与小程序端的菜品状态不同步,并且暂时查不到原因,所以先以收银机端菜品状态为准
        TODO: 当查明此问题后应不再调用此函数
        """
        if dishes_changeful_info is None:
            return
        dish_changeful_info = dishes_changeful_info.get(dish.id)
        if not dish_changeful_info:
            return
        if dish.status in [dish_pb.Dish.DISABLE, dish_pb.Dish.OFFLINE, dish_pb.Dish.NOT_IN_TIME_LIMIT_SALE]:
            return
        old_status = dish.status
        # dish_status = dish_pb.Dish.Status.Name(dish.status)
        status = dish_changeful_info.get("status")
        # logger.info(f"收银机端菜品与小程序端菜品状态: {dish.id} {dish.name} {dish_status} {status} 是否套餐子菜: {is_child_dish}")
        if status == "SOLD_OUT":
            dish.status = dish_pb.Dish.GUQING
        elif status == "ON_SALE":
            dish.status = dish_pb.Dish.NORMAL
        elif status == "INACTIVE":
            dish.status = dish_pb.Dish.GUQING
        elif status == "DELETED":
            dish.status = dish_pb.Dish.GUQING
        if old_status != dish.status:
            order_da = OrderingServiceDataAccessHelper()
            order_da.update_dish(dish=dish, id=dish.id, merchant_id=dish.merchant_id, status=dish.status)

    def try_to_set_dish_sold_out(self, client, dish, dishes_changeful_info=None):
        """尝试设置菜品为估清状态"""
        if dish.status == dish_pb.Dish.GUQING:
            return
        if dish.status != dish_pb.Dish.NORMAL:
            return
        if not dish.enable_quantity_setting:
            return
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            key = CacheServerKeys.get_dish_remain_quantity_key(dish.id)
            value = client.get(key)
            if value and int(value) == 0:
                dish.status = dish_pb.Dish.GUQING
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            if not dishes_changeful_info:
                return
            dish_changeful_info = dishes_changeful_info.get(dish.id)
            if not dish_changeful_info:
                return
            if dish_changeful_info.get("status") == "SOLD_OUT":
                dish.status = dish_pb.Dish.GUQING
            if dish_changeful_info.get("remainQuantiy") == 0:
                dish.status = dish_pb.Dish.GUQING
            if dish_changeful_info.get("enableQuantitySetting") and dish_changeful_info.get("remainQuantity") == 0:
                dish.status = dish_pb.Dish.GUQING

    def _set_packaging_box_info(self, catalog):
        packaging_box_config = self.registration_info.packaging_box_config
        dish = self.get_dish_from_cache(packaging_box_config.dish_id)
        if not dish:
            return
        catalog.package_fee_per_product = int(dish.price)
        self._convert_dish_vo(dish, catalog.packaging_box_config.dish)
        for order_bill_fee in packaging_box_config.order_bill_fee:
            catalog.packaging_box_config.order_bill_fee.append(order_bill_fee)

    def _set_table_info(self, catalog_vo, meal_type):
        ordering_da = OrderingServiceDataAccessHelper()
        table = None
        if meal_type == dish_pb.DishOrder.TAKE_OUT:
            table = ordering_da.get_table(merchant_id=self.merchant.id, meal_type=meal_type)
        if meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            table = ordering_da.get_table(merchant_id=self.merchant.id, meal_type=meal_type)
        if meal_type == dish_pb.DishOrder.TAKE_AWAY:
            table = ordering_da.get_table(merchant_id=self.merchant.id, meal_type=meal_type)
        if not table:
            return None
        catalog_vo.table_info.table_id = table.ordering_service_table_id
        catalog_vo.table_info.table_name = table.name
        return table

    def _set_red_packet_discount_config(self, catalog_vo):
        if not self.discount_plan:
            return
        m0 = self.discount_plan.red_packet_discount.max_red_packet_percentage
        m1 = self.discount_plan.red_packet_discount.min_red_packet_percentage
        catalog_vo.red_packet_discount.max_red_packet_percentage = m0
        catalog_vo.red_packet_discount.max_value_cap = self.discount_plan.red_packet_discount.max_value_cap
        catalog_vo.red_packet_discount.min_red_packet_percentage = m1

    def _groupify_dishes(self, dish_list, categories, catalog_vo):
        dish_by_category = {}
        for dish in dish_list:
            category_id = dish.category_id
            dishes = dish_by_category.get(category_id, [])
            dishes.append(dish)
            dish_by_category.update({dish.category_id: dishes})

        for category_id, dishes in dish_by_category.items():
            category = categories.get(category_id)
            dish = catalog_vo.dishes.add()
            dish.category.id = category.id
            dish.category.name = category.name
            dish.category.sort = category.sort
            dish.category.no_discount = category.no_discount
            dish.category.required = category.required
            dish.dish_list.extend(dishes)

    def get_shilai_order_items(self, table):
        """从收银机的服务上面获取桌台必点菜品"""
        if self.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return None
        shilai_dish_manager = ShilaiDishManager(dish_manager=self)
        required_dishes = shilai_dish_manager.get_table_required_dishes(table=table)
        if not required_dishes:
            return None
        ret = []
        for type, dishes in required_dishes.items():
            for dish in dishes:
                r = registration_pb.RequiredOrderItem()
                r.name = dish.get("name")
                r.id = dish.get("id")
                r.price = dish.get("price")
                if type == "PER_PERSON":
                    r.type = registration_pb.RequiredOrderItem.EVERYONE
                elif type == "PER_TABLE":
                    r.type = registration_pb.RequiredOrderItem.ONLY_ONE
                ret.append(r)
        return ret

    def _required_dish_config(self, catalog_vo, meal_type, table=None):
        ordering_da = OrderingServiceDataAccessHelper()

        required_order_items = self.registration_info.ordering_config.required_order_items

        if table is not None:
            _required_order_items = self.get_shilai_order_items(table)
            if _required_order_items is not None:
                required_order_items = _required_order_items

        for required_order_item in required_order_items:
            id = required_order_item.id
            dish = ordering_da.get_dish(dish_id=id, merchant_id=self.merchant.id)
            if not dish:
                continue
            if dish.status != dish_pb.Dish.NORMAL:
                continue
            if meal_type == dish_pb.DishOrder.TAKE_OUT and dish.name != "配送费":
                continue
            if meal_type == dish_pb.DishOrder.EAT_IN and dish.name == "配送费":
                continue
            if meal_type == dish_pb.DishOrder.SELF_PICK_UP and dish.name == "配送费":
                continue
            if meal_type == dish_pb.DishOrder.TAKE_AWAY and dish.name == "配送费":
                continue
            ritem_vo = catalog_vo.required_order_items.add()
            dish_vo = self._convert_dish_vo(dish)
            dish_vo.type = required_order_item.type
            dish_vo.discount_price = 0
            ritem_vo.CopyFrom(dish_vo)

    def _convert_dish_vo(self, dish, dish_vo=None, category=None, dishes_changeful_info=None):
        if dish_vo is None:
            dish_vo = page_dish_pb.DishCatalog.Menu.Dish()
        dish_vo.category_id = dish.categories[0]
        dish_vo.id = dish.id
        if len(dish.images) > 0:
            dish_vo.image = dish.images[0]
        if dish_vo.image == "":
            dish_vo.image = dish.shilai_dish_image
            dish_vo.thumb_image = dish.shilai_dish_thumb_image
        else:
            dish_vo.thumb_image = dish.thumb_image
        dish_vo.name = dish.name
        dish_vo.origin_price = dish.price
        dish_vo.price = dish.price
        dish_vo.discount_price = dish.price - self.get_discount_price(dish)
        dish_vo.tag = dish.tag
        # recommend_dish_ids = [dish.id for dish in self.registration_info.recommend_dish.dishes]
        # if dish_vo.id in recommend_dish_ids:
        #     dish_vo.discount_price = 0
        dish_vo.supply_condiment_uplimit = dish.supply_condiment_uplimit
        dish_vo.selection_type.CopyFrom(dish.selection_type)
        dish_vo.sort = dish.sort
        dish_vo.market_price = dish.market_price
        dish_vo.box_qty = dish.box_qty
        dish_vo.min_order_num = dish.min_order_num
        dish_vo.max_order_num = dish.max_order_num
        dish_vo.status = dish.status
        dish_vo.sold_number = dish.sold_number
        dish_vo.vip_price = dish.vip_price
        dish_vo.desc = dish.desc
        dish_vo.supply_condiment_group_name = dish.supply_condiment_group_name

        dish_changeful_info = None
        if dishes_changeful_info:
            dish_changeful_info = dishes_changeful_info.get(dish.id)
        if dish_changeful_info:
            tag = dish_changeful_info.get("tag", "NONE")
            tag = dish_pb.Dish.DishTag.Value(tag)
            dish_vo.tag = tag
        rq = self.get_dish_remain_quantity(dish, dish_changeful_info)
        dish_vo.enable_quantity_setting = rq.enable_quantity_setting
        dish_vo.remain_quantity = rq.remain_quantity

        if dish.type == dish_pb.Dish.COMBO_MEAL:
            dish_vo.uuid = dish.uuid
        if category:
            dish_vo.has_discount = not category.no_discount
        else:
            dish_vo.has_discount = not dish.no_discount
        self.set_dish_vo_attrs(dish, dish_vo)
        self.set_dish_vo_supply_condiments(dish, dish_vo)
        self.set_dish_vo_child_dishes(dish, dish_vo, dishes_changeful_info=dishes_changeful_info)
        return dish_vo

    def get_dish_remain_quantity(self, dish, dish_changeful_info=None):
        """获取菜品余量
        1. 时来收银机模式: 根据dish_changeful_info来获取余量
        2. 飞鹅模式: 如果菜品有开启余量,则从缓存中去获取余量.如果缓存中没有则设置在缓存中
        """
        RQ = namedtuple("RQ", ["remain_quantity", "enable_quantity_setting", "available_quantity"])
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            if dish_changeful_info is None:
                return RQ(0, False, 0)
            remain_quantity = dish_changeful_info.get("remainQuantity", 0)
            enable_quantity_setting = dish_changeful_info.get("enableQuantitySetting", False)
            available_quantity = dish_changeful_info.get("availableQuantity", 0)
            return RQ(remain_quantity, enable_quantity_setting, available_quantity)

        remain_quantity = dish.remain_quantity
        enable_quantity_setting = dish.enable_quantity_setting
        available_quantity = dish.available_quantity
        if not enable_quantity_setting:
            return RQ(0, False, 0)
        client = RedisClient().get_connection()
        key = CacheServerKeys.get_dish_remain_quantity_key(dish.id)
        cache_remain_quantity = client.get(key)
        if cache_remain_quantity:
            remain_quantity = int(cache_remain_quantity)
        else:
            client.setnx(key, remain_quantity)
        return RQ(remain_quantity, enable_quantity_setting, available_quantity)

    def set_dish_vo_child_dishes(self, dish, dish_vo, dishes_changeful_info=None):
        if dish.type != dish_pb.Dish.COMBO_MEAL:
            return
        display_status = [dish_pb.Dish.NORMAL, dish_pb.Dish.GUQING]
        redis_client = RedisClient().get_connection()
        for dish_group in dish.child_dish_groups:
            dish_group_vo = dish_vo.child_dish_groups.add()
            dish_group_vo.id = dish_group.id
            dish_group_vo.group_name = dish_group.group_name
            dish_group_vo.order_min = dish_group.order_min
            dish_group_vo.order_max = dish_group.order_max
            dish_group_vo.sort = dish_group.sort
            dish_group_vo.allow_duplicate = dish_group.allow_duplicate
            dish_group_vo.is_fixed = dish_group.is_fixed
            for child_dish in dish_group.child_dishes:
                dish = self.get_dish_from_cache(child_dish.id)
                dish_changeful_info = None
                if dishes_changeful_info is not None:
                    dish_changeful_info = dishes_changeful_info.get(child_dish.id)
                if not dish:
                    continue
                self.set_dish_status_as_pos(dish, dishes_changeful_info, is_child_dish=True)
                self.try_to_set_dish_sold_out(redis_client, dish, dishes_changeful_info=dishes_changeful_info)
                if dish.status not in display_status:
                    continue
                child_dish_vo = dish_group_vo.child_dishes.add()
                child_dish_vo.id = child_dish.id
                child_dish_vo.name = child_dish.name
                child_dish_vo.price = child_dish.price
                child_dish_vo.market_price = child_dish.market_price
                child_dish_vo.sort = child_dish.sort
                child_dish_vo.is_must = child_dish.is_must
                child_dish_vo.status = dish.status
                child_dish_vo.box_qty = dish.box_qty
                child_dish_vo.quantity_increment = child_dish.quantity_increment
                child_dish_vo.selection_type.CopyFrom(dish.selection_type)
                self._check_dish_sale_time(dish, child_dish_vo)
                rq = self.get_dish_remain_quantity(dish, dish_changeful_info)
                child_dish_vo.enable_quantity_setting = rq.enable_quantity_setting
                child_dish_vo.remain_quantity = rq.remain_quantity
            dish_group_vo.child_dishes.sort(key=lambda dish: dish.sort)
        dish_vo.child_dish_groups.sort(key=lambda group: group.sort)

    def set_dish_vo_supply_condiments(self, dish, dish_vo):
        self.set_dish_supply_condiments()
        unshown_status = [dish_pb.SupplyCondiment.OUT_OF_STOCK]
        for supply_condiment in dish.supply_condiments:
            _supply_condiment = self.dish_supply_condiments.get(supply_condiment.id)
            if _supply_condiment:
                if _supply_condiment.status in unshown_status:
                    continue
            if supply_condiment.status in unshown_status:
                continue

            supply_condiment_vo = dish_vo.supply_condiments.add()
            supply_condiment_vo.id = supply_condiment.id
            supply_condiment_vo.name = supply_condiment.name
            supply_condiment_vo.market_price = supply_condiment.market_price
            supply_condiment_vo.status = supply_condiment.status

    def set_dish_vo_attrs(self, dish, dish_vo):
        attr_lists = {}
        for attr in dish.attrs:
            group_id = attr.group_id
            attr_group = attr_lists.get(group_id, [])
            attr_group.append(attr)
            attr_lists.update({group_id: attr_group})
        self.set_dish_attrs()
        for group_id, attr_list in attr_lists.items():
            attr_list_vo = page_dish_pb.DishCatalog.Menu.AttrList()
            packaget_list_vo = page_dish_pb.DishCatalog.Menu.AttrList()
            attr_group = None
            for attr in attr_list:
                _attr = self.dish_attrs.get(attr.id)
                if _attr:
                    if _attr.status in [dish_pb.Attr.OUT_OF_STOCK, dish_pb.Attr.SOLD_OUT]:
                        continue
                if attr.status in [dish_pb.Attr.OUT_OF_STOCK, dish_pb.Attr.SOLD_OUT]:
                    continue

                if attr.type == dish_pb.Attr.TAG:
                    continue
                list_vo = packaget_list_vo if attr.type == dish_pb.Attr.TAKE_AWAY else attr_list_vo
                attr_vo = list_vo.attrs.add()
                attr_vo.id = attr.id
                attr_vo.name = attr.name
                attr_vo.reprice = attr.reprice
                attr_vo.type = attr.type
                attr_vo.status = attr.status
                list_vo.group_name = attr.group_name
                list_vo.group_id = group_id
                list_vo.is_multi_select = attr.is_multi_select
                attr_group = self.dish_attr_groups.get(attr.group_id)
            if attr_group:
                attr_list_vo.attr_selection_type.CopyFrom(attr_group.selection_type)
                packaget_list_vo.attr_selection_type.CopyFrom(attr_group.selection_type)
            else:
                attr_list_vo.attr_selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE
                packaget_list_vo.attr_selection_type.type = dish_attr_pb.AttrSelectionType.SINGLE
            if len(attr_list_vo.attrs) > 0:
                dish_vo.attr_list.append(attr_list_vo)
            if len(packaget_list_vo.attrs) > 0:
                dish_vo.package_list.append(packaget_list_vo)

    def get_discount_price(self, dish):
        if dish.price < 10:
            return int(dish.price)
        if self.discount_plan:
            rate = self.discount_plan.dish_discounts.dish_discounts.get(str(dish.id), 100)
        else:
            rate = 100
        required_order_dishes = {item.name: item.type for item in self.registration_info.ordering_config.required_order_items}
        if dish.name in required_order_dishes:
            return int(dish.price)
        price = float(rate) / 100 * dish.price
        price = round(price)
        return price

    def _dish_parse_dict(self, dish):
        if isinstance(dish, dict):
            return json_format.ParseDict(dish, dish_pb.Dish(), ignore_unknown_fields=True)
        return dish

    def _adapt_dish_time_limited_sale(self, dish, dish_vo):
        """优化老版本限时售卖字段输出，聚合到新字段dish_vo.sale_time_schedule，由前端统一实时处理"""

        def date2timestamp(date_str):
            date_time = datetime.strptime(date_str, '%Y-%m-%d')
            return str(int(time.mktime(date_time.timetuple())))

        if dish_vo is None:
            return
        if not dish.sale_times or len(dish.sale_times) == 0:
            return

        tzchina = timezone(date_utils.TIMEZONE_SHANGHAI)
        utc = timezone(date_utils.TIMEZONE_UTC)
        week_day = datetime.utcnow().replace(tzinfo=utc).astimezone(tzchina).weekday()

        fingerprint_map = dict()
        for sale_time in dish.sale_times:
            fingerprint = f"{sale_time.start_date}:{sale_time.end_date}:{sale_time.start}:{sale_time.end}"
            if not fingerprint_map.get(fingerprint):
                fingerprint_map[fingerprint] = set()
            fingerprint_map[fingerprint].add(sale_time.weekday)

        for fingerprint, weekdays in fingerprint_map.items():
            start_date, end_date, start, end = fingerprint.split(':')
            if not start_date and not end_date and dish.sale_begin_day and dish.sale_end_day:
                start_date, end_date = date2timestamp(dish.sale_begin_day), date2timestamp(dish.sale_end_day)
            d = dish_vo.sale_time_schedule.add()
            d.start_date, d.end_date, d.start, d.end = start_date, end_date, int(start), int(end)
            if len(weekdays) >= 7:
                d.weekday.append(datetime_pb.Weekday.WEEKDAY_NONE)
            else:
                for weekday in weekdays:
                    d.weekday.append(weekday)

            # 哗啦啦模式特殊处理：通过dish_week_day控制被过滤的礼拜天数
            if (
                self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA
                and dish.dish_week_day != -1
                and dish.dish_week_day & (1 << week_day) == 0
            ):
                if datetime_pb.Weekday.WEEKDAY_NONE in d.weekday:
                    d.weekday.pop()
                    for w in datetime_pb.Weekday.values()[1:]:
                        d.weekday.append(w)
                if len(d.weekday) > 0 and week_day + 1 in d.weekday:
                    d.weekday.pop(week_day)

    def _check_dish_sale_time(self, dish, dish_vo=None):
        """菜品限时逻辑
        1. dish.sale_begin_day, dish.sale_end_day: 年-月-日 用于表示菜品售卖时间区间
        2. dish.dish_week_day: 哗啦啦专用字段,-1表示周一到周七都售卖.其它则表示只在特定星期才售卖
        3. dish.sale_times:
            weekday: 星期,0~7,0表示所有星期都售卖.1~7表示只在特定星期都售卖
            start,end: 表示售卖的秒数,如 7:30~10:00售卖,则 start = 7 * 60 * 60 + 30 * 60, end = 10 * 60 * 60
        """
        self._adapt_dish_time_limited_sale(dish, dish_vo)

        # utc_now = datetime.utcnow()
        # tzchina = timezone(date_utils.TIMEZONE_SHANGHAI)
        # utc = timezone(date_utils.TIMEZONE_UTC)
        # now = utc_now.replace(tzinfo=utc).astimezone(tzchina)
        # now_timestamp = now.timestamp()
        # now_seconds = now.hour * 60 * 60 + now.minute * 60 + now.second
        # now_str = now.strftime("%Y-%m-%d")
        # week_day = now.weekday()

        # if not dish.sale_times or len(dish.sale_times) == 0:
        #     return True
        # not_in_sale_time_str = "此菜品不在售卖时间段"
        # # 控制菜品售卖的日期区间,如: 2021-01-01 ~ 2021-01-31
        # if dish.sale_begin_day != "" and dish.sale_end_day != "":
        #     if not (dish.sale_begin_day <= now_str <= dish.sale_end_day):
        #         dish_vo.sale_time_str = not_in_sale_time_str
        #         dish_vo.status = dish_pb.Dish.NOT_IN_TIME_LIMIT_SALE
        #         return False

        # if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
        #     # 如果dish.dish_week_day不为-1,那么表示这个菜品只在特定的星期才售卖
        #     if dish.dish_week_day != -1:
        #         if dish.dish_week_day & (1 << week_day) == 0:
        #             dish_vo.sale_time_str = not_in_sale_time_str
        #             dish_vo.status = dish_pb.Dish.NOT_IN_TIME_LIMIT_SALE
        #             return False
        # for sale_time in dish.sale_times:
        #     if sale_time.start_date and sale_time.end_date:
        #         if not (int(sale_time.start_date) < now_timestamp < int(sale_time.end_date)):
        #             continue
        #     if sale_time.weekday == datetime_pb.WEEKDAY_NONE:
        #         # WEEKDAY_NONE 表示所有星期都依赖些配置
        #         if sale_time.start <= now_seconds <= sale_time.end:
        #             return True
        #     else:
        #         if sale_time.weekday - 1 == week_day:
        #             if sale_time.start <= now_seconds <= sale_time.end:
        #                 return True

        # # 哗啦啦可以配置多个时间段,这种直接返回菜品不在售卖时间段就行了
        # if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
        #     dish_vo.sale_time_str = not_in_sale_time_str
        # else:
        #     # 客如云只能配置单个时间段(如果跨天了则会有两个时间段),则返回售卖时间
        #     self._set_dish_sale_time(dish_vo, dish.sale_times)
        # if dish_vo:
        #     dish_vo.status = dish_pb.Dish.NOT_IN_TIME_LIMIT_SALE
        # return False

    # def _set_dish_sale_time(self, dish_vo, sale_times):
    #     """菜单接口使用"""
    #     if not dish_vo:
    #         return
    #     utc_now = datetime.utcnow()
    #     tzchina = timezone(date_utils.TIMEZONE_SHANGHAI)
    #     utc = timezone(date_utils.TIMEZONE_UTC)
    #     now = utc_now.replace(tzinfo=utc).astimezone(tzchina)
    #     today = now.replace(hour=0, minute=0, second=0)
    #     tomorrow = today + timedelta(1)
    #     if len(sale_times) == 1:
    #         sale_time = sale_times[0]
    #         start = sale_time.start
    #         end = sale_time.end
    #         start = today + timedelta(seconds=start)
    #         end = today + timedelta(seconds=end)
    #         sale_time_str_start = start.strftime("%H:%M")
    #         sale_time_str_end = end.strftime("%H:%M")
    #         dish_vo.sale_time_str = "{}-{}".format(sale_time_str_start, sale_time_str_end)
    #     elif len(sale_times) == 2:
    #         s0 = sale_times[0]
    #         s1 = sale_times[1]
    #         start = max(s0.start, s1.start)
    #         end = min(s0.end, s1.end)
    #         start = today + timedelta(seconds=start)
    #         end = tomorrow + timedelta(seconds=end)
    #         sale_time_str_start = start.strftime("%H:%M")
    #         sale_time_str_end = end.strftime("%H:%M")
    #         dish_vo.sale_time_str = "{}-{}".format(sale_time_str_start, sale_time_str_end)

    def get_dish_from_cache(self, dish_id):
        self.load_dish()
        dish = self.dishes.get(dish_id)
        if not dish:
            return None
        if isinstance(dish, dict):
            dish = json_format.ParseDict(dish, dish_pb.Dish(), ignore_unknown_fields=True)
            self.dishes.update({dish_id: dish})
        return dish

    def get_recommend_dishes(self, user_id):
        """
        加料和属性不能有这个菜
        """
        recommend_dish_vo = page_dish_pb.RecommendDishVO()
        self.get_discount_plan(user_id, enbale_sale_time_discount=True)
        for recommend_dish in self.registration_info.recommend_dish.dishes:
            dish = self.get_dish_from_cache(str(recommend_dish.id))
            if not dish:
                continue
            # 移至前端处理
            # if not self._check_dish_sale_time(dish, page_dish_pb.DishCatalog.Menu.Dish()):
            #     continue
            if dish.status != dish_pb.Dish.NORMAL:
                continue
            dish_vo = recommend_dish_vo.dishes.add()
            dish_vo.image_url = dish.shilai_dish_image
            dish_vo.image = dish.shilai_dish_image
            if len(dish.images) > 0:
                dish_vo.image_url = dish.images[0]
                dish_vo.image = dish.images[0]
            dish_vo.id = dish.id
            dish_vo.market_price = dish.market_price
            dish_vo.name = dish.name
            dish_vo.price = dish.price
            dish_vo.discount_price = int(dish.price - self.get_discount_price(dish))
            dish_vo.origin_dish_id = recommend_dish.origin_dish_id
        return recommend_dish_vo

    def get_all_recommend_dishes(self, user_id):
        """
        获取所有推荐菜以配置
        """
        recommend_dish_vo = page_dish_pb.RecommendDishVO()
        self.get_discount_plan(user_id, enbale_sale_time_discount=True)
        for recommend_dish in self.registration_info.recommend_dish.dishes:
            dish = self.get_dish_from_cache(str(recommend_dish.id))
            if not dish:
                continue
            dish_vo = recommend_dish_vo.dishes.add()
            dish_vo.image_url = dish.shilai_dish_image
            dish_vo.image = dish.shilai_dish_image
            if len(dish.images) > 0:
                dish_vo.image_url = dish.images[0]
                dish_vo.image = dish.images[0]
            dish_vo.id = dish.id
            dish_vo.market_price = dish.market_price
            dish_vo.name = dish.name
            dish_vo.price = dish.price
            dish_vo.discount_price = int(dish.price - self.get_discount_price(dish))
            dish_vo.origin_dish_id = recommend_dish.origin_dish_id
        return recommend_dish_vo

    def update_recommend_dishes(self, enable_recommend_dish=None, dish_ids=None):
        order_da = OrderingServiceDataAccessHelper()
        registration_info = order_da.get_registration_info(merchant_id=self.merchant.id)
        if enable_recommend_dish is not None:
            registration_info.recommend_dish.enable_recommend_dish = enable_recommend_dish
        if dish_ids is not None:
            new_dishes = list()
            for dish_id in dish_ids:
                rd = registration_pb.RecommendDish.Dish()
                rd.id = dish_id
                new_dishes.append(rd)
            self.list_update(registration_info.recommend_dish.dishes, new_dishes)
        order_da.add_or_update_registration_info(registration_info)
        return registration_info

    def async_categories(self, names=None, dish_type_ids=None):
        fname = inspect.currentframe().f_code.co_name
        if not hasattr(self.dish_manager, fname):
            return
        dish_categories = self.dish_manager.async_categories(names=names, dish_type_ids=dish_type_ids)
        ordering_da = OrderingServiceDataAccessHelper()
        db_categories = ordering_da.get_categories(merchant_id=self.merchant.id)
        db_categories_by_id = {str(category.id): category for category in db_categories}
        db_categories_by_third_party_id = {str(category.third_party_category_id): category for category in db_categories}
        ret = []
        for category in dish_categories:
            category_id = category.get("id")
            third_party_category_id = category.get("third_party_category_id")
            db_category = db_categories_by_id.get(category_id)
            if db_category is None:
                db_category = db_categories_by_third_party_id.get(third_party_category_id)
            if db_category and db_category.status in [dish_pb.DishCategory.INACTIVE, dish_pb.DishCategory.DELETE]:
                continue
            if names is not None and category.get("name") not in names:
                continue
            if dish_type_ids is not None and category_id not in dish_type_ids:
                continue
            c = dish_pb.DishCategory()
            c.name = category.get("name")
            c.sort = category.get("sort")
            c.id = str(category_id)
            c.parent_id = str(category.get("parentId"))
            c.level = category.get("level", 0)
            c.merchant_id = self.merchant.id
            c.third_party_category_id = category.get("third_party_category_id")
            if db_category:
                c.no_discount = db_category.no_discount
                c.required = db_category.required
                c.id = db_category.id
            ordering_da.add_or_update_category(category=c)
            ret.append(c)
        return ret

    def change_dish_category_status(self, category_id, status):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            ret = self.dish_manager.change_dish_category_status(category_id, status)
            return ret

    def change_dish_status(self, dish_id, status):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            ret = self.dish_manager.change_dish_status(dish_id, status)
            return ret

    def change_brand_dish_status(self, dish_brand_id, status):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            ret = self.dish_manager.change_brand_dish_status(dish_brand_id, status)
            return ret

    def staff_update_dish(self, dishes, session):
        """批量更新菜品图片
        `dishes`: 数组
            {"dishId": "", "shilaiDishImage": ""}
        """
        conn = RedisClient().get_connection()
        if not conn.get(session):
            raise errors.ShowError("请先登陆")
        conn.set(session, 1, 60 * 60 * 24)
        ordering_da = OrderingServiceDataAccessHelper()
        saved_dishes = []
        for dish_dict in dishes:
            dish_id = dish_dict.get("dishId")
            dish = ordering_da.get_dish(dish_id=dish_id, merchant_id=self.merchant.id)
            if not dish:
                continue
            if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
                while dish.images:
                    dish.images.pop()
                dish.thumb_image = ""
            shilai_image_url = dish_dict.get("shilaiImageUrl")
            self.update_shilai_dish_image(dish, shilai_image_url)
            saved_dishes.append(dish)
        ordering_da.add_or_update_dishes(dishes=saved_dishes, merchant_id=self.merchant.id)

    def update_shilai_dish_image(self, dish, image_url):
        """更新时来菜品图
        dish.images 中保存的是第三方平台的图片.dish.images为数组,使用dish.images[0]
        dish.shilai_dish_image 保存的是时来图片
        如果 dish.images中没有图片,则以shilai_dish_image来显示
        """
        if image_url == "":  # 清空菜品图片
            dish.shilai_dish_image = ''
            dish.shilai_dish_thumb_image = ''
            return
        aliyun_oss_helper = AliyunOSSHelper()
        image_split = image_url.split(".")
        if len(image_split) == 0:
            suffix = "jpg"
        else:
            suffix = image_split[-1]
        if suffix.lower() not in ["jpg", "png", "jpeg"]:
            suffix = "jpg"
        md5 = hashlib.md5()
        md5.update(self.merchant.id.encode())
        md5.update(dish.id.encode())
        name = md5.hexdigest()
        name = "{}.{}".format(name, suffix)
        image_obj = aliyun_oss_helper.upload_image_network_stream(image_url, name=name)
        aliyun_oss_helper.try_to_resize(image_obj, model="m_lfit")
        target_name = "thumb-{}".format(name)
        thumb_image = aliyun_oss_helper.resize(image_obj.name, target_name, model="m_lfit", w=480, h=480)
        dish.shilai_dish_image = image_obj.url
        dish.shilai_dish_thumb_image = thumb_image.url

    def get_required_dishes(self, *args, **kwargs):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            return self.dish_manager.get_required_dishes(*args, **kwargs)

    def get_all_dish(self):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            return self.dish_manager.get_all_dish()

    def get_all_dish_categories(self):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            return self.dish_manager.get_all_dish_categories()

    def update_dish_update_timestamp(self):
        config_manager = ConfigManager(merchant=self.merchant)
        now = int(time.time())
        config_manager.update_update_timestamp(dish_catalog_update_timestamp=now)

    def sync_dish(self, saved_dish=None, dish_id=None, dish_brand_id=None, category_dish=None):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.dish_manager, fname):
            dish = self.dish_manager.sync_dish(
                saved_dish=saved_dish, dish_id=dish_id, dish_brand_id=dish_brand_id, category_dish=category_dish
            )
            if dish:
                attr_manager = AttrManager(merchant=self.merchant)
                attr_manager.update_dishes_attr_group(dishes=[dish])
                supply_condiment_manager = SupplyCondimentManager(merchant=self.merchant)
                supply_condiment_manager.add_or_update_dish_supply_condiment(dish=dish)
            return dish

    def shilai_pos_sync_dishes(self, dish_ids):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, dish_ids=dish_ids)
        data = self.dish_manager.sync_dishes(dishes, dish_ids)
        dishes = data.get("dishes")
        # categories = data.get("categories")
        # for category in categories:
        #     ordering_da.add_or_update_category(category=category)
        dishes = list(dishes.values())
        ordering_da.add_or_update_dishes(dishes=dishes, merchant_id=self.merchant.id)
        self.update_dish_supply_condiments(dishes)

    def update_dish_supply_condiments(self, dishes):
        """如果菜品是加料菜,当它改变时需要同时处理有这个菜品的加料菜品"""
        ordering_da = OrderingServiceDataAccessHelper()
        for dish in dishes:
            _dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, supply_condiment_id=dish.id)
            for _dish in _dishes:
                for sc in _dish.supply_condiments:
                    if sc.id != dish.id:
                        continue
                    status = dish_pb.Dish.Status.Name(dish.status)
                    logger.info("修改菜品 {} {} 加料 {} {}".format(_dish.id, _dish.name, dish.name, status))
                    sc.name = dish.name
                    sc.market_price = int(dish.price)
                    sc.sort = dish.sort
                    if dish.status == dish_pb.Dish.NORMAL:
                        sc.status = dish_pb.SupplyCondiment.NORMAL
                    elif dish.status == dish_pb.Dish.GUQING:
                        sc.status = dish_pb.SupplyCondiment.SOLD_OUT
                    elif dish.status == dish_pb.Dish.DISABLE:
                        sc.status = dish_pb.SupplyCondiment.OUT_OF_STOCK
                _dish.supply_condiments.sort(key=lambda x: x.sort)
            ordering_da.add_or_update_dishes(dishes=_dishes, merchant_id=self.merchant.id)

    def shilai_pos_sync_categories(self):
        ordering_da = OrderingServiceDataAccessHelper()
        categories = self.dish_manager.sync_categories()
        for category in categories:
            id = category.get("id")
            _category = ordering_da.get_category(id=id, merchant_id=self.merchant.id)
            if not _category:
                _category = dish_pb.DishCategory()
                _category.id = id
            _category.name = category.get("name")
            ordering_da.add_or_update_category(category)

    def set_dish_activation(self, dish, activation):
        if hasattr(self.dish_manager, "set_dish_activation"):
            return self.dish_manager.set_dish_activation(dish, activation)

    def compress_dish_image(self):
        ssl._create_default_https_context = ssl._create_unverified_context
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_all_dishes(merchant_id=self.merchant.id, nocache=True)
        base_dir = constants.DISH_IMAGE_COMPRESS_PACKAGE
        compress_dir = "{}/{}".format(base_dir, self.merchant.id)
        if not os.path.exists(compress_dir):
            os.makedirs(compress_dir)
        for dish in dishes:
            image = dish.shilai_dish_image
            if len(dish.images) > 0:
                image = dish.images[0]
            if image == "":
                continue
            _, suffix = os.path.splitext(image)
            filename = "{}/{}.{}".format(compress_dir, dish.name, suffix)
            urllib.request.urlretrieve(image, filename)
        Compress.tar_gz_compress(compress_dir, rmtree=True)
        domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        return "{}/dish-images/{}.tar.gz".format(domain, self.merchant.id)

    def set_dish_default_value(self, dish_obj, saved_dish=None):
        if not saved_dish:
            dish_obj.sold_number_base_value = random.randint(5, 10)
            dish_obj.id = id_manager.generate_common_id()
            dish_obj.merchant_id = self.merchant.id
        if saved_dish:
            dish_obj.market_price = saved_dish.market_price
            dish_obj.cannot_pay = saved_dish.cannot_pay
            dish_obj.is_discount_dish = saved_dish.is_discount_dish
            dish_obj.discount_rate = saved_dish.discount_rate
            dish_obj.discount_amount = saved_dish.discount_amount
            dish_obj.no_discount = saved_dish.no_discount
            dish_obj.is_zero_dish = saved_dish.is_zero_dish
            dish_obj.status = saved_dish.status
            dish_obj.sold_number = saved_dish.sold_number
            dish_obj.shilai_dish_image = saved_dish.shilai_dish_image
            dish_obj.shilai_dish_thumb_image = saved_dish.shilai_dish_thumb_image
            dish_obj.sold_number_base_value = saved_dish.sold_number_base_value
            if dish_obj.sold_number_base_value == 0:
                dish_obj.sold_number_base_value = random.randint(5, 10)
            dish_obj.supply_condiment_uplimit = saved_dish.supply_condiment_uplimit
            dish_obj.selection_type.CopyFrom(saved_dish.selection_type)

    def get_or_create_dish(self, dish_id=None, name=None):
        """从数据库里取出菜品对象或者创建菜品对象
        `dish_id`: 菜品ID,不传则表明是创建新的菜品,传了则取出已存的菜品
        `name`: 菜品名.用于做去重处理.菜品名不能重复
        """
        ordering_da = OrderingServiceDataAccessHelper()
        dish_obj = None

        # 如果有传dish_id,那么根据id去查,如果没有查到则返回ID错误
        # 如果查到了则直接返回
        if dish_id is not None:
            dish_obj = ordering_da.get_dish(dish_id=dish_id, merchant_id=self.merchant.id)
            if not dish_obj:
                raise errors.ShowError("菜品ID错误: {}".format(dish_id))

            if name is not None:
                dishes_by_name = ordering_da.get_dish_by_merchant_id_name(merchant_id=self.merchant.id, name=name)
                if len(dishes_by_name) > 0:
                    if dishes_by_name[0].id != dish_id:
                        raise errors.ShowError("该菜品名已存在")

            return dish_obj

        # 根据菜品名去查.飞鹅模式的菜品名是不能重复的
        # 所以如果查到了就直接返回
        if name is not None:
            # 如果在数据库没有查到这个菜品
            # 但是这个名字已经在数据库中存在,那么菜品就重复了
            dish_objs = ordering_da.get_dish_by_merchant_id_name(merchant_id=self.merchant.id, name=name)
            if len(dish_objs) > 0:
                return dish_objs[0]

        dish_obj = dish_pb.Dish()
        self.set_dish_default_value(dish_obj)
        return dish_obj

    def merchant_assist_create_or_update_dish(self, **kargs):
        """创建或者更新菜品信息"""
        ordering_da = OrderingServiceDataAccessHelper()
        dish_id = kargs.get("dish_id", None)
        name = kargs.get("name", None)
        dish = self.get_or_create_dish(dish_id, name=name)

        tag = kargs.get("tag")
        if tag is not None:
            if isinstance(tag, str):
                tag = dish_pb.Dish.DishTag.Value(tag)
            dish.tag = tag

        desc = kargs.get("desc")
        if desc is not None:
            dish.desc = desc

        supply_condiment_group_name = kargs.get('supply_condiment_group_name')
        if supply_condiment_group_name is not None:
            dish.supply_condiment_group_name = supply_condiment_group_name

        supply_condiment_uplimit = kargs.get("supply_condiment_uplimit")
        if supply_condiment_uplimit is not None:
            dish.supply_condiment_uplimit = supply_condiment_uplimit

        if kargs.get("shilai_dish_image") is not None:
            self.update_shilai_dish_image(dish, kargs.get("shilai_dish_image"))

        price = kargs.get("price")
        if price is not None:
            dish.price = price

        name = kargs.get("name")
        if name is not None:
            dish.name = name

        status = kargs.get("status")
        if status is not None:
            status = dish_pb.Dish.Status.Value(status)
            dish.status = status
            if dish.status == dish_pb.Dish.Status.DISABLE:
                dish.name += f'(已删除-{int(time.time())})'

        category_id = kargs.get("category_id")
        if category_id is not None:
            if len(dish.categories) != 0:
                dish.categories[0] = category_id
            else:
                dish.categories.append(category_id)

        sale_times = kargs.get("sale_times")
        if sale_times is not None:
            self.update_dish_update_sale_times(dish, sale_times)

        sale_begin_day = kargs.get("sale_begin_day")
        sale_end_day = kargs.get("sale_end_day")
        self.update_dish_update_begin_end_day(dish, sale_begin_day, sale_end_day)

        supply_condiment_ids = kargs.get("supply_condiment_ids")
        if supply_condiment_ids is not None:
            self.update_dish_update_supply_condiments(dish, supply_condiment_ids)

        attr_group_ids = kargs.get("attr_group_ids")
        attr_ids = kargs.get("attr_ids")
        if attr_group_ids is not None and attr_ids is not None:
            self.update_dish_update_attrs(dish, attr_group_ids, attr_ids, kargs.get('attr_type'))

        selection_type = kargs.get("selection_type")
        if selection_type is not None:
            upper_limit = kargs.get("upper_limit")
            lower_limit = kargs.get("lower_limit")
            self.update_dish_update_selection_type(dish, selection_type, upper_limit, lower_limit)

        box_qty = kargs.get("box_qty")
        if box_qty is not None:
            dish.box_qty = box_qty

        min_order_num = kargs.get("min_order_num")
        if min_order_num is not None:
            dish.min_order_num = min_order_num

        max_order_num = kargs.get("max_order_num")
        if max_order_num is not None:
            dish.max_order_num = max_order_num

        is_discount_dish = kargs.get("is_discount_dish")
        if is_discount_dish is not None:
            dish.is_discount_dish = is_discount_dish

        available_quantity = kargs.get("available_quantity")
        if available_quantity is not None:
            dish.available_quantity = available_quantity

        enable_quantity_setting = kargs.get("enable_quantity_setting")
        if enable_quantity_setting is not None:
            dish.enable_quantity_setting = enable_quantity_setting

        # 设置菜品类型
        self.set_dish_type(dish, kargs.get("type"))
        # 设置子菜分组
        self.set_combo_child_dish_groups(dish, kargs.get("child_dish_groups"))

        ordering_da.add_or_update_dish(dish=dish)

        # 此参数不能放在 add_or_update_dish 前面,否则会被覆盖
        remain_quantity = kargs.get("remain_quantity")
        if remain_quantity is not None:
            dish.remain_quantity = remain_quantity
            self.set_dish_remain_quantity(dish.id, remain_quantity)
        if dish.type == dish_pb.Dish.DishType.SINGLE:
            self.sync_combo_dish(dish)
        return dish

    def set_dish_type(self, dish, dish_type=None):
        if dish_type:
            dish.type = dish_pb.Dish.DishType.Value(dish_type)
        return dish

    def set_combo_child_dish_groups(self, dish, child_dish_groups=None):
        if child_dish_groups is None:
            return dish
        child_groups = [
            json_format.ParseDict(group, dish_pb.ChildDishGroup(), ignore_unknown_fields=True) for group in child_dish_groups
        ]
        for ind, group in enumerate(child_groups):
            group.sort = ind
            if not group.id:
                group.id = id_manager.generate_common_id()
            for jnd, child_dish in enumerate(group.child_dishes):
                child_dish.sort = jnd
        self.list_update(dish.child_dish_groups, child_groups)
        return dish

    def set_dish_remain_quantity(self, dish_id, remain_quantity):
        key = CacheServerKeys.get_dish_remain_quantity_key(dish_id)
        client = RedisClient().get_connection()
        client.set(key, remain_quantity)
        self.increase_dish_remain_quantity({dish_id: {"quantity": 0}})

    def update_dish_update_sale_times(self, dish, sale_times):
        """更新菜品的星期售卖时间
        `sale_times`: 数组
        [{
            "start": "08:00",
            "end": "12:00",
            "weekday": "WEEKDAY_MONDAY"
        },{
            "start": "08:00",
            "end": "12:00",
            "weekday": "WEEKDAY_NONE" // WEEKDAY_NONE 表示,周一到周天的 start~end都售卖
        }]
        """
        while dish.sale_times:
            dish.sale_times.pop()
        for sale_time in sale_times:
            st = dish.sale_times.add()
            start = sale_time.get("start")
            start = start.split(":")
            start = int(start[0]) * date_utils.ONE_HOUR + int(start[1]) * date_utils.ONE_MINUTE
            st.start = start
            end = sale_time.get("end")
            end = end.split(":")
            end = int(end[0]) * date_utils.ONE_HOUR + int(end[1]) * date_utils.ONE_MINUTE
            st.end = end
            st.weekday = datetime_pb.Weekday.Value(sale_time.get("weekday"))

    def update_dish_update_begin_end_day(self, dish, sale_begin_day, sale_end_day):
        """更新菜品的售卖日期
        `sale_begin_day`: 开始售卖时间, 2021/01/03
        `sale_end_day`: 结束售卖时间, 2021/01/31
        """
        fmt = "%Y/%m/%d"
        if sale_begin_day is not None:
            sale_begin_day = datetime.strptime(sale_begin_day, fmt)
            dish.sale_begin_day = sale_begin_day.timestamp()
        if sale_end_day is not None:
            sale_end_day = datetime.strptime(sale_end_day, fmt)
            dish.sale_end_day = sale_end_day.timestamp()

    def update_dish_update_supply_condiments(self, dish, supply_condiment_ids):
        supply_condiment_da = DishSupplyCondimentDataAccessHelper()
        while dish.supply_condiments:
            dish.supply_condiments.pop()
        supply_condiment_templates = supply_condiment_da.get_supply_condiments(merchant_id=self.merchant.id)
        supply_condiment_templates = {s.id: s for s in supply_condiment_templates}

        for sc_id in supply_condiment_ids:
            template = supply_condiment_templates.get(sc_id)
            if not template:
                continue
            sc = dish.supply_condiments.add()
            sc.id = template.id
            sc.name = template.name
            sc.market_price = template.market_price
            sc.sort = template.sort
            sc.status = template.status

    def update_dish_update_attrs(self, dish, attr_group_ids, attr_ids, attr_type=None):
        """把菜品的属性全部替换"""
        attr_da = DishAttrDataAccessHelper()
        keep_attrs = []
        while dish.attrs:
            attr = dish.attrs.pop()
            dish_attr_type = dish_pb.Attr.AttrType.Name(attr.type)
            if attr_type is not None and (
                dish_attr_type != attr_type
                if attr_type
                in [
                    dish_pb.Attr.AttrType.Name(dish_pb.Attr.AttrType.SPECIFICATION),
                    dish_pb.Attr.AttrType.Name(dish_pb.Attr.AttrType.TAKE_AWAY),
                ]
                else dish_attr_type
                in [
                    dish_pb.Attr.AttrType.Name(dish_pb.Attr.AttrType.SPECIFICATION),
                    dish_pb.Attr.AttrType.Name(dish_pb.Attr.AttrType.TAKE_AWAY),
                ]
            ):
                keep_attrs.append(attr)
        for keep in keep_attrs:
            attr = dish.attrs.add()
            attr.group_id = keep.group_id
            attr.group_name = keep.group_name
            attr.id = keep.id
            attr.name = keep.name
            attr.reprice = keep.reprice
            attr.sort = keep.sort
            attr.is_multi_select = keep.is_multi_select
            attr.type = keep.type
            attr.status = keep.status
            keep.id in attr_ids and attr_ids.remove(keep.id)
        attr_ids = set(attr_ids)
        groups = attr_da.get_attr_groups(merchant_id=self.merchant.id)
        groups = {a.id: a for a in groups}

        for ag_id in attr_group_ids:
            group = groups.get(ag_id)
            if not group:
                continue
            for group_attr in group.attrs:
                if group_attr.id not in attr_ids:
                    continue
                attr = dish.attrs.add()
                attr.group_id = group.id
                attr.group_name = group.name
                attr.id = group_attr.id
                attr.name = group_attr.name
                attr.reprice = group_attr.reprice
                attr.sort = group_attr.sort
                attr.is_multi_select = group.is_multi_select
                attr.type = group.attr_group_type
                attr.status = group_attr.status

    def update_dish_update_selection_type(self, dish, selection_type, upper_limit, lower_limit):
        dish.selection_type.type = dish_pb.SupplyCondimentSelectionType.Type.Value(selection_type)
        if dish.selection_type.type == dish_pb.SupplyCondimentSelectionType.NUMBER_REQUIRED:
            dish.selection_type.upper_limit = upper_limit
            dish.selection_type.lower_limit = lower_limit
            dish.supply_condiment_uplimit = upper_limit
        if dish.selection_type.type == dish_pb.SupplyCondimentSelectionType.NUMBER_RANGE:
            dish.selection_type.upper_limit = upper_limit
            dish.selection_type.lower_limit = lower_limit
            dish.supply_condiment_uplimit = upper_limit

    def merchant_assist_get_dish_list(self):
        """业务助手获取菜品列表"""
        dish_list_vo = page_dish_pb.DishListVO()
        dish_list_vo.merchant_id = self.merchant.id
        self.set_dish_vo(dish_list_vo)
        self.set_attr_group_vo(dish_list_vo)
        self.set_supply_condiment_vo(dish_list_vo)
        for dish_category in dish_list_vo.dish_categories:
            dish_category.dishes.sort(key=lambda x: x.sort)
        dish_list_vo.dish_categories.sort(key=lambda x: x.sort)
        return dish_list_vo

    def set_dish_vo(self, dish_list_vo):
        dishes_changeful_info = None
        if hasattr(self.dish_manager, "get_dishes_changeful_info"):
            dishes_changeful_info = self.dish_manager.get_dishes_changeful_info()
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(merchant_id=self.merchant.id)
        categories = ordering_da.get_categories(merchant_id=self.merchant.id)
        categories = {c.id: c for c in categories}
        category_id_map_dish_category_vo = {}
        for dish in dishes:
            for category_id in dish.categories:
                dish_category_vo = category_id_map_dish_category_vo.get(category_id)
                category = categories.get(category_id)
                if not category:
                    continue
                if category.status == dish_pb.DishCategory.DELETE:
                    continue
                if not dish_category_vo:
                    dish_category_vo = dish_list_vo.dish_categories.add()
                    category_id_map_dish_category_vo.update({category_id: dish_category_vo})
                    dish_category_vo.id = category.id
                    dish_category_vo.name = category.name
                    dish_category_vo.sort = category.sort
                dish_vo = dish_category_vo.dishes.add()
                dish_vo.id = dish.id
                dish_vo.name = dish.name
                dish_vo.type = dish.type
                dish_vo.price = dish.price
                dish_vo.sort = dish.sort
                dish_vo.category_sort = category.sort
                if len(dish.images) > 0:
                    dish_vo.image_url = dish.images[0]
                dish_vo.thumb_image = dish.thumb_image
                dish_vo.shilai_dish_image = dish.shilai_dish_image
                dish_vo.shilai_dish_thumb_image = dish.shilai_dish_thumb_image
                dish_vo.status = dish.status
                dish_vo.desc = dish.desc
                dish_vo.supply_condiment_group_name = dish.supply_condiment_group_name
                dish_changeful_info = None
                if dishes_changeful_info:
                    dish_changeful_info = dishes_changeful_info.get(dish.id)
                rq = self.get_dish_remain_quantity(dish, dish_changeful_info)
                dish_vo.available_quantity = rq.available_quantity
                dish_vo.remain_quantity = rq.remain_quantity
                dish_vo.enable_quantity_setting = rq.enable_quantity_setting

    def set_supply_condiment_vo(self, dish_list_vo):
        dish_supply_condiment_template_da = DishSupplyCondimentDataAccessHelper()
        supply_condiment_templates = dish_supply_condiment_template_da.get_supply_condiments(merchant_id=self.merchant.id)
        for template in supply_condiment_templates:
            supply_condiment_vo = dish_list_vo.supply_condiments.add()
            supply_condiment_vo.id = template.id
            supply_condiment_vo.name = template.name
            supply_condiment_vo.market_price = template.market_price
            supply_condiment_vo.status = template.status
            supply_condiment_vo.sort = template.sort

    def set_attr_group_vo(self, dish_list_vo):
        dish_attr_da = DishAttrDataAccessHelper()
        groups = dish_attr_da.get_attr_groups(merchant_id=self.merchant.id)
        for group in groups:
            attr_group_vo = dish_list_vo.attr_groups.add()
            attr_group_vo.id = group.id
            attr_group_vo.group_name = group.name
            attr_group_vo.status = group.status
            attr_group_vo.sort = group.sort
            attr_group_vo.attr_group_type = group.attr_group_type
            for attr in group.attrs:
                if attr.status == dish_pb.Attr.DELETED:
                    continue
                attr_vo = attr_group_vo.attrs.add()
                attr_vo.id = attr.id
                attr_vo.name = attr.name
                attr_vo.reprice = attr.reprice
                attr_vo.sort = attr.sort
                attr_vo.status = attr.status
                attr_vo.type = group.attr_group_type

    def merchant_assist_reorder_dish(self, dish_ids, category_id):
        """对某个分类下的菜品排序
        `dishes`: 数组, [dish_id1, dish_id2]
            数组索引作为菜品的排序值
        `category_id`: 菜品分类列表
        """
        ordering_da = OrderingServiceDataAccessHelper()
        db_dishes = ordering_da.get_dishes(merchant_id=self.merchant.id, category_id=category_id)
        db_dishes = {d.id: d for d in db_dishes}
        for index, dish_id in enumerate(dish_ids):
            db_dish = db_dishes.get(dish_id)
            db_dish.sort = index + 1
        ordering_da.add_or_update_dishes(dishes=db_dishes, merchant_id=self.merchant.id)

    def merchant_assist_get_dish_detail(self, dish_id):
        """获取菜品详情"""
        dishes_changeful_info = None
        if hasattr(self.dish_manager, "get_dishes_changeful_info"):
            dishes_changeful_info = self.dish_manager.get_dishes_changeful_info()
        if dish_id is None:
            raise errors.ShowError("菜品ID不能为空")
        ordering_da = OrderingServiceDataAccessHelper()
        dish = ordering_da.get_dish(merchant_id=self.merchant.id, dish_id=dish_id)
        if not dish:
            raise errors.ShowError("菜品不存在")
        dish_detail_vo = page_dish_pb.DishDetailVO()
        dish_detail_vo.id = dish.id
        dish_detail_vo.name = dish.name
        if len(dish.images) > 0:
            dish_detail_vo.image_url = dish.images[0]
        dish_detail_vo.thumb_image = dish.thumb_image
        self.list_update(dish_detail_vo.child_dish_groups, dish.child_dish_groups)
        dish_detail_vo.shilai_dish_image = dish.shilai_dish_image
        dish_detail_vo.shilai_dish_thumb_image = dish.shilai_dish_thumb_image
        dish_detail_vo.category_id = dish.categories[0]
        category = ordering_da.get_category(merchant_id=self.merchant.id, id=dish_detail_vo.category_id)
        dish_detail_vo.category_name = category.name
        dish_detail_vo.sort = dish.sort
        dish_detail_vo.price = dish.price
        dish_detail_vo.status = dish.status
        dish_detail_vo.sale_begin_day = dish.sale_begin_day
        dish_detail_vo.sale_end_day = dish.sale_end_day
        dish_detail_vo.supply_condiment_uplimit = dish.supply_condiment_uplimit
        dish_detail_vo.remain_quantity = dish.remain_quantity
        dish_detail_vo.enable_quantity_setting = dish.enable_quantity_setting
        dish_detail_vo.available_quantity = dish.available_quantity
        dish_changeful_info = None
        if dishes_changeful_info:
            dish_changeful_info = dishes_changeful_info.get(dish.id)
        rq = self.get_dish_remain_quantity(dish, dish_changeful_info)
        dish_detail_vo.available_quantity = rq.available_quantity
        dish_detail_vo.remain_quantity = rq.remain_quantity
        dish_detail_vo.enable_quantity_setting = rq.enable_quantity_setting
        dish_detail_vo.tag = dish.tag
        dish_detail_vo.desc = dish.desc
        dish_detail_vo.supply_condiment_group_name = dish.supply_condiment_group_name

        for attr in dish.attrs:
            if attr.status != dish_pb.Attr.NORMAL:
                continue
            attr_vo = dish_detail_vo.attrs.add()
            attr_vo.name = attr.name
            attr_vo.reprice = attr.reprice
            attr_vo.status = attr.status
            attr_vo.sort = attr.sort
            attr_vo.id = attr.id
            attr_vo.type = attr.type
        for supply_condiment in dish.supply_condiments:
            if supply_condiment.status != dish_pb.SupplyCondiment.NORMAL:
                continue
            supply_condiment_vo = dish_detail_vo.supply_condiments.add()
            supply_condiment_vo.id = supply_condiment.id
            supply_condiment_vo.name = supply_condiment.name
            supply_condiment_vo.market_price = supply_condiment.market_price
            supply_condiment_vo.sort = supply_condiment.sort
        for sale_time in dish.sale_times:
            sale_time_vo = dish_detail_vo.sale_times.add()
            sale_time_vo.start = sale_time.start
            sale_time_vo.end = sale_time.end
            sale_time_vo.weekday = sale_time.weekday
        dish_detail_vo.selection_type.CopyFrom(dish.selection_type)
        return dish_detail_vo

    def increase_dish_remain_quantity(self, dishes_info):
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
        ]:
            return
        if not self.dishes:
            self.load_dish()
        self.dish_manager.increase_dish_remain_quantity(dishes_info, dishes_cache=self.dishes)

    def decrease_dish_remain_quantity(self, dishes_info, raise_error=False):
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
        ]:
            return
        if not self.dishes:
            self.load_dish()
        self.dish_manager.decrease_dish_remain_quantity(dishes_info, dishes_cache=self.dishes, raise_error=raise_error)

    def check_dish_remain_quantity(self, dishes_info):
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
        ]:
            return
        if not self.dishes:
            self.load_dish()
        self.dish_manager.check_dish_remain_quantity(dishes_info, dishes_cache=self.dishes)

    def clear_dish(self):
        if not self.merchant:
            raise errors.ShowError("商家不存在")

        pos_type = registration_pb.OrderingServiceRegistrationInfo.PosType.Name(self.registration_info.pos_type)
        h5_domain = os.environ.get("H5_SERVICE_DOMAIN", "http://shilai-h5.zhiyi.cn")
        resp = requests.get(
            f"https://test.shilai.zhiyi.cn/crawling-menu-service/menu?mode={str(pos_type).lower()}&qrcodeUrl={h5_domain}/merchant?merchantId={self.merchant.id}"
        )
        resp.raise_for_status()

        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.remove_recommend_dish(self.merchant.id)
        return dict(
            {
                'attr_count': DishAttrDataAccessHelper()
                ._ordering_attr_group_collection.delete_many({'merchantId': self.merchant.id})
                .deleted_count,
                'supply_count': DishSupplyCondimentDataAccessHelper()
                ._ordering_supply_condiment_collection.delete_many({'merchantId': self.merchant.id})
                .deleted_count,
            },
            **OrderingServiceDataAccessHelper().clear_dish(self.merchant.id),
        )

    def remove_recommend_dish(
        self,
        dish: dish_pb.Dish = None,
    ):
        if dish.status != dish_pb.Dish.DISABLE:
            return
        dish_id = dish.id if dish is not None else None
        return OrderingServiceDataAccessHelper().remove_recommend_dish(merchant_id=self.merchant.id, dish_id=dish_id)

    def bind_dish_printer(
        self,
        dish: dish_pb.Dish = None,
    ):
        printer_config_manager = PrinterConfigManager()
        configs = printer_config_manager.get_config(merchant_id=self.merchant.id)
        for conf in configs.get("printer_configs", []):
            bind_dish = conf.get("bindDish", {})
            bind_dish_ids = bind_dish.get("dishIds", [])
            if dish.status == dish_pb.Dish.DISABLE:
                if dish.id in bind_dish_ids:
                    bind_dish_ids.remove(dish.id)

        printer_config_manager.update_config(merchant_id=self.merchant.id, **configs)

# -*- coding: utf-8 -*-

from business_ops.ordering.hualala_ops import HualalaOPSManager
from business_ops.ordering.base_table_manager import BaseTableManager
from business_ops.ordering.constants import HualalaConstants
from common.utils import id_manager


class HualalaTableManager(HualalaOPSManager, BaseTableManager):

    def __init__(self, table_manager=None, *args, **kargs):
        self._manager = table_manager
        super(HualalaTableManager, self).__init__(*args, **kargs)

    def get_tables(self):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id
        }
        params.update(self.must_params)
        route = HualalaConstants.GET_SHOP_TABLES
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data.get("shopTableAreas")
        return None

    def sync_tables(self):
        tables = self.get_tables()
        if not tables:
            return
        ret = []
        for table in tables:
            table_obj = {
                "id": id_manager.generate_common_id(),
                "ordering_service_table_id": table.get("tableID"),
                "name": table.get("tableName"),
                "table_person_count": table.get("maxPerson"),
                "area_name": table.get("areaName"),
                "area_code": table.get("areaKey"),
                "area_id": table.get("areaID"),
                "sort": table.get("sortIndex"),
                "can_booking": table.get("isReceiveReserve") == "true"
            }
            ret.append(table_obj)
        return ret

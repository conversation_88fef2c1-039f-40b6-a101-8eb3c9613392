import time
import logging
from itertools import product

from business_ops.base_manager import BaseManager
from common.utils.id_manager import generate_common_id
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.dish_sale_time_da_helper import DishSaleTimeDataAccessHelper
from proto.common import datetime_pb2 as datetime_pb2
import proto.ordering.dish_pb2 as dish_pb

logger = logging.getLogger(__name__)


class DishSaleTimeManager(BaseManager):

    @property
    def dao(self):
        return DishSaleTimeDataAccessHelper()

    @property
    def dish_dao(self):
        return OrderingServiceDataAccessHelper()

    def get_dish_sale_time_list(self):
        return self.dao.get_dish_sale_time_list(merchant_id=self.merchant.id)

    def get_dish_sale_time(self, id=None, name=None):
        result = self.dao.get_dish_sale_time(id=id, name=name, merchant_id=self.merchant.id)
        return result

    def create_dish_sale_time(self, name):
        dish_sale_time = dish_pb.DishSaleTime()
        dish_sale_time.id = generate_common_id()
        dish_sale_time.merchant_id = self.merchant.id
        dish_sale_time.name = name
        dish_sale_time.status = dish_pb.DishSaleTime.SaleTimeStatus.ACTIVE
        dish_sale_time.create_time = str(int(time.time()))
        dish_sale_time.update_time = str(int(time.time()))
        return dish_sale_time

    def format_sale_times_of_dish(self, sale_times):
        def clock_time2int(ct):
            hours, minutes = ct.split(":")
            return int(hours) * 3600 + int(minutes) * 60

        new_sale_times = list()
        for sale_time in sale_times:
            if not sale_time.periods and sale_time.weekdays:
                for weekday in sale_time.weekdays:
                    st_obj = dish_pb.Dish.SaleTime()
                    st_obj.weekday = weekday
                    st_obj.start_date = sale_time.start_date
                    st_obj.end_date = sale_time.end_date
                    new_sale_times.append(st_obj)
            elif not sale_time.weekdays and sale_time.periods:
                for period in sale_time.periods:
                    st_obj = dish_pb.Dish.SaleTime()
                    st_obj.start = clock_time2int(period.start)
                    st_obj.end = clock_time2int(period.end)
                    st_obj.start_date = sale_time.start_date
                    st_obj.end_date = sale_time.end_date
                    new_sale_times.append(st_obj)
            else:
                for weekday, period in product(sale_time.weekdays, sale_time.periods):
                    st_obj = dish_pb.Dish.SaleTime()
                    st_obj.start = clock_time2int(period.start)
                    st_obj.end = clock_time2int(period.end)
                    st_obj.weekday = weekday
                    st_obj.start_date = sale_time.start_date
                    st_obj.end_date = sale_time.end_date
                    new_sale_times.append(st_obj)
        return new_sale_times

    def batch_update_sale_times_of_dish(self, dish_ids):
        """
        菜品与sale_time 是 多对多的关系
        :param dish_ids:
        """
        dishes = list()
        logger.info(f"batch_update_sale_times_of_dish: {dish_ids}")
        for dish in self.dish_dao.get_dishes(dish_ids=list(dish_ids)):
            sale_times = self.dao.get_dish_sale_time_list(
                merchant_id=self.merchant.id, dish_id=dish.id, status=dish_pb.DishSaleTime.SaleTimeStatus.ACTIVE)
            new_sale_times = self.format_sale_times_of_dish(sale_times)
            self.list_update(dish.sale_times, new_sale_times)
            dishes.append(dish)
        if dishes:
            self.dish_dao.add_or_update_dishes(dishes=dishes, merchant_id=self.merchant.id)
        return dishes

    def update_sale_time_of_dishes(self, dish_ids, sale_time):
        """
        转换 sale time
        """
        new_sale_times = self.format_sale_times_of_dish([sale_time])
        dishes = self.dish_dao.get_dishes(dish_ids=dish_ids)
        for dish in dishes:
            self.list_update(dish.sale_times, new_sale_times)
        if dishes:
            self.dish_dao.add_or_update_dishes(dishes=dishes, merchant_id=self.merchant.id)
        return dishes

    def _update_sale_time_period(self, sale_time, periods):
        new_periods = list()
        for period in periods:
            period_obj = dish_pb.DishSaleTime.DishSaleTimePeriod()
            if period.get('start'):
                period_obj.start = period['start']
            if period.get('end'):
                period_obj.end = period['end']
            new_periods.append(period_obj)
        self.list_update(sale_time.periods, new_periods)
        return sale_time

    def update_dish_sale_time(self, sale_time, name=None, start_date=None, end_date=None, periods=None,
                              weekdays=None, dish_ids=None, status=None):
        if name is not None:
            sale_time.name = name
        if start_date is not None:
            sale_time.start_date = str(start_date)
        if end_date is not None:
            sale_time.end_date = str(end_date)
        if periods is not None:
            self._update_sale_time_period(sale_time, periods)
        if weekdays is not None:
            weekdays = [datetime_pb2.Weekday.Value(d) for d in weekdays]
            self.list_update(sale_time.weekdays, weekdays)
        if dish_ids is not None:
            self.list_update(sale_time.dish_ids, dish_ids)
        if status is not None:
            sale_time.status = dish_pb.DishSaleTime.SaleTimeStatus.Value(status)
        sale_time.update_time = str(int(time.time()))
        return sale_time

    def delete_dish_sale_time(self, dish_sale_time_id):
        return self.dao.delete_dish_sale_time(dish_sale_time_id)

    def add_or_update_dish_sale_time(self, sale_time):
        return self.dao.add_or_update_dish_sale_time(sale_time)

# -*- coding: utf-8 -*-

import logging

import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.registration_pb2 as registration_pb
from google.protobuf import json_format
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from business_ops.ordering.base_dish_manager import BaseDishManager
from cache.redis_client import RedisClient
from common.cache_server_keys import CacheServerKeys
from service import error_codes
from service import errors


logger = logging.getLogger(__name__)


_INCREASE_DISH_REMAIN_QUANTITY_SCRIPT = """
for index=1,#KEYS,2 do
    local dish_id = string.sub(KEYS[index], 1, -1)
    local key = dish_id .. "_remain_quantity"
    local quantity = tonumber(KEYS[index+1])
    redis.call("INCRBY", key, quantity)
end
"""

_DECREASE_DISH_REMAIN_QUANTITY_SCRIPT = """
for index=1,#KEYS,2 do
    repeat
        local dish_id = string.sub(KEYS[index], 1, -1)
        local key = dish_id .. "_remain_quantity"
        local quantity = tonumber(KEYS[index+1])
        local remain_quantity = redis.call("GET", key)
        if remain_quantity == nil
        then
            break
        end
        if tonumber(remain_quantity) < quantity
        then
            return dish_id
        end
        break
    until true
end
for index=1,#KEYS,2 do
    local dish_id = string.sub(KEYS[index], 1, -1)
    local key = dish_id .. "_remain_quantity"
    local quantity = tonumber(KEYS[index+1])
    redis.call("DECRBY", key, quantity)
end
return 200
"""

_CHECK_DISH_REMAIN_QUANTITY_SCRIPT = """
for index=1,#KEYS,2 do
    repeat
        local dish_id = string.sub(KEYS[index], 1, -1)
        local key = dish_id .. "_remain_quantity"
        local quantity = tonumber(KEYS[index+1])
        local remain_quantity = redis.call("GET", key)
        if remain_quantity == nil
        then
            break
        end
        if tonumber(remain_quantity) < quantity
        then
            return dish_id
        end
        break
    until true
end
return 200
"""


class FeieDishManager(BaseDishManager):

    def __init__(self, *args, **kargs):
        dish_manager = kargs.get("dish_manager")
        if dish_manager is not None:
            kargs['merchant_id'] = dish_manager.merchant.id
        super().__init__(*args, **kargs)
        self._redis_client = RedisClient().get_connection()

    def increase_dish_remain_quantity(self, dishes_info, dishes_cache: dict = None):
        """恢复菜品余量"""
        if not dishes_info:
            return
        keys = self._get_keys(dishes_info)
        if not keys:
            return
        self._run_lua_script(
            _INCREASE_DISH_REMAIN_QUANTITY_SCRIPT,
            keys
        )
        logger.info(f"恢复菜品余量: dishes_info={dishes_info}")
        # 顾客撤销订单：已沽清菜品恢复上架
        self._update_dish_status(dishes_info, dishes_cache, is_decrease=False)

    def decrease_dish_remain_quantity(self, dishes_info=None, dishes_cache: dict = None, raise_error=False):
        """扣减菜品余量"""
        if not dishes_info:
            return
        keys = self._get_keys(dishes_info)
        if not keys:
            return
        status = self._run_decrease_dish_script(
            _DECREASE_DISH_REMAIN_QUANTITY_SCRIPT,
            keys,
            dishes_info,
            raise_error=raise_error
        )
        try:
            logger.info(f"扣减菜品余量，dishes_info={dishes_info}，status={status}")
            # 菜品余量为0时，设置菜品为沽清状态
            self._update_dish_status(dishes_info, dishes_cache, is_decrease=True)
        except Exception as e:
            logger.exception(f"扣减菜品余量失败, dishes_info={dishes_info}", exc_info=e)
            self.increase_dish_remain_quantity(dishes_info, dishes_cache)
            raise 

    def check_dish_remain_quantity(self, dishes_info: dict, dishes_cache: dict = None, **kwargs):
        """检测菜品余量是否满足扣减"""
        if not dishes_info:
            return
        # for dish_id, _ in dishes_info.items():
        #     dish = dishes_cache.get(dish_id)
        #     if not dish:
        #         continue
        #     if not dish.enable_quantity_setting:
        #         continue
        #     if dish.status in [dish_pb.Dish.Status.GUQING, dish_pb.Dish.Status.OFFLINE]:
        #         raise errors.DishSoldOut(dish.name)

        keys = self._get_keys(dishes_info)
        if not keys:
            return
        self._run_decrease_dish_script(
            _CHECK_DISH_REMAIN_QUANTITY_SCRIPT,
            keys,
            dishes_info,
            raise_error=True
        )

    def _get_keys(self, dishes_info: dict):
        keys = []
        for dish_id, info in dishes_info.items():
            if not info:
                continue
            keys.append(dish_id)
            keys.append(info.get("quantity"))
        return keys

    def _run_lua_script(self, lua: str, keys: list):
        callback = self._redis_client.register_script(lua)
        return callback(keys=keys)

    def _run_decrease_dish_script(self, lua, keys, dishes_info, raise_error=False):
        result = self._run_lua_script(lua, keys)
        if raise_error and result != 200:
            dish_name = dishes_info.get(result.decode()).get("name")
            logger.warning(f"菜品余量不足，下单菜品信息={dishes_info}")
            raise errors.DishNotEnough(
                err=error_codes.DISH_NOT_ENOUGH,
                name=dish_name
            )
        return result

    def _update_dish_status(self, dishes_info, dishes_cache, is_decrease=True):
        if self.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.FEIE:
            return
        if not dishes_cache:
            return

        ordering_da = OrderingServiceDataAccessHelper()
        for dish_id in dishes_info.keys():
            dish = dishes_cache.get(dish_id)
            if not dish:
                msg = "扣减" if is_decrease else "恢复"
                logger.warning(f"{msg}菜品余量失败，未找到菜品，dish_id={dish_id}")
                continue
            if is_decrease and not dish.enable_quantity_setting:
                continue
            if isinstance(dish, dict):
                dish = json_format.ParseDict(dish, dish_pb.Dish(), ignore_unknown_fields=True)
            value = self._redis_client.get(CacheServerKeys.get_dish_remain_quantity_key(dish.id))
            if value is None:
                continue
            value = int(value.decode())
            dish.is_auto_sold_out = False
            if is_decrease and value <= 0:
                normal = dish_pb.Dish.GUQING
                remain_quantity = 0
                dish.status = normal
                dish.remain_quantity = remain_quantity
                ordering_da.add_or_update_dish_info(
                    dish=dish,
                    status=normal,
                    is_auto_sold_out=False,
                    remain_quantity=remain_quantity
                )
                logger.info(f"扣减菜品余量，状态设置为沽清: dish_id={dish.id}, dish_name={dish.name}, remain_quantity={remain_quantity}")
            # elif not is_decrease and value > 0 and dish.status == dish_pb.Dish.GUQING:
            elif not is_decrease and value > 0:
                normal = dish_pb.Dish.NORMAL
                dish.status = normal
                dish.remain_quantity = value
                ordering_da.add_or_update_dish_info(
                    dish=dish,
                    status=normal,
                    is_auto_sold_out=False,
                    remain_quantity=value
                )
                logger.info(f"恢复菜品余量，状态设置为上架: dish_id={dish.id}, dish_name={dish.name}, remain_quantity={value}")

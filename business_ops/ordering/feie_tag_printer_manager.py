# -*- coding: utf-8 -*-

"""
Filename: feie_tag_printer_manager.py
Date: 2020-07-03 16:10:27
Title: 飞鹅标签机
"""

import hashlib
import json
import time
import requests
import logging
import pytz
import re

from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.constants import XPRINTER, FeieTagPrinter
from common.utils import date_utils
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
import proto.ordering.registration_pb2 as registration_pb
from service import errors

logger = logging.getLogger(__name__)


class FeieTagPrinterManager(PrinterManager):
    __chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】《》]")

    def __init__(self, *args, merchant_id=None, merchant=None, registration_info=None, **kargs):
        super(FeieTagPrinterManager, self).__init__(
            *args, merchant=merchant, mercahnt_id=merchant_id, registration_info=registration_info, **kargs
        )
        self.contents = {}

    def __call_printer(self, printer_sn, content, type):
        if content == "":
            return

        stime = str(int(time.time()))
        try:
            if type == registration_pb.PrinterConfigByType.Type.FEIE:
                signature = hashlib.sha1(str(FeieTagPrinter.USER + FeieTagPrinter.UKEY + stime).encode('utf-8')).hexdigest()
                params = {
                    'user': FeieTagPrinter.USER,
                    'sig': signature,
                    'stime': stime,
                    'apiname': 'Open_printLabelMsg',
                    'sn': printer_sn,
                    'content'
                    if isinstance(content, str)
                    else 'contents': content
                    if isinstance(content, str)
                    else json.dumps(content, ensure_ascii=False),
                    # 'times': '1',
                }
                logger.info("飞鹅标签机参数-{}: {}".format(printer_sn, params))
                try:
                    response = requests.post(FeieTagPrinter.URL, data=params, timeout=30)
                    logger.info("调用飞鹅标签机返回: {} {}".format(response.content.decode(), printer_sn))
                except requests.exceptions.ReadTimeout as ex:
                    logger.info("调用飞鹅标签机接口出错ReadTimeout: {}".format(ex))
                    raise errors.RequestsTimeoutError()
                except Exception as ex:
                    logger.info("调用飞鹅标签机接口出错CommonError: {}".format(ex))

            else:
                content = content.replace('font="12"', 'font="9"')
                signature = hashlib.sha1(str(XPRINTER.USER + XPRINTER.UKEY + stime).encode('utf-8')).hexdigest()
                params = {
                    'user': XPRINTER.USER,
                    'sign': signature,
                    'timestamp': stime,
                    'sn': printer_sn,
                    'content': content,
                    'copies': '1',
                    'mode': 1,
                    'expiresIn': 43200,
                }
                logger.info("芯烨标签机参数-{}: {}".format(printer_sn, params))
                try:
                    response = requests.post(
                        XPRINTER.URL + "printLabel",
                        data=json.dumps(params),
                        timeout=30,
                        headers={"Content-Type": "application/json;charset=UTF-8"},
                    )
                    logger.info(f"芯烨标签机返回: {response.content.decode()} {printer_sn}")
                except requests.exceptions.ReadTimeout as ex:
                    logger.info("调用芯烨标签机接口出错ReadTimeout: {}".format(ex))
                except Exception as ex:
                    logger.info("调用芯烨标签机接口出错CommonError: {}".format(ex))
        except Exception as e:
            logger.error(e)

    def init_text_setting(self, size=40):
        self.size = size
        # 字宽为1倍
        self.w = 1
        # 字高为1倍
        self.h = 1 if size == 40 else 2
        # w=1,h=1时,字的高度
        self.font_height = 25
        # 当前写了多少行
        self.line_number = 1
        # 左空白
        self.x_start = 12
        # 上空白
        self.y_start = 12
        # 一行最多能写多少个字.w=1,h=1时,一行最多只能显示13个字.使用只使用最多12个字
        self.max_font_num = 12 if size == 40 else 16
        self.single_h_height = 1
        self.prev_y = 0
        self.product_price = 0

        self.template_0 = {
            "product_name": f'<TEXT x="5" y="5" font="12" w="{self.w}" h="{self.h}">' + '{product_name}</TEXT>',
            "attrs": f'<TEXT x="5" y="30" font="12" w="{self.w}" h="{self.w}">' + '{attrs}</TEXT>',
            "supply_condiments": f'<TEXT x="5" y="85" font="12" w="{self.w}" h="{self.w}">' + '{supply_condiments}</TEXT>',
            "serial_number": f'<TEXT x="5" y="120" font="12" w="{self.w * 2}" h="{self.w * 2}">' + '{serial_number}</TEXT>',
            "price": f'<TEXT x="5" y="180" font="12" w="{self.w}" h="{self.w}">' + '价格: {price}</TEXT>',
            "paid_time": f'<TEXT x="5" y="210" font="12" w="1" h="1">' + '{paid_time}</TEXT>',
        }

        self.template_value = {}

    def build_content_text(self, value, w=None, h=None, center=False):
        y = (self.line_number - 1) * self.font_height + self.y_start
        if w is None:
            w = self.w
        if h is None:
            h = self.h
        x = self.x_start
        if center:
            count = self.calculate_total_char_count(w, value)
            if self.size == 50:
                x = int((50 * 8 - (50 / 32) * count * 8) / 2)
            else:
                x = int((40 * 8 - (40 / 26) * count * 8) / 2)
        content = '<TEXT x="{}" y="{}" font="12" w="{}" h="{}">{}</TEXT>'.format(x, y, w, h, value)
        self.line_number += int(h / self.single_h_height)
        return content

    def pay_success(self, order, transaction, table):
        if not self.registration_info.printer_config.feie_printer.enable_tag_print:
            return

        category_products = {}
        for p in order.products:
            products = category_products.get(p.category_id, [])
            products.append(p)
            category_products.update({p.category_id: products})

        printer_sns = {}
        ordering_da = OrderingServiceDataAccessHelper()

        tag_printer_sns = []
        if self.registration_info.printer_config.feie_printer.tag_printer_sn:
            tag_printer_sns.append(self.registration_info.printer_config.feie_printer.tag_printer_sn)
        if self.registration_info.printer_config.feie_printer.tag_printer_sns:
            tag_printer_sns.extend(self.registration_info.printer_config.feie_printer.tag_printer_sns)

        for category_id, ps in category_products.items():
            dish_category_printer = ordering_da.get_dish_category_printer(self.merchant.id, category_id)
            if dish_category_printer:
                for printer in dish_category_printer.printer_sns:
                    if printer in tag_printer_sns:
                        pssss = printer_sns.get(printer, [])
                        pssss.extend(ps)
                        printer_sns.update({printer: pssss})
            else:
                # 如果没有为这个分类设置标签机就用默认的标签机
                pssss = printer_sns.get(self.registration_info.printer_config.feie_printer.tag_printer_sn, [])
                pssss.extend(ps)
                printer_sns.update({self.registration_info.printer_config.feie_printer.tag_printer_sns: pssss})

        totals = {}
        for printer_sn, products in printer_sns.items():
            total = 0
            for product in products:
                total += int(product.quantity)
            totals.update({printer_sn: total})

        table = ordering_da.get_table(id=order.table_id, merchant_id=order.merchant_id)
        if not table:
            table = ordering_da.get_table(ordering_service_table_id=order.table_id, merchant_id=order.merchant_id)
        table_name = "无"
        if table_name:
            table_name = table.name
        for printer_sn, products in printer_sns.items():
            current_index = 1
            printer_config = self.printer_config.get(printer_sn, {})
            size = 40
            if printer_config and printer_config.specification == '58mm':
                size = 50
            self.line_number = 1
            contents = []
            for product in products:
                quantity = int(product.quantity)
                for i in range(quantity):
                    # 每份菜品显示在一张纸上
                    self.init_text_setting(size)
                    content = self.build_product_content(order, product, totals.get(printer_sn), current_index, table_name)
                    contents.append(content)
                    current_index += 1
            self.contents.update({printer_sn: contents})

    def build_product_content(self, order, product, total, index, table_name):
        count = "[{}/{}]".format(index, total)
        content = ""
        if order.meal_code:
            title = "桌台:{} {}:{} {}".format(table_name, "取餐号", order.meal_code, count)
            content += self.build_content_text(title)
        else:
            title = "桌台:{} {}:{} {}".format(table_name, "流水号", order.serial_number, count)
            content += self.build_content_text(title)
        # content += self.build_content_text("{}     {}".format("时来收银", count))
        content += self.build_product(product.name, w=1 if self.size == 40 else 2, h=2)
        if len(product.attrs) > 0:
            content += self.build_product_attr_content(product, w=1, h=1 if self.size == 40 else 2)

        if len(product.supply_condiments) > 0:
            content += self.build_product_supply_condiment(product, w=1, h=1 if self.size == 40 else 2)
        # content += self.build_contact()
        content += self.build_paid_time(order.create_time, w=1, h=1, center=True)
        if order.remark:
            content += self.build_remark(order.remark)
        return content

    def build_remark(self, remark):
        remark = "备注:" + remark
        if len(remark) > 25:
            content = self.build_content_text(remark[0:12])
            content += self.build_content_text(remark[12:25])
            content += self.build_content_text(remark[25:])
        elif len(remark) > 12:
            content = self.build_content_text(remark[0:12])
            content += self.build_content_text(remark[12:])
        else:
            content = self.build_content_text(remark)
        return content

    def build_contact(self):
        content = ""
        # if self.merchant and self.merchant.basic_info.contact_mobile:
        #     content += self.build_content_text(self.merchant.basic_info.contact_mobile)
        if self.store and self.store.address:
            content += self.build_content_text(self.store.address)
        return content

    def build_product(self, value, w=1, h=2):
        slice_num = int(self.max_font_num / w)
        if len(value) * w > self.max_font_num * 2 + 1:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num : slice_num * 2 + 1], w, h)
            content += self.build_content_text(value[slice_num * 2 + 1 :], w, h)
        elif len(value) * w > self.max_font_num:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num:], w, h)
        else:
            content = self.build_content_text(value, w, h)
        return content

    def build_product_attr_content(self, product, w=1, h=1):
        if len(product.attrs) == 0:
            return ""
        contents = []
        for attr in product.attrs:
            contents.append(attr.name)
        value = "属性:{}".format(",".join(contents))
        slice_num = int(self.max_font_num / w)
        if len(value) * w > self.max_font_num * 2 + 1:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num : slice_num * 2 + 1], w, h)
            content += self.build_content_text(value[slice_num * 2 + 1 :], w, h)
        elif len(value) * w > self.max_font_num:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num:], w, h)
        else:
            content = self.build_content_text(value, w, h)
        return content

    def build_product_supply_condiment(self, product, w=1, h=1):
        if len(product.supply_condiments) == 0:
            return ""
        contents = []
        for supply_condiment in product.supply_condiments:
            contents.append("{}.x{}".format(supply_condiment.name, int(supply_condiment.quantity / product.quantity)))
        value = "加料:{}".format(",".join(contents))
        slice_num = int(self.max_font_num / w)
        if len(value) * w > self.max_font_num * 2 + 1:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num : slice_num * 2 + 1], w, h)
            content += self.build_content_text(value[slice_num * 2 + 1 :], w, h)
        elif len(value) * w > self.max_font_num:
            content = self.build_content_text(value[0:slice_num], w, h)
            content += self.build_content_text(value[slice_num:], w, h)
        else:
            content = self.build_content_text(value, w, h)
        return content

    def build_serial_number(self, serial_number):
        content = "流水号:{}".format(serial_number)
        content = self.build_content_text(content, w=2)
        return content

    def build_paid_time(self, create_time, w=1, h=1, center=True):
        paid_time = date_utils.timestamp_to_string(create_time, fromtz=pytz.timezone('Asia/Shanghai'))
        return self.build_content_text(str(paid_time), w, h, center)

    def print(self):
        if not self.contents or not isinstance(self.contents, dict):
            return
        for printer_sn, contents in self.contents.items():
            printer_config = self.printer_config.get(printer_sn, {})
            if not printer_config or printer_config.printer_type != 'tag':
                continue
            size = 40
            if printer_config.specification == '58mm':
                size = 50
            size = f"<SIZE>{size},{(size-10)}</SIZE>"
            if printer_config.type == registration_pb.PrinterConfigByType.Type.FEIE:
                if not printer_config.is_send_spec:
                    size = ''
                else:
                    printer_config.is_send_spec = False
                    printer_config_da = PrinterConfigDataAccessHelper()
                    printer_config_da.add_or_update_printer_config(printer_config)
                tmp_contents = []
                for content in contents:
                    content = self.entirety_bold_content(printer_sn, content)
                    content = {'content': f"{size}<DIRECTION>1</DIRECTION>" + content, 'times': 1}
                    tmp_contents.append(content)
                self.__call_printer(printer_sn, tmp_contents, printer_config.type)
            else:
                text = ''
                for content in contents:
                    content = self.entirety_bold_content(printer_sn, content)
                    text += f'<PAGE>{size}{content}</PAGE>'
                self.__call_printer(printer_sn, text, printer_config.type)

    def chinese_count(self, string):
        """计算中文字符的个数"""
        string = str(string)
        chinese_array = self.__chinese_re_pattern.findall(string)
        return len(chinese_array)

    def word_count(self, string):
        """计算字符的总个数"""
        string = str(string)
        cn_count = self.chinese_count(string)
        total_count = len(string)
        en_count = total_count - cn_count
        return cn_count, en_count

    def calculate_total_char_count(self, w, content):
        """返回content在 formater 格式下应该占用多少个ASCII字符数"""
        width_ratio = w
        cn_count, en_count = self.word_count(content)
        cn_char_number = cn_count * 2 * width_ratio
        en_char_number = en_count * width_ratio
        total_char_count = cn_char_number + en_char_number
        return total_char_count

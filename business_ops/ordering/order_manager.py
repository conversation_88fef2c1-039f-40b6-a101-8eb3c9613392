# -*- coding: utf-8 -*-

import logging
import time
import inspect
from collections import namedtuple
from math import sin, cos, sqrt, atan2, radians
from datetime import datetime
from datetime import timedelta

from pytz import timezone

import proto.ordering.dish_pb2 as dish_pb
import proto.page.dish_pb2 as page_dish_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.coupons_pb2 as coupons_pb
import proto.ordering.keruyun.export_detail_pb2 as export_detail_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.payment_manager import PaymentManager
from business_ops.wallet_manager import WalletManager
from business_ops.logistics.dada_manager import DadaManager
from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.keruyun_dinner_manager import KeruyunDinnerManager
from business_ops.ordering.keruyun_fast_food_manager import KeruyunFastFoodManager
from business_ops.ordering.shilai_order_manager import <PERSON><PERSON>OrderManager
from business_ops.ordering.feie_order_manager import FeieOrderManager
from business_ops.ordering.hualala_dinner_manager import HualalaDinnerManager
from business_ops.ordering.hualala_fast_food_manager import HualalaFastFoodManager
from business_ops.transaction_manager import TransactionManager
from business_ops.base_manager import BaseManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.table_manager import TableManager
from business_ops.ordering.none_exists_order_manager import NoneExistsOrderManager
from business_ops.data_center_helper import DataCenterHelper
from business_ops.coupon_manager import CouponManager
from business_ops.ordering.shop_manager import ShopManager
from business_ops.fanpiao_helper import FanpiaoHelper
from business_ops.promotion.coupon.coupon_manager import CouponManager as NewCouponManager
from common.utils import id_manager
from common.utils import date_utils
from cache.redis_client import RedisClient
from common.utils import distribute_lock
from common.cache_server_keys import CacheServerKeys
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from proto.group_dining import red_packet_pb2 as red_packet_pb
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class OrderManager(BaseManager):
    # 时来订单退款
    SHILAI_POS_ORDER_REFUND = 0x10
    # 正餐订单确认
    SHILAI_POS_ORDER_CONFIRM = 0x11
    # 正餐订单拒绝
    SHILAI_POS_ORDER_REFUSE = 0x12
    # 从收银机创建订单
    SHILAI_POS_ORDER_CREATE = 0x13
    # 收银机上订单已支付
    SHILAI_POS_ORDER_PAID = 0x14
    # 收银机上菜品有更新
    SHILAI_POS_ORDER_ITEM_UPDATE = 0x15
    # 订单已撤消
    SHILAI_POS_ORDER_REVOKE = 0x16
    # 部分退款
    SHILAI_POS_ORDER_PARTIAL_REFUND = 0x17
    # 转台
    SHILAI_CHANGE_TABLE = 0x18

    def __init__(self, business_obj=None, *args, **kargs):
        super(OrderManager, self).__init__(*args, **kargs)
        self.business_obj = business_obj
        self.init_order_manager(*args, **kargs)
        self.discount_plan = None
        self.redis_client = RedisClient().get_connection()
        if self.merchant:
            self.printer_manager = PrinterManager(merchant=self.merchant)
            self.dish_manager = DishManager(merchant=self.merchant, user=self.user)
            self.table_manager = TableManager(merchant=self.merchant)
        self._order_manager_v2 = kargs.get("order_manager_v2")

    def init_order_manager(self, *args, **kargs):
        if not self.registration_info:
            # 某些函数无须self.order_manager
            return
        dinner_type = self.registration_info.ordering_config.dinner_type
        if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            self.order_manager = ShilaiOrderManager(order_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            if dinner_type == registration_pb.OrderingConfig.DINNER:
                self.order_manager = KeruyunDinnerManager(order_manager=self)
            elif dinner_type == registration_pb.OrderingConfig.FAST_FOOD:
                self.order_manager = KeruyunFastFoodManager(order_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.FEIE:
            self.order_manager = FeieOrderManager(order_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.NONE_EXISTS:
            self.order_manager = NoneExistsOrderManager(order_manager=self)
        elif self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.HUALALA:
            if dinner_type == registration_pb.OrderingConfig.DINNER:
                self.order_manager = HualalaDinnerManager(order_manager=self)
            elif dinner_type == registration_pb.OrderingConfig.FAST_FOOD:
                self.order_manager = HualalaFastFoodManager(order_manager=self)
        else:
            raise errors.Error(err=error_codes.POS_NOT_SUPPORTED)

    def create_shilai_order_obj(self, order_id=None):
        order = dish_pb.DishOrder()
        if order_id is not None:
            order.id = order_id
        else:
            order.id = id_manager.generate_common_id()
        order.merchant_id = self.merchant.id
        order.create_time = date_utils.timestamp_second()
        order.approve_time = order.create_time
        order.status = dish_pb.DishOrder.ORDERED
        # 需要根据限时折扣设置全场折扣
        order.max_discount = self.merchant.preferences.coupon_config.max_discount
        if self.user:
            order.user_id = self.user.id
            order.order_user_id = self.user.id
        order.settlement_rate = self.merchant.payment_info.settlement_rate
        order.shilai_service_rate = self.merchant.payment_info.shilai_service_rate
        order.create_time = date_utils.timestamp_second()
        extra_discount = self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        order.fanpiao_and_coupon_extra_discount = extra_discount
        return order

    def add_user_to_table_recently_order(self, user_id, table_id):
        """如果这个桌台6小时内有未结订单,那么就把这个用户加入到order的user_ids列表中"""
        if not user_id:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        before_time = int(time.time()) - 60 * 60 * 6
        recently_order = ordering_da.get_recently_order(
            merchant_id=self.merchant.id, table_id=table_id, before_time=before_time
        )
        if not recently_order:
            return
        if recently_order.user_id == user_id:
            return
        if user_id in recently_order.user_ids:
            return
        recently_order.user_ids.append(user_id)
        ordering_da.add_or_update_order(order=recently_order)

    def get_recently_order(self, user_id=None, table_id=None):
        ordering_da = OrderingServiceDataAccessHelper()
        before_time = int(time.time()) - 60 * 60 * 6
        recently_order = ordering_da.get_recently_order(
            merchant_id=self.merchant.id, table_id=table_id, before_time=before_time
        )
        if not recently_order:
            return
        if recently_order.user_id == user_id:
            pass
        if user_id in recently_order.user_ids:
            pass
        else:
            if user_id:
                recently_order.user_ids.append(user_id)
                ordering_da.add_or_update_order(order=recently_order)
        return recently_order

    def get_recently_unpaid_order(self, merchant_id, user_id=None, table_id=None):
        ordering_da = OrderingServiceDataAccessHelper()
        table = None
        recently_order = page_dish_pb.UserOrTableRecentlyOrder()
        before_time = int(time.time()) - 60 * 60 * 6

        table = ordering_da.get_table(id=table_id, merchant_id=merchant_id)

        if table:
            table_recently_order = ordering_da.get_user_recently_order(
                merchant_id=merchant_id, table_id=table.ordering_service_table_id, before_time=before_time
            )
            if table_recently_order:
                recently_order.table_order_id = table_recently_order.id
                table = ordering_da.get_table(ordering_service_table_id=table_recently_order.table_id, merchant_id=merchant_id)
                recently_order.table_recently_order_table_id = table.id
                recently_order.order_id = table_recently_order.id

        user_recently_order = ordering_da.get_user_recently_order(
            merchant_id=merchant_id, user_id=user_id, before_time=before_time
        )
        if user_recently_order:
            recently_order.user_order_id = user_recently_order.id
            table = ordering_da.get_table(ordering_service_table_id=user_recently_order.table_id, merchant_id=merchant_id)
            recently_order.user_recently_order_table_id = table.id
            recently_order.order_id = user_recently_order.id

        return recently_order

    def get_user_recently_order(self, user_id, merchant_id, table_id=None):
        ordering_da = OrderingServiceDataAccessHelper()
        before_time = int(time.time()) - 60 * 60 * 6
        order = ordering_da.get_user_recently_order(
            user_id=user_id, merchant_id=merchant_id, table_id=table_id, before_time=before_time
        )
        if not order:
            return None
        if order.user_id != user_id and user_id not in order.user_ids:
            return None

        self.dish_manager.get_discount_plan(user_id)

        order_vo = page_dish_pb.Order()
        order_vo.id = order.id
        order_vo.meal_code = order.meal_code
        for product in order.products:
            dish = self.dish_manager.get_dish_from_cache(product.id)
            discount_price = dish.price - self.dish_manager.get_discount_price(dish)  # 折后价
            order_dish = order_vo.dish_list.add()
            order_dish.name = dish.name
            order_dish.quantity = product.quantity
            order_dish.price = int(dish.price)
            order_dish.discount_price = int(discount_price)
            if len(dish.images) > 0:
                order_dish.image = dish.images[0]
        return order_vo

    def __transaction_info(self, order_vo, transaction):
        if not transaction:
            return
        order_vo.paid_time = transaction.paid_time
        order_vo.transaction_id = transaction.id
        order_vo.paid_fee = transaction.paid_fee
        order_vo.pay_method = transaction.pay_method
        if transaction.pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
            if transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                order_vo.pay_method = wallet_pb.Transaction.WECHAT_PAY
            elif transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                order_vo.pay_method = wallet_pb.Transaction.ALIPAY
        if transaction.use_coupon_id:
            coupon_id = transaction.use_coupon_id
            coupon = CouponDataAccessHelper().get_coupon_by_id(coupon_id)
            if coupon:
                coupon_category_da = CouponCategoryDataAccessHelper()
                coupon_category = coupon_category_da.get_coupon_category(coupon.coupon_category_id)
                order_vo.coupon_reduce = coupon_category.cash_coupon_spec.reduce_cost
            else:
                coupon_manager = NewCouponManager(self.user, self.merchant)
                coupon = coupon_manager.get_coupon(coupon_id)
                order_vo.coupon_reduce = coupon_manager.get_coupon_reduce_fee(order_vo.bill_fee, coupon) or 0

    def set_dish_shown(self, product, dish):
        product.shown = True
        if dish.id == self.registration_info.packaging_box_config.dish_id:
            product.shown = False

    def __products_info(self, order, order_vo):
        products = []
        products.extend(order.products)
        for _products in order.add_products:
            for product in _products.products:
                products.append(product)
        for index, product in enumerate(products):
            self.__product_info(order_vo, product, order_vo.dish_list)

        for product in order.to_be_confirmed_products:
            self.__product_info(order_vo, product, order_vo.to_be_confirmed_dish_list)

    def __product_info(self, order_vo, product, dish_list):
        dish = self.dish_manager.get_dish_from_cache(product.id)
        if not dish:
            return
        if dish.is_discount_dish and (dish.discount_amount == 0 or dish.discount_rate == 0):
            return
        order_dish = page_dish_pb.DishBrief()
        order_dish.id = dish.id
        order_dish.box_qty = dish.box_qty
        order_dish.name = dish.name
        order_dish.uuid = product.uuid
        order_dish.discount_price = product.discount_price
        if product.parent_uuid != "":
            order_dish.is_child_dish = True
            order_dish.parent_uuid = product.parent_uuid
            order_dish.uuid = product.uuid

        if dish.name == "配送费":
            order_vo.shipping_fee += int(dish.price * product.quantity)
        if dish.name == "打包盒":
            order_vo.packaging_fee += int(dish.price * product.quantity)

        order_dish.price = int(dish.price)
        order_dish.vip_price = int(dish.vip_price)
        order_dish.quantity = product.quantity
        order_dish.total_fee = product.total_fee
        order_dish.batch_number = product.batch_number
        order_dish.create_time = product.create_time
        self.set_dish_shown(order_dish, dish)
        if len(dish.images) > 0:
            order_dish.image = dish.images[0]
        elif dish.shilai_dish_image:
            order_dish.image = dish.shilai_dish_image
        for attr in product.attrs:
            _attr = order_dish.attrs.add()
            _attr.name = attr.name
            _attr.quantity = 1
            _attr.type = attr.type
            _attr.reprice = attr.reprice
            # 创建订单的时候 属性 的reprice已经加入到product的总金额里去了,就不用再加一次了
            # order_dish.total_fee += product.quantity * attr.reprice
        for supply_condiment in product.supply_condiments:
            # 创建订单的时候 加料 的金额并没有加入到product的总金额里去,这里就要加上
            order_dish.total_fee += supply_condiment.market_price * supply_condiment.quantity
            _attr = order_dish.attrs.add()
            _attr.name = supply_condiment.name
            _attr.quantity = supply_condiment.quantity / order_dish.quantity
            _attr.type = dish_pb.Attr.AttrType.SUPPLY_CONDIMENT
            _attr.reprice = supply_condiment.market_price

        order_dish.added_user_id = product.added_user_id
        order_dish.added_nickname = product.added_nickname
        order_dish.added_headimgurl = product.added_headimgurl

        dish_vo = dish_list.add()
        dish_vo.CopyFrom(order_dish)

    def get_order_v2(self, order_id, user_id, order=None):
        helper = DataCenterHelper(order_id=order_id)
        data = helper.get_order(order_id=order_id)
        order = data.get("data")
        if not order:
            return None
        # logger.info("get_order_v2: {}".format(order))
        merchant_id = order.get("merchantId")
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        order_vo = page_dish_pb.Order()
        order_vo.id = order.get("id")
        dine_in_order = order.get("dineInOrder", {})
        if dine_in_order:
            order_vo.table_id = dine_in_order.get("tableId")
            order_vo.table_name = dine_in_order.get("tableName")
            meal_type = dine_in_order.get("mealType")
            if meal_type:
                meal_type = dish_pb.DishOrder.MealType.Value(meal_type)
                order_vo.meal_type = meal_type
                order_vo.meal_code = order.get("mealCode", "")
        order_vo.store_name = merchant.stores[0].name
        order_vo.paid_time = int(order.get("payTime"))
        user_info = order.get("userInfo")
        if user_info and user_info.get("userPhone"):
            order_vo.phone = user_info.get("userPhone")

        order_vo.merchant_phone = merchant.stores[0].phone
        order_vo.merchant_id = merchant_id
        order_vo.giving_fee = order.get("givingFee", 0)
        order_vo.discount_amount = order.get("totalOrderPromotionFee", 0)
        order_vo.serial_number = str(order.get("serialNumber"))
        status = order.get("status")
        if status:
            order_vo.status = dish_pb.DishOrder.OrderStatus.Value(status)
        appointment_time = order.get("appointmentTime")
        if appointment_time:
            fmt = "%H:%M"
            appointment_time = datetime.fromisoformat(appointment_time).strftime(fmt)
            order_vo.appointment_time = appointment_time

        for transaction_info in order.get("transactionInfos"):
            order_vo.transaction_type = transaction_info.get("type")
            try:
                order_vo.pay_method = wallet_pb.Transaction.PayMethod.Value(transaction_info.get("payMethod"))
            except:
                pass

        # order_vo.bill_fee = order.get("billFee", 0)
        order_vo.total_fee = order.get("totalDishBillFee", 0)
        order_vo.bill_fee = order_vo.total_fee
        order_vo.paid_fee = order.get("totalPaidFee", 0)
        self.__deal_with_items(order, order_vo)
        return order_vo

    def __order_dish_base_info(self, order_dish, item, is_addon=False):
        order_dish.id = item.get("dishId")
        order_dish.name = item.get("name")
        order_dish.price = int(item.get("price"))
        order_dish.total_fee = int(item.get("billFee"))
        order_dish.image = item.get("imageUrl", "")
        order_dish.quantity = item.get("quantity", 1)
        order_dish.uuid = id_manager.generate_common_id()
        order_dish.batch_number = item.get("batchNumber", 1)
        create_time = item.get("createTime")
        if create_time:
            order_dish.create_time = int(item.get("createTime"))
        self.__deal_with_attrs(item, order_dish)
        self.__deal_with_spec_options(item, order_dish)
        self.__deal_with_package_boxs(item, order_dish)

    def __deal_with_items(self, order, order_vo):
        items = order.get("items")
        for item in items:
            order_dish = order_vo.dish_list.add()
            self.__order_dish_base_info(order_dish, item)
            self.__deal_with_sub_items(order_vo, order_dish, item.get("subItems"))

    def __deal_with_sub_items(self, order_vo, parent, sub_items):
        for sub_item in sub_items:
            dish_type = sub_item.get("dishType")
            if dish_type == "SINGLE":
                order_dish = order_vo.dish_list.add()
                self.__order_dish_base_info(order_dish, sub_item)
                self.__deal_with_sub_items(order_vo, order_dish, sub_item.get("subItems"))
                order_dish.is_child_dish = True
                order_dish.parent_uuid = parent.uuid
            if dish_type == "ADDON":
                _attr = parent.attrs.add()
                _attr.name = sub_item.get("name")

    def __deal_with_attrs(self, item, order_dish):
        attribute_options = item.get("attributeOptions")
        if not attribute_options:
            return
        for attr in attribute_options:
            name = attr.get("name")
            if not name:
                continue
            _attr = order_dish.attrs.add()
            _attr.name = attr.get("name")
            _attr.quantity = attr.get('quantity', 1)
            _attr.reprice = attr.get('reprice', 0)

    def __deal_with_spec_options(self, item, order_dish):
        spec_options = item.get("specOptions")
        if not spec_options:
            return
        for attr in spec_options:
            name = attr.get("name")
            if not name:
                continue
            _attr = order_dish.attrs.add()
            _attr.name = attr.get("name")
            _attr.quantity = attr.get('quantity', 1)
            _attr.type = dish_pb.Attr.AttrType.SPECIFICATION
            _attr.reprice = attr.get('price', 0)

    def __deal_with_package_boxs(self, item, order_dish):
        package_box = item.get("packagingBox")
        if not package_box:
            return
        name = package_box.get("name")
        if not name:
            return
        _attr = order_dish.attrs.add()
        _attr.name = package_box.get("name")
        _attr.quantity = package_box.get('quantity', 1)
        _attr.type = dish_pb.Attr.AttrType.TAKE_AWAY
        _attr.reprice = package_box.get('price', 0)

    def get_order(self, order_id, user_id, order=None):
        ordering_da = OrderingServiceDataAccessHelper()
        if not order:
            order = ordering_da.get_order(id=order_id)
        if not order:
            return None
        order = self.order_manager.merge_pos_order(order)
        transaction_da = TransactionDataAccessHelper()
        order_vo = page_dish_pb.Order()
        order_vo.id = order.id
        order_vo.table_id = order.table_id
        table = ordering_da.get_table(ordering_service_table_id=order.table_id, merchant_id=order.merchant_id)
        if table:
            order_vo.table_name = table.name
        order_vo.remark = order.remark
        merchant = MerchantDataAccessHelper().get_merchant(order.merchant_id)
        order_vo.store_name = merchant.stores[0].name
        order_vo.paid_time = 0
        order_vo.meal_code = order.meal_code
        order_vo.phone = order.phone
        order_vo.meal_type = order.meal_type
        order_vo.merchant_phone = merchant.stores[0].phone
        order_vo.shipping_fee = order.shipping_fee
        order_vo.keruyun_take_out_shipping_fee = order.keruyun_take_out_shipping_fee
        order_vo.keruyun_take_out_packaging_fee = order.keruyun_take_out_packaging_fee
        order_vo.shipping_status = dish_pb.DishOrder.NO_SHIPPING
        order_vo.shipping_address.CopyFrom(order.shipping_address)
        order_vo.merchant_id = order.merchant_id
        order_vo.enable_ifeedu = False
        order_vo.ifeedu_fee = order.ifeedu_fee
        order_vo.giving_fee = order.giving_fee
        order_vo.discount_amount = order.discount_amount
        order_vo.status = order.status
        order_vo.logo_url = merchant.basic_info.logo_url
        order_vo.package_box_type = merchant.stores[0].package_box_type
        order_vo.package_fee = order.package_fee
        order_vo.is_phone_member_pay = order.is_phone_member_pay
        order_vo.packaging_box_dish_id = self.registration_info.packaging_box_config.dish_id
        order_vo.bill_fee = order.bill_fee
        order_vo.people_count = order.people_count
        order_vo.create_time = order.create_time
        order_vo.serial_number = str(order.serial_number)
        order_vo.enable_discount_fee = order.enable_discount_fee
        if order.ordering_service_serial_number:
            order_vo.serial_number = order.ordering_service_serial_number

        appointment_time = order.appointment_time
        if appointment_time != 0:
            appointment_time = datetime.fromtimestamp(appointment_time).strftime("%H:%M")
            order_vo.appointment_time = appointment_time

        transaction = transaction_da.get_transaction_by_id(order.transaction_id)
        if transaction:
            order_vo.transaction_type = wallet_pb.Transaction.TransactionType.Name(transaction.type)
            red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
            if red_packet:
                order_vo.red_packet_id = red_packet.id

        self.__products_info(order, order_vo)
        order_vo.total_fee = order.total_fee + order.discount_amount + order.shipping_fee
        order_vo.paid_fee = order.paid_fee - order.giving_fee
        self.__transaction_info(order_vo, transaction)

        if order.status == dish_pb.DishOrder.POS_RETURN and order.refund_transaction_id:
            refund_transaction = transaction_da.get_transaction_by_id(order.refund_transaction_id)
            order_vo.refund_time = str(refund_transaction.paid_time)

        if order.is_weighing:
            ordering_da.add_or_update_order(order=order)
        return order_vo

    def get_order_by_handheld_pos_order_no(self, handheld_pos_order_no):
        order_da = OrderingServiceDataAccessHelper()
        order = order_da.get_order(handheld_pos_order_no=handheld_pos_order_no)
        return order

    def update_order_enable_discount_fee(self, dish, order, product, supply_condiment_fee):
        if dish.no_discount:
            return
        if product.is_ifeedu:
            return
        required_order_dishes = {item.name: item.type for item in self.registration_info.ordering_config.required_order_items}
        if dish.name in required_order_dishes:
            return
        category_id = dish.categories[0]
        category = OrderingServiceDataAccessHelper().get_category(id=category_id, merchant_id=order.merchant_id)
        if not category:
            return
        if category.no_discount:
            return
        fee = product.total_fee + supply_condiment_fee
        order.enable_discount_fee += int(fee)

    def _add_child_dish_to_dish_vo(self, dish, dish_vo):
        for dish_group in dish.child_dish_groups:
            dish_group_vo = dish_vo.child_dish_groups.add()
            dish_group_vo.id = dish_group.id
            dish_group_vo.group_name = dish_group.group_name
            dish_group_vo.order_min = dish_group.order_min
            dish_group_vo.order_max = dish_group.order_max
            dish_group_vo.sort = dish_group.sort
            for child_dish in dish_group.child_dishes:
                dish = self.dish_manager.get_dish_from_cache(child_dish.id)
                if not dish:
                    continue
                if dish.status not in [dish_pb.Dish.NORMAL]:
                    continue
                child_dish_vo = dish_group_vo.child_dishes.add()
                child_dish_vo.id = child_dish.id
                child_dish_vo.name = child_dish.name
                child_dish_vo.price = child_dish.price
                child_dish_vo.market_price = child_dish.market_price
                child_dish_vo.sort = child_dish.sort
                child_dish_vo.is_must = child_dish.is_must
            dish_group_vo.child_dishes.sort(key=lambda dish: dish.sort)
        dish_vo.child_dish_groups.sort(key=lambda group: group.sort)

    def generate_meal_code(self):
        tz = timezone(date_utils.TIMEZONE_SHANGHAI)
        now = datetime.now()
        today = now.astimezone(tz).strftime("%Y-%m-%d")
        key = "{}-{}".format(today, self.merchant.id)
        if int(time.time()) < 1728748800:  # 2024-10-13 00:00:00
            redis_client = RedisClient().get_connection(use_new=False)
        else:
            redis_client = RedisClient().get_connection()
        ret = redis_client.incr(key, 1)
        tomorrow = now + timedelta(1)
        tomorrow.replace(hour=0, minute=0, second=0)
        redis_client.expireat(key, int(tomorrow.timestamp()))
        meal_code_base_value = self.registration_info.meal_code_base_value
        if meal_code_base_value == 0:
            ret += 1000
        else:
            ret += meal_code_base_value - 1
        if self.registration_info.meal_code_max_value > 0 and ret >= self.registration_info.meal_code_max_value:
            redis_client.delete(key)
        return ret

    def generate_serial_number(self):
        tz = timezone(date_utils.TIMEZONE_SHANGHAI)
        now = datetime.now()
        today = now.astimezone(tz).strftime("%Y-%m-%d")
        key = "{}:{}:serial:number".format(today, self.merchant.id)
        if int(time.time()) < 1728748800:  # 2024-10-13 00:00:00
            redis_client = RedisClient().get_connection(use_new=False)
        else:
            redis_client = RedisClient().get_connection()
        ret = redis_client.incr(key, 1)
        logger.info(f"generate_serial_number: {ret} {redis_client.get(key)}")
        tomorrow = now + timedelta(1)
        tomorrow.replace(hour=0, minute=0, second=0)
        redis_client.expireat(key, int(tomorrow.timestamp()))
        serial_number_base_value = self.registration_info.serial_number_base_value
        if serial_number_base_value == 0:
            serial_number_base_value = 1
        ret = ret + serial_number_base_value - 1
        if self.registration_info.serial_number_max_value > 0 and ret >= self.registration_info.serial_number_max_value:
            redis_client.delete(key)
        return ret

    def _remove_order_discount_price(self, order):
        order.discount_amount = 0
        order.paid_fee = order.bill_fee
        products = order.products
        order.total_fee = order.bill_fee
        for product in products:
            product.discount_price = product.price
        for add_products in order.add_products:
            for product in add_products.products:
                product.discount_price = product.price

    def _cal_commission(self, order, transaction=None):
        commission_rate = 0
        if order.meal_type == dish_pb.DishOrder.TAKE_AWAY:
            commission_rate = self.registration_info.take_away_commission_rate
        elif order.meal_type == dish_pb.DishOrder.EAT_IN:
            commission_rate = self.registration_info.eat_in_commission_rate
        elif order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            commission_rate = self.registration_info.take_out_commission_rate
        elif order.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            commission_rate = self.registration_info.self_pick_up_commission_rate
        elif order.meal_type == dish_pb.DishOrder.KERUYUN_TAKE_OUT:
            commission_rate = self.registration_info.keruyun_take_out_commission_rate
        elif order.meal_type == dish_pb.DishOrder.KERUYUN_FAST_FOOD:
            commission_rate = self.registration_info.keruyun_fast_food_commission_rate
        elif order.meal_type == dish_pb.DishOrder.KERUYUN_FAST_FOOD_TAKE_AWAY:
            commission_rate = self.registration_info.keruyun_fast_food_take_away_commission_rate

        # 饭票佣金
        order.fanpiao_commission_fee = self.cal_fanpiao_commission_fee(transaction, order)
        order.coupon_package_commission_fee = self.cal_coupon_package_commission_fee(transaction)

        commission = round(float(commission_rate) / 100 * order.bill_fee)
        if commission > order.paid_fee:
            return order.paid_fee
        order.commission = commission
        return commission

    def cal_coupon_commission_fee(self, order, transaction):
        commission_rate = self.registration_info.coupon_commission_rate
        if commission_rate == 0:
            return 0
        if len(order.coupon_ids) == 0:
            return 0
        commission_fee = int(float(commission_rate) / 100 * transaction.bill_fee + 0.5)
        order.paid_in_fee -= commission_fee
        order.actual_total_receivable_fee -= commission_fee
        order.coupon_commission_fee = commission_fee
        return commission_fee

    def cal_wallet_pay_commission_fee(self, order, transaction):
        """红包支付的方式的佣金计算: 要在计算完paid_in_fee字段之后才能计算红包支付方式的佣金
        order.paid_in_fee * commission_rate / 100
        """
        commission_rate = self.registration_info.wallet_pay_commission_rate
        if commission_rate == 0:
            return 0
        if transaction.pay_method != wallet_pb.Transaction.WALLET:
            return 0
        coupon_id = transaction.use_coupon_id
        if coupon_id != "":
            return 0
        if len(order.coupon_ids) > 0:
            return 0
        commission_fee = int(float(commission_rate) / 100 * transaction.bill_fee + 0.5)
        order.paid_in_fee -= commission_fee
        order.actual_total_receivable_fee -= commission_fee
        order.wallet_commission_fee = commission_fee
        return commission_fee

    def cal_coupon_package_commission_fee(self, transaction):
        if transaction is None:
            return 0
        commission_rate = self.registration_info.coupon_package_pay_commission_rate
        if commission_rate == 0:
            return 0
        coupon_id = transaction.use_coupon_id
        if coupon_id == "":
            return 0
        manager = CouponManager()
        coupon_category = manager.get_coupon_category_by_coupon(coupon_id=coupon_id)
        if not coupon_category:
            return 0
        if coupon_category.issue_scene != coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            return 0
        commission_fee = round(float(commission_rate) / 100 * transaction.bill_fee)
        return commission_fee

    def cal_fanpiao_commission_fee(self, transaction, order=None, no_discount_fee=0):
        """用户饭票支付时,时来应收的佣金"""
        if transaction is None:
            return 0
        if transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            return 0
        commission_rate = self.registration_info.fanpiao_pay_commission_rate
        # 未设置饭票佣金的商户直接返回0
        if commission_rate == 0:
            return 0
        if order is None:
            commission_fee = round(
                float(commission_rate)
                / 100
                * (
                    (transaction.bill_fee - no_discount_fee)
                    if transaction.bill_fee > 0
                    else (transaction.bill_fee + no_discount_fee)
                )
            )
        else:
            commission_fee = round(float(commission_rate) / 100 * order.enable_discount_fee)
        return commission_fee

    def _get_distance(self, lng1, lat1, lng2, lat2):
        # 地球半径(km)
        R = 6373.0

        lng1, lat1, lng2, lat2 = map(radians, [lng1, lat1, lng2, lat2])

        dlng = lng2 - lng1
        dlat = lat2 - lat1

        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlng / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))

        distance = R * c
        return distance * 1000

    def _check_shipping_dish(self, order, dishes, store, shipping_address_id):
        if shipping_address_id is None:
            return
        if order.meal_type not in [dish_pb.DishOrder.TAKE_OUT, dish_pb.DishOrder.KERUYUN_TAKE_OUT]:
            return

        has_shipping_dish = False
        if order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            for dish in dishes:
                if dish.get('name') == '配送费':
                    has_shipping_dish = True
                    # order.shipping_fee = dish.get('price')
                    break
            if not has_shipping_dish:
                raise errors.NoShippingFeeSelected()

        user = UserDataAccessHelper().get_user(order.user_id)
        for s in user.member_profile.shipping_address:
            if s.id != shipping_address_id:
                continue
            location = store.poi.location
            if self.registration_info.max_take_out_distance and store.poi.location:
                distance = self._get_distance(s.longitude, s.latitude, location.longitude, location.latitude)
                # 如果设置了最远配送距离就需要检查是不是超过最远配送距离.
                if distance > self.registration_info.max_take_out_distance:
                    raise errors.TooFarAwayToSend()
            order.shipping_address.CopyFrom(s)
            break

    def _check_dish_sale_time(self, dish):
        utc_now = datetime.utcnow()
        tzchina = timezone(date_utils.TIMEZONE_SHANGHAI)
        utc = timezone(date_utils.TIMEZONE_UTC)
        now = utc_now.replace(tzinfo=utc).astimezone(tzchina)
        now_seconds = now.hour * 60 * 60 + now.minute * 60 + now.second
        if not dish.sale_times or len(dish.sale_times) == 0:
            return True
        for sale_time in dish.sale_times:
            if sale_time.start < now_seconds < sale_time.end:
                return True
        return False

    def _check_table(self, order, table):
        if table and table.only_for_pay:
            raise errors.TableOnlyForPay()
        if order.meal_type in [dish_pb.DishOrder.KERUYUN_TAKE_OUT]:
            return True
        if not table:
            raise errors.TableNotMatched()
        # if table.meal_type != order.meal_type:
        #     raise errors.TableNotMatched()

    def _check_opening_hours(self):
        now = datetime.now()
        tz = timezone(date_utils.TIMEZONE_SHANGHAI)
        now = now.astimezone(tz)
        weekday = now.weekday()
        now_seconds = now.hour * 60 * 60 + now.minute * 60 + now.second
        opening = True
        for opening_time_range in self.registration_info.opening_time_ranges:
            if weekday != opening_time_range.day_of_week:
                continue
            if opening_time_range.start_second_of_day < now_seconds < opening_time_range.end_second_of_day:
                opening = True
                break
            else:
                opening = False
        if not opening:
            raise errors.NotTheBusinessTimes()

    def _create_shilai_attr(self, order, product, order_attrs, dish):
        dish_attrs = dish.attrs
        dish_attrs_dict = {str(dish_attr.id): dish_attr for dish_attr in dish_attrs}
        total_reprice = 0
        self.dish_manager.set_dish_attrs()
        for order_attr in order_attrs:
            attr_id = order_attr.get("id")
            dish_attr = dish_attrs_dict.get(str(attr_id))
            if not dish_attr:
                logger.info("菜品属性不存在: {}".format(order_attr.get("name")))
                raise errors.ShowError("{} 中的 {} 信息有更新,请删除后再下单".format(product.name, order_attr.get("name")))

            _attr = self.dish_manager.dish_attrs.get(dish_attr.id)
            if _attr:
                if _attr.status in [dish_pb.Attr.SOLD_OUT, dish_pb.Attr.OUT_OF_STOCK]:
                    raise errors.DishAttrSoldOut(dish_attr.name)
            # TODO: 兼容性处理 10个版本后应该删此逻辑
            if dish_attr.status in [dish_pb.Attr.SOLD_OUT, dish_pb.Attr.OUT_OF_STOCK]:
                raise errors.DishAttrSoldOut(dish_attr.name)

            product_attr = product.attrs.add()
            product_attr.id = attr_id
            product_attr.name = dish_attr.name
            product_attr.group_name = dish_attr.group_name
            product_attr.type = dish_attr.type
            product_attr.reprice = dish_attr.reprice
            product.total_fee += round(product_attr.reprice * product.quantity)
            total_reprice += round(product_attr.reprice * product.quantity)
        if order is not None:
            order.total_fee += total_reprice
            order.bill_fee += total_reprice
            order.market_bill_fee += total_reprice
        return total_reprice

    def _create_shilai_supply_condiment(self, order, product, order_supply_condiments, dish):
        dish_supply_condiments = dish.supply_condiments
        dish_supply_condiments_dict = {supply_condiment.id: supply_condiment for supply_condiment in dish_supply_condiments}
        total_price = 0
        self.dish_manager.set_dish_supply_condiments()
        for supply_condiment in order_supply_condiments:
            try:
                supply_condiment_id = supply_condiment.get("id")
            except:
                continue
            dish_supply_condiment = dish_supply_condiments_dict.get(supply_condiment_id)
            if not dish_supply_condiment:
                logger.info(f"cannot find {supply_condiment_id}")
                continue
            if supply_condiment.get("quantity", 0) == 0:
                continue

            _dish_supply_condiment = self.dish_manager.dish_supply_condiments.get(supply_condiment.get("id"))
            if _dish_supply_condiment:
                if _dish_supply_condiment.status in [dish_pb.SupplyCondiment.SOLD_OUT, dish_pb.SupplyCondiment.OUT_OF_STOCK]:
                    raise errors.DishSupplyCondimentSoldOut(dish_supply_condiment.name)
            # TODO: 兼容性处理, 10个版本后应该删此逻辑
            if dish_supply_condiment.status in [dish_pb.SupplyCondiment.SOLD_OUT, dish_pb.SupplyCondiment.OUT_OF_STOCK]:
                raise errors.DishSupplyCondimentSoldOut(dish_supply_condiment.name)

            product_supply_condiment = product.supply_condiments.add()
            product_supply_condiment.id = supply_condiment_id
            product_supply_condiment.name = dish_supply_condiment.name
            product_supply_condiment.market_price = dish_supply_condiment.market_price
            product_supply_condiment.quantity = int(product.quantity) * supply_condiment.get("quantity", 0)
            total_price += product_supply_condiment.market_price * product_supply_condiment.quantity
        if order is not None:
            order.total_fee += total_price
            order.bill_fee += total_price
            order.market_bill_fee += total_price
        return total_price

    def order_confirm(self, order=None):
        if order.status != dish_pb.DishOrder.TO_BE_CONFIRMED:
            return
        order.status = dish_pb.DishOrder.APPROVED

    def order_refuse(self, order=None):
        if order.status != dish_pb.DishOrder.TO_BE_CONFIRMED:
            return
        order.status = dish_pb.DishOrder.REJECRED

    def order_revoke(self, order=None):
        if order.status != dish_pb.DishOrder.APPROVED:
            return
        order.status = dish_pb.DishOrder.REVOKED

    def pos_order_create(self, order_id):
        order = self.create_shilai_order_obj()
        order.ordering_service_order_id = order_id
        self.order_manager.merge_pos_order(order)
        logger.info("收银机创建订单: {} {}".format(order.id, order.table_id))

    def merge_pos_order(self, order):
        if hasattr(self.order_manager, "merge_pos_order"):
            self.order_manager.merge_pos_order(order)

    def pos_return_order(self, transaction_id=None, transaction=None, order_id=None, order=None):
        """扫码点餐退款
        1. 客如云退货回调
        2. 时来商家后台退款
        """
        transaction_da = TransactionDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        if not transaction:
            transaction = transaction_da.get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if not order:
            order = ordering_da.get_order(transaction_id=transaction.id)
        if not order:
            raise errors.OrderNotFound()
        payment_manager = PaymentManager(pay_method=transaction.pay_method, merchant_id=transaction.payee_id)
        refund_transaction = payment_manager.ordering_refund(transaction, order)
        if transaction.pay_method in PaymentManager.get_refund_callback_pay_methods() and refund_transaction.paid_fee > 0:
            order.status = dish_pb.DishOrder.REFUNDING
            ordering_da.add_or_update_order(order)
            return refund_transaction
        self.__pos_return_deal_with_red_packet(order, transaction)
        self.__pos_return_deal_with_coupon(order, transaction)
        self.__pos_return_deal_with_new_coupon(order, transaction)
        self.__do_refund(order, transaction, refund_transaction)
        FanpiaoHelper().set_user_fanpiao_purchase_risk_control(user_id=order.user_id, merchant_id=order.merchant_id)

        refund_pay_method = wallet_pb.Transaction.PayMethod.Name(transaction.pay_method)
        msg = """
        给用户退款: user_id: {}, transaction_id: {}, order_id: {}
        退款金额: {}, 退款方式: {}
        退款成功
        """.format(
            order.user_id, transaction.id, order.id, transaction.paid_fee, refund_pay_method
        )
        logger.info(msg)
        order.is_total_refund = True
        order.is_partial_refund = False
        order.actual_total_receivable_fee = 0
        order.paid_in_fee = 0
        order.paid_fee = 0
        order.red_packet_fee = 0
        order.fanpiao_commission_fee = 0
        order.platform_discount_fee = 0
        try:
            self.send_order_to_data_center(order)
        except Exception as ex:
            logger.info("退款时传送订单到数据中心: {} {}".format(order.id, ex))
        return refund_transaction

    def handle_partial_ordering_refund(self):
        pass

    def ordering_refund(self, transaction, refund_transaction_id=None):
        """部分支付方式的退款通过回调的方式发送退款的状态.对于这部分支付方式,会调用此函数来处理扫码点餐的退款逻辑"""
        OrderingRefund = namedtuple("OrderingRefund", ["flag", "order", "refund_transaction"])
        ordering_da = OrderingServiceDataAccessHelper()
        order = ordering_da.get_order(transaction_id=transaction.id)
        if not order:
            logger.info("无法查询到订单: {}".format(transaction.id))
            return OrderingRefund(flag=False)
        # 把红包的钱从用户的时来钱包里扣除
        # 如果用户的钱是0,那么就会扣成负数
        self.__pos_return_deal_with_red_packet(order, transaction)
        # 优惠券
        self.__pos_return_deal_with_coupon(order, transaction)
        order.status = dish_pb.DishOrder.POS_RETURN
        order.refund_transaction_id = refund_transaction_id
        ordering_da.add_or_update_order(order)
        transaction_manager = TransactionManager()
        refund_transaction = transaction_manager.handle_ordering_refund(
            user_id=transaction.payer_id,
            merchant_id=transaction.payee_id,
            pay_method=transaction.pay_method,
            paid_fee=transaction.paid_fee,
            bill_fee=transaction.bill_fee,
            refunded_transaction_id=transaction.id,
            use_coupon_id=transaction.use_coupon_id,
            id=refund_transaction_id,
        )
        try:
            self.send_order_to_data_center(order)
        except Exception as ex:
            logger.info("退款时传送订单到数据中心: {} {}".format(order.id, ex))
        return OrderingRefund(flag=True, order=order, refund_transaction=refund_transaction)

    def ordering_refund_rollback(self, transaction):
        ordering_da = OrderingServiceDataAccessHelper()
        order = ordering_da.get_order(transaction_id=transaction.id)
        order.status = dish_pb.DishOrder.PAID
        ordering_da.add_or_update_order(order)

    def __do_refund(self, order, transaction, refund_transaction):
        ordering_da = OrderingServiceDataAccessHelper()
        wechat_payment_manager = PaymentManager(pay_method=wallet_pb.Transaction.WECHAT_PAY, merchant_id=transaction.payee_id)
        wechat_payment_manager.update_scan_code_order_merchant_transfer_fee(order, refund_transaction)
        order.refund_transaction_id = refund_transaction.id
        order.status = dish_pb.DishOrder.POS_RETURN
        ordering_da.add_or_update_order(no_check=True, order=order)
        logger.info(f"__do_refund: {order.id} 退款金额 {refund_transaction.paid_fee}")

    def __pos_return_deal_with_new_coupon(self, order, transaction):
        coupon_manager = NewCouponManager(user_id=order.user_id, merchant_id=order.merchant_id)
        for coupon_id in order.coupon_ids:
            coupon = coupon_manager.get_coupon(id=coupon_id)
            if not coupon:
                continue
            if coupon.coupon_type not in [
                coupon_pb.Coupon.OPENING_GROUP_PURCHASE,
                coupon_pb.Coupon.FANPIAO_PURCHASE_BONUS,
            ]:  # 当前只有团长立减券可以退
                continue
            coupon_manager.update_coupon(coupon, status=coupon_pb.Coupon.ACCEPTED)
            coupon_manager.add_or_update_coupon(coupon)

    def __pos_return_deal_with_coupon(self, order, transaction):
        """如果有使用优惠券,那么就要把优惠券返回给用户"""
        if transaction.use_coupon_id:
            coupon = CouponDataAccessHelper().get_coupon_by_id(transaction.use_coupon_id)
            if not coupon:
                return
            coupon.state = coupons_pb.Coupon.ACCEPTED
            CouponDataAccessHelper().update_or_create_coupon(coupon)
        if order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            if order.logistics_platform == registration_pb.LogisticsPlatform.DADA:
                DadaManager().cancel_order(order.id, "商家取消")

    def __pos_return_deal_with_red_packet(self, order, transaction):
        # 查询出已领取的这个订单相关的红包
        red_packet_da = RedPacketDataAccessHelper()
        red_packet = red_packet_da.get_red_packet(new_transaction_id=order.transaction_id)
        if not red_packet:
            return
        red_packet.status = red_packet_pb.RedPacket.CANCELLED
        value_assignments = red_packet.value_assignments
        drawn_users = red_packet.drawn_users
        for user_id in drawn_users:
            # 如果有红包,且已经领取了,那么需要减掉红包的钱
            # TODO: 如果用户领取红包后满20元.用户提现后,用户钱包的钱小于红包金额.这个时候,用户时来钱包有可能出现负数.
            WalletManager().decrease_balance(user_id, 0, value_assignments.get(user_id), order.merchant_id)
            transaction_ma = TransactionManager()
            transaction = transaction_ma.handle_user_transfer_prepay(
                payer_id=transaction.payer_id,
                payee_id=transaction.payee_id,
                bill_fee=value_assignments.get(user_id),
                transaction_type=wallet_pb.Transaction.RED_PACKET_DEDUCTION,
            )
            transaction.paid_time = date_utils.timestamp_second()
            transaction.state = wallet_pb.Transaction.SUCCESS
            transaction_ma.update_transaction(transaction)

    def _calculate_paid_in_fee(self, business_obj=None, order=None, transaction=None):
        if business_obj is not None:
            order = business_obj.order
            if not hasattr(business_obj, "transaction"):
                # 对于后付款模式就不计算了,因为还没有transaction
                return order.paid_fee
            transaction = business_obj.transaction
        else:
            if not transaction:
                return order.paid_fee

        red_packet_value = 0
        if business_obj and hasattr(business_obj, "red_packet") and business_obj.red_packet:
            red_packet_value = (
                business_obj.red_packet.total_value
                if business_obj.red_packet.status != red_packet_pb.RedPacket.CANCELLED
                else 0
            )

        fee = transaction.paid_fee

        has_coupon = transaction.use_coupon_id != ''
        has_ifeedu_fee = order.ifeedu_fee != 0
        has_platform_discount_fee = order.platform_discount_fee != 0

        no_coupon = not has_coupon
        no_ifeedu_fee = not has_ifeedu_fee
        no_platform_discount_fee = not has_platform_discount_fee

        # 1. 无券包, 无投喂, 无补贴
        if no_coupon and no_ifeedu_fee and no_platform_discount_fee:
            logger.info("{} 情况一: 无券包,无投喂,无补贴".format(order.id))
            # self.temp_msg = "情况一: 无券包,无投喂,无补贴"
            fee = transaction.paid_fee - red_packet_value

        # 2. 有券包, 无投喂, 无补贴
        if has_coupon and no_ifeedu_fee and no_platform_discount_fee:
            logger.info("{} 情况二: 有券包,无投喂,无补贴".format(order.id))
            # self.temp_msg = "情况二: 有券包,无投喂,无补贴"
            fee = transaction.paid_fee

        # 3. 有券包, 无投喂, 有补贴
        if has_coupon and no_ifeedu_fee and has_platform_discount_fee:
            logger.info("{} 情况三: 有券包,无投喂,有补贴".format(order.id))
            # self.temp_msg = "情况三: 有券包,无投喂,有补贴"
            fee = transaction.paid_fee + order.platform_discount_fee

        # 4. 有券包, 有投喂, 无补贴
        if has_coupon and has_ifeedu_fee and no_platform_discount_fee:
            logger.info("{} 情况四: 有券包,有投喂,无补贴".format(order.id))
            # self.temp_msg = "情况四: 有券包,有投喂,无补贴"
            fee = transaction.paid_fee + order.ifeedu_fee

        # 5. 有券包, 有投喂, 有补贴
        if has_coupon and has_ifeedu_fee and has_platform_discount_fee:
            logger.info("{} 情况五: 有券包,有投喂,有补贴".format(order.id))
            # self.temp_msg = "情况五: 有券包,有投喂,有补贴"
            fee = transaction.paid_fee + order.ifeedu_fee + order.platform_discount_fee

        # 6. 无券包, 有投喂, 无补贴
        if no_coupon and has_ifeedu_fee and no_platform_discount_fee:
            logger.info("{} 情况六: 无券包,有投喂,无补贴".format(order.id))
            # self.temp_msg = "情况六: 无券包,有投喂,无补贴"
            fee = transaction.paid_fee + order.ifeedu_fee - red_packet_value

        # 7. 无券包,无投喂,有补贴(使用饭票)
        if no_coupon and no_ifeedu_fee and has_platform_discount_fee and len(order.coupon_ids) == 0:
            logger.info("{} 情况七: 无券包,无投喂,有补贴 {}".format(order.id, red_packet_value))
            fee = transaction.paid_fee + order.platform_discount_fee
            if fee > transaction.paid_fee and transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
                fee -= red_packet_value

        # 8. 新版券
        if len(order.coupon_ids) > 0:
            logger.info(f"{order.id} 情况八: 新版用券")
            fee = transaction.paid_fee + order.platform_discount_fee

        if self.store.enable_subsidies_overflow_fee:
            logger.info(f"商户打开了补贴溢出金额的开关,订单 {order.id} 补贴溢出金额为: {order.subsidies_overflow_fee}")
            fee -= order.subsidies_overflow_fee
        fee = (
            fee
            - order.commission
            - order.fanpiao_commission_fee
            - order.coupon_package_commission_fee
            - order.wallet_commission_fee
            - order.coupon_commission_fee
        )
        if transaction.pay_method in [wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY]:
            order.paid_in_fee = 0
        else:
            order.paid_in_fee = fee
        order.actual_total_receivable_fee = fee
        logger.info("订单 {}, 商户 {} 实际收入金额为: {}".format(order.id, order.merchant_id, fee))
        return fee

    def _set_order_meal_code(self, order):
        if order.meal_code != "":
            return
        if order.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            order.meal_code = str(self.generate_meal_code())
        elif order.meal_type == dish_pb.DishOrder.TAKE_AWAY:
            order.meal_code = str(self.generate_meal_code())
        elif order.meal_type == dish_pb.DishOrder.KERUYUN_FAST_FOOD_TAKE_AWAY:
            order.meal_code = str(self.generate_meal_code())
        elif order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            pass
        elif (
            order.meal_type == dish_pb.DishOrder.EAT_IN
            and self.registration_info.is_pre_order
            and self.registration_info.is_pre_order_show_meal_code
        ):
            order.meal_code = str(self.generate_meal_code())

    def is_pay_later(self):
        return self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER

    def check_already_has_order(self, user_id, table_id):
        """后付款模式 有可能 多个人同时创建同一个桌台的订单,这个时候 直接返回创建 失败重新 扫码可加菜"""
        if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            return
        elif self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            key = "create-order-{}".format(table_id)
            with distribute_lock.redislock(key=key, ttl=5000, retry_count=5, retry_delay=200) as lock:
                if not lock:
                    raise errors.TableHasOrderCreating()
                before_time = int(time.time()) - 60 * 60 * 6
                recently_order = OrderingServiceDataAccessHelper().get_recently_order(
                    merchant_id=self.merchant.id, table_id=table_id, before_time=before_time
                )
                if not recently_order:
                    return
                pos_type = self.registration_info.pos_type
                if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
                    keruyun_order_detail = self.get_order_detail_info(recently_order)
                    if not keruyun_order_detail:
                        return
                    trade_pay_status = keruyun_order_detail.base_info.trade_pay_status
                    if trade_pay_status == export_detail_pb.KeruyunOrderDetail.BaseInfo.PAID:
                        logger.info("已存在的订单在客如云端已支付: {}".format(recently_order.id))
                    else:
                        self.add_user_to_table_recently_order(user_id, table_id)
                        raise errors.TableHasOrderCreating()

    def get_user_recently_paid_order(self, user_id, before_time=None):
        """返回用户before_time秒之内支付过的订单数
        Args:
            before_time: 向前的秒数
        Return:
            order_pb.Order
        """
        ordering_da = OrderingServiceDataAccessHelper()
        if before_time is None:
            before_time = int(time.time()) - 10 * 60
        orders = ordering_da.count_recently_paid_order(merchant_id=self.merchant.id, user_id=user_id, before_time=before_time)
        if len(orders) > 0:
            return orders[0]
        return None

    def set_order_cannot_pay(self, order, dish):
        dish_ids = [
            "364549701680324608",
            "346052058531738624",
            "302200447656768512",
            "285858849054851072",
            "222663789420502016",
            "284113196130692096",
            "391771969501154304",
            "306809265625253888",
            "306806081523550208",
            "306808386015492096",
            "306814588686566400",
            "222704857035889664",
            "393034382556751872",
        ]
        if str(dish.id) in dish_ids:
            order.is_cannot_pay_order = True
        if dish.cannot_pay:
            order.is_cannot_pay_order = True

    def create_shilai_product(self, products, order, dish, order_dish, shopping_card=None):
        discount_price = self.dish_manager.get_discount_price(dish)
        product = products.add()
        product.id = dish.id
        product.name = dish.name
        product.unit = dish.unit
        product.quantity = order_dish.get("quantity")
        product.required_item_count = order_dish.get("requiredItemCount", 0)
        product.copies = 0
        product.market_price = dish.market_price
        product.price = round(dish.price)
        product.box_qty = dish.box_qty
        product.create_time = int(time.time())
        product.batch_number = 1
        product.is_required = order_dish.get("isRequired", False)
        product.added_user_id = order_dish.get("addedUserId", "")
        product.added_nickname = order_dish.get("addedNickname", "")
        product.added_headimgurl = order_dish.get("addedHeadimgurl", "")
        product.vip_price = order_dish.get("vipPrice", 0)
        if shopping_card:
            product.total = order_dish.get("total", 0)
        if order is not None:
            if len(order.add_products) > 0:
                product.batch_number = len(order.add_products) + 1
        if len(dish.images) > 0:
            product.image_url = dish.images[0]
        else:
            product.image_url = dish.shilai_dish_image
        if product.market_price == 0:
            product.market_price = product.price
        product.discount_price = int(discount_price)
        # recommend_dish_ids = [dish.id for dish in self.registration_info.recommend_dish.dishes]
        # if dish.id in recommend_dish_ids:
        #     product.discount_price = int(dish.price)
        product.uuid = id_manager.generate_common_id()
        product.total_fee = round(product.price * product.quantity)
        product.type = dish.type
        product.category_id = dish.categories[0]

        attrs = order_dish.get("attrs", [])
        self._create_shilai_attr(order, product, attrs, dish)

        supply_condiments = order_dish.get("supplyCondiments", [])
        total_price = self._create_shilai_supply_condiment(order, product, supply_condiments, dish)

        if order is not None:
            order.total_fee += round(product.discount_price * product.quantity)
            order.paid_fee = order.total_fee
            order.bill_fee += round(product.price * product.quantity)
            order.market_bill_fee += round(product.market_price * product.quantity)
            order.discount_amount += round((product.price - product.discount_price) * product.quantity)
            if dish.type != dish_pb.Dish.COMBO_MEAL:
                self.update_order_enable_discount_fee(dish, order, product, total_price)

        if dish.type not in [dish_pb.Dish.COMBO_MEAL]:
            return
        total_price += self._create_shilai_combo_meal_child_dish(order, order_dish, product, shopping_card=shopping_card)
        if order is not None:
            if dish.type == dish_pb.Dish.COMBO_MEAL:
                self.update_order_enable_discount_fee(dish, order, product, total_price)

    def _create_shilai_combo_meal_child_dish(self, order, order_dish, parent_product, shopping_card=None):
        if order is None and shopping_card is None:
            return
        if order is not None:
            products = order.products
        elif shopping_card is not None:
            products = shopping_card.products
        total_price = 0
        for child_dish_group in order_dish.get("childDishGroups", []):
            for child_dish in child_dish_group.get("childDishes", []):
                if child_dish.get("quantity", 0) <= 0:
                    continue
                dish = self.dish_manager.get_dish_from_cache(child_dish.get("id"))
                if dish.status in [dish_pb.Dish.GUQING, dish_pb.Dish.OFFLINE]:
                    # 菜品己售罄
                    raise errors.DishSoldOut(dish.name)
                product = products.add()
                quantity = child_dish.get("quantity")
                price = child_dish.get("price")
                product.id = dish.id
                product.name = dish.name
                product.unit = dish.unit
                product.quantity = quantity
                product.price = 0
                product.parent_uuid = parent_product.uuid
                product.uuid = id_manager.generate_common_id()
                product.total_fee = 0
                product.discount_price = 0
                product.create_time = int(time.time())
                product.child_group_id = child_dish_group.get("id", "")
                product.child_group_name = child_dish_group.get("groupName", "")
                product.child_dish_price = price
                product.type = dish.type
                if len(dish.categories) > 0:
                    product.category_id = dish.categories[0]
                self._create_shilai_attr(order, product, child_dish.get("attrs", []), dish)
                supply_condiments = child_dish.get("supplyCondiments", [])
                if order is not None:
                    total_price += self._create_shilai_supply_condiment(order, product, supply_condiments, dish)
                    order.total_fee += round(price * quantity)
                    order.paid_fee = order.total_fee
                    order.bill_fee += round(price * quantity)
                    order.market_bill_fee += round(price * quantity)
                parent_product.price += int(price)
                parent_product.total_fee += int(price * quantity)
        return total_price

    def set_repeat_create_order_flag(self):
        ex = 3
        if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            ex = 10
        key = "repeat_create_order_flag_{}_{}".format(self.user.id, self.merchant.id)
        if not self.redis_client.set(key, 1, nx=True, ex=ex):
            raise errors.Error(err=(100037, f"请勿重复下单或等待{ex}秒后重新下单"))

    def clear_repeat_create_order_flag(self):
        # if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
        key = "repeat_create_order_flag_{}_{}".format(self.user.id, self.merchant.id)
        self.redis_client.delete(key)

    def _set_appointment_time(self, order, appointment_time):
        """前端传过来的appointment_time格式为 小时:分钟,如 12:01
        1. 如果appointment_time为空字符串,那么就是当前时间
        2. 如果appointment_time的时间在今天晚些时候,那么就直接记录
        3. 如果appointment_time的时间为今天早些时间,那么日期就推到明天
        """
        if appointment_time is None:
            return
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(ordering_service_table_id=order.table_id)
        if not table or (order.meal_type == dish_pb.DishOrder.EAT_IN and table.meal_type != dish_pb.DishOrder.SELF_PICK_UP):
            return
        if order.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            if appointment_time == "":
                order.appointment_time = int(time.time())
                return
            now = datetime.now()
            appointment_date = now
            atime_hour = int(appointment_time.split(":")[0])
            atime_minute = int(appointment_time.split(":")[1])
            now_hour = now.hour
            now_minute = now.minute
            if now_hour > atime_hour or (now_hour == atime_hour and now_minute > atime_minute):
                appointment_date = now + timedelta(1)
            appointment_date = appointment_date.replace(hour=atime_hour, minute=atime_minute)
            order.appointment_time = int(appointment_date.timestamp())

    def _set_products(self, order, dishes, shopping_card=None):
        """创建订单时用于设置菜品信息"""
        for order_dish in dishes:
            dish = self.dish_manager.get_dish_from_cache(order_dish.get("id"))
            if dish.status in [dish_pb.Dish.GUQING, dish_pb.Dish.OFFLINE]:
                # 菜品己售罄
                raise errors.DishSoldOut(dish.name)
            if not self._check_dish_sale_time(dish):
                raise errors.DishNotInSaleTime(dish.name)
            self.set_order_cannot_pay(order, dish)
            try:
                self.create_shilai_product(order.products, order, dish, order_dish, shopping_card=shopping_card)
            except Exception as e:
                raise errors.Error(err=(220001, "菜品有变化，请清空购物车，重新下单"))

    def update_order(self, order_id, **kargs):
        order_da = OrderingServiceDataAccessHelper()
        order = order_da.get_order(id=order_id)
        if not order:
            return

        if order.status == dish_pb.DishOrder.OrderStatus.PAID:
            logger.error(f"该订单已支付成功，无法直接修改状态，orderId={order.id}")
            return

        # status = kargs.get("status")
        # if status is not None:
        #     order.status = dish_pb.DishOrder.OrderStatus.Value(status)
        #     order_da.add_or_update_order(order)

        release_lock = kargs.get("release_lock")
        if release_lock is not None and release_lock:
            self.release_order_lock(order)

    def increase_dish_remain_quantity(self, order):
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
        ]:
            return
        dishes_info = self.generate_dishes_info_from_order(order)
        if len(dishes_info) == 0:
            return
        self.dish_manager.increase_dish_remain_quantity(dishes_info)

    def decrease_dish_remain_quantity(self, order, raise_error=False, check_pay_later=True):
        if check_pay_later and self.is_pay_later():
            return
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
        ]:
            return
        dishes_info = self.generate_dishes_info_from_order(order)
        if len(dishes_info) == 0:
            return
        self.dish_manager.decrease_dish_remain_quantity(dishes_info, raise_error=raise_error)

    def check_dish_remain_quantity(self, order):
        if self.is_pay_later():
            return
        if self.registration_info.pos_type not in [
            registration_pb.OrderingServiceRegistrationInfo.SHILAI,
            registration_pb.OrderingServiceRegistrationInfo.FEIE,
        ]:
            return
        dishes_info = self.generate_dishes_info_from_order(order)
        if len(dishes_info) == 0:
            return
        self.dish_manager.check_dish_remain_quantity(dishes_info)

    def generate_dishes_info_from_order(self, order):
        dishes_info = {}
        for product in order.products:
            dish = self.dish_manager.get_dish_from_cache(product.id)
            if not dish:
                continue
            # 如果菜品没有开启余量估清的话
            if not dish.enable_quantity_setting:
                continue
            dish_info = dishes_info.get(product.id, {})
            dish_info.update({"name": product.name, "quantity": product.quantity + dish_info.get("quantity", 0)})
            dishes_info.update({product.id: dish_info})
            if self.registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
                for sc in product.supply_condiments:
                    dish_info = dishes_info.get(sc.id, {})
                    dish_info.update({"name": sc.name, "quantity": sc.quantity + dish_info.get("quantity", 0)})
                    dishes_info.update({sc.id: dish_info})
        return dishes_info

    def create_no_dish_order(self, transaction):
        """创建一个没有菜品的订单,用于直接支付时使用"""
        order = self.create_shilai_order_obj()
        order.bill_fee = transaction.bill_fee
        order.paid_fee = transaction.paid_fee
        order.paid_time = int(time.time())
        order.user_id = transaction.payer_id
        order.transaction_id = transaction.id
        order.merchant_id = transaction.payee_id
        try:
            order.store_id = self.store.id
        except Exception as ex:
            logger.info(f"create no dish order: {ex}")
            order.store_id = transaction.payee_store_id
        # order.serial_number = self.generate_serial_number()
        return order

    def sync_order_to_pos(self, transaction, order):
        ordering_service_da = OrderingServiceDataAccessHelper()
        self._calculate_paid_in_fee(order=order, transaction=transaction)
        try:
            ret = self.order_manager.sync_direct_pay_order(order, transaction)
            if ret:
                order.ordering_service_serial_number = ret.get("data").get("serialNumber")
                order.serial_number = int(ret.get("data").get("serialNumber"))
                order.ordering_service_order_id = ret.get("data").get("id")
            else:
                order.serial_number = self.generate_serial_number()
        except Exception as ex:
            order.serial_number = self.generate_serial_number()
            logger.exception("创建直接付订单报错,不影响最终结果: {}".format(ex))
        ordering_service_da.add_or_update_order(order=order)

    def create_order(
        self,
        table_id,
        dishes,
        people_count,
        remark=None,
        meal_type=None,
        force_create=False,
        phone=None,
        appointment_time=None,
        shipping_address_id=None,
        package_fee=0,
        # shopping_card=None,
    ):
        '''创建时来订单'''
        self.dish_manager.load_dish()
        if self.user:
            self.check_already_has_order(self.user.id, table_id)
        ordering_service_da = OrderingServiceDataAccessHelper()
        table = ordering_service_da.get_table(ordering_service_table_id=str(table_id), merchant_id=self.merchant.id)
        if not table:
            table = ordering_service_da.get_table(id=str(table_id), merchant_id=self.merchant.id)
        order = self.create_shilai_order_obj()
        order.meal_type = meal_type
        self._check_table(order, table)
        if meal_type in [dish_pb.DishOrder.TAKE_OUT, dish_pb.DishOrder.SELF_PICK_UP]:
            # 自提和外卖模式需要检查是否是在营业时间
            self._check_opening_hours()
        if self.user:
            self.dish_manager.get_discount_plan(self.user.id, enbale_sale_time_discount=True)

        order.merchant_id = self.merchant.id
        order.store_id = self.store.id
        order.create_time = date_utils.timestamp_second()
        order.table_id = str(table.ordering_service_table_id)
        if remark:
            order.remark = remark
            if self.user:
                user_da = UserDataAccessHelper()
                config = user_da.get_user_config(self.user.id)
                if remark not in config.common_remarks:
                    config.common_remarks.append(remark)
                    if len(config.common_remarks) > 5:
                        config.common_remarks.pop(0)
                    user_da.add_or_update_user_config(config)
        order.people_count = int(people_count)
        order.package_fee = package_fee
        if phone is not None:
            order.phone = phone
        self._set_appointment_time(order, appointment_time)
        self._check_shipping_dish(order, dishes, self.store, shipping_address_id)
        self._set_products(order, dishes)
        if self.user:
            recently_paid_order = self.get_user_recently_paid_order(user_id=self.user.id)
            if recently_paid_order and force_create is False:
                if recently_paid_order.bill_fee == order.bill_fee:
                    raise errors.Error(err=error_codes.HAS_RECENTLY_PAID_ORDER)

        if self.is_pay_later():
            self.decrease_dish_remain_quantity(order, raise_error=True, check_pay_later=False)

        # if shopping_card is not None:
        #     for user_id in shopping_card.users:
        #         order.user_ids.append(user_id)
        ordering_service_da.add_or_update_order(order=order)
        return order

    def check_can_add_dish(self, order):
        if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            key = CacheServerKeys.get_order_paying_cache_key(order.id)
            if self.redis_client.ttl(key) > 0:
                raise errors.ShowError(message="订单支付中，请取消支付再下单")

    def add_dish_to_shilai_order(self, dishes, order, user_id):
        if order.status not in [dish_pb.DishOrder.TO_BE_CONFIRMED, dish_pb.DishOrder.APPROVED]:
            raise errors.ShowError(message="订单状态错误,请联系服务员")
        if order.status == dish_pb.DishOrder.TO_BE_CONFIRMED:
            raise errors.ShowError(message="订单未确认,请联系服务员")
        order = self.order_manager.merge_pos_order(order)
        self.dish_manager.get_discount_plan(user_id, enbale_sale_time_discount=True)
        products = None
        # 从收银机服务端获取门店设置
        store_setting = self.get_store_setting()
        if store_setting:
            # 如果设置不自动接单
            if not store_setting.get("orderSetting", {}).get("enableDinnerAutoConfirm"):
                products = order.to_be_confirmed_products
        if products is None:
            add_products = order.add_products.add()
            products = add_products.products

        for order_dish in dishes:
            dish = self.dish_manager.get_dish_from_cache(order_dish.get("id"))
            pos_type = self.registration_info.pos_type
            if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
                if dish.type == dish_pb.Dish.COMBO_MEAL:
                    raise errors.AddDishDonnotSupportComboMeal()
            if dish.status in [dish_pb.Dish.GUQING, dish_pb.Dish.OFFLINE]:
                # 菜品己售罄
                raise errors.DishSoldOut(dish.name)
            if not self._check_dish_sale_time(dish):
                raise errors.DishNotInSaleTime(dish.name)
            self.set_order_cannot_pay(order, dish)

            self.create_shilai_product(products, order, dish, order_dish)
            if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
                for product in products:
                    if len(product.supply_condiments) > 0:
                        raise errors.CannotAddDishWithSupplyCondiments()

    def change_order_table(self, order, table_id=None):
        """收到回调时修改时来客如云的订单桌台信息"""
        if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            return
        if order.table_id == table_id:
            return
        order_detail = self.get_order_detail(order.ordering_service_trade_id)
        if not order_detail:
            return
        table_info = order_detail[0].get("tableInfos")[0]
        table_id = table_info.get("tableId")
        order.table_id = table_id
        OrderingServiceDataAccessHelper().add_or_update_order(order=order)

    def create_ordering_order(self, order):
        """创建第三方订单"""
        ret = self.order_manager.create_ordering_order(order)
        self.send_order_to_data_center(order)
        return ret

    def add_dish_ordering_order(self, dish_list, order, user_id):
        """调用第三方加菜接口"""
        ret = self.order_manager.add_dish_ordering_order(dish_list, order, user_id)
        self.send_order_to_data_center(order)
        return ret

    def create_pos_order(self, table_id, dishes, people_count):
        return self.order_manager.create_pos_order(table_id, dishes, people_count)

    def create_zero_order(self):
        order = self.create_shilai_order_obj()
        self.order_manager.create_zero_order(order)
        return order

    def get_store_setting(self):
        shop_manager = ShopManager(merchant=self.merchant, store=self.store)
        store_setting = shop_manager.get_store_setting()
        return store_setting

    def order_status_callback(self, **kargs):
        """订单状态更新"""
        return self.order_manager.order_status(**kargs)

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        """扫码点餐退单"""
        return self.order_manager.initiate_ordering_refund(order, transaction_id, **kargs)

    def pay_order(self, **kargs):
        """支付完成后通知第三方收银机平台,订单已支付"""
        partial_refund = kargs.get("partial_refund", False)
        if self.business_obj and not partial_refund:
            self.business_obj.order.serial_number = self.generate_serial_number()
            self._set_order_meal_code(self.business_obj.order)
        ret = self.order_manager.pay_order(**kargs)
        return ret

    def patch_kitchen_print(self, order, table):
        self.order_manager.patch_kitchen_print(order=order, table=table)

    def patch_checkout_print(self, order, table):
        self.order_manager.patch_checkout_print(order, table)

    def get_order_detail(self, order_id):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.order_manager, fname):
            return self.order_manager.get_order_detail(order_id)
        return None

    def get_order_detail_info(self, order):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.order_manager, fname):
            return self.order_manager.get_order_detail_info(order)
        return None

    def _merge_order_detail_to_shilai_order(self, order, user_id=None):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.order_manager, fname):
            return self.order_manager._merge_order_detail_to_shilai_order(order, user_id=user_id)
        return order

    def send_order_to_data_center(self, order, source=None):
        data_center_helper = DataCenterHelper(order=order)
        data_center_helper.send_order_to_data_center(source=source)

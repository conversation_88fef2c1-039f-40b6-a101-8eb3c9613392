# -*- coding: utf-8 -*-


"""
客如云正餐
"""

import logging
import time
import requests

from google.protobuf import json_format

import proto.ordering.keruyun.order_pb2 as order_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.keruyun.export_detail_pb2 as export_detail_pb
from business_ops.ordering.dish_manager import DishManager
from business_ops.ordering.table_manager import TableManager
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.keruyun_order_manager import KeruyunOrderManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common.utils import date_utils
from common.utils import distribute_lock
from common.utils import id_manager
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class KeruyunDinnerManager(KeruyunOrderManager):

    def __init__(self, order_manager=None, *args, **kargs):
        self._manager = order_manager
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>er<PERSON>ana<PERSON>, self).__init__(*args, **kargs)

        self.dish_package_box_merchant_ids = [
            "********************************", "d3348a6659ac401486d53753ea58e8d2"]
        self.table_manager = TableManager(
            merchant=self._manager.merchant, registration_info=self._manager.registration_info)
        self.dish_manager = DishManager(
            merchant=self._manager.merchant, registration_info=self._manager.registration_info)

        self.printer_manager = PrinterManager(merchant=self._manager.merchant)
        self.init_order()

    def init_order(self):
        ordering_service_da = OrderingServiceDataAccessHelper()
        self.keruyun_order = None
        if hasattr(self._manager, "order"):
            order = self._manager.order
            if order is not None:
                self.keruyun_order = ordering_service_da.get_keruyun_order(tp_order_id=order.id)
                self.order = order
        if hasattr(self._manager.business_obj, "order"):
            order = self._manager.business_obj.order
            if order is not None:
                self.keruyun_order = ordering_service_da.get_keruyun_order(tp_order_id=order.id)
                self.order = order

    def create_ordering_order(self, order, direct_pay=False):
        ordering_service_da = OrderingServiceDataAccessHelper()
        self.dish_manager.get_discount_plan(order.user_id, enbale_sale_time_discount=True)
        self.keruyun_order = order_pb.DinnerOrder()
        self.order = order
        self.__add_table_info()
        keruyun_pos_info = self._manager.registration_info.keruyun_pos_info
        self.keruyun_order.shop_identy = int(keruyun_pos_info.shop_id)
        self.keruyun_order.shop_name = keruyun_pos_info.shop_name
        self.keruyun_order.status = 2
        self.keruyun_order.remark = order.remark
        self.keruyun_order.create_time = date_utils.timestamp_second()
        self.keruyun_order.people_count = order.people_count
        self.keruyun_order.print = 1
        self.keruyun_order.tp_order_id = order.id
        self.__add_products()
        pay_type = self._manager.registration_info.pay_type
        if pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER and not direct_pay:
            self.__create_order()
            ordering_service_da.add_or_update_order(order=self.order)
        ordering_service_da.add_or_update_keruyun_order(self.keruyun_order)

    def __add_table_info(self):
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(
            ordering_service_table_id=str(self.order.table_id), merchant_id=self.order.merchant_id)
        t = self.keruyun_order.tables.add()
        t.table_id = int(self.order.table_id)
        t.table_name = table.name

    def __add_products(self):
        """ 创建菜单时把数据转成product实体
        """
        for shilai_product in self.order.products:
            product = self.keruyun_order.products.add()
            product.id = int(shilai_product.id)
            product.tp_id = shilai_product.id
            product.type = shilai_product.type
            product.name = shilai_product.name
            product.unit = shilai_product.unit
            product.price = shilai_product.price
            product.quantity = shilai_product.quantity
            product.uuid = shilai_product.uuid
            product.parent_uuid = shilai_product.parent_uuid
            product.total_fee = shilai_product.total_fee
            product.remark = ""
            if self.order.meal_type == dish_pb.DishOrder.TAKE_AWAY \
               and self._manager.merchant.id in self.dish_package_box_merchant_ids:
                # 如果是外带桌台
                # 并且每道菜都要增加外带备注
                product.remark = "外带"

            # self.keruyun_order.user_fee += int(shilai_product.discount_price * shilai_product.quantity + 0.5)
            self.keruyun_order.total_price += int(shilai_product.price * shilai_product.quantity + 0.5)

            total_reprice = self._create_keruyun_attr(product, shilai_product.attrs)
            # self.keruyun_order.user_fee += int(total_reprice + 0.5)
            self.keruyun_order.total_price += int(total_reprice + 0.5)

            total_price = self._create_keruyun_supply_condiment(
                product, shilai_product, shilai_product.supply_condiments)
            self.keruyun_order.total_price += total_price
            # self.keruyun_order.user_fee += total_price

    def __init_keruyun_order_discount_detail(self):
        # 商户让利
        discount_detail = self.keruyun_order.discount_details.add()
        discount_detail.discount_type = 3  # 商家优惠
        if hasattr(self._manager, "business_obj"):
            self.keruyun_order.user_fee = self._manager._calculate_paid_in_fee(
                self._manager.business_obj, order=self.order)
            if self.keruyun_order.user_fee - self.keruyun_order.total_price == 1:
                self.keruyun_order.user_fee = self.order.bill_fee
        else:
            self.keruyun_order.user_fee = self._manager._calculate_paid_in_fee(
                business_obj=None, order=self.order, transaction=None)
        discount_detail.discount_fee = self.order.bill_fee - self.keruyun_order.user_fee
        self.keruyun_order.discount_amount = discount_detail.discount_fee

    def __create_order(self):
        self.keruyun_order.remark = self.printer_manager.build_order_remark(self.order, newline='\n')
        self._manager._cal_commission(self.order)
        self.__init_keruyun_order_discount_detail()
        self.table_manager.check_table_status(table_id=self.order.table_id)
        json_body = json_format.MessageToDict(self.keruyun_order, including_default_value_fields=True)
        logger.info("向客如云发起创建订单: {}".format(json_body))
        # 向客如云发起订单
        resp = self.keruyun_try_create(json_body, try_times=3)
        if resp.get("code") == 2000 and "duplicate" in resp.get("message"):
            raise errors.KeruyunDuplicateKeyError()
        logger.info("客如云创建订单接口返回: {}, {}".format(resp, self.order.id))
        if resp.get("result") is None:
            raise errors.KeruyunCreateOrderError()
        self.order.ordering_service_order_id = resp.get("result").get("orderId")
        self.order.ordering_service_trade_id = resp.get("result").get("tradeId")
        self.order.ordering_service_serial_number = resp.get("result").get("serialNumber")
        self.order.status = dish_pb.DishOrder.APPROVED
        self.order.approve_time = date_utils.timestamp_second()

    def _create_keruyun_attr(self, product, order_attrs):
        _dish_attr_dict = {}
        for _attr in order_attrs:
            _dish_attr_dict.update({_attr.id: _attr})
        total_reprice = 0
        for attr in order_attrs:
            attr_id = attr.id
            dish_attr = _dish_attr_dict.get(attr_id)
            if not dish_attr:
                return 0
            product_attr = product.properties.add()
            product_attr.name = dish_attr.name
            product_attr.id = int(dish_attr.id)
            product_attr.type = dish_attr.type

            # 不要删除此注释
            # 因为从时来order转成客如云order的时候product.total_fee被设置为shilai_product.total_fee.
            # 这个时候已经加上了属性的变价了,所以下面这一行就不再需要了
            # product.total_fee += dish_attr.reprice * product.quantity

            product_attr.reprice = dish_attr.reprice
            total_reprice += dish_attr.reprice * product.quantity
        return total_reprice

    def _create_keruyun_supply_condiment(self, parent_product, shilai_product, order_supply_condiments):
        _dish_supply_condiment_dict = {}
        for _supply_condiment in order_supply_condiments:
            _dish_supply_condiment_dict.update({_supply_condiment.id: _supply_condiment})
        total_price = 0
        for supply_condiment in order_supply_condiments:
            supply_condiment_id = supply_condiment.id
            dish_supply_condiment = _dish_supply_condiment_dict.get(supply_condiment_id)
            if not dish_supply_condiment:
                continue
            if supply_condiment.quantity == 0:
                continue
            product = self.keruyun_order.products.add()
            product.quantity = supply_condiment.quantity
            product.id = int(supply_condiment_id)
            product.tp_id = str(supply_condiment_id)
            product.name = dish_supply_condiment.name
            product.price = dish_supply_condiment.market_price
            product.unit = shilai_product.unit
            product.type = 2
            product.parent_uuid = parent_product.uuid
            product.total_fee += int(product.price * product.quantity + 0.5)
            total_price += product.total_fee
        return total_price

    def pay_order(self, **kargs):
        """ 支付成功后调用此函数
        """
        order = self._manager.business_obj.order
        reordering = kargs.get("reordering", None)
        if reordering:
            return self.reordering(**kargs)
        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            with distribute_lock.redislock(key=order.table_id, ttl=5000, retry_count=50, retry_delay=200) as lock:
                if not lock:
                    raise errors.KeruyunPayOrderError()
                logger.info("调用客如云create_order: {}".format(order.id))
                self.keruyun_order.user_fee = self._manager._calculate_paid_in_fee(self._manager.business_obj)
                self.__create_order()
                return self.__pay_order()
            logger.info('不能获取桌台锁: tableID: {}, order_id{}'.format(order.table_id, order.id))
            raise errors.KeruyunPayOrderError()
        elif self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            # 后付款模式,计算佣金和计算商户让利需要在实际付款时才做计算
            self._manager._cal_commission(order)
            self.keruyun_order.user_fee = self._manager._calculate_paid_in_fee(self._manager.business_obj)
            self.__init_keruyun_order_discount_detail()
            return self.__pay_order()
        return False

    def reordering(self, **kargs):
        """ 针对客如云正餐的sync同步出错的问题
        """
        failed_order = kargs.get("failed_order")
        ordering_da = OrderingServiceDataAccessHelper()
        order = self._manager.business_obj.order
        order.ordering_service_order_id = failed_order.ordering_service_order_id
        order.ordering_service_trade_id = failed_order.ordering_service_trade_id
        self._manager._cal_commission(order)
        self.keruyun_order = ordering_da.get_keruyun_order(tp_order_id=order.id)
        self.keruyun_order.user_fee = self._manager._calculate_paid_in_fee(self._manager.business_obj)
        self.__init_keruyun_order_discount_detail()
        return self.__pay_order()

    def __pay_order(self, try_times=3):
        ''' 调用客如云的pay_order接口
            https://open.keruyun.com/docs?fir=8&sec=7
        '''
        if try_times <= 0:
            # 抛出异常.在支付的回调函数中会给用户退款
            raise errors.KeruyunPayOrderError()
        privilege_fee = self.order.bill_fee - self.keruyun_order.user_fee
        json_body = {
            "orderId": self.order.ordering_service_order_id,
            "totalFee": self.order.bill_fee,
            "isPrint": 0,
            "userFee": self.keruyun_order.user_fee,
            "privilegeFee": privilege_fee,
            "paymentDetail": {
                "payType": 3
            },
            "discountDetails": [
                {
                    "discountType": 3,
                    "discountFee": privilege_fee
                }
            ]
        }
        if self._manager.registration_info.printer_config.keruyun_printer.check_out_print:
            json_body.update({"isPrint": 1})

        logger.info("向客如云发起订单已支付: {}".format(json_body))
        uri = KeruyunConstants.KERUYUN_DISCOUNT_PAY
        token = self._manager.registration_info.keruyun_pos_info.token
        ret = self.post(token=token, uri=uri, json_body=json_body).json()
        logger.info("客如云订单已支付接口返回: {}".format(ret))
        if ret.get("code") == 0:
            self.table_manager.try_clear_table(order=self.order)
        elif ret.get("code") == 3001 and (try_times - 1) == 0:
            raise errors.KeruyunSyncOrderError()
        elif ret.get("code") == 2000 and (try_times - 1) == 0:
            raise errors.KeruyunBusinessException()
        else:
            # 有可能通知客如云订单已支付时,客如云主从还未同步创建的订单.
            # 所以失败之后进行2次重试
            logger.info('客如云调用支付接口失败,重试: {}'.format(self.order.id))
            time.sleep(0.5)
            return self.__pay_order(try_times=try_times - 1)

    def keruyun_try_create(self, json_body, try_times=5):
        """ 多个人同时用多个桌台的情况下,有可能会出现就餐中的情况
        """
        if try_times == 0:
            raise errors.KeruyunCreateOrderError()
        try:
            uri = KeruyunConstants.KERUYUN_CREATE_ORDER
            token = self._manager.registration_info.keruyun_pos_info.token
            resp = self.post(token=token, uri=uri, json_body=json_body)
        except requests.exceptions.ConnectTimeout:
            raise errors.KeruyunCreateOrderTimeout(error_codes.KERUYUN_CREATE_ORDER_TIMEOUT)
        resp = resp.json()
        logger.info("客如云创建订单接口返回: {}".format(resp))
        if resp.get("message") == "系统异常:java.net.SocketTimeoutException: Read timed out":
            return self.keruyun_try_create(json_body, try_times=try_times - 1)
        if resp.get("code") == 3000:
            if "就餐中" in resp.get("apiMessage") and try_times == 0:
                raise errors.KeruyunTableInDining()
            logger.info("客如云创建订单失败,重试: {}".format(json_body))
            time.sleep(1)
            return self.keruyun_try_create(json_body, try_times=try_times - 1)
        return resp

    def _create_dish_cooking_ways(self, dish_info, dish_cooking_ways):
        if not dish_cooking_ways:
            return
        for dish_cooking_way in dish_cooking_ways:
            c = dish_info.dish_cooking_way.add()
            cc = json_format.ParseDict(
                dish_cooking_way, export_detail_pb.KeruyunOrderDetail.DishInfo.DishCookingWay(), ignore_unknown_fields=True)
            c.CopyFrom(cc)

    def _create_dish_properties(self, dish_info, dish_properties):
        if not dish_properties:
            return
        for dish_property in dish_properties:
            p = dish_info.dish_properties.add()
            pp = json_format.ParseDict(
                dish_property, export_detail_pb.KeruyunOrderDetail.DishInfo.DishProperty(), ignore_unknown_fields=True)
            p.CopyFrom(pp)

    def _create_child_nodes(self, dish_info, child_nodes):
        if not child_nodes:
            return
        for child_node in child_nodes:
            c = dish_info.child_nodes.add()
            cc = json_format.ParseDict(
                child_node, export_detail_pb.KeruyunOrderDetail.DishInfo.ChildNode(), ignore_unknown_fields=True)
            c.CopyFrom(cc)

    def _merge_order_detail_to_shilai_order(self, order, user_id=None):
        """ 如果是后付款模式,有可能会在pos机上面修改菜单。这种情况直接把时来的order和客如云的order进行同步
        """
        # 越小栈模式不需要重新同步
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            return order
        # 先付款模式不需要同步
        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            return order
        keruyun_order_detail = self.get_order_detail_info(order)
        if not keruyun_order_detail:
            return order
        # 如果客如云的订单总金额和order中的订单总金额相同,就不用同步
        # if keruyun_order_detail.order_info.origin_amount == order.bill_fee:
        #     return order
        product_infos = {product_info.item_id: product_info for product_info in keruyun_order_detail.dish_infos}
        order.bill_fee = keruyun_order_detail.order_info.origin_amount
        order.paid_fee = 0
        order.discount_amount = 0
        order.enable_discount_fee = 0
        order.giving_fee = keruyun_order_detail.giving_fee
        order.is_pos_discount = False
        order.discount_amount = 0
        order.ifeedu_fee = 0
        # order.market_bill_fee = 0

        # 只要是从客如云同步了订单,就要去掉投喂的的菜品
        # 并把投喂的数量加回给用户
        # self.__recover_ifeedu_copies(order)

        ordering_service_da = OrderingServiceDataAccessHelper()
        combo_meals = []

        while len(order.products) > 0:
            product = order.products.pop(0)
            # 套餐
            if product.type == dish_pb.Dish.COMBO_MEAL:
                combo_meals.append(product)
            # 套餐子菜
            if product.parent_uuid != "" and product.type == dish_pb.Dish.SINGLE:
                combo_meals.append(product)
        while len(order.add_products) > 0:
            order.add_products.pop(0)
            # 套餐
            if product.type == dish_pb.Dish.COMBO_MEAL:
                combo_meals.append(product)
            # 套餐子菜
            if product.parent_uuid != "" and product.type == dish_pb.Dish.SINGLE:
                combo_meals.append(product)

        if order.user_id:
            self.dish_manager.get_discount_plan(order.user_id, enbale_sale_time_discount=True)
        elif user_id is not None:
            order.user_id = user_id
            self.dish_manager.get_discount_plan(user_id, enbale_sale_time_discount=True)
        dish_discount_amount = 0

        for item_id, product_info in product_infos.items():
            dish = ordering_service_da.get_dish(uuid=product_info.uuid, merchant_id=order.merchant_id, no_cache=True)
            self._manager.set_order_cannot_pay(order, dish)
            if dish.type == dish_pb.Dish.COMBO_MEAL:
                continue
            if int(product_info.quantity) <= 0:
                continue
            p = order.products.add()
            p.id = dish.id
            p.type = dish.type
            p.name = dish.name
            p.price = int(dish.price)
            p.quantity = int(product_info.quantity)
            p.category_id = dish.categories[0]
            p.market_price = dish.market_price
            if p.market_price == 0:
                p.market_price = p.price
            discount_price = self.dish_manager.get_discount_price(dish)
            p.discount_price = discount_price
            order.discount_amount += int(p.quantity * (p.price - p.discount_price) + 0.5)
            p.total_fee = int(product_info.amount)
            # 如果在pos机上修改菜单,无法精确的把时来的products和客如云返回的products对应上
            # 所以如果是投喂就要把投喂的菜品设置为不使用投喂
            p.is_ifeedu = False
            p.copies = 0
            p.uuid = product_info.uuid

            if dish.is_discount_dish:
                order.is_pos_discount = True
                order.discount_rate = dish.discount_rate
                dish_discount_amount = dish.discount_amount

            total_fee = 0
            if dish.type == dish_pb.Dish.SINGLE:
                total_fee = self._add_supply_condiment_from_child_nodes(p, product_info.child_nodes, dish)
                self._add_attr_from_dish_cooking_ways(p, product_info.dish_cooking_way, dish, order)
                self._add_attr_from_dish_properties(p, product_info.dish_properties, dish, order)
            # order.market_bill_fee += total_fee

            self._manager.update_order_enable_discount_fee(dish, order, p, total_fee)

        for product in combo_meals:
            p = order.products.add()
            p.CopyFrom(product)
            self._manager.update_order_enable_discount_fee(dish, order, p, 0)

        if order.is_pos_discount:
            if dish_discount_amount == 0:
                order.discount_amount = int(order.enable_discount_fee * order.discount_rate / float(100) + 0.5)
            else:
                order.discount_amount = dish_discount_amount

        order.paid_fee = order.bill_fee - order.discount_amount
        order.total_fee = order.paid_fee
        order.ifeedu_fee = 0
        commission = self._manager._cal_commission(order)
        order.commission = commission
        OrderingServiceDataAccessHelper().add_or_update_order(order)
        return order

    def _add_attr_from_dish_cooking_ways(self, product, dish_cooking_ways, dish, order):
        attrs_dict = {attr.name: attr for attr in dish.attrs}
        for dish_cooking_way in dish_cooking_ways:
            attr = product.attrs.add()
            name = dish_cooking_way.dish_cooking_way_name
            dish_attr = attrs_dict.get(name)
            attr.CopyFrom(dish_attr)
            product.total_fee += int(dish_attr.reprice * product.quantity)
            order.market_bill_fee += int(dish_attr.reprice * product.quantity)

    def _add_supply_condiment_from_child_nodes(self, product, child_nodes, dish):
        supply_condiment_dict = {supply_condiment.name: supply_condiment for supply_condiment in dish.supply_condiments}
        total_fee = 0
        for child_node in child_nodes:
            supply_condiment = product.supply_condiments.add()
            name = child_node.dish_name
            dish_supply_condiment = supply_condiment_dict.get(name)
            supply_condiment.id = dish_supply_condiment.id
            supply_condiment.name = name
            supply_condiment.market_price = dish_supply_condiment.market_price
            supply_condiment.quantity = int(child_node.quantity)
            total_fee += supply_condiment.market_price * supply_condiment.quantity
        return total_fee

    def _add_attr_from_dish_properties(self, product, dish_properties, dish, order):
        attrs_dict = {attr.name: attr for attr in dish.attrs}
        for dish_property in dish_properties:
            attr = product.attrs.add()
            name = dish_property.dish_property_name
            dish_attr = attrs_dict.get(name)
            attr.CopyFrom(dish_attr)
            product.total_fee += int(dish_attr.reprice * product.quantity)
            order.market_bill_fee += int(dish_attr.reprice * product.quantity)

    def get_order_detail_info(self, order):
        order_detail = self.get_order_detail(order.ordering_service_trade_id)
        keruyun_order_detail = None
        if order_detail:
            order_detail = order_detail[0]
            order_info = order_detail.get("orderInfo")
            keruyun_order_detail = export_detail_pb.KeruyunOrderDetail()
            keruyun_order_detail.order_info.CopyFrom(json_format.ParseDict(
                order_info, export_detail_pb.KeruyunOrderDetail.OrderInfo(), ignore_unknown_fields=True))
            for privilege_info in order_detail.get("privilegeInfos"):
                if privilege_info.get("privilegeValue") == "赠送":
                    keruyun_order_detail.giving_fee += privilege_info.get("privilegeAmount", 0) * -1
            for dish_info in order_detail.get("dishInfos"):
                dish_info_obj = keruyun_order_detail.dish_infos.add()
                dish_info_obj.amount = dish_info.get("amount")
                dish_info_obj.unit_name = dish_info.get("unitName")
                dish_info_obj.quantity = dish_info.get("quantity")
                dish_info_obj.price = dish_info.get("price")
                dish_info_obj.dish_name = dish_info.get("dishName")
                dish_info_obj.uuid = dish_info.get("uuid")
                dish_info_obj.item_id = dish_info.get("itemId")
                self._create_dish_cooking_ways(dish_info_obj, dish_info.get("dishCookingWay"))
                self._create_dish_properties(dish_info_obj, dish_info.get("dishProperties"))
                self._create_child_nodes(dish_info_obj, dish_info.get("childNodes"))
            for table_info in order_detail.get("tableInfos"):
                table_info_obj = keruyun_order_detail.table_infos.add()
                t = json_format.ParseDict(
                    table_info, export_detail_pb.KeruyunOrderDetail.TableInfo(), ignore_unknown_fields=True)
                table_info_obj.CopyFrom(t)

            base_info = order_detail.get("baseInfo")
            _base_info = json_format.ParseDict(
                base_info, export_detail_pb.KeruyunOrderDetail.BaseInfo(), ignore_unknown_fields=True)
            keruyun_order_detail.base_info.CopyFrom(_base_info)

            return keruyun_order_detail
        else:
            pass
        return keruyun_order_detail

    def create_pos_order(self, table_id, dishes, people_count):
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(merchant_id=self._manager.merchant.id, id=table_id)
        if not table:
            raise errors.Error(errcode=error_codes.KERUYUN_TABLE_NOT_FOUND,
                               errmsg=error_codes.KERUYUN_TABLE_NOT_FOUND_MSG)
        order = self._manager.create_shilai_order_obj()
        order.meal_type = dish_pb.DishOrder.EAT_IN
        order.user_id = ""
        order.merchant_id = self._manager.merchant.id
        order.create_time = date_utils.timestamp_second()
        order.people_count = people_count
        order.table_id = table.ordering_service_table_id
        for order_dish in dishes:
            quantity = order_dish.get("quantity", 1)
            dish_id = order_dish.get("id")
            dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, dish_id=dish_id)
            product = order.products.add()
            product.id = dish.id
            product.name = dish.name
            product.unit = dish.unit
            product.quantity = quantity
            product.price = int(dish.price)
            product.uuid = id_manager.generate_common_id()
            product.total_fee = int(dish.price) * quantity
            product.type = dish.type
            product.category_id = dish.categories[0]

            order.paid_fee += product.total_fee
            order.bill_fee += product.total_fee
        try:
            self.order = order
            self.create_ordering_order(order, direct_pay=True)
            self.__create_order()
            ordering_da.add_or_update_order(order=order)
        except errors.KeruyunTableInDining:
            raise errors.Error(err=error_codes.TABLE_IN_PAYING)
        return order

    def add_dish_ordering_order(self, dishes, order, user_id):
        additional_dish_order = order_pb.AdditionalDishOrder()
        ordering_service_da = OrderingServiceDataAccessHelper()
        self.dish_manager.get_discount_plan(user_id, enbale_sale_time_discount=True)

        keruyun_order = ordering_service_da.get_keruyun_order(tp_order_id=order.id)
        additional_dish_order.order_id = order.ordering_service_order_id
        additional_dish_order.table_id = int(order.table_id)
        table = ordering_service_da.get_table(ordering_service_table_id=order.table_id)
        additional_dish_order.table_name = table.name
        additional_dish_order.need_pos_agree = 0  # TODO: 加入到配置项
        additional_dish_order.shilai_order_id = order.id
        total_price = 0
        user_fee = 0
        for shilai_product in order.add_products[-1].products:
            dish = self.dish_manager.get_dish_from_cache(shilai_product.id)
            price = int(shilai_product.price)
            discount_price = self.dish_manager.get_discount_price(dish)
            quantity = shilai_product.quantity

            product = additional_dish_order.products.add()
            product.quantity = quantity
            product.tp_id = dish.id
            product.id = int(dish.id)
            product.name = dish.name
            product.unit = dish.unit
            product.uuid = shilai_product.uuid
            product.parent_uuid = shilai_product.parent_uuid
            product.price = int(price)
            product.total_fee += int(int(price) * quantity)
            product.remark = shilai_product.remark

            for order_attr in shilai_product.attrs:
                attr = product.properties.add()
                attr.id = int(order_attr.id)
                attr.name = order_attr.name
                attr.type = order_attr.type
                attr.reprice = int(order_attr.reprice + 0.5)
                product.total_fee += int(quantity * attr.reprice)
                total_price += product.total_fee
                user_fee += product.total_fee

            # 客如云不支持加有加料的菜品
            if len(shilai_product.supply_condiments) > 0:
                raise errors.CannotAddDishWithSupplyCondiments()

            total_price += product.price * quantity
            user_fee += discount_price * quantity

        discount_detail = additional_dish_order.discount_details.add()
        discount_detail.discount_type = 3
        discount_detail.discount_fee = int(total_price - user_fee)

        json_body = json_format.MessageToDict(additional_dish_order, including_default_value_fields=True)
        logger.info("add dish order to keruyun: {}".format(json_body))
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_APPLY_ADD_DISH
        ret = self.post(token=token, uri=uri, json_body=json_body).json()
        logger.info("add dish keruyun return: {}".format(ret))
        if ret.get("code") != 0:
            raise errors.AddDishFailed()
        ordering_service_da.add_keruyun_additional_dish_order(additional_dish_order)
        keruyun_order.total_price += int(total_price)
        keruyun_order.user_fee += int(user_fee)
        ordering_service_da.add_or_update_keruyun_order(keruyun_order)
        user_ids = order.user_ids
        if user_id not in user_ids and user_id != order.user_id:
            user_ids.add(user_id)
        ordering_service_da.add_or_update_order(order=order)

    def create_zero_order(self, order):
        """ 开台,并创建一个0元订单
        """
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(
            merchant_id=self._manager.merchant.id, meal_type=dish_pb.DishOrder.DIRECT_PAY)
        if not table:
            raise errors.Error(errcode=error_codes.KERUYUN_TABLE_NOT_FOUND,
                               errmsg=error_codes.KERUYUN_TABLE_NOT_FOUND_MSG)
        zero_dish = ordering_da.get_dish(merchant_id=self._manager.merchant.id, is_zero_dish=True)
        if not zero_dish:
            raise errors.Error(errcode=error_codes.DISH_NOT_EXISTS,
                               errmsg=error_codes.DISH_NOT_EXISTS_MSG)
        order.meal_type = dish_pb.DishOrder.DIRECT_PAY
        order.user_id = self._manager.user.id
        order.merchant_id = self._manager.merchant.id
        order.create_time = date_utils.timestamp_second()
        order.people_count = 1
        product = order.products.add()
        product.id = zero_dish.id
        product.name = zero_dish.name
        product.unit = zero_dish.unit
        product.quantity = 1
        product.price = 0
        product.uuid = id_manager.generate_common_id()
        product.total_fee = 0
        product.type = zero_dish.type
        product.category_id = zero_dish.categories[0]
        order.table_id = table.ordering_service_table_id
        try:
            self.create_ordering_order(order, direct_pay=True)
            self.__create_order(order=order, transaction=None, coupon_fee=0)
            ordering_da.add_or_update_order(order=order)
        except errors.KeruyunTableInDining:
            raise errors.Error(err=error_codes.TABLE_IN_PAYING)
        return order

# -*- coding: utf-8 -*-

import logging
import json
from collections import namedtuple

from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.base_order_manager import BaseOrderManager
from business_ops.ordering.shilai_ops_manager import Shilai<PERSON>SManager
from business_ops.ordering.constants import ShilaiPosConstants
from business_ops.message_manager import MessageManager
from business_ops.transaction_manager import TransactionManager
from cache.redis_client import RedisClient
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
from service import error_codes
from proto.ordering import dish_pb2 as dish_pb

logger = logging.getLogger(__name__)


class ShilaiOrderManager(BaseOrderManager):
    def __init__(self, order_manager, *args, **kargs):
        self._manager = order_manager
        self._partial_refund = kargs.get("partial_refund")
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kargs)
        self._ops = ShilaiOPSManager(merchant=order_manager.merchant, store=order_manager.store)

    def __create_ordering_order(self, order):
        ordering_da = OrderingServiceDataAccessHelper()
        dinner_type = registration_pb.OrderingConfig.DinnerType.Name(
            self._manager.registration_info.ordering_config.dinner_type
        )
        shilai_pos_order = {
            "orderType": dish_pb.DishOrder.MealType.Name(order.meal_type),
            "shippingAddress": json_format.MessageToDict(order.shipping_address),
            "comment": order.remark,
            "merchantId": self._manager.merchant.id,
            "storeId": self._manager.store.id,
            "source": "MINI_PROGRAM",
            "tableId": order.table_id,
            "cashierMode": dinner_type,
            "operateSerialNumber": order.operate_serial_number,
            "peopleCount": order.people_count,
        }
        if order.ordering_service_order_id != "":
            shilai_pos_order.update({"id": order.ordering_service_order_id})

        items = []
        products = self.__preprocess_order_products(order)
        items = self.__create_order_items(ordering_da, products, shilai_pos_order)
        shilai_pos_order.update({"items": items})

        if self._partial_refund:
            back_items = []
            back_products = self.__preprocess_back_products(order.back_products[-1])
            back_items = self.__create_order_items(ordering_da, back_products, shilai_pos_order)
            shilai_pos_order.update({"backItems": back_items})

        url = self._ops.generate_url(ShilaiPosConstants.CREATE_ORDER)
        ret = self._ops.do_post(url, shilai_pos_order)
        if ret.get("errcode") != 0:
            raise errors.ShowError(ret.get("errmsg"))

        dinner_type = self._manager.registration_info.ordering_config.dinner_type
        if dinner_type == registration_pb.OrderingConfig.DINNER and order.status == dish_pb.DishOrder.ORDERED:
            order.status = dish_pb.DishOrder.APPROVED
        pos_order = ret.get("data")
        order.ordering_service_order_id = pos_order.get("id")
        order.session_id = pos_order.get("sessionId", "")
        order.operate_serial_number = pos_order.get("operateSerialNumber")
        status = pos_order.get("status")
        serial_number = pos_order.get("serialNumber")
        if serial_number and not order.serial_number:
            order.serial_number = int(pos_order.get("serialNumber"))
        if status == "TO_BE_CONFIRMED":
            order.status = dish_pb.DishOrder.TO_BE_CONFIRMED
        elif status == "APPROVED":
            order.status = dish_pb.DishOrder.APPROVED
        ordering_da.add_or_update_order(order)
        return True

    def create_ordering_order(self, order):
        # 快餐模式必须要支付之后才下单
        if self._manager.registration_info.ordering_config.dinner_type == registration_pb.OrderingConfig.FAST_FOOD:
            return True
        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
            return self.__create_ordering_order(order)
        return True

    def __preprocess_back_products(self, back_products):
        Products = namedtuple("Products", ["products", "sub_products"])
        products = []
        sub_products = {}
        for product in back_products.products:
            if product.parent_uuid == "":
                products.append(product)
            elif product.parent_uuid != "":
                _sub_products = sub_products.get(product.parent_uuid, [])
                _sub_products.append(product)
                sub_products.update({product.parent_uuid: _sub_products})
        return Products(products, sub_products)

    def __preprocess_order_products(self, order):
        Products = namedtuple("Products", ["products", "sub_products"])
        products = []
        sub_products = {}
        for product in order.products:
            if product.parent_uuid == "":
                products.append(product)
            elif product.parent_uuid != "":
                _sub_products = sub_products.get(product.parent_uuid, [])
                _sub_products.append(product)
                sub_products.update({product.parent_uuid: _sub_products})
        for add_product in order.add_products:
            for product in add_product.products:
                if product.parent_uuid == "":
                    products.append(product)
                elif product.parent_uuid != "":
                    _sub_products = sub_products.get(product.parent_uuid, [])
                    _sub_products.append(product)
                    sub_products.update({product.parent_uuid: _sub_products})
        for product in order.to_be_confirmed_products:
            products.append(product)
        return Products(products, sub_products)

    def __create_order_items(self, ordering_da, products, shilai_pos_order):
        order_da = OrderingServiceDataAccessHelper()
        required_items = order_da.get_required_items(shilai_pos_order['merchantId'])
        items = []
        for product in products.products:
            item = self.__create_order_item(ordering_da, None, product, shilai_pos_order, required_items=required_items)
            _sub_items = item.get("subItems", [])
            for sub_product in products.sub_products.get(product.uuid, []):
                sub_item = self.__create_order_item(ordering_da, product, sub_product, shilai_pos_order, required_items=required_items)
                _sub_items.append(sub_item)
            item["subItems"] = _sub_items
            items.append(item)
        return items

    def __create_order_item(self, ordering_da, parent_product, product, shilai_pos_order, required_items=None):
        dish = ordering_da.get_dish(dish_id=product.id, merchant_id=self._manager.merchant.id)
        dish_type = dish_pb.Dish.DishType.Name(dish.type)
        child_add_price = product.price
        if parent_product:
            quantity = int(product.quantity / parent_product.quantity)
            parent_dish = ordering_da.get_dish(dish_id=parent_product.id, merchant_id=self._manager.merchant.id)
            child_add_price = self.__get_child_dish_add_price(parent_dish, product)
        else:
            quantity = int(product.quantity)
        item = {
            "quantity": quantity,
            "price": int(product.discount_price or product.price),
            "dishId": product.id,
            "dishType": dish_type,
            "name": dish.name,
            "addPrice": int(child_add_price),
            "uuid": product.uuid,
            "packagingBox": {
                "quantity": 0,
                "price": 0,
                "name": '',
                "id": '',
                'isPacked': False
                if shilai_pos_order.get("orderType") == dish_pb.DishOrder.MealType.Name(dish_pb.DishOrder.MealType.TAKE_AWAY)
                else False,
            },
            "requiredItemInfo": {
                "isRequired": False,
                "requiredType": ""
            }
        }
        if required_items and product.id in required_items:
            item['requiredItemInfo']['isRequired'] = True
            required_item = required_items[product.id]
            required_type_map = {
                "EVERYONE": "PER_PERSON",
                "ONLY_ONE": "PER_TABLE"
            }
            required_type = required_type_map.get(required_item['type'])
            if required_type:
                item['requiredItemInfo'].update({
                    "isRequired": True,
                    "requiredType": required_type
                })

        self.__create_order_set_attributes(item, product, dish, parent_product)
        self.__create_order_set_supply_condiments(item, product)
        return item

    def __get_child_dish_add_price(self, parent_dish, product):
        child_add_price = int(product.price)
        for child_group in parent_dish.child_dish_groups:
            for child_dish in child_group.child_dishes:
                if child_dish.id == product.id:
                    return child_dish.price
        return child_add_price

    def __create_order_set_attributes(self, item, product, dish, parent_product):
        attribute_options = []
        spec_options = []
        for attr in product.attrs:
            if attr.type in [dish_pb.Attr.TASTE, dish_pb.Attr.UNKNOWN]:
                attribute_options.append(
                    {"addPrice": int(attr.reprice), "price": int(attr.reprice), "name": attr.name, "optionId": attr.id}
                )
            elif attr.type == dish_pb.Attr.SPECIFICATION:
                if attr.is_sync_from_pos:
                    price = int(attr.reprice)
                else:
                    price = int(attr.reprice + dish.price)
                spec_options.append({"price": price, "addPrice": price, "name": attr.name, "optionId": attr.id})
            elif attr.type == dish_pb.Attr.TAKE_AWAY:
                item.update(
                    {
                        "packagingBox": {
                            "quantity": 1 if parent_product else int(product.quantity),
                            "price": int(attr.reprice),
                            "name": attr.name,
                            "id": attr.id,
                            'isPacked': True,
                        }
                    }
                )
        item.update({"attributeOptions": attribute_options, "specOptions": spec_options})

    def __create_order_set_supply_condiments(self, item, product):
        _sub_items = item.get("subItems", [])
        for sc in product.supply_condiments:
            quantity = int(sc.quantity / product.quantity)
            _sub_items.append({"quantity": quantity, "price": sc.market_price, "dishType": "ADDON", "dishId": sc.id})
        item["subItems"] = _sub_items

    def pay_order(self, **kargs):
        """支付成功后调用此函数"""
        self._partial_refund = kargs.get("partial_refund", self._partial_refund)
        platform = kargs.get("platform")
        order = self._manager.business_obj.order
        if self._manager.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_FIRST:
            if not self.__is_pos_partial_refund(platform):
                # 如果不是从收银机下发下来的部分退款的消息才会去更新
                self.__create_ordering_order(order)
        transaction = self._manager.business_obj.transaction
        self._manager._cal_commission(order, transaction)
        self._manager._calculate_paid_in_fee(self._manager.business_obj)
        self._manager.cal_wallet_pay_commission_fee(order, transaction)
        self._manager.cal_coupon_commission_fee(order, transaction)
        params = {
            "orderId": order.ordering_service_order_id,
            "merchantId": order.merchant_id,
            "storeId": order.store_id,
            "promotionInfos": self.set_promotion_infos(),
            "totalPaidFee": transaction.paid_fee,
            "totalReceivableFee": order.paid_in_fee,
            "actualTotalReceivableFee": order.actual_total_receivable_fee,
            "appointmentTime": order.appointment_time,
        }
        if not self.__is_pos_partial_refund(platform):
            url = self._ops.generate_url(ShilaiPosConstants.PAY_SUCCESS)
            ordering_order = self._ops.do_post(url, params)
            if ordering_order.get("data"):
                if order.meal_type in [dish_pb.DishOrder.TAKE_AWAY, dish_pb.DishOrder.SELF_PICK_UP]:
                    order.meal_code = ordering_order.get("data").get("serialNumber")
                order.ordering_service_serial_number = ordering_order.get("data").get("serialNumber")
        self.__send_order_paid_message(order)
        return True

    def __is_pos_partial_refund(self, platform):
        """是否是从收银机下发的部分退款的消息"""
        if platform == "POS" and self._partial_refund:
            return True
        return False

    def set_promotion_infos(self):
        order = self._manager.business_obj.order
        transaction = self._manager.business_obj.transaction
        promotion_infos = []
        if order.discount_amount > 0:
            promotion_infos.append({"promotionType": "MINI_PROGRAM_REDUCE", "discountFee": order.discount_amount})
        if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            promotion_infos.append(
                {
                    "promotionType": "MINI_PROGRAM_FANPIAO",
                    "discountFee": order.bill_fee - transaction.paid_fee,
                    "commissionFee": order.fanpiao_commission_fee,
                }
            )
        if transaction.use_coupon_id:
            promotion_infos.append(
                {
                    "promotionType": "MINI_PROGRAM_COUPON_PACKAGE",
                    "discountFee": order.coupon_fee,
                    "commissionFee": order.coupon_package_commission_fee,
                }
            )
        return promotion_infos

    def add_dish_ordering_order(self, dishes, order, user_id):
        self.__create_ordering_order(order)

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        if not order:
            raise errors.OrderNotFound()
        if order.status not in [dish_pb.DishOrder.PAID, dish_pb.DishOrder.REFUNDING]:
            raise errors.Error(err=error_codes.ORDER_NOT_PAID)
        return self._manager.pos_return_order(transaction_id=order.transaction_id)

    def sync_direct_pay_order(self, order, transaction):
        uri = ShilaiPosConstants.SYNC_DIRECT_PAY_ORDER
        url = self._ops.generate_url(uri)
        pay_method_name = TransactionManager.get_transaction_pay_method_name(transaction)
        speak_fee = order.paid_in_fee + order.fanpiao_commission_fee + order.coupon_package_commission_fee
        params = {
            "order": {
                "merchantId": order.merchant_id,
                "storeId": order.store_id,
                "billFee": order.bill_fee,
                "paidFee": order.paid_fee,
                "paidTime": order.paid_time,
                "id": order.id,
                "source": "NUMBER_PLATE_PAY",
                "paySource": "NUMBER_PLATE_PAY",
                "payMethodName": pay_method_name,
                "posType": "SHILAI",
                "totalReceivableFee": order.paid_in_fee,
            },
            "speakFee": speak_fee,
        }
        ret = self._ops.do_post(url, params)
        if order.meal_type == dish_pb.DishOrder.DIRECT_PAY:
            self._ops.voice_broadcast(fee=speak_fee, type=self._ops.PAY_SUCCESS)
        return ret

    def sync_order_from_pos(self, order_id):
        uri = ShilaiPosConstants.GET_ORDER
        url = self._ops.generate_url(uri)
        params = {"id": order_id, "withConfirmed": True}
        ret = self._ops.do_post(url, params)
        data = ret.get("data")
        if not data:
            return None
        order = data.get("order")
        return order

    def get_order_detail_info(self, order):
        pos_order = self.sync_order_from_pos(order.ordering_service_order_id)
        return pos_order

    def merge_pos_order(self, order):
        if self._manager.registration_info.pos_type != registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            return order
        if order.status == dish_pb.DishOrder.PAID:
            return order
        if self._manager.registration_info.ordering_config.dinner_type == registration_pb.OrderingConfig.FAST_FOOD:
            return order
        pos_order = self.sync_order_from_pos(order.ordering_service_order_id)
        if not pos_order:
            return order
        self.__set_order_base_info(order, pos_order)
        order.operate_serial_number = pos_order.get("operateSerialNumber", 0)
        items = pos_order.get("items")
        items = {item.get("uuid"): item for item in items}
        self.__merge_products(order, items)
        ordering_da = OrderingServiceDataAccessHelper()
        ordering_da.add_or_update_order(order)
        return order

    def __set_order_base_info(self, order, pos_order):
        order.merchant_id = pos_order.get("merchantId")
        order.store_id = pos_order.get("storeId")
        status = pos_order.get("status")
        self.__set_order_status(order, status)
        order.serial_number = int(pos_order.get("serialNumber"))
        order.ordering_service_serial_number = pos_order.get("serialNumber")
        order.session_id = pos_order.get("sessionId")
        if pos_order.get("dineInOrder"):
            order.table_id = pos_order.get("dineInOrder").get("tableId")

    def __set_order_status(self, order, status):
        if status == "TO_BE_CONFIRMED":
            order.status = dish_pb.DishOrder.TO_BE_CONFIRMED
        elif status == "CONFIRMED":
            order.status = dish_pb.DishOrder.APPROVED
        elif status == "APPROVED":
            order.status = dish_pb.DishOrder.APPROVED
        elif status == "REFUSED":
            order.status = dish_pb.DishOrder.REJECRED
        elif status == "REVERTED_ORDER_CREATED":
            order.status = dish_pb.DishOrder.ORDERED
        elif status == "PAID":
            order.status = dish_pb.DishOrder.PAID
        elif status == "REFUNDED":
            order.status = dish_pb.DishOrder.POS_RETURN
        elif status == "CREATED":
            order.status = dish_pb.DishOrder.ORDERED
        elif status == "CANCELLED":
            order.status = dish_pb.DishOrder.CANCELLED
        elif status == "PAID_TO_BE_CONFIRMED":
            order.status = dish_pb.DishOrder.PAID
        elif status == "REVOKED":
            order.status = dish_pb.DishOrder.REVOKED

    def __merge_products(self, order, items):
        """从收银机上面把订单同步下来,并合并菜品信息到数据库"""
        while order.products:
            order.products.pop()
        while order.add_products:
            order.add_products.pop()
        while order.to_be_confirmed_products:
            order.to_be_confirmed_products.pop()
        order.total_fee = 0
        order.paid_fee = 0
        order.bill_fee = 0
        order.enable_discount_fee = 0
        order_da = OrderingServiceDataAccessHelper()
        for uuid, item in items.items():
            status = item.get("status", "") # REFUSED -> REJECRED
            if status == "REFUSED":
                continue
            order.total_fee += item.get("billFee")
            order.paid_fee += item.get("billFee")
            order.bill_fee += item.get("billFee")
            batch_number = item.get("batchNumber")
            dish = order_da.get_dish(dish_id=item.get("dishId"), merchant_id=self._manager.merchant.id)
            if status == "TO_BE_CONFIRMED":
                self.__merge_product(order.to_be_confirmed_products, item, to_be_confirmed=True)
            else:
                if batch_number == 1:
                    self.__merge_product(order.products, item, dish=dish, order=order)
                else:
                    # while len(order.add_products) < batch_number:
                    #     order.add_products.add()
                    #     batch_number -= 1
                    # self.__merge_product(order.add_products[batch_number - 1].products, item, dish=dish, order=order)

                    if not order.add_products:
                        order.add_products.add()
                        last_batch_number = 1
                    else:
                        last_batch_number = order.add_products[-1].products[-1].batch_number

                    if batch_number > last_batch_number:
                        order.add_products.add()
                    self.__merge_product(order.add_products[-1].products, item, dish=dish, order=order)
                    """
                    addProducts: [
                        {products: [{}, {}]}  // batch_num1
                        {products: [{}, {}]}  // batch_num2
                    ]
                    """

        idx = len(order.add_products) - 1
        while idx >= 0:
            if not order.add_products[idx].products:
                order.add_products.pop(idx)
            idx -= 1

    def __merge_product(
        self, products, item, parent_uuid=None, to_be_confirmed=False, dish=None, order=None, parent_quantity=None
    ):
        product = products.add()
        product.id = item.get("dishId")
        product.name = item.get("name")
        product.price = item.get("price")
        if parent_uuid is not None:
            product.price = item.get("addPrice", 0)
        product.quantity = item.get("quantity")
        if parent_uuid is not None and parent_quantity is not None:
            product.quantity *= parent_quantity
        product.uuid = item.get("uuid")
        product.total_fee = int(product.price * product.quantity)
        product.create_time = int(item.get("createTime", 0))
        product.batch_number = int(item.get("batchNumber", 0))
        product.image_url = item.get("imageUrl", "")
        dish_type = item.get("dishType")
        if dish_type == "SINGLE":
            product.type = dish_pb.Dish.SINGLE
        elif dish_type == "COMBO":
            product.type = dish_pb.Dish.COMBO_MEAL
        self.__merge_product_specs(product, item)
        self.__merge_product_attrs(product, item)
        if parent_uuid is not None:
            product.parent_uuid = parent_uuid
        merge_sub_items = self.__merge_sub_items(products, product, item.get("subItems"))
        if not to_be_confirmed and dish and order:
            self._manager.update_order_enable_discount_fee(
                dish=dish, order=order, product=product, supply_condiment_fee=merge_sub_items.sc_fee
            )

    def __merge_sub_items(self, products, product, sub_items):
        MergeSubItems = namedtuple("MergeSubItems", ["sc_fee"])
        sc_fee = 0
        for sub_item in sub_items:
            dish_type = sub_item.get("dishType")
            if dish_type == "ADDON":
                sc_fee = self.__merge_product_supply_condiment(product, sub_item)
            else:
                self.__merge_product(products, item=sub_item, parent_uuid=product.uuid, parent_quantity=product.quantity)
        return MergeSubItems(sc_fee=sc_fee)

    def __merge_product_attrs(self, product, item):
        for option in item.get("attributeOptions"):
            attr = product.attrs.add()
            attr.name = option.get("name")
            attr.reprice = option.get("addPrice")
            attr.id = option.get("optionId")
            product.total_fee += int(attr.reprice * product.quantity)

    def __merge_product_supply_condiment(self, product, item):
        sc = product.supply_condiments.add()
        sc.name = item.get("name")
        sc.market_price = item.get("price")
        sc.quantity = int(item.get("quantity"))
        sc.id = item.get("dishId")
        sc_fee = sc.quantity * sc.market_price
        return sc_fee

    def __merge_product_specs(self, product, item):
        for option in item.get("specOptions"):
            attr = product.attrs.add()
            attr.name = option.get("name")
            attr.reprice = option.get("price")
            attr.type = dish_pb.Attr.SPECIFICATION
            attr.is_sync_from_pos = True
            product.total_fee = int(attr.reprice * product.quantity)

    def __send_order_update_message(self, order):
        message = {"type": MessageManager.ORDER_UPDATE, "orderInfo": {"id": order.id}}
        channel = order.table_id
        logger.info("发送订单已下单的消息: {} {}".format(message, channel))
        self.__send_message(channel, message)

    def __send_order_paid_message(self, order):
        message = {
            "type": MessageManager.ORDER_PAID,
            "orderInfo": {"id": order.id},
            "exclude_user_ids": [self._manager.business_obj.user.id],
        }
        channel = order.table_id
        logger.info("发送订单已支付的消息: {} {}".format(message, channel))
        self.__send_message(channel, message)

    def __send_message(self, channel, message):
        message = json.dumps(message)
        redis_client = RedisClient()
        redis_client.publish(channel, message)

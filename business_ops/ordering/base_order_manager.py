# -*- coding: utf-8 -*-


from business_ops.base_manager import BaseManager
from service import error_codes
from service import errors


class BaseOrderManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(BaseOrderManager, self).__init__(*args, **kargs)

    def create_ordering_order(self, *args, **kargs):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def pay_order(self, *args, **kargs):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def add_dish_ordering_order(self, *args, **kargs):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def pos_order_bell(self, table, source, content, order=None):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def create_pos_order(self, table_id, dishes, people_count):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def create_zero_order(self, order):
        raise errors.Error(err=error_codes.NOT_IMPLEMENT)

    def patch_kitchen_print(self, order, *args, **kargs):
        return

    def sync_direct_pay_order(self, order, transaction):
        return None

    def merge_pos_order(self, order):
        return order

    def pos_order_create(self, order_id):
        return None

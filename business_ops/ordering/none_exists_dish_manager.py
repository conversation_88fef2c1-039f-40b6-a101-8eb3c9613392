# -*- coding: utf-8 -*-

from business_ops.ordering.base_dish_manager import BaseDishManager
from service.errors import ShowError


class NoneExistsDishManager(BaseDishManager):

    whitelist_merchant_id = set([
        "d3348a6659ac401486d53753ea58e8d2",
        "1e543376139b474e97d38d487fa9fbe8"
    ])

    def __init__(self, dish_manager=None, *args, **kargs):
        self._manager = dish_manager
        if self._manager.merchant.id not in self.whitelist_merchant_id:
            raise ShowError("POS type 错误")
        super(NoneExistsDishManager, self).__init__(*args, **kargs)

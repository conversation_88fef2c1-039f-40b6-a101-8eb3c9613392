# -*- coding: utf-8 -*-

import logging
import json
import re
from collections import namedtuple

import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.hualala_ops import HualalaOPSManager
from business_ops.ordering.base_dish_manager import BaseDishManager
from business_ops.ordering.constants import HualalaConstants
from common.aliyun_oss_helper import AliyunOSSHelper
from common.utils import id_manager
from common.utils import date_utils
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper

logger = logging.getLogger(__name__)


class HualalaDishManager(HualalaOPSManager, BaseDishManager):

    def __init__(self, dish_manager=None, *args, **kargs):
        self._manager = dish_manager
        self.set_food_group_name_pattern = re.compile(r"(\d+)选(\d+)")
        super(HualalaDishManager, self).__init__(*args, **kargs)

    def get_dish_categories(self):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id
        }
        route = HualalaConstants.GET_DISH_CATEGORIES
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data
        return None

    def get_dish_sale_times(self, dish):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id,
            "foodID": dish.third_party_dish_id
        }
        route = HualalaConstants.GET_FOOD_SALE_TIME
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data
        return None

    def set_dish_sale_times(self, dish, records):
        dish.dish_week_day = -1
        if not records:
            return
        for record in records:
            try:
                sale_times = json.loads(record.get("salTimeJson"))
            except:
                logger.info("哗啦啦同步菜品售卖时间报错: {}".format(record))
                continue
            sale_day_type = record.get("salDayType")
            sale_time_type = record.get("salTimeType")
            if sale_day_type == "1":
                sale_end_day = record.get("salEndDay")
                sale_begin_day = record.get("salBeginDay")
                dish.sale_begin_day = sale_begin_day
                dish.sale_end_day = sale_end_day
                mon = record.get("mon")
                dish.dish_week_day = 0
                if mon == "1":
                    dish.dish_week_day |= self._manager.MON
                tue = record.get("tues")
                if tue == "1":
                    dish.dish_week_day |= self._manager.TUE
                wed = record.get("wed")
                if wed == "1":
                    dish.dish_week_day |= self._manager.WED
                thu = record.get("thur")
                if thu == "1":
                    dish.dish_week_day |= self._manager.THU
                fri = record.get("fri")
                if fri == "1":
                    dish.dish_week_day |= self._manager.FRI
                sat = record.get("sat")
                if sat == "1":
                    dish.dish_week_day |= self._manager.SAT
                sun = record.get("sun")
                if sun == "1":
                    dish.dish_week_day |= self._manager.SUN
            if sale_time_type == "1":
                for sale_time in sale_times:
                    start_time = sale_time.get("startTime")
                    end_time = sale_time.get("endTime")
                    start_timestamp = date_utils.time_to_timestamp(start_time)
                    end_timestamp = date_utils.time_to_timestamp(end_time)
                    dish_sale_time = dish.sale_times.add()
                    dish_sale_time.start = start_timestamp
                    dish_sale_time.end = end_timestamp

    def async_categories(self, names=None, dish_type_ids=None):
        dish_categories = self.get_dish_categories()
        dish_categories = dish_categories.get("foodCategory")
        ret = []
        for category in dish_categories:
            category_template = self.create_shilai_dish_category_template()
            category_template.update({
                "name": category.get("foodCategoryName"),
                "sort": int(category.get("sortIndex")),
                "id": id_manager.generate_common_id(),
                "third_party_category_id": str(category.get("foodCategoryID"))
            })
            logger.info("sync dish category from hualala: {}".format(category_template))
            ret.append(category_template)
        return ret

    def get_all_dish(self):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id
        }
        route = HualalaConstants.GET_DISHES
        ret = self.try_post(route, params)
        if ret.flag:
            dishes = []
            food_list = ret.data.get("foodList")
            dishes_dict = self.deal_with_dish(food_list)
            food_tags = ret.data.get("foodTags")
            self.sync_dish_supply_condiments(dishes_dict, food_tags, food_list)
            self.sync_combo_meal_dish(dishes_dict, food_list)
            for _, dish in dishes_dict.items():
                sale_times = self.get_dish_sale_times(dish)
                self.set_dish_sale_times(dish, sale_times.get("records"))
                dishes.append(dish)
            return dishes
        return None

    def deal_with_dish(self, food_list):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes_dict = {}
        for dish in food_list:
            third_party_dish_id = dish.get("foodID")
            dish_obj = self.get_dish_obj(third_party_dish_id)
            dish_obj.third_party_dish_id = third_party_dish_id
            third_party_category_id = dish.get("foodCategoryID")
            category = ordering_da.get_category(
                third_party_category_id=third_party_category_id, no_cache=True,
                merchant_id=self._manager.merchant.id)
            logger.info("花啦啦同步菜品1: {}".format(dish.get("foodName")))
            if not category:
                continue
            if len(dish_obj.categories) == 1:
                dish_obj.categories[0] = category.id
            else:
                dish_obj.categories.append(category.id)
            self.sync_dish_common_info(dish, dish_obj)
            logger.info("花啦啦同步菜品2: {}".format(dish.get("foodName")))
            while dish_obj.attrs:
                dish_obj.attrs.pop()
            self.sync_making_method_group_list(dish, dish_obj)
            self.sync_taste_group_list(dish, dish_obj)
            dishes_dict.update({dish_obj.third_party_dish_id: dish_obj})
        return dishes_dict

    def sync_dish_common_info(self, dish, dish_obj):
        """ 同步菜品的基本属性
        """
        dish_obj.name = dish.get("foodName")
        if dish.get("isDiscount") == "true":
            dish_obj.no_discount = False
        else:
            dish_obj.no_discount = True
        if dish.get("isActive") == "false":
            dish_obj.status = dish_pb.Dish.GUQING
        dish_obj.sort = int(dish.get("foodSortIndex"))
        dish_obj.min_order_num = int(float(dish.get("minOrderCount")))
        unit = self.get_dish_price(dish.get("units"))
        dish_obj.price = unit.price
        dish_obj.unit = unit.name
        dish_obj.unit_key = unit.key
        try:
            self.set_image(dish, dish_obj)
        except:
            pass

    def set_image(self, dish, dish_obj):
        is_has_image = dish.get("isHasImage")
        if is_has_image == "false":
            return
        hualala_domain = "http://res.hualala.com"
        image = "{}/{}".format(hualala_domain, dish.get("imgePath"))
        aliyun_oss_helper = AliyunOSSHelper()
        image_obj = aliyun_oss_helper.upload_image_network_stream(image)
        target_name = "thumb-{}".format(image_obj.name)
        thumb_image_obj = aliyun_oss_helper.resize(image_obj.name, target_name, model="m_lfit", w=160, h=160)
        if len(dish_obj.images) == 0:
            dish_obj.images.append(image_obj.url)
        else:
            dish_obj.images[0] = image_obj.url
        dish_obj.thumb_image = thumb_image_obj.url

    def get_dish_price(self, units):
        Unit = namedtuple("Unit", ["price", "name", "key"])
        if len(units) == 0:
            return 0
        unit = units[0]
        price = round(float(unit.get("price", 0)) * 100)
        name = unit.get("unit")
        key = unit.get("unitKey")
        return Unit(price=price, name=name, key=key)

    def sync_combo_meal_dish(self, dishes_dict, food_list):
        """ 同步套餐
        """
        for food in food_list:
            set_food_detail_json = food.get("setFoodDetailJson")
            if not set_food_detail_json:
                continue
            if food.get("isSetFood") == "false":
                continue
            set_food_detail_json = json.loads(set_food_detail_json)
            dish_obj = dishes_dict.get(food.get("foodID"))
            if not dish_obj:
                continue
            set_food_list = set_food_detail_json.get("foodLst")
            child_dish_groups = {}
            while dish_obj.child_dish_groups:
                group = dish_obj.child_dish_groups.pop()
                child_dish_groups.update({group.group_name: group})
            for set_food in set_food_list:
                self.set_child_dish_groups(dishes_dict, dish_obj, set_food, child_dish_groups)

    def set_child_dish_groups(self, dishes_dict, dish_obj, set_food, child_dish_groups):
        items = set_food.get("items")
        group_name = set_food.get("foodCategoryName")
        g = child_dish_groups.get(group_name)
        if not g:
            group = dish_obj.child_dish_groups.add()
            group.group_name = group_name
            group.id = id_manager.generate_common_id()
        else:
            group = dish_obj.child_dish_groups.add()
            group.id = g.id
            group.group_name = g.group_name

        child_dishes = {c.name: c for c in group.child_dishes}
        dish_obj.type = dish_pb.Dish.COMBO_MEAL
        group.order_min = int(set_food.get("chooseCount"))
        group.order_max = int(set_food.get("chooseCount"))
        if group.order_min != 1:
            order_selection = self.get_set_food_number_range_from_group_name(group.group_name)
            if order_selection:
                group.order_min = order_selection.order_min
                group.order_max = order_selection.order_max
        for item in items:
            item_id = item.get("foodID")
            name = item.get("foodName")
            child_food = dishes_dict.get(item_id)
            if not child_food:
                continue
            child_dish = child_dishes.get(name)
            if not child_dish:
                child_dish = group.child_dishes.add()
                child_dish.id = child_food.id
            price = round(float(item.get("addPrice", 0)) * 100)
            child_dish.name = child_food.name
            child_dish.market_price = price
            child_dish.price = price
            child_dish.sort = child_food.sort

    def get_set_food_number_range_from_group_name(self, group_name):
        """ 哗啦啦的套餐不支持套餐的的数量范围选择,所以通过名字来做处理
        如组名为: 主食-3选2, 则处理之后 orderMax=3, orderMin=2
        """
        selection = None
        if '-' in group_name:
            selection = group_name.split("-")[-1]
        if '—' in group_name:
            selection = group_name.split("—")[-1]
        if selection is None:
            return None
        OrderSelection = namedtuple("OrderSelection", ["order_min", "order_max"])
        matches = self.set_food_group_name_pattern.findall(selection)
        if not matches:
            return None
        # matches = [('3','2')]
        order_max = max(int(matches[0][0]), int(matches[0][-1]))
        order_min = min(int(matches[0][0]), int(matches[0][-1]))
        return OrderSelection(order_min, order_max)

    def sync_dish_supply_condiments(self, dishes_dict, food_tags, food_list):
        """ 同步菜品的配料
        """
        if not food_tags:
            return
        food_tags = {food_tag.get("itemID"): food_tag for food_tag in food_tags}
        for food in food_list:
            batching_food_json = food.get("batchingFoodJson")
            if not batching_food_json:
                continue
            batching_food_json = json.loads(batching_food_json)
            food_third_party_dish_id = food.get("foodID")
            dish_obj = dishes_dict.get(food_third_party_dish_id)
            if not dish_obj:
                continue
            for f in batching_food_json:
                item_id = f.get("batchingFoodTagID")
                food_tag = food_tags.get(item_id)
                if not food_tag:
                    continue
                self.create_dish_supply_condiment(dishes_dict, dish_obj, food_tag)

    def create_dish_supply_condiment(self, dishes_dict, dish_obj, food_tag):
        food_ids = food_tag.get("foodIDs")
        food_ids = food_ids.split(",")
        saved_supply_condiments = dish_obj.supply_condiments
        saved_supply_condiments = {s.name: s for s in saved_supply_condiments}
        for food_id in food_ids:
            s_dish = dishes_dict.get(food_id)
            if not s_dish:
                continue
            name = s_dish.name
            s0 = saved_supply_condiments.get(name)
            if not s0:
                s0 = dish_obj.supply_condiments.add()
                s0.id = id_manager.generate_common_id()
            s0.name = name
            # s0.market_price = int(s_dish.price) * 100
            s0.market_price = int(s_dish.price)

    def sync_making_method_group_list(self, dish, dish_obj):
        """ 同步做法
        """
        making_method_group_list = dish.get("makingMethodGroupList")
        if not making_method_group_list:
            return
        if not making_method_group_list:
            return
        making_method_group_list = json.loads(making_method_group_list)
        for group in making_method_group_list:
            group_name = group.get("groupName")
            group_id = id_manager.generate_common_id()
            attrs = group.get("detailList")
            self.create_attrs(attrs, dish_obj.attrs, group_id, group_name)

    def sync_taste_group_list(self, dish, dish_obj):
        """ 同步口味
        """
        taste_group_list = dish.get("tasteGroupList")
        if not taste_group_list:
            return
        taste_group_list = json.loads(taste_group_list)

        for group in taste_group_list:
            group_name = group.get("groupName")
            group_id = id_manager.generate_common_id()
            attrs = group.get("detailList")
            self.create_attrs(attrs, dish_obj.attrs, group_id, group_name)

    def create_attrs(self, attrs, attrs_obj, group_id, group_name):
        _attrs = {"{}-{}".format(attr.name, attr.reprice): attr for attr in attrs_obj}
        for taste in attrs:
            name = taste.get("notesName")
            _attr = _attrs.get(name)
            if _attr:
                group_id = _attr.group_id
                break
        for taste in attrs:
            name = taste.get("notesName")
            reprice = round(float(taste.get("addPriceValue") or 0) * 100)
            key = "{}-{}".format(name, reprice)
            _attr = _attrs.get(key)
            if not _attr:
                _attr = attrs_obj.add()
                _attr.id = id_manager.generate_common_id()
                _attr.name = name
                _attr.type = dish_pb.Attr.TASTE
            _attr.group_id = group_id
            _attr.group_name = group_name
            _attr.reprice = reprice

    def get_dish_obj(self, third_party_dish_id):
        ordering_da = OrderingServiceDataAccessHelper()
        dish_obj = ordering_da.get_dish(
            third_party_dish_id=third_party_dish_id, merchant_id=self._manager.merchant.id)
        if not dish_obj:
            dish_obj = dish_pb.Dish()
            self._manager.set_dish_default_value(dish_obj)
            dish_obj.merchant_id = self._manager.merchant.id
            dish_obj.id = id_manager.generate_common_id()
        return dish_obj

    def set_dish_activation(self, dish, activation):
        params = {
            "groupID": self.group_id,
            "shopID": self.shop_id,
            "foodID": str(dish.third_party_dish_id),
            "isActive": str(activation),
            "header": {
                "traceID": id_manager.generate_common_id()
            }
        }
        route = HualalaConstants.DISH_ACTIVATION
        ret = self.try_post(route, params)
        if ret.flag:
            return ret.data
        return None

# -*- coding: utf-8 -*-
import json
import logging
import hashlib
import time
import requests

import proto.finance.wallet_pb2 as wallet_pb
from business_ops.ordering.printer_manager import PrinterManager
from business_ops.ordering.constants import KitchenPrinter, XPRINTER
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
import proto.ordering.registration_pb2 as registration_pb
from service import errors

logger = logging.getLogger(__name__)


class FeiePrinterManager(PrinterManager):
    """
    58mm 一行可打印32个非中文字符或16个中文
    58mm加粗 一行可打印16个非中文字符或8个中文
    80mm 一行可打印48个非中文字符或24个中文
    80mm加粗 一行可打印24个非中文字符或12个中文
    """

    def __init__(self, *args, merchant_id=None, merchant=None, registration_info=None, **kargs):
        self.merchant = merchant
        if not self.merchant:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

        super().__init__(merchant=merchant, merchant_id=merchant_id, *args, **kargs)

        self.registration_info = registration_info
        if self.registration_info is None:
            self.registration_info = OrderingServiceDataAccessHelper().get_registration_info(self.merchant.id)

        self.content = ""
        self.product_content = []
        self._refund_prefix = ""

    @property
    def refund_prefix(self):
        return self._refund_prefix

    @refund_prefix.setter
    def refund_prefix(self, p):
        self._refund_prefix = p

    def kitchen_print_add_product(self, order, products, table, newline="<BR>"):
        if not self.registration_info.printer_config.feie_printer.kitchen_print:
            logger.info("商户: {} 没有设置后厨打印".format(order.merchant_id))
            return
        category_products = {}
        for p in products:
            products = category_products.get(p.category_id, [])
            products.append(p)
            category_products.update({p.category_id: products})

        printer_sns = {}
        ordering_da = OrderingServiceDataAccessHelper()

        all_category = ordering_da.get_categories(self.merchant.id, orderby=[('sort', 1)])

        for category_id, ps in category_products.items():
            dish_category_printer = ordering_da.get_dish_category_printer(self.merchant.id, category_id)
            if dish_category_printer:
                for printer in dish_category_printer.printer_sns:
                    pssss = printer_sns.get(printer, [])
                    pssss.extend(ps)
                    printer_sns.update({printer: pssss})
            else:
                # 如果没有为这个分类设置打印机就用默认的打印机
                pssss = printer_sns.get(self.registration_info.printer_config.feie_printer.printer_sn, [])
                pssss.extend(ps)
                printer_sns.update({self.registration_info.printer_config.feie_printer.printer_sn: pssss})

        for printer_sn, products in printer_sns.items():
            self.kitchen_print(order, table, products, printer_sn, all_category)

    def kitchen_print_create_order(self, order, table):
        """创建订单时: 打印后厨单"""
        if not self.registration_info.printer_config.feie_printer.kitchen_print:
            return
        self.kitchen_print_add_product(order, order.products, table)

    def kitchen_dish_content(self, index, product, content):
        if not self.disable_kitchen_price_print:
            index_name = "{}.{}".format(index + 1, product.name)
            name = self.content_align(index_name, length=self.goods_name_length)
            quantity = self.content_align("{}".format(int(product.quantity)), length=self.goods_quantity_length)
            price = "{:.2f}".format(float(product.price) / 100)
            price = self.content_align(price, length=self.goods_price_length)
            content += "{}{}{}{}{}<BR>".format(self.bold_start_flag, name, quantity, price, self.bold_end_flag)
        else:
            index_name = "{}.{}".format(index + 1, product.name)
            length = self.goods_name_length + self.goods_price_length
            name = self.content_align(index_name, length=length)
            quantity = self.content_align("{}".format(int(product.quantity)), length=self.goods_quantity_length)
            content += "{}{}{}{}<BR>".format(self.bold_start_flag, name, quantity, self.bold_end_flag)

        content = self.kitchen_attr_content(product, content)
        content = self.kitchen_supply_condiment_content(product, content)
        return content

    def kitchen_attr_content(self, product, content):
        for prop in product.attrs:
            if not self.disable_kitchen_price_print:
                name = self.content_align(prop.name, length=self.attr_name_length)
                price = "{:.2f}".format(float(prop.reprice) / 100)
                price = self.content_align(price, length=self.attr_price_length)
                content += "{}{:2s}{}{}{}<BR>".format(self.bold_start_flag, "", name, price, self.bold_end_flag)
            else:
                length = self.attr_name_length + self.attr_price_length
                name = self.content_align(prop.name, length=length - 1)
                content += "{}{:2s}{}{}<BR>".format(self.bold_start_flag, "", name, self.bold_end_flag)
        return content

    def kitchen_supply_condiment_content(self, product, content):
        for supply_condiment in product.supply_condiments:
            if not self.disable_kitchen_price_print:
                name = self.content_align(supply_condiment.name, length=self.supply_condiment_name_length)
                length = self.supply_condiment_quantity_length
                quantity = self.content_align(str(supply_condiment.quantity), length=length)
                price = "{:.2f}".format(float(supply_condiment.market_price) / 100)
                price = self.content_align(price, length=self.supply_condiment_price_length)
                content += "{}{:2s}{}{}{}{}<BR>".format(self.bold_start_flag, "", name, quantity, price, self.bold_end_flag)
            else:
                length = self.supply_condiment_name_length
                name = self.content_align(supply_condiment.name, length=length)
                length = self.supply_condiment_quantity_length
                quantity = self.content_align(str(supply_condiment.quantity), length=length)
                content += "{}{:2s}{}{}{}<BR>".format(self.bold_start_flag, "", name, quantity, self.bold_end_flag)
        return content

    def kitchen_print(self, order, table, products, printer_sn, all_category):
        """飞鹅打印后厨单,小票机"""
        content = self.get_kitchen_print_header(table, order)
        # 是否带切刀
        cut = self.registration_info.printer_config.feie_printer.cut
        # 是否一菜一切
        one_dish_cut = self.registration_info.printer_config.feie_printer.one_dish_cut
        if self.registration_info.printer_config.feie_printer.enable_dish_sort:
            new_products = []
            for cate in all_category:
                for product in products:
                    if cate.id == product.category_id:
                        new_products.append(product)
            products = new_products
        for index, product in enumerate(products):
            content = self.kitchen_dish_content(index, product, content)
            if one_dish_cut:
                if cut:
                    if index != len(products) - 1:
                        content += "<BR><BR><BR><CUT>"
                        content += self.get_kitchen_print_header(table, order)
                else:
                    content += "<BR><BR>"
                    if index != len(products) - 1:
                        content += self.get_kitchen_print_header(table, order)

        self.product_content.append({printer_sn: content})
        logger.info('打印机参数: {}'.format(content))

    def get_kitchen_print_header(self, table, order):
        enable_serial_number = self.registration_info.printer_config.feie_printer.enable_kitchen_print_serial_number
        enable_meal_code = self.registration_info.printer_config.feie_printer.enable_kitchen_meal_code_print
        bold_kitchen_serial_number = self.registration_info.printer_config.feie_printer.bold_kitchen_serial_number
        content = self._get_table_info(
            table,
            "后厨总单",
            order=order,
            enable_serial_number=enable_serial_number,
            enable_meal_code=enable_meal_code,
            bold_kitchen_serial_number=bold_kitchen_serial_number,
        )
        if not self.disable_kitchen_price_print:
            shangpin = self.content_align("商品", length=self.goods_title_length * self.guest_font_ratio)
            shuliang = self.content_align("数量", length=self.quantity_title_length * self.guest_font_ratio)
            jiage = self.content_align("价格", length=self.price_title_length * self.guest_font_ratio)
            content += "{}{}{}<BR>".format(shangpin, shuliang, jiage)
        else:
            length = self.goods_title_length + self.price_title_length - 4
            shangpin = self.content_align("商品", length=length * self.guest_font_ratio)
            shuliang = self.content_align("数量", length=self.quantity_title_length * self.guest_font_ratio)
            content += "{}{}<BR>".format(shangpin, shuliang)
        return content

    def reception_print_add_product(self, order, products, table, newline="<BR>"):
        """加菜时: 打印前台单"""
        if not self.registration_info.printer_config.feie_printer.merchant_print:
            return
        self.content += self._get_table_info(table, header="用户加菜单")
        self.content += "{:5s}{:^5s}{:>5s}<BR>".format("商品", "数量", "价格(元)")
        self.content += self._build_product_content(products)

    def reception_print_create_order(self, transaction, order, table):
        """创建订单时: 打印前台单"""
        if not self.registration_info.printer_config.feie_printer.merchant_print:
            return
        products = order.products
        ordering_da = OrderingServiceDataAccessHelper()
        all_category = ordering_da.get_categories(self.merchant.id, orderby=[('sort', 1)])
        self.content += self._get_table_info(table, header="商户保留单", order=order)
        shangpin = self.content_align("商品", length=self.goods_title_length * self.guest_font_ratio)
        shuliang = self.content_align("数量", length=self.quantity_title_length * self.guest_font_ratio)
        jiage = self.content_align("价格(元)", length=self.price_title_length * self.guest_font_ratio)
        self.content += "{}{}{}<BR>".format(shangpin, shuliang, jiage)
        self.content += self._build_product_content(products, all_category)
        self.content += "<BR>"
        bill_fee = "%.2f" % (float(order.bill_fee) / 100)
        paid_in_fee = "%.2f" % (float(order.paid_in_fee) / 100)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "商品总价: {}元".format(bill_fee), self.bold_end_flag)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "实收金额: {}元".format(paid_in_fee), self.bold_end_flag)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "用餐人数: {}人".format(order.people_count), self.bold_end_flag)
        self.content += "{}{}{}".format(self.bold_start_flag, self.build_order_remark(order), self.bold_end_flag)
        self.content += "                                <BR>"

    def pay_success(self, order, transaction, table):
        """后付款模式支付成功单: 结账单"""
        if not self.registration_info.printer_config.feie_printer.check_out_print:
            return
        self.content += self._get_table_info(table, header="用户结账单")
        red_packet = RedPacketDataAccessHelper().get_red_packet(new_transaction_id=transaction.id)
        red_packet_value = red_packet.total_value if red_packet else 0
        bill_fee = "%.2f" % (float(order.bill_fee) / 100)
        discount_amount = "%.2f" % (float(order.discount_amount + red_packet_value) / 100)
        paid_in_fee = "%.2f" % (float(order.paid_in_fee) / 100)
        coupon_fee = "%.2f" % (float(order.coupon_fee) / 100)
        if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "卡包支付", self.bold_end_flag)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "商品总价: {}元".format(bill_fee), self.bold_end_flag)
        if transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "优惠金额: {}元".format(discount_amount), self.bold_end_flag)
        if transaction.use_coupon_id != "":
            self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "用券金额: {}元".format(coupon_fee), self.bold_end_flag)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "实收金额: {}元".format(paid_in_fee), self.bold_end_flag)
        self.content += "{}{:<}{}<BR>".format(self.bold_start_flag, "用餐人数: {}人".format(order.people_count), self.bold_end_flag)
        self.content += "{}{}{}".format(self.bold_start_flag, self.build_order_remark(order), self.bold_end_flag)

    def __call_printer(self, printer_sn, content, times=None):
        if content == "":
            return
        printer_config = self.printer_config.get(printer_sn)
        if not printer_config:
            return
        if times is None and printer_config:
            times = printer_config.print_times
        if times is None or times == 0:
            times = 1
        STIME = str(int(time.time()))
        if printer_config.type == registration_pb.PrinterConfigByType.Type.FEIE:
            signature = hashlib.sha1(str(KitchenPrinter.USER + KitchenPrinter.UKEY + STIME).encode('utf-8')).hexdigest()
            params = {
                'user': KitchenPrinter.USER,
                'sig': signature,
                'stime': STIME,
                'apiname': 'Open_printMsg',
                'sn': printer_sn,
                'content': content,
                'times': str(times),  # 打印联数
            }
            logger.info("飞鹅打印机content: {}".format(params))
            try:
                response = requests.post(KitchenPrinter.URL, data=params, timeout=30)
            except requests.exceptions.ReadTimeout as ex:
                logger.info("调用飞鹅打印机接口出错: {}".format(ex))
                raise errors.RequestsTimeoutError()
            logger.info("飞鹅打印机返回: {} {}".format(response.content.decode(), printer_sn))
        else:
            """调用芯烨打印机的服务"""
            if content == "":
                return
            if times is None or times == 0:
                times = 1
            STIME = str(int(time.time()))
            signature = hashlib.sha1(str(XPRINTER.USER + XPRINTER.UKEY + STIME).encode('utf-8')).hexdigest()
            params = {
                'user': XPRINTER.USER,
                'sign': signature,
                'timestamp': STIME,
                'sn': printer_sn,
                'content': "<L>" + content,
                'copies': str(times),  # 打印联数
                'mode': 1,
                'expiresIn': 43200,
            }
            logger.info("芯烨打印机参数: {}".format(params))
            try:
                response = requests.post(
                    XPRINTER.URL + "print",
                    data=json.dumps(params),
                    timeout=30,
                    headers={"Content-Type": "application/json;charset=UTF-8"},
                )
                logger.info(f"芯烨打印机返回: {response.content.decode()} {printer_sn}")
            except requests.exceptions.ReadTimeout as ex:
                logger.info("调用芯烨打印机接口出错ReadTimeout: {}".format(ex))
            except Exception as ex:
                logger.info("调用芯烨打印机接口出错CommonError: {}".format(ex))

    def print(self, choice_type=None):
        printer_sn = self.registration_info.printer_config.feie_printer.printer_sn
        content = self.entirety_bold_content(printer_sn, self.content)
        self.__call_printer(printer_sn, content, times=1)
        for printer_content in self.product_content:
            for printer in printer_content:
                content = printer_content.get(printer)
                content = self.entirety_bold_content(printer, content)
                self.__call_printer(printer, content)

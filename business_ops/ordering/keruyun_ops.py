# -*- coding: utf-8 -*-


"""
Filename: keruyun_ops.py
Date: 2020-06-17 11:43:51
Title: 客如云API公共父类
"""

import errno
import hashlib
import requests
import logging
from urllib import parse

from business_ops.ordering.constants import KeruyunConstants
from business_ops.base_manager import BaseManager
from common.utils import date_utils
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class KeruyunOPSManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(KeruyunOPSManager, self).__init__(*args, **kargs)

    def get(self, uri, shop_id=None, token=None, secret_key=None, json_body=None):
        params = {
            'appKey': KeruyunConstants.APP_KEY,
            'shopIdenty': shop_id,
            'version': KeruyunConstants.VERSION,
            'timestamp': date_utils.timestamp_second()
        }
        if token is not None:
            params.update({"token": token})
        if secret_key is not None:
            params.update({"secret_key": secret_key})
        common_params = self.add_signature(**params)
        params = '&'.join(['{}={}'.format(key, common_params[key]) for key in common_params])
        headers = {'Content-type': 'application/json'}
        url = parse.urljoin(KeruyunConstants.BASE_URL, uri + '?' + params)
        try:
            resp = requests.get(url, json=json_body, headers=headers)
        except Exception as e:
            if e.errno in (errno.ECONNRESET, errno.ECONNABORTED):
                return self.get(uri, token, secret_key, json_body)
            else:
                raise e
        return resp

    def post(self, uri=None, token=None, secret_key=None, json_body=None, try_times=5, timeout=5):
        if try_times == 0:
            logger.info("客如云接口调用失败: {}, {}".format(uri, json_body))
            raise errors.Error(err=error_codes.KERUYUN_POST_ERROR)
        shop_id = self._manager.registration_info.keruyun_pos_info.shop_id
        params = {
            'appKey': KeruyunConstants.APP_KEY,
            'shopIdenty': shop_id,
            'version': KeruyunConstants.VERSION,
            'timestamp': date_utils.timestamp_second()
        }
        if token is not None:
            params.update({"token": token})
        if secret_key is not None:
            params.update({"secret_key": secret_key})
        common_params = self.add_signature(**params)
        params = '&'.join(['{}={}'.format(key, common_params[key]) for key in common_params])
        headers = {'Content-type': 'application/json'}
        url = parse.urljoin(KeruyunConstants.BASE_URL, uri + '?' + params)
        logger.info("调用客如云接口: {}".format(url))
        try:
            resp = requests.post(url, json=json_body, headers=headers, timeout=timeout)
            logger.info("客如云post: {}".format(json_body))
        except requests.exceptions.ConnectTimeout as e:
            if try_times == 1:
                raise e
            logger.info("客如云接口超时,做一次20秒的重试: {}".format(params))
            return self.post(uri, token, secret_key, json_body, try_times=1, timeout=10)
        except requests.exceptions.ReadTimeout as e:
            if try_times == 1:
                raise e
            logger.info("客如云接口超时,做一次20秒的重试: {}".format(params))
            return self.post(uri, token, secret_key, json_body, try_times=1, timeout=10)
        except Exception as e:
            logger.info("客如云接口调用出错: {}".format(e))
            return self.post(uri, token, secret_key, json_body, try_times=try_times - 1)
        return resp

    def get_token(self, shop_id):
        """ 获取商户的token
            签名规则:
                1. appKey, shopIdenty, version, timestamp
                   ASCII顺序排列成与其值拼接在一起
                2. 第一步返回拼接后的值加上SECRET_KEY
                3. 第二步的返回值做hash256运算
            详情参考: https://open.keruyun.com/docs?fir=2&sec=5
        Args:
            merchant_id: (string)时来平台商户id
        Return:
            token: (string)客如云token
        """
        uri = KeruyunConstants.KERUYUN_TOKEN_URI
        resp = self.get(shop_id=shop_id, secret_key=KeruyunConstants.SECRET_KEY, uri=uri)
        json_resp = resp.json()
        logger.info("获取客如云token: {}".format(json_resp))
        if json_resp.get("code") == 0:
            token = json_resp.get("result").get("token")
            return token
        return None

    def add_signature(self, **kargs):
        """ 根据请求主体生成签名
        """
        result, request_json = '', {}
        for key in sorted(kargs.keys()):
            if key == 'token':
                continue
            if key == 'secret_key':
                continue
            request_json.update({key: kargs.get(key)})
            result += '{}{}'.format(key, request_json[key])
        if 'token' in kargs:
            result += kargs.get('token')
        if 'secret_key' in kargs:
            result += kargs.get('secret_key')

        request_json['sign'] = hashlib.sha256(result.encode('utf-8')).hexdigest()
        return request_json

# -*- coding: utf-8 -*-

import logging

from google.protobuf import json_format

import proto.ordering.keruyun.order_bell_pb2 as order_bell_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.base_order_manager import BaseOrderManager
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from common.utils import id_manager
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class KeruyunOrderManager(KeruyunOPSManager, BaseOrderManager):
    def __init__(self, *args, **kargs):
        super(KeruyunOrderManager, self).__init__(*args, **kargs)

    def get_order_detail(self, order_id):
        """ 从客如云获取订单详情
        """
        shop_id = self._manager.registration_info.keruyun_pos_info.shop_id
        json_body = {
            "shopIdenty": shop_id,
            "ids": [order_id]
        }
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_ORDER_EXPORT_DETAIL
        ret = self.post(uri=uri, token=token, json_body=json_body).json()
        logger.info("get_order_detail: {}".format(ret))
        if ret.get("code") == 0:
            return ret.get("result")
        return None

    def get_order_list(self, page_no, start_time, end_time, time_type=1):
        """ 从客如云获取订单详情
        """
        shop_id = self._manager.registration_info.keruyun_pos_info.shop_id
        json_body = {
            "shopIdenty": shop_id,
            "pageNo": page_no,
            "startTime": start_time,
            "endTime": end_time,
            "timeType": time_type
        }
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_ORDER_EXPORT
        ret = self.post(uri=uri, token=token, json_body=json_body).json()
        logger.info("keuryun get order list: {}".format(ret))
        if ret.get("code") == 0:
            return ret.get("result")
        return None

    def pos_order_bell(self, table, source, content, order=None):
        content_codes = [
            1,  # 催菜
            2,  # 倒茶
            3,  # 结账
            11,  # 其它
        ]
        source = order_bell_pb.OrderBell.Source.Value(source)
        bell = order_bell_pb.OrderBell()
        bell.table_id = table.ordering_service_table_id
        bell.table_name = table.name
        bell.table_area_id = table.table_area.area_id
        bell.table_area_name = table.table_area.area_name
        if order:
            bell.trade_id = order.ordering_service_trade_id
        bell.source = source
        bell.content_code = str(content_codes[source - 1])
        bell.content = content
        bell.uuid = id_manager.generate_common_id()
        json_body = json_format.MessageToDict(
            bell, including_default_value_fields=True, use_integers_for_enums=True)
        token = self._manager.registration_info.keruyun_pos_info.token
        uri = KeruyunConstants.KERUYUN_BELL
        resp = self.post(token=token, uri=uri, json_body=json_body).json()
        logger.info("客如云post返回: {}".format(resp))

    def initiate_ordering_refund(self, order, transaction_id, **kargs):
        if not order:
            raise errors.OrderNotFound()
        if order.status != dish_pb.DishOrder.PAID:
            raise errors.Error(err=error_codes.ORDER_NOT_PAID)
        return self._manager.pos_return_order(transaction_id=order.transaction_id)

    def order_status(self, **kargs):
        pass

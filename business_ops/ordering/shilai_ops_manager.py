# -*- coding: utf-8 -*-

import os
import json
import logging
from business_ops.base_manager import BaseManager
from business_ops.ordering.constants import ShilaiPosConstants, ShilaiMerchantConstants
from common.utils.log_utils import timeit
from common.utils.requests_utils import retry
from common import http

logger = logging.getLogger(__name__)


class ShilaiOPSManager(BaseManager):

    # 用户支付成功语音播报的消息类型
    PAY_SUCCESS = "PAY_SUCCESS"
    # 用户取消支付语音播报的消息类型
    CANCEL_PAY = "CANCEL_PAY"

    def get_domain(self):
        return self.__get_domain()

    @property
    def outer_version(self):
        outer_version = os.environ.get("OUTER_VERSION", "v1.6")
        return outer_version

    def __get_domain(self):
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == "test":
            return ShilaiPosConstants.TEST_URL
        return ShilaiPosConstants.BASE_URL

    def __get_payment_domain(self):
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == "test":
            return ShilaiPosConstants.TEST_URL
        return ShilaiPosConstants.PAYMENT_BASE_URL

    def generate_url(self, uri):
        domain = self.__get_domain()
        return "{}/{}/{}".format(domain, self.outer_version, uri)

    def generate_payment_url(self, uri):
        domain = self.__get_payment_domain()
        return "{}/{}/{}".format(domain, self.outer_version, uri)

    def generate_base_params(self):
        base_params = {
            "merchantId": self.merchant.id,
            "storeId": self.store.id,
            "key": ShilaiPosConstants.SECRET_KEY
        }
        return base_params

    @retry(times=2, delay=0.5)
    def do_post(self, url, params):
        params.update(self.generate_base_params())
        headers = {
            "token": ShilaiPosConstants.SECRET_KEY,
            "platform": "MINI_PROGRAM"
        }
        ret = http.post(url, json=params, headers=headers, timeout=30)
        return ret.json()

    def get_transaction_pagination(self, lastest_paid_time=None, size=10):
        endpoint = f'/{self.outer_version}/store/order/transaction/pagination'
        url = self.__get_domain() + endpoint
        params = {'size': size, 'merchantId': self.merchant.id}
        if lastest_paid_time:
            params['latestPayTime'] = lastest_paid_time
        response = http.post(url, json=params, timeout=60)
        return response.json().get('data', [])

    def get_transaction_summary(self, start_time, end_time):
        endpoint = f'/{self.outer_version}/store/order/transaction/summary'
        url = self.__get_domain() + endpoint
        params = {'endTime': end_time, 'merchantId': self.merchant.id, 'startTime': start_time}
        response = http.post(url, json=params, timeout=60)
        return response.json().get('data', {})

    @timeit
    def get_transaction_summary_2(self, start_time, end_time):
        endpoint = f'/{self.outer_version}/store/order/transaction/summary_v2'
        url = self.__get_domain() + endpoint
        params = {'endTime': end_time, 'merchantId': self.merchant.id, 'startTime': start_time}
        response = http.post(url, json=params, timeout=60)
        return response.json().get('data', {})

    @timeit
    def get_pos_shilai_orders(self, start_time, end_time):
        endpoint = f'/{self.outer_version}/store/order/transaction/pos_by_fanpiao'
        url = self.__get_domain() + endpoint
        params = {'endTime': end_time, 'merchantId': self.merchant.id, 'startTime': start_time}
        response = http.post(url, json=params, timeout=60)
        return response.json().get('data', [])

    def voice_broadcast(self, fee, type, prefix=None):
        """ 发起语音播报
        """
        try:
            uri = ShilaiPosConstants.VOICE_BROADCAST
            url = self.generate_url(uri)
            params = {
                "fee": fee,
                "type": type,
                "prefix": prefix
            }
            self.do_post(url, params)
        except Exception as ex:
            logger.exception(ex)


class ShilaiPosMerchantManager(BaseManager):
    TIMEOUT = 30

    class Endpoint(object):
        DISH_STATS = "/pm/merchant/insight/dish_stats"
        STRATEGY_STATS = "/pm/merchant/insight/strategy_stats"
        STORE_SETTING = "/pm/store/setting/single"
        STORE_MEMBER_SETTING = "/pm/store/membership/setting"
        REQUIRED_DISHES = "/pm/store/table/required/list"

    def __get_domain(self):
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == "test":
            return ShilaiMerchantConstants.TEST_URL
        return ShilaiMerchantConstants.BASE_URL

    @timeit
    @retry(times=2, delay=0.5)
    def post(self, endpoint, data=None):
        api = self.__get_domain() + endpoint
        logger.info(f"[*] Shilai Pos Merchant POST API: {api} {data}")
        resp = http.post(api, json=data, timeout=self.TIMEOUT)
        try:
            return resp.json().get("data", {})
        except Exception as e:
            logger.error(f"Shilai Pos Merchant Error Response: {resp.text}")
            raise e

    @timeit
    @retry(times=2, delay=0.5)
    def get(self, endpoint, params=None):
        api = self.__get_domain() + endpoint
        logger.info(f"[*] Shilai Pos Merchant GET API: {api} {params}")
        resp = http.get(api, params=params, timeout=self.TIMEOUT)
        try:
            return resp.json().get("data", {})
        except Exception as e:
            logger.error(f"Shilai Pos Merchant Error Response: {resp.text}")
            raise e

    @timeit
    def get_merchant_dish_stats(self, start_time, end_time):
        params = {'endTime': end_time, 'merchantId': self.merchant.id, 'startTime': start_time}
        return self.post(self.Endpoint.DISH_STATS, params)

    @timeit
    def get_merchant_strategy_stats(self, start_time, end_time):
        params = {'endTime': end_time, 'merchantId': self.merchant.id, 'startTime': start_time}
        return self.post(self.Endpoint.STRATEGY_STATS, params)

    @timeit
    def get_store_setting(self):
        params = {'merchantId': self.merchant.id}
        return self.post(self.Endpoint.STORE_SETTING, params)

    @timeit
    def update_store_setting(self, enable_mini_program_recharge=None, enable_mini_program_member_account_pay=None):
        params = {
            'merchantId': self.merchant.id, "enableMiniProgramRecharge": enable_mini_program_recharge,
            "enableMiniProgramMemberAccountPay": enable_mini_program_member_account_pay,
        }
        return self.post(self.Endpoint.STORE_MEMBER_SETTING, params)

    def get_required_dishes(self, merchant_id, store_id):
        params = {
            'merchantId': merchant_id,
            "storeId": store_id
        }
        return self.post(self.Endpoint.REQUIRED_DISHES, params)

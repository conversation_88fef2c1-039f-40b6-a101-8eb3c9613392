# -*- coding: utf-8 -*-

import base64
import time
import hashlib
import requests
import json
import logging
from collections import namedtuple
from urllib import parse

from Crypto.Cipher import AES

from business_ops.base_manager import BaseManager
from business_ops.ordering.constants import HualalaConstants
from common.utils.AES import AESCrypt
from common.utils import id_manager

logger = logging.getLogger(__name__)


class HualalaOPSManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(HualalaOPSManager, self).__init__(*args, **kargs)
        self.group_id = kargs.get("group_id", None)
        self.shop_id = kargs.get("shop_id", None)
        self.app_secret = HualalaConstants.APP_SECRET
        self.app_key = kargs.get("app_key", None)
        self.__init()
        self.aes_crypt = AESCrypt(self.app_secret + self.app_secret)
        self.header = {}
        self.header_params()
        self.must_params = {"timestamp": int(time.time() * 1000), "appKey": self.app_key, "version": 2.0}

    def __init(self):
        if self._manager.registration_info:
            pos_info = self._manager.registration_info.hualala_pos_info
            if pos_info.group_id != "" and pos_info.group_id != self.group_id:
                self.group_id = pos_info.group_id
            if pos_info.shop_id != "" and pos_info.shop_id != self.shop_id:
                self.shop_id = pos_info.shop_id
            if pos_info.app_secret != "" and pos_info.app_secret != self.app_secret:
                self.app_secret = pos_info.app_secret
            if pos_info.app_key != 0 and pos_info.app_key != self.app_key:
                self.app_key = pos_info.app_key

    def parse_api_response(self, response, data_name="data"):
        ParseApiResponse = namedtuple("ParseApiResponse", ["flag", "data"])
        data, flag = None, False
        result = response.get("result", None)
        code = response.get("code")
        if code and code != "000":
            return ParseApiResponse(flag=False, data={})
        if result is None:
            data = response.get(data_name)
            result = response.get("data").get("result")
        if result is None:
            result = response.get("data")
            if result is not None:
                result.update({"message": result.get("resultmsg"), "code": result.get("resultcode")})
        if result is None:
            data = response.get(data_name)
        if result is not None:
            message = result.get("message")
            code = result.get("code")
            msg = result.get("msg")
            if code == "000" and (message == "执行成功" or msg == "000"):
                flag = True
        return ParseApiResponse(flag=flag, data=data)

    def try_post(self, route, params=None, try_times=5):
        if try_times == 0:
            return None
        ret = self.__post(route, (params or {}).copy())
        if not ret:
            time.sleep(1)
            return self.try_post(route, params=params, try_times=try_times - 1)
        response = self.parse_api_response(ret)
        return response

    def __post(self, route, params):
        uri = HualalaConstants.BASE_URL
        url = "{}/{}".format(uri, route)
        params.update(self.must_params)
        params.update({"signature": self.add_signature(params)})
        params.update({"requestBody": self.aes_crypt.encrypt(json.dumps(params))})
        logger.info("调用花啦啦接口: {}, {}, {}".format(url, params, self.header))
        try:
            params = parse.urlencode(params)
            ret = requests.post(url, headers=self.header, data=params, timeout=30)
            ret_json = ret.json()
            logger.info("调用花啦啦接口返回: {}".format(ret_json))
            return ret_json
        except Exception as ex:
            logger.exception(ex)
            return None

    def header_params(self, shop_id=None):
        traceID = id_manager.generate_common_id()
        groupID = str(self.group_id)
        self.header = {"traceID": traceID, "groupID": groupID, "Content-Type": "application/x-www-form-urlencoded"}
        if shop_id is not None:
            self.header.update({"shopID": str(shop_id)})

    def is_basic_datatype(self, value):
        """是不是基础数据类型"""
        if isinstance(value, str):
            return True
        if isinstance(value, int):
            return True
        if isinstance(value, float):
            return True
        return False

    def flat_dict(self, params):
        ret = {}
        for key, value in params.items():
            if self.is_basic_datatype(value):
                ret.update({key: value})
            elif isinstance(value, dict):
                ret.update(self.flat_dict(value))
            elif isinstance(value, list):
                if len(value) == 0:
                    continue
                value = value[0]
                if isinstance(value, dict):
                    ret.update(self.flat_dict(value))
        return ret

    def add_signature(self, params, **kargs):
        """
        签名生成步骤：
        1) 请求公共参数timestamp,appKey,version参与签名,业务参数对象A(业务参数整体称为对象A)中的所有非空基本类型字段参与签名,
        复杂类型字段中的基本类型字段参与签名,复杂类型列表取列表中的第一个复杂类型对象中的基本类型字段参与签名
        2) 将所有参与签名字段按照参数名的字典顺序排序(忽略大小写)
        3) 将参数以参数1值1参数2值2...的顺序拼接,例如a=&c=3&b=1,变为b1c3
        4) 开发者秘钥以appSecret=xxx按第一步规则拼接到排序字符串中,例如a=&c=3&e=1,变为appSecretxxxc3e1
        5) 按key+排序后的参数+secret,得到加密前字符串
        6) 对加密前的字符串进行sha1加密并转成16进制编码,然后转为大写字符串,得到签名
        7) 将得到的签名赋给signature作为请求的参数
        """
        params = self.flat_dict(params)
        params.update({"appSecret": self.app_secret})
        s = ""
        keys = sorted(params.keys(), key=lambda x: x.lower())
        for key in keys:
            value = params.get(key)
            s += str(key) + str(value)
        s = "key" + s + "secret"
        logger.info("待签名字符串: {}".format(s))
        logger.info("traceID: {}".format(self.header.get("traceID")))
        s = s.encode("utf8")
        sha1 = hashlib.sha1()
        sha1.update(s)
        return sha1.hexdigest().upper()

    def aes_decrypt(self, body):
        """用于哗啦啦回调解密: 哗啦啦端的加密方式为:
        用AES算法加密,秘钥为appSecret重复两遍，偏移量为appSecret重复两遍(16位秘钥不用重复)，加密模式CBC，填充模式；NoPadding
        """
        BS = AES.block_size  # 这个等于16
        mode = AES.MODE_CBC

        key = "{}{}".format(self.app_secret, self.app_secret)
        vi = key
        pad = key + (BS - len(key)) * "\0"

        try:
            cryptor = AES.new(pad, mode, vi)
        except:
            cryptor = AES.new(pad.encode('utf8'), mode, vi.encode('utf8'))
        # 解密，解密后text文本会包含用来补全的字符
        plain_text = cryptor.decrypt(base64.b64decode(body))
        plain_text = plain_text.decode("utf8")
        plain_text = plain_text.strip("\x00")
        return json.loads(plain_text)

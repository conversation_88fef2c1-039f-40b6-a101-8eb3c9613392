# -*- coding: utf-8 -*-


from business_ops.base_manager import BaseManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
import proto.ordering.dish_pb2 as dish_pb


class BaseDishManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(BaseDishManager, self).__init__(*args, **kargs)

    def create_shilai_dish_category_template(self):
        category = {"merchantId": self._manager.merchant.id, "id": "", "name": "", "sort": "", "parentId": "", "level": 0}
        return category

    def sync_combo_dish(self, dish):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_dishes(dish.merchant_id, type=dish_pb.Dish.DishType.COMBO_MEAL)
        for item in dishes:
            need_update = False
            for child_group in item.child_dish_groups:
                for i, child_dish in enumerate(child_group.child_dishes):
                    if child_dish.id == dish.id:
                        need_update = True
                        if dish.status == dish_pb.Dish.Status.DISABLE:
                            child_group.child_dishes.pop(i)
                            if child_dish.is_must:
                                item.status = dish_pb.Dish.Status.GUQING
                            continue
                        child_group.child_dishes[i].name = dish.name
            if need_update:
                ordering_da.add_or_update_dish(dish=item)

# -*- coding: utf-8 -*-


import proto.ordering.registration_pb2 as registration_pb
from business_ops.ordering.keruyun_ops import KeruyunOPSManager


class KeruyunShopManager(KeruyunOPSManager):
    def __init__(self, shop_manager=None, *args, **kargs):
        self._manager = shop_manager
        super(KeruyunShopManager, self).__init__(*args, **kargs)

    def register(self, shop_id, registration_info=None, *args, **kargs):
        self.shop_id = shop_id
        token = self.get_token(shop_id=shop_id)
        if registration_info is None:
            registration_info = registration_pb.OrderingServiceRegistrationInfo()
            registration_info.merchant_id = self._manager.merchant.id
        registration_info.keruyun_pos_info.shop_id = shop_id
        if token is not None:
            registration_info.keruyun_pos_info.token = token
        return registration_info

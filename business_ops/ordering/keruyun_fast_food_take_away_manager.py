# -*- coding: utf-8 -*-

"""
Filename: keruyun_fast_food_take_away_manager.py
Date: 2020-08-25 13:31:23
Title: 客如云快餐外带
@deprecated
"""

import logging
import time

from google.protobuf import json_format

import proto.ordering.keruyun.order_pb2 as order_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.keruyun_ops import KeruyunOPSManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.ordering.constants import KeruyunConstants
from business_ops.ordering.printer_manager import PrinterManager
from common.utils import date_utils
from common.utils import distribute_lock
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import errors


logger = logging.getLogger(__name__)


class KeruyunFastFoodTakeAwayManager(OrderManager):

    def __init__(self, registration_info, *args, **kargs):
        super(KeruyunFastFoodTakeAwayManager, self).__init__(*args, **kargs)
        self.registration_info = registration_info
        self.keruyun_ops = KeruyunOPSManager(registration_info)
        self.merchant = MerchantDataAccessHelper().get_merchant(self.registration_info.merchant_id)
        self.merchant_id = self.merchant.id

        self.printer_manager = PrinterManager(merchant=self.merchant)

    def create_keruyun_order(self, order):
        ordering_da = OrderingServiceDataAccessHelper()
        fast_food_order = order_pb.FastFoodOrder()
        keruyun_pos_info = self.keruyun_ops.registration_info.keruyun_pos_info

        fast_food_order.create_time = int(time.time())
        fast_food_order.tp_order_id = order.id
        fast_food_order.shop_identy = keruyun_pos_info.shop_id
        fast_food_order.shop_name = keruyun_pos_info.shop_name
        fast_food_order.remark = order.remark
        fast_food_order.people_count = order.people_count
        fast_food_order.total_price = order.bill_fee
        fast_food_order.status = 2
        fast_food_order.print = 1
        if self.keruyun_ops.registration_info.printer_config.keruyun_printer.disable_fast_food_print:
            fast_food_order.print = 0

        # 桌台信息
        self.fill_table_info(order, fast_food_order)

        # 商品信息
        self.fill_products_info(order, fast_food_order)
        ordering_da.add_or_update_keruyun_fast_food_order(fast_food_order)

    def fill_table_info(self, order, keruyun_order):
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(ordering_service_table_id=order.table_id)
        table_info = keruyun_order.tables.add()
        table_info.table_id = int(table.ordering_service_table_id)
        table_info.table_name = table.name

    def fill_discount_details(self, order, keruyun_order):
        if order.discount_amount == 0:
            return
        discount_detail = keruyun_order.discount_details.add()
        discount_detail.discount_type = 3
        discount_detail.discount_fee = keruyun_order.payment.total_discount_fee

    def fill_snack_payment(self, order, keruyun_order, transaction):
        keruyun_order.payment.total_fee = transaction.bill_fee
        keruyun_order.payment.shop_fee = int(self._calculate_paid_in_fee(order, transaction))
        keruyun_order.payment.user_fee = keruyun_order.payment.shop_fee
        keruyun_order.payment.pay_type = 3
        keruyun_order.payment.total_discount_fee = order.bill_fee - (keruyun_order.payment.shop_fee)
        keruyun_order.payment.shop_discount_fee = keruyun_order.payment.total_discount_fee

    def fill_products_info(self, order, keruyun_order):
        for product in order.products:
            self.add_product(product, keruyun_order)

    def add_product(self, shilai_product, keruyun_order):
        product = keruyun_order.products.add()
        product.tp_id = shilai_product.id
        product.id = int(shilai_product.id)
        product.parent_uuid = shilai_product.parent_uuid
        product.uuid = shilai_product.uuid
        product.type = shilai_product.type
        product.name = shilai_product.name
        product.unit = shilai_product.unit
        product.price = shilai_product.price
        product.total_fee = shilai_product.total_fee
        product.quantity = shilai_product.quantity

        self.set_attrs(shilai_product, product)
        self.set_supply_condiments(shilai_product, keruyun_order, product)

    def set_attrs(self, shilai_product, keruyun_product):
        for shilai_attr in shilai_product.attrs:
            attr = keruyun_product.properties.add()
            attr.name = shilai_attr.name
            attr.id = int(shilai_attr.id)
            attr.type = shilai_attr.type
            attr.reprice = shilai_attr.reprice

    def set_supply_condiments(self, shilai_product, keruyun_order, parent_product):
        for supply_condiment in shilai_product.supply_condiments:
            product = keruyun_order.products.add()
            product.name = supply_condiment.name
            product.price = supply_condiment.market_price
            product.quantity = supply_condiment.quantity
            product.parent_uuid = parent_product.uuid
            product.total_fee = int(product.price * product.quantity)
            product.uuid = id_manager.generate_common_id()
            product.type = 2

    def __create_order(self, order, transaction, coupon_fee, try_times=3):
        if try_times == 0:
            raise errors.KeruyunCreateOrderError()
        ordering_service_da = OrderingServiceDataAccessHelper()
        keruyun_order = ordering_service_da.get_keruyun_fast_food_order(tp_order_id=order.id)

        self.fill_snack_payment(order, keruyun_order, transaction)
        # 优惠信息
        self.fill_discount_details(order, keruyun_order)
        keruyun_order.remark = self.printer_manager.build_order_remark(order, newline="\n")

        json_body = json_format.MessageToDict(keruyun_order, including_default_value_fields=True)
        # 向客如云发起订单
        logger.info("向客如云发起创建订单: {}".format(json_body))
        resp = self.keruyun_ops.post(
            token=self.keruyun_ops.token, uri=KeruyunConstants.KERUYUN_FAST_FOOD_ORDER, json_body=json_body).json()
        logger.info("客如云创建订单接口返回: {}, {}".format(resp, order.id))
        if resp.get("code") == 0:
            order.ordering_service_order_id = resp.get("result").get("orderId")
            order.ordering_service_trade_id = resp.get("result").get("tradeId")
            order.ordering_service_serial_number = resp.get("result").get("serialNumber")
            order.status = dish_pb.DishOrder.APPROVED
            order.approve_time = date_utils.timestamp_second()
            # ordering_service_da.add_or_update_order(no_check=True, order=order)
            return True
        else:
            logger.info('创建客如云订单失败,重试: {}, resp.code: {}'.format(order.id, resp.get('code')))
            return self.__create_order(order, transaction, coupon_fee, try_times=try_times - 1)
        return False

    def pay_order(self, order, transaction, coupon_fee=0):
        with distribute_lock.redislock(key=order.table_id, ttl=5000, retry_count=50, retry_delay=200) as lock:
            if not lock:
                raise errors.KeruyunPayOrderError()
            return self.__create_order(order, transaction, coupon_fee)
        logger.info('不能获取桌台锁: tableID: {}, order_id{}'.format(order.table_id, order.id))
        raise errors.KeruyunPayOrderError()

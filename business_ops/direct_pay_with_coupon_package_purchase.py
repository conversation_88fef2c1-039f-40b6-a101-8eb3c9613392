# -*- coding: utf-8 -*-


"""
码牌支付与券包同时合并使用
"""

import logging
import time

import proto.finance.wallet_pb2 as wallet_pb
import proto.coupons_pb2 as coupons_pb
from business_ops.business_manager import BusinessManager
from business_ops import merchant_store_manager
from business_ops.payment_manager import PaymentManager
from business_ops.transaction_manager import TransactionManager
from business_ops.direct_pay_manager import DirectPayManager
from business_ops.coupon_package_manager import CouponPackageManager
from cache.redis_client import RedisClient
from common.utils.number_utils import NumberUtils
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class DirectPayWithCouponPackagePurchaseManager(BusinessManager):

    def __init__(self, transaction_id=None, user_id=None, merchant_id=None,
                 bill_fee=None, paid_fee=None, pay_method=None, coupon_package_id=None,
                 no_discount_fee=None, coupon_fee=None, transaction=None, order_manager_v2=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.shipping_fee = 0
        self.coupon_fee = coupon_fee
        self.no_discount_fee = 0

        super().__init__(
            user_id=user_id, transaction_id=transaction_id, merchant_id=merchant_id,
            pay_method=pay_method, coupon_package_id=coupon_package_id, transaction=transaction)

        self.order_manager_v2 = order_manager_v2
        self.paid_fee = NumberUtils.safe_round(self.paid_fee)
        self.bill_fee = NumberUtils.safe_round(self.bill_fee)
        self.shipping_fee = NumberUtils.safe_round(self.shipping_fee)
        if self.coupon_package:
            self.coupon_fee = self.coupon_package.coupon_package_spec.reduce_cost
        self.redis_client = RedisClient().get_connection()

    def check_no_discount_time_ranges(self):
        ret = merchant_store_manager.is_no_discount_time_ranges(self.merchant)
        if ret.flag:
            raise errors.NoDiscountTimeRanges()

    def check_order_valid(self):
        pass

    def prepay(self):
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_direct_pay_with_coupon_package_purchase_transaction(
            payer_id=self.user.id, payee_id=self.merchant.id, pay_method=self.pay_method,
            bill_fee=self.bill_fee,
            paid_fee=self.paid_fee)

        result = payment_manager.prepay(
            transaction=self.transaction, coupon_package_id=self.coupon_package.id)
        logger.info("扫码点餐与券包聚合支付: {}".format(result))

        if result.get("errcode") == error_codes.SUCCESS:
            self.transaction.state = wallet_pb.Transaction.ORDERED
            transaction_manager.update_transaction(self.transaction)
            result["transactionId"] = self.transaction.id
        return result

    def notification(self, **kargs):
        coupons = None
        try:
            coupons = self.buy_coupon_package_notification()
            self.ordering_notification(coupons)
        except Exception as e:
            logger.exception("券包扫码点餐合并支付出错: {}".format(e))
            # 删除券包
            if coupons is not None:
                self.delete_coupons(coupons)
            raise e
        self.deal_with_ordering_coupon_package_union_pay_transaction()
        self.deal_with_union_pay_transaction()

    def delete_coupons(self, coupons):
        if coupons is None:
            return
        coupon_da = CouponDataAccessHelper()
        coupon_package = coupon_da.get_coupon_package(self.buy_coupon_package_transaction.id)
        coupon_package.status = coupons_pb.CouponPackage.DELETED
        coupon_da.add_or_update_coupon_package(coupon_package)
        for coupon in coupons:
            coupon.state = coupons_pb.Coupon.DELETED
            coupon_da.update_or_create_coupon(coupon)

    def buy_coupon_package_notification(self):
        """ 购买券包
        """
        transaction_manager = TransactionManager()
        coupon_package_sell_price = self.coupon_package.coupon_package_spec.sell_price
        self.buy_coupon_package_transaction = transaction_manager.handle_buy_coupon_package_prepay(
            user_id=self.user.id, merchant_id=self.merchant.id, pay_method=self.transaction.pay_method,
            fee=coupon_package_sell_price)
        manager = CouponPackageManager(
            merchant=self.merchant, transaction=self.buy_coupon_package_transaction,
            coupon_package=self.coupon_package)
        manager.generate_coupon_package()
        coupons = manager.notification(union_pay=True)
        return coupons

    def ordering_notification(self, coupons):
        transaction_manager = TransactionManager()
        store_id = "{}_0".format(self.merchant.id)
        coupon_package_sell_price = self.coupon_package.coupon_package_spec.sell_price
        order_bill_fee = self.transaction.bill_fee - coupon_package_sell_price
        order_paid_fee = self.transaction.paid_fee - coupon_package_sell_price
        self.ordering_transaction = transaction_manager.create_direct_pay_transaction(
            payer_id=self.user.id, payee_id=self.merchant.id, pay_method=self.transaction.pay_method,
            payee_store_id=store_id, bill_fee=order_bill_fee, paid_fee=order_paid_fee)
        self.ordering_transaction.use_coupon_id = coupons[0].id
        manager = DirectPayManager(
            transaction=self.ordering_transaction,
            merchant=self.merchant,
            coupon_id=coupons[0].id,
            bill_fee=self.ordering_transaction.bill_fee,
            order_manager_v2=self.order_manager_v2)
        self.order = manager.notification(union_pay=True)

    def deal_with_ordering_coupon_package_union_pay_transaction(self):
        """ 保存 聚合支付,扫码点餐支付,券包支付 三个transactionId
        """
        transaction_da = TransactionDataAccessHelper()
        transaction = wallet_pb.OrderingCouponPackageUnionPayTransaction()
        transaction.transaction_id = self.transaction.id
        transaction.ordering_transaction_id = self.ordering_transaction.id
        transaction.buy_coupon_package_transaction_id = self.buy_coupon_package_transaction.id
        transaction_da.add_or_update_ordering_coupon_package_union_pay_transaction(transaction)

    def deal_with_union_pay_transaction(self):
        """ 执行分账
        """
        if self.transaction.ledgered:
            return
        transaction_manager = TransactionManager()
        payment_manager = PaymentManager(pay_method=self.transaction.pay_method, merchant=self.merchant)
        payment_manager.ordering_coupon_package_union_pay_launch_ledger(
            self.transaction, self.ordering_transaction, self.order,
            self.buy_coupon_package_transaction)
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = int(time.time())
        transaction_manager.update_transaction(self.transaction)

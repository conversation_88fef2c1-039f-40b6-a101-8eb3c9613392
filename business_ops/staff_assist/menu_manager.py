# -*- coding: utf-8 -*-

from datetime import datetime

import proto.staff_pb2 as staff_pb
from base_cls import BaseCls
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.staff_assist import MenuDataAccessHelper
from common.utils import id_manager
from service import errors


class MenuManager(BaseCls):

    # 增
    A_CREATE = 1
    # 查
    A_RETRIEVE = 1 << 1
    # 改
    A_UPDATE = 1 << 2
    # 删
    A_DELETE = 1 << 3

    # 创建新的权限
    CREATE = "create"
    # 根据ID更新权限
    UPDATE = "update"
    # 获取权限列表
    GET_LIST = "get-list"

    def do_operate(self, **kargs):
        menu = None
        menus = None
        if self.operation == self.CREATE:
            menu = self.create_menu_obj()
            self.update_menu(menu, **kargs)
        elif self.operation == self.UPDATE:
            if self.menu is None:
                raise errors.ShowError("菜单不存在")
            self.update_menu(self.menu, **kargs)
            menu = self.menu
        elif self.operation == self.GET_LIST:
            menus = self.get_menus()
        else:
            raise errors.ShowError("操作不支持")
        if menu is not None:
            self.add_or_update_menu(menu)
        to_dict = kargs.get("to_dict", True)
        if menu:
            if to_dict:
                return self.to_dict(menu)
            return menu
        if menus:
            if to_dict:
                return self.batch_to_dict(menus)
            return menus

    @property
    def menu(self):
        if self._menu is not None:
            return self._menu
        if self.menu_id is None:
            return None
        self._menu = self.get_menu(self.menu_id)
        return self._menu

    @property
    def merchant_user_da(self):
        if self._merchant_user_da is not None:
            return self._merchant_user_da
        self._merchant_user_da = MerchantUserDataAccessHelper()
        return self._merchant_user_da

    @property
    def menu_da(self):
        if self._menu_da is not None:
            return self._menu_da
        self._menu_da = MenuDataAccessHelper()
        return self._menu_da

    @property
    def user(self):
        if self._user:
            return self._user
        self._user = self.merchant_user_da.get_user(self.user_id)
        return self._user

    def create_menu_obj(self):
        obj = staff_pb.StaffAssistMenu()
        obj.id = id_manager.generate_common_id()
        now = datetime.now()
        obj.create_time = int(now.timestamp())
        return obj

    def update_menu(self, menu, **kargs):
        if menu is None:
            return
        self.__update_menu_name(menu, kargs.get("name"))
        parent_menu_id = kargs.get("parent_menu_id")
        if parent_menu_id is not None:
            menu.parent_menu_id = parent_menu_id
        self.__update_menu_authorities(menu, kargs.get("authorities"))

    def __update_menu_name(self, menu, name):
        if name is None:
            return
        menu_by_name = self.get_menu(name=name)
        if menu_by_name is not None:
            if menu.id != menu_by_name.id:
                raise errors.ShowError("菜单名不能重复")
        menu.name = name

    def __update_menu_authorities(self, menu, authorities):
        if authorities is None:
            return
        for role, authority in authorities.items():
            if isinstance(role, int):
                role = staff_pb.ShilaiStaff.Role.Name(role)
            menu.authorities[role] = authority

    def add_or_update_menu(self, menu):
        if menu is None:
            return
        now = datetime.now()
        menu.update_time = int(now.timestamp())
        self.menu_da.add_or_update_menu(menu)

    def get_menu(self, id=None, name=None):
        menu = self.menu_da.get_menu(id=id, name=name)
        return menu

    def get_menus(self):
        menus = self.menu_da.get_menus()
        return menus

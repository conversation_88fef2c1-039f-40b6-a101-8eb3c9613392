# -*- coding: utf-8 -*-

import copy
import logging
import time

import proto.ordering.dish_pb2 as dish_pb
import proto.ordering.supply_condiment_pb2 as addon_pb
from business_ops.base_manager import BaseManager
from common.utils import id_manager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.ordering.dish_supply_condiment_da_helper import DishSupplyCondimentDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class DishAddonManager(BaseManager):
    CREATE_ADDON = "CREATE_ADDON"
    UPDATE_ADDON = "UPDATE_ADDON"
    REORDER_ADDONS = "REORDER_ADDONS"

    ADD_ADDON_TO_DISHES = "ADD_ADDON_TO_DISHES"
    UPDATE_ADDON_IN_DISHES = "UPDATE_ADDON_IN_DISHES"
    REMOVE_ADDON_FROM_DISHES = "REMOVE_ADDON_FROM_DISHES"

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._operation = kargs.get("operation")
        self._order_da = None
        self._addon_da = None
        self._addons = None
        self._addon_map_by_name = None
        self._addon_map_by_id = None
        self._dishes = None
        self._need_update_addons = {}
        self._need_update_dishes = {}

    @property
    def operation(self):
        return self._operation

    @property
    def order_da(self):
        if self._order_da is not None:
            return self._order_da
        self._order_da = OrderingServiceDataAccessHelper()
        return self._order_da

    @property
    def addon_da(self):
        if self._addon_da is not None:
            return self._addon_da
        self._addon_da = DishSupplyCondimentDataAccessHelper()
        return self._addon_da

    @property
    def dishes(self):
        if self._dishes is not None:
            return self._dishes
        self._dishes = dict()
        dishes = self.order_da.get_all_dishes(merchant_id=self.merchant.id, nocache=True)
        for dish in dishes:
            self._dishes.update({dish.id: dish})
        return self._dishes

    @property
    def addons(self):
        if self._addons is not None:
            return self._addons
        self._addons = self.addon_da.get_supply_condiments(merchant_id=self.merchant.id)
        return self._addons

    def __add_or_update_addon(self, addon):
        self.addon_da.add_or_update_supply_condiment(addon)

    def __add_or_update_dish(self, dish):
        self.order_da.add_or_update_dish(dish=dish)

    def __get_dish(self, dish_id, raise_error=False):
        dish = self.dishes.get(dish_id)
        if dish is not None:
            return dish
        if not dish and raise_error:
            raise errors.ShowError("菜品不存在")
        return dish

    def __map_addon(self):
        if None not in (self._addon_map_by_id, self._addon_map_by_name):
            return
        self._addon_map_by_id = dict()
        self._addon_map_by_name = dict()
        for addon in self.addons:
            self._addon_map_by_id.update({addon.id: addon})
            self._addon_map_by_name.update({addon.name: addon.id})

    def __get_addon_id_by_name(self, name):
        if name is None:
            return None
        self.__map_addon()
        id = self._addon_map_by_name.get(name)
        return self.__get_addon_by_id(id)

    def __get_addon_by_id(self, id, raise_error=False):
        if id is None:
            return None
        self.__map_addon()
        addon = self._addon_map_by_id.get(id)
        if addon is None and raise_error:
            raise errors.ShowError("加料不存在")
        return addon

    def __check_addon_exists(self, name, addon=None, raise_error=False):
        exists_addon = self.__get_addon_id_by_name(name)
        if exists_addon and addon and exists_addon.market_price == addon.market_price and raise_error:
            raise errors.ShowError(f"创建加料出错: {name} 已存在")

    def __create_addon_obj(self):
        addon = addon_pb.DishSupplyCondiment()
        addon.id = id_manager.generate_common_id()
        return addon

    def __update_addon_obj(self, addon, **kargs):
        if addon is None:
            return
        market_price = kargs.get("market_price")
        if market_price is not None:
            addon.market_price = market_price
        name = kargs.get("name")
        if name is not None:
            self.__check_addon_exists(name, addon=addon, raise_error=True)
            addon.name = name
        status = kargs.get("status")
        if status is not None:
            if isinstance(status, str):
                status = dish_pb.SupplyCondiment.Status.Value(status)
            if status == dish_pb.SupplyCondiment.OUT_OF_STOCK:
                addon.name += f'(已删除-{int(time.time())})'
            addon.status = status
        self.__update_need_update_addons(addon)

    def __update_dish_addon(self, dish, addon, dish_addon):
        msg = f"""
        更新菜品加料前: {dish.name} {dish.id}
        加料ID: {dish_addon.id} {addon.id}
        加料名: {dish_addon.name} {addon.name}
        价格: {dish_addon.market_price} {addon.market_price}
        排序: {dish_addon.sort} {addon.sort}
        """
        logger.info(msg)
        dish_addon.id = addon.id
        dish_addon.name = addon.name
        dish_addon.market_price = addon.market_price
        dish_addon.status = addon.status
        dish_addon.sort = addon.sort

    def __add_addon_to_dish(self, addon, dish):
        if None in (addon, dish):
            return
        dish_addon = dish.supply_condiments.add()
        self.__update_dish_addon(dish, addon, dish_addon)
        self.__update_need_update_dishes(dish)

    def __update_addon_in_dish(self, addon, dish):
        for dish_addon in dish.supply_condiments:
            if dish_addon.id != addon.id:
                continue
            self.__update_dish_addon(dish, addon, dish_addon)
            self.__update_need_update_dishes(dish)
            break

    def __update_addon_in_all_dishes(self, addon):
        for dish_id, dish in self.dishes.items():
            self.__update_addon_in_dish(addon, dish)

    def __remove_addon_from_dish(self, addon, dish):
        for dish_addon in dish.supply_condiments:
            if addon.id != dish_addon.id:
                continue
            dish.supply_condiments.remove(dish_addon)
            self.__update_need_update_dishes(dish)
            break

    def __update_need_update_dishes(self, dish):
        self._need_update_dishes.update({dish.id: dish})

    def __update_need_update_addons(self, addon):
        self._need_update_addons.update({addon.id: addon})

    def add_addon_to_dishes(self, addon_id, dish_ids):
        addon = self.__get_addon_by_id(addon_id, raise_error=True)
        for dish_id in dish_ids:
            dish = self.__get_dish(dish_id, raise_error=True)
            self.__add_addon_to_dish(addon, dish)

    def update_addon_in_dishes(self, addon_id, dish_ids):
        addon = self.__get_addon_by_id(addon_id, raise_error=True)
        for dish_id in dish_ids:
            dish = self.__get_dish(dish_id, raise_error=True)
            self.__update_addon_in_dish(addon, dish)

    def remove_addon_in_dishes(self, addon_id, dish_ids):
        addon = self.__get_addon_by_id(addon_id, raise_error=True)
        for dish_id in dish_ids:
            dish = self.__get_dish(dish_id, raise_error=True)
            self.__remove_addon_from_dish(addon, dish)

    def create_addon(self, **kargs):
        addon = self.__create_addon_obj()
        self.__update_addon_obj(addon, **kargs)

    def update_addon(self, addon_id, **kargs):
        addon = self.__get_addon_by_id(addon_id, raise_error=True)
        if addon:
            addon = copy.deepcopy(addon)
        self.__update_addon_obj(addon, **kargs)
        self.__update_addon_in_all_dishes(addon)
        self._need_update_addons.update({addon.id: addon})
        return addon

    def reorder_addons(self, sort_array):
        for index, addon_id in enumerate(sort_array):
            addon = self.__get_addon_by_id(addon_id, raise_error=True)
            addon.sort = index
            self.__update_addon_in_all_dishes(addon)
            self._need_update_addons.update({addon.id: addon})

    def operate(self, request):
        name = request.json.get("name", None)
        market_price = request.json.get("marketPrice", None)
        sort_array = request.json.get("sortArray", None)
        status = request.json.get("status", None)
        addon_id = request.json.get("addonId", None)
        dish_ids = request.json.get("dishIds", None)
        ret = None
        if self.operation == self.CREATE_ADDON:
            ret = self.create_addon(name=name, market_price=market_price)
        elif self.operation == self.UPDATE_ADDON:
            ret = self.update_addon(addon_id, name=name, market_price=market_price, status=status)
        elif self.operation == self.ADD_ADDON_TO_DISHES:
            self.add_addon_to_dishes(addon_id, dish_ids)
        elif self.operation == self.UPDATE_ADDON_IN_DISHES:
            self.update_addon_in_dishes(addon_id, dish_ids)
        elif self.operation == self.REMOVE_ADDON_FROM_DISHES:
            self.remove_addon_in_dishes(addon_id, dish_ids)
        elif self.operation == self.REORDER_ADDONS:
            self.reorder_addons(sort_array)
        for dish_id, dish in self._need_update_dishes.items():
            self.__add_or_update_dish(dish)
        for addon_id, addon in self._need_update_addons.items():
            self.__add_or_update_addon(addon)
        return ret

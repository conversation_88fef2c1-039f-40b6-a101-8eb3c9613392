# -*- coding: utf-8 -*-

import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.group_dining.group_dining_pb2 as group_dining_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
from business_ops.business_manager import BusinessManager
from business_ops.payment_manager import PaymentManager
from business_ops.transaction_manager import TransactionManager
from business_ops.group_dining_manager import GroupDiningManager
from common.utils import date_utils
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.group_dining_da_helper import GroupDiningDataAccessHelper
from dao.invitation_da_helper import InvitationDataAccessHelper
from dao.user_da_helper import <PERSON>r<PERSON>ata<PERSON>cc<PERSON><PERSON><PERSON>per
from service import errors
from service import error_codes
from wechat_lib import subscribe_message_send

logger = logging.getLogger(__name__)


class GroupDiningPaymentManager(BusinessManager):
    def __init__(self, merchant_id=None, user_id=None, pay_method=None, bill_fee=None, paid_fee=None,
                 transaction_id=None, user_cnt=0, dining_id=None, no_discount_bill_fee=0, coupon_id=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.user_cnt = user_cnt
        self.no_discount_bill_fee = no_discount_bill_fee

        super(GroupDiningPaymentManager, self).__init__(
            transaction_id=transaction_id, user_id=user_id, merchant_id=merchant_id, coupon_id=coupon_id, dining_id=dining_id)

        self.paid_fee = self.round_off(self.paid_fee)
        self.bill_fee = self.round_off(self.bill_fee)
        self.no_discount_bill_fee = self.round_off(self.no_discount_bill_fee)

    def prepay(self, ordering=False):
        """ 饭局支付的prepay
        """
        if self.dining.state == group_dining_pb.GroupDiningEvent.PAID:
            raise errors.GroupDiningAlreadyPaid()

        if self.user_id != self.dining.director_id:
            # 2. 不是局长不能付款
            raise errors.NotDirectorCannotPay()

        self.transaction = TransactionManager().handle_group_dining_payment_prepay(
            dining=self.dining, pay_method=self.pay_method, bill_fee=self.bill_fee, paid_fee=self.paid_fee,
            coupon_id=self.coupon_id)

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 采用微信支付
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction)
            logger.info("饭局{}微信prepay成功".format(self.dining.id))
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            # 采用钱包支付
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction)
            logger.info("饭局{}使用钱包支付成功".format(self.dining.id))
        else:
            raise errors.PayMethodNotSupport()
        if result.get('errcode') == error_codes.SUCCESS:
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                # 1. 更新transaction状态为ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.WALLET:
                # 使用钱包支付成功,直接调用notification
                self.handle_group_dining_payment_notification()
            result.update({"transactionId": self.transaction.id})
        return result

    def notification(self):
        """ 约饭支付的回调函数
        Args:
            transaction: (wallet_pb.Transaction)结构体
        Return:
            transaction: (wallet_pb.Transaction)结构体
        """

        has_red_packet = False
        if not self.dining:
            return
        if self.dining.state != group_dining_pb.GroupDiningEvent.SCHEDULED:
            return

        policy = self.__calculate_policy()
        if policy and not self.coupon_id:
            red_packet_value = int(policy.red_packet_value)
            has_red_packet = self.generate_red_packet(
                red_packet_value=red_packet_value, issue_scene=red_packet_pb.RedPacket.GROUP_DINING)
        # 更新红包状态为可领取
        RedPacketDataAccessHelper().update_red_packet_status(
            transaction_id=self.transaction.id, status=red_packet_pb.RedPacket.NORMAL)

        # 1. 设置买单人的Invitation表中的transaction_id
        # 2. 设置饭局相关的所有invitation的state与monetary_state
        self.__set_invitation_info(has_red_packet)

        # 1. 更新transaction的状态为成功
        # 2. 更新transaction的支付时间
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        TransactionManager().update_transaction(self.transaction)

        # 1. 更新饭局的状态为成功
        # 2. 更新饭局相关的transaction_id
        GroupDiningDataAccessHelper().update(
            dining_id=self.dining.id, state=group_dining_pb.GroupDiningEvent.PAID,
            transaction_id=self.transaction.id)
        invitation_da = InvitationDataAccessHelper()
        invitees = invitation_da.get_invitations(dining_id=self.dining.id, state=group_dining_pb.Invitation.ACCEPTED)
        if self.dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            for invitee in invitees:
                if invitee.invitee_id == self.user_id:
                    # 局长不用收到消息通知
                    continue
                user = UserDataAccessHelper().get_user(invitee.invitee_id)
                fee = GroupDiningManager.calculate_transfer_bill_fee(self.dining_id, self.transaction)
                subscribe_message_send.waiting_for_pay_subscribe_message(
                    user.wechat_profile.openid, fee, self.transaction.id, self.store.name, self.dining.id)

        if self.dining.payment_rule == group_dining_pb.GroupDiningEvent.ALL_SHARING:
            logger.info('给局长发饭局完成的订阅消息,这样局长可以再次进入到饭局账单页')
            subscribe_message_send.pay_success_subscribe_message(
                self.user.wechat_profile.openid, self.transaction.paid_fee,
                self.store.name, self.transaction.id, self.dining_id)
        self.consume_coupon()

    def __set_invitation_info(self, has_red_packet):
        """ 设置局长invitation表中的状态
        Args:
            dining: 饭局
            transaction: 交易
        Return:
            无
        """
        invitation_da = InvitationDataAccessHelper()
        if has_red_packet:
            # 如果有生成红包
            if self.dining.payment_rule in [
                    group_dining_pb.GroupDiningEvent.ALL_SHARING,
                    group_dining_pb.GroupDiningEvent.SPELL_SPLIT]:
                # 如果是AA局或者拼单局
                # 更新饭局其它人的monetary_state为局长己买单,等待转账
                invitation_da.update(dining_id=self.dining_id,
                                     monetary_state=group_dining_pb.Invitation.TRANSFER_PENDING)
            else:
                if self.order:
                    # 1. 请客局,直接更新为等待开红包
                    invitation_da.update(dining_id=self.dining_id,
                                         monetary_state=group_dining_pb.Invitation.RED_PACKET_PENDING)
                else:
                    # 如果是扫码点餐的请客局,就只有局长可以领取红包
                    pass
            # 无论如何,买单人的monetary_state状态设置为可领红包
            invitation_da.update(dining_id=self.dining_id,
                                 user_id=self.dining.director_id,
                                 transaction_id=self.transaction.id,
                                 monetary_state=group_dining_pb.Invitation.RED_PACKET_PENDING)
        else:
            # 如果没有生成红包
            if self.dining.payment_rule in [group_dining_pb.GroupDiningEvent.ALL_SHARING,
                                            group_dining_pb.GroupDiningEvent.SPELL_SPLIT]:
                # AA或者拼单
                # 其它人设置为待转账
                invitation_da.update(dining_id=self.dining.id,
                                     monetary_state=group_dining_pb.Invitation.TRANSFER_PENDING)
            # 局长设置为己完成
            invitation_da.update(dining_id=self.dining.id,
                                 user_id=self.dining.director_id,
                                 transaction_id=self.transaction.id,
                                 monetary_state=group_dining_pb.Invitation.COMPLETED)

    def __calculate_policy(self):
        """ 根据支付的钱和用户数,计算应该使用哪个policy
        """
        if self.user_cnt <= 1:
            return None
        policies = self.dining.group_dining_coupon_policies
        for policy in reversed(policies):
            if policy.target_diner_count > 1 and \
               policy.least_cost <= self.bill_fee and \
               self.user_cnt >= policy.target_diner_count:
                return policy
        return None

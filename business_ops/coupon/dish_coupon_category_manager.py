# -*- coding: utf-8 -*-

import logging

import proto.coupon_category_pb2 as coupon_category_pb
import proto.wechat_common_pb2 as wechat_common_pb
from business_ops.coupon.base_coupon_category_manager import BaseCouponCategoryManager
from common.utils import id_manager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class DishCouponCategoryManager(BaseCouponCategoryManager):

    def __init__(self, *args, **kargs):
        super(DishCouponCategoryManager, self).__init__(*args, **kargs)

    def generate_coupon_category(self, dish_id=None, dish_brand_id=None, get_limit=9999, use_limit=9999, *args, **kargs):
        if dish_id is None and dish_brand_id is None:
            raise errors.Error(err=error_codes.DISH_COUPON_CATEGORY_DISH_ID_CONNOT_BE_NONE)
        base_info_dict = kargs.get("base_info")
        type = kargs.get("type")
        title = kargs.get("title")

        coupon_category = self.try_to_create_dish_coupon_category(dish_id, dish_brand_id)
        coupon_category.merchant_id = self.merchant.id
        coupon_category.brand_id = self.merchant.brand_info.id
        coupon_category.issue_scene = coupon_category_pb.CouponCategory.DISH
        coupon_category.coupon_type = wechat_common_pb.DISH
        coupon_category_spec = self.get_coupon_category_spec(coupon_category)
        if dish_id is not None:
            coupon_category_spec.dish_id = dish_id
        if dish_brand_id is not None:
            coupon_category_spec.dish_brand_id = dish_brand_id
        self.init_coupon_category_base_info(coupon_category_spec, title, get_limit, use_limit)
        self.init_date_info(coupon_category_spec.base_info, type, base_info_dict)
        self.init_dish_info(coupon_category_spec, dish_id, dish_brand_id)
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_category_da.add_or_update_coupon_category(coupon_category)
        return coupon_category

    def init_dish_info(self, coupon_category_spec, dish_id, dish_brand_id):
        ordering_da = OrderingServiceDataAccessHelper()
        dish = ordering_da.get_dish(dish_id=dish_id, merchant_id=self.merchant.id)
        if not dish:
            dish = ordering_da.get_dish(dish_brand_id=dish_brand_id, merchant_id=self.merchant.id)
        if not dish:
            raise errors.Error(err=error_codes.DISH_COUPON_CATEGORY_DISH_NOT_EXISTS)
        coupon_category_spec.dish_id = dish.id
        coupon_category_spec.dish_brand_id = dish.dish_brand_id

    def try_to_create_dish_coupon_category(self, dish_id, dish_brand_id):
        """ 创建菜品券.同一家商户,同一个菜品ID的菜品券类型只能有一个.所以会先把以前的都设置成为INACTIVE状态
        然后再创建一个新的菜品券
        """
        coupon_category_da = CouponCategoryDataAccessHelper()
        state = coupon_category_pb.CouponCategory.ACTIVE
        coupon_categories = coupon_category_da.get_dish_coupon_categories(
            merchant_id=self.merchant.id, dish_id=dish_id, dish_brand_id=dish_brand_id, state=state)
        if coupon_categories:
            for coupon_category in coupon_categories:
                coupon_category.state = coupon_category_pb.CouponCategory.INACTIVE
                coupon_category_da.add_or_update_coupon_category(coupon_category)
        coupon_category = coupon_category_pb.CouponCategory()
        coupon_category.id = id_manager.generate_common_id()
        return coupon_category

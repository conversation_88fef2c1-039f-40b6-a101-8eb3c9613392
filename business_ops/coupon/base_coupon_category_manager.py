# -*- coding: utf-8 -*-

import time

import proto.coupon_category_pb2 as coupon_category_pb
from business_ops.base_manager import BaseManager
from common.utils import date_utils


class BaseCouponCategoryManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(BaseCouponCategoryManager, self).__init__(*args, **kargs)
        coupon_category = kargs.get("coupon_category")
        self.coupon_category = coupon_category

    def generate_coupon_category(self, *args, **kargs):
        pass

    def init_coupon_category_base_info(self, coupon_category_spec, title, get_limit, use_limit):
        coupon_category_spec.base_info.logo_url = self.merchant.basic_info.logo_url
        coupon_category_spec.base_info.brand_name = self.merchant.basic_info.display_name
        coupon_category_spec.base_info.title = title
        coupon_category_spec.base_info.get_limit = get_limit
        coupon_category_spec.base_info.use_limit = use_limit
        coupon_category_spec.create_time = int(time.time())

    def init_date_info(self, base_info, type, base_info_dict):
        if type == "DATE_TYPE_FIX_TIME_RANGE":
            self.create_date_type_fix_time_range_date_info(base_info, base_info_dict)
        elif type == "DATE_TYPE_FIX_TERM":
            self.create_date_type_fix_term_date_info(base_info, base_info_dict)
        else:
            self.create_date_type_permanent_date_info(base_info)
        if base_info_dict.get("endTime"):
            base_info.date_info.end_time = base_info_dict.get("end_time")

    def create_date_type_fix_time_range_date_info(self, base_info, base_info_dict):
        date_info = base_info_dict.get("dateInfo")
        base_info.date_info.type = "DATE_TYPE_FIX_TIME_RANGE"
        now = int(time.time())
        end_timestamp = now + date_utils.ONE_DAY * 30
        base_info.date_info.begin_timestamp = date_info.get("beginTimestamp", now)
        base_info.date_info.end_timestamp = date_info.get("endTimestamp", end_timestamp)

    def create_date_type_fix_term_date_info(self, base_info, base_info_dict):
        date_info = base_info_dict.get("dateInfo")
        base_info.date_info.type = "DATE_TYPE_FIX_TERM"
        base_info.date_info.fixed_begin_term = date_info.get("fixedBeginTerm")
        base_info.date_info.fixed_term = date_info.get("fixedTerm")

    def create_date_type_permanent_date_info(self, base_info):
        base_info.date_info.type = "DATE_TYPE_PERMANENT"

    def get_coupon_category_spec(self, coupon_category=None):
        """获取CouponCategory具体的配置信息
        """
        if coupon_category is None:
            coupon_category = self.coupon_category
        if not coupon_category:
            return None
        issue_scene = coupon_category.issue_scene
        if issue_scene == coupon_category_pb.CouponCategory.INVITE_SHARE:
            return coupon_category.invite_share_coupon_spec
        if issue_scene == coupon_category_pb.CouponCategory.DISH:
            return coupon_category.dish_coupon_spec
        return None

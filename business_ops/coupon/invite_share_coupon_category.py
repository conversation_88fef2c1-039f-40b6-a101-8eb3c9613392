# -*- coding: utf-8 -*-

import logging

import proto.coupon_category_pb2 as coupon_category_pb
import proto.wechat_common_pb2 as wechat_common_pb
from business_ops.coupon.base_coupon_category_manager import BaseCouponCategoryManager
from common.utils import id_manager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper

logger = logging.getLogger(__name__)


class InviteShareCouponCategoryManagery(BaseCouponCategoryManager):

    def __init__(self, *args, **kargs):
        super(InviteShareCouponCategoryManagery, self).__init__(*args, **kargs)

    def generate_coupon_category(self, get_limit=9999, use_limit=9999, *args, **kargs):
        user_type = kargs.get("user_type", "NEW_USER")
        user_type = coupon_category_pb.InviteShareCouponCategory.UserType.Value(user_type)
        least_cost = kargs.get("least_cost")
        reduce_cost = kargs.get("reduce_cost") or 0
        if least_cost == 0 or reduce_cost == 0:
            return None
        base_info_dict = kargs.get("base_info")
        type = kargs.get("type")
        title = kargs.get("title")
        self.inactive_old_invite_share_coupon_category(user_type=user_type)
        coupon_category = coupon_category_pb.CouponCategory()
        coupon_category.id = id_manager.generate_common_id()
        coupon_category.merchant_id = self.merchant.id
        coupon_category.brand_id = self.merchant.brand_info.id
        coupon_category.issue_scene = coupon_category_pb.CouponCategory.INVITE_SHARE
        coupon_category.coupon_type = wechat_common_pb.INVITE_SHARE
        coupon_category_spec = self.get_coupon_category_spec(coupon_category)
        coupon_category_spec.least_cost = least_cost
        coupon_category_spec.reduce_cost = reduce_cost
        coupon_category_spec.user_type = user_type
        self.init_coupon_category_base_info(coupon_category_spec, title, get_limit, use_limit)
        self.init_date_info(coupon_category_spec.base_info, type, base_info_dict)
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_category_da.add_or_update_coupon_category(coupon_category)
        return coupon_category

    def inactive_old_invite_share_coupon_category(self, user_type=None):
        state = coupon_category_pb.CouponCategory.ACTIVE
        issue_scene = coupon_category_pb.CouponCategory.INVITE_SHARE
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_categories = coupon_category_da.get_coupon_categories(
            merchant_id=self.merchant.id, state=state, issue_scene=issue_scene)
        if not coupon_categories:
            return
        for coupon_category in coupon_categories:
            coupon_spec = coupon_category.invite_share_coupon_spec
            if coupon_spec.user_type != user_type:
                continue
            coupon_category.state = coupon_category_pb.CouponCategory.INACTIVE
            coupon_category_da = CouponCategoryDataAccessHelper()
            coupon_category_da.add_or_update_coupon_category(coupon_category)

# -*- coding: utf-8 -*-

import time

import proto.coupon_category_pb2 as coupon_category_pb
from business_ops.base_manager import BaseManager
from business_ops.coupon.dish_coupon_category_manager import DishCouponCategoryManager
from business_ops.coupon.invite_share_coupon_category import InviteShareCouponCategoryManagery
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper


class CouponCategoryManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(CouponCategoryManager, self).__init__(*args, **kargs)

        self.init_coupon_category_manager(*args, **kargs)

    def init_coupon_category_manager(self, *args, **kargs):
        coupon_category_id = kargs.get("coupon_category_id")
        coupon_category = kargs.get("coupon_category")
        if coupon_category is not None:
            coupon_category = coupon_category
        if coupon_category_id is not None:
            coupon_category_da = CouponCategoryDataAccessHelper()
            coupon_category = coupon_category_da.get_coupon_category(id=coupon_category_id)
        if not coupon_category:
            return
        issue_scene = coupon_category.issue_scene
        if issue_scene == coupon_category_pb.CouponCategory.IssueScene.INVITE_SHARE:
            self._manager = InviteShareCouponCategoryManagery(
                coupon_category=coupon_category, merchant=self.merchant)
        elif issue_scene == coupon_category_pb.CouponCategory.IssueScene.DISH:
            self._manager = DishCouponCategoryManager(
                coupon_category=coupon_category, merchant=self.merchant)

    def create_coupon_category(self, issue_scene, *args, **kargs):
        issue_scene = coupon_category_pb.CouponCategory.IssueScene.Value(issue_scene)
        if issue_scene == coupon_category_pb.CouponCategory.IssueScene.INVITE_SHARE:
            return InviteShareCouponCategoryManagery(merchant=self.merchant).generate_coupon_category(*args, **kargs)
        elif issue_scene == coupon_category_pb.CouponCategory.IssueScene.DISH:
            return DishCouponCategoryManager(merchant=self.merchant).generate_coupon_category(*args, **kargs)
        return None

    def get_coupon_category_expire_time(self, coupon_category=None):
        coupon_spec = self._manager.get_coupon_category_spec(coupon_category)
        date_info = coupon_spec.base_info.date_info
        if date_info.type == "DATE_TYPE_FIX_TIME_RANGE":
            return self.get_fix_time_range_coupon_category_expire_time(date_info)
        elif date_info.type == "DATE_TYPE_FIX_TERM":
            return self.get_fix_term_coupon_category_expire_time(date_info)

        if date_info.end_time:
            return date_info.end_time
        return -1

    def get_fix_time_range_coupon_category_expire_time(self, date_info):
        return date_info.end_timestamp

    def get_fix_term_coupon_category_expire_time(self, date_info):
        if date_info.fixed_begin_term == 0:
            start_timestamp = int(time.time())
        else:
            start_timestamp = int(time.time()) + date_utils.ONE_DAY * date_info.fixed_begin_term
        end_timestamp = start_timestamp + date_info.fixed_term * date_utils.ONE_DAY
        return end_timestamp

import os
from common import constants

WECHAT_MEMBER_CARD_CATEGORY_OUTER_STRING = 'shilai_member_card'

DATA_PATH = '/data'
IMAGES_PATH = '/data/images'

# 保存商户二维码路径
MERCHANT_ID_QRCODE_SAVE_DIR = '{}/qrcode_images'.format(IMAGES_PATH)
# 保存上传的静态图片的路径
STATIC_IMAGES_DIR = '{}/static_images'.format(IMAGES_PATH)
# 下载图片文件请求路径
REQUEST_IMAGE_URL = "{}/image/".format(os.environ[constants.SERVICE_DOMAIN_ENV_NAME])
# 保存上传或生成的文件的路径
DATA_FILES_DIR = '{}/files'.format(DATA_PATH)
# 下载数据文件请求路径
REQUEST_FILE_URL = "{}/files/".format(os.environ[constants.SERVICE_DOMAIN_WITHOUT_VERSION_ENV_NAME])
# iFeedU投喂功能海报生成
IFEEDU_POSTER_IMAGES_DIR = '{}/ifeedu/posters'.format(IMAGES_PATH)
# 临时上传目录
TEMP_UPLOAD_DIR = '{}/temp_images'.format(IMAGES_PATH)
# 菜品图片压缩包存放位置
DISH_IMAGE_COMPRESS_PACKAGE = "{}/dish-images".format(DATA_PATH)

# 生成标准logo图片使用字体所在路径
FONT_DIR = '{}/characters'.format(IMAGES_PATH)
# 生成标准logo图片存储路径
LOGO_IMAGE_SAVE_DIR = '{}/logo'.format(IMAGES_PATH)

# 生成商户首页图片存储路径
MERCHANT_PAGE_SAVE_DIR = '{}/merchant_page'.format(IMAGES_PATH)

SHILAI_MCH_ID = "1530024931"

# 支付宝网关
ALIPAY_GATEWAY = 'https://openapi.alipay.com/gateway.do'

# H5相关配置
# 支付宝appid
ALIPAY_APPID = '2021001102610646'
# 支付宝公钥
ALIPAY_PUBLIC_KEY_FILE = '/data/alipay-keys/alipay-public-key.pem'
# 应用私钥
ALIPAY_SHILAI_PRIVATE_KEY_FILE = '/data/alipay-keys/shilai-private-key.pem'

# 小程序相关配置
# 小程序appid
ALIPAY_MINIPROGRAM_APPID = '2021002121604365'
# 支付宝根证书路径
ALIPAY_MINIPROGRAM_ROOT_CRT_FILE = "/data/alipay-miniprogram-keys/alipay-root.crt"
# 小程序支付宝公钥证书
ALIPAY_MINIPROGRAM_PUBLIC_CRT_FILE = "/data/alipay-miniprogram-keys/alipay-public.crt"
# 小程序应用公钥证书路径
ALIPAY_MINIPROGRAM_SHILAI_CRT_FILE = "/data/alipay-miniprogram-keys/shilai-miniprogram.crt"
# 小程序支付宝公钥
ALIPAY_MINIPROGRAM_PUBLIC_KEY_FILE = "/data/alipay-miniprogram-keys/alipayCertPublicKey_RSA2.cer"
# 小程序应用私钥
ALIPAY_MINIPROGRAM_SHILAI_PRIVATE_KEY = "/data/alipay-miniprogram-keys/shilai-private-key.pem"
# 小程序 AES 加解密密钥
ALIPAY_MINIPROGRAM_AES_SECRET_KEY = "q5DBTacp747UgHxCEQU45w=="

# 天阙支付 start ##################################
TIAN_QUE_PAY_DOMAIN = 'https://openapi.tianquetech.com'
TIAN_QUE_PAY_ORGID = '15966157'
TIAN_QUE_PAY_SHILAI_MNO = '399220125979509'
TIAN_QUE_PAY_PUBLIC_KEY_FILE = '/data/tian-que-keys/tian-que-public-key.pem'
TIAN_QUE_PAY_SHILAI_PUBLIC_KEY_FILE = '/data/tian-que-keys/shilai-public-key.pem'
TIAN_QUE_PAY_SHILAI_PRIVACE_KEY_FILE = '/data/tian-que-keys/shilai-private-key.pem'
# 微信支付分配的渠道商商户号
TIAN_QUE_PAY_CHANNEL_ID = "392709909"
# 时来用于分账收款的商户编号
TIAN_QUE_PAY_SHILAI_LAUNCH_LEDGER_MNO = "399220125979509" # '399200910874210'
TIAN_QUE_PAY_SHILAI_COMMISSION_RATE = 0.0023
# 天阙支付 end ##################################

# 银联支付 start ################################
CHINAUMS_WECHAT_PAY_DOMAIN = 'https://qr.chinaums.com/netpay-route-server/api/'
CHINAUMS_WECHAT_PAY_SHILAI_MID = "898102173920101"
CHINAUMS_WECHAT_PAY_SHILAI_TID = "AA000009"
CHINAUMS_WECHAT_PAY_MSG_SRC = "WWW.SHZYKEJI.COM"
CHINAUMS_WECHAT_PAY_MSG_SRC_ID = "10KV"
CHINAUMS_WECHAT_PAY_SECRET_KEY = "AnJEYG6kBsZpNYkftGJ3wJ6wpE2Qn6WNCznNcP7FBZHPPpnx"
# 银联支付 end ##################################

import logging
import urllib

import proto.merchant_rules_pb2 as merchant_rules_pb2
import proto.membership_pb2 as membership_pb
import proto.membership_service_pb2 as membership_service_pb
import proto.wechat_member_card_pb2 as wechat_member_card_pb2
import proto.coupon_category_pb2 as coupon_category_pb
import proto.ordering.keruyun.keruyun_member_card_balance_pb2 as keruyun_member_card_balance_pb
import proto.ordering.registration_pb2 as registration_pb
from business_ops import constants
from business_ops import member_card_manager
from business_ops.coupon_manager import CouponManager
from business_ops.brand_dish_verification_code_manager import BrandDishVerificationCodeManager
from business_ops.shilai_pos_member_manager import ShilaiPosMemberManager
from business_ops.shilai_member_card_pay_manager import ShilaiMemberCardPayManager
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from common.config import config
from common.utils import date_utils
from common.utils import id_manager
from wechat_lib import member_card_api_helper
from common.utils.db_utils import *

logger = logging.getLogger(__name__)


class MemberCardManager(object):
    def __init__(self):
        pass

    def get_card_category_for_user(self, user_id, merchant_id):
        card_categories = MembershipDataAccessHelper().get_member_card_categories_for_merchant(merchant_id)
        if not card_categories:
            return None
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            return None

        # 若用户在时来平台上已存在，则返回支持一键开卡的会员卡类型
        auto_activate = True if user.HasField('member_profile') else False
        for card_category in card_categories:
            if auto_activate == card_category.wechat_card.member_card.auto_activate:
                return card_category

        return None

    def create_member_card(self, user_id, card_category_id, card_id, brand_id=None):
        """根据指定用户及商户会员卡信息，创建一条MemberCard记录。
        Args:
            user_id: (string) 用户ID
            card_category_id: (string) 会员卡类型ID
            card_id: (string) 会员卡ID
        """
        # TODO: 因为Android系统的技术性原因，会导致同一个激活会员卡请求被多次提交，因此这里添加了去重逻辑。
        #       日后需再看看有没有更合理的处理方案。
        if MembershipDataAccessHelper().get_member_card_by_id(card_id):
            return

        merchant = MerchantDataAccessHelper().get_merchant_for_card_category(card_category_id)
        if merchant:
            member_card = membership_pb.MemberCard()
            member_card.id = card_id
            member_card.user_id = user_id
            member_card.merchant_id = merchant.id
            member_card.card_category_id = card_category_id
            member_card.issue_time = date_utils.timestamp_second()
            member_card.initial_activate_time = date_utils.timestamp_second()
            member_card.last_activate_time = date_utils.timestamp_second()
            if brand_id is not None:
                member_card.brand_id = brand_id
            MembershipDataAccessHelper().add_member_card(member_card)

    def issue_brand_dish_coupon_if_not_brand_member(self, user_id, merchant_id):
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        brand_id = merchant.brand_info.id
        member_card = MembershipDataAccessHelper().get_member_card(user_id=user_id, brand_id=brand_id)
        if member_card:
            return brand_id
        BrandDishVerificationCodeManager(user_id=user_id, merchant_id=merchant_id).issue_new_member_brand_dish_coupon(
            user_id, merchant_id, brand_id
        )
        return brand_id

    def create_member_card_for_user_if_not_member(self, user_id, merchant_id, brand_id=None):
        """之前会员卡只和merchantId相关联.有的多个商户属于同一个品牌,会员卡就与brandId相关联.
        但是一个用户只能与一个brandId相关联,同时会员卡余额也放在这个有brandId的数据中
        """
        is_member = MembershipDataAccessHelper().is_member(user_id, merchant_id)
        if not is_member and user_id:
            membership_da = MembershipDataAccessHelper()
            member_card_category = membership_da.get_member_card_categories_for_merchant(
                merchant_id=merchant_id, card_type="MEMBER_CARD"
            )
            if not member_card_category:
                member_card_manager.create_default_member_card(merchant_id, True)
                member_card_manager.create_default_member_card(merchant_id, False)

            # 如果 brand_id不是None,则需要检查用户是不是有与这个brand_id相关联的会员卡
            if brand_id is not None:
                member_card = membership_da.get_member_card(user_id=user_id, brand_id=brand_id)
                if member_card:
                    brand_id = None

            card_id = id_manager.generate_card_id()
            if len(member_card_category) > 0:
                # 生成会员卡
                MemberCardManager().create_member_card(user_id, member_card_category[0].id, card_id, brand_id=brand_id)
                logger.info('为{}生成{}会员卡'.format(user_id, merchant_id))
                # 为该用户创建一张新会员优惠券
                CouponManager().issue_coupons_to_user(merchant_id, user_id, coupon_category_pb.CouponCategory.NEW_MEMBER)

    def get_user_member_card_balance(self, user_id, merchant_id):
        user_da = UserDataAccessHelper()
        user = user_da.get_user(user_id=user_id)
        if not user:
            return 0
        manager = ShilaiMemberCardPayManager(merchant_id=merchant_id, user_id=user_id)
        member_card = manager.get_member_card_with_balance(user_id=user_id, user=user)
        phone = user.member_profile.mobile_phone
        if phone:
            membership_da = MembershipDataAccessHelper()
            keruyun_member_card_balance = membership_da.get_keruyun_member_card_balance(
                phone, merchant_id, status=keruyun_member_card_balance_pb.KeruyunMemberCardBalance.UNMERGE
            )
            if keruyun_member_card_balance:
                member_card.keruyun_member_card_balance = keruyun_member_card_balance.balance
                member_card.balance += keruyun_member_card_balance.balance
                membership_da.update_or_create_member_card(member_card)
                keruyun_member_card_balance.status = keruyun_member_card_balance_pb.KeruyunMemberCardBalance.MERGED
                membership_da.add_or_update_keruyun_member_card_balance(keruyun_member_card_balance)
                logger.info("合并 user: {}, merchant: {} 客如云卡余额到时来会员卡余额".format(user.id, merchant_id))
        if not member_card:
            return 0
        return member_card.balance

    def sync_user_member_card_balance(self, user_id):
        """把会员卡账户余额同步到pos端,并清空小程序端的储值余额"""
        membership_da = MembershipDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        user_da = UserDataAccessHelper()
        member_cards = membership_da.get_member_cards(user_id=user_id)
        user = user_da.get_user(user_id)
        if not user.member_profile.mobile_phone:
            return
        for member_card in member_cards:
            merchant_id = member_card.merchant_id
            registration_info = ordering_da.get_registration_info(merchant_id=merchant_id)
            if not registration_info:
                continue
            if not (registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI):
                continue
            balance = member_card.balance
            member_card.balance = 0
            manager = ShilaiPosMemberManager(merchant_id=member_card.merchant_id)
            manager.increase_member_card_balance_by_balance(balance, user.member_profile.mobile_phone, user.id)
            membership_da.update_or_create_member_card(member_card)
            logger.info("用户首次授权手机号,同步会员账户余额: {} {} {}".format(user_id, merchant_id, balance))

    def sync_merchant_member_card_balance(self, merchant_id, user_id=None):
        """用于把商户所有用户的储值余额同步到pos端
        :param user_id: 如果有传用户ID则只同步这个用户的余额到pos端
        """
        ordering_da = OrderingServiceDataAccessHelper()
        membership_da = MembershipDataAccessHelper()
        user_da = UserDataAccessHelper()
        registration_info = ordering_da.get_registration_info(merchant_id=merchant_id)
        if not (registration_info.pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI):
            return
        member_cards = membership_da.get_member_cards(merchant_id=merchant_id)
        for member_card in member_cards:
            if user_id is not None and member_card.user_id != user_id:
                continue
            user = user_da.get_user(member_card.user_id)
            if not user.member_profile.mobile_phone:
                continue
            balance = member_card.balance
            member_card.balance = 0
            manager = ShilaiPosMemberManager(merchant_id=member_card.merchant_id)
            manager.increase_member_card_balance_by_balance(balance, user.member_profile.mobile_phone, user.id)
            membership_da.update_or_create_member_card(member_card)
            logger.info("商户切换到时来pos系统,同步会员账户余额: {} {} {}".format(user.id, merchant_id, balance))


def get_wechat_member_card_base_info(merchant):
    """
    Args:
        merchant: (merchant_rules_pb2.Merchant)

    Returns:
      一个CreateMemberCardCategoryResponse信息结构体。
    """
    base_info = wechat_member_card_pb2.WechatMemberCardBaseInfo()

    # 如果是时来子商户，则表示按第三方代制模式制券
    # https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1451025292
    if merchant.join_method == merchant_rules_pb2.SHILAI_MP_SUBMERCHANT:
        base_info.sub_merchant_info.merchant_id = merchant.shilai_mp_submerchant_info.id

    # 是	 string(128)	卡券的商户logo，建议像素为300*300。
    # http://mmbiz.qpic.cn/mmbiz/iaL1LJM1mF9aRKPZ/0
    base_info.logo_url = merchant.basic_info.logo_url

    # 是	string	商户名字,字数上限为12个汉字。
    # "海底捞",
    base_info.brand_name = merchant.basic_info.name

    # 是	string(16)	Code展示类型，
    # "CODE_TYPE_TEXT" 文本
    # "CODE_TYPE_BARCODE" 一维码
    # "CODE_TYPE_QRCODE" 二维码
    # "CODE_TYPE_ONLY_QRCODE" 仅显示二维码
    # "CODE_TYPE_ONLY_BARCODE" 仅显示一维码
    # "CODE_TYPE_NONE" 不显示任何码型
    # "CODE_TYPE_TEXT",
    base_info.code_type = "CODE_TYPE_QRCODE"

    # 是 string	卡券名，字数上限为9个汉字 (建议涵盖卡券属性、服务及金额)。
    # "海底捞会员卡",
    base_info.title = "会员卡"

    # 券颜色。按色彩规范标注填写Color010-Color100
    if merchant.preferences.HasField('design_config') and merchant.preferences.design_config.color:
        base_info.color = merchant.preferences.design_config.color
    else:
        base_info.color = 'Color090'

    # 是	string	卡券使用提醒，字数上限为16个汉字。
    # "使用时向服务员出示此券",
    base_info.notice = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（24）	客服电话
    # "020-88888888",
    # base_info.service_phone = "https://mmbiz.qlogo.cn/mmbiz/"

    # 是	 string	卡券使用说明，字数上限为1024个汉字。
    # "不可与其他优惠同享"
    base_info.description = "不可与其他优惠同享"

    # 是 JSON 使用日期，有效期的信息。
    # 是	string	使用时间的类型 支持固定时长有效类型 固定日期有效类型 永久有效类型( DATE_TYPE_PERMANENT)
    # "DATE_TYPE_PERMANENT"
    base_info.date_info.type = "DATE_TYPE_PERMANENT"

    # begin_timestamp	否	int	type为DATE_TYPE_FIX_TIME_RANGE时专用， 表示起用时间。从1970年1月1日00:00:00至起用时间的秒数 （ 东八区时间,UTC+8，单位为秒 ）
    # base_info.date_info.begin_timestamp = date_utils.timestamp_second()
    # end_timestamp	否	int	type为DATE_TYPE_FIX_TERM_RANGE时专用，表示结束时间 （ 东八区时间,UTC+8，单位为秒 ）
    # base_info.date_info.begin_timestamp = date_utils.timestamp_second()

    # 商品信息。
    # 是	int	卡券库存的数量，不支持填写0，上限为100000000。
    # 200
    base_info.sku.quantity = 99999999
    if merchant.join_method == merchant_rules_pb2.SHILAI_MP_SUBMERCHANT:
        # 时来公众号子商户单个卡券种类的 sku 限制为 10000 张
        base_info.sku.quantity = 10000

    # 否	int	每人可领券的数量限制，建议会员卡每人限领一张
    # 3
    base_info.get_limit = 1

    # 否	bool	是否自定义Code码。填写true或false，默认为false 通常自有优惠码系统的开发者选择自定义Code码，详情见 是否自定义code
    # false
    # base_info.use_custom_code = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	bool	卡券是否可转赠,默认为true
    # true
    # base_info.can_give_friend = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	array	门店位置ID。调用 POI门店管理接口 获取门店位置ID。
    # [123, 12321]
    # base_info.location_id_list = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（15）	自定义跳转外链的入口名字。
    # "立即使用",
    # base_info.custom_url_name = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（128）	自定义跳转的URL。
    #  "http://weixin.qq.com",
    # base_info.custom_url = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（18）	显示在入口右侧的提示语。
    # "6个汉字tips",
    # base_info.custom_url_sub_title = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（15）	营销场景的自定义入口名称。
    # "营销入口1",
    # base_info.promotion_url_name = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（128）	入口跳转外链的地址链接。
    #  "http://www.qq.com",
    # base_info.promotion_url = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	bool	填写true为用户点击进入会员卡时推送事件，默认为false。详情见 进入会员卡事件推送
    # true
    # base_info.need_push_on_view = "https://mmbiz.qlogo.cn/mmbiz/"
    return base_info


# 自定义会员信息类目，会员卡激活后显示。
def get_wechat_member_card_custom_cell():
    custom_cell = wechat_member_card_pb2.WechatMemberCardSpec.CustomCell()
    # 是	string(15)	入口名称。
    # "使用入口2",
    custom_cell.name = "https://mmbiz.qlogo.cn/mmbiz/"
    # 	是	string(18)	入口右侧提示语，6个汉字内。
    # "激活后显示",
    custom_cell.tips = "https://mmbiz.qlogo.cn/mmbiz/"
    # 是	string(128)	入口跳转链接。
    # "http://www.qq.com"
    custom_cell.url = "https://mmbiz.qlogo.cn/mmbiz/"

    return custom_cell


# 会员信息类目半自定义名称，当开发者变更这类类目信息的value值时,
def get_wechat_member_card_custom_field():
    custom_field = wechat_member_card_pb2.WechatMemberCardSpec.CustomCell()

    #  会员信息类目自定义名称，当开发者变更这类类目信息的value值时,
    # 不会触发系统模板消息通知用户。
    custom_field.custom_field1.name = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string(24)	会员信息类目半自定义名称，当开发者变更这类类目信息的value值时 可以选择触发系统模板消息通知用户。 FIELD_NAME_TYPE_LEVEL 等级 FIELD_NAME_TYPE_COUPON 优惠券 FIELD_NAME_TYPE_STAMP 印花 FIELD_NAME_TYPE_DISCOUNT 折扣 FIELD_NAME_TYPE_ACHIEVEMEN 成就 FIELD_NAME_TYPE_MILEAGE 里程 FIELD_NAME_TYPE_SET_POINTS 集点 FIELD_NAME_TYPE_TIMS 次数
    # "FIELD_NAME_TYPE_LEVEL",
    custom_field.custom_field1.name_type = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	string（128）	点击类目跳转外链url
    #  "http://www.qq.com"
    custom_field.custom_field1.url = "https://mmbiz.qlogo.cn/mmbiz/"

    return custom_field


# 积分规则
def get_wechat_member_card_bonus_rule():
    bonus_rule = wechat_member_card_pb2.WechatMemberCardSpec().BonusRule()
    # 否	int	消费金额。以分为单位。
    # 100,
    bonus_rule.cost_money_unit = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	对应增加的积分。
    # 1,
    bonus_rule.increase_bonus = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	用户单次可获取的积分上限。
    # 200,
    bonus_rule.max_increase_bonus = "https://mmbiz.qlogo.cn/mmbiz/"

    # 	否	int	初始设置积分。
    # 10,
    bonus_rule.init_increase_bonus = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	每使用5积分。
    # 5,
    bonus_rule.cost_bonus_unit = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	抵扣xx元，（这里以分为单位）
    # 100,
    bonus_rule.reduce_money = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	抵扣条件，满xx元（这里以分为单位）可用。
    # 1000,
    bonus_rule.least_money_to_use_bonus = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	int	抵扣条件，单笔最多使用xx积分。
    # 50
    bonus_rule.max_reduce_bonus = "https://mmbiz.qlogo.cn/mmbiz/"

    return bonus_rule


def get_wechat_member_card_info(auto_activate, merchant):
    """获取默认配置微信会员卡信息
    :param auto_activate: 是否自动激活
    :param merchant: merchant_rules_pb2.Merchant()
    :return wechat_member_card_category: wechat_member_card_pb2.WechatMemberCardCategory():
    """
    # 会员卡类型的卡券信息
    member_card = wechat_member_card_pb2.WechatMemberCardSpec()

    # 否	string(128)	商家自定义会员卡背景图，须 先调用 上传图片接口 将背景图上传至CDN，否则报错， 卡面设计请遵循 微信会员卡自定义背景设计规范 ,像素大小控制在 1000像素*600像素以下
    # "https://mmbiz.qlogo.cn/mmbiz/"
    # member_card.background_pic_url = "https://mmbiz.qlogo.cn/mmbiz/"

    # 是	JSON 基本的卡券数据，见下表，所有卡券类型通用。
    base_info = get_wechat_member_card_base_info(merchant)
    member_card.base_info.CopyFrom(base_info)

    # 否	JSON结构	创建优惠券特有的高级字段
    # member_card.advanced_info.CopyFrom(advanced_info)

    # 是  bool	显示积分，填写true或false，如填写true，积分相关字段均为必 填 若设置为true则后续不可以被关闭。
    # true
    member_card.supply_bonus = False

    # 是	bool	是否支持储值，填写true或false。如填写true，储值相关字段均为必 填 若设置为true则后续不可以被关闭。该字段须开通储值功能后方可使用， 详情见： 获取特殊权限
    # false
    member_card.supply_balance = False

    # 是 strin g(3072)	会员卡特权说明,限制1024汉字。
    # "test_prerogative",
    member_card.prerogative = "无特权"

    # 否	bool	设置为true时用户领取会员卡后系统自动将其激活，无需调用激活接口，详情见 自动激活 。
    # true
    if auto_activate:
        member_card.auto_activate = auto_activate
    else:
        member_card.wx_activate = True
        member_card.wx_activate_after_submit = True
        member_card.wx_activate_after_submit_url = "http://www.zhiyi.ai"
        member_card.activate_app_brand_user_name = "{}@app".format(config.WECHAT_MINIPROGRAM_APPID)
        member_card.activate_app_brand_pass = "pages/merchant/index?from=memberCardRegistry&merchantId={}".format(merchant.id)

    # 否	JSON结构	自定义会员信息类目，会员卡激活后显示,包含name_type (name) 和url字段
    # custom_field1 = create_wechat_member_card_custom_field()
    # member_card.custom_field1.CopyFrom(custom_field1)

    # 否	string（128）	激活会员卡的url。
    #  "http://www.qq.com",
    # member_card.activate_url = "https://mmbiz.qlogo.cn/mmbiz/"

    # 否	JSON结构	自定义会员信息类目，会员卡激活后显示。
    # custom_cell = create_wechat_member_card_custom_cell()
    # member_card.custom_cell.CopyFrom(custom_cell)

    # 否	JSON结构	积分规则 。
    # bonus_rule = create_wechat_member_card_custom_cell()
    # member_card.custom_cell.CopyFrom(bonus_rule)

    # 否	int	折扣，该会员卡享受的折扣优惠,填10就是九折。
    # 10
    # member_card.discount = "10"
    return member_card


# 创建默认设置的微信会员卡
def create_default_wechat_member_card(merchant_id, auto_activate):
    """创建默认设置的微信会员卡
    :param merchant_id: 商户信息
    :param auto_activate: 是否自动激活

    :return id: 微信会员卡ID:
    :return id: 微信会员卡信息:
    """
    # 读取商户信息
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    # 创建微信会员卡信息
    wechat_member_card_category = wechat_member_card_pb2.WechatMemberCardCategory()

    # 是	string	会员卡类型。
    # 'MEMBER_CARD'
    wechat_member_card_category.card_type = 'MEMBER_CARD'
    member_card = get_wechat_member_card_info(auto_activate, merchant)
    wechat_member_card_category.member_card.CopyFrom(member_card)
    print(wechat_member_card_category)

    return wechat_member_card_category


def create_default_member_card(merchant_id, auto_activate):
    """创建默认配置会员卡
    :param merchant_id: 商户信息
    :param auto_activate: 是否自动激活

    :return id: 微信会员卡ID:
    :return id: 微信会员卡信息:
    """
    # 实例化会员卡信息
    member_card_category = membership_pb.MemberCardCategory()

    # 创建默认设置的微信会员卡
    wechat_member_card = create_default_wechat_member_card(merchant_id, auto_activate)
    member_card_category.wechat_card.CopyFrom(wechat_member_card)

    # 创建 Request
    request = membership_service_pb.CreateMemberCardCategoryRequest()
    request.member_card_category.CopyFrom(member_card_category)

    card_id = id_manager.generate_card_id()
    # 对接微信API 申请微信会员卡
    # card_id = member_card_api_helper.create_member_card_class(merchant_id, request)
    # if not card_id:
    #     logger.error("创建会员卡失败，商户ID: {}".format(merchant_id))
    #     return None

    member_card_category.id = card_id
    member_card_category.merchant_id = merchant_id
    member_card_category.create_time = date_utils.timestamp_second()

    #  推广门店ID列表
    # store_id_list = []
    # member_card_category.store_id_list = store_id_list
    # 创建时间(UNIX Epoch时间格式，单位为秒)

    # 保存会员卡信息
    MembershipDataAccessHelper().add_member_card_category(member_card_category)
    # 如果是非自动激活状态会员卡，则
    if not auto_activate:
        set_activate_form(merchant_id, card_id)

    return member_card_category


def set_activate_form(merchant_id, card_category_id):
    requred_fields = {
        "required_form": {
            "common_field_id_list": [
                "USER_FORM_INFO_FLAG_NAME",
                "USER_FORM_INFO_FLAG_SEX",
                "USER_FORM_INFO_FLAG_MOBILE",
                "USER_FORM_INFO_FLAG_BIRTHDAY",
            ],
        },
        "card_id": card_category_id,
    }
    member_card_api_helper.set_membercard_activate_user_form(merchant_id, requred_fields)


def get_activate_extra(merchant_id, card_category_id):
    def _decode_activate_extra_url(url):
        # Example URL: 'https://mp.weixin.qq.com/bizmall/activatemembercard?action=preshow&&encrypt_card_id=kxvzpofr8L1PJaw%2FEIghD1By9TW2lq%2F7yWsQS6VuthL9ZwZHb8tvr%2FQx2%2FvXAwRe&outer_str=hello&biz=MzI5NDk0MzIzMw%3D%3D#wechat_redirect'
        result = {}
        parts = url.split('#')[0].split('?')[1].split('&')
        for part in parts:
            if not part:
                continue

            (name, value) = part.split('=')
            if name == "encrypt_card_id" or name == "outer_str" or name == "biz":
                result[name] = urllib.parse.unquote(value)
        return result

    result = member_card_api_helper.activate_member_card_category(
        merchant_id, card_category_id, constants.WECHAT_MEMBER_CARD_CATEGORY_OUTER_STRING
    )
    url = result['url']
    return _decode_activate_extra_url(url)

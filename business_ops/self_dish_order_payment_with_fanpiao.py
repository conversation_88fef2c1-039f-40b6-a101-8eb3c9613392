# -*- coding: utf-8 -*-

import logging

import proto.finance.wallet_pb2 as wallet_pb
from business_ops.fanpiao_manager import FanpiaoManager
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserData<PERSON>ccessHelper
from service import errors

logger = logging.getLogger(__name__)


class OrderingFanpiaoManager(FanpiaoManager):

    def __init__(self, user_id=None, pay_method=None, transaction_id=None, merchant_id=None,
                 fanpiao_category_id=None, return_url=None, order_id=None):
        self.return_url = return_url
        self.order_id = order_id
        if fanpiao_category_id is not None:
            self.fanpiao_category = FanpiaoDataAccessHelper().get_fanpiao_category_by_id(fanpiao_category_id)
        if transaction_id is not None:
            self.transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
            user_id = self.transaction.payer_id
            pay_method = self.transaction.pay_method

        if merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

        self.pay_method = pay_method

        super(OrderingFanpiaoManager, self).fill_fanpiao_category(fanpiao_category_id)

    def notification(self):
        super(OrderingFanpiaoManager, self).notification(decrease_dish_remain=False)

        order_da = OrderingServiceDataAccessHelper()
        order = order_da.get_order(id=self.order_id)
        fanpiao_pay_manager = FanpiaoPayManager(merchant=self.merchant)
        fee = fanpiao_pay_manager.get_show_order_paid_fee(
            order, self.user.id, raise_error=False)
        paid_fee = fee.fee
        remain_fee = fee.remain_fee
        if remain_fee > 0:
            logger.info("订单 {} 使用卡包支付余额不足".format(order.id))
            raise errors.ShowError("卡包余额不足")
        pay_method = wallet_pb.Transaction.FANPIAO_PAY
        order_manager_v2 = OrderManagerV2(order=order)
        manager = SelfDishOrderPaymentManager(
            merchant_id=order.merchant_id, user=self.user, order=order,
            bill_fee=order.bill_fee, paid_fee=paid_fee, pay_method=pay_method,
            order_manager_v2=order_manager_v2)
        manager.prepay(check_dish_remain=False)

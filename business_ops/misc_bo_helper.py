import os
from urllib import parse
import uuid

from flask import Response
from PIL import Image

from business_ops import constants as const
from common.aliyun_oss_helper import AliyunOSSHelper
from service import errors
from service import error_codes


# 设置允许的文件格式
ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'JPG', 'PNG', 'bmp'])


def allowed_file(filename):
    """ 检查图片是否符合要求的格式
    :param filename: 图片名称
    :return:
    """
    return '.' in filename and filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS


class MiscBusinessHelper(object):
    def __init__(self):
        self.shilai_img_host = os.environ.get("SHILAI_IMG_HOST", "shilai.zhiyi.cn")

    def get_uuid(self):
        """ 获取uuid
            :return: unique_id (string)
        """
        unique_id = str(uuid.uuid4()).replace("-", '')
        return unique_id

    def save_request_image(self, request):
        """ 保存通过flask上传的图片
            上传的图片会保存在默认的位置

            :param request: (flask request) flask 接收到的request
            :return: 返回 可以被访问到url
        """
        f = request.files['file']
        filename = f.filename
        if not (f and allowed_file(f.filename)):
            raise errors.Error(err=error_codes.INVALID_IMAGE_TYPE)

        filename = "{}_{}".format(uuid.uuid4(), filename)
        oss_helper = AliyunOSSHelper("shilai-images")
        image_url = oss_helper.upload_image_binary(f, filename)
        return image_url.url

    def get_response_image(self, filename):
        """ 获取保存在服务器的图片
            将图片 转换为 flask 返回的 response
            :param filename: 图片的名字
            :return: resp (flask Response)
        """
        img_path = os.path.join(const.STATIC_IMAGES_DIR, filename)

        if not os.path.exists(img_path):
            return ""
        with open(img_path, 'rb') as f:
            image = f.read()

        # 获取图片存储的格式
        filename_type = filename.split(".")[-1]

        resp = Response(image, mimetype="image/{}".format(filename_type))
        return resp

    def get_data_file(self, filename):
        """ 获取保存在服务器的数据文件
        """
        file_path = os.path.join(const.DATA_FILES_DIR, filename)
        with open(file_path, 'rb') as f:
            content = f.read()

        resp = Response(content, mimetype="text/plain")
        return resp

    def get_thumbnail_image_url(self, url):
        """ 返回url对应的缩略图,
            1. 如果缩略图存在直接返回.
            2. 如果缩略图是外部地址,原样返回
            3. 如果缩略图不存在,则生成缩略图
            TODO: 使用脚本生成缩略图,而不是在请求的时候再生成
        Args:
            url: (string)原图地址
        Return:
            url: (string)缩略图地址
        """
        image_name = url.split("/")[-1]
        thumbnail_image_path = os.path.join(const.STATIC_IMAGES_DIR, "thumbnail{}".format(image_name))
        url_path = "{host}thumbnail{image_name}".format(host=const.REQUEST_IMAGE_URL, image_name=image_name)
        if os.path.exists(thumbnail_image_path):
            # 如果有缩略图,直接返回缩略图地址
            return url_path

        image_host = parse.urlparse(url)
        if image_host.netloc != self.shilai_img_host:
            # 如果图片不是存在时来服务器上,直接返回原图
            return url

        dir_path = os.path.join(const.STATIC_IMAGES_DIR, image_name)
        # 生成缩略图.
        if not os.path.exists(dir_path):
            return None
        im = Image.open(dir_path)
        im.thumbnail((300, 300))
        im.save(thumbnail_image_path)
        return url_path

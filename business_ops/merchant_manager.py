# -*- coding: utf-8 -*-

import hashlib
import random
import uuid
import logging
import time
import os

import proto.coupon_category_pb2 as coupon_category_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.wechat_member_card_pb2 as wechat_member_card_pb
import proto.config_pb2 as config_pb
from proto import staff_pb2 as staff_pb
from business_ops.ordering.shop_manager import ShopManager
from common.config import config
from common.utils import access_token_helper
from common.utils import date_utils
from common.utils import id_manager
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.qrcode_da_helper import QrcodeDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from proto import merchant_rules_pb2 as merchant_pb
from service import errors
from wechat_lib import card_submerchant_api_helper
from wechat_lib import link_miniprogram_api_helper

logger = logging.getLogger(__name__)


class MerchantManager:
    def __init__(self, merchant=None, merchant_id=None):
        self.merchant = merchant
        self.merchant_id = merchant_id
        if merchant is not None:
            self.merchant = self.merchant
        if merchant_id is not None and self.merchant is None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id)

    def get_valid_merchant(self, merchant_id):
        """获取有效商户"""
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant and merchant.status != merchant_rules_pb.DELETED:
            return merchant
        return None

    def inviter_user_as_manager(self, inviter_id, user_id, merchant_id):
        store_id = "{}_0".format(merchant_id)
        da = MerchantUserDataAccessHelper()
        inviter = da.get_user(inviter_id)
        role = None
        for merchant in inviter.merchants:
            if merchant.merchant_id == merchant_id and merchant.role in [merchant_pb.SUPER_ADMIN, merchant_pb.ADMIN]:
                role = merchant.role
        if not role:
            return
        user = da.get_user(user_id)
        for merchant in user.merchants:
            if merchant.merchant_id == merchant_id:
                return
        merchant = user.merchants.add()
        merchant.merchant_id = merchant_id
        merchant.store_id = store_id
        merchant.role = merchant_pb.ADMIN if len(da.get_users(merchant_id=merchant_id)) > 0 else merchant_pb.SUPER_ADMIN
        da.update_or_create_user(user)

    def scan_code_become_manager(self, scene_id, user_id, merchant_id):
        """商户扫业务助手的二维码成为商户管理员"""
        qrcode_da = QrcodeDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        merchant_user_da = MerchantUserDataAccessHelper()
        scene = qrcode_da.get_merchant_assist_login_scene(scene_id=scene_id)
        if not scene:
            logger.info("cannot find scene: {}".format(scene_id))
            return
        now = int(time.time())
        if now > scene.expire_at:
            raise errors.ShowError("二维码已过期,请联系相关人员")
        merchant_id = scene.merchant_id
        merchant = merchant_da.get_merchant(merchant_id)
        already_merchant_manager = False
        for manager in merchant.manager_list:
            if manager.user_id == user_id:
                logger.info("已经是商户的管理员了: {}".format(user_id))
                already_merchant_manager = True
                break
        if not already_merchant_manager:
            manager = merchant.manager_list.add()
            manager.user_id = user_id
            manager.role = merchant_pb.ADMIN
            merchant_da.update_or_create_merchant(merchant)
        user = merchant_user_da.get_user(user_id)
        already_merchant_manager = False
        for user_merchant in user.merchants:
            if user_merchant.merchant_id == merchant_id:
                logger.info("已经是商户的管理员了: {}".format(user_id))
                already_merchant_manager = True
                break
        if not already_merchant_manager:
            user_merchant = user.merchants.add()
            user_merchant.merchant_id = merchant_id
            user_merchant.role = (
                merchant_pb.ADMIN if len(merchant_user_da.get_users(merchant_id=merchant_id)) > 0 else merchant_pb.SUPER_ADMIN
            )
            merchant_user_da.update_or_create_user(user)
            logger.info("商户用户为商户管理员: {}, {}".format(user_id, merchant.id))

    def init_merchant(
        self, id, user_id, binding_staff_id, join_method=merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER, assist_build=False
    ):
        """初始化商户信息 >>>>> The Only Init Function For Search <<<<<<

        当商户管理员完成加入流程（授权时来平台或绑定时来公众号子商户），创建商户信息实例

        Args:
            id: (string) 商户ID
            user_id: (string) 管理员角色的用户ID
            binding_staff_id: (string) 业务员ID
            join_method: (MerchantJoinMethod) 加入途径

        Return:
            (Merchant) 商户实例
        """
        merchant = merchant_rules_pb.Merchant()
        if id is not None:
            merchant.id = id
        else:
            merchant.id = id_manager.generate_common_id()
        merchant.join_method = join_method
        merchant.binding_staff_id = binding_staff_id
        merchant.create_timestamp = date_utils.timestamp_second()
        merchant.enable_number_plate_splash = True
        merchant.disable_number_plate_pay_fanpiao_pay = True
        store = merchant.stores.add()
        store.id = id_manager.generate_common_id()
        store.enable_ordering_service = True
        store.enable_ordering_coupon_package_union_pay = True
        store.enable_coupon_expiration_reminder = True
        store.disable_buy_fanpiao = True
        store.disable_people_count = True
        if assist_build:
            store.disable_buy_fanpiao = False
            store.enable_fanpiao = True
            store.banner_mode = merchant_pb.Store.BannerMode.BANNER_FANPIAO
            store.splash_mode = merchant_pb.Store.SplashMode.FANPIAO
        merchant.enable_cancel_pay_broadcast = True
        merchant.payment_info.settlement_rate = 0.0038

        self.restore_merchant_default_preferences(merchant)

        # 创建用户信息
        user_account = merchant_rules_pb.UserAccount()
        user_account.user_id = user_id
        user_account.role = merchant_rules_pb.SUPER_ADMIN

        # 添加用户到 manager_list
        manager = merchant.manager_list.add()
        manager.CopyFrom(user_account)

        self.init_config(merchant)
        self.sync_to_pos_service(merchant)

        return merchant

    def sync_to_pos_service(self, merchant):
        """把商家同步到pos端"""
        store = merchant.stores[0]
        shop_manager = ShopManager(merchant=merchant, store=store)
        pos_type = registration_pb.OrderingServiceRegistrationInfo.SHILAI
        shop_manager.register(pos_type=pos_type)

    def init_config(self, merchant):
        config_da = ConfigDataAccessHelper()
        ordering_config = config_da.get_ordering_config(merchant_id=merchant.id)
        if not ordering_config:
            ordering_config = config_pb.OrderingConfig()
            ordering_config.disable_show_sold_number = True
            config_da.add_or_update_ordering_config(ordering_config)

            try:
                config = config_pb.VipMembershipConfig()
                config.enable_vip_membership_payment = True
                config.merchant_id = merchant.id
                config.vip_membership_discount = 5
                config_da.add_or_update_vip_membership_config(config)
            except Exception as ex:
                logger.info(f"创建商家会员配置出错: {ex}")
                pass

    def init_registration_info(self, merchant_id):
        """初始化商户点餐功能相关信息。该初始化流程应当与创建商户同时完成。

        Args:
            merchant_id: (string) 商户ID
        """
        order_da = OrderingServiceDataAccessHelper()
        registration_info = order_da.get_registration_info(merchant_id=merchant_id)

        registration_info = registration_pb.OrderingServiceRegistrationInfo()
        registration_info.merchant_id = merchant_id
        registration_info.package_type = registration_pb.OrderingServiceRegistrationInfo.PackageType.TAKE_AWAY
        registration_info.max_take_out_distance = 3000

        registration_info.ordering_config.enable_eat_in = True
        registration_info.ordering_config.diner_uplimit = 16
        registration_info.keruyun_pos_info.shop_id = ''
        registration_info.fanpiao_pay_commission_rate = 5
        registration_info.coupon_package_pay_commission_rate = 5
        registration_info.wallet_pay_commission_rate = 5
        registration_info.coupon_commission_rate = 5
        registration_info.printer_config.feie_printer.print_format_version = '2'
        registration_info.ordering_config.dinner_type = registration_pb.OrderingConfig.FAST_FOOD
        order_da.add_or_update_registration_info(registration_info)

    def restore_merchant_default_preferences(self, merchant):
        """恢复商户配置选项的系统初始化设置。

        Args:
            merchant: (Merchant) 商户信息结构体
        """

        def _pick_random_color():
            """随机选择并返回一种微信官方卡券颜色代码。

            Returns:
                (string) 随机选取的卡券颜色代码
            """
            COLORS = ["Color030", "Color040", "Color070", "Color080", "Color090"]
            return COLORS[random.randint(0, len(COLORS) - 1)]

        preferences = merchant_rules_pb.MerchantPreferences()

        # 优惠券相关设置
        preferences.coupon_config.max_monthly_coupons = 100000
        preferences.coupon_config.max_cash_per_coupon = 200
        # 默认折扣底线设置为100
        preferences.coupon_config.max_discount = 0
        # 营销折扣底线默认设置为100
        preferences.coupon_config.fanpiao_and_coupon_extra_discount = 0
        preferences.coupon_config.exclude_groupon = False
        preferences.coupon_config.exclude_cash_coupon = False
        preferences.coupon_config.exclude_discount_coupon = False
        preferences.coupon_config.exclude_gift_coupon = False
        preferences.coupon_config.exclude_general_coupon = False
        preferences.coupon_config.drink_excluded = False

        preferences.red_packet_config.red_packet_discount = 0
        preferences.red_packet_config.shilai_extra_discount = 0
        preferences.red_packet_config.block_rate = 0
        # 商户拉新券默认设置
        # TODO: 目前暂时不创建缺省拉新券
        # cash_coupon_spec = strategy_pb.CashCouponCategorySpec()
        # cash_coupon_spec.coupon_category_spec.issue_scene = coupon_category_pb.CouponCategory.NEW_MEMBER
        # cash_coupon_spec.coupon_category_spec.date_info.type = 'DATE_TYPE_FIX_TERM'
        # cash_coupon_spec.coupon_category_spec.date_info.fixed_begin_term = 0
        # cash_coupon_spec.coupon_category_spec.date_info.fixed_term = 7
        # cash_coupon_spec.coupon_category_spec.reject_categories = ''
        # cash_coupon_spec.coupon_category_spec.can_use_with_other_discount = False
        # cash_coupon_spec.coupon_category_spec.description = COUPON_DEFAULT_DESCRIPTION
        # cash_coupon_spec.coupon_category_spec.get_limit = 1000
        # cash_coupon_spec.coupon_category_spec.use_limit = 1000
        # cash_coupon_spec.least_cost = 10000
        # cash_coupon_spec.reduce_cost = 500

        # new_member_coupon_spec = preferences.coupon_config.new_member_coupons.add()
        # new_member_coupon_spec.id = id_manager.generate_coupon_strategy_id()
        # new_member_coupon_spec.name = '缺省拉新代金券'
        # new_member_coupon_spec.cash_coupon_spec.CopyFrom(cash_coupon_spec)

        merchant.preferences.CopyFrom(preferences)

        # 商户展示信息
        preferences.design_config.logo_url = merchant.basic_info.logo_url
        # TODO: 添加商户背景图片
        preferences.design_config.background_pic_url = ''
        preferences.design_config.color = _pick_random_color()

    def get_card_ext(self, merchant_id, card_category_id):
        """根据指定会员卡类型ID，生成相应的cardExt (微信小程序结构体)

        Args:
            user_id: 用户ID
            coupon_category_id: (string) 会员卡类型ID
        Returns:
            若指定会员卡类型存在，返回该会员卡类型对应的MemberCardCategoryExt结构体，若不存在则返回None
        """

        def _generate_signature(card_ext, ticket):
            """生成微信小程序cardExt指定所需签名的算法。详见:
                https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141115

            Args:
                card_ext: (MemberCardCategoryExt) cardExt结构体
                ticket: 从微信平台获取的ticket字符串
            Returns:
                (string) 签名字符串
            """
            values = [ticket, str(card_ext.timestamp), card_ext.nonce_str, card_category_id]
            values.sort()
            value_str = ''.join(values)
            return hashlib.sha1(value_str.encode(encoding='utf-8')).hexdigest()

        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        card_ticket = self.get_card_api_ticket(merchant)
        if card_ticket:
            card_ext = wechat_member_card_pb.MemberCardCategoryExt()
            card_ext.nonce_str = str(uuid.uuid4())[0:8]
            # TODO: 时间戳，需注意返回东八区时间,UTC+8，单位为秒
            card_ext.timestamp = date_utils.timestamp_second()
            card_ext.signature = _generate_signature(card_ext, card_ticket)
            return card_ext
        return None

    def get_card_api_ticket(self, merchant):
        """获取商户卡券的 API Tickets

        Args:
            merchant: (merchant_rules_pb.Merchant) 商户信息

        Return:
            (authorizer_pb.AuthorizerTicket)
        """
        if merchant.join_method == merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER:
            appid = merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
            return access_token_helper.get_authorizer_api_ticket(appid, 'WX_CARD')
        elif merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
            appid = config.SHILAI_MP_APPID
            return access_token_helper.get_shilai_app_api_ticket(appid, 'WX_CARD')

        return None

    def update_shilai_platform_authorizer_info(self, merchant_id, authorizer_info):
        """更新通过授权平台加入的商户公众号信息

        Args:
            merchant_id: (string) 商户ID
            authorizer_info: (Authorizer) 需要更新的平台授权商户公众号信息
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant:
            merchant.shilai_platform_authorizer_info.CopyFrom(authorizer_info)
            MerchantDataAccessHelper().update_or_create_merchant(merchant)

    def update_shilai_mp_submerchant_info(self, merchant_id, brand_name, logo_url, auth_letter_media_id, auth_letter_end_time):
        """更新时来公众号子商户信息

        Args:
            merchant_id: (string) 商户ID
            brand_name: (string) 商户名称
            logo_url: (string) 商户LOGO
            auth_letter_media_id: (string) 授权函上传后的 media_id
            auth_letter_end_time: (string) 授权函有效期截止时间

        Returns:
            (Merchant) 更新后的商户
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant and merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
            submerchant_info = merchant.shilai_mp_submerchant_info
            submerchant_id = None
            if submerchant_info and submerchant_info.id:
                submerchant_id = submerchant_info.id

            submerchant_info = card_submerchant_api_helper.create_or_update_submerchant(
                submerchant_id=submerchant_id,
                brand_name=brand_name,
                logo_url=logo_url,
                auth_letter_media_id=auth_letter_media_id,
                auth_letter_end_time=auth_letter_end_time,
            )

            if submerchant_info is not None:
                # 更新 basic_info
                merchant.basic_info.name = submerchant_info.brand_name
                merchant.basic_info.display_name = submerchant_info.brand_name
                merchant.basic_info.logo_url = submerchant_info.logo_url

                merchant.shilai_mp_submerchant_info.CopyFrom(submerchant_info)
                MerchantDataAccessHelper().update_or_create_merchant(merchant)
                return merchant
        return None

    def update_shilai_mp_submerchant_audit_status(self, submerchant_id, status, rejected_reason=None):
        """更新时来公众号子商户审核状态

        Args:
            submerchant_id: (string) 公众号子商户ID
            status: (ShilaiMPSubmerchantInfo.SubmerchantStatus) 子商户审核状态
            rejected_reason: (string | None) 拒绝原因
        """
        merchant = MerchantDataAccessHelper().get_merchant_by_submerchant_id(submerchant_id)
        if merchant and merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
            submerchant_info = merchant.shilai_mp_submerchant_info
            submerchant_info.status = status
            if rejected_reason is not None:
                submerchant_info.rejected_reason = rejected_reason
            MerchantDataAccessHelper().update_or_create_merchant(merchant)

    def add_user_to_merchant_manager_list(self, merchant_id, store_id, user_id, role):
        """添加用户到商户管理员列表

        Args:
            merchant_id: (string) 商户ID
            store_id: (string) 门店ID
            user_id: (string) 用户ID
            role: (merchant_rules_pb.Role) 用户角色
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        is_invited = False
        for manager in merchant.manager_list:
            if manager.user_id == user_id:
                is_invited = True
                break

        if not is_invited:
            manager = merchant.manager_list.add()
            manager.user_id = user_id
            manager.role = role
            manager.store_id_list.append(store_id)
            MerchantDataAccessHelper().update_or_create_merchant(merchant)

    def get_merchant_staff_role(self, merchant_id, user_id):
        """获取商户员工的角色

        Args:
            merchant_id: (string) 商户ID
            user_id: (string) 用户ID

        Return:
            (merchant_pb.Role|None) 返回角色，如果不属于该商户，则返回 None
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant:
            for manager in merchant.manager_list:
                if manager.user_id == user_id:
                    return manager.role
        return None

    def link_shilai_miniprogram(self, merchant_id):
        """发起关联商户公众号与时来饭票小程序

        Args:
            merchant_id: (String) 商户ID

        Return:
            (Boolean) 关联成功返回True，否则返回False

        Raise:
            (WechatApiResponseError)
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        appid = self.__get_authorization_appid(merchant)
        if appid:
            link_miniprogram_api_helper.link_miniprogram(appid)
            return True
        return False

    def check_linking_shilai_miniprogram_status(self, merchant_id):
        """检查商户公众号关联时来饭票小程序的状态

        微信目前还没有关联小程序状态更新的通知机制，目前只能通过人工的方式
         1. 如果数据库中的状态信息不为 LINKED，则查询微信接口；
         2. 如果微信接口返回的状态不为 LINKED，则返回 False
         3. 如果微信接口返回的状态为 LINKED，则保存数据库且返回 True
        """
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
            return True

        if merchant.join_method == merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER:
            is_linked = self.__is_linked_shilai_miniprogram(merchant.linking_miniprograms)
            if not is_linked:
                appid = self.__get_authorization_appid(merchant)
                if appid:
                    linking_miniprograms = link_miniprogram_api_helper.get_linking_miniprograms(appid)
                    is_linked = self.__is_linked_shilai_miniprogram(linking_miniprograms)
                    self.__update_linking_miniprogram_info(merchant, linking_miniprograms)
            return is_linked
        return False

    def activate_merchant(self, merchant_id, activate_staff_id=None):
        # TODO: 返回失败的详细信息
        """商户上线，返回上线成功或失败

        检查商户信息的完整性:
          1. 授权信息
          2. 基本信息
          3. 门店信息
          4. 拉新券配置
          5. 支付子商户号
        更新商户状态为 RUNNING

        Args:
            merchant_id: (String) 商户ID
        """
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(merchant_id)
        if merchant:
            # 只有第一次能设置成功
            if activate_staff_id is not None and merchant.activate_staff_id == "":
                merchant.activate_staff_id = activate_staff_id
            merchant.status = merchant_rules_pb.RUNNING
            now = int(time.time())
            merchant.activate_timestamp = now
            merchant_da.update_or_create_merchant(merchant)
            return True
        return False

    def save_coupon_category_spec(self, merchant, coupon_category_spec):
        """保存商户优惠券类型设定"""
        coupon_category_specs = None
        coupon_config = merchant.preferences.coupon_config
        if coupon_category_spec.HasField('cash_coupon_spec'):
            issue_scene = coupon_category_spec.cash_coupon_spec.coupon_category_spec.issue_scene
        elif coupon_category_spec.HasField('coupon_package_spec'):
            issue_scene = coupon_category_spec.coupon_package_spec.coupon_category_spec.issue_scene

        if issue_scene == coupon_category_pb.CouponCategory.NEW_MEMBER:
            coupon_category_specs = coupon_config.new_member_coupons
        if issue_scene == coupon_category_pb.CouponCategory.WECHAT_MOMENTS_AD:
            coupon_category_specs = coupon_config.wechat_moments_ad_coupons
        if issue_scene == coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            coupon_category_specs = coupon_config.coupon_packages
        if issue_scene == coupon_category_pb.CouponCategory.REGULAR_COUPONS:
            coupon_category_specs = coupon_config.regular_coupons

        if not coupon_category_specs:
            raise errors.CouponCategorySpecNotSupportError()

        # 寻找已有索引
        index = -1
        for i, spec in enumerate(coupon_category_specs):
            if spec.id == coupon_category_spec.id:
                index = i
                break

        # 更新或添加配置
        if index == -1:
            new_coupon_category_spec = coupon_category_specs.add()
            new_coupon_category_spec.CopyFrom(coupon_category_spec)
        else:
            coupon_category_specs[index].CopyFrom(coupon_category_spec)

        MerchantDataAccessHelper().update_or_create_merchant(merchant)

    def __get_linking_shilai_miniprogram_status(self, linking_miniprograms):
        """获取关联时来小程序的状态"""
        for miniprogram_info in linking_miniprograms:
            if miniprogram_info.origin_id == config.WECHAT_MINIPROGRAM_ORIGIN_ID:
                return miniprogram_info.status
        return None

    def __update_linking_miniprogram_info(self, merchant, linking_miniprograms):
        """更新商户关联的小程序列表"""
        for miniprogram_info in linking_miniprograms:
            info = merchant.linking_miniprograms.add()
            info.CopyFrom(miniprogram_info)
        MerchantDataAccessHelper().update_or_create_merchant(merchant)

    def __get_authorization_appid(self, merchant):
        """获取商户授权公众号的appid"""
        if merchant and merchant.join_method == merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER:
            return merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
        return None

    def __is_linked_shilai_miniprogram(self, linking_miniprograms=None):
        """是否已关联时来饭票小程序"""
        linking_status = None
        if linking_miniprograms:
            linking_status = self.__get_linking_shilai_miniprogram_status(linking_miniprograms)

        return linking_status is not None and linking_status == merchant_rules_pb.LinkingMiniProgramInfo.LINKED

    def get_settlement_rate(self):
        return self.merchant.payment_info.settlement_rate

    def get_merchant_list(self):
        merchant_da = MerchantDataAccessHelper()
        status = merchant_rules_pb.RUNNING
        merchant_list = merchant_da.get_merchant_list(status=status)
        return merchant_list

    def get_merchant_list_multi_status(self, status=None):
        merchant_da = MerchantDataAccessHelper()
        m0 = merchant_da.get_merchant_list(status=merchant_rules_pb.RUNNING)
        m1 = merchant_da.get_merchant_list(status=merchant_rules_pb.EDITING)
        merchant_list = m0 + m1
        return merchant_list

    def get_merchant_name(self, merchant=None):
        if merchant is None:
            merchant = self.merchant
        if merchant is None:
            return None
        basic_info = merchant.basic_info
        name = merchant.id
        if len(merchant.stores) > 0:
            name = merchant.stores[0].name
        elif basic_info.display_name:
            name = basic_info.display_name
        elif basic_info.name:
            name = basic_info.name
        return name

    def get_merchant_list_for_merchant_user(self, user, retcode):
        merchant_da = MerchantDataAccessHelper()
        merchant_list = []
        for merchant in user.merchants:
            role = merchant_pb.Role.Name(merchant.role)
            m = merchant_da.get_merchant(merchant.merchant_id)
            if len(m.stores) == 0:
                continue
            merchant_list.append(
                {"merchantId": m.id, "name": m.basic_info.display_name, "address": m.stores[0].address, "role": role}
            )
            retcode.update({'authorized': True, 'merchantId': merchant.merchant_id, "role": role})
        if user.role == merchant_pb.SHILAI_STAFF:
            retcode.update({"role": "SHILAI_STAFF", "authorized": True})
        else:
            staff = StaffDataAccessHelper().get_staff_by_union_id(user.wechat_profile.unionid)
            if staff:
                retcode.update({"role": "USER", "authorized": True})
        retcode.update({"merchantList": merchant_list})
        return retcode

    def get_all_staff_ids(self, staffs, staff_ids=[]):
        staff_da = StaffDataAccessHelper()
        for staff in staffs:
            if staff.role == staff_pb.ShilaiStaff.MANAGER and staff.id not in staff_ids:
                stfs = staff_da.get_staff_list(manager_id=staff.id)
                self.get_all_staff_ids(stfs, staff_ids)
            if staff.id not in staff_ids:
                staff_ids.append(staff.id)
        return staff_ids

    def get_staff_merchant_list(
        self, prev_create_timestamp, status, size=20, user_id=None, staff_ids=None, admin=False, staffs=None
    ):
        merchant_da = MerchantDataAccessHelper()
        merchant_list = []
        if user_id is not None:
            merchant_list = merchant_da.get_merchant_list(
                main_staff_id=user_id,
                binding_staff_id=user_id,
                assist_staff_id=user_id,
                order_by=[("createTimestamp", -1)],
                prev_create_timestamp=prev_create_timestamp,
                size=size,
                status=status,
            )
        elif staffs is not None:
            staff_ids = self.get_all_staff_ids(staffs)
            merchant_list = merchant_da.get_merchant_list(
                staff_ids=staff_ids,
                order_by=[("createTimestamp", -1)],
                prev_create_timestamp=prev_create_timestamp,
                size=size,
                status=status,
            )
        elif admin:
            merchant_list = merchant_da.get_merchant_list(
                order_by=[("createTimestamp", -1)], prev_create_timestamp=prev_create_timestamp, size=size, status=status
            )
        return merchant_list

    def fuzzy_query_merchant(self, name, status=None, user_id=None, staff_ids=None, admin=False):
        merchant_da = MerchantDataAccessHelper()
        if name is not None:
            name = name.split(" ")
            name = ".*".join(name)
        if user_id is not None:
            merchant_list = merchant_da.get_merchant_list(
                main_staff_id=user_id,
                binding_staff_id=user_id,
                assist_staff_id=user_id,
                fuzzy_query_name=name,
                order_by=[("createTimestamp", -1)],
                status=status,
            )
        elif staff_ids is not None:
            merchant_list = merchant_da.get_merchant_list(
                staff_ids=staff_ids, fuzzy_query_name=name, order_by=[("createTimestamp", -1)], status=status
            )
        elif admin:
            merchant_list = merchant_da.get_merchant_list(
                fuzzy_query_name=name, order_by=[("createTimestamp", -1)], status=status
            )
        return merchant_list

    def get_merchant_by_id(self, merchant_id):
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant_by_id(merchant_id=merchant_id)
        return merchant

    def get_merchant_by_handheld_pos_sn(self, sn):
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(handheld_sn=sn)
        return merchant

    def add_or_update_merchant(self, merchant):
        if not merchant:
            return
        merchant_da = MerchantDataAccessHelper()
        merchant_da.update_or_create_merchant(merchant)

# -*- coding: utf-8 -*-

import time
import logging
from collections import namedtuple

from common.utils import date_utils
from common.utils import id_manager
from business_ops import constants as business_ops_constants
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto.finance import wallet_pb2 as wallet_pb
from proto.page import user_bill_pb2 as user_bill_pb

logger = logging.getLogger(__name__)


class TransactionManager():

    def __init__(self):
        self._transaction_da = None

    @property
    def transaction_da(self):
        if self._transaction_da is not None:
            return self._transaction_da
        self._transaction_da = TransactionDataAccessHelper()
        return self._transaction_da

    def get_shilai_member_card_recharge_transaction(self, user_id, merchant_id, pay_method, bill_fee, paid_fee):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_common_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def get_keruyun_member_card_recharge_transaction(self, user_id, merchant_id, pay_method, bill_fee, paid_fee):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_common_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.KERUYUN_MEMBER_CARD_RECHARGE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def get_fanpiao_purchase_transaction(self, user_id, bill_fee, paid_fee, pay_method, payee_id):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.FANPIAO_PURCHASE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_open_group_purchase_transaction(self, payee_id, user_id, bill_fee, paid_fee, pay_method):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.OPEN_GROUP_PURCHASE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_ordering_with_group_purchase_transaction(self, payee_id, payer_id, bill_fee, paid_fee, pay_method):
        """开团与扫码点餐合并支付."""
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        if isinstance(pay_method, str):
            pay_method = wallet_pb.Transaction.PayMethod.Value(pay_method)
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_OPEN_GROUP_PURCHASE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_group_purchase_member_finish_leader_wallet_revenue(self, user_id, paid_fee, transaction_id=None):
        """团购活动,团员下单成功之后,给团长增加零钱"""
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        if transaction_id is None:
            transaction_id = id_manager.generate_transaction_id()
        transaction.id = transaction_id
        transaction.state = wallet_pb.Transaction.PENDING
        transaction.payer_id = user_id
        transaction.pay_method = wallet_pb.Transaction.WALLET
        transaction.bill_fee = paid_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.GROUP_PURCHASE_LEADER_WALLET_REVENUE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def feeding_prepay(self, user_id, po_master_id, bill_fee, paid_fee, pay_method):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = po_master_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.type = wallet_pb.Transaction.FEEDING_PAYMENT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_buy_coupon_package_prepay(self, user_id, merchant_id, pay_method, fee):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = fee
        transaction.paid_fee = fee
        transaction.type = wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_ordering_refund(self, user_id, merchant_id, pay_method, paid_fee, bill_fee, use_coupon_id, id=None, refunded_transaction_id=None):
        """ 扫码点餐退款
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        if id is not None:
            transaction.id = id
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee * -1
        transaction.paid_fee = paid_fee * -1
        transaction.use_coupon_id = use_coupon_id
        transaction.refunded_transaction_id=refunded_transaction_id
        transaction.type = wallet_pb.Transaction.ORDERING_REFUND
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_partial_refund_transaction(self, transaction, bill_fee, paid_fee=None):
        t = wallet_pb.Transaction()
        t.create_time = date_utils.timestamp_second()
        t.id = id_manager.generate_transaction_id()
        t.payer_id = transaction.payer_id
        t.payee_id = transaction.payee_id
        t.bill_fee = bill_fee
        if paid_fee is not None:
            t.paid_fee = paid_fee
        t.pay_method = transaction.pay_method
        t.paid_time = date_utils.timestamp_second()
        t.type = wallet_pb.Transaction.PARTIAL_REFUND
        t.refunded_transaction_id = transaction.id
        TransactionDataAccessHelper().add_or_update_transaction(t)
        return t

    def handle_shilai_pos_order_refund(self, user_id, merchant_id, pay_method, paid_fee, bill_fee, id=None):
        """ pos机点餐退款
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        if id is not None:
            transaction.id = id
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee * -1
        transaction.paid_fee = paid_fee * -1
        transaction.type = wallet_pb.Transaction.SHILAI_POS_ORDER_REFUND
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_coupon_package_refund(self, user_id, merchant_id, pay_method, paid_fee, bill_fee, id=None):
        """ 券包退款
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        if id is not None:
            transaction.id = id
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee * -1
        transaction.paid_fee = paid_fee * -1
        transaction.type = wallet_pb.Transaction.COUPON_PACKAGE_REFUND
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_fanpiao_refund(self, user_id, merchant_id, pay_method, paid_fee,
                              bill_fee, refunded_transaction_id, id=None):
        """ 饭票退款
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        if id is not None:
            transaction.id = id
        transaction.refunded_transaction_id = refunded_transaction_id
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee * -1
        transaction.paid_fee = paid_fee * -1
        transaction.type = wallet_pb.Transaction.FANPIAO_REFUND
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_self_dining_payment_with_coupon_package_prepay(
            self, user_id=None, merchant_id=None, pay_method=None, bill_fee=None, paid_fee=None):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_common_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.pay_method = pay_method
        transaction.type = wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_member_card_recharge_refund(self, user_id, merchant_id, pay_method, paid_fee, bill_fee, id=None):
        """ 会员卡储值退款
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        if id is not None:
            transaction.id = id
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee * -1
        transaction.paid_fee = paid_fee * -1
        transaction.type = wallet_pb.Transaction.MEMBER_CARD_RECHARGE_REFUND
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_bonus_to_user(self, payer_id, payee_id, pay_method, bill_fee, paid_fee):
        """ 给用户发平台奖励金
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.use_coupon_id = ""
        transaction.type = wallet_pb.Transaction.BONUS
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_activity_bonus(self, payer_id, payee_id, pay_method, bill_fee, paid_fee):
        """ 给用户发平台奖励金
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.paid_time = date_utils.timestamp_second()
        transaction.type = wallet_pb.Transaction.ACTIVITY_BONUS
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_direct_pay_transaction(self, payer_id, payee_id, payee_store_id, bill_fee, paid_fee, pay_method):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        if payer_id is not None:
            transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.payee_store_id = payee_store_id
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.pay_method = pay_method
        transaction.type = wallet_pb.Transaction.DIRECT_PAY
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_direct_pay_with_coupon_package_purchase_transaction(self, payer_id, payee_id, bill_fee, paid_fee, pay_method):
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.bill_fee = bill_fee
        transaction.paid_fee = paid_fee
        transaction.pay_method = pay_method
        transaction.type = wallet_pb.Transaction.DIRECT_PAY_WITH_COUPON_PACKAGE_PURCHASE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_self_dining_ordering_payment_prepay(self, payer_id, payee_id, payee_store_id, pay_method,
                                                   bill_fee, paid_fee, coupon_id):
        """ 个人点餐支付
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED  # 刚建的交易都是CREATED状态
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.payee_store_id = payee_store_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        if coupon_id is not None:
            transaction.use_coupon_id = coupon_id
        transaction.type = wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_self_dining_payment_prepay(self, payer_id, payee_id, payee_store_id, pay_method,
                                          bill_fee, paid_fee, use_coupon_id):
        """ 个人支付
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.state = wallet_pb.Transaction.CREATED  # 刚建的交易都是CREATED状态
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.payee_store_id = payee_store_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.use_coupon_id = use_coupon_id or ""
        transaction.type = wallet_pb.Transaction.SELF_DINING_PAYMENT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_group_dining_payment_prepay(self, dining, pay_method, bill_fee, paid_fee):
        """ 饭局支付prepay
        Args:
            dining: (GroupDiningEvent)饭局
            pay_method: (GroupDiningEvent.PayMethod)支付方式
            bill_fee: (int)消费金额
            paid_fee: (int)实付金额
        Return:
            transaction: (wallet_pb.Transaction)交易实体
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = dining.director_id
        transaction.payee_id = dining.merchant_id
        transaction.payee_store_id = dining.store_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.type = wallet_pb.Transaction.GROUP_DINING_PAYMENT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_group_dish_order_payment_prepay(self, dining, pay_method, bill_fee, paid_fee, coupon_id):
        """ 扫码饭局支付prepay
        """
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = dining.director_id
        transaction.payee_id = dining.merchant_id
        transaction.payee_store_id = dining.store_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.use_coupon_id = coupon_id
        transaction.type = wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_vip_membership_subscribe_transaction(self, payer_id, payee_id, bill_fee, paid_fee, pay_method):
        pay_method = self.__pay_method_to_enum(pay_method)
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.type = wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_vip_membership_recharge_transaction(self, payer_id, payee_id, bill_fee, paid_fee, pay_method):
        pay_method = self.__pay_method_to_enum(pay_method)
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.type = wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_vip_membership_subscribe_recharge_combine_transaction(self, payer_id, payee_id, bill_fee, paid_fee, pay_method):
        pay_method = self.__pay_method_to_enum(pay_method)
        transaction = wallet_pb.Transaction()
        transaction.create_time = date_utils.timestamp_second()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.type = wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_user_transfer_prepay(self, payer_id, payee_id, bill_fee, transaction_type):
        """ 生成转账的transaction
            1. 饭局转账
            2. 用户给用户转账
        Args:
            payer_id: (string)转出人的ID
            payee_id: (string)转入人的ID
            bill_fee: (int)转账金额
            transaction_type: (int) wallet.Transaction.TransactionType
        Return:
            (wallet_pb.Transaction)结构体
        """
        transaction = wallet_pb.Transaction()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = payee_id
        transaction.bill_fee = bill_fee
        transaction.paid_fee = bill_fee
        transaction.type = transaction_type
        transaction.pay_method = wallet_pb.Transaction.WALLET
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_group_dining_transfer_prepay(self, payer_id, dining, pay_method, bill_fee):
        """ 饭局转帐
        """
        transaction = wallet_pb.Transaction()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = dining.director_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(bill_fee)
        transaction.type = wallet_pb.Transaction.GROUP_DINING_TRANSFER
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_group_dish_order_transfer(self, payer_id, dining, pay_method, bill_fee):
        """ 扫码饭局转帐
        """
        transaction = wallet_pb.Transaction()
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.create_time = date_utils.timestamp_second()
        transaction.id = id_manager.generate_transaction_id()
        transaction.payer_id = payer_id
        transaction.payee_id = dining.director_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(bill_fee)
        transaction.type = wallet_pb.Transaction.GROUP_DISH_ORDER_TRANSFER
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_red_packet_deposit_prepay(self, user_id, bill_fee, payer_id):
        """ 抽红包
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.paid_time = transaction.create_time
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.payee_id = user_id
        transaction.payer_id = payer_id
        transaction.bill_fee = int(bill_fee)
        transaction.type = wallet_pb.Transaction.RED_PACKET_DEPOSIT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)

    def handle_cash_topup_prepay(self, payer_id, pay_method, bill_fee):
        """ 充值
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.payer_id = payer_id
        transaction.payee_id = business_ops_constants.SHILAI_MCH_ID
        transaction.pay_method = pay_method
        transaction.bill_fee = bill_fee
        transaction.paid_fee = bill_fee
        transaction.state = wallet_pb.Transaction.CREATED
        transaction.type = wallet_pb.Transaction.CASH_DEPOSIT
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_cash_withdraw(self, user_id, amount, pay_method, transfer_user_wallet=None):
        """ 用户钱包提现
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.type = wallet_pb.Transaction.CASH_WITHDRAW
        transaction.state = wallet_pb.Transaction.ORDERED
        transaction.payer_id = user_id
        transaction.payee_id = user_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(amount)
        transaction.paid_fee = int(amount)
        if transfer_user_wallet:
            transaction.transfer_user_wallet.CopyFrom(transfer_user_wallet)
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_fanpiao_balance_withdraw(self, user_id, bill_fee, paid_fee, pay_method, transfer_user_wallet=None):
        """ 用户饭票提现
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.type = wallet_pb.Transaction.FANPIAO_BALANCE_WITHDRAW
        transaction.state = wallet_pb.Transaction.ORDERED
        transaction.payer_id = user_id
        transaction.payee_id = user_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        if transfer_user_wallet:
            transaction.transfer_user_wallet.CopyFrom(transfer_user_wallet)
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def handle_scan_code_pay(
            self, user_id, merchant_id, store_id, bill_fee, paid_fee, no_discount_bill_fee,
            pay_method):
        """ 扫码买单
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.type = wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT
        transaction.state = wallet_pb.Transaction.ORDERED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        transaction.payee_store_id = store_id
        transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        transaction.no_discount_bill_fee = no_discount_bill_fee
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def shilai_pos_scan_qrcode_pay(self, user_id, merchant_id, bill_fee, paid_fee, pay_method=None):
        """ 时来pos机扫饭票支付
        """
        transaction = wallet_pb.Transaction()
        transaction.id = id_manager.generate_transaction_id()
        transaction.create_time = date_utils.timestamp_second()
        transaction.type = wallet_pb.Transaction.SHILAI_POS_ORDER_PAYMENT
        transaction.state = wallet_pb.Transaction.ORDERED
        transaction.payer_id = user_id
        transaction.payee_id = merchant_id
        if pay_method is None:
            transaction.pay_method = wallet_pb.Transaction.FANPIAO_PAY
        else:
            transaction.pay_method = pay_method
        transaction.bill_fee = int(bill_fee)
        transaction.paid_fee = int(paid_fee)
        TransactionDataAccessHelper().add_or_update_transaction(transaction)
        return transaction

    def create_union_transaction(self, main_transaction):
        union_transaction = wallet_pb.UnionPayTransaction()
        union_transaction.transaction_id = main_transaction.id
        union_transaction.create_time = date_utils.timestamp_second()
        union_transaction.update_time = date_utils.timestamp_second()
        TransactionDataAccessHelper().add_or_update_union_pay_transaction(union_transaction)
        return union_transaction

    def update_union_transaction(self, union_transaction, sub_transaction_ids=None):
        if sub_transaction_ids is not None:
            for sub_transaction_id in sub_transaction_ids:
                if sub_transaction_id in union_transaction.sub_transaction_ids:
                    continue
                union_transaction.sub_transaction_ids.append(sub_transaction_id)
        union_transaction.update_time = date_utils.timestamp_second()
        TransactionDataAccessHelper().add_or_update_union_pay_transaction(union_transaction)

    def get_union_transaction(self, transaction_id=None, sub_transaction_id=None):
        transaction_da = TransactionDataAccessHelper()
        union_transaction = transaction_da.get_union_pay_transaction(
            transaction_id=transaction_id,
            sub_transaction_id=sub_transaction_id
        )
        return union_transaction

    def get_transactions_by_union_transaction(self, union_transaction):
        if union_transaction is None:
            return None
        transaction_da = TransactionDataAccessHelper()
        ids = list(union_transaction.sub_transaction_ids)
        ids.append(union_transaction.transaction_id)
        transactions = transaction_da.get_transactions(ids=ids)
        result = {t.id: t for t in transactions}
        return result

    # TODO: 更新transaction加锁
    def update_transaction(self, transaction):
        TransactionDataAccessHelper().add_or_update_transaction(transaction)

    def set_transaction_success(self, transaction):
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.paid_time = date_utils.timestamp_second()
        return self.update_transaction(transaction)

    def get_dining_payment_type_transactions(
            self, payee_id=None, payee_store_id=None,
            payer_id=None, state=None,
            start_time=None, end_time=None, type_list=[], pay_method=None):
        """ 获取带Payment类型的账单
        目前支持 SELF_DINING_PAYMENT, GROUP_DINING_PAYMENT
        """
        transaction_da = TransactionDataAccessHelper()
        transactions = transaction_da.get_transactions(
            payee_id=payee_id, payee_store_id=payee_store_id,
            payer_id=payer_id, state=state, multi_types=type_list,
            start_time=start_time, end_time=end_time, pay_method=pay_method)
        return transactions

    def calculate_policy(self, dining, bill_fee, user_cnt):
        """ 根据支付的钱和用户数,计算应该使用哪个policy
        """
        if user_cnt <= 1:
            return None
        policies = dining.group_dining_coupon_policies
        for policy in reversed(policies):
            if policy.target_diner_count > 1 and policy.least_cost <= bill_fee and user_cnt >= policy.target_diner_count:
                return policy
        return None

    def user_bill(self, user_id, page=None, size=None):
        """ 用户账单
        """
        transactions = TransactionDataAccessHelper().get_transactions(payer_id=user_id,
                                                                      page=page, size=size,
                                                                      state=wallet_pb.Transaction.SUCCESS,
                                                                      orderby=[("paidTime", -1)])
        result = []
        for transaction in transactions:
            user_bill = user_bill_pb.UserBill()
            total_pay = TransactionDataAccessHelper().count_paid(user_id)
            if len(total_pay) > 0:
                user_bill.total_pay = total_pay[0]["count"]
            total_incoming = TransactionDataAccessHelper().count_incoming(user_id)
            if len(total_incoming) > 0:
                user_bill.total_incoming = total_incoming[0]["count"]
            bill = user_bill.bill.add()
            bill.pay_method = transaction.pay_method
            bill.type = transaction.type
            bill.paid_time = transaction.paid_time
            bill.paid_fee = transaction.paid_fee
            if transaction.type == wallet_pb.Transaction.GROUP_DINING_TRANSFER:
                user = UserDataAccessHelper().get_user(transaction.payee_id)
                bill.headimgurl = user.member_profile.head_image_url
            else:
                merchant = MerchantDataAccessHelper().get_merchant(transaction.payee_id)
                bill.headimgurl = merchant.basic_info.logo_url
            result.append(user_bill)
        return result

    def add_inexistence_transaction(self, **kargs):
        """ 有可能在收到回调时,transaction在从库中还没有入库,从而查询不到transaction,
        把相关数据保存下来,方便后面数据恢复,也可以在脚本中进行自动重试
        """
        obj = wallet_pb.InexistenceTransaction()
        transaction_id = kargs.get("transaction_id")
        order_id = kargs.get("order_id")
        merchant_id = kargs.get("merchant_id")
        fanpiao_category_id = kargs.get("fanpiao_category_id")
        wishlist_id = kargs.get("whishlist_id")
        coupon_package_id = kargs.get("coupon_package_id")
        obj.transaction_id = transaction_id
        obj.update_timestamp = int(time.time())
        if order_id is not None:
            obj.order_id = order_id
        if merchant_id is not None:
            obj.merchant_id = merchant_id
        if fanpiao_category_id is not None:
            obj.fanpiao_category_id = fanpiao_category_id
        if wishlist_id is not None:
            obj.wishlist_id = wishlist_id
        if coupon_package_id is not None:
            obj.coupon_package_id = coupon_package_id
        transaction_da = TransactionDataAccessHelper()
        transaction_da.add_or_update_inexistence_transaction(obj)

    def get_recently_transactions(self, merchant, size=50, before_timestamp=None, type=None):
        transaction_da = TransactionDataAccessHelper()
        end_time = int(time.time())
        if before_timestamp is None:
            start_time = end_time - 24 * 60 * 60
        else:
            start_time = before_timestamp
        state = wallet_pb.Transaction.SUCCESS
        transactions = transaction_da.get_transactions(
            payee_id=merchant.id, start_time=start_time, end_time=end_time,
            state=state, type=type, size=size, orderby=[("paidTime", -1)])
        return transactions

    @classmethod
    def get_transaction_pay_method_name(cls, transaction):
        pay_method_name = "其它"
        if transaction.pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
            if transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                pay_method_name = "支付宝"
            elif transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                pay_method_name = "微信"
        elif transaction.pay_method == wallet_pb.Transaction.WALLET:
            pay_method_name = "智能营销买单"
        elif transaction.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            pay_method_name = "微信"
        elif transaction.pay_method == wallet_pb.Transaction.ALIPAY:
            pay_method_name = "支付宝"
        elif transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            pay_method_name = "智能营销买单"
        elif transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            pay_method_name = "会员储值支付"
        return pay_method_name

    def get_transaction_by_id(self, transaction_id):
        transaction_da = TransactionDataAccessHelper()
        transaction = transaction_da.get_transaction_by_id(transaction_id)
        return transaction

    def get_membership_deposit_transactions(
            self, payee_id, start_timestamp, end_timestamp):
        """ 获取时间段内的储值
        count: 储值笔数
        shilai_fee: 储值金额,这个金额需要由时来转账(或者分账)到商户
        """
        Ret = namedtuple("Ret", ["count", "shilai_fee"])
        type = wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE
        transactions = self.transaction_da.get_transactions(
            payee_id=payee_id,
            start_time=start_timestamp,
            end_time=end_timestamp,
            type=type,
            state=wallet_pb.Transaction.SUCCESS
        )
        count, shilai_fee = 0, 0
        for transaction in transactions:
            count += 1
            shilai_fee += transaction.paid_fee
        return Ret(count, shilai_fee)

    def count_today_user_buy_fanpiao(self, user, merchant_id):
        """ 这个用户今天一共购买了几次饭票
        """
        today_zero = date_utils.get_today_zero()
        tomorrow_zero = today_zero + date_utils.ONE_DAY
        transactions = self.transaction_da.get_transactions(
            payer_id=user.id,
            payee_id=merchant_id,
            type=wallet_pb.Transaction.FANPIAO_PURCHASE,
            start_time=today_zero,
            end_time=tomorrow_zero,
            state=wallet_pb.Transaction.SUCCESS
        )
        result = self.__filter_count_transactions(transactions)
        logger.info(f"{user.member_profile.nickname} {merchant_id} 购买饭票支付次数: {result}")
        return result

    def count_today_user_use_fanpiao(self, user, merchant_id):
        """ 这个用户今天一共使用了几次饭票
        """
        today_zero = date_utils.get_today_zero()
        tomorrow_zero = today_zero + date_utils.ONE_DAY
        types = [
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
            wallet_pb.Transaction.DIRECT_PAY
        ]
        transactions = self.transaction_da.get_transactions(
            payer_id=user.id,
            payee_id=merchant_id,
            multi_types=types,
            start_time=today_zero,
            end_time=tomorrow_zero,
            state=wallet_pb.Transaction.SUCCESS,
            pay_method=wallet_pb.Transaction.FANPIAO_PAY
        )
        result = self.__filter_count_transactions(transactions)
        logger.info(f"{user.member_profile.nickname} {merchant_id} 使用饭票支付次数: {result}")
        return result

    def __filter_count_transactions(self, transactions):
        count = 0
        for transaction in transactions:
            refund_transaction  = self.transaction_da.get_transactions(
                refunded_transaction_id=transaction.id
            )
            if not refund_transaction:
                count += 1
        return count

    def __pay_method_to_enum(self, pay_method):
        if isinstance(pay_method, str):
            pay_method = wallet_pb.Transaction.PayMethod.Value(pay_method)
        return pay_method

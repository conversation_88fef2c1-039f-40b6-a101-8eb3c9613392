# -*- coding: utf-8 -*-

import logging

import proto.coupons_pb2 as coupons_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.page.coupon_package_pb2 as page_coupon_package_pb
import proto.coupon_category_pb2 as coupon_category_pb
from common.utils import id_manager
from common.utils import date_utils
from common.utils.number_utils import NumberUtils
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.coupon_manager import CouponManager
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.coupon_package_da_helper import CouponPackageDataAccessHelper
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from service import errors
from service import error_codes


logger = logging.getLogger(__name__)


class CouponPackageManager():
    def __init__(self, user_id=None, coupon_package_id=None, merchant_id=None, paid_fee=None, bill_fee=None,
                 pay_method=None, transaction_id=None, return_url=None, transaction=None, coupon_package=None,
                 user=None, merchant=None):
        self.user_id = user_id
        self.paid_fee = paid_fee
        self.bill_fee = bill_fee
        self.pay_method = pay_method
        self.coupon_package_id = coupon_package_id
        self.merchant_id = merchant_id
        self.coupon_package = None
        self.return_url = return_url
        self.transaction_id = transaction_id

        self.init_transaction_info(transaction=transaction)
        self.init_user_info(user=user)
        self.init_merchant_info(merchant=merchant)
        self.init_coupon_package_info(coupon_package=coupon_package)

        self.bill_fee = NumberUtils.safe_round(self.bill_fee)
        self.paid_fee = NumberUtils.safe_round(self.paid_fee)

    def init_user_info(self, user=None, user_id=None):
        if user is not None:
            self.user = user
        else:
            if self.user_id is not None:
                self.user = UserDataAccessHelper().get_user(self.user_id)

    def init_merchant_info(self, merchant=None, merchant_id=None):
        if merchant is not None:
            self.merchant = merchant
        else:
            if self.merchant_id is not None:
                self.merchant = MerchantDataAccessHelper().get_merchant(self.merchant_id)
                if not self.merchant:
                    raise errors.MerchantNotFoundError()

    def init_coupon_package_info(self, coupon_package=None, coupon_package_id=None):
        if coupon_package is not None:
            self.coupon_package = coupon_package
        else:
            if self.coupon_package_id is not None:
                for package in self.merchant.preferences.coupon_config.coupon_packages:
                    if package.id == self.coupon_package_id:
                        self.coupon_package = package
                        break
                if not self.coupon_package:
                    raise errors.CouponPackageNotFound()

    def init_transaction_info(self, transaction=None, transaction_id=None):
        logger.info("transaction: {}, transactionId: {}".format(transaction, transaction_id))
        if transaction is not None:
            self.transaction = transaction
            self.user_id = self.transaction.payer_id
            self.merchant_id = self.transaction.payee_id
            self.bill_fee = self.transaction.bill_fee
            self.paid_fee = self.transaction.paid_fee
            self.pay_method = self.transaction.pay_method
        else:
            if self.transaction_id is not None:
                self.transaction = TransactionDataAccessHelper().get_transaction_by_id(self.transaction_id)
                self.user_id = self.transaction.payer_id
                self.merchant_id = self.transaction.payee_id
                self.bill_fee = self.transaction.bill_fee
                self.paid_fee = self.transaction.paid_fee
                self.pay_method = self.transaction.pay_method

    def prepay(self):
        """ 购买券包
        """
        if self.coupon_package.coupon_package_spec.sell_price != self.paid_fee:
            logger.info('购买券包金额错误: 支付金额: {}, 券包价格: {}'.format(
                self.paid_fee, self.coupon_package.coupon_package_spec.sell_price))
            raise errors.BuyCouponPackageWrongPaidFee()
        self.transaction = TransactionManager().handle_buy_coupon_package_prepay(
            self.user.id, self.merchant.id, self.pay_method, self.paid_fee)
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 采用微信支付
            result = payment_manager.prepay(transaction=self.transaction)
            logger.info('wechat_pay _buy_coupon_package_prepay prepay result: {}'.format(result))
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = payment_manager.prepay(transaction=self.transaction, return_url=self.return_url, user_id=self.user.id)
            logger.info("alipay _buy_coupon_package_prepay prepay result: {}".format(result))
        elif self.pay_method == wallet_pb.Transaction.NONE_EXISTS_PAY:
            result = payment_manager.prepay(transaction=self.transaction)
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") == error_codes.SUCCESS:
            self.generate_coupon_package()
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.NONE_EXISTS_PAY:
                TransactionManager().update_transaction(self.transaction)
            result['transactionId'] = self.transaction.id
        return result

    def notification(self, union_pay=False):
        """ 购买券包
        """
        coupon_package = CouponDataAccessHelper().get_coupon_package(transaction_id=self.transaction.id)
        coupon_package_spec = None  # self.merchant.preferences.coupon_config.coupon_packages[0].coupon_package_spec
        coupon_packages = self.merchant.preferences.coupon_config.coupon_packages
        for _cp in coupon_packages:
            if _cp.coupon_category_id == coupon_package.coupon_category_id:
                coupon_package_spec = _cp.coupon_package_spec
                break

        if not coupon_package_spec:
            raise errors.CouponPackageNotFound()

        number = coupon_package_spec.coupon_count
        coupons = []
        for _ in range(0, number):
            coupon = CouponManager().issue_coupon_to_user(
                coupon_package.coupon_category_id, self.user.id, state=coupons_pb.Coupon.ACCEPTED)
            logger.info("generate coupon for coupon_package: {}, {}, {}".format(self.transaction.id, coupon_package.id, coupon.id))
            coupon_package.coupon_ids.append(coupon.id)
            coupons.append(coupon)
        CouponDataAccessHelper().add_or_update_coupon_package(coupon_package)

        if not union_pay and not self.transaction.ledgered:
            payment_manager = PaymentManager(self.pay_method, merchant=self.merchant)
            payment_manager.coupon_package_launch_ledger(self.transaction)

        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        TransactionManager().update_transaction(self.transaction)
        return coupons

    def generate_coupon_package(self):
        coupon_package = coupons_pb.CouponPackage()
        coupon_package.id = id_manager.generate_common_id()
        coupon_package.coupon_category_id = self.coupon_package.coupon_category_id
        coupon_package.create_time = date_utils.timestamp_second()
        coupon_package.transaction_id = self.transaction.id
        CouponDataAccessHelper().add_or_update_coupon_package(coupon_package)

    def coupon_package_refund(self, transaction_id, transaction=None, reason=None, phone=None):
        """ 券包退款
        1. 财付通退款通过回调来处理业务逻辑
        2. 其它在发起退款时就处理业务逻辑
        """
        transaction_da = TransactionDataAccessHelper()
        coupon_da = CouponDataAccessHelper()
        if not transaction:
            transaction = transaction_da.get_transaction_by_id(transaction_id)
        if not transaction:
            raise errors.TransactionNotExists()
        if transaction.payer_id != self.user.id:
            raise errors.CouponPackageNotYours()

        # 检查券包能否退款,不能的话会直接抛出不能退款提示给用户
        coupon_package = coupon_da.get_coupon_package(transaction_id=transaction.id)
        if not coupon_package:
            raise errors.CouponPackageNotFound()
        coupons = self.check_coupon_package_can_refund(coupon_package)

        # 发起退款,如果发起退款失败,会直接抛出提示给用户
        payment_manager = PaymentManager(
            pay_method=transaction.pay_method, is_refund=True, merchant_id=transaction.payee_id)
        payment_manager.coupon_package_refund(transaction, reason=reason)

        if transaction.pay_method not in PaymentManager.get_refund_callback_pay_methods():
            # 如果支付方式不是以回调的方式来处理业务逻辑,则把coupon_package,和coupons的状态都设置为删除
            self.refund_coupon_package_without_callback(coupon_package=coupon_package, coupons=coupons, reason=reason, phone=phone)
        else:
            status = coupons_pb.CouponPackage.REFUNDING
            self.update_coupon_package_status(coupon_package=coupon_package, status=status)
            state = coupons_pb.Coupon.REFUNDING
            self.update_coupons_state(state=state, coupons=coupons)

    def refund_coupon_package_without_callback(self, coupon_package, coupons=None, reason=None, phone=None):
        """ 对于某些不使用回调进行退款的支付方式,申请退款后直接修改券包的状态
        """
        status = coupons_pb.CouponPackage.REFUND
        self.update_coupon_package_status(coupon_package=coupon_package, reason=reason, phone=phone, status=status)
        state = coupons_pb.Coupon.DELETED
        self.update_coupons_state(state=state, coupons=coupons, coupon_ids=coupon_package.coupon_ids)

    def refund_coupon_package_with_callback(self, transaction):
        coupon_da = CouponDataAccessHelper()
        coupon_package = coupon_da.get_coupon_package(transaction_id=transaction.id)
        status = coupons_pb.CouponPackage.REFUND
        self.update_coupon_package_status(coupon_package=coupon_package, status=status)
        state = coupons_pb.Coupon.DELETED
        self.update_coupons_state(state=state, coupon_ids=coupon_package.coupon_ids)

    def rollback_refund_coupon_package(self, transaction):
        """ 在那些通过退款回调处理业务的支付方式,如果收到的回调是失败,那么就要把用户的券包的状态改为可用状态
        """
        coupon_da = CouponDataAccessHelper()
        coupon_package = coupon_da.get_coupon_package(transaction_id=transaction.id)
        status = coupons_pb.CouponPackage.NORMAL
        self.update_coupon_package_status(coupon_package=coupon_package, status=status)
        state = coupons_pb.Coupon.ACCEPTED
        self.update_coupons_state(state=state, coupon_ids=coupon_package.coupon_ids)

    def update_coupon_package_status(self, coupon_package=None, reason=None, phone=None, status=None):
        coupon_da = CouponDataAccessHelper()
        coupon_package.status = coupons_pb.CouponPackage.REFUND
        if reason is not None:
            coupon_package.refund_reason = reason
        if phone is not None:
            coupon_package.refund_phone = phone
        coupon_da.add_or_update_coupon_package(coupon_package)

    def check_coupon_package_can_refund(self, coupon_package):
        """ 检测券包是否能退款,以下情况同时满足,才能申请退款
        1. coupon_package 的状态要为NORMAL
        2. coupon_packages.coupon_ids 指向的所有券的状态为 ACCEPTED
        """
        coupon_da = CouponDataAccessHelper()
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_manager = CouponManager()
        if not coupon_package:
            raise errors.CouponPackageNotFound()
        if coupon_package.status != coupons_pb.CouponPackage.NORMAL:
            raise errors.CouponPackageCannotRefund()
        coupon_category = coupon_category_da.get_coupon_category(id=coupon_package.coupon_category_id)
        if not coupon_category:
            raise errors.Error(err=error_codes.COUPON_CATEGORY_NOT_FOUND)
        if coupon_category.issue_scene != coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            raise errors.Error(err=error_codes.COUPON_CATEGORY_CANNOT_REFUND)
        coupons = []
        for coupon_id in coupon_package.coupon_ids:
            coupon = coupon_da.get_coupon_by_id(coupon_id)
            if coupon and coupon.state != coupons_pb.Coupon.ACCEPTED:
                raise errors.CouponPackageCannotRefund()
            if coupon_manager.check_coupon_expired(coupon, coupon_category):
                raise errors.Error(err=error_codes.COUPON_EXPIRED_CANNOT_REFUND)
            coupons.append(coupon)
        return coupons

    def update_coupons_state(self, state, coupons=None, coupon_ids=None):
        coupon_da = CouponDataAccessHelper()
        if coupons is not None:
            for coupon in coupons:
                coupon.state = state
                coupon_da.update_or_create_coupon(coupon)
        elif coupon_ids is not None:
            for coupon_id in coupon_ids:
                coupon = coupon_da.get_coupon_by_id(coupon_id)
                if not coupon:
                    continue
                coupon.state = state
                coupon_da.update_or_create_coupon(coupon)

    def get_merchant_coupon_packages(self):
        coupon_packages = self.merchant.preferences.coupon_config.coupon_packages
        if len(coupon_packages) == 0:
            return None
        coupon_package_da = CouponPackageDataAccessHelper()
        merchant_coupon_package_vo = page_coupon_package_pb.MerchantCouponPackageListVO()
        for coupon_package in coupon_packages:
            if coupon_package.state != coupon_category_pb.CouponCategory.ACTIVE:
                continue
            number = coupon_package.coupon_package_spec.coupon_count
            total_price = coupon_package.coupon_package_spec.reduce_cost * number
            original_price = coupon_package.coupon_package_spec.total_value
            price = coupon_package.coupon_package_spec.sell_price
            valid_days = coupon_package.coupon_package_spec.coupon_category_spec.date_info.fixed_term
            least_cost = coupon_package.coupon_package_spec.least_cost
            reduce_cost = coupon_package.coupon_package_spec.reduce_cost

            coupon_package_vo = page_coupon_package_pb.MerchantCouponPackageListVO.CouponPackage()
            for i in range(number):
                coupon_vo = coupon_package_vo.coupons.add()
                coupon_vo.reduce_cost = reduce_cost
            coupon_package_vo.id = coupon_package.id
            coupon_package_vo.original_price = original_price
            coupon_package_vo.price = price
            coupon_package_vo.name = coupon_package.name
            coupon_package_vo.number = number
            coupon_package_vo.total_price = total_price
            coupon_package_vo.valid_days = valid_days
            coupon_package_vo.display_scene = coupon_package.coupon_package_spec.coupon_category_spec.display_scene
            coupon_package_vo.selling_quantity = coupon_package_da.count_coupon_package(
                coupon_category_id=coupon_package.coupon_category_id, status=coupons_pb.CouponPackage.NORMAL)
            merchant_coupon_package_vo.coupon_packages[least_cost].CopyFrom(coupon_package_vo)
            merchant_coupon_package_vo.bill_fee_ladder.append(coupon_package.coupon_package_spec.least_cost)
        merchant_coupon_package_vo.bill_fee_ladder.sort()
        return merchant_coupon_package_vo

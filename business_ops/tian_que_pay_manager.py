# -*- coding: utf-8 -*-

"""
Filename: tiaojue_pay_manager.py
Date: 2020-07-17 10:59:36
Title: 天阙聚合支付
文档: http://doc.tianquetech.com/web/#/3?page_id=193
"""

import os
import base64
import collections
import logging
import socket
import requests
import time
import itertools
import json
import urllib3
from base64 import b64decode
from collections import namedtuple
from datetime import datetime
from urllib.parse import urlparse

import pytz
from Crypto.Hash import SHA
from Crypto.Signature import PKCS1_v1_5
from Crypto.PublicKey import RSA
from google.protobuf import json_format
from flask import request

import proto.ordering.registration_pb2 as registration_pb
import proto.payment_pb2 as payment_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops import constants
from business_ops.pay_manager import PayManager
from business_ops.transaction_manager import TransactionManager
from business_ops.merchant_manager import MerchantManager
from common.config import config
from common.config import DefaultConfig
from common.utils import date_utils
from common.utils import id_manager
from common import constants as common_constants
from dao.user_da_helper import UserDataAccessHelper
from dao.payment_da_helper import PaymentDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import error_codes
from service import errors
from service import base_responses
from common.base_app import record_cpu_cost

# from common import http_session

logger = logging.getLogger(__name__)

_CIPHER = None
_VERIFIER = None

http = urllib3.PoolManager(cert_reqs='CERT_NONE', maxsize=100)


class TianQuePayManager(PayManager):
    # 联合支付
    TIAN_QUE_PAY_UNIFIED_ORDER = "/order/jsapiScan"
    # 商户分账签约
    TIAN_QUE_PAY_MERCHANT_SIGN = "/merchant/sign/getUrl"
    # 实名认证
    TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY = "/merchant/realName/commitApply"
    # 支付查询
    TIAN_QUE_PAY_TRADE_QUERY = "/query/tradeQuery"
    # 申请退款
    TIAN_QUE_PAY_REFUND = "/order/refund"
    # 退款查询
    TIAN_QUE_PAY_REFUND_QUERY = "/query/refundQuery"
    # 分账
    TIAN_QUE_PAY_LAUNCH_LEDGER = "/query/ledger/launchLedger"
    # 实名认证查询
    TIAN_QUE_PAY_REAL_NAME_COMMIT_QUERY = "/merchant/realName/queryApplyInfo"
    # 撤销实名认证申请
    TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_UNDO = "/merchant/realName/backApplyBill"
    # 转账
    TIAN_QUE_PAY_CAPITAL_FUNDMANAGE_ORGTRANSFER = "/capital/fundManage/orgTransfer"
    # 设置分账权限(分账签约的商户把分账权限授权到时来)
    TIAN_QUE_PAY_QUERY_LEDGER_SET_MNO_ARRAY = "/query/ledger/setMnoArray"
    # 查询分账信息
    TIAN_QUE_PAY_QUERY_LEDGER = "/query/ledger/queryLedgerAccount"
    # 转账查询
    TIAN_QUE_PAY_GET_TRANSFER_INFO = "/capital/fundManage/getTransferInfo"
    # 交易明细查询
    TIAN_QUE_PAY_GET_FILE_URL = "/capital/fileDownload/getFileUrl"
    # 商户信息查询
    TIAN_QUE_PAY_MERCHANT_INFO_QUERY = "/merchant/merchantInfoQuery"
    # 营销余额查询
    TIAN_QUE_PAY_QUERY_BALANCE = "/capital/query/queryBalance"
    # 结算查询
    TIAN_QUE_PAY_QUERY_SETTLEMENT = "/capital/query/querySettlement"
    # 查询商户分账状态
    TIAN_QUE_PAY_QUERY_SIGN_CONTRACT = "/merchant/sign/querySignContract"
    # 退款查询
    TIAN_QUE_PAY_QUERY_REFUND = "/query/refundQuery"
    # 查询转账信息
    TIAN_QUE_PAY_QUERY_TRANSFER_INFO = "/capital/fundManage/getTransferInfo"
    # 支付宝码蚁门店创建
    TIAN_QUE_PAY_ANT_SHOP_CREATE = "/merchant/antShop/create"
    # 支付这蚂蚁门店查询
    TIAN_QUE_PAY_ANT_SHOP_QUERY = "/merchant/antShop/query"
    # 根据创建时返回的orderId查询支付宝门店信息
    TIAN_QUE_PAY_ANT_SHOP_ORDER_QUERY = "/merchant/antShop/orderQuery"
    # 天阙商户信息修改
    MERCHANT_SETUP = "/merchant/merchantSetup"
    # 用户被扫支付
    TIAN_QUE_PAY_REVERSE_SCAN = "/order/reverseScan"

    TRADE_FAILED = -1
    TRADE_SUCCESS = 0
    TRADE_PAYING = 1

    ip = None

    def __init__(self, *args, **kargs):
        global _CIPHER, _VERIFIER
        super(TianQuePayManager, self).__init__(*args, **kargs)
        self._cipher = _CIPHER
        if self._cipher is None:
            self._reflush_cipher()
        self._verifier = _VERIFIER
        if self._verifier is None:
            self._reflush_verifier()

        self.tian_que_pay_orgid = constants.TIAN_QUE_PAY_ORGID
        self.tian_que_pay_domain = constants.TIAN_QUE_PAY_DOMAIN
        self.tian_que_pay_shilai_mno = constants.TIAN_QUE_PAY_SHILAI_MNO
        self.tian_que_pay_shilai_launch_ledger_mno = constants.TIAN_QUE_PAY_SHILAI_LAUNCH_LEDGER_MNO
        self.tian_que_pay_shilai_commission_rate = constants.TIAN_QUE_PAY_SHILAI_COMMISSION_RATE

        self.pay_type = kargs.get("pay_type", "WECHAT")
        self.pay_way = kargs.get("pay_way", "03")
        self._merchant_da = None

        merchant = kargs.get("merchant")
        merchant_id = kargs.get("merchant_id")
        mno = kargs.get("mno")
        self.mno = mno
        self.tian_que_pay_info = None

        self.merchant = merchant
        if merchant_id and not self.merchant:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)

        if self.merchant and self.tian_que_pay_info is None:
            self.tian_que_pay_info = PaymentDataAccessHelper().get_tian_que_pay_info(
                merchant_id=self.merchant.id, state=payment_pb.TianQuePayInfo.NORMAL
            )
            if self.tian_que_pay_info:
                self.mno = self.tian_que_pay_info.mno
        if self.tian_que_pay_info is None and mno is not None:
            self.tian_que_pay_info = payment_pb.TianQuePayInfo()
            self.tian_que_pay_info.mno = mno
            self.tian_que_pay_info.merchant_id = merchant_id

        self.merchant_manager = MerchantManager(merchant=self.merchant)

        if self.ip is None:
            domain = os.environ.get("SERVICE_DOMAIN", "shilai.zhiyi.cn")
            self.ip = self._parse_domain(domain)

    def _reflush_cipher(self):
        global _CIPHER
        logger.info(f"loading _reflush_cipher")
        self.prikey_string = open(constants.TIAN_QUE_PAY_SHILAI_PRIVACE_KEY_FILE, "r").read()
        private_key = RSA.importKey(self.prikey_string)
        self._cipher = PKCS1_v1_5.new(private_key)
        _CIPHER = self._cipher

    def _reflush_verifier(self):
        global _VERIFIER
        logger.info(f"loading _reflush_verifier")
        self.pubkey_string = open(constants.TIAN_QUE_PAY_PUBLIC_KEY_FILE, "r").read()
        self._verifier = PKCS1_v1_5.new(RSA.importKey(self.pubkey_string))
        _VERIFIER = self._verifier

    @staticmethod
    def _parse_domain(url):
        if not urlparse(url).scheme:
            url = f"http://{url}"
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        domain = domain.replace("www.", "")
        ip = socket.gethostbyname(domain)
        return ip

    @property
    def merchant_da(self):
        if self._merchant_da is not None:
            return self._merchant_da
        self._merchant_da = MerchantDataAccessHelper()
        return self._merchant_da

    @record_cpu_cost
    def rsa_sign(self, plaintext):
        # private_key = RSA.importKey(self.prikey_string)
        # cipher = PKCS1_v1_5.new(self._private_key)
        h = SHA.new(plaintext)
        signature = self._cipher.sign(h)
        sign = base64.b64encode(signature)
        sign = sign.decode('utf-8')
        return sign

    def rsa_verify(self, sign, plaintext):
        """校验RSA 数字签名"""
        digest = SHA.new()
        digest.update(plaintext)
        # verifier = PKCS1_v1_5.new(RSA.importKey(self.pubkey_string))
        verify_flag = self._verifier.verify(digest, b64decode(sign))
        logger.info("天阙ras_verify: {} {} {}".format(plaintext, sign, verify_flag))
        return verify_flag

    def check_op_success(self, ret):
        if not ret.flag:
            return False
        # logger.info("check op success: {}".format(ret))
        if ret.ret.get("code") == "0000" and ret.ret.get("respData", {}).get("bizCode") == "0000":
            return True
        return False

    @record_cpu_cost
    def build_params(self, reqdata):
        now = int(time.time())
        shanghai = pytz.timezone(date_utils.TIMEZONE_SHANGHAI)
        now = datetime.fromtimestamp(now).astimezone(shanghai).strftime("%Y%m%d%H%M%S")
        params = {
            "orgId": self.tian_que_pay_orgid,
            "reqId": id_manager.generate_common_id(),
            "version": "1.0",
            "timestamp": str(now),
            "reqData": reqdata,
            "signType": "RSA",
        }
        signature = self.sign(params)
        params.update({"sign": signature})
        return params

    def merchant_init(self, mno, ledger_account_flag, enforce=False):
        """商户天阙支付信息初始化.主要用于在业务助手后台初始化,mno为必须"""
        now = int(time.time())
        if not self.tian_que_pay_info:
            tian_que_pay_info = payment_pb.TianQuePayInfo()
            tian_que_pay_info.merchant_id = self.merchant.id
            tian_que_pay_info.id = id_manager.generate_common_id()
            tian_que_pay_info.ledger_account_flag = ledger_account_flag  # 是否做分账
            tian_que_pay_info.ledger_account_effect_time = 30  # 分账有效时间为30天
            tian_que_pay_info.order_time_expire = 30  # 订单有效时间30分钟
            tian_que_pay_info.need_receipt = "01"  # 是否需要发票
            tian_que_pay_info.create_timestamp = now
            tian_que_pay_info.mno = mno
        else:
            tian_que_pay_info = self.tian_que_pay_info
            tian_que_pay_info.update_timestamp = now
        tian_que_pay_info.next_mno = mno
        merchant_info = self.get_merchant_info(mno)
        if merchant_info:
            repo_info = merchant_info.get("respData").get("repoInfo", [])
            for info in repo_info:
                if info.get("childNoType") == "WX":
                    tian_que_pay_info.child_no = info.get("childNo")
        PaymentDataAccessHelper().add_or_update_tian_que_pay_info(tian_que_pay_info)

    def _get_order_expire_time(self, order_lock_expire=1):
        if self.is_multi_party_order():
            return order_lock_expire
        return self.tian_que_pay_info.order_time_expire

    def create_ant_shop(self, mno=None):
        if mno is None:
            mno = self.tian_que_pay_info.mno
        appid = constants.ALIPAY_APPID
        kargs = {
            "mno": mno,
            "appid": appid,
            "shopCategory": "B0001",
            "shopType": "01",
            "shopName": self.merchant.basic_info.display_name,
            "storeId": self.merchant.id,
        }
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_ANT_SHOP_CREATE)
        ret = self.try_post(url, params, try_times=1)
        resp_data = ret.ret.get("respData", {})
        if resp_data.get("bizCode") != "0000":
            return ret.ret
        self.merchant.alipay_merchant_shop_info.create_shop_order_id = resp_data.get("orderId")
        self.merchant_da.update_or_create_merchant(merchant=self.merchant)
        return ret.ret

    def order_query_ant_shop(self, mno=None):
        if mno is None:
            mno = self.tian_que_pay_info.mno
        if self.merchant.alipay_merchant_shop_info.create_shop_order_id == "":
            return {}
        kargs = {"mno": mno, "orderId": self.merchant.alipay_merchant_shop_info.create_shop_order_id}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_ANT_SHOP_ORDER_QUERY)
        ret = self.try_post(url, params)
        resp_data = ret.ret.get("respData", {})
        if resp_data.get("bizCode") != "0000":
            return ret.ret
        if resp_data.get("shopId"):
            self.merchant.alipay_merchant_shop_info.id = resp_data.get("shopId")
            self.merchant_da.update_or_create_merchant(merchant=self.merchant)
        return ret.ret

    def merchant_sign(self, mno=None):
        """分账签约"""
        if mno is None:
            mno = self.tian_que_pay_info.mno
        kargs = {"signType": "00", "mno": mno}
        params = self.build_params(kargs)
        logger.info("天阙分账支付签约: {}".format(params))
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_SIGN)
        ret = self.try_post(url, params)
        return ret.ret

    def get_merchant_info(self, mno=None):
        if mno is None:
            mno = self.tian_que_pay_info.mno
        kargs = {"mno": mno}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_INFO_QUERY)
        ret = self.try_post(url, params)
        return ret.ret

    def query_ledger_set_mno_array(self, mno=None):
        if mno is None:
            mno = self.tian_que_pay_info.mno
        kargs = {"mno": mno, "mnoArray": self.tian_que_pay_shilai_launch_ledger_mno}
        params = self.build_params(kargs)
        logger.info("天阙分账权限设置: {}".format(params))
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_LEDGER_SET_MNO_ARRAY)
        ret = requests.post(url, json=params)
        ret = self.try_post(url, params)
        return ret.ret

    def is_pay_success(self, trade_query_result):
        trade_msg = trade_query_result.get("respData").get("tradeMsg")
        if trade_msg == '交易失败，请稍后重试':
            return self.TRADE_FAILED
        if trade_msg == '成功':
            return self.TRADE_SUCCESS
        if trade_msg == '用户支付中，请稍后进行查询':
            return self.TRADE_PAYING
        return self.TRADE_FAILED

    def trade_query(self, transaction_id):
        payment_da = PaymentDataAccessHelper()
        transaction_da = TransactionDataAccessHelper()

        prepay_info = payment_da.get_tian_que_pay_prepay_info(transaction_id=transaction_id)
        transaction = transaction_da.get_transaction_by_id(transaction_id=transaction_id)
        tian_que_pay_info = payment_da.get_tian_que_pay_info(
            merchant_id=transaction.payee_id, state=payment_pb.TianQuePayInfo.NORMAL
        )
        kargs = {"mno": tian_que_pay_info.mno, "ordNo": transaction_id, "uuid": prepay_info.resp_data.uuid}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_TRADE_QUERY)
        ret = self.try_post(url, params)
        return ret.ret

    def trade_query_v2(self, mno, wechat_or_alipay_order_id, transaction_id):
        data = self.build_params({"mno": mno, "transactionId": wechat_or_alipay_order_id, "ordNo": transaction_id})
        ret = self.try_post(f"{self.tian_que_pay_domain}{self.TIAN_QUE_PAY_TRADE_QUERY}", data)
        return ret.ret

    def merchant_real_name_commit_apply(self):
        """随行付实名认证"""
        payment_da = PaymentDataAccessHelper()

        mno = self.tian_que_pay_info.mno
        domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        back_url = "{}/tian_que_pay/real_name_commit_apply_callback".format(domain)
        kargs = {"mno": mno, "backUrl": back_url}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY)
        ret = self.try_post(url, params)
        if not self.check_op_success(ret):
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_FAIL)
        resp_data = ret.ret.get("respData")
        resp_data["merchantId"] = self.merchant.id
        payment_da.add_or_update_real_name_commit(resp_data)
        self.get_merchant_real_name_commit()
        return ret

    def get_merchant_real_name_commit(self):
        payment_da = PaymentDataAccessHelper()
        mno = self.tian_que_pay_info.mno
        apply_info = payment_da.get_tian_que_pay_real_name_commit(merchant_id=self.merchant.id)
        if not apply_info:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_NOT_EXISTS)
        kargs = {"mno": mno, "wxApplyNo": apply_info.wx_apply_no}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REAL_NAME_COMMIT_QUERY)
        ret = self.try_post(url, params)
        if not ret.flag:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_NOT_EXISTS)
        resp_data = ret.ret.get("respData")
        if resp_data.get("idenStatus") == payment_pb.TianQuePayRealNameCommit.IdenStatus.Name(
            payment_pb.TianQuePayRealNameCommit.APPLYMENT_STATE_PASSED
        ):
            self.tian_que_pay_info.is_real_name_commit_apply = True
            payment_da.add_or_update_tian_que_pay_info(self.tian_que_pay_info)
        apply_info.info_qrcode = resp_data.get("infoQrcode")
        payment_da.add_or_update_real_name_commit(apply_info)
        return resp_data

    def merchant_real_name_commit_apply_callback(self, iden_status, info_qrcode, wx_apply_no, reject_code, reject_info):
        payment_da = PaymentDataAccessHelper()
        apply_info = payment_da.get_tian_que_pay_real_name_commit(merchant_id=self.merchant.id, wx_apply_no=wx_apply_no)
        if not apply_info:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_NOT_EXISTS)
        if iden_status == payment_pb.TianQuePayRealNameCommit.IdenStatus.Name(
            payment_pb.TianQuePayRealNameCommit.APPLYMENT_STATE_PASSED
        ):
            self.tian_que_pay_info.is_real_name_commit_apply = True
            payment_da.add_or_update_tian_que_pay_info(self.tian_que_pay_info)
        apply_info.iden_status = payment_pb.TianQuePayRealNameCommit.IdenStatus.Value(iden_status)
        apply_info.info_qrcode = info_qrcode
        apply_info.reject_code = reject_code
        apply_info.reject_info = reject_info
        payment_da.add_or_update_real_name_commit(apply_info)

    def undo_real_name_commit(self):
        """撤销实名认证申请"""
        payment_da = PaymentDataAccessHelper()
        apply_info = payment_da.get_tian_que_pay_real_name_commit(merchant_id=self.merchant.id)
        if not apply_info:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_NOT_EXISTS)
        mno = self.tian_que_pay_info.mno
        kargs = {"mno": mno, "wxApplyNo": apply_info.wx_apply_no}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REAL_NAME_COMMIT_APPLY_UNDO)
        ret = self.try_post(url=url, params=params)
        if not self.check_op_success(ret):
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_UNDO_REAL_NAME_COMMIT_APPLY_FAIL)
        return ret.ret.get("respData")

    def check_tian_que_pay_info(self, raise_error=True):
        if not self.tian_que_pay_info:
            if raise_error:
                raise errors.Error(err=error_codes.TIAN_QUE_PAY_INFO_NOT_FOUND)
            return False
        # if not self.tian_que_pay_info.is_real_name_commit_apply:
        #     if raise_error:
        #         raise errors.Error(err=error_codes.TIAN_QUE_PAY_NOT_REAL_NAME_COMMIT_APPLY)
        #     return False
        if self.tian_que_pay_info.state != payment_pb.TianQuePayInfo.NORMAL:
            if raise_error:
                raise errors.Error(err=error_codes.TIAN_QUE_PAY_INFO_STATE_ERROR)
            return False
        return True

    def get_sync_launch_ledger_rules(self, transaction, order=None):
        if transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            return self.generate_scan_code_order_ledger_rules(transaction, order)
        elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
            return self.generate_coupon_package_ledger_rules(transaction)
        elif transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
            return self.generate_fanpiao_ledger_rules(transaction)
        elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE:
            return self.generate_scan_code_order_ledger_rules(transaction, order)

    def get_pay_user_id(self, user):
        if self.pay_type == "WECHAT":
            return user.wechat_profile.openid
        elif self.pay_type == "ALIPAY":
            return user.alipay_profile.user_id

    def wechat_pay_unified_order(self, transaction, notify_url, ledger_account_flag=None, try_times=3):
        if try_times == 0:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_PREPAY_FAIL)
        fee = "{:.2f}".format(transaction.paid_fee / float(100))
        user = UserDataAccessHelper().get_user(user_id=transaction.payer_id)
        self.check_tian_que_pay_info()
        if ledger_account_flag is None:
            ledger_account_flag = self.tian_que_pay_info.ledger_account_flag
        kargs = {
            "ordNo": transaction.id,  # 订单号
            "mno": self.tian_que_pay_info.mno,
            "amt": fee,  # 总金额
            "discountAmt": 0,  # 打折金额
            "unDiscountAmt": fee,  # 不打折的金额
            "payType": self.pay_type,  # 支付渠道
            "subject": "时来扫码点餐",
            "tradeSource": "01",  # 交易来源
            "trmIp": self.ip,  # 本机ID
            "userId": self.get_pay_user_id(user),  # 用户wechat openid
            # "subAppid": DefaultConfig.WECHAT_MINIPROGRAM_APPID,
            "subAppid": config.WECHAT_MINIPROGRAM_APPID,
            "needReceipt": self.tian_que_pay_info.need_receipt,  # 是否开发票 01: 否. 00: 是
            "ledgerAccountFlag": ledger_account_flag,  # 是否支付分账 00: 做, 01: 不做
            "ledgerAccountEffectTime": self.tian_que_pay_info.ledger_account_effect_time,  # 分账有效时间. 单位(天)
            # "timeExpire": self.tian_que_pay_info.order_time_expire,  # 订单失效时间,不传默认5分钟,单位(分钟)
            "timeExpire": self._get_order_expire_time(),
            "notifyUrl": notify_url,  # 支付结果通知地址
            "wechatFoodOrder": "FoodOrder",
            "payWay": self.pay_way,  # 支付方式: 03: 小程序
            "wxSceneInfo": {"id": transaction.payee_id},
        }
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_UNIFIED_ORDER)
        ret = self.try_post(url, params, timeout=30, try_times=5)
        if not self.check_op_success(ret):
            # logger.info("调用天阙支付失败: {}".format(params))
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_PREPAY_FAIL)
        ret = ret.ret
        ret["respData"]["transactionId"] = transaction.id
        PaymentDataAccessHelper().add_tian_que_pay_prepay_info(ret)
        transaction.pay_method = wallet_pb.Transaction.TIAN_QUE_PAY
        transaction.pay_channel = wallet_pb.Transaction.WECHAT_CHANNEL

        result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        resp_data = ret.get("respData")
        pay_package = resp_data.get("payPackage")
        prepay_id = pay_package.split("=")[1]
        result.update(
            {
                "signData": {
                    "timeStamp": resp_data.get("payTimeStamp"),
                    "nonceStr": resp_data.get("paynonceStr"),
                    "package": resp_data.get("payPackage"),
                    "signType": resp_data.get("paySignType"),
                    "paySign": resp_data.get("paySign"),
                    "prepayId": prepay_id,
                },
                "channelId": resp_data.get("sxfUuid"),
                "childNo": self.tian_que_pay_info.child_no,
            }
        )
        return result

    def alipay_unified_order(self, transaction, notify_url, ledger_account_flag=None, try_times=3):
        if try_times == 0:
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_PREPAY_FAIL)
        fee = "{:.2f}".format(transaction.paid_fee / float(100))
        user = UserDataAccessHelper().get_user(user_id=transaction.payer_id)
        self.check_tian_que_pay_info()
        if ledger_account_flag is None:
            ledger_account_flag = self.tian_que_pay_info.ledger_account_flag
        kargs = {
            "ordNo": transaction.id,  # 订单号
            "mno": self.tian_que_pay_info.mno,
            "amt": fee,  # 总金额
            "discountAmt": 0,  # 打折金额
            "unDiscountAmt": fee,  # 不打折的金额
            "payType": self.pay_type,  # 支付渠道
            "subject": "时来扫码点餐",
            "tradeSource": "01",  # 交易来源
            "trmIp": self.ip,  # 本机ID
            "userId": self.get_pay_user_id(user),  # 用户wechat openid
            "ledgerAccountFlag": ledger_account_flag,  # 是否支付分账 00: 做, 01: 不做
            "ledgerAccountEffectTime": self.tian_que_pay_info.ledger_account_effect_time,  # 分账有效时间. 单位(天)
            # "timeExpire": self.tian_que_pay_info.order_time_expire,  # 订单失效时间,不传默认5分钟,单位(分钟)
            "timeExpire": self._get_order_expire_time(),
            "notifyUrl": notify_url,  # 支付结果通知地址
            "alipayFoodOrderType": "QR_FOOD_ORDER",
            "payWay": self.pay_way,  # 支付方式: 03: 小程序
        }
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_UNIFIED_ORDER)

        ret = self.try_post(url, params, timeout=30, try_times=5)
        if not self.check_op_success(ret):
            logger.info("调用天阙支付失败: {}".format(params))
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_PREPAY_FAIL)
        ret = ret.ret
        ret["respData"]["transactionId"] = transaction.id
        PaymentDataAccessHelper().add_tian_que_pay_prepay_info(ret)
        transaction.pay_method = wallet_pb.Transaction.TIAN_QUE_PAY
        transaction.pay_channel = wallet_pb.Transaction.ALIPAY_CHANNEL
        result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        resp_data = ret.get("respData")
        result["tradeNo"] = resp_data.get("source")
        return result

    def get_file_url(self, bill_date, bill_type):
        kargs = {"billDate": bill_date, "billType": bill_type}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_GET_FILE_URL)
        ret = requests.post(url, json=params).json()
        return ret

    def prepay(self, transaction, **kargs):
        if kargs.get("auth_code"):
            return self.reverse_scan(transaction, kargs.get("auth_code"))
        notify_url = self.generate_notify_url(transaction=transaction, payment_prefix="tian_que_pay", **kargs)
        ledger_account_flag = kargs.get("ledger_account_flag", None)
        if ledger_account_flag is not None:
            if ledger_account_flag:
                ledger_account_flag = "00"
            else:
                ledger_account_flag = "01"
        else:
            ledger_account_flag = self.tian_que_pay_info.ledger_account_flag
        if self.pay_type == "WECHAT":
            return self.wechat_pay_unified_order(transaction, notify_url, ledger_account_flag=ledger_account_flag)
        elif self.pay_type == "ALIPAY":
            return self.alipay_unified_order(transaction, notify_url, ledger_account_flag=ledger_account_flag)
        raise errors.Error(err=error_codes.TIAN_QUE_PAY_TYPE_NOT_SUPPORTED)

    def wechat_reverse_scan(self, transaction, auth_code, kargs):
        """微信付款码付款"""
        transaction.pay_channel = wallet_pb.Transaction.WECHAT_CHANNEL
        kargs.update({"payType": "WECHAT"})
        params = self.build_params(kargs)
        logger.info("微信扫码付参数: {}".format(params))
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REVERSE_SCAN)
        api_result = self.try_post(url, params)
        return api_result

    def alipay_reverse_scan(self, transaction, auth_code, kargs):
        """支付宝付款码付款"""
        kargs.update({"payType": "ALIPAY"})
        transaction.pay_channel = wallet_pb.Transaction.ALIPAY_CHANNEL
        params = self.build_params(kargs)
        logger.info("支付宝扫码付参数: {}".format(params))
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REVERSE_SCAN)
        api_result = self.try_post(url, params)
        return api_result

    def reverse_scan(self, transaction, auth_code):
        """用户被扫支付"""
        discount_amt = 0
        un_discount_amt = transaction.paid_fee
        discount_amt = "{:.2f}".format(discount_amt / float(100))
        un_discount_amt = "{:.2f}".format(un_discount_amt / float(100))
        notify_url = self.generate_notify_url(transaction=transaction, payment_prefix="tian_que_pay")
        fee = "{:.2f}".format(transaction.paid_fee / float(100))
        kargs = {
            "ordNo": transaction.id,  # 订单号
            "mno": self.tian_que_pay_info.mno,
            "authCode": auth_code,
            "amt": fee,  # 总金额
            "discountAmt": discount_amt,  # 打折金额
            "unDiscountAmt": un_discount_amt,
            "subject": "时来扫码点餐",
            "trmIp": self.ip,  # 本机ID
            "tradeSource": "01",
            "notifyUrl": notify_url,
            "ledgerAccountFlag": "00",
            "ledgerAccountEffectTime": self.tian_que_pay_info.ledger_account_effect_time,
        }
        transaction.auth_code = auth_code
        transaction.pay_method = wallet_pb.Transaction.TIAN_QUE_PAY
        if transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
            result = self.alipay_reverse_scan(transaction, auth_code, kargs)
        elif transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
            result = self.wechat_reverse_scan(transaction, auth_code, kargs)
        resp_data = result.ret.get("respData", {})
        if resp_data.get("bizCode") == "0000":
            # 免密支付的话直接成功
            resp_data.update({"mno": self.mno})
            self.notification(resp_data)
            transaction.state = wallet_pb.Transaction.SUCCESS
            transaction.paid_time = int(time.time())
            ret = result.ret
            ret["respData"]["transactionId"] = transaction.id
            PaymentDataAccessHelper().add_tian_que_pay_prepay_info(ret)
            return {"errcode": error_codes.SUCCESS}
        elif resp_data.get("bizCode") == "2002":
            ret = result.ret
            ret["respData"]["transactionId"] = transaction.id
            PaymentDataAccessHelper().add_tian_que_pay_prepay_info(ret)
            return {"errcode": error_codes.USER_PAYING}
        elif resp_data.get("bizCode") == "0001":
            if resp_data.get("bizMsg") == "二维码已失效，请刷新后重试" or resp_data.get("bizMsg") == "支付宝提示:请消费者刷新付款码":
                raise errors.QrcodeInvalid()
            raise errors.ShowError("二维码错误")
        else:
            raise errors.ShowError("支付失败")
        return result

    def notification(self, data):
        Callback = namedtuple("Callback", ["wechat_transaction_id"])
        if data.get("bizCode") != "0000":
            logger.warning(f"天阙回调报错，url={request.path}, json={request.json}")
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_TRADE_FAIL)
        # self.verify(data)
        PaymentDataAccessHelper().add_tian_que_pay_notification_info(data)
        return Callback(wechat_transaction_id=data.get("transactionId"))

    def query_launch_ledger(self, transaction_id):
        kargs = {"mno": self.tian_que_pay_info.mno, "ordNo": transaction_id}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_LEDGER)
        ret = self.try_post(url, params)
        return ret.ret

    def __generate_allocate_to_shilai_launch_rules(self, transaction):
        """全部都分账给时来的分账逻辑"""
        commission_fee = int(transaction.paid_fee * self.tian_que_pay_shilai_commission_rate + 0.5)
        allot_value = transaction.paid_fee - commission_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def do_allocate_to_shilai(self, transaction):
        """全部都分账给时来的分账逻辑."""
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.__generate_allocate_to_shilai_launch_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def get_scan_code_order_shilai_allot_value(self, transaction, order):
        """扫码点餐时来应该分到的金额"""
        settlement_rate = self.merchant_manager.get_settlement_rate()
        # 商户应该收到金额
        merchant_fee = int(order.paid_in_fee * (1 - settlement_rate) + 0.5)
        # 时来应该分到的金额
        allot_value = abs(transaction.paid_fee) - merchant_fee
        return allot_value

    def generate_scan_code_order_ledger_rules(self, transaction, order):
        """扫码点餐
        分账逻辑:
        transaction.paid_fee: 用户实际支付金额
        order.paid_in_fee: 商户应收金额(未扣除费率)
        1. 保证用户收到的钱一定是 order.paid_in_fee * (1 - settlement_rate),剩下的为时来的分账金额.
        """
        allot_value = self.get_scan_code_order_shilai_allot_value(transaction, order)
        if allot_value < 0:
            allot_value = 0
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]

        return ledger_rules

    def generate_recharge_launch_ledger_rules(self, transaction):
        """会员充值"""
        merchant_manager = MerchantManager(merchant=self.merchant)
        settlement_rate = merchant_manager.get_settlement_rate()
        merchant_fee = int(transaction.paid_fee * (1 - settlement_rate) + 0.5)
        allot_value = transaction.paid_fee - merchant_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def generate_coupon_package_ledger_rules(self, transaction):
        """购买券包分账逻辑
        百分百分账到时来.因为费率是由商户出,所以时来实际分账的金额为 transaction.paid_fee - commission_fee
        """
        commission_fee = int(transaction.paid_fee * self.tian_que_pay_shilai_commission_rate + 0.5)
        allot_value = transaction.paid_fee - commission_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def generate_fanpiao_ledger_rules(self, transaction):
        """购买饭票分账逻辑
        百分百分账到时来.因为费率是由商户出,所以时来实际分账的金额为 transaction.paid_fee - commission_fee
        """
        commission_fee = int(transaction.paid_fee * self.tian_que_pay_shilai_commission_rate + 0.5)
        allot_value = transaction.paid_fee - commission_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def coupon_package_launch_ledger(self, transaction):
        """购买券包分账"""
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_coupon_package_ledger_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def generate_self_dining_payment_ledger_rules(self, transaction):
        settlement_rate = self.merchant_manager.get_settlement_rate()
        merchant_fee = int(transaction.paid_fee * (1 - settlement_rate) + 0.5)
        allot_value = transaction.paid_fee - merchant_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def self_dining_payment_launch_ledger(self, transaction):
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_self_dining_payment_ledger_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def fanpiao_launch_ledger(self, transaction):
        """购买饭票分账"""
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_fanpiao_ledger_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def open_group_purchase_launch_ledger(self, transaction):
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_open_group_purchase_ledger_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def generate_open_group_purchase_ledger_rules(self, transaction):
        """团长开团分账逻辑
        百分百分账到时来.因为费率是由商户出,所以时来实际分账的金额为 transaction.paid_fee - commission_fee
        """
        commission_fee = int(transaction.paid_fee * self.tian_que_pay_shilai_commission_rate + 0.5)
        allot_value = transaction.paid_fee - commission_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def ordering_with_open_group_purchase_launch_ledger(self, transaction, order):
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_ordering_with_open_group_purchase_launch_ledger_rules(transaction, order)
        return self.launch_ledger(transaction, ledger_rules)

    def generate_ordering_with_open_group_purchase_launch_ledger_rules(self, transaction, order):
        """
        transaction: 合并支付的transaction
        order: 点餐订单
        transaction.paid_fee - order.paid_fee => 开团的支付金额
        所以时来分账金额为 transaction.paid_fee - order.paid_fee - commission_fee

        在 ordering_notify中，会通过 order.paid_in_fee - order.paid_fee 来计算补贴金额,
        所以此处 merchant_fee = order.paid_fee
        """
        merchant_fee = order.paid_fee
        shilai_fee = transaction.paid_fee - merchant_fee
        commission_fee = int(shilai_fee * self.tian_que_pay_shilai_commission_rate + 0.5)
        allot_value = transaction.paid_fee - merchant_fee - commission_fee
        allot_value = allot_value / float(100)
        allot_value = "{:.2f}".format(allot_value)
        ledger_rules = [{"allotValue": allot_value, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def scan_code_order_launch_ledger(self, transaction, order):
        """扫码点餐分账"""
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_scan_code_order_ledger_rules(transaction, order)
        return self.launch_ledger(transaction, ledger_rules)

    def ordering_coupon_package_union_pay_launch_ledger(
        self, transaction, ordering_transaction, order, coupon_package_transaction
    ):
        ordering_ledger_rules = self.generate_scan_code_order_ledger_rules(ordering_transaction, order)
        coupon_package_ledger_rules = self.generate_coupon_package_ledger_rules(coupon_package_transaction)
        ledger_rules = {}
        for ledger_rule in itertools.chain(ordering_ledger_rules, coupon_package_ledger_rules):
            mno = ledger_rule.get("mno")
            lr = ledger_rules.get(mno, {})
            allot_value = "{:.2f}".format(float(lr.get("allotValue", 0)) + float(ledger_rule.get("allotValue", 0)))
            lr.update({"mno": mno, "allotValue": allot_value})
            ledger_rules.update({mno: lr})
        ledger_rules = list(ledger_rules.values())
        return self.launch_ledger(transaction, ledger_rules)

    def recharge_launch_ledger(self, transaction):
        """会员储值分账"""
        if transaction.pay_method != wallet_pb.Transaction.TIAN_QUE_PAY:
            return
        ledger_rules = self.generate_recharge_launch_ledger_rules(transaction)
        return self.launch_ledger(transaction, ledger_rules)

    def launch_ledger(self, transaction, ledger_rules):
        now = int(time.time())
        """分账"""
        if now > 1704816000:
            logger.warning(f"payeeId: {transaction.payee_id} transactionId: {transaction.id} 使用新分账")
            transaction.ledgered = True
            return True
        payment_da = PaymentDataAccessHelper()
        prepay_info = payment_da.get_tian_que_pay_prepay_info(transaction_id=transaction.id)
        uuid = prepay_info.resp_data.uuid
        if uuid:
            notification_info = payment_da.get_tian_que_pay_notification_info(uuid=uuid)
        else:
            sxf_uuid = prepay_info.resp_data.sxf_uuid
            notification_info = payment_da.get_tian_que_pay_notification_info(sxf_uuid=sxf_uuid)
        if notification_info:
            mno = notification_info.mno
        else:
            mno = self.tian_que_pay_info.mno
        ledger_account_flag = self.get_launch_ledger_flag(ledger_rules)
        kargs = {
            "mno": mno,
            "uuid": id_manager.generate_common_id(),
            "ordNo": transaction.id,
            "ledgerAccountFlag": ledger_account_flag,
            "ledgerRule": ledger_rules,
        }
        if ledger_account_flag == "00":  # 取消分账
            del kargs["ledgerRule"]
        params = self.build_params(kargs)
        logger.info("设置分账: {}".format(params))
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_LAUNCH_LEDGER)
        ret = self.try_post(url, params, try_times=2)
        if not self.check_op_success(ret):
            if ret.ret.get("code") == "0001" and ret.ret.get("msg") == '原交易订单已做过分账，不允许重复分账':
                logger.info("transactionId: {} 已分过账".format(transaction.id))
                ledger_info = payment_pb.TianQuePayLedgerInfo()
                ledger_info.ord_no = transaction.id
                ledger_info.status = payment_pb.TianQuePayLedgerInfo.NORMAL
                payment_da.add_or_update_tian_que_pay_ledger_info(ledger_info)
            else:
                logger.info("transactionId: {} 分账失败".format(transaction.id))
                ledger_info = payment_pb.TianQuePayLedgerInfo()
                ledger_info.ord_no = transaction.id
                ledger_info.status = payment_pb.TianQuePayLedgerInfo.FAILED
                payment_da.add_or_update_tian_que_pay_ledger_info(ledger_info)
            return False
        else:
            ledger_info = payment_pb.TianQuePayLedgerInfo()
            ledger_info.ord_no = transaction.id
            ledger_info.uuid = kargs.get("uuid")
            ledger_info.rec_fee_amt = ret.ret.get("respData").get("recFeeAmt")
            for rule in ledger_rules:
                rule_vo = ledger_info.ledger_rule.add()
                rule_vo.mno = rule.get("mno")
                rule_vo.allot_value = rule.get("allotValue")
            payment_da.add_or_update_tian_que_pay_ledger_info(ledger_info)
            logger.info("transactionId: {} 分账成功".format(transaction.id))
            transaction.ledgered = True
            return True

    def get_launch_ledger_flag(self, rules):
        # 下单的接口 ledgerAccountFlag: 00: 分账, 01: 不分账
        # 分账的接口 ledgerAccountFlag: 01: 分账, 00: 不分账
        ledger_account_flag = self.tian_que_pay_info.ledger_account_flag
        if ledger_account_flag == "00":
            ledger_account_flag = "01"
        elif ledger_account_flag == "01":
            ledger_account_flag = "00"
        if ledger_account_flag == "":
            ledger_account_flag = "00"
        for rule in rules:
            # 如果分账金额为0,那么就不用分账.flag传00商家第二天就能收到钱了
            mno = rule.get("mno")
            allot_value = rule.get("allotValue")
            if mno == self.tian_que_pay_shilai_launch_ledger_mno and int(float(allot_value) * 100) == 0:
                ledger_account_flag = "00"
        return ledger_account_flag

    def verify(self, data):
        signature = data.get("sign")
        del data['sign']
        keys = data.keys()
        keys = sorted(keys, key=lambda x: x)
        s = []
        for key in keys:
            if data.get(key, "") == "":
                continue
            s.append("{}={}".format(key, data.get(key)))
        s = "&".join(s)
        if not self.rsa_verify(signature, s.encode("utf8")):
            raise errors.Error(err=error_codes.TIAN_QUE_PAY_VERIFY_FAIL)

    def org_transfer(self, fee, order_no):
        kargs = {
            "mno": self.tian_que_pay_info.mno,
            "investor": "01",
            "orderNo": order_no,
            "amount": str(fee),
            "content": "时来智慧餐饮入账",
        }
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_CAPITAL_FUNDMANAGE_ORGTRANSFER)
        try:
            response = requests.post(url, json=params)
            ret = response.json()
            biz_code = ret.get("respData", {}).get("bizCode")
            if ret.get("code") == "0000" and (biz_code == "0000"):
                transfer_info = json_format.ParseDict(kargs, payment_pb.TianQuePayTransferInfo(), ignore_unknown_fields=True)
                transfer_info.transaction_no = ret.get("respData").get("transactionNo")
                transfer_info.transaction_time = ret.get("respData").get("transactionTime")
                payment_da = PaymentDataAccessHelper()
                payment_da.add_or_update_tian_que_pay_transfer_info(transfer_info)
                payment_da.increase_tian_que_pay_transfer_amount(fee)
                return True
            return False
        except Exception as e:
            logger.error(str(e) + '\n' + response.content.decode())
            return False

    def get_tian_que_pay_transfer_info(self, order_no):
        payment_da = PaymentDataAccessHelper()
        transfer_info = payment_da.get_tian_que_pay_transfer_infos(order_no=order_no)
        if transfer_info and len(transfer_info) > 0:
            transfer_info = transfer_info[0]
        else:
            return {"msg": "无法查找到订单: {}".format(order_no)}
        kargs = {"orderNo": order_no}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_GET_TRANSFER_INFO)
        try:
            response = requests.post(url, json=params)
            ret = response.json()
            resp_data = ret.get("respData")
            status = resp_data.get("tranStatus")
            if status == "00":
                transfer_info.status = payment_pb.TianQuePayTransferInfo.SUCCESS
            elif status == "01":
                transfer_info.status = payment_pb.TianQuePayTransferInfo.FAIL
            elif status == "02":
                transfer_info.status = payment_pb.TianQuePayTransferInfo.ABNORMAL
            transfer_info.rate = resp_data.get("rate")
            try:
                transfer_info.timestamp = datetime.strptime(transfer_info.transaction_time, "%Y-%m-%d %H:%M:%S")
            except Exception:
                transfer_info.timestamp = int(time.time())
            payment_da.add_or_update_tian_que_pay_transfer_info(transfer_info)
            return resp_data
        except Exception as e:
            logger.error(str(e) + '\n' + response.content.decode())

    def refund(self, transaction, refund_transaction_id=None, union_transaction=None, refund_fee=None):
        payment_da = PaymentDataAccessHelper()
        if refund_transaction_id is None:
            refund_transaction_id = id_manager.generate_common_id()
        pay_transaction = self.get_pay_transaction(transaction)
        prepay_info = payment_da.get_tian_que_pay_prepay_info(transaction_id=pay_transaction.id)
        uuid = prepay_info.resp_data.uuid
        notification_info = payment_da.get_tian_que_pay_notification_info(uuid=uuid)
        notify_url = self.generate_refund_notify_url(
            pay_transaction, transaction.id, refund_transaction_id, prefix="tian_que_pay/refund"
        )

        paid_fee = "{:.2f}".format(transaction.paid_fee / float(100))
        if refund_fee is not None:
            paid_fee = "{:.2f}".format(refund_fee / float(100))
        if float(paid_fee) == 0:
            return True
        kargs = {
            "ordNo": refund_transaction_id,
            "mno": notification_info.mno,
            "origOrderNo": pay_transaction.id,
            "origUuid": uuid,
            "origSxfUuid": str(notification_info.sxf_uuid),
            "amt": paid_fee,
            "notifyUrl": notify_url,
        }
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_REFUND)
        logger.info("天阙退款参数: {}".format(params))
        ret = requests.post(url, json=params).json()
        logger.info("天阙退款返回: {}".format(ret))
        biz_code = ret.get("respData", {}).get("bizCode")

        if ret.get("code") == "0000" and (biz_code == "0000" or biz_code == "2002"):
            ledger = payment_da.get_tian_que_pay_ledger_infos(transaction_id=pay_transaction.id)
            if ledger and len(ledger) == 1:
                ledger = ledger[0]
                ledger.status = payment_pb.TianQuePayLedgerInfo.REFUND
                payment_da.add_or_update_tian_que_pay_ledger_info(ledger)
            return True
        # 如果发起退款都失败了,那么直接抛出异常,提示用户联收银员
        raise errors.Error(
            err=error_codes.REFUND_REQUEST_FAILED,
            reason=ret.get("respData", {}).get("bizMsg", "UNKNOW") if biz_code is not None else ret.get('msg', 'UNKNOW'),
        )

    def ordering_coupon_package_union_pay_ordering_refund(self, ordering_transaction):
        """聚合支付的情况下,一共有三个transaction
        1. 用于支付的transaction => transaction1
        2. 用于券包的transaction => transaction2
        3. 用于订单的transaction => transaction3
        退扫码点餐的时候,用的是transaction3,我们需要根据transaction3找到transaction1,
        然后再用transaction1去退款,退款金额为transaction3.paidFee
        """
        RefundReturn = collections.namedtuple("RefundReturn", ["flag", "refund_transaction"])
        refund_transaction_id = id_manager.generate_common_id()
        transaction_da = TransactionDataAccessHelper()
        union_transaction = transaction_da.get_ordering_coupon_package_union_pay_transaction(
            ordering_transaction_id=ordering_transaction.id
        )
        if not union_transaction:
            return RefundReturn(flag=False, refund_transaction=None)
        ret = self.refund(ordering_transaction, refund_transaction_id, union_transaction)
        refund_transaction = None
        if ordering_transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_ordering_refund(
                user_id=ordering_transaction.payer_id,
                merchant_id=ordering_transaction.payee_id,
                pay_method=wallet_pb.Transaction.TIAN_QUE_PAY,
                paid_fee=ordering_transaction.paid_fee,
                bill_fee=ordering_transaction.bill_fee,
                use_coupon_id=ordering_transaction.use_coupon_id,
                refunded_transaction_id=ordering_transaction.id,
                id=refund_transaction_id,
            )
            # self.cal_refund_ordering_coupon_package_union_pay_transfer_fee(union_transaction, refund_transaction)
            return RefundReturn(flag=True, refund_transaction=refund_transaction)
        return RefundReturn(flag=True, refund_transaction=None)

    def partial_refund(self, transaction=None, refund_transaction=None, **kargs):
        ret = self.refund(transaction, refund_transaction.id, refund_fee=refund_transaction.paid_fee)
        return ret

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        refund_return = self.ordering_coupon_package_union_pay_ordering_refund(transaction)
        if refund_return.flag:
            return refund_return.refund_transaction
        refund_transaction_id = id_manager.generate_common_id()
        ret = self.refund(transaction, refund_transaction_id)
        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        refund_transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=transaction.pay_method,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                use_coupon_id=transaction.use_coupon_id,
                id=refund_transaction_id,
            )
        return refund_transaction

    def ordering_coupon_package_union_pay_coupon_package_refund(self, coupon_package_transaction):
        """聚合支付的情况下,一共有三个transaction
        1. 用于支付的transaction => transaction1
        2. 用于券包的transaction => transaction2
        3. 用于订单的transaction => transaction3
        退券包的时候,用的是transaction2,我们需要根据transaction2找到transaction1,
        然后再用transaction1去退款,退款金额为transaction2.paidFee
        """
        RefundReturn = collections.namedtuple("RefundReturn", ["flag", "refund_transaction"])
        refund_transaction_id = id_manager.generate_common_id()
        transaction_da = TransactionDataAccessHelper()
        union_transaction = transaction_da.get_ordering_coupon_package_union_pay_transaction(
            coupon_package_transaction_id=coupon_package_transaction.id
        )
        if not union_transaction:
            return RefundReturn(flag=False, refund_transaction=None)
        ret = self.refund(coupon_package_transaction, refund_transaction_id, union_transaction)
        refund_transaction = None
        if coupon_package_transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_coupon_package_refund(
                user_id=coupon_package_transaction.payer_id,
                merchant_id=coupon_package_transaction.payee_id,
                pay_method=wallet_pb.Transaction.TIAN_QUE_PAY,
                paid_fee=coupon_package_transaction.paid_fee,
                bill_fee=coupon_package_transaction.bill_fee,
                id=refund_transaction_id,
            )
            # self.cal_refund_ordering_coupon_package_union_pay_transfer_fee(union_transaction, refund_transaction)
            return RefundReturn(flag=True, refund_transaction=refund_transaction)
        return RefundReturn(flag=True, refund_transaction=None)

    def coupon_package_refund(self, transaction, reason=None):
        refund_return = self.ordering_coupon_package_union_pay_coupon_package_refund(transaction)
        if refund_return.flag:
            return refund_return.refund_transaction
        refund_transaction_id = id_manager.generate_common_id()
        ret = self.refund(transaction, refund_transaction_id)
        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        refund_transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_coupon_package_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.TIAN_QUE_PAY,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                id=refund_transaction_id,
            )
        return refund_transaction

    def cal_refund_ordering_coupon_package_union_pay_transfer_fee(self, union_pay_transaction, transaction):
        """券包,扫码点餐 合并支付退款时
        1. 如果退的是扫码点餐部分,需要通过营销补贴给商户转 X 元
        2. 如果退的是券包部分,需要通过营销补贴给商户转 Y 元
        如: 总金额 100元, 扫码点餐80元,券包20元.商户分账金额为 80元,时来分账金额为20元
        扫码点餐退款时, 天阙按分账比例退款,所以 会从 商户分得的80中退64,从时来分得的20中退16元. 这个时候X为-16
        券包退款时,会从 商户分得的80中退16,从时来分得的20中退4元.这个时候Y为16
        """
        transaction_da = TransactionDataAccessHelper()
        union_pay_transaction = transaction_da.get_transaction_by_id(union_pay_transaction.transaction_id)
        launch_ledger = self.query_launch_ledger(transaction_id=union_pay_transaction.id)
        resp_data = launch_ledger.get("respData")
        account_infos = resp_data.get("accountInfo")
        account_infos = json.loads(account_infos)
        shilai_money, merchant_money, rec = 0, 0, 0
        rec = float(resp_data.get("recFeeAmt")) * 100
        for account_info in account_infos:
            mno = account_info.get("mno", None)
            allot_value = account_info.get("allotValue")
            if not mno:
                continue
            if mno == self.tian_que_pay_shilai_launch_ledger_mno:
                shilai_money = float(allot_value) * 100
            elif mno == self.tian_que_pay_info.mno:
                merchant_money = float(allot_value) * 100

        if shilai_money == 0 or merchant_money == 0:
            # 合并支付应该是不会出现这种情况
            return
        paid_fee = union_pay_transaction.paid_fee
        refund_money = abs(transaction.paid_fee)

        if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
            rec_fee = int(round(shilai_money / paid_fee * rec, 2))
            fee = int(round(shilai_money / paid_fee * refund_money, 2))
            logger.info("合并支付点餐退款: {}, {}".format(rec_fee, fee))
            self.update_transfer_fee((-1) * (fee + rec_fee), transaction)
        elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_REFUND:
            # 当有部分已经退款时,recFeeAmt的金额不会变
            rec_fee = int(round(merchant_money / paid_fee * rec, 2))
            fee = int(round(merchant_money / paid_fee * refund_money, 2))
            logger.info("合并支付券包退款: {}, {}".format(rec_fee, fee))
            self.update_transfer_fee(fee + rec_fee, transaction)

    def ordering_coupon_package_union_pay_refund(self, transaction):
        """目前只用于券包,扫码点餐聚合支付的自动退款"""
        refund_transaction_id = id_manager.generate_common_id()
        self.refund(transaction, refund_transaction_id)
        return None

    def fanpiao_refund(self, transaction, reason):
        refund_transaction_id = id_manager.generate_common_id()
        ret = self.refund(transaction, refund_transaction_id)
        # 如果是因为代码中报错而进行到退款逻辑,transaction.state并不是SUCCESS的,就不用生成refund的transaction
        refund_transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_fanpiao_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.TIAN_QUE_PAY,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                id=refund_transaction_id,
            )
        return refund_transaction

    def member_card_recharge_refund(self, transaction, reason):
        refund_transaction_id = id_manager.generate_common_id()
        ret = self.refund(transaction, refund_transaction_id)
        refund_transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS and ret:
            refund_transaction = TransactionManager().handle_member_card_recharge_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.TIAN_QUE_PAY,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                id=refund_transaction_id,
            )
            self.update_member_card_recharge_refund_merchant_transfer_fee(refund_transaction)
        return refund_transaction

    def update_member_card_recharge_refund_merchant_transfer_fee(self, transaction):
        if transaction.pay_method in [wallet_pb.Transaction.TIAN_QUE_PAY]:
            fee = int(abs(transaction.paid_fee) * constants.TIAN_QUE_PAY_SHILAI_COMMISSION_RATE + 0.5) * (-1)
            self.update_transfer_fee(fee, transaction)

    def refund_notification(self, data):
        """退款的回调"""
        pass

    def generate_ledger_rules_to_shilai(self, transaction):
        """对于某些出错的订单,商户给用户做餐了,我们私下转给商户了,那么这笔订单就需要全部分账给时来"""
        # 这种计算方式在某种金额下可能会无法分账
        # 比如: 90元, 天阙会收取手续费 0.23元
        # round(90 * (1 - 0.0025), 2) = 89.775,4舍5入后为89.78,天阙的手续费就只剩下了0.22元,就会分账失败
        # fee = round(transaction.paid_fee * (1 - 0.0025), 2)

        fee = round(transaction.paid_fee * constants.TIAN_QUE_PAY_SHILAI_COMMISSION_RATE, 2)
        fee = transaction.paid_fee - fee
        fee = "{:.2f}".format(fee / float(100))
        ledger_rules = [{"allotValue": fee, "mno": self.tian_que_pay_shilai_launch_ledger_mno}]
        return ledger_rules

    def update_self_dining_payment_transfer_fee(self, transaction):
        """直接扫码付款应该给商户补贴的金额
        时来收取商家的费率在分账的时候已经处理了,所以此处直接给商家补千25的费率金额就行了
        """
        fee = int(abs(transaction.paid_fee) * self.tian_que_pay_shilai_commission_rate + 0.5)
        self.update_transfer_fee(fee, transaction)

    def update_pos_order_merchant_transfer_fee(self, transaction, platform_discount_fee, fanpiao_commission_fee):
        settlement_rate = self.merchant_manager.get_settlement_rate()
        fee = abs(transaction.paid_fee) + platform_discount_fee
        fee -= abs(fanpiao_commission_fee)
        fee = self.__round(fee * (1 - settlement_rate))
        # 下单的commission计算出来的fanpiao_commission_fee将是正数
        # 退款的transaction计算出来的fanpiao_commission_fee将是负数
        # 下单的commission计算出来的fanpiao_commission_fee将是正数
        # 退款的transaction计算出来的fanpiao_commission_fee将是负数
        f = 1
        if transaction.type == wallet_pb.Transaction.SHILAI_POS_ORDER_PAYMENT:
            pass
        elif transaction.type == wallet_pb.Transaction.SHILAI_POS_ORDER_REFUND:
            fee = abs(fee) * -1
            f = -1
        msg = f"""
        更新收银机端饭票支付时天阙营销补贴打款的金额:
        流水ID: {transaction.id}
        订单总金额: {transaction.bill_fee}
        转账金额: {fee}
        支付金额: {transaction.paid_fee}
        补贴金额 : {platform_discount_fee}
        饭票佣金: {fanpiao_commission_fee}
        费率: {round(settlement_rate, 5)}
        fee = {fee} = int({f} * round((abs({transaction.paid_fee}) + {platform_discount_fee} - abs({fanpiao_commission_fee})) * (1 - {round(settlement_rate, 5)})))
        """
        logger.info(msg)
        self.update_transfer_fee(0, transaction=transaction, exact_fee=fee)

    def update_scan_code_order_merchant_transfer_fee(self, order, transaction):
        """扫码点餐补贴金额
        1. 饭票: order.paid_in_fee * (1 - 商户在时来的费率)
        2. 钱包买单: order.paid_in_fee * (1 - 商户在时来的费率)
        3. 支付宝: order.paid_in_fee * (1 - 商户在时来的费率)
        4. 天阙支付:
            1. 如果剩下的为负数.则负数金额为时来补贴给商户的金额.
            2. 随行付从商户账户里扣除的手续费,所以还要补贴费率给商户: 总金额 * 天阙给时来的费率(0.0025)
        """
        fee = 0

        if transaction.pay_method in [
            wallet_pb.Transaction.FANPIAO_PAY,
            wallet_pb.Transaction.WALLET,
            wallet_pb.Transaction.ALIPAY,
        ]:
            settlement_rate = self.merchant_manager.get_settlement_rate()
            fee = int(order.paid_in_fee * (1 - settlement_rate) + 0.5)
            logger.info("天阙扫码点餐营销补贴: {}, {}*{}={}".format(transaction.id, order.paid_in_fee, settlement_rate, fee))
        elif transaction.pay_method in [wallet_pb.Transaction.TIAN_QUE_PAY]:
            allot = self.get_scan_code_order_shilai_allot_value(transaction, order)
            fee = int(abs(transaction.paid_fee) * self.tian_que_pay_shilai_commission_rate + 0.5)
            if allot < 0:
                fee += abs(allot)

        if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
            fee = fee * -1

        logger.info("天阙扫码点餐营销补贴: {}, {}".format(transaction.id, fee))
        if fee != 0:
            self.update_transfer_fee(fee, transaction)
        return fee

    def update_member_card_recharge_merchant_transfer_fee(self, transaction):
        if transaction.pay_method in [wallet_pb.Transaction.TIAN_QUE_PAY]:
            fee = int(transaction.paid_fee * constants.TIAN_QUE_PAY_SHILAI_COMMISSION_RATE + 0.5)
            self.update_transfer_fee(fee, transaction)

    def update_transfer_fee(self, fee, transaction, exact_fee=None):
        """
        :exact_fee: transaction对应的补贴金额
        TODO: fee参数应尽早废弃
        """
        transaction_da = TransactionDataAccessHelper()
        transfers = transaction_da.get_merchant_day_transfers(transaction_id=transaction.id)
        if not transfers:
            transfer = wallet_pb.MerchantDayTransfer()
        else:
            transfer = transfers[0]
        transfer.transaction_id = transaction.id
        transfer.transaction_timestamp = transaction.paid_time
        transfer.merchant_id = self.merchant.id
        transfer.fee += fee
        if exact_fee is not None:
            transfer.fee = exact_fee
        if transfer.fee != 0:
            transaction_da.add_or_update_merchant_day_transfer(transfer)

    def query_balance(self, mno=None):
        kargs = {"mno": mno, "type": "01"}
        if mno is None:
            kargs = {"mno": self.tian_que_pay_info.mno, "type": "01"}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_BALANCE)
        ret = requests.post(url, json=params).json()
        return ret

    def query_settlement(self, query_time):
        kargs = {"mno": self.tian_que_pay_info.mno, "queryTime": query_time}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_SETTLEMENT)
        ret = requests.post(url, json=params).json()
        return ret

    def query_refund(self, transaction_id):
        kargs = {"mno": self.tian_que_pay_info.mno, "ordNo": transaction_id}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_REFUND)
        ret = requests.post(url, json=params).json()
        return ret

    def query_transfer_info(self, transaction_id):
        kargs = {"orderNo": transaction_id}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_TRANSFER_INFO)
        ret = requests.post(url, json=params).json()
        return ret

    def query_sign_contract(self, mno=None):
        if mno is None:
            mno = self.tian_que_pay_info.mno
        kargs = {"mno": mno}
        params = self.build_params(kargs)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_QUERY_SIGN_CONTRACT)
        ret = requests.post(url, json=params).json()
        return ret

    def merchant_setup(self, rate):
        if not self.tian_que_pay_info:
            return
        params = {
            "mno": self.tian_que_pay_info.mno,
            "qrcodeList": [
                {"rateType": "01", "rate": f"{rate}"},
                {"rateType": "02", "rate": f"{rate}"},
                {"rateType": "06", "rate": f"{rate}"},
            ],
        }
        params = self.build_params(params)
        url = "{}{}".format(self.tian_que_pay_domain, self.MERCHANT_SETUP)
        ret = requests.post(url, json=params)
        ret = ret.json()
        return ret

    def try_post(self, url, params, timeout=30, try_times=15):
        Ret = namedtuple("Ret", ["flag", "ret"])
        ret = {}
        if try_times == -1:
            logger.error(f"天阙支付尝试5次后失败, mno={self.mno}, method=POST, url={url}, json_data={params}")
            return Ret(flag=False, ret={})
        try:
            logger.info(f"天阙支付请求信息, mno={self.mno}, method=POST, url={url}, json_data={params}")
            start_time = time.process_time()
            # response = requests.post(url, json=params, timeout=timeout)
            # response = http_session.post(url, json=params, timeout=timeout)
            # http = urllib3.PoolManager(cert_reqs='CERT_NONE')
            response = http.request(
                "POST", url, body=json.dumps(params), headers={"Content-Type": "application/json"}, timeout=timeout
            )
            logger.info(
                f"天阙支付响应信息, 记录cpu耗时: {(time.process_time() - start_time) * 1000:.3f}ms, mno={self.mno}, method=POST, url={url}, reponse={response.data.decode()}"
            )
            # if not response.ok:
            if response.status != 200:
                time.sleep(0.2)
                # logger.error(f"天阙支付响应错误, status={response.status_code}, reason={response.reason}")
                logger.error(f"天阙支付响应错误, status={response.status}, reason={response.reason}")
                return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times - 1)
            # ret = response.json()
            ret = json.loads(response.data.decode())
            biz_code = ret.get("respData", {}).get("bizCode")
            biz_msg = ret.get("respData", {}).get("bizMsg")
            if ret.get("code") == "0001" and ret.get("msg") == "原交易订单已做过分账，不允许重复分账":
                return Ret(flag=False, ret=ret)
            if biz_code == "0001":
                if biz_msg == "二维码已失效，请刷新后重试" or biz_msg == "支付宝提示:请消费者刷新付款码":
                    return Ret(flag=False, ret=ret)
            if ret.get("code") == "0000" and ret.get("respData", {}).get("bizCode") == "2002":  # 支付中
                return Ret(flag=True, ret=ret)
            if ret.get("code") == "0002" and ret.get("msg") == "ordNo不能重复":
                return Ret(flag=True, ret=ret)
            if not (ret.get("code") == "0000" and ret.get("respData", {}).get("bizCode") == "0000"):
                time.sleep(0.2)
                if try_times == 0:
                    return Ret(flag=False, ret=ret)
                return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times - 1)
            return Ret(flag=True, ret=ret)
        except requests.exceptions.RequestException as e:
            time.sleep(0.2)
            logger.error(f"天阙支付请求错误, {e}")
            return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times - 1)
        except Exception as ex:
            logger.exception(f"天阙支付接口异常, mno={self.mno}, method=POST, url={url}, json_data={params}", exc_info=ex)
        return Ret(flag=False, ret=ret)

    def __round(self, value):
        value = int(value + 0.5)
        return value

# -*- coding: utf-8 -*-

import logging
import time
import requests
from collections import namedtuple
from datetime import datetime

import proto.coupon_category_pb2 as coupon_category_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
import proto.staff_pb2 as staff_pb
import proto.finance.wallet_pb2 as wallet_pb
import proto.ui.user_pb2 as user_ui_pb
import proto.user_pb2 as user_pb
import proto.page.user_center_pb2 as user_center_pb
import proto.page.user_merchant_info_pb2 as user_merchant_info_pb
from business_ops.coupon_manager import CouponManager
from business_ops.member_card_manager import MemberCardManager
from business_ops.template_message_helper import TemplateMessageHelper
from business_ops.merchant_phone_member import MerchantPhoneMemberManager
from cache.redis_client import RedisClient
from common import constants
from common.config import config
from common.utils import access_token_helper
from common.utils import date_utils
from common.utils import id_manager
from common.cache_server_keys import CacheServerKeys
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.merchant_user_da_helper import MerchantUserDataAccessHelper
from dao.staff_da_helper import StaffDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import base_responses
from service import error_codes
from service import errors
from wechat_lib import coupon_api_helper
from wechat_lib import api_helper_util

logger = logging.getLogger(__name__)


class UserManager:
    def __init__(self):
        pass

    def get_platform_user_by_id(self, from_platform, user_id):
        user = None
        if from_platform == constants.PLATFORM_MERCHANT:
            user = MerchantUserDataAccessHelper().get_user(user_id)
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            user = StaffDataAccessHelper().get_staff(user_id)
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            user = StaffDataAccessHelper().get_staff(user_id)
        else:
            user = UserDataAccessHelper().get_user(user_id)

        return user

    def get_platform_user_by_union_id(self, from_platform, union_id):
        user = None
        if from_platform == constants.PLATFORM_MERCHANT:
            user = MerchantUserDataAccessHelper().get_user_by_union_id(union_id)
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            user = StaffDataAccessHelper().get_staff_by_union_id(union_id)
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            user = StaffDataAccessHelper().get_staff_by_union_id(union_id)
        else:
            user = UserDataAccessHelper().get_user_by_union_id(union_id)

        return user

    def get_platform_user_by_openid(self, from_platform, openid):
        if from_platform == constants.PLATFORM_MERCHANT:
            return MerchantUserDataAccessHelper().get_user_by_openid(openid)
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            return StaffDataAccessHelper().get_user_by_openid(openid)
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            return StaffDataAccessHelper().get_user_by_openid(openid)
        else:
            return UserDataAccessHelper().get_user_by_openid(openid)
        return None

    def make_platform_user(self, from_platform):
        user = None
        if from_platform == constants.PLATFORM_MERCHANT:
            user = user_pb.MerchantUser()
            user.id = id_manager.generate_user_id()
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            user = staff_pb.ShilaiStaff()
            user.id = id_manager.generate_staff_id()
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            user = staff_pb.ShilaiStaff()
            user.id = id_manager.generate_staff_id()
        else:
            user = user_pb.User()
            user.id = id_manager.generate_user_id()

        # 初始化用户信息
        user.joined_time = date_utils.timestamp_second()
        return user

    def get_or_make_platform_user_by_id(self, from_platform, user_id):
        user = self.get_platform_user_by_id(from_platform, user_id)
        if not user:
            user = self.make_platform_user(from_platform)
        return user

    def get_or_make_platform_user_by_openid(self, from_platform, openid):
        user = self.get_platform_user_by_openid(from_platform, openid)
        if not user:
            user = self.make_platform_user(from_platform)
            logger.info(f"通过openid未查找到用户, openid={openid}, from_platform={from_platform}, 创建user={user}")
        return user

    def get_or_make_platform_user_by_union_id(self, from_platform, union_id):
        user = self.get_platform_user_by_union_id(from_platform, union_id)
        if not user:
            user = self.make_platform_user(from_platform)
        return user

    def update_or_create_user_by_platform(self, from_platform, user):
        if from_platform == constants.PLATFORM_MERCHANT:
            MerchantUserDataAccessHelper().update_or_create_user(user)
        elif from_platform == constants.PLATFORM_SHILAI_STAFF:
            StaffDataAccessHelper().update_or_create_staff(user)
        elif from_platform == constants.PLATFORM_SHIYI_CAT:
            StaffDataAccessHelper().update_or_create_staff(user)
        else:
            UserDataAccessHelper().update_or_create_user(user)

    def get_user_member_profile(self, merchant_id, activate_ticket):
        """根据微信回传的activate_ticket, 获取用户在小程序中填写的注册会员信息

        Args:
            merchant_id: (string) 商户ID
            activate_ticket: (string) 从微信平台获取的activate_ticket
        """

        def _get_common_field_value(common_field_list, field_name):
            """从common_field_list列表中查找并返回对应field_name的相应value

            Args:
                common_field_list: (list of key-value objects) 用户填写的个人信息
                field_name: (string) 需要查找的信息域名称，如姓名，生日等
            """
            for field in common_field_list:
                if field['name'] == field_name:
                    return field['value']
            return None

        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant.join_method == merchant_rules_pb.SHILAI_PLATFORM_AUTHORIZER:
            access_token = access_token_helper.get_authorizer_access_token(
                merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
            )
        elif merchant.join_method == merchant_rules_pb.SHILAI_MP_SUBMERCHANT:
            access_token = access_token_helper.get_shilai_app_access_token(config.SHILAI_MP_APPID)

        if not access_token:
            return None

        url = 'https://api.weixin.qq.com/card/membercard/activatetempinfo/get?access_token={}'.format(access_token)
        req_obj = {'activate_ticket': activate_ticket}
        resp = requests.post(url, json=req_obj)
        resp_json = resp.json()

        common_field_list = resp_json['info']['common_field_list']
        member_profile = user_pb.MemberProfile()
        member_profile.name = _get_common_field_value(common_field_list, 'USER_FORM_INFO_FLAG_NAME')
        member_profile.sex = _get_common_field_value(common_field_list, 'USER_FORM_INFO_FLAG_SEX')
        member_profile.mobile_phone = _get_common_field_value(common_field_list, 'USER_FORM_INFO_FLAG_MOBILE')
        birthday = _get_common_field_value(common_field_list, 'USER_FORM_INFO_FLAG_BIRTHDAY')
        # 生日字符串格式：'1988-08-10'
        bd = time.strptime(birthday, '%Y-%m-%d')
        member_profile.birth_year = bd.tm_year
        member_profile.birth_month = bd.tm_mon
        member_profile.birth_day = bd.tm_mday

        return member_profile

    def update_user_member_profile(self, user_id, member_profile):
        """为指定用户设定或更新MemberProfile

        Args:
            user_id: (string) 时来平台用户ID
            member_profile: (user_pb.MemberProfile) 需要更新的MemberProfile信息
        """
        user_da = UserDataAccessHelper()
        user = user_da.get_user(user_id)
        if user:
            user.member_profile.CopyFrom(member_profile)
            user_da.update_or_create_user(user)

    def get_user_profile(self, user_id):
        user = UserDataAccessHelper().get_user(user_id)
        user_profile = user_ui_pb.UserProfile()
        user_profile.user_info.CopyFrom(user)

        # merchant_da = MerchantDataAccessHelper()
        # visited_stores = {}
        # transactions = TransactionDataAccessHelper().get_transactions(
        #     payer_id=user.id, state=wallet_pb.Transaction.SUCCESS)
        # for transaction in transactions:
        #     if transaction.payee_store_id:
        #         merchant_id = transaction.payee_id
        #         merchant = merchant_da.get_merchant(merchant_id)
        #         store = merchant.stores[0]
        #         if store.id in visited_stores:
        #             continue
        #         visited_stores[store.id] = True

        #         visited_store = user_profile.visited_stores.add()
        #         visited_store.merchant_id = merchant_id
        #         visited_store.store_id = store.id
        #         visited_store.store_name = store.name
        #         if len(store.store_photo_urls) > 0:
        #             visited_store.store_image_url = store.store_photo_urls[0]
        #         visited_store.avg_cost_per_person = store.avg_cost_per_person

        return user_profile

    def activate_member_card(self, user_id, merchant_id, card_id):
        """通知微信平台激活一张会员卡。

        Args:
            user_id: (string) 时来平台用户ID
            merchant_id: (string) 商户ID
            card_id: (string) 已被用户领取的会员卡ID
        """
        base_url = 'https://api.weixin.qq.com/card/membercard/activate'
        url = api_helper_util.get_token_embedded_url(merchant_id, base_url)
        req_json = {
            'membership_number': card_id,
            'code': card_id,
        }
        # 完成激活
        requests.post(url, json=req_json)

    def handle_wx_activate_member_card(self, user_id, card_category_id, card_id, activate_ticket, form_id=None):
        """利用微信平台的wx_activate途径激活一张会员卡。

        Args:
            user_id: (string) 时来平台用户ID
            card_category_id: (string) 会员卡类型ID
            card_id: (string) 用户领取的会员卡ID
            activate_ticket: (string) 从微信平台获取的activate_ticket
            form_id: (string) 在小程序中提交的表单ID

        Returns:
            (json) 用于回传前端的响应JSON结构体
        """
        merchant = MerchantDataAccessHelper().get_merchant_for_card_category(card_category_id)
        if not merchant:
            return None

        member_profile = self.get_user_member_profile(merchant.id, activate_ticket)
        if not member_profile:
            return None

        # 会员在时来平台上首次注册，更新该用户的MemberProfile
        self.update_user_member_profile(user_id, member_profile)
        # 创建会员卡领取信息
        MemberCardManager().create_member_card(user_id, card_category_id, card_id)
        # 通知微信平台激活会员卡
        # self.activate_member_card(user_id, merchant.id, card_id)
        # 为该用户创建一张新会员优惠券
        CouponManager().issue_coupons_to_user(merchant.id, user_id, coupon_category_pb.CouponCategory.NEW_MEMBER)
        # 发送服务通知
        if form_id is not None:
            TemplateMessageHelper().send_member_card_registered_message(merchant.id, user_id, form_id)

        return base_responses.create_responses_obj(error_codes.SUCCESS, 'success')

    def handle_auto_activate_member_card(self, user_id, card_category_id, card_id, form_id=None):
        """利用微信平台的auto_activate途径激活一张会员卡。通过此途径注册与激活会员卡，
           表明该用户之前已经在时来平台上注册过会员信息，可以重复利用该信息，无需用户重新填写。

        Args:
            user_id: (string) 时来平台用户ID
            card_category_id: (string) 会员卡类型ID
            card_id: (string) 用户领取的会员卡ID. NOTE: 通过auto_activate方式传递进来为经过加密的card_id，需先通过微信平台解码
            form_id: (string) 在小程序中提交的表单ID

        Returns:
            (json) 用于回传前端的响应JSON结构体
        """
        # card_id经过微信平台加密，需要先对其解密后获得真实ID。详见:
        #   https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1451025239
        merchant = MerchantDataAccessHelper().get_merchant_for_card_category(card_category_id)
        if not merchant:
            return base_responses.create_responses_obj(
                error_codes.MERCHANT_MEMBERCARD_CATEGORY_NOT_FOUND, error_codes.MERCHANT_MEMBERCARD_CATEGORY_NOT_FOUND_MSG
            )

        real_card_id = coupon_api_helper.get_decrypted_code(merchant.id, card_id)
        if real_card_id:
            user = UserDataAccessHelper().get_user(user_id)
            if user.HasField('member_profile'):
                # 创建会员卡领取信息
                MemberCardManager().create_member_card(user_id, card_category_id, real_card_id)
                # 为该用户创建一张新会员优惠券
                CouponManager().issue_coupons_to_user(merchant.id, user_id, coupon_category_pb.CouponCategory.NEW_MEMBER)
                # 发送服务通知
                if form_id is not None:
                    TemplateMessageHelper().send_member_card_registered_message(merchant.id, user_id, form_id)
                return base_responses.create_responses_obj(error_codes.SUCCESS, 'success')
            else:
                return base_responses.create_responses_obj(
                    error_codes.USER_MEMBER_PROFILE_NOT_FOUND, error_codes.USER_MEMBER_PROFILE_NOT_FOUND_MSG
                )
        else:
            return base_responses.error_responses()

    def get_user_merchant_info(self, user_id, merchant_id):
        user_da = UserDataAccessHelper()
        user = user_da.get_user(user_id)
        if not user:
            raise errors.ShowError("用户不存在")
        user_merchant_info = user_merchant_info_pb.UserMerchantInfo()
        user_merchant_info.user_id = user.id
        user_merchant_info.phone = user.member_profile.mobile_phone
        manager = MerchantPhoneMemberManager(merchant_id=merchant_id)
        phone_member = manager.get_merchant_phone_member(user_id=user.id)
        if not phone_member:
            return user_merchant_info
        if phone_member.status != user_pb.MerchantPhoneMember.NORMAL:
            return user_merchant_info
        user_merchant_info.phone_member_discount = phone_member.discount
        user_merchant_info.is_phone_member = True
        return user_merchant_info

    def get_user_center_info(self, user_id):
        """用户中心页面
        Args:
            user_id: (string)
        Return:
            page/user_center.proto.UserInfo
        """

        user = UserDataAccessHelper().get_user(user_id)
        user_center_info = user_center_pb.UserCenterInfo()
        user_center_info.id = user_id
        user_center_info.headimgurl = user.member_profile.head_image_url
        user_center_info.nickname = user.member_profile.nickname
        user_center_info.use_coupon_count = 0
        user_center_info.total_save = 0
        recently_order = TransactionDataAccessHelper().get_user_recently_order(user_id)
        if recently_order:
            merchant = MerchantDataAccessHelper().get_merchant(recently_order.payee_id)
            store = merchant.stores[0]
            user_center_info.logo_url = merchant.basic_info.logo_url
            user_center_info.store_name = store.name
            if recently_order.state == wallet_pb.Transaction.SUCCESS:
                user_center_info.event_time = recently_order.paid_time
            else:
                user_center_info.event_time = recently_order.create_time
        return user_center_info

    def get_user_home_page(self, user_id):
        """用户个人主页
        Args:
            user_id: (string)
        """
        user = UserDataAccessHelper().get_user(user_id)
        home_page = user_center_pb.UserHomePage()
        home_page.id = user_id
        home_page.headimgurl = user.member_profile.head_image_url
        home_page.nickname = user.member_profile.nickname
        home_page.birth_year = user.member_profile.birth_year
        home_page.birth_month = user.member_profile.birth_month
        home_page.birth_day = user.member_profile.birth_day
        home_page.sex = user.member_profile.sex
        home_page.individuality_signature = user.member_profile.individuality_signature
        for tag in user.member_profile.tags:
            home_page.tags.append(tag)
        return home_page

    def get_user_visited_stores(self, user_id, page=None, size=None):
        """用户去过的餐厅
        Args:
            user_id: (string)
            page:
            size:
        """
        transactions = TransactionDataAccessHelper().get_user_visited_stores(user_id, page=page, size=size)
        result = []
        for transaction in transactions:
            merchant = MerchantDataAccessHelper().get_merchant(transaction.get("_id"))
            store = user_center_pb.Store()
            store.id = merchant.id
            store.dining_type = merchant.dining_type
            store.avg_cost_per_person = merchant.stores[0].avg_cost_per_person
            if len(merchant.stores[0].store_photo_urls) > 0:
                store.img = merchant.stores[0].store_photo_urls[0]
            result.append(store)
        return result

    def add_shipping_address(
        self, user_id, mobile_phone, username, gender, street, house_number, longitude, latitude, province, city
    ):
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.UserNotFound()
        _address = user.member_profile.shipping_address.add()
        _address.id = id_manager.generate_common_id()
        _address.mobile_phone = mobile_phone
        _address.username = username
        _address.gender = gender
        _address.street = street
        _address.house_number = house_number
        _address.latitude = latitude
        _address.longitude = longitude
        address_compontent = self.get_city(latitude, longitude)
        if address_compontent:
            _address.city = address_compontent.city
            _address.province = address_compontent.province
        UserDataAccessHelper().update_or_create_user(user)

    def get_city(self, lat, lng):
        AddressCompontent = namedtuple("AddressCompontent", ['province', 'city'])
        url = "http://api.map.baidu.com/reverse_geocoding/v3/?ak=c9Pfd2lNjgsy6TkNCRit24yNOGOMq2BG&output=json&coordtype=wgs84ll&location={},{}&language_auto=1".format(
            lat, lng
        )
        try:
            result = requests.post(url).json()
            logger.info("百度API调用返回: {}".format(result))
            province = result.get('result').get('addressComponent').get('province')
            city = result.get('result').get('addressComponent').get('city')
            return AddressCompontent(province, city)
        except Exception as ex:
            logger.error("百度API调用返回出错: {}".format(ex))
        return None

    def update_shipping_address(
        self, shipping_address_id, user_id, mobile_phone, username, gender, street, house_number, latitude, longitude
    ):
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            raise errors.UserNotFound()
        for _address in user.member_profile.shipping_address:
            if not (_address.id == shipping_address_id):
                continue
            if mobile_phone:
                _address.mobile_phone = mobile_phone
            if username:
                _address.username = username
            if gender:
                _address.gender = gender
            if street:
                _address.street = street
            if house_number:
                _address.house_number = house_number
            if latitude and longitude:
                _address.latitude = latitude
                _address.longitude = longitude
            UserDataAccessHelper().update_or_create_user(user)
            break

    def generate_phone_verify_code(self, scene, phone, user=None, user_id=None):
        if user is None and user_id is not None:
            user_da = UserDataAccessHelper()
            user = user_da.get_user(user_id=user_id)
        if user is None:
            raise errors.UserNotFound()
        conn = RedisClient().get_connection()
        key = CacheServerKeys.get_user_phone_code_verify_key(user.id)
        old_phone = user.member_profile.mobile_phone
        if old_phone == phone:
            raise errors.Error(err=error_codes.SAME_PHONE_DO_NOT_NEED_CHANGE)
        if conn.exists(key):
            data = conn.hgetall(key)
            code = data.get(b"code").decode()
            logger.info(f"生成手机验证码{user.id} {code} {phone} {old_phone}")
            return
        pipeline = conn.pipeline()
        code = id_manager.generate_number_string()
        logger.info(f"生成手机验证码: {user.id} {code} {phone} {old_phone}")
        pipeline.hset(key, "scene", str(scene))
        pipeline.hset(key, "new_phone", phone)
        pipeline.hset(key, "code", str(code))
        pipeline.hset(key, "old_phone", old_phone)
        pipeline.expire(key, date_utils.ONE_MINUTE * 5)
        pipeline.execute()
        return code

    def phone_code_verify(self, code, user=None, user_id=None):
        user_da = UserDataAccessHelper()
        if user is None and user_id is not None:
            user = user_da.get_user(user_id=user_id)
        if user is None:
            raise errors.UserNotFound()
        key = CacheServerKeys.get_user_phone_code_verify_key(user.id)
        conn = RedisClient().get_connection()
        data = conn.hgetall(key)
        if data is None:
            raise errors.Error(err=error_codes.PHONE_VERIFY_CODE_NOT_MATCH)
        cache_code = data.get(b"code").decode()
        old_phone = data.get(b"old_phone").decode()
        new_phone = data.get(b"new_phone").decode()
        if cache_code != code:
            raise errors.Error(err=error_codes.PHONE_VERIFY_CODE_NOT_MATCH)
        users = user_da.get_users_by_condition(phone=old_phone)
        for user in users:
            logger.info(f"修改用户手机号: {user.id} {new_phone} {old_phone}")
            user.member_profile.mobile_phone = new_phone
            user_da.update_or_create_user(user)
        conn.delete(key)

    def get_user(self, user_id):
        user_da = UserDataAccessHelper()
        user = user_da.get_user(user_id=user_id)
        return user

    def query_users(self, user_id):
        user_da = UserDataAccessHelper()
        return user_da.query_users(user_id=user_id)

    def __set_user_risk_info(self, setting=None, is_purchase_risk_control=None, fanpiao_risk_level=None):
        user_da = UserDataAccessHelper()
        if is_purchase_risk_control is not None:
            setting.fanpiao_risk_control.is_purchase_risk_control = is_purchase_risk_control
        if fanpiao_risk_level is not None:
            setting.fanpiao_risk_control.level = fanpiao_risk_level
        user_da.add_or_update_user_platform_setting(setting)

    def set_user_fanpiao_purchase_risk_control(self, user, level):
        user_da = UserDataAccessHelper()
        setting = user_da.get_user_platform_setting(user_id=user.id)
        if setting is None:
            setting = self.__create_user_platform_setting(user)
        setting.fanpiao_risk_control.update_time = date_utils.timestamp_second()
        self.__set_user_risk_info(setting=setting, is_purchase_risk_control=True, fanpiao_risk_level=level)

    def clear_user_fanpiao_pruchase_risk_control(self, user):
        user_da = UserDataAccessHelper()
        setting = user_da.get_user_platform_setting(user_id=user.id)
        if not setting:
            return
        update_time = setting.fanpiao_risk_control.update_time
        now = datetime.now()
        update_datetime = datetime.fromtimestamp(update_time)
        if not date_utils.is_same_day(now, update_datetime):
            # 如果不是同一天则不能自动取消风控
            return
        self.__set_user_risk_info(setting=setting, is_purchase_risk_control=False)

    def __create_user_platform_setting(self, user):
        setting = user_pb.UserPlatformSetting()
        setting.user_id = user.id
        setting.fanpiao_risk_control.update_time = date_utils.timestamp_second()
        return setting

    def qrcode_login(self, id, user_id):
        if not user_id or not id:
            return False
        record = user_pb.QrcodeLoginRecord()
        record.id = id
        record.user_id = user_id
        record.create_time = date_utils.timestamp_second()
        user_da = UserDataAccessHelper()
        return user_da.qrcode_login_update(record)

    def qrcode_login_check(self, id):
        if not id:
            return {}
        user_da = UserDataAccessHelper()
        return user_da.get_qrcode_login(matcher={'id': id}) or {}

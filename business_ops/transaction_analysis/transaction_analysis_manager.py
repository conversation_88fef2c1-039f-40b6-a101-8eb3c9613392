from pprint import pprint
from datetime import datetime
from collections import defaultdict
from business_ops.base_manager import BaseManager
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper


class TransactionAnalysisManager(BaseManager):

    @property
    def transaction_dao(self):
        return TransactionDataAccessHelper()

    @property
    def merchant_dao(self):
        return MerchantDataAccessHelper()

    def get_merchant_simple_info(self):
        merchants = self.merchant_dao.__collection.find(
            {'status': {'$in': ['RUNNING', 'DELETED']}},
            {'id': 1, 'status': 1, "basicInfo": 1, 'stores': 1, 'createTimestamp': 1, 'activateTimestamp': 1}
        )
        results = list()
        for doc in merchants:
            store = dict()
            store['id'] = doc['id']
            store['status'] = doc['status']
            store['name'] = doc['basicInfo']["name"] if doc.get("basicInfo") else (
                doc['stores'][0]['name'] if doc['stores'] else "")
            store['createDay'] = datetime.fromtimestamp(
                int(doc["createTimestamp"] if doc["activateTimestamp"] == '0' else doc["activateTimestamp"])).strftime(
                "%Y-%m-%d")
            results.append(store)
        return results

    def aggregate_transaction_by_type(self, start_date, end_date, multiple_types):
        """
        dimension:
            - merchant id
            - type
        metrics:
            - count
            - paidFee
        Args:
            start_date:
            end_date:

        Args:
            start_date:
            end_date:
            multiple_types:

        Returns:

        """
        start_time = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
        end_time = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp()) + 24 * 3600 - 1
        match = {
            'state': 'SUCCESS', 'paidTime': {'$gte': str(start_time), '$lte': str(end_time)},
            "type": {'$in': multiple_types}
        }
        projection = {"merchantId": "$payeeId", "type": 1, 'paidFee': {"$toInt": "$paidFee"}}
        group = {
            '_id': {"merchantId": "$merchantId", "type": "$type"}, 'count': {"$sum": 1},
            "billFee": {"$sum": {"$toInt": "$billFee"}},
            "paidFee": {"$sum": "$paidFee"}
        }
        pipeline = [
            {"$match": match},
            {"$project": projection},
            {"$group": group},
            {'$project': {
                'merchantId': '$_id.merchantId', 'type': '$_id.type',
                'paidFee': 1, 'count': 1, '_id': 0, 'billFee': 1,
            }},
        ]
        pprint(pipeline)
        merge = defaultdict(dict)
        for doc in self.transaction_dao.__collection.aggregate(pipeline):
            doc.update(doc.pop('_id'))
            flag = -1 if 'REFUND' in doc['type'] else 1
            bill_fee = doc['billFee']
            paid_fee = doc['paidFee']
            count = doc['count'] * flag
            if merge[doc['merchantId']].get('paidFee') is None:
                merge[doc['merchantId']]['count'] += count
                merge[doc['merchantId']]['billFee'] += bill_fee
                merge[doc['merchantId']]['paidFee'] += paid_fee
            else:
                merge[doc['merchantId']]['count'] = count
                merge[doc['merchantId']]['paidFee'] = paid_fee
                merge[doc['merchantId']]['billFee'] = bill_fee
        results = list()
        for merchant_id in merge:
            results.append({
                "merchantId": merchant_id,
                "billFee": merge[merchant_id]['billFee'], "paidFee": merge[merchant_id]['paidFee'],
                'count': merge[merchant_id]['count'], 'startTime': start_time, 'endTime': end_time
            })
        return results

    def aggregate_coupon_transaction(self, start_date, end_date):
        """
        券包的购买
        """
        multi_types = ['COUPON_PACKAGE_REFUND', 'COUPON_PACKAGE_PURCHASE']
        return self.aggregate_transaction_by_type(start_date, end_date, multi_types)

    def aggregate_fanpiao_transaction(self, start_date, end_date):
        """
        饭票的购买
        """
        multi_types = ['FANPIAO_REFUND', 'FANPIAO_PURCHASE']
        return self.aggregate_transaction_by_type(start_date, end_date, multi_types)


if __name__ == '__main__':
    """
    python -m business_ops.transaction_analysis.transaction_analysis_manager
    """
    manager = TransactionAnalysisManager()
    manager.aggregate_coupon_transaction("2021-09-01", "2021-09-16")


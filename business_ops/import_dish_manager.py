# -*- coding: utf-8 -*-

"""
文件格式为: xlsx
当前能处理的sheet名: 菜品导入

菜品导入举例如下:
    商品名称 菜品分类 标准单位 规格类别       规格          价格(元)  做法类别   做法                 加料
    菜名     类名     份       规格1:10,规格2 超大/大,中/小 20        辣度,甜味  中辣/不辣,很甜/不甜  土豆,粉丝:2
规格:10 10的意思是此规格变价为10元
粉丝:2 2的意思是此配料价格为2元
"""

import traceback
import logging
import os
from collections import namedtuple
from collections import OrderedDict
from datetime import datetime

from openpyxl import Workbook

import proto.ordering.dish_pb2 as dish_pb
from business_ops.ordering.supply_condiment_manager import SupplyCondimentManager
from business_ops.ordering.dish_category_manager import DishCategoryManager
from business_ops.base_manager import BaseManager
from business_ops.ordering.attr_manager import AttrManager
from business_ops.ordering.dish_manager import DishManager
from business_ops.merchant_manager import MerchantManager
from cache.redis_client import RedisClient
from dao.ordering.dish_attr_da_helper import DishAttrDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class ImportDishManager(BaseManager):
    def __init__(self, **kargs):
        super(ImportDishManager, self).__init__(**kargs)
        self.attr_groups = {}
        self.supply_condiments = {}
        self.supply_condiment_sort = {}
        self.attr_sort = {}
        self.dishes = []
        self.categories = {}
        self.merchant_dish_xlsx_path = "/data/merchant-dish-xlsx"
        if not os.path.exists(self.merchant_dish_xlsx_path):
            os.makedirs(self.merchant_dish_xlsx_path)
        self.very_max_price = 999999
        domain = os.environ.get("STAFF_ASSIST_FILE_DOMAIN")
        self.xlsx_url_prefix = f"{domain}/main_service/merchant-assist/download"

    def parse_excel(self, wb):
        """解析excel文档"""
        for sheet in wb:
            self.import_dish(sheet)
        redis_client = RedisClient().get_connection()
        redis_client.delete(f"dishes_key_exists_{self.merchant.id}")
        redis_client.delete(f"dishes_key_{self.merchant.id}")

    def __replace_common(self, values):
        results = []
        for v in values:
            if v is not None and isinstance(v, str):
                v = v.replace("，", ",")
            results.append(v)
        return results

    def import_dish(self, sheet):
        dish_names = set()
        for index, values in enumerate(sheet.values):
            if index == 0:
                continue
            values = self.__replace_common(values)
            name = values[0]
            if name in dish_names:
                continue
            try:
                dish_names.add(name)
                category = values[1]
                unit = values[2]
                attr_groups_0 = values[3]
                attrs_0 = values[4]
                price = values[5]
                attr_groups_1 = values[6]
                attrs_1 = values[7]
                supply_condiments = values[8]
                package_box_groups = values[9]
                package_boxs = values[10]

                price = self.preprocess_price(price)
                specification_attrs = self.preprocess_price_specification_attrs(price, attr_groups_0, attrs_0)
                common_attrs = self.preprocess_attrs(attr_groups_1, attrs_1)
                package_boxs = self.preprocess_attrs(package_box_groups, package_boxs)
                supply_condiments = self.preprocess_supply_condiments(supply_condiments)
                price = specification_attrs.price

                self.parse(
                    name=name,
                    category_name=category,
                    unit=unit,
                    specification_attrs=specification_attrs,
                    common_attrs=common_attrs,
                    price=price,
                    supply_condiments=supply_condiments,
                    index=index,
                    package_boxs=package_boxs,
                )
            except:
                logger.error(f"导入出错: value={values}, error_msg={traceback.format_exc()}")
                raise errors.ShowError(str(values))

        for name, group in self.attr_groups.items():
            attr_da = DishAttrDataAccessHelper()
            attr_da.add_or_update_attr_group(group)

        ordering_da = OrderingServiceDataAccessHelper()

        ordering_da.add_or_update_dishes(dishes=self.dishes, merchant_id=self.merchant.id)
        ordering_da.add_or_update_categories(categories=list(self.categories.values()), merchant_id=self.merchant.id)

    def parse(
        self, name, category_name, unit, specification_attrs, common_attrs, price, supply_condiments, index, package_boxs
    ):
        date = datetime.now().strftime("%Y%m%d")
        dish_manager = DishManager(merchant=self.merchant)
        dish = dish_manager.get_or_create_dish(name=name)
        if dish.sort == 0:
            dish.sort = int(date) + index

        if name:
            dish.name = str(name)

        if category_name:
            category = self.get_category(category_name=category_name)
            if category.sort == 0:
                category.sort = int(date) + index
            if len(dish.categories) > 0:
                dish.categories[0] = category.id
            else:
                dish.categories.append(category.id)

        if unit:
            dish.unit = unit

        if price:
            dish.price = price

        while dish.attrs:
            dish.attrs.pop()

        while dish.supply_condiments:
            dish.supply_condiments.pop()

        for index, supply_condiment in enumerate(supply_condiments.supply_condiments):
            supply_condiment = self.get_supply_condiment(sc=supply_condiment)
            sc = dish.supply_condiments.add()
            sc.name = supply_condiment.name
            sc.id = supply_condiment.id
            sc.market_price = supply_condiment.market_price
            sc.sort = self.calculate_supply_condiment_sort(sc)

        for group_name, _attrs in specification_attrs.groups.items():
            attr_group = self.get_attr_group(group_name, _attrs, group_type=dish_pb.Attr.SPECIFICATION)
            _attrs = set(["{}:{}".format(a.get("name"), a.get("reprice")) for a in _attrs])
            for index, attr in enumerate(attr_group.attrs):
                key = self.get_attr_duplicate_key(attr)
                if key not in _attrs:
                    continue
                self.parse_attr(dish, attr_group, attr)

        for group_name, _attrs in common_attrs.groups.items():
            attr_group = self.get_attr_group(group_name, _attrs, group_type=dish_pb.Attr.TASTE)
            _attrs = set(["{}:{}".format(a.get("name"), a.get("reprice")) for a in _attrs])
            for index, attr in enumerate(attr_group.attrs):
                key = self.get_attr_duplicate_key(attr)
                if key not in _attrs:
                    continue
                self.parse_attr(dish, attr_group, attr)

        for group_name, _attrs in package_boxs.groups.items():
            attr_group = self.get_attr_group(group_name, _attrs, group_type=dish_pb.Attr.TAKE_AWAY)
            _attrs = set(["{}:{}".format(a.get("name"), a.get("reprice")) for a in _attrs])
            for index, attr in enumerate(attr_group.attrs):
                key = self.get_attr_duplicate_key(attr)
                if key not in _attrs:
                    continue
                self.parse_attr(dish, attr_group, attr)

        if dish.name == "属性顺序":
            # 此菜品只用于更新菜品属性
            return
        if len(dish.categories) == 0:
            return
        self.dishes.append(dish)

    def preprocess_supply_condiments(self, supply_condiments):
        """预处理配置
        参数:
            配料举例: 土豆:10/红薯:3/金针菇
        返回:
            Preprocess.supply_condiments:
            [
                {"name": "土豆", "price": 1000},
                {"name": "红薯", "price": 300},
                {"name": "金针菇", "price": 0}
            ]
        """
        Preprocess = namedtuple("Preprocess", ["supply_condiments"])
        if not supply_condiments:
            return Preprocess([])
        supply_condiments = supply_condiments.split("/")
        scs = []
        for s in supply_condiments:
            s = s.split(":")
            if len(s) == 2:
                s_name = s[0]
                s_price = int(float(s[1]) * 100)
            elif len(s) == 1:
                s_name = s[0]
                s_price = 0
            else:
                continue
            scs.append({"name": s_name, "price": s_price})
        return Preprocess(scs)

    def preprocess_price(self, price):
        if not price:
            return 0
        return int(round(float(price) * 100))

    def preprocess_attrs(self, groups, attrs):
        """预处理属性与属性组,属性与属性组以','分隔
        参数:
            属性组: group1,group2
            属性: a0:2.01/a1:3.01,a00:3.31
        返回:
            Process.groups: {
                "groupName1": {"name": "name", "reprice": 101},
                "groupName2": {"name": "name", "reprice": 100}
            }
        """
        Preprocess = namedtuple("Preprocess", ["groups"])
        if not groups:
            return Preprocess({})
        groups = groups.split(",")
        attrs = attrs.split(",")
        minlen = min(len(groups), len(attrs))
        grs = {}
        for i in range(minlen):
            group_name = groups[i]
            _attrs = attrs[i]
            _attrs = _attrs.split("/")
            gas = []
            for attr in _attrs:
                attr = attr.split(":")
                if len(attr) == 2:
                    attr_reprice = int(float(attr[1]) * 100)
                    attr_name = attr[0]
                elif len(attr) == 1:
                    attr_reprice = 0
                    attr_name = attr[0]
                else:
                    continue
                gas.append({"name": attr_name, "reprice": attr_reprice})
            grs.update({group_name: gas})
        return Preprocess(grs)

    def preprocess_price_specification_attrs(self, price, groups, attrs):
        """预处理价格和规格
        当有多个规格属性组时,第一个属性组的最小价格将做为菜品的基础价格.同时,其它属性的价格需要做减价处理
        参数:
            价格举例: 17.81 元
            规格组举例: group1,group1
            规格举例: 大份:20.31/中份:10.31/小份:3.33,纯茶:3.5/芝士:4
        返回:
            Preprocess.price: 333
            Preprocess.groups:
            {
                "groupName1": [{"name": "大份", "reprice": 1797}, {"name": "中份", "reprice": 698}, {"name": "小份", "reprice": 0}],
                "groupName2": [{"name": "纯茶", "reprice": 17}, {"name": "name2", "reprice": 67}]
            }
        """
        Preprocess = namedtuple("Preprocess", ["price", "groups"])
        if not groups or not attrs:
            return Preprocess(price, {})
        groups = groups.split(",")
        attrs = attrs.split(",")
        minlen = min(len(groups), len(attrs))
        grs = {}
        min_reprice = self.very_max_price
        for i in range(minlen):
            _group_name = groups[i]
            _attrs = attrs[i]
            _attrs = _attrs.split("/")
            gas = []
            for _attr in _attrs:
                _attr = _attr.split(":")
                if len(_attr) == 2:
                    _attr_name = _attr[0]
                    try:
                        _attr_reprice = int(round(float(_attr[1]) * 100))
                    except:
                        _attr_reprice = 0
                elif len(_attr) == 1:
                    _attr_name = _attr[0]
                    _attr_reprice = 0
                else:
                    continue
                if i == 0:
                    min_reprice = min(_attr_reprice, min_reprice)
                gas.append({"name": _attr_name, "reprice": _attr_reprice})
            if i == 0:
                for g in gas:
                    g.update({"reprice": g.get("reprice") - min_reprice})
            gas.sort(key=lambda x: x.get("reprice"))
            if min_reprice != self.very_max_price:
                price = min_reprice
            grs.update({_group_name: gas})
        return Preprocess(price, grs)

    def parse_attr(self, dish, group, attr):
        key = self.get_attr_duplicate_key(attr)
        a = dish.attrs.add()
        a.id = attr.id
        a.name = attr.name
        a.reprice = attr.reprice
        a.group_name = group.name
        a.group_id = group.id
        a.is_multi_select = group.is_multi_select
        a.sort = self.calculate_attr_sort(attr)
        self.attr_sort.update({key: a.sort})
        a.type = group.attr_group_type

    def get_attr_duplicate_key(self, attr):
        return "{}:{}".format(attr.name, attr.reprice)

    def calculate_supply_condiment_sort(self, supply_condiment):
        supply_condiment_count = len(self.supply_condiment_sort.keys())
        if self.supply_condiment_sort.get(supply_condiment.name):
            return self.supply_condiment_sort.get(supply_condiment.name)
        self.supply_condiment_sort.update({supply_condiment.name: supply_condiment_count})
        return self.supply_condiment_sort.get(supply_condiment.name)

    def calculate_attr_sort(self, attr, base_sort=None):
        attr_count = len(self.attr_sort.keys())
        key = self.get_attr_duplicate_key(attr)
        if self.attr_sort.get(key):
            return self.attr_sort.get(key)
        sort = attr_count
        if base_sort:
            sort += base_sort
        self.attr_sort.update({key: sort})
        return sort

    def get_attr_group(self, group_name, attrs, group_type=None):
        attr_manager = AttrManager(merchant=self.merchant)
        attr_group = self.attr_groups.get(group_name)
        if not attr_group:
            attr_group = attr_manager.get_or_create_attr_group(group_id=None, group_name=group_name, group_type=group_type)
        if group_type is not None:
            attr_group.attr_group_type = group_type
        exists_attrs = {self.get_attr_duplicate_key(attr): attr for attr in attr_group.attrs}
        base_sort = 0
        if len(attr_group.attrs) > 0:
            base_sort = attr_group.attrs[-1].sort
        for index, attr in enumerate(attrs):
            attr_name = attr.get("name")
            attr_reprice = attr.get("reprice")
            key = "{}:{}".format(attr.get("name"), attr.get("reprice"))
            exists_attr = exists_attrs.get(key)
            if not exists_attr:
                attr = attr_manager.add_attr_to_group(
                    group=attr_group, attr_name=attr_name, attr_reprice=attr_reprice, type=group_type
                )
                attr.sort = self.calculate_attr_sort(attr, base_sort=base_sort)
                exists_attrs.update({key: attr})
            else:
                exists_attr.sort = self.calculate_attr_sort(exists_attr, base_sort=base_sort)
        self.attr_groups.update({group_name: attr_group})
        attr_group.attrs.sort(key=lambda k: k.sort)
        return attr_group

    def get_supply_condiment(self, sc):
        supply_condiment = self.supply_condiments.get(sc.get("name"))
        if supply_condiment is None:
            supply_condiment_manager = SupplyCondimentManager(merchant=self.merchant)
            name = sc.get("name")
            market_price = sc.get("price")
            supply_condiment = supply_condiment_manager.add_or_update_supply_condiment(name=name, market_price=market_price)
            self.calculate_supply_condiment_sort(supply_condiment)
            self.supply_condiments.update({name: supply_condiment})
        return supply_condiment

    def get_category(self, category_name):
        category = self.categories.get(category_name)
        if category is None:
            dish_category_manager = DishCategoryManager(merchant=self.merchant)
            category = dish_category_manager.get_or_create_category(category_id=None, name=category_name)
            self.categories.update({category_name: category})
        return category

    def export_dish_xlsx(self):
        ordering_da = OrderingServiceDataAccessHelper()
        dishes = ordering_da.get_all_dishes(merchant_id=self.merchant.id, nocache=True)
        dishes.sort(key=lambda d: d.sort)
        merchant_manager = MerchantManager(merchant=self.merchant)
        name = merchant_manager.get_merchant_name()
        name = "{}.xlsx".format(name)
        filepath = os.path.join(self.merchant_dish_xlsx_path, name)
        # if os.path.exists(filepath):
        #     return
        categories = ordering_da.get_categories(merchant_id=self.merchant.id)
        categories = {c.id: c for c in categories}
        wb = Workbook()
        ws = wb.active
        ws.title = "sheet1"
        ws.append(["商品名称", "菜品分类", "标准单位", "规格类别", "规格", "价格", "做法类别", "做法", "加料", "打包盒类别", "打包盒"])
        for dish in dishes:
            if dish.status == dish_pb.Dish.OFFLINE or dish.status == dish_pb.Dish.DISABLE:
                continue
            data = []
            if len(dish.categories) == 0:
                continue
            category_id = dish.categories[0]
            category = categories.get(category_id)
            if not category:
                continue
            data.append(dish.name)
            data.append(category.name)
            data.append(dish.unit)
            attr_infos = self.export_dish_attrs(dish)
            data.append(attr_infos.specification_groups)
            data.append(attr_infos.specification_attrs)
            data.append(self.format_price(dish.price - attr_infos.minimal_specification_price, dish=dish))
            data.append(attr_infos.common_groups)
            data.append(attr_infos.common_attrs)
            data.append(self.export_dish_supply_condiments(dish))
            data.append(attr_infos.package_groups)
            data.append(attr_infos.package_boxs)
            ws.append(data)
        wb.save(filepath)
        return f"{self.xlsx_url_prefix}/{name}"

    def export_dish_supply_condiments(self, dish):
        supply_condiments = dish.supply_condiments
        scs = []
        for sc in supply_condiments:
            if sc.status == dish_pb.SupplyCondiment.Status.OUT_OF_STOCK:
                continue
            price = self.format_price(sc.market_price)
            scs.append("{}:{}".format(sc.name, price))
        return "/".join(scs)

    def export_dish_attrs(self, dish):
        DishAttrs = namedtuple(
            "DishAttrs",
            [
                "specification_groups",
                "common_groups",
                "specification_attrs",
                "common_attrs",
                "minimal_specification_price",
                "package_groups",
                "package_boxs",
            ],
        )
        attrs = dish.attrs
        specification_groups = []
        specification_attrs = OrderedDict()
        common_groups = []
        common_attrs = OrderedDict()
        package_groups = []
        package_boxs = OrderedDict()
        minimal_specification_price = self.very_max_price
        for attr in attrs:
            if attr.status == dish_pb.Attr.Status.DELETED:
                continue
            if attr.type == dish_pb.Attr.SPECIFICATION:
                g_attrs = specification_attrs.get(attr.group_id, [])
                reprice = self.format_price(attr.reprice)
                len_specification_attrs = len(specification_attrs)
                specification_attrs.update({attr.group_id: g_attrs})
                if len_specification_attrs == 0 or (len_specification_attrs == 1 and attr.group_id in specification_attrs):
                    reprice = self.format_price(attr.reprice + dish.price)
                    minimal_specification_price = min(minimal_specification_price, attr.reprice)
                if reprice != "":
                    g_attrs.append("{}:{}".format(attr.name, reprice))
                else:
                    g_attrs.append(attr.name)
                if attr.group_name not in specification_groups:
                    specification_groups.append(attr.group_name)
            elif attr.type in [dish_pb.Attr.TASTE, dish_pb.Attr.UNKNOWN, dish_pb.Attr.ORIDINARY]:
                g_attrs = common_attrs.get(attr.group_id, [])
                reprice = self.format_price(attr.reprice)
                if reprice != "":
                    g_attrs.append("{}:{}".format(attr.name, reprice))
                else:
                    g_attrs.append(attr.name)
                common_attrs.update({attr.group_id: g_attrs})
                if attr.group_name not in common_groups:
                    common_groups.append(attr.group_name)
            elif attr.type == dish_pb.Attr.TAKE_AWAY:
                g_attrs = package_boxs.get(attr.group_id, [])
                reprice = self.format_price(attr.reprice)
                if reprice != "":
                    g_attrs.append("{}:{}".format(attr.name, reprice))
                else:
                    g_attrs.append(attr.name)
                package_boxs.update({attr.group_id: g_attrs})
                if attr.group_name not in package_groups:
                    package_groups.append(attr.group_name)
        specification_groups = ",".join(specification_groups)
        common_groups = ",".join(common_groups)
        package_groups = ",".join(package_groups)

        _common_attrs = []
        for cgs in common_attrs.values():
            cgs = "/".join(cgs)
            _common_attrs.append(cgs)
        common_attrs = ",".join(_common_attrs)

        _specification_attrs = []
        for sgs in specification_attrs.values():
            sgs = "/".join(sgs)
            _specification_attrs.append(sgs)
        specification_attrs = ",".join(_specification_attrs)

        _package_attrs = []
        for sgs in package_boxs.values():
            sgs = "/".join(sgs)
            _package_attrs.append(sgs)
        package_boxs = ",".join(_package_attrs)

        if minimal_specification_price == self.very_max_price:
            minimal_specification_price = 0
        return DishAttrs(
            specification_groups,
            common_groups,
            specification_attrs,
            common_attrs,
            minimal_specification_price,
            package_groups,
            package_boxs,
        )

    def format_price(self, price, dish=None):
        """把以分为单位的价格转成以元为单位的价格"""
        if price == 0:
            return "0"
        price = "{:.2f}".format(price / float(100))
        p = price.split(".")
        integer = p[0]
        decimal = p[1]
        decimal = int(decimal)
        if decimal == 0:
            return str(integer)
        return price

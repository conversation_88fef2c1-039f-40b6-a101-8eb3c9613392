# -*- coding: utf-8 -*-

import logging
import time
from datetime import timedelta

import proto.coupons_pb2 as coupons_pb
import proto.page.coupon_list_pb2 as coupon_list_pb
import proto.coupon_category_pb2 as coupon_category_pb
from business_ops import coupon_category_helper
from common.utils import date_utils
from common.utils import id_manager
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.coupon_da_helper import CouponDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.brand_dish_verification_code_da_helper import BrandDishVerificationCodeDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import base_responses
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class CouponManager():
    def __init__(self):
        pass

    def update_coupon_state(self, coupon_id, state):
        """根据ID更新指定优惠券State信息。

        Args:
            coupon_id: (string) 优惠券ID
            state: (CouponState) 需更新的Coupon状态
        """
        coupon_da = CouponDataAccessHelper()
        coupon = coupon_da.get_coupon_by_id(coupon_id)
        if not coupon:
            return

        coupon.state = state
        if state == coupons_pb.Coupon.ISSUED:
            coupon.issue_time = date_utils.timestamp_second()
        elif state == coupons_pb.Coupon.ACCEPTED:
            coupon.accept_time = date_utils.timestamp_second()
        elif state == coupons_pb.Coupon.USED:
            coupon.use_time = date_utils.timestamp_second()

        coupon_da.update_or_create_coupon(coupon)

    def update_coupon_if_expired(self, coupon_id):
        """如果优惠券过期，则更新其状态

        Args:
            coupon_id: (string) 优惠券ID
        """
        coupon_da = CouponDataAccessHelper()
        coupon = coupon_da.get_coupon_by_id(coupon_id)
        if coupon and coupon.state != coupons_pb.Coupon.EXPIRED and self.is_coupon_expired(coupon):
            coupon.state = coupons_pb.Coupon.EXPIRED
            coupon_da.update_or_create_coupon(coupon)
            return True
        return False

    def is_coupon_in_usable_time_ranges(self, coupon):
        """ 检查当前时间是否在优惠券的不可用时间段内
            如果没有设置任何一个时间段做为特殊时间段,那么就表示每天都可用
            如果设置了时间段,则除了这个时间段之外的其它时间都不可用
        """
        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            return False
        usable_time_ranges = coupon_category.usable_time_ranges
        if len(usable_time_ranges) <= 0:
            # 如果没有设置,则随时有用
            return True
        now = date_utils.datetime_now_in_timezone()
        weekday = now.weekday()
        now_second = now.hour * date_utils.ONE_HOUR + now.minute * date_utils.ONE_MINUTE + now.second

        for time_range in usable_time_ranges:
            wday = time_range.day_of_week
            if weekday == wday:
                if time_range.start_second_of_day <= now_second <= time_range.end_second_of_day:
                    # 当前时间如果在这个时间段内,则不可使用
                    return True
        # 如果usable_time_ranges不为空,并且当前时间不在时间段内,那么返回False
        return False

    def is_coupon_started(self, coupon):
        if coupon.state != coupons_pb.Coupon.ACCEPTED:
            return False

        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            return False

        base_info = coupon_category_helper.get_coupon_category_base_info(coupon_category)
        date_info = base_info.date_info
        if date_info.type == 'DATE_TYPE_FIX_TERM':
            accept_date = date_utils.get_datetime_in_timezone(coupon.accept_time, date_utils.TIMEZONE_SHANGHAI)
            start_date = accept_date + timedelta(days=date_info.fixed_begin_term)
            # 优惠券计算起始有效期时，采用round-down方式，即第一天0点开始即认为优惠券有效。
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            now = date_utils.datetime_now_in_timezone(date_utils.TIMEZONE_SHANGHAI)
            return now > start_date
        elif date_info.type == 'DATE_TYPE_FIX_TIME_RANGE':
            start_time = date_utils.get_datetime_in_timezone(date_info.begin_timestamp, date_utils.TIMEZONE_SHANGHAI)
            now = date_utils.datetime_now_in_timezone(date_utils.TIMEZONE_SHANGHAI)
            return now > start_time
        elif date_info.type == 'DATE_TYPE_PERMANENT':
            return True

        return False

    def is_coupon_expired(self, coupon):
        if coupon.state == coupons_pb.Coupon.ISSUED or coupon.state == coupons_pb.Coupon.USED:
            return False

        if coupon.state == coupons_pb.Coupon.EXPIRED:
            return True

        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon.coupon_category_id)
        if not coupon_category:
            return True

        base_info = coupon_category_helper.get_coupon_category_base_info(coupon_category)
        date_info = base_info.date_info
        if date_info.type == 'DATE_TYPE_FIX_TERM':
            accept_date = date_utils.get_datetime_in_timezone(coupon.accept_time, date_utils.TIMEZONE_SHANGHAI)
            start_date = accept_date + timedelta(days=date_info.fixed_begin_term)
            end_date = start_date + timedelta(days=date_info.fixed_term)
            # TODO: 微信对于优惠券过期时间的计算方式为严格界定（领取时间+优惠券的指定有效时间）为失效时间，
            #       而不采用round-up到当天结束的方式。但这仍然是个值得讨论的问题，之后若解绑微信会员卡系统，
            #       可重新评估此问题。
            #
            # 优惠券计算终止有效期时，采用round-up方式，即到最后一天即将结束的午夜凌晨优惠券才失效，
            # 但为了留出余量，这里判断失效时间为晚上11点55分。
            end_date = end_date.replace(hour=23, minute=55, second=0, microsecond=0)

            now = date_utils.datetime_now_in_timezone(date_utils.TIMEZONE_SHANGHAI)
            return now > end_date
        elif date_info.type == 'DATE_TYPE_FIX_TIME_RANGE':
            end_time = date_utils.get_datetime_in_timezone(date_info.end_timestamp, date_utils.TIMEZONE_SHANGHAI)
            now = date_utils.datetime_now_in_timezone(date_utils.TIMEZONE_SHANGHAI)
            return now > end_time
        elif date_info.type == 'DATE_TYPE_PERMANENT':
            return False

        return True

    def is_coupon_in_valid_period(self, coupon):
        """判断给定优惠券是否在有效使用期间。

        Args:
            coupon: (Coupon) 优惠券结构体

        Returns:
            (bool) 若优惠券在有效期间则返回True, 否则返回False
        """

        # 如果该优惠券尚未被领取、已核销、或者已过期 (即非已领取状态)，均应返回不可用
        if coupon.state != coupons_pb.Coupon.ACCEPTED:
            return False

        return self.is_coupon_started(coupon) \
            and not self.is_coupon_expired(coupon) \
            and self.is_coupon_in_usable_time_ranges(coupon)

    def can_consume_coupon(self, coupon_id):
        """检查优惠券能否被核销

        核销步骤:
          1. 如果优惠券已同步到微信，则调用微信接口检查优惠券的核销状态
          2. 判断核销时间是否在有效期内

        Args:
            coupon_id: (string) 优惠券ID
        """
        coupon = CouponDataAccessHelper().get_coupon_by_id(coupon_id)
        if not coupon:
            return False

        return self.is_coupon_in_valid_period(coupon)

    def consume_coupon(self, coupon_id):
        """核销优惠券

        步骤:
          1. 如果优惠券已同步到微信，则调用微信接口核销，核销成功则更新状态
          2. 如果优惠券仅供时来平台使用，则直接更新状态

        Args:
            coupon_id: (string) 优惠券ID
        """
        # TODO: 写入日志
        if self.can_consume_coupon(coupon_id):
            self.update_coupon_state(coupon_id, coupons_pb.Coupon.USED)

    def issue_coupons_to_user(self, merchant_id, user_id, issue_scene=None):
        """投放商户的优惠券给指定用户

        Args:
            merchant_id: (string) 商户ID
            user_id: (string) 用户ID
            issue_scene: (coupon_category_pb.CouponCategory.IssueScene) 投放场景

        Return:
            (List of coupons_pb.Coupon) 被投放的优惠券列表
        """
        coupon_da = CouponDataAccessHelper()
        coupon_categories = CouponCategoryDataAccessHelper().get_coupon_categories(merchant_id=merchant_id,
                                                                                   issue_scene=issue_scene)
        coupons = []
        if coupon_categories:
            for coupon_category in coupon_categories:
                coupon_category_id = coupon_category.id
                if not coupon_da.get_user_coupon_by_category_id(user_id, coupon_category.id):
                    coupons.append(self.issue_coupon_to_user(coupon_category_id, user_id))
        return coupons

    def issue_coupon_to_user(self, coupon_category_id, user_id, state=None, id=None):
        """给指定用户投放指定优惠券。

        Args:
            coupon_category_id: (string) 用于投放的优惠券ID
            user_id: (string) 指定用户ID

        Returns:
            如成功投放该优惠券给指定用户则返回新创建的Coupon，否则返回None
        """
        user = UserDataAccessHelper().get_user(user_id)
        if not user:
            return None

        coupon_category = CouponCategoryDataAccessHelper().get_coupon_category(coupon_category_id)
        if not coupon_category:
            return None
        coupons = CouponDataAccessHelper().get_coupon_list(user_id=user_id, coupon_category_id=coupon_category_id)
        get_limit = coupon_category.cash_coupon_spec.base_info.get_limit
        if get_limit > 0 and coupons and len(coupons) >= get_limit:
            return None
        if state is None:
            state = coupons_pb.Coupon.ISSUED

        coupon = coupons_pb.Coupon()
        if id is not None:
            coupon.id = id
        else:
            coupon.id = id_manager.generate_coupon_id()
        coupon.coupon_category_id = coupon_category.id
        coupon.user_id = user_id
        coupon.merchant_id = coupon_category.merchant_id
        coupon.state = state
        coupon.issue_time = date_utils.timestamp_second()
        if coupon.state == coupons_pb.Coupon.ACCEPTED:
            coupon.accept_time = date_utils.timestamp_second()
        if coupon.state == coupons_pb.Coupon.USED:
            coupon.accept_time = date_utils.timestamp_second()
            coupon.use_time = date_utils.timestamp_second()
        CouponDataAccessHelper().add_coupon(coupon)

        return coupon

    def accept_coupon(self, coupon_id, user_id, wechat_coupon_id=None):
        """领取被投放的优惠券

        1. 兼容微信优惠券，如果是已同步到微信卡券，则解密优惠券Code
        2. 更新被投放的优惠券状态

        Args:
            coupon_id: (String) 被投放的优惠券ID
            user_id: (String) 用户ID
            wechat_coupon_id: (String|None) 微信官方返回的优惠券加密Code
        """
        coupon_da = CouponDataAccessHelper()
        coupon = coupon_da.get_coupon_by_id(coupon_id)
        if not coupon:
            raise errors.CouponNotFound()

        coupon.state = coupons_pb.Coupon.ACCEPTED
        coupon.accept_time = date_utils.timestamp_second()
        coupon_da.update_or_create_coupon(coupon)

    def create_new_member_coupon_categories(self, merchant_id):
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if not merchant:
            return

        for coupon_spec in merchant.preferences.coupon_config.new_member_coupons:
            self.create_coupon_category(merchant=merchant, coupon_spec=coupon_spec)

    def create_wechat_moments_ad_coupon_categories(self, merchant_id):
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if not merchant:
            return

        for coupon_spec in merchant.preferences.coupon_config.wechat_moments_ad_coupons:
            self.create_coupon_category(merchant=merchant, coupon_spec=coupon_spec)

    # TODO: 使用 coupon_category_manager.create 替代
    def create_coupon_category(self, merchant, coupon_spec, store_id=None):
        if not merchant:
            return

        # 目前仅支持生成代金券
        coupon_category = None
        if coupon_spec.HasField("cash_coupon_spec"):
            cash_coupon_spec = coupon_spec.cash_coupon_spec
            coupon_category = coupon_category_helper.create_cash_coupon_category(
                merchant=merchant, cash_coupon_spec=cash_coupon_spec)

        if coupon_category:
            if store_id:
                coupon_category.store_id_list.append(store_id)
            CouponCategoryDataAccessHelper().add_coupon_category(coupon_category)
            return base_responses.success_responses_obj()

        return base_responses.create_responses_obj(
            error_codes.COUPON_CATEGORY_NOT_SUPPORT, error_codes.COUPON_CATEGORY_NOT_SUPPORT_MSG)

    def deal_with_date_info(self, date_info, coupon_vo, coupon):
        if date_info.type == "DATE_TYPE_FIX_TERM":
            end_timestamp = coupon.accept_time + date_info.fixed_term * date_utils.ONE_DAY
            coupon_vo.date_info.begin_timestamp = coupon.accept_time
            coupon_vo.date_info.end_timestamp = end_timestamp
            coupon_vo.expired_time = end_timestamp
            coupon_vo.start_time = coupon.accept_time
        elif date_info.type == "DATE_TYPE_FIX_TIME_RANGE":
            end_timestamp = coupon.accept_time + date_info.fixed_term * date_utils.ONE_DAY
            coupon_vo.date_info.end_timestamp = coupon.accept_time
            coupon_vo.expired_time = end_timestamp
            coupon_vo.start_time = date_info.begin_timestamp
        coupon_vo.date_info.type = date_info.type
        coupon_vo.date_info.fixed_term = date_info.fixed_term

    def get_user_regular_coupons(self, category, user, merchant, coupon_list_vo):
        coupon_da = CouponDataAccessHelper()
        coupons = coupon_da.get_coupon_list(coupon_category_id=category.id, user_id=user.id)
        for coupon in coupons:
            if coupon.state not in [
                coupons_pb.Coupon.ACCEPTED,
                coupons_pb.Coupon.EXPIRED,
                coupons_pb.Coupon.USED
            ]:
                continue
            coupon_vo = coupon_list_pb.OrderCouponList.OrderCouponVO()
            self.init_coupon_vo_base(coupon, category, coupon_vo)
            self.deal_with_date_info(category.cash_coupon_spec.base_info.date_info, coupon_vo, coupon)
            coupon_list_vo.num += 1

            _coupon_vo = coupon_list_vo.coupons.add()
            _coupon_vo.CopyFrom(coupon_vo)

    def get_user_coupon_package_coupon(self, category, user, merchant, coupon_list_vo):
        coupon_da = CouponDataAccessHelper()
        coupons = coupon_da.get_coupon_list(coupon_category_id=category.id, user_id=user.id)
        for coupon in coupons:
            if coupon.state not in [
                coupons_pb.Coupon.ACCEPTED,
                coupons_pb.Coupon.EXPIRED,
                coupons_pb.Coupon.USED
            ]:
                continue
            coupon_vo = coupon_list_pb.OrderCouponList.OrderCouponVO()
            self.init_coupon_vo_base(coupon, category, coupon_vo)
            self.deal_with_date_info(category.cash_coupon_spec.base_info.date_info, coupon_vo, coupon)
            coupon_list_vo.num += 1

            _coupon_vo = coupon_list_vo.coupons.add()
            _coupon_vo.CopyFrom(coupon_vo)

    def get_user_verification_code_coupons(self, category, user, merchant, coupon_list_vo):
        pass

    def get_user_dish_verification_code_coupons(self, category, user, merchant, coupon_list_vo):
        pass

    def get_user_brand_dish_verification_code_coupons(self, category, user, merchant, coupon_list_vo):
        brand_dish_verification_code_da = BrandDishVerificationCodeDataAccessHelper()
        strategy = brand_dish_verification_code_da.get_brand_dish_verification_code_strategy(
            brand_id=merchant.brand_info.id, strategy_id=category.brand_dish_verification_code_coupon_spec.strategy_id)
        if not strategy:
            return
        coupon_da = CouponDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        coupons = coupon_da.get_coupon_list(coupon_category_id=category.id, user_id=user.id)
        for coupon in coupons:
            # if not (coupon.state == coupons_pb.Coupon.ACCEPTED):
            #     continue
            coupon_vo = coupon_list_pb.OrderCouponList.OrderCouponVO()
            self.init_coupon_vo_base(coupon, category, coupon_vo)
            date_info = category.brand_dish_verification_code_coupon_spec.base_info.date_info
            self.deal_with_date_info(date_info, coupon_vo, coupon)
            dish = ordering_da.get_dish(dish_brand_id=strategy.dish_brand_id, merchant_id=merchant.id)
            if not dish:
                continue
            coupon_vo.dish_name = dish.name
            coupon_vo.dish_id = dish.id
            coupon_vo.reduce_cost = int(dish.price)
            if len(dish.images) > 0:
                coupon_vo.dish_image_url = dish.images[0]
            coupon_list_vo.num += 1

            _coupon_vo = coupon_list_vo.coupons.add()
            _coupon_vo.CopyFrom(coupon_vo)

    def get_invite_share_coupon(self, category, user, merchant, coupon_list_vo):
        coupon_da = CouponDataAccessHelper()
        coupons = coupon_da.get_user_coupons(user_id=user.id, coupon_category_id=category.id)
        for coupon in coupons:
            if coupon.state not in [
                coupons_pb.Coupon.ACCEPTED,
                coupons_pb.Coupon.EXPIRED,
                coupons_pb.Coupon.USED
            ]:
                continue
            coupon_vo = coupon_list_pb.OrderCouponList.OrderCouponVO()
            self.init_coupon_vo_base(coupon, category, coupon_vo)
            coupon_vo.least_cost = category.invite_share_coupon_spec.least_cost
            coupon_vo.reduce_cost = category.invite_share_coupon_spec.reduce_cost
            date_info = category.invite_share_coupon_spec.base_info.date_info
            self.deal_with_date_info(date_info, coupon_vo, coupon)
            _coupon_vo = coupon_list_vo.coupons.add()
            _coupon_vo.CopyFrom(coupon_vo)

    def init_coupon_vo_base(self, coupon, category, coupon_vo):
        merchant_da = MerchantDataAccessHelper()
        merchant = merchant_da.get_merchant(category.merchant_id)
        coupon_vo.id = coupon.id
        coupon_vo.least_cost = category.cash_coupon_spec.least_cost
        coupon_vo.reduce_cost = category.cash_coupon_spec.reduce_cost
        coupon_vo.accept_time = coupon.accept_time
        coupon_vo.issue_scene = category.issue_scene
        coupon_vo.merchant_name = merchant.basic_info.display_name
        coupon_vo.logo_url = merchant.basic_info.logo_url
        # 检查券是否已过期,如果已过期,就把券的状态改为EXPIRED
        self.check_coupon_can_consume(coupon, category)
        coupon_vo.state = coupon.state

    def get_user_coupons(self, user, merchant):
        """ 返回用户在某家商户下的所有券
        1. 常规券
        2. 券包
        3. 核销券
        4. 菜品核销券
        5. 品牌菜品核销券
        """
        coupon_list_vo = coupon_list_pb.OrderCouponList()
        coupon_category_da = CouponCategoryDataAccessHelper()
        categories = coupon_category_da.get_coupon_categories(merchant.id)
        for category in categories:
            CouponCategory = coupon_category_pb.CouponCategory
            issue_scene = category.issue_scene
            if issue_scene == CouponCategory.REGULAR_COUPONS:
                self.get_user_regular_coupons(category, user, merchant, coupon_list_vo)
            elif issue_scene == CouponCategory.COUPON_PACKAGE:
                self.get_user_coupon_package_coupon(category, user, merchant, coupon_list_vo)
            elif issue_scene == CouponCategory.INVITE_SHARE:
                self.get_invite_share_coupon(category, user, merchant, coupon_list_vo)
        return coupon_list_vo

    def check_coupon_can_consume_by_coupon_id(self, coupon_id):
        coupon_da = CouponDataAccessHelper()
        coupon = coupon_da.get_coupon_by_id(coupon_id)
        return self.check_coupon_can_consume(coupon)

    def check_coupon_can_consume(self, coupon, coupon_category=None):
        if not coupon:
            return False
        if coupon.state != coupons_pb.Coupon.ACCEPTED:
            return False
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_category = coupon_category_da.get_coupon_category(id=coupon.coupon_category_id)
        if self.check_coupon_expired(coupon, coupon_category):
            return False
        return True

    def check_coupon_expired(self, coupon, coupon_category):
        coupon_spec = self.get_coupon_category_spec(coupon_category)
        date_info = coupon_spec.base_info.date_info
        flag = True
        if date_info.type == "DATE_TYPE_FIX_TIME_RANGE":
            flag = self.check_coupon_expired_date_type_fix_time_range(date_info)
        elif date_info.type == "DATE_TYPE_FIX_TERM":
            flag = self.check_coupon_expired_date_type_fix_term(coupon, date_info)
        elif date_info.type == "DATE_TYPE_PERMANENT":
            # 永久有效
            flag = False
        if flag:
            logger.info("券 {} 已过期,修改状态为 EXPIRED".format(coupon.id))
            # 如果券已过期,那么就把这个券的状态设置为已过期
            coupon_da = CouponDataAccessHelper()
            coupon.state = coupons_pb.Coupon.EXPIRED
            coupon_da.update_or_create_coupon(coupon)
        return flag

    def check_coupon_expired_date_type_fix_time_range(self, date_info):
        now = int(time.time())
        if date_info.begin_timestamp < now < date_info.end_timestamp:
            return False
        return True

    def check_coupon_expired_date_type_fix_term(self, coupon, date_info):
        """ 券是否已过期
        True: 已过期,或未开始
        False: 未过期
        """
        begin_timestamp = date_info.begin_timestamp
        now = int(time.time())
        if date_info.end_time:
            if now < date_info.end_time:
                return False
            return True
        if date_info.fixed_begin_term == 0:
            begin_timestamp = coupon.accept_time
        expire_timestamp = date_info.fixed_term * date_utils.ONE_DAY + begin_timestamp
        margin_seconds = date_utils.ONE_DAY - expire_timestamp % date_utils.ONE_DAY
        if begin_timestamp <= now <= (expire_timestamp + margin_seconds):
            return False
        return True

    def get_coupon_category_spec(self, coupon_category):
        """获取CouponCategory具体的配置信息
        """
        issue_scene = coupon_category.issue_scene
        if issue_scene == coupon_category_pb.CouponCategory.INVITE_SHARE:
            return coupon_category.invite_share_coupon_spec
        if issue_scene == coupon_category_pb.CouponCategory.COUPON_PACKAGE:
            return coupon_category.cash_coupon_spec
        if issue_scene == coupon_category_pb.CouponCategory.REGULAR_COUPONS:
            return coupon_category.cash_coupon_spec
        return None

    def get_coupon_category_by_coupon(self, coupon_id=None, coupon=None):
        if coupon_id is None and coupon is None:
            return None
        if coupon is None:
            coupon_da = CouponDataAccessHelper()
            coupon = coupon_da.get_coupon_by_id(coupon_id)
        if coupon is None:
            return None
        coupon_category_id = coupon.coupon_category_id
        coupon_category_da = CouponCategoryDataAccessHelper()
        coupon_category = coupon_category_da.get_coupon_category(id=coupon_category_id)
        if not coupon_category:
            return None
        return coupon_category

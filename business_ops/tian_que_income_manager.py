# -*- coding: utf-8 -*-

"""
Filename: tia<PERSON><PERSON><PERSON>_pay_manager.py
Date: 2020-07-17 10:59:36
Title: 天阙聚合支付
文档: http://doc.tianquetech.com/web/#/3?page_id=193
"""

import base64
import logging
import socket
import requests
import time
import os
from base64 import b64decode
from collections import namedtuple
from datetime import datetime
from urllib.parse import urlparse

import pytz
from Crypto.Hash import SHA
from Crypto.Signature import PKCS1_v1_5
from Crypto.PublicKey import RSA

from business_ops import constants
from business_ops.merchant_manager import MerchantManager
from common.utils import date_utils
from common.utils import id_manager
from dao.payment_da_helper import PaymentDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import errors

logger = logging.getLogger(__name__)


class TianQueIncomeManager:

    # 商户入驻
    TIAN_QUE_PAY_MERCHANT_INCOME = "/merchant/income"
    # 商户入驻信息修改
    TIAN_QUE_PAY_MERCHANT_UPDATE_MERCHANT_INFO = "/merchant/updateMerchantInfo"
    # 图片上传
    TIAN_QUE_PAY_MERCHANT_UPLOAD_PICTURE = "/merchant/uploadPicture"
    # 入驻结果查询
    TIAN_QUE_PAY_MERCHANT_QUERY_MERCHANT_INFO = "/merchant/queryMerchantInfo"

    ip = None

    def __init__(self, *args, **kargs):
        self.pubkey_string = open("/data/tian-que-keys/tian-que-public-key-test.pem", "r").read()
        self.prikey_string = open(constants.TIAN_QUE_PAY_SHILAI_PRIVACE_KEY_FILE, "r").read()
        self.tian_que_pay_orgid = "97580894"
        self.tian_que_pay_domain = "https://openapi-test.tianquetech.com"
        self.tian_que_pay_shilai_mno = constants.TIAN_QUE_PAY_SHILAI_MNO
        self.tian_que_pay_shilai_launch_ledger_mno = constants.TIAN_QUE_PAY_SHILAI_LAUNCH_LEDGER_MNO
        self.tian_que_pay_shilai_commission_rate = constants.TIAN_QUE_PAY_SHILAI_COMMISSION_RATE

        self.pay_type = kargs.get("pay_type", "WECHAT")
        self.pay_way = kargs.get("pay_way", "03")

        merchant = kargs.get("merchant")
        merchant_id = kargs.get("merchant_id")

        self.merchant = merchant
        if merchant_id and not self.merchant:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)
        merchant_id = kargs.get("merchant_id")
        if self.merchant is None and merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)

        self.merchant_manager = MerchantManager(merchant=self.merchant)
        if self.merchant and len(self.merchant.stores) > 0:
            self.store = self.merchant.stores[0]

        if self.ip is None:
            domain = os.environ.get("SERVICE_DOMAIN", "shilai.zhiyi.cn")
            self.ip = self._parse_domain(domain)
        self.send_file = None
        if int(self.merchant.create_timestamp) < 1614009600:
            raise errors.ShowError("此商户创建时间早于2021-02-23,不支持在小程序上面进行天阙支付入驻")

    @staticmethod
    def _parse_domain(url):
        if not urlparse(url).scheme:
            url = f"http://{url}"
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        domain = domain.replace("www.", "")
        ip = socket.gethostbyname(domain)
        return ip

    def rsa_sign(self, plaintext):
        private_key = RSA.importKey(self.prikey_string)
        cipher = PKCS1_v1_5.new(private_key)
        h = SHA.new(plaintext)
        signature = cipher.sign(h)
        sign = base64.b64encode(signature)
        sign = sign.decode('utf-8')
        return sign

    def rsa_verify(self, sign, plaintext):
        """校验RSA 数字签名"""
        digest = SHA.new()
        digest.update(plaintext)
        verifier = PKCS1_v1_5.new(RSA.importKey(self.pubkey_string))
        return verifier.verify(digest, b64decode(sign))

    def check_op_success(self, ret):
        if not ret.flag:
            return False
        logger.info("check op success: {}".format(ret))
        if ret.ret.get("code") == "0000" and ret.ret.get("respData", {}).get("bizCode") == "0000":
            return True
        return False

    def sign(self, params):
        keys = params.keys()
        keys = sorted(keys, key=lambda x: x)
        s = []
        for key in keys:
            if params.get(key, "") == "":
                continue
            s.append("{}={}".format(key, params.get(key)))
        s = "&".join(s)
        s = s.replace("'", "\"")
        s = s.replace(" ", "")
        logger.info("待签名字符串: {}".format(s))
        s = s.encode("utf-8")
        sign = self.rsa_sign(s)
        logger.info("生成签名: {}".format(sign))
        return sign

    def build_params(self, reqdata):
        now = int(time.time())
        shanghai = pytz.timezone(date_utils.TIMEZONE_SHANGHAI)
        now = datetime.fromtimestamp(now).astimezone(shanghai).strftime("%Y%m%d%H%M%S")
        params = {
            "orgId": self.tian_que_pay_orgid,
            "reqId": id_manager.generate_common_id(),
            "version": "1.0",
            "timestamp": str(now),
            "reqData": reqdata,
            "signType": "RSA"
        }
        sign = self.sign(params)
        params.update({"sign": sign})
        return params

    def try_post(self, url, params, timeout=3, try_times=0):
        Ret = namedtuple("Ret", ["flag", "ret"])
        if try_times == -1:
            logger.info("try post url: {} fail".format(url))
            return Ret(flag=False, ret={})
        try:
            logger.info("api request params: {}".format(params))
            if self.send_file is not None:
                ret = requests.post(url, data=params, files=self.send_file, timeout=timeout)
            else:
                ret = requests.post(url, json=params, timeout=timeout)
            logger.info("api request return text: {}".format(ret.text))
            ret = ret.json()
            if ret.get("code") == "0001" and ret.get("msg") == "原交易订单已做过分账，不允许重复分账":
                return Ret(flag=False, ret=ret)
            if not (ret.get("code") == "0000" and ret.get("respData", {}).get("bizCode") == "0000"):
                time.sleep(0.2)
                return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times - 1)
            return Ret(flag=True, ret=ret)
        except Exception as ex:
            logger.info("try_post: {}".format(ex))
            logger.info("0.2秒后　{} 重试".format(url))
            time.sleep(0.2)
            return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times - 1)
        return Ret(flag=False, ret={})

    def upload_picture(self, type, f):
        params = {
            "pictureType": type,
            "orgId": self.tian_que_pay_orgid,
            "reqId": id_manager.generate_common_id()
        }
        # params = self.build_params(params)
        suffix = os.path.splitext(f.filename)[-1]
        self.send_file = {"file": ("file{}".format(suffix), f.stream)}
        url = "{}{}".format(
            self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_UPLOAD_PICTURE)
        ret = self.try_post(url, params)
        if not self.check_op_success(ret):
            logger.info("天阙上传图片失败")
            raise errors.ShowError("天阙上传图片失败")
        logger.info("天阙上传图片成功: {}".format(ret))
        return ret.ret.get("respData").get("PhotoUrl")

    def generate_income_params(self, update=False, **kargs):
        params = {}
        # 法人/商户负责人
        identity_name = self.merchant.basic_info.contact_name
        if self.is_param_invalid(identity_name):
            raise errors.ShowError("法人/商户负责人必传")
        # 商户简称
        mec_dis_nm = self.merchant.basic_info.name
        if self.is_param_invalid(mec_dis_nm):
            raise errors.ShowError("商户简称必传")
        # 商户联系手机号
        mbl_no = self.merchant.basic_info.contact_phone
        if self.is_param_invalid(mbl_no):
            raise errors.ShowError("商户联系手机号必传")
        # 经营类型
        # 01: 线下
        # 02: 线上
        # 03: 非盈利类
        # 04: 缴费类
        # 05: 保险类
        # 06: 私立院校
        operational_type = kargs.get("operational_type")
        if not update and self.is_param_invalid(operational_type):
            raise errors.ShowError("经营类型必传")
        # 01: 自然人
        # 02: 个体户
        # 03: 企业
        have_license_no = kargs.get("have_license_no")
        if not update and self.is_param_invalid(have_license_no):
            raise errors.ShowError("资质类型必传")
        if not update and operational_type == "02":
            if have_license_no == "03":
                raise errors.ShowError("线上商户必须是企业类型")

        # 商户类型
        # 00: 普通单店商户
        # 01: 连锁总
        # 02: 连锁分
        # 03: 1+n总
        # 04: 1+n分
        mec_type_flag = kargs.get("mec_type_flag")
        if not update and self.is_param_invalid(mec_type_flag):
            raise errors.ShowError("商户类型必传")
        # 所属总店商户编号
        # 商户类型为02,04时必传
        parent_mno = kargs.get("parent_mno")
        if not update and mec_type_flag in ["02", "04"] and self.is_param_invalid(parent_mno):
            errmsg = "商户类型为 连锁分 1+n分时 必传 所属总店商户编号"
            raise errors.ShowError(errmsg)
        if not update and mec_type_flag in ["02", "04"]:
            params.update({"parentMno": parent_mno})
        # 分店是否独立结算
        # 00: 是
        # 01: 否
        independent_model = kargs.get("independent_model")
        if not update and mec_type_flag == "02" and self.is_param_invalid(independent_model):
            raise errors.ShowError("商户类型为连锁分店时,分店是否独立结算必传")
        if not update and mec_type_flag == "02":
            params.update({"independentModel": independent_model})

        # 二维码费率,json格式,单位为%,修改时必须同时传
        # "qrcodeList": [{"rate":"0.28","rateType":"01"}]
        # rateType取值如下:
        # 01: 微信
        # 02: 支付宝
        # 06: 银联单笔小于等于 1000
        # 07: 银联单笔大于 1000
        # qrcode_list = kargs.get("qrcode_list")
        qrcode_list = [
            {
                "rateType": "01",
                "rate": "2.5"
            },
            {
                "rateType": "02",
                "rate": "2.5"
            },
            {
                "rateType": "06",
                "rate": "2.5"
            },
            {
                "rateType": "07",
                "rate": "2.5"
            }
        ]
        # 刷卡费率,json格式传入,单位为%
        # "bankCardRates":[{"rate":"0.60","type":"21"}]
        # 21: 贷记卡
        # 22: 借记卡
        # 23: 借记卡手续费封顶值
        bank_card_rates = kargs.get("bank_card_rates")
        if not update and (not self.is_param_invalid(bank_card_rates)):
            params.update({"bankCardRates": bank_card_rates})
        # 结算类型
        # 03: T1
        # 04: D1
        settle_type = "04"
        # 支持的支付渠道,不传默认全部开通
        # 01: 支付宝
        # 02: 微信
        # 03: 银联二维码
        # support_pay_channels = ["01", "02", "03"]
        # 支持的交易类型,不传默认全部开通
        # 01: 主扫
        # 02: 被扫
        # 03: 公众号/小程序/服务窗/银联JS
        # 04: 退货
        # support_trade_types = ["01", "02", "03", "04"]
        # 线上产品类型,线上类型商户必传
        # 01: APP
        # 02: 网站
        # 03: 公众号
        online_type = kargs.get("online_type")
        if not update and operational_type == "02" and self.is_param_invalid(online_type):
            raise errors.ShowError("线上产品类型必须指明商户类型")
        if not update and operational_type == "02":
            params.update({"onlineType": online_type})

        # 线上产品名称,线上类型商户必传
        online_name = kargs.get("online_name")
        if not update and operational_type == "02" and self.is_param_invalid(online_name):
            raise errors.ShowError("线上产品类型必须指明商户名称")
        if not update and operational_type == "02":
            params.update({"onlineName": online_name})

        # APP下载地址及账号信息
        online_type_info = kargs.get("online_type_info")
        if not update and online_type_info is not None:
            params.update({"onlineTypeInfo": online_type_info})
        # 营业执照注册名称: 企业,个体户必传
        # 如果个体户营业执照中没有注册名称或注册名称是*，此处需要按照“个体户+法人姓名”的格式传入，例如：个体户张三
        cpr_reg_nm_cn = kargs.get("cpr_reg_nm_cn")
        if not update and have_license_no in ["02", "03"]:
            if have_license_no == "02" and cpr_reg_nm_cn == "" or cpr_reg_nm_cn == "*":
                cpr_reg_nm_cn = "个体户{}".format(identity_name)
            else:
                if self.is_param_invalid(cpr_reg_nm_cn):
                    raise errors.ShowError("企业,个体户必传营业执照注册名称")
        if not update and have_license_no in ["02", "03"]:
            params.update({"cprRegNmCn": cpr_reg_nm_cn})

        # 营业执照注册号: 企业,个体户必传
        regist_code = kargs.get("regist_code")
        if not update and have_license_no in ["02", "03"] and self.is_param_invalid(regist_code):
            raise errors.ShowError("个业,个体户必传营业执照注册号")
        if not update and have_license_no in ["02", "03"]:
            params.update({"registCode": regist_code})

        # 注册地址省编码
        prov_cd = kargs.get("prov_cd")
        # 注册地址市编码
        city_cd = kargs.get("city_cd")
        # 注册地址区编码
        dist_cd = kargs.get("dist_cd")
        # 注册地址街道地址
        detail_address = kargs.get("detail_address")

        # 是否三证合一,企业必传
        # 00: 是
        # 01: 否
        # license_match = kargs.get("license_match")
        license_match = "00"
        if not update and have_license_no == "03":
            params.update({"licenseMatch": license_match})

        # 组织机构代码
        org_code = kargs.get("org_code")
        # 税务登记号
        tax_reg_no = kargs.get("tax_reg_no")
        # 营业执照起始日
        business_lic_stt = kargs.get("business_lic_stt")
        # 商户经营详细地址,直接填写详细街道门牌号即可
        cpr_reg_addr = self.store.address
        if self.is_param_invalid(cpr_reg_addr):
            raise errors.ShowError("商户经营详细地址必传")
        # 商户经营地址省编码
        reg_prov_cd = kargs.get("reg_prov_cd")
        if self.is_param_invalid(reg_prov_cd):
            raise errors.ShowError("商户经营地址省编码必传")
        # 商户经营地址市编码
        reg_city_cd = kargs.get("reg_city_cd")
        if self.is_param_invalid(reg_city_cd):
            raise errors.ShowError("商户经营地址市编码必传")
        # 商户经营地址区编码
        reg_dist_cd = kargs.get("reg_dist_cd")
        if self.is_param_invalid(reg_dist_cd):
            raise errors.ShowError("商户经营地址区编码必传")
        # 经营类目
        # 参见随行付MCC表附件: http://doc.tianquetech.com/web/#/3?page_id=311
        mcc_cd = kargs.get("mcc_cd")
        if not update and self.is_param_invalid(mcc_cd):
            raise errors.ShowError("经营类目必传")
        # 客服电话,数字,不能有-
        cs_tel_no = kargs.get("cs_tel_no")
        if cs_tel_no is None or cs_tel_no == "":
            cs_tel_no = mbl_no
        # 法人/商户负责人证件类型
        # 00: 身份证  03: 军人证  04: 警察证  05: 港澳居民往来内地通行证
        # 06: 台湾居民往　来大陆通行证  07: 护照  98: 单位证件  99: 其它
        identity_typ = kargs.get("identity_typ")
        if self.is_param_invalid(identity_typ):
            raise errors.ShowError("法人/商户负责人证件类型必传")
        # 法人/商户负责人证件号
        identity_no = kargs.get("identity_no")
        if self.is_param_invalid(identity_no):
            raise errors.ShowError("法人/商户负责人证件号必传")
        # 法人/商户负责人证件起始日
        legal_person_lic_stt = kargs.get("legal_person_lic_stt")
        # 法人/商户负责人证件到期日
        legal_person_lic_ent = kargs.get("legal_person_lic_ent")

        # 结算人证件号起始日
        account_lic_stt = kargs.get("account_lic_stt")
        # 结算人证件号到期日
        account_lic_ent = kargs.get("account_lic_ent")
        # 结算卡号
        act_no = kargs.get("act_no")
        # 开户银行
        depo_bank = kargs.get("depo_bank")
        if not self.is_param_invalid(depo_bank):
            params.update({"depoBank": depo_bank})
        # 开户省份
        depo_prov_cd = kargs.get("depo_prov_cd")
        if not self.is_param_invalid(depo_prov_cd):
            params.update({"depoProvCd": depo_prov_cd})
        # 开户城市
        depo_city_cd = kargs.get("depo_city_cd")
        if not self.is_param_invalid(depo_city_cd):
            params.update({"depoCityCd": depo_city_cd})
        # 回调地址
        callback_url = kargs.get("callback_url")
        email = kargs.get("email")

        # 结算账户类型
        # 自然人商户只允许对私结算
        # 线上商户只允许对公结算
        # 00: 对公
        # 01: 对私
        act_typ = kargs.get("act_typ")
        if self.is_param_invalid(act_typ):
            raise errors.ShowError("结算账户类型必传")
        if have_license_no == "01" and act_typ == "00":
            raise errors.ShowError("自然人只允许对私结算")
        if operational_type == "02" and act_typ == "01":
            raise errors.ShowError("线上商户只允许对公结算")
        # 结算人证件号,对私必传
        stm_man_id_no = kargs.get("stm_man_id_no")
        if not update and act_typ == "01" and self.is_param_invalid(stm_man_id_no):
            raise errors.ShowError("对私结算必传结算人证件号")
        if not update and act_typ == "01":
            params.update({"stmManIdNo": stm_man_id_no})
        is_auth_settle = self.is_auth_settle(stm_man_id_no, identity_no)
        if (have_license_no == "01" or operational_type == "02") and is_auth_settle:
            raise errors.ShowError("自然人商户,线上类商户不允许授权结算")
        # 开户支行联行行号
        # 对公结算必传
        # 对私结算,如果发卡行是,村镇银行,城市商业银行,农村商业银行或其它银行则必须传
        lbnk_no = kargs.get("lbnk_no")
        if act_typ == "00" and self.is_param_invalid(lbnk_no):
            raise errors.ShowError("对公结算必传开户支行联行行号")
        if not self.is_param_invalid(lbnk_no):
            params.update({"lbnkNo": lbnk_no})

        # 开户银行名称
        lbnk_nm = kargs.get("lbnk_nm")

        # 结算账户名
        # 1. 对公结算时账户名需与注册名一致
        # 2. 自然人商户,线上类商户不允许授权结算
        act_nm = kargs.get("act_nm")
        if self.is_param_invalid(act_nm):
            raise errors.ShowError("结算账户名必传")
        if act_typ == "00":
            if act_nm != cpr_reg_nm_cn:
                raise errors.ShowError("对公结算,账户名,注册名必须一致")

        # 营业执照照片: 企业,个体户必传
        license_pic = kargs.get("license_pic")
        if have_license_no in ["02", "03"] and self.is_param_invalid(license_pic):
            raise errors.ShowError("企业,个体户必传 营业执照照片")
        if have_license_no in ["02", "03"]:
            params.update({"licensePic": license_pic})

        # 税务登记证照片
        # 企业非三证合一必传
        tax_regist_license_pic = kargs.get("tax_regist_license_pic")
        if license_match == "01" and have_license_no == "03" and self.is_param_invalid(tax_regist_license_pic):
            raise errors.ShowError("税务登记证照片: 企业 非三证合一必传")
        if license_match == "01" and have_license_no == "03":
            params.update({"taxRegistLicensePic": tax_regist_license_pic})

        org_code_pic = kargs.get("org_code_pic")
        if license_match == "01" and have_license_no == "03" and self.is_param_invalid(org_code_pic):
            raise errors.ShowError("组织机构代码: 企业 非三证合一必传")
        if license_match == "01" and have_license_no == "03":
            params.update({"orgCodePic": org_code_pic})

        # 法人/商户 负责人身份证正面(人像面)
        legal_personid_positive_pic = kargs.get("legal_personid_positive_pic")
        if self.is_param_invalid(legal_personid_positive_pic):
            raise errors.ShowError("法人/商户 负责人身份证正面照(人像面)必传")

        # 法人/商户 负责人身份证反面(国徽面)
        legal_personid_opposite_pic = kargs.get("legal_personid_opposite_pic")
        if self.is_param_invalid(legal_personid_opposite_pic):
            raise errors.ShowError("法人/商户 负责人身份证反面照(国徽面)必传")

        # 开户许可证,对公结算必传
        opening_account_license_pic = kargs.get("opening_account_license_pic")
        if act_typ == "00" and self.is_param_invalid(opening_account_license_pic):
            raise errors.ShowError("开户许可证: 对公结算必传")
        if act_typ == "00":
            params.update({"openingAccountLicensePic": opening_account_license_pic})

        # 银行卡正面,对私结算必传
        bank_card_positive_pic = kargs.get("bank_card_positive_pic")
        if act_typ == "01" and self.is_param_invalid(bank_card_positive_pic):
            raise errors.ShowError("银行卡正面,对私结算必传")
        if act_typ == "01":
            params.update({"bankCardPositivePic": bank_card_positive_pic})
        # 银行卡反面
        bank_card_opposite_pic = kargs.get("bank_card_opposite_pic")
        # 结算人身份证反面(国徽面)
        # 1. 对私授权结算必传
        # 2. 连锁分店统一结算的情况可不传
        settle_person_idcard_opposite = kargs.get("settle_person_idcard_opposite")
        if is_auth_settle and act_typ == "01" and self.is_param_invalid(settle_person_idcard_opposite):
            raise errors.ShowError("对私授权结算时结算人身份证反面")
        if is_auth_settle and act_typ == "01":
            params.update({"settlePersonIdcardOpposite": settle_person_idcard_opposite})

        # 结算人身份证正面(人像面)
        # 1. 对私授权结算必传
        # 2. 连锁分店统一结算的情况可不传
        settle_person_idcard_positive = kargs.get("settle_person_idcard_positive")
        if is_auth_settle and act_typ == "01" and self.is_param_invalid(settle_person_idcard_positive):
            raise errors.ShowError("对私授权结算时结算人身份证正面")
        if is_auth_settle and act_typ == "01":
            params.update({"settlePersonIdcardPositive": settle_person_idcard_positive})

        # 商户协议照片
        merchant_argeement_pic = kargs.get("merchant_argeement_pic")
        # 门头照片
        store_pic = kargs.get("store_pic")
        if self.is_param_invalid(store_pic):
            raise errors.ShowError("门头照片必传")

        # 真实商户内景图片
        inside_scene_pic = kargs.get("inside_scene_pic")
        if self.is_param_invalid(inside_scene_pic):
            raise errors.ShowError("真实商户内景图片必传")

        # 经营场所-含收银台
        business_place_pic = kargs.get("business_place_pic")
        # ICP许可证或公众号主体信息截图
        # 线上网站类,公众号类商户必传
        icp_licence = kargs.get("icp_licence")
        if operational_type == "02" and online_type in ["02", "03"] and self.is_param_invalid(icp_licence):
            raise errors.ShowError("ICP许可证或公众号主体信息截图: 线上网站类,公众号类商户必传")
        if operational_type == "02" and online_type in ["02", "03"]:
            params.update({"icpLicense": icp_licence})

        # 手持身份证(人像面)照片
        handcard_pic = kargs.get("handlecard_pic")
        # 租赁协议三(签章面)
        lease_argeement_three_pic = kargs.get("lease_argeement_three_pic")
        # 租赁协议二(面积,有效期页)
        lease_agreement_two_pic = kargs.get("lease_agreement_two_pic")
        # 租赁协议一(封面)
        lease_argeement_one_pic = kargs.get("lease_argeement_one_pic")
        # 其它资料照片1
        other_material_picture_one = kargs.get("other_material_picture_one")
        other_material_picture_two = kargs.get("other_material_picture_two")
        other_material_picture_three = kargs.get("other_material_picture_three")
        other_material_picture_four = kargs.get("other_material_picture_four")
        other_material_picture_five = kargs.get("other_material_picture_five")
        # 代理人签名
        agent_person_signature = kargs.get("agent_person_signature")
        # 确认代理人签名
        confirm_person_signature = kargs.get("confirm_person_signature")
        # 非法人结算授权函,授权结算必传
        # 非法人对私结算,即结算账户类型为对私结算,且结算人身份证号与法人身份证号不一致时必传
        letter_of_auth_pic = kargs.get("letter_of_auth_pic")
        if is_auth_settle and self.is_param_invalid(letter_of_auth_pic):
            raise errors.ShowError("非法人结算授权函: 授权结算必传")
        if is_auth_settle:
            params.update({"letterOfAuthPic": letter_of_auth_pic})
        # 统一结算无营业执照说明,个人资质的连锁分店如果统一结算,需上传总店对该店情况说明,证明连锁关系
        union_settle_without_license = kargs.get("union_settle_without_license")
        # 社会团体法人证书
        society_group_leg_per_pic = kargs.get("society_group_leg_per_pic")
        # 基金会法人登记证书
        foundation_leg_per_reg_pic = kargs.get("foundation_leg_per_reg_pic")
        # 办学许可证
        school_license = kargs.get("school_license")
        # 医疗机构执业许可证
        medical_institution_license = kargs.get("medical_institution_license")
        # 经营保险业务许可证
        insurance_licese = kargs.get("insurance_licese")
        # 保险业务法人等级证书
        insurance_leg_per_grade_pic = kargs.get("insurance_leg_per_grade_pic")
        # 民办教育许可证
        private_education_license = kargs.get("private_education_license")
        # 收费证明文件
        charge_proof_pic = kargs.get("charge_proof_pic")
        # 民办非企事业单位登记证书
        private_non_enterprise_pic = kargs.get("private_non_enterprise_pic")
        # 收费样本文件
        fee_simples_pic = kargs.get("fee_simples_pic")
        # 卫生局批文
        health_bureau_approva_i_pic = kargs.get("health_bureau_approva_i_pic")

        params.update({
            "mecDisNm": mec_dis_nm,
            "mblNo": mbl_no,
            "cprRegAddr": cpr_reg_addr,
            "regProvCd": reg_prov_cd,
            "regCityCd": reg_city_cd,
            "regDistCd": reg_dist_cd,
            "csTelNo": cs_tel_no,
            "actNm": act_nm,
            "actTyp": act_typ,
            "actNo": act_no,
            "legalPersonidPositivePic": legal_personid_positive_pic,
            "legalPersonidOppositePic": legal_personid_opposite_pic,
            "storePic": store_pic,
            "mccCd": mcc_cd,
            "insideScenePic": inside_scene_pic,
            "identityName": identity_name,
            "identityTyp": identity_typ,
            "identityNo": identity_no

        })
        if not update:
            params.update({
                "operationalType": operational_type,
                "mecTypeFlag": mec_type_flag,
                "haveLicenseNo": have_license_no,
                "qrcodeList": qrcode_list,
                "settleType": settle_type
            })
        return params

    def income(self, **params):
        params = self.build_params(params)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_INCOME)
        ret = self.try_post(url, params)
        self.add_or_update_income_info(params)
        if not self.check_op_success(ret):
            logger.info("天阙商户入驻失败")
            self.add_or_update_income_result(ret.ret.get("respData"))
            raise errors.ShowError("天阙商户入驻失败")
        logger.info("天阙商户入驻成功: {}".format(ret))
        self.add_or_update_income_result(ret.ret.get("respData"))

    def update_income(self, **params):
        params = self.build_params(params)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_UPDATE_MERCHANT_INFO)
        ret = self.try_post(url, params)
        self.add_or_update_income_info(params)
        if not self.check_op_success(ret):
            logger.info("天阙商户入驻修改失败")
            self.add_or_update_income_result(ret.ret.get("respData"))
            raise errors.ShowError("天阙商户入驻修改失败")
        logger.info("天阙商户入驻修改成功: {}".format(ret))
        self.add_or_update_income_result(ret.ret.get("respData"))

    def add_or_update_income(self, **kargs):
        payment_da = PaymentDataAccessHelper()
        result = payment_da.get_tian_que_pay_income_result(merchant_id=self.merchant.id)
        if not result:
            params = self.generate_income_params(**kargs)
            return self.income(**params)
        else:
            params = self.generate_income_params(update=True, **kargs)
            params.update({"mno": result.get("mno")})
            return self.update_income(**params)

    def query_income_result(self):
        payment_da = PaymentDataAccessHelper()
        result = payment_da.get_tian_que_pay_income_result(merchant_id=self.merchant.id)
        if not result:
            return None
        application_id = result.get("applicationId")
        params = {"applicationId": application_id}
        params = self.build_params(params)
        url = "{}{}".format(self.tian_que_pay_domain, self.TIAN_QUE_PAY_MERCHANT_QUERY_MERCHANT_INFO)
        result = self.try_post(url, params)
        return result

    def query_income_info(self):
        payment_da = PaymentDataAccessHelper()
        info = payment_da.get_tian_que_pay_income_info(merchant_id=self.merchant.id)
        if info:
            req_data = info.get("reqData")
            keys = req_data.keys()
            for key in keys:
                if key.endswith("Pic"):
                    pic_url = payment_da.get_tian_que_pic_id_url(req_data.get(key))
                    if pic_url:
                        req_data.update({key: pic_url.get("url")})
            return req_data
        return None

    def add_or_update_income_info(self, params):
        params.update({"merchantId": self.merchant.id})
        payment_da = PaymentDataAccessHelper()
        payment_da.add_or_update_tian_que_pay_income_info(params)

    def add_or_update_income_result(self, result):
        result.update({"merchantId": self.merchant.id})
        payment_da = PaymentDataAccessHelper()
        payment_da.add_or_update_tian_que_pay_income_result(result)

    def is_param_invalid(self, value):
        """ 检查参数是否非法
        返回值:
        True: 非法
        False: 不非法
        """
        if value is None:
            return True
        if value == "":
            return True
        return False

    def is_auth_settle(self, settle_idcard, legal_person_idcard):
        """ 如果结算人身份证号与法人身份证号不一致,则为授权结算,否则为非授权结算
        """
        if settle_idcard != legal_person_idcard:
            return True
        return False

    def update_tian_que_pic_id_url(self, id, url):
        payment_da = PaymentDataAccessHelper()
        payment_da.update_tian_que_pic_id_url(self.merchant.id, id, url)

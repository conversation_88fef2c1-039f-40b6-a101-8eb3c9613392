# -*- coding: utf-8 -*-

import collections
import logging
import time

import proto.finance.wallet_pb2 as wallet_pb
import proto.finance.fanpiao_pb2 as fanpiao_pb
from business_ops.transaction_manager import TransactionManager
from business_ops.pay_manager import PayManager
from common.utils import distribute_lock
from dao.fanpiao_da_helper import FanpiaoDataAccessHelper
from service import errors
from service.base_responses import success_responses_obj
from service.base_responses import fail_responses_obj

logger = logging.getLogger(__name__)


class FanpiaoPayManager(PayManager):
    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)

    def __round(self, value):
        """对一位小数做4舍5入"""
        value = int(value + 0.5)
        return value

    def partial_refund(self, transaction=None, refund_transaction=None, order=None, **kargs):
        """部分退款
        :no_discount_fee: 不打折的金额,
            打折的金额为 refund_transaction.bill_fee - no_discount_fee
        """
        enable_discount_fee = kargs.get("enable_discount_fee")
        no_discount_fee = order.bill_fee - enable_discount_fee
        for product in order.products if order else []:
            if product.vip_price > 0:
                enable_discount_fee -= int(product.price)
                no_discount_fee += int(product.vip_price)
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos(record_transaction_id=transaction.id)
        for fanpiao in fanpiaos:
            updated = False
            for record in fanpiao.fanpiao_payment_records:
                if record.transaction_id != transaction.id:
                    continue
                if record.is_refund:
                    continue
                logger.info(f"饭票部分退款 {order.id} {record.transaction_id} {transaction.id}")
                fanpiao.total_used_fee -= record.paid_fee
                record.is_refund = True
                updated = True
            if updated:
                fanpiao_da.add_or_update_fanpiao(fanpiao)
        if order and order.bill_fee > 0:
            ret = self._calculate_fanpiao_usage(
                order, transaction, fanpiaos=fanpiaos, discount_fee=enable_discount_fee, no_discount_fee=no_discount_fee
            )
            refund_fee = transaction.paid_fee - ret.paid_fee
            refund_transaction.paid_fee = refund_fee
            transaction.paid_fee = ret.paid_fee
        else:
            refund_transaction.paid_fee = transaction.paid_fee
            transaction.paid_fee = 0
        return True

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos(record_transaction_id=transaction.id)
        for fanpiao in fanpiaos:
            for record in fanpiao.fanpiao_payment_records:
                if record.transaction_id == transaction.id:
                    if record.is_refund is True:
                        raise errors.ShowError(message="该订单已退过了")
                    fanpiao.total_used_fee -= record.paid_fee
                    record.is_refund = True
            fanpiao_da.add_or_update_fanpiao(fanpiao)
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            refund_transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.FANPIAO_PAY,
                paid_fee=transaction.paid_fee,
                bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                use_coupon_id=transaction.use_coupon_id,
            )
            return refund_transaction

    def pos_order_prepay(self, fanpiao_qrcode_info, **kargs):
        if fanpiao_qrcode_info.bill_fee == 0:
            return
        merchant_id = fanpiao_qrcode_info.merchant_id
        bill_fee = fanpiao_qrcode_info.bill_fee
        user_id = fanpiao_qrcode_info.user_id
        no_discount_fee = kargs.get('no_discount_fee', 0)
        paid_fee = self.get_use_fanpiao_fee(bill_fee=bill_fee, no_discount_fee=no_discount_fee, user_id=user_id, merchant_id=merchant_id)
        transaction_manager = TransactionManager()
        transaction = transaction_manager.shilai_pos_scan_qrcode_pay(user_id, merchant_id, bill_fee, paid_fee.fee)
        now = int(time.time())
        transaction.paid_time = now
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction_manager.update_transaction(transaction)
        self.prepay(transaction, fanpiao_qrcode_obj=fanpiao_qrcode_info, **kargs)
        return transaction

    def prepay(self, transaction, **kargs):
        order = kargs.get("order")
        payment = kargs.get("payment")
        key = "{}-fan-piao".format(transaction.payer_id)
        # 因为代码实现不够好,返回值不好修改,所以需要用ret_param来返回额外的返回值
        ret_param = kargs.get("ret_param")
        with distribute_lock.redislock(key=key, ttl=10000, retry_count=5, retry_delay=200) as lock:
            if not lock:
                logger.info("无法获取到饭票锁 - {}".format(transaction.payer_id))
                raise errors.FanpiaoPayProcessing()
            ret = self._calculate_fanpiao_usage(order, transaction, payment, no_discount_fee=kargs.get("no_discount_fee", 0))
            fanpiao_qrcode_obj = kargs.get("fanpiao_qrcode_obj")
            if fanpiao_qrcode_obj is not None:
                fanpiao_qrcode_obj.platform_discount_fee = ret.platform_discount_fee
            if ret_param is not None:
                ret_param.update({"paid_fee": ret.paid_fee})
            return success_responses_obj()
        return fail_responses_obj()

    def cal_fanpiao_fee(self, fee, used_fanpiaos, fanpiaos, no_discount=False, raise_error=True):
        """计算要使用的饭票,计算结果放在used_fanpiaos字典里"""
        FanpiaoFee = collections.namedtuple("FanpiaoFee", ["total_balance", "fee"])
        if fee <= 0:
            return FanpiaoFee(0, 0)
        total_balance = 0
        for fanpiao in fanpiaos:
            this_time_used_fee = used_fanpiaos.get(fanpiao.id, 0)
            # 余额还剩多少
            balance = fanpiao.total_value - fanpiao.total_used_fee - this_time_used_fee
            if balance == 0:
                continue
            # 虽然你一共支付了paid_fee,但是只需要从balance中扣除打折后的金额
            balance_fee = int(float(fee) * (100 - fanpiao.discount) / 100 + 0.5)
            if no_discount:
                balance_fee = fee
            bill_fee_dict = used_fanpiaos.get("bill_fee", {})
            bill_fee = bill_fee_dict.get(fanpiao.id, 0)
            # 如果一张卡的余额足够支付
            if balance >= balance_fee:
                # 支付完了,把paid_fee设置为0,不然后面会提示饭票余额不足
                used_fanpiaos.update({fanpiao.id: balance_fee + used_fanpiaos.get(fanpiao.id, 0)})
                if not no_discount:
                    bill_fee_dict.update({fanpiao.id: bill_fee + fee})
                    used_fanpiaos.update({"bill_fee": bill_fee_dict})
                total_balance += balance_fee
                fee = 0
            else:
                # 如果这张卡只够支付一部分,那么就全使用了
                total_balance += balance
                used_fanpiaos.update({fanpiao.id: balance + used_fanpiaos.get(fanpiao.id, 0)})
                # 计算这张卡能抵用paid_fee中的金额
                _paid_fee = int(float(balance) * 100 / (100 - fanpiao.discount) + 0.5)
                if no_discount:
                    _paid_fee = balance
                if not no_discount:
                    bill_fee_dict.update({fanpiao.id: bill_fee + _paid_fee})
                    used_fanpiaos.update({"bill_fee": bill_fee_dict})
                fee -= _paid_fee
            if fee == 0:
                break
        if fee > 0 and raise_error:
            raise errors.FanpiaoNotEnoughMoney()
        return FanpiaoFee(total_balance=total_balance, fee=fee)

    def get_use_fanpiao_fee(self, bill_fee, no_discount_fee, user_id, merchant_id, raise_error=True):
        members = ["fee", "fanpiaos", "remain_fee", "remain_discount_fee", "remain_no_discount_fee"]
        FanpiaoPayFee = collections.namedtuple("FanpiaoPayFee", members)
        status = fanpiao_pb.Fanpiao.ACTIVE
        fanpiaos = FanpiaoDataAccessHelper().get_fanpiaos(
            user_id=user_id,
            merchant_id=merchant_id,
            brand_id=self.merchant.brand_info.id
            if self.merchant and self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
            else None,
            status=status,
        )
        filter(lambda x: x.total_value > x.total_used_fee, fanpiaos)  # 过滤掉已用完的饭票
        fanpiaos.sort(key=lambda x: x.total_value - x.total_used_fee)  # 把剩余金额做从小到大排序
        used_fanpiaos = {}
        fanpiao_fee_0 = self.cal_fanpiao_fee(bill_fee - no_discount_fee, used_fanpiaos, fanpiaos, raise_error=raise_error)
        fanpiao_fee_1 = self.cal_fanpiao_fee(
            no_discount_fee, used_fanpiaos, fanpiaos, no_discount=True, raise_error=raise_error
        )
        total_balance = fanpiao_fee_0.total_balance + fanpiao_fee_1.total_balance
        remain_fee = fanpiao_fee_0.fee + fanpiao_fee_1.fee
        ret = FanpiaoPayFee(
            total_balance,
            used_fanpiaos,
            remain_fee=remain_fee,
            remain_discount_fee=fanpiao_fee_0.fee,
            remain_no_discount_fee=fanpiao_fee_1.fee,
        )
        return ret

    def get_show_order_paid_fee(self, order, user_id, raise_error=True, fanpiaos=None, **kargs):
        members = ["fee", "fanpiaos", "remain_fee", "remain_discount_fee", "remain_no_discount_fee"]
        FanpiaoPayFee = collections.namedtuple("FanpiaoPayFee", members)
        if fanpiaos is None:
            status = fanpiao_pb.Fanpiao.ACTIVE
            fanpiaos = FanpiaoDataAccessHelper().get_fanpiaos(
                user_id=user_id,
                merchant_id=order.merchant_id,
                brand_id=self.merchant.brand_info.id
                if self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
                else None,
                status=status,
            )
        discount_fee = kargs.get("discount_fee")
        no_discount_fee = kargs.get("no_discount_fee")
        if discount_fee is None or no_discount_fee is None:
            DiscountFee = self.cal_discount_fee_no_discount_fee(order)
            discount_fee = DiscountFee.discount_fee
            no_discount_fee = DiscountFee.no_discount_fee
            for product in order.products:
                if product.vip_price > 0:
                    discount_fee -= int(product.price)
                    no_discount_fee += int(product.vip_price)
        logger.info("如果订单{}使用饭票支付, 打折金额: {}, 不打折金额: {}".format(order.id, discount_fee, no_discount_fee))
        filter(lambda x: x.total_value > x.total_used_fee, fanpiaos)  # 过滤掉已用完的饭票
        fanpiaos.sort(key=lambda x: x.total_value - x.total_used_fee)  # 把剩余金额做从小到大排序
        # {饭票ID: 使用金额}
        used_fanpiaos = {}
        fanpiao_fee_0 = self.cal_fanpiao_fee(discount_fee, used_fanpiaos, fanpiaos, raise_error=raise_error)
        fanpiao_fee_1 = self.cal_fanpiao_fee(
            no_discount_fee, used_fanpiaos, fanpiaos, no_discount=True, raise_error=raise_error
        )
        total_balance = fanpiao_fee_0.total_balance + fanpiao_fee_1.total_balance
        remain_fee = fanpiao_fee_0.fee + fanpiao_fee_1.fee
        logger.info("如果订单{}使用饭票支付: {}".format(order.id, total_balance))
        ret = FanpiaoPayFee(
            total_balance,
            used_fanpiaos,
            remain_fee=remain_fee,
            remain_discount_fee=fanpiao_fee_0.fee,
            remain_no_discount_fee=fanpiao_fee_1.fee,
        )
        return ret

    def _calculate_fanpiao_usage(self, order, transaction, payment=None, fanpiaos=None, no_discount_fee=0, **kargs):
        """计算用户这一次使用饭票情况"""
        Ret = collections.namedtuple("Ret", ["platform_discount_fee", "paid_fee"])
        if fanpiaos is None:
            status = fanpiao_pb.Fanpiao.ACTIVE
            fanpiaos = FanpiaoDataAccessHelper().get_fanpiaos(
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                brand_id=self.merchant.brand_info.id
                if self.merchant.enable_brand_fanpiao and self.merchant.brand_info.id
                else None,
                status=status,
            )
        filter(lambda x: x.total_value > x.total_used_fee, fanpiaos)  # 过滤掉已用完的饭票
        fanpiaos.sort(key=lambda x: x.total_value - x.total_used_fee)  # 把剩余金额做从小到大排序
        fanpiaos_dict = {fanpiao.id: fanpiao for fanpiao in fanpiaos}

        if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
            fanpiao_payment_record_list = self.get_use_fanpiao_fee(
                bill_fee=transaction.bill_fee,
                no_discount_fee=payment.no_discount_fee,
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
            ).fanpiaos
        elif transaction.type == wallet_pb.Transaction.SHILAI_POS_ORDER_PAYMENT:
            fanpiao_payment_record_list = self.get_use_fanpiao_fee(
                bill_fee=transaction.bill_fee,
                user_id=transaction.payer_id,
                merchant_id=transaction.payee_id,
                no_discount_fee=no_discount_fee,
            ).fanpiaos
        else:
            fanpiao_payment_record_list = self.get_show_order_paid_fee(
                order, transaction.payer_id, fanpiaos=fanpiaos, **kargs
            ).fanpiaos

        platform_discount_fee = 0
        paid_fee = 0
        for _, fanpiao in fanpiaos_dict.items():
            if fanpiao.id in fanpiao_payment_record_list:
                fanpiao_payment = fanpiao.fanpiao_payment_records.add()
                fanpiao_payment.create_time = int(time.time())
                fanpiao_payment.transaction_id = transaction.id
                fanpiao_payment.paid_fee = fanpiao_payment_record_list.get(fanpiao.id)
                paid_fee += fanpiao_payment.paid_fee
                fanpiao_payment.bill_fee = fanpiao_payment_record_list.get("bill_fee", {}).get(fanpiao.id, 0)
                subsidy_fee = self.__calculate_fanpiao_payment_record_subsidy_fee(fanpiao, fanpiao_payment)
                fanpiao_payment.subsidy_fee = subsidy_fee
                platform_discount_fee += subsidy_fee
                fanpiao.total_used_fee += fanpiao_payment.paid_fee
                logger.info("使用饭票: {} 支付金额: {} 补贴金额为: {}".format(fanpiao.id, fanpiao_payment.paid_fee, subsidy_fee))
                FanpiaoDataAccessHelper().add_or_update_fanpiao(fanpiao)
        if order is not None:
            order.platform_discount_fee = platform_discount_fee
        return Ret(platform_discount_fee, paid_fee)

    def __calculate_fanpiao_payment_record_subsidy_fee(self, fanpiao, fanpiao_payment):
        current_max_discount = self.merchant.preferences.coupon_config.max_discount
        discount = fanpiao.discount
        max_discount = fanpiao.max_discount
        extra_discount = fanpiao.extra_discount
        if max_discount == 0:
            max_discount = current_max_discount
        if extra_discount == 0:
            extra_discount = self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        max_discount += extra_discount
        if self.registration_info.fanpiao_pay_commission_rate > 0:
            max_discount = 0
        if discount <= max_discount:
            return 0
        fee = self.__round(fanpiao_payment.bill_fee * ((discount - max_discount) / float(100)))
        return fee

    def cal_discount_fee_no_discount_fee(self, order):
        DiscountFee = collections.namedtuple("DiscountFee", ["discount_fee", "no_discount_fee"])
        discount_fee = order.enable_discount_fee
        no_discount_fee = order.bill_fee - order.enable_discount_fee
        if order.giving_fee > 0:
            discount_fee = order.bill_fee - order.giving_fee
            no_discount_fee = 0
        return DiscountFee(discount_fee, no_discount_fee)

    def cal_fanpiao_platform_discount(self, transaction):
        """pos机使用饭票支付的话计算出时来需要的金额"""
        bill_fee = abs(transaction.bill_fee)
        paid_fee = abs(transaction.paid_fee)
        max_discount = (
            int(
                self.merchant.preferences.coupon_config.max_discount
                * self.merchant.preferences.coupon_config.dish_discount_rate
                / float(100)
            )
            + self.merchant.preferences.coupon_config.fanpiao_and_coupon_extra_discount
        )
        if self.registration_info.fanpiao_pay_commission_rate > 0:
            # 如果商家的饭票佣金费率大于0,饭票补贴金额需要补贴到 100%
            max_discount = 0
        least_paid_fee = int(bill_fee * (100 - max_discount) / float(100) + 0.5)
        real_paid_fee = paid_fee
        if real_paid_fee >= least_paid_fee:
            return 0
        platform_discount_fee = least_paid_fee - real_paid_fee
        return self.__round(platform_discount_fee)

    def pos_order_refund(self, transaction, **kargs):
        """收银机端发起饭票退款"""
        if not transaction:
            return None
        if transaction.pay_method != wallet_pb.Transaction.FANPIAO_PAY:
            return None
        if transaction.state != wallet_pb.Transaction.SUCCESS:
            return None
        fanpiao_da = FanpiaoDataAccessHelper()
        fanpiaos = fanpiao_da.get_fanpiaos(record_transaction_id=transaction.id)
        refund_fee = kargs.get("refund_fee")
        for fanpiao in fanpiaos:
            refund_success = self.pos_order_refund_single_fanpiao(fanpiao, transaction, refund_fee)
            if refund_success:
                fanpiao_da.add_or_update_fanpiao(fanpiao)
        refund_transaction = TransactionManager().handle_shilai_pos_order_refund(
            user_id=transaction.payer_id,
            merchant_id=transaction.payee_id,
            pay_method=wallet_pb.Transaction.FANPIAO_PAY,
            paid_fee=transaction.paid_fee,
            bill_fee=transaction.bill_fee,
        )
        return refund_transaction

    def pos_order_refund_single_fanpiao(self, fanpiao, transaction, refund_fee):
        """退款时,这一张饭票应退多少
        :fanpiao: 饭票实体
        :transaction: 本次支付对应的流水
        :refund_fee: 当前还需要退款的总金额
        """
        for record in fanpiao.fanpiao_payment_records:
            if record.transaction_id == transaction.id:
                if record.is_refund is True:
                    raise errors.ShowError(message="该订单已退过了")
                fanpiao.total_used_fee -= record.paid_fee
                record.is_refund = True
                return True
        return False

# -*- coding: utf-8 -*-

import base64
import hashlib
import io
import json
import re
import os
import time
import logging
from collections import namedtuple

import qrcode
from PIL import Image
from PIL import ImageDraw
from PIL import ImageFont

import proto.config_pb2 as config_pb
import proto.page.table_pb2 as page_table_pb
from business_ops.base_manager import BaseManager
from common.config import config
from common import http
from dao.config_da_helper import ConfigDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper

logger = logging.getLogger(__name__)


class QrcodePrinterManager(BaseManager):
    def __init__(self, *args, **kargs):
        super(QrcodePrinterManager, self).__init__(*args, **kargs)
        self.UNLIMITED_CODE_URL = "https://api.weixin.qq.com/wxa/getwxacodeunlimit"
        self.printer_configs = ConfigDataAccessHelper().get_qrcode_printer_configs()
        self.url = "https://poll.kuaidi100.com/printapi/printtask.do"
        self.xp_yun_url = "https://open-barcode.xpyun.net/api/openapi/sprinter"
        self.xp_yun_method = 'printImage'
        self.method = "imgOrder"
        self.chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】]+")
        self.city = kargs.get("city")
        self.wifi_account = kargs.get("wifi_account")
        self.wifi_pwd = kargs.get("wifi_pwd")
        self.err_messages = []
        self.font_filepath = "/data/fonts/Alibaba-PuHuiTi-Regular.otf"
        self.copy_num = kargs.get("copy_num", 1)
        if self.wifi_account and self.wifi_pwd:
            self.store.wifi_account = self.wifi_account
            self.store.wifi_pwd = self.wifi_pwd
        else:
            self.wifi_account = None
            self.wifi_pwd = None
        self.fill_color = "#000000"

    def get_sign(self, param, config):
        Sign = namedtuple("Sign", ["sign", "timestamp", "url"])
        param_json = json.dumps(param)
        timestamp = str(int(time.time()))
        md = hashlib.md5()
        tmp = param_json + timestamp + config.kuai_di_hundred.key + config.kuai_di_hundred.secret
        md.update(tmp.encode())
        sign = md.hexdigest().upper()
        key = config.kuai_di_hundred.key
        url = "{}?method={}&key={}&t={}&param={}&sign={}".format(self.url, self.method, key, timestamp, param_json, sign)
        return Sign(sign, timestamp, url)

    def get_sign_xpyun(self, param, config):
        Sign = namedtuple("Sign", ["sign", "timestamp", "url"])
        timestamp = int(time.time())
        sha1 = hashlib.sha1()
        tmp = config.xp_yun.key + config.xp_yun.secret + str(timestamp)
        sha1.update(tmp.encode())
        sign = sha1.hexdigest().lower()
        param.update({'timestamp': timestamp, 'sign': sign})
        return Sign(sign, timestamp, f"{self.xp_yun_url}/{self.xp_yun_method}")

    def get_table_name_start_x_y(self, table_name_obj, type=None):
        Position = namedtuple("Position", ["x", "y", "font_size"])
        start_x = self.calculate_start_position(table_name_obj)
        start_y = 545
        font_size = 130
        if self.wifi_account is None and self.wifi_pwd is None:
            start_y = 625
        if table_name_obj.cn_count > 0:
            start_y += 40
        else:
            start_y += 40
        return Position(start_x, start_y, font_size)

    def get_wifi_start_x_y(self, table_name_obj, type=None):
        Position = namedtuple("Position", ["account_x", "account_y", "pwd_x", "pwd_y", "account_font_size", "pwd_font_size"])
        account_x = 40
        pwd_x = 40
        account_y = 715
        pwd_y = 785
        account_font_size = 60
        pwd_font_size = 60
        if table_name_obj == " ":
            account_y += 5
            pwd_y += 5
        else:
            if table_name_obj.cn_count > 0:
                account_y += 50
                pwd_y += 50
            else:
                account_y += 45
                pwd_y += 45
        return Position(account_x, account_y, pwd_x, pwd_y, account_font_size, pwd_font_size)

    def print_qrcode(self, image, config, table=None, type=None):
        param = (
            {
                "height": "80",
                "width": "40",
                "siid": config.kuai_di_hundred.siid,
                "copyNum": str(self.copy_num),
            }
            if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED
            else {
                'user': config.xp_yun.key,
                'sn': config.xp_yun.siid,
                'copies': self.copy_num,
                'paperHeight': 80 * 8,
                'paperWidth': 40 * 8,
                'mode': 1,
            }
        )
        sign = (
            self.get_sign(param, config)
            if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED
            else self.get_sign_xpyun(param, config)
        )
        logger.info(
            "{}打印接口url: {} {}".format(
                '快递100' if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED else '芯烨云',
                table.id if table else '',
                sign.url,
            )
        )
        width, height = image.size
        logger.info(
            "{}打印机图片size: {} {}".format(
                '快递100' if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED else '芯烨云', width, height
            )
        )
        blank_image = Image.new(mode="RGB", size=(width, height + int(height / 2)), color=(255, 255, 255))
        blank_image.paste(image, box=(0, 15))

        draw = ImageDraw.Draw(blank_image)

        self.fill_table_info(draw, table)
        self.fill_wifi_info(draw, type, table=table)

        img_byte_arr = io.BytesIO()
        blank_image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()

        if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED:
            files = [('file', ("test.png", img_byte_arr, 'image/png'))]
            resp = http.post(sign.url, data={}, files=files)
        else:
            logger.info(f"芯烨打印参数: {param}")
            param.update({'content': base64.b64encode(img_byte_arr).decode('ascii')})
            resp = http.post(sign.url, json=param)
        logger.info(
            "{}打印接口返回: {}".format(
                '快递100' if config.category == config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED else '芯烨云', resp.json()
            )
        )

    def fill_table_info(self, draw, table):
        if table is None:
            return
        table_name = self.calculate_table_name(table.name)

        table_name_position = self.get_table_name_start_x_y(table_name, type=type)
        table_font_set = ImageFont.truetype(self.font_filepath, table_name_position.font_size)
        draw.text((table_name_position.x, table_name_position.y), table_name.name, fill=self.fill_color, font=table_font_set)

    def fill_wifi_info(self, draw, type, table):
        if not (self.wifi_account is not None and self.wifi_pwd is not None):
            return
        wifi_account = "WIFI: {}".format(self.wifi_account)
        wifi_pwd = "密码: {}".format(self.wifi_pwd)
        if table is not None:
            table_name = self.calculate_table_name(table.name)
            wifi_position = self.get_wifi_start_x_y(table_name, type=type)
        else:
            wifi_position = self.get_wifi_start_x_y(" ", type=type)
        wifi_font_set = ImageFont.truetype(self.font_filepath, wifi_position.account_font_size)
        wifi_pwd_font_set = ImageFont.truetype(self.font_filepath, wifi_position.pwd_font_size)
        account_x = wifi_position.account_x
        account_y = wifi_position.account_y
        pwd_x = wifi_position.pwd_x
        pwd_y = wifi_position.pwd_y
        draw.text((account_x, account_y), wifi_account, fill=self.fill_color, font=wifi_font_set)
        draw.text((pwd_x, pwd_y), wifi_pwd, fill=self.fill_color, font=wifi_pwd_font_set)

    def print_table_qrcode(self, image, config, table, type=None):
        self.print_qrcode(image, config, table=table, type=type)

    def calculate_table_name(self, table_name):
        Table = namedtuple("Table", ["name", "cn_count", "en_count"])
        chinese = self.chinese_re_pattern.findall(table_name)
        total_count = len(table_name)
        cn_count = 0
        en_count = 0
        for c in chinese:
            cn_count += len(c)
        en_count = total_count - cn_count
        return Table(table_name, cn_count, en_count)

    def calculate_start_position(self, table):
        """Alibaba-PuHuiTi-Regular.otf 一个汉字=1.5个英文"""
        start_x = 55
        if table.cn_count == 4 and table.en_count == 0:
            return start_x - 10
        if table.cn_count == 3 and table.en_count == 2:
            return start_x - 30
        if table.cn_count == 3 and table.en_count == 1:
            return start_x + 5
        if table.cn_count == 3 and table.en_count == 0:
            return start_x + 55
        if table.cn_count == 2 and table.en_count == 4:
            return start_x - 40
        if table.cn_count == 2 and table.en_count == 3:
            return start_x
        if table.cn_count == 2 and table.en_count == 2:
            return start_x + 35
        if table.cn_count == 2 and table.en_count == 1:
            return start_x + 75
        if table.cn_count == 2 and table.en_count == 0:
            return start_x + 120
        if table.cn_count == 1 and table.en_count == 6:
            return start_x - 40
        if table.cn_count == 1 and table.en_count == 5:
            return start_x - 20
        if table.cn_count == 1 and table.en_count == 4:
            return start_x + 10
        if table.cn_count == 1 and table.en_count == 3:
            return start_x + 60
        if table.cn_count == 1 and table.en_count == 2:
            return start_x + 105
        if table.cn_count == 1 and table.en_count == 1:
            return start_x + 140
        if table.cn_count == 1 and table.en_count == 0:
            return start_x + 180
        if table.cn_count == 0 and table.en_count == 8:
            return start_x - 40
        if table.cn_count == 0 and table.en_count == 7:
            return start_x - 40
        if table.cn_count == 0 and table.en_count == 6:
            return start_x + 10
        if table.cn_count == 0 and table.en_count == 5:
            return start_x + 45
        if table.cn_count == 0 and table.en_count == 4:
            return start_x + 85
        if table.cn_count == 0 and table.en_count == 3:
            return start_x + 130
        if table.cn_count == 0 and table.en_count == 2:
            return start_x + 170
        if table.cn_count == 0 and table.en_count == 1:
            return start_x + 210
        return start_x

    def get_table_info(self):
        ordering_da = OrderingServiceDataAccessHelper()
        config_da = ConfigDataAccessHelper()
        printer_configs = config_da.get_qrcode_printer_configs()
        tables = ordering_da.get_tables(merchant_id=self.merchant.id)
        tables.sort(key=lambda t: t.sort)
        table_info_vo = page_table_pb.PrinterQrcodeTableInfo()
        table_info_vo.merchant_id = self.merchant.id
        table_info_vo.wifi_account = self.store.wifi_account
        table_info_vo.wifi_pwd = self.store.wifi_pwd
        table_info_vo.pos_type = self.registration_info.pos_type
        table_names = []
        for table in tables:
            table_names.append(table.name)
        table_info_vo.table_names = ",".join(table_names)
        for printer_config in printer_configs:
            table_info_vo.citys.append(printer_config.city)
        return table_info_vo

    def create_qrcode(self, **kargs):
        config_da = ConfigDataAccessHelper()
        ordering_da = OrderingServiceDataAccessHelper()
        configs = config_da.get_qrcode_printer_configs()
        if kargs.get("mphelper"):
            self.add_material()
        table_names = kargs.get("table_names", "")
        table_names = table_names.split(",")
        tables = []
        for table_name in table_names:
            table = ordering_da.get_table(merchant_id=self.merchant.id, name=table_name)
            if not table:
                continue
            tables.append(table)
        buy_fanpiao_qrcode = kargs.get("buy_fanpiao_qrcode")
        for printer_config in configs:
            if printer_config.city != self.city:
                continue
            if printer_config.category not in [
                config_pb.QrcodePrinterConfig.KUAIDI_HUNDRED,
                config_pb.QrcodePrinterConfig.XP_YUN,
            ]:
                continue
            if buy_fanpiao_qrcode is not None:
                self.create_buy_fanpiao_qrcode(printer_config)
                break
            for table in tables:
                if len(table_names) > 0 and table.name not in table_names:
                    continue
                if kargs.get("wechat_alipay_two_in_one"):
                    self.create_wechat_alipay_two_in_one_qrcode(table, printer_config)
                if kargs.get("mphelper"):
                    self.create_mphelper_qrcode(table, printer_config)
                if kargs.get("wechat_qrcode"):
                    page = kargs.get("page")
                    self.create_wechat_qrcode(table, printer_config, page)
                if kargs.get("home_two_in_one"):
                    self.create_home_wechat_alipay_two_in_one_qrcode(table, printer_config)
                time.sleep(1)

    def get_access_token(self, appid=None):
        if appid is None:
            appid = config.WECHAT_MINIPROGRAM_APPID
        domain = os.environ.get("ACCESS_TOKEN_SERVICE_DOMAIN", "https://shilai.zhiyi.cn")
        url = f"{domain}/wxaccess_token/get_authorizer_token/{appid}"
        resp = http.get(url)
        resp_json = resp.json()
        if not resp_json.get("access_token"):
            return None
        return resp_json['access_token']

    def create_unlimited_code(self, scene, page, is_hyaline):
        access_token = self.get_access_token()
        url = "{}?access_token={}".format(self.UNLIMITED_CODE_URL, access_token)
        request_json = {'scene': scene, 'page': page, 'width': 430, 'is_hyaline': is_hyaline}
        resp = http.post(url, json=request_json)
        return resp.content

    def add_material(self):
        appid = self.merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
        token = self.get_access_token(appid)
        if not token:
            self.err_messages.append("公众号获取token失败")
            return False
        url = self.merchant.basic_info.logo_url
        if url == "":
            self.err_messages.append("公众号logo为空")
            return False
        material_image = http.get(url).content
        bytes_array = io.BytesIO(material_image)

        files = [('media', ("test.jpg", bytes_array, "image/jpg"))]
        url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={}".format(token)
        ret = http.post(url, files=files).json()
        media_id = ret.get("media_id")
        self.merchant.shilai_platform_authorizer_info.authorization_info.thumb_media_id = media_id
        return True

    def create_wechat_qrcode(self, table, config, page=None):
        if page is None:
            page = "package-merchant/menu"
        table_id = table.id
        code_content = None
        code_content = self.create_unlimited_code(scene=table_id, page=page, is_hyaline=True)
        if code_content:
            image_bytes = io.BytesIO(code_content)
            image = Image.open(image_bytes)
            self.print_table_qrcode(image, config, table, type="wechat_qrcode")
        else:
            self.err_messages.append("创建桌台'{}'微信二维码失败".format(table.name))

    def create_buy_fanpiao_qrcode(self, printer_config):
        scenes = {
            "POS_A1_POSTER": "收银台A1",
            "POS_A4_DECA": "收银台A4",
            "POS_A5_DECA": "收银台A5",
            "POS_A6_DECA": "收银台A6",
            "_8_10_STICKER": "8_10贴纸",
            "CATALOG_STICKER": "菜单贴纸",
            "JUMP_CARD": "跳跳卡",
            "HORIZONTAL_DESK_STICKER": "横板桌贴",
            "VERTICAL_DESK_STICKER": "竖板桌贴",
            "NUMBER_PLATE_STICKER": "号牌贴",
            "DOOR_A1": "门口A1",
            "DOOR_A3": "门口A3",
            "DOOR_A4": "门口A4",
            "KT_BOARD": "门口KT",
            "POS_VICE": "收银副屏",
            "OTHERS": "其它",
        }
        h5_domain = os.environ.get("H5_SERVICE_DOMAIN", "http://shilai-h5.zhiyi.cn")
        domain = os.environ.get("STAFF_ASSIST_SERVICE_DOMAIN", "https://shilai.zhiyi.cn")
        url = f"{domain}/qrcode-info/generate"
        for scene, name in scenes.items():
            if scene != "OTHERS":
                continue
            params = {
                "page": "pages/home/<USER>",
                "function": "MINI_PROGRAM_BUY_FANPIAO",
                "merchantId": self.merchant.id,
                "scene": "POS_QRCODE",
                "name": name,
            }
            ret = http.post(url, json=params)
            ret = ret.json()
            scene_id = ret.get("data").get("id")
            qrcode_url = f"{h5_domain}/home?scene={scene_id}"
            img = qrcode.make(qrcode_url, version=9)
            self.print_qrcode(img, printer_config)

    def create_home_wechat_alipay_two_in_one_qrcode(self, table, config):
        """主页二合一码"""
        h5_domain = os.environ.get("H5_SERVICE_DOMAIN", "http://shilai-h5.zhiyi.cn")
        qrcode_url = f"{h5_domain}/merchant?merchantId={self.merchant.id}&tableId={table.ordering_service_table_id}&tableName={table.name}&id={table.id}"
        img = qrcode.make(qrcode_url, version=9)
        self.print_table_qrcode(img, config, table)

    def create_wechat_alipay_two_in_one_qrcode(self, table, config):
        """微信支付宝二合一码"""
        name = table.name
        name = name.replace("\n", "")
        h5_domain = os.environ.get("H5_SERVICE_DOMAIN", "http://shilai-h5.zhiyi.cn")
        kargs = {
            "merchantId": self.merchant.id,
            "tableId": table.ordering_service_table_id,
            "tableName": name,
            "id": table.id,
            "h5_domain": h5_domain,
        }
        qrcode_url = '{h5_domain}/menu?merchantId={merchantId}&tableId={tableId}&tableName={tableName}&id={id}'.format(**kargs)
        logger.info(f"微信支付宝二合一码: {qrcode_url}")
        img = qrcode.make(qrcode_url, version=9)
        self.print_table_qrcode(img, config, table)

    def create_mphelper_qrcode(self, table, config):
        """公众号码"""
        appid = self.merchant.shilai_platform_authorizer_info.authorization_info.authorization_appid
        token = self.get_access_token(appid)
        table_id = table.id
        url = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={}"
        params = {"action_name": "QR_LIMIT_STR_SCENE", "action_info": {"scene": {"scene_str": table_id}}}
        url = url.format(token)
        ret = http.post(url, json=params).json()
        ticket = ret.get("ticket")
        url = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={}".format(ticket)
        ret = http.get(url)
        image = Image.open(io.BytesIO(ret.content))
        image = image.resize((610, 610))
        self.print_table_qrcode(image, config, table)

# -*- coding: utf-8 -*-


""" 直接扫码支付,生成一个没有菜品的订单
"""

import logging
import time

import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.dish_pb2 as dish_pb
from business_ops.business_manager import BusinessManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.fanpiao_helper import FanpiaoHelper
from business_ops.coupon_manager import CouponManager
from common.utils.number_utils import NumberUtils
from dao.coupon_da_helper import CouponDataAccessHelper
from business_ops.promotion.coupon.coupon_manager import Coupon<PERSON>anager as NewCouponManager
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import error_codes
from service import errors
import proto.ordering.registration_pb2 as registration_pb

logger = logging.getLogger(__name__)


class DirectPayManager(BusinessManager):
    def __init__(self, *args, **kargs):
        self.transaction_id = kargs.get("transaction_id")
        self.transaction = kargs.get("transaction")
        self.pay_method = kargs.get("pay_method")
        self.user_id = kargs.get("user_id")
        self.paid_fee = NumberUtils.safe_round(kargs.get("paid_fee"))
        self.bill_fee = NumberUtils.safe_round(kargs.get("bill_fee"))
        self.coupon_id = kargs.get("coupon_id")
        self._order_manager_v2 = kargs.get("order_manager_v2")

        self._coupon_manager = None
        self._coupon_da = None

        super(DirectPayManager, self).__init__(*args, **kargs)
        if self.paid_fee == 0:
            self.pay_method = wallet_pb.Transaction.WALLET

    @property
    def coupon_manager(self):
        if self._coupon_manager is not None:
            return self._coupon_manager
        self._coupon_manager = CouponManager()
        return self._coupon_manager

    @property
    def coupon_da(self):
        if self._coupon_da:
            return self._coupon_da
        self._coupon_da = CouponDataAccessHelper()
        return self._coupon_da

    def compensation_transaction(self, transaction):
        """微信/支付宝: 已经支付成功了,但是订单没有成功的时候做补偿."""
        manager = OrderManager(merchant=self.merchant, business_obj=self)
        self.transaction = transaction
        self.order = manager.create_no_dish_order(transaction=self.transaction)
        self.order.enable_discount_fee = self.transaction.bill_fee
        self.notification(order=self.order)

    def prepay(self, *args, **kargs):
        self.__check_pay_method()
        self.init_coupon_info_v2_single()
        has_coupon = self.__check_coupon_can_consume()
        logger.info(f"券与支付金额: {self.coupon_id} {has_coupon} {self.paid_fee}")
        if not has_coupon and self.paid_fee <= 0:
            raise errors.ShowError("支付金额错误")
        transaction_manager = TransactionManager()
        user_id = None
        self.order = None
        if self.user:
            user_id = self.user.id
        self.transaction = transaction_manager.create_direct_pay_transaction(
            payer_id=user_id,
            payee_id=self.merchant.id,
            payee_store_id=self.store.id,
            bill_fee=self.bill_fee,
            paid_fee=self.paid_fee,
            pay_method=self.pay_method,
        )
        if self.coupon_id is not None:
            self.transaction.use_coupon_id = self.coupon_id
        if kargs.get("pay_channel") is not None:
            self.transaction.pay_channel = kargs.get("pay_channel")
        if self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            if self.merchant.disable_number_plate_pay_fanpiao_pay:
                raise errors.ShowError("该门店不允许使用卡包在码牌支付")
        manager = OrderManager(merchant=self.merchant, business_obj=self)
        self.order = manager.create_no_dish_order(transaction=self.transaction)
        self.order.enable_discount_fee = self.transaction.bill_fee
        if has_coupon == 2:
            self.order.coupon_ids.append(self.coupon_id)
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        ret_param = {}
        result = payment_manager.prepay(
            transaction=self.transaction, order=self.order, ret_param=ret_param, auth_code=kargs.get("auth_code")
        )
        if result.get("errcode") not in [error_codes.SUCCESS, error_codes.USER_PAYING]:
            raise errors.ShowError("余额不足,支付失败")
        if ret_param:
            self.transaction.paid_fee = ret_param.get("paid_fee")
            self.order.paid_fee = self.transaction.paid_fee
        msg = f"""
        码牌prepay完成: {self.transaction.id} {self.order.id}
        transaction.bill_fee: {self.transaction.bill_fee}
        transaction.paid_fee: {self.transaction.paid_fee}
        payMethod: {wallet_pb.Transaction.PayMethod.Name(self.transaction.pay_method)}
        """
        logger.info(msg)
        result['transactionId'] = self.transaction.id
        transaction_manager.update_transaction(self.transaction)
        if self.transaction.pay_method in [wallet_pb.Transaction.FANPIAO_PAY, wallet_pb.Transaction.WALLET]:
            self.__prepay_success()
        elif self.transaction.pay_method in [wallet_pb.Transaction.TIAN_QUE_PAY]:
            if self.transaction.auth_code != "":
                if result.get("errcode") == error_codes.SUCCESS:
                    self.__prepay_success()
                elif result.get("errcode") == error_codes.USER_PAYING:
                    self.order.status = dish_pb.DishOrder.PAYING
        result['orderId'] = self.order.id
        return result

    def __prepay_success(self):
        self.notification()
        self.order.status = dish_pb.DishOrder.PAID
        if self.transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            FanpiaoHelper().set_user_fanpiao_purchase_risk_control(user=self.user, merchant=self.merchant)

    def __check_pay_method(self):
        if self.pay_method == "FANPIAO_PAY":
            self.pay_method = wallet_pb.Transaction.PayMethod.Value(self.pay_method)
        if self.pay_method not in [
            wallet_pb.Transaction.WECHAT_PAY,
            wallet_pb.Transaction.ALIPAY,
            wallet_pb.Transaction.FANPIAO_PAY,
            wallet_pb.Transaction.WALLET,
        ]:
            raise errors.PayMethodNotSupport()

    def __check_coupon_can_consume(self):
        if self.coupon_id is None or self.coupon_id == "":
            return 0
        if self.pay_method == "FANPIAO_PAY":
            self.pay_method = wallet_pb.Transaction.PayMethod.Value(self.pay_method)
        if self.pay_method in [wallet_pb.Transaction.FANPIAO_PAY]:
            raise errors.ShowError(message="卡包与券包不能同时使用")
        if self.coupon_manager.can_consume_coupon(self.coupon_id):
            return 1
        if self.check_coupon_fee_matched_direct():
            return 2
        raise errors.ShowError(message="优惠券已过期或已使用")

    def __consume_coupon(self):
        if self.coupons and len(self.coupons) > 0:
            self.consume_coupon_v2_single()
        elif self.transaction.use_coupon_id:
            coupon = self.coupon_da.get_coupon_by_id(self.transaction.use_coupon_id)
            if coupon:
                coupon_category = self.coupon_manager.get_coupon_category_by_coupon(coupon=coupon)
                self.order.coupon_fee = coupon_category.cash_coupon_spec.reduce_cost
                self.coupon_manager.consume_coupon(self.transaction.use_coupon_id)

    def notification(self, *args, **kargs):
        transaction_manager = TransactionManager()
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = int(time.time())
        transaction_manager.update_transaction(self.transaction)

        manager = OrderManager(merchant=self.merchant, business_obj=self)
        if self.order is None:
            ordering_da = OrderingServiceDataAccessHelper()
            self.order = ordering_da.get_order(transaction_id=self.transaction.id)
            if self.order and self.order.coupon_ids:
                coupon_manager = NewCouponManager(self.user, self.merchant)
                coupon = coupon_manager.get_coupon(self.order.coupon_ids[-1])
                if coupon:
                    self.coupons.append(coupon)
        if self.order is None:
            self.order = manager.create_no_dish_order(transaction=self.transaction)
            if self.transaction.use_coupon_id:
                coupon_manager = NewCouponManager(self.user, self.merchant)
                coupon = coupon_manager.get_coupon(self.transaction.use_coupon_id)
                if coupon:
                    self.coupons.append(coupon)
                    self.order.coupon_ids.append(self.transaction.use_coupon_id)
        self.order.meal_type = dish_pb.DishOrder.DIRECT_PAY
        self.order.status = dish_pb.DishOrder.PAID
        self.__consume_coupon()
        self._cal_coupon_subsidy_fee(registration_info=self.registration_info)
        self._cal_platform_discount(registration_info=self.registration_info)
        if self.order.coupon_ids and len(self.order.coupon_ids) > 0:
            manager.cal_coupon_commission_fee(self.order, self.transaction)
        else:
            manager._cal_commission(self.order, self.transaction)

        manager.sync_order_to_pos(transaction=self.transaction, order=self.order)

        payment_manager = PaymentManager(pay_method=self.transaction.WECHAT_PAY, merchant=self.merchant)
        if not kargs.get("union_pay"):
            payment_manager.scan_code_order_launch_ledger(self.transaction, order=self.order)
        payment_manager.update_scan_code_order_merchant_transfer_fee(self.order, self.transaction)

        if self._order_manager_v2:
            self._order_manager_v2.order = self.order
            self._order_manager_v2.transactions = [self.transaction]
            self._order_manager_v2.update_order_operation_record_pay_order(type=wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT)
        manager.send_order_to_data_center(order=self.order, source="NUMBER_PLATE_PAY")
        return self.order

# -*- encoding: utf-8 -*-

'''
@Time        :   2025/02/18 15:52:06
'''

import re


_cn_pattern = re.compile("[\u4e00-\u9fa5（）【】《》]")


def split_two_text(name: str, max_ch_width: int) -> int:
    current_width = 0
    for idx, char in enumerate(name):
        char_w = 2 if ord(char) > 127 else 1
        if current_width + char_w > max_ch_width:
            return idx
        current_width += char_w
    return len(name)


def split_text(name: str, max_ch_width: int) -> int:
    current_width = 0
    last_idx = 0
    texts = []
    for idx, char in enumerate(name):
        char_w = 2 if ord(char) > 127 else 1
        if current_width + char_w > max_ch_width:
            texts.append(name[last_idx:idx])
            current_width = 0
            last_idx = idx
        current_width += char_w
    if current_width > 0:
        texts.append(name[last_idx:])
    return texts


def calculate_width(s: str, char_alpha=1) -> int:
    cn_count = len(_cn_pattern.findall(s))
    en_count = len(s) - cn_count
    cn_char_number = cn_count * 2 * char_alpha
    en_char_number = en_count * char_alpha
    return cn_char_number + en_char_number


def ljust_split_text(name: str, max_ch_width: int, char_alpha: float = 1):
    """
    实现左对齐的精准分割并填充
    :param name: 原始文本
    :param max_ch_width: 目标总宽度（中文字符宽度）
    :return: (填充后的前半部分, 剩余部分)
    """
    split_idx = split_two_text(name, max_ch_width)
    first_part = name[:split_idx]
    remaining = name[split_idx:]
    actual_width = calculate_width(first_part, char_alpha=char_alpha)
    padding = max(max_ch_width - actual_width, 0)
    aligned_part = first_part + ' ' * padding
    return aligned_part, remaining


def split_tail_item(name: str, *args, max_total_width: int, char_alpha: float = 1, space_alpha: int = 1) -> list:
    """
    动态换行属性名并右对齐价格
    :param attr_name: 属性名称（支持中英文混合）
    :param price: 价格数值
    :param max_total_width: 总显示宽度（单位：英文字符宽度）
    :param char_alpha: 字体大小倍率
    :param space_alpha: args每列的空格数倍率
    """
    texts = split_text(name, max_total_width)
    name = texts.pop(-1)
    space = " " * int(space_alpha)
    item_str = f" {space.join(map(str, args))}"
    item_with = calculate_width(item_str, char_alpha=char_alpha)
    actual_width = calculate_width(name, char_alpha=char_alpha)
    if actual_width + item_with < max_total_width:
        first_line_name = ""
        remaining_name = name
    else:
        first_line_name = name
        remaining_name = ""
    
    name_width = calculate_width(remaining_name, char_alpha=char_alpha)
    padding_spaces = max(max_total_width - name_width - item_with, 0)
    lines = [first_line_name] if first_line_name else []
    lines.append(f"{remaining_name}{' ' * padding_spaces}{item_str}")
    texts.extend(lines)
    return texts


def split_head_item(name: str, *args, max_total_width: int, char_alpha: float = 1, space_alpha: int = 1):
    space = " " * int(space_alpha)
    item_str = f" {space.join(map(str, args))}"
    item_with = calculate_width(item_str, char_alpha=char_alpha)
    first_index = split_two_text(name, max_total_width - item_with)
    first_name = name[:first_index]
    padding_spaces = max(max_total_width - item_with - calculate_width(first_name, char_alpha=char_alpha), 0)
    texts = [f"{first_name}{' ' * padding_spaces}{item_str}"]
    last_name = name[first_index:]
    if last_name:
        texts.append(name[first_index:])
    return texts


def right_align_item(*items, max_total_width: int, char_alpha: float = 1, space_alpha: int = 1):
    space = " " * int(space_alpha)
    item_str = f" {space.join(map(str, items))}"
    item_with = calculate_width(item_str, char_alpha=char_alpha)
    padding_spaces = max(max_total_width - item_with, 0)
    return ' ' * padding_spaces + item_str


if __name__ == "__main__":
    attr = "我是一个名字相当长的属性名称"
    # attrs = split_tail_item(attr, "x2", 45.8, max_total_width=10, space_alpha=1)
    # print(attrs)
    # attrs = split_head_item(attr, "x3", 45.8, max_total_width=10, space_alpha=1.5)
    # print(attrs)

    dish_name = right_align_item("x3", 45.8, max_total_width=10, space_alpha=1.5)
    print(dish_name)

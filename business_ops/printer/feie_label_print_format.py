



"""
    未完成，仅作参考 ---- 陈波
"""







# import re
# import requests
# import time
# import hashlib
#
#
# class FeieLabelPrintFormat:
#
#     __chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】《》]")
#     # 40mm宽度标签纸一行占用26个字符，50mm宽度标签纸请改成32个字符
#     # 即：40mm宽 使用font=12（GB码）一行可以打26个字母或者13个汉字
#     #    50mm宽 使用font=12（GB码）一行可以打32个字母或者16个汉字
#     # 排版控制中的 x，y值需要按照实际使用的纸规格大小取值
#     #   1mm=8dots；例如标签纸大小为：宽40mm，高度为30mm。x的最大值就是40*8=320，x的取值范围是0-320。y的最大值就是为30*8=240，y的取值范围是0-240。
#
#     def __init__(self):
#         pass
#
#     def align_center(self, paper, setting, content):
#         """
#             居中
#         """
#         ascii_count = self.__calculate_total_char_count(setting, content)
#         if paper['width'] == 40:
#             x = int((40 * 8 - (40/26) * ascii_count * 8)/2)
#         elif paper['width'] == 50:
#             x = int((50 * 8 - (50/32) * ascii_count * 8)/2)
#         body = '<TEXT x="{}" y="110" font="12" w="{}" h="{}" r="0">{}</TEXT>'.format(x, setting['width_ratio'], setting['height_ratio'], content)
#         return body
#
#     def align_right(self, paper, setting, content):
#         """
#             靠右
#         """
#         ascii_count = self.__calculate_total_char_count(setting, content)
#         if paper['width'] == 40:
#             x = int((40 * 8 - (40/26) * ascii_count * 8))
#         elif paper['width'] == 50:
#             x = int((50 * 8 - (50/32) * ascii_count * 8))
#         body = '<TEXT x="{}" y="210" font="12" w="{}" h="{}" r="0">{}</TEXT>'.format(x, setting['width_ratio'], setting['height_ratio'], content)
#         return body
#
#     def both_sides(self, paper, setting, content1, content2):
#         """
#             内容放两侧
#         """
#         ascii_count1 = self.__calculate_total_char_count(setting, content1)
#         ascii_count2 = self.__calculate_total_char_count(setting, content2)
#         ascii_count = ascii_count1 + ascii_count2
#         if paper['width'] == 40:
#             x = int(((40 - (40/26) * ascii_count) / (40/26)))
#             print(ascii_count)
#             print(x)
#             content = content1 + " " * x + content2
#         elif paper['width'] == 50:
#             x = int(((50*8 - (50/26)*8 * ascii_count) / (50/26)))
#             content = content1 + " " * x + content2
#         body = '<TEXT x="10" y="10" font="12" w="{}" h="{}" r="0">{}</TEXT>'.format(setting['width_ratio'], setting['height_ratio'], content)
#         return body
#
#     def chinese_count(self, string):
#         """ 计算中文字符的个数
#         """
#         string = str(string)
#         chinese_array = self.__chinese_re_pattern.findall(string)
#         return len(chinese_array)
#
#     def word_count(self, string):
#         """ 计算字符的总个数
#         """
#         string = str(string)
#         cn_count = self.chinese_count(string)
#         total_count = len(string)
#         en_count = total_count - cn_count
#         return cn_count, en_count
#
#     def __calculate_total_char_count(self, config, content):
#         """ 返回content在 formater 格式下应该占用多少个ASCII字符数
#         """
#         width_ratio = config['width_ratio']
#         # if config['enable_bold']:
#         #     width_ratio = 2
#         cn_count, en_count = self.word_count(content)
#         cn_char_number = cn_count * 2 * width_ratio
#         en_char_number = en_count * width_ratio
#         total_char_count = cn_char_number + en_char_number
#         return total_char_count
#
#     def print_label(self, printer, format, order):
#         header = self.__build_header()
#         dish =
#         price =
#
#
# if __name__ == '__main__':
#     feie_label = FeieLabelPrintFormat()
#     paper = {
#         'width': 40,
#         'height': 30,
#     }
#     setting = {
#         'width_ratio': 2,
#         'height_ratio': 2,
#         'enable_bold': True
#     }
#     content = feie_label.both_sides(paper, setting, "时来收银", "[1/2]")
#     content += feie_label.align_center(paper, setting, "测试菜品")
#     content += feie_label.align_right(paper, setting, "谢谢惠顾")
#     print(content)
#     STIME = str(int(time.time()))
#     signature = hashlib.sha1(str("<EMAIL>" + "CPLf5uYyNMInmInX" + STIME).encode('utf-8')).hexdigest()
#     print(signature)
#     params = {
#         'user': "<EMAIL>",
#         'sig': signature,
#         'stime': STIME,
#         'apiname': 'Open_printLabelMsg',
#         'sn': "960219468",
#         'content': content,
#         'times': "1"  # 打印联数
#     }
#     response = requests.post(url="http://api.feieyun.cn/Api/Open/", data=params).json()
#     print(response)
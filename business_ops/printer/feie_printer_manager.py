# -*- coding: utf-8 -*-
import json
import urllib3

import logging
import hashlib
import time
import requests
import re

from common.config import config as app_config
from business_ops.ordering.constants import FeiePrinter, XPRINTER
from business_ops.base_manager import BaseManager
from business_ops.printer.feie_kitchen_print_format import FeieKitchenPrintFormat
from business_ops.printer.feie_checkout_print_format import FeieCheckoutPrintFormat
import proto.ordering.registration_pb2 as registration_pb
from common.base_app import record_cpu_cost_from_name

logger = logging.getLogger(__name__)


class FeiePrinterManager(BaseManager):
    __chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】]")

    @record_cpu_cost_from_name(name="FeiePrinterManager.__init__")
    def __init__(self, *args, **kargs):
        super(FeiePrinterManager, self).__init__(*args, **kargs)
        self.contents = []
        # 飞鹅 打印机一次打印字符上限为 5000个字节
        self.__bytes_max_len = 3000
        self._is_refund = False

    @property
    def is_refund(self):
        return self._is_refund

    @is_refund.setter
    def is_refund(self, k):
        self._is_refund = k

    @record_cpu_cost_from_name(name="FeiePrinterManager.kitchen_print_create_order")
    def kitchen_print_create_order(self, order, table):
        kitchen_print_format = FeieKitchenPrintFormat(merchant=self.merchant)
        kitchen_print_format.is_refund = self.is_refund
        contents = kitchen_print_format.kitchen_print_create_order(order, table)
        self.contents.extend(contents)

    @record_cpu_cost_from_name(name="FeiePrinterManager.reception_print_create_order")
    def reception_print_create_order(self, transaction, order, table):
        checkout_print_format = FeieCheckoutPrintFormat(merchant=self.merchant)
        checkout_print_format.is_refund = self.is_refund
        contents = checkout_print_format.reception_print_create_order(transaction, order, table)
        self.contents.extend(contents)

    @record_cpu_cost_from_name(name="FeiePrinterManager.print")
    def print(self, choice_type=None, print_times=None, refund_print=False):
        cs = {}
        sn_config = {}
        sn_times = {}
        for content in self.contents:
            sn = content.get("sn")
            if refund_print:
                if not content['config'].enable_kitchen_print:
                    continue
                if not content['config'].kitchen_print_format.refund_print:
                    continue
            cut = content.get("cut")
            one_dish_cut = content.get("one_dish_cut")
            sn_contents = cs.get(sn, [])
            content0, content1 = None, None
            sn_config.update({sn: content.get("config")})
            config = content.get("config")
            if not config or config.printer_type == 'tag':
                continue

            # 组装为一个整单
            if one_dish_cut:
                content0 = self.__print_one_dish_cut(content)
            else:
                content1 = self.__print_dishes_one_paper(content)
            # 当前整单和历史票据单分割开（添加切刀）
            if len(sn_contents) > 0 and (content0 or content1):
                sn_contents[-1] += cut
            # 添加当前整单到打印机
            if content0:
                sn_contents.extend(content0)
            if content1:
                sn_contents.extend(content1)

            if choice_type == "kitchen":
                try:
                    times = config.kitchen_print_format.print_times
                except Exception as e:
                    times = config.print_times
                    logger.error(f'kitchen_print_format.print_times error, error_msg={e}')
            elif choice_type == "checkout":
                try:
                    times = config.checkout_print_format.print_times
                except Exception as e:
                    times = config.print_times
                    logger.error(f'kitchen_print_format.print_times error, error_msg={e}')
            else:
                times = config.print_times

            sn_times[sn] = print_times or times
            cs[sn] = sn_contents

        if not cs:
            return

        if app_config.DEPLOYMENT_ENV != "prod":
            self._test_print(cs)
            # raise

        for sn, contents in cs.items():
            content = "".join(contents)
            # 如果要打印的内容的总长度没有超过飞鹅的限制,那么一次打印
            if len(content) < self.__bytes_max_len:
                config = sn_config.get(sn)
                if config.type == registration_pb.PrinterConfigByType.Type.FEIE:
                    self.call_print(sn, content, times=sn_times[sn])
                else:
                    self.call_xprint(sn, content, times=sn_times[sn])
            else:
                # 如果要打印的内容总长度超过了飞鹅的限制,那么分开打印
                for content in contents:
                    config = sn_config.get(sn)
                    if config.type == registration_pb.PrinterConfigByType.Type.FEIE:
                        self.call_print(sn, content, times=sn_times[sn])
                    else:
                        self.call_xprint(sn, content, times=sn_times[sn])

    def __print_one_dish_cut(self, content):
        contents = content.get("contents")
        header = content.get("header")
        tail = content.get("tail")
        dish_header = content.get("dish_header")
        seperate_line = content.get("seperate_line")
        cut = content.get("cut")
        ret = []
        c = ""
        for index, content in enumerate(contents):
            if content.endswith(seperate_line):
                content = content[:-len(seperate_line)]
            current_c = header + dish_header + content + seperate_line + tail
            new_c = current_c + c
            if c and len(new_c) >= self.__bytes_max_len:
                c = c.rstrip(cut)
                ret.append(c)
                c = current_c
            else:
                c += current_c
            if index < len(contents) - 1:
                c += cut
        ret.append(c)
        return ret

    def __print_dishes_one_paper(self, content):
        contents = content.get("contents")
        header = content.get("header")
        tail = content.get("tail")
        dish_header = content.get("dish_header")
        seperate_line = content.get("seperate_line")
        c = "".join(contents)
        if c.endswith(seperate_line):
            c = c[:-len(seperate_line)]
        c = header + dish_header + c + seperate_line + tail
        return [c]

    def call_print(self, printer_sn, content, times=None):
        """调用飞鹅的服务"""
        if content == "":
            return
        if times is None or times == 0:
            times = 1
        STIME = str(int(time.time()))
        signature = hashlib.sha1(str(FeiePrinter.USER + FeiePrinter.UKEY + STIME).encode('utf-8')).hexdigest()
        params = {
            'user': FeiePrinter.USER,
            'sig': signature,
            'stime': STIME,
            'apiname': 'Open_printMsg',
            'sn': printer_sn,
            'content': content,
            'times': str(times),  # 打印联数
        }
        url = FeiePrinter.URL
        logger.info(f"飞鹅打印机请求: printer_sn={printer_sn}, url={url}, data: {params}")
        return self._send_request(
            url=url,
            printer_sn=printer_sn,
            data=params,
            suffix="飞鹅"
        )

    def call_xprint(self, printer_sn, content, times=None):
        """调用芯烨打印机的服务"""
        if content == "":
            return
        if times is None or times == 0:
            times = 1
        STIME = str(int(time.time()))
        signature = hashlib.sha1(str(XPRINTER.USER + XPRINTER.UKEY + STIME).encode('utf-8')).hexdigest()
        params = {
            'user': XPRINTER.USER,
            'sign': signature,
            'timestamp': STIME,
            'sn': printer_sn,
            'content': content + "<L>",
            'copies': str(times),  # 打印联数
            'mode': 1,
            'expiresIn': 43200,
        }
        url = XPRINTER.URL + "print"
        logger.info(f"芯烨打印机请求: printer_sn={printer_sn}, url={url}, data: {params}")
        return self._send_request(
            url=url,
            printer_sn=printer_sn,
            data=json.dumps(params),
            headers={"Content-Type": "application/json;charset=UTF-8"},
            suffix="芯烨"
        )

    def _send_request(self, url, printer_sn, data, suffix="", **kwargs):
        try:
            response = requests.post(
                url,
                data=data,
                timeout=30,
                **kwargs
            )
            result = response.json()
            logger.info(f"{suffix}打印机响应: printer_sn={printer_sn}, response={result}")
            return result
        except requests.exceptions.ReadTimeout as ex:
            logger.info(f"{suffix}打印机响应ReadTimeout报错: printer_sn={printer_sn}, error_msg={ex}")
        except urllib3.exceptions.ConnectTimeoutError as ex:
            logger.info(f"{suffix}打印机响应ConnectTimeoutError报错: printer_sn={printer_sn}, error_msg={ex}")
        except Exception as ex:
            logger.info(f"{suffix}打印机响应报错: printer_sn={printer_sn}, error_msg={ex}")

    def _test_print(self, cs):
        for sn, c in cs.items():
            for row in c:
                print('\n\n')
                print(f"打印机：{sn}")
                print('=' * 100)
                for cut in row.split('<CUT>'):
                    for out in cut.split('<BR>'):
                        print(out)
                    print('<CUT>', '>' * 50)
                    print()
# -*- coding: utf-8 -*-

import re
from functools import reduce
from typing import List, Dict
from datetime import datetime, timedelta
from collections import defaultdict

import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.ordering.dish_pb2 as dish_pb
from common.protobuf_transformer import copy_obj
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.printer_config_da_helper import PrinterConfigDataAccessHelper
from dao.ordering.table_area_da_helper import TableAreaDaHelper
from dao.config_da_helper import ConfigDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors, error_codes
from common.base_app import record_cpu_cost
from cache.printer_cache import PrinterDishCache
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper

# from business_ops.printer.format_helper import right_align_item
from business_ops.printer.base_cmd import CMD_MAP


class ProductInfo(object):
    __solt__ = (
        "index",  # 菜品下标
        "printer_configs",  # 打印机配置
        "printer_content",  # 打印机内容
        "product",  # 菜品
        "meal_type",  # 用餐方式
        "price_max_len",
        "is_sub_product",  # 是否子菜
        "printer_format",
        "printer_combo_header",
        "printer_combo_header_nums",
    )

    def __init__(
        self,
        product: dish_pb.Product,
        meal_type: int,
        printer_configs: dict = None,
        printer_format: dict = None,
        printer_content: dict = None,
        index: int = None,
        is_sub_product: bool = False,
        price_max_len: int = None,
        printer_combo_header: str = "",
        printer_combo_header_nums: int = 1,
    ):
        self.printer_configs = printer_configs or dict()
        self.printer_format = printer_format or dict()
        self.printer_content = printer_content or defaultdict(lambda: [])
        self.product = product
        self.index = index
        self.meal_type = meal_type
        self.price_max_len = price_max_len
        self.is_sub_product = is_sub_product
        self.printer_combo_header = printer_combo_header
        self.printer_combo_header_nums = printer_combo_header_nums

    def copy(self):
        return ProductInfo(
            product=copy_obj(self.product, dish_pb.Product),
            meal_type=self.meal_type,
            printer_configs=self.printer_configs,
            printer_format=self.printer_format,
            printer_content=self.printer_content,
            index=self.index,
            is_sub_product=self.is_sub_product,
            price_max_len=self.price_max_len,
            printer_combo_header=self.printer_combo_header,
            printer_combo_header_nums=self.printer_combo_header_nums,
        )


class ProductNode(object):
    __solt__ = (
        "name",  # 菜品名称
        "product",  # 菜品
        "sub_product",  # 子菜
    )

    def __init__(self, name, product: ProductInfo, sub_product: List[ProductInfo] = None):
        self.name = name
        self.product = product
        self.sub_product = sub_product or []


class DefaultPrintFormt(object):
    def set_merchant_printer_config(self, merchant):
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(merchant_id=merchant.id)
        printer_config_da = PrinterConfigDataAccessHelper()
        printer_configs = printer_config_da.get_printer_configs(merchant_id=merchant.id)
        default_printer_sn = registration_info.printer_config.feie_printer.printer_sn
        has_default_printer_sn = False
        for config in printer_configs:
            if config.printer_sn == default_printer_sn:
                has_default_printer_sn = True
            self.set_default_config(config, registration_info)
            self.__set_compatibility_config(config, registration_info)
            printer_config_da.add_or_update_printer_config(config)

        # 默认打印机设置
        if not has_default_printer_sn:
            self.new_printer_config(default_printer_sn, registration_info)

    def new_printer_config(self, sn, registration_info, format_type=None):
        config = registration_pb.PrinterConfigByType()
        config.merchant_id = registration_info.merchant_id
        config.specification = registration_info.printer_config.feie_printer.printer_specification
        config.printer_sn = sn
        if sn in registration_info.printer_config.feie_printer.tag_printer_sns:
            config.printer_type = "tag"
        else:
            config.printer_type = "receipt"
        self.set_default_config(config, registration_info)
        self.__set_compatibility_config(config, registration_info)
        self.__set_format_type_config(config, format_type)
        printer_config_da = PrinterConfigDataAccessHelper()
        printer_config_da.add_or_update_printer_config(config)

    def delete_printer_config(self, sn, registration_info):
        matcher = {"merchantId": registration_info.merchant_id, "printerSn": sn}
        printer_config_da = PrinterConfigDataAccessHelper()
        printer_config_da.delete_printer_config(matcher)

    def set_default_config(self, config, registration_info):
        if config.specification == "58mm":
            self.set_58_checkout_default_config(config)
            self.set_58_kitchen_default_config(config)
        else:
            config.specification = "80mm"
            self.set_80_checkout_default_config(config)
            self.set_80_kitchen_default_config(config)
        config.checkout_print_format.title.item = "结账单"

    def __set_format_type_config(self, config, format_type):
        if not format_type:
            return
        if isinstance(format_type, str):
            format_type = registration_pb.PrinterConfigByType.Type.Value(format_type)
        config.type = format_type

    def __set_compatibility_config(self, config, registration_info):
        feie_printer = registration_info.printer_config.feie_printer
        config.one_dish_cut = feie_printer.one_dish_cut
        config.enable_checkout_print = feie_printer.merchant_print
        config.enable_kitchen_print = feie_printer.kitchen_print

    def __set_line_default_print_format(
        self,
        config,
        item="",
        item_position=registration_pb.LinePrintFormat.LEFT_ALIGN,
        enable_seperate_line=True,
        typography=registration_pb.LinePrintFormat.ONE_BY_ONE,
        enable_bold=True,
        disable_display=False,
    ):
        config.item = item
        config.item_position = item_position
        config.enable_seperate_line = enable_seperate_line
        config.typography = typography
        config.enable_bold = enable_bold
        config.disable_display = disable_display

    def __set_list_default_print_format(self, config):
        config.header.enable_bold = False
        config.header.disable_seperate_line = False
        config.header.disable_display = False
        config.body.enable_bold = True
        config.body.disable_seperate_line = False
        config.body.disable_number = False
        config.body.disable_price = True

    def __set_order_comment_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="整单备注:")
        config.enable_bold = True

    def __set_pay_time_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="结账时间:")
        config.enable_seperate_line = False
        config.enable_bold = False

    def __set_serial_number_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="流水号:")
        config.enable_seperate_line = False
        config.enable_bold = True
        config.disable_display = False

    def __set_table_name_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="桌号:")
        config.enable_seperate_line = False
        config.enable_bold = True

    def __set_pay_method_default_print_format(self, config):
        self.__set_line_default_print_format(
            config,
            item="支付方式:",
            disable_display=True,
            enable_bold=True,
            enable_seperate_line=False,
        )

    def __set_take_meal_time_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="取餐时间:")
        config.enable_seperate_line = False
        config.enable_bold = False

    def __set_title_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="后厨单")
        config.enable_seperate_line = True
        config.enable_bold = True
        config.item_position = registration_pb.LinePrintFormat.CENTER

    def __set_meal_code_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="取餐号:")
        config.enable_seperate_line = False
        config.enable_bold = False

    def __set_kitchen_dish_bill_fee_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="")
        config.typography = registration_pb.LinePrintFormat.ONE_BY_ONE
        config.item_position = registration_pb.LinePrintFormat.LEFT_ALIGN
        config.disable_display = True

    def __set_dish_bill_fee_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="菜品总价:")
        config.typography = registration_pb.LinePrintFormat.BEGIN_AND_END

    def __set_total_paid_fee_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="实付金额:")
        config.typography = registration_pb.LinePrintFormat.BEGIN_AND_END

    def __set_packaging_box_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="打包费:")
        config.typography = registration_pb.LinePrintFormat.BEGIN_AND_END

    def __set_shipping_address_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="")

    def __set_font_size_default_print_format(self, config):
        config.dish_size = registration_pb.FeiePrintFontSize.BOLD
        config.attr_size = registration_pb.FeiePrintFontSize.BOLD
        config.addon_size = registration_pb.FeiePrintFontSize.BOLD

    def __set_self_pick_up_info_default_print_format(self, config):
        self.__set_line_default_print_format(config, enable_bold=False)

    def __set_meal_type_default_print_format(self, config):
        self.__set_line_default_print_format(config, item="", item_position=registration_pb.LinePrintFormat.CENTER)

    def set_printer_config_default_config(self, config):
        formaters = [config.checkout_print_format, config.kitchen_print_format]
        for formater in formaters:
            self.__set_order_comment_default_print_format(formater.order_comment)
            self.__set_pay_time_default_print_format(formater.pay_time)
            self.__set_serial_number_default_print_format(formater.serial_number)
            self.__set_table_name_default_print_format(formater.table_name)
            self.__set_take_meal_time_default_print_format(formater.take_meal_time)
            self.__set_title_default_print_format(formater.title)
            self.__set_meal_code_default_print_format(formater.meal_code)
            self.__set_list_default_print_format(formater.dishes_info)
            self.__set_font_size_default_print_format(formater)
            self.__set_self_pick_up_info_default_print_format(formater.self_pick_up_info)
            self.__set_meal_type_default_print_format(formater.meal_type)
            self.__set_pay_method_default_print_format(formater.pay_method)
            formater.print_times = 1

    def set_80_kitchen_default_config(self, config):
        self.set_printer_config_default_config(config)
        self.__set_kitchen_dish_bill_fee_default_print_format(config.kitchen_print_format.dish_bill_fee)
        self.__set_shipping_address_default_print_format(config.kitchen_print_format.shipping_address)

    def set_80_checkout_default_config(self, config):
        self.set_printer_config_default_config(config)
        self.__set_dish_bill_fee_default_print_format(config.checkout_print_format.dish_bill_fee)
        self.__set_total_paid_fee_default_print_format(config.checkout_print_format.total_paid_fee)
        self.__set_packaging_box_default_print_format(config.checkout_print_format.packaging_box)
        self.__set_shipping_address_default_print_format(config.checkout_print_format.shipping_address)
        config.checkout_print_format.title.item = "结账单"
        config.checkout_print_format.dishes_info.body.enable_bold = False
        config.checkout_print_format.dishes_info.body.disable_price = False

    def set_58_kitchen_default_config(self, config):
        self.set_printer_config_default_config(config)
        self.__set_kitchen_dish_bill_fee_default_print_format(config.kitchen_print_format.dish_bill_fee)
        self.__set_shipping_address_default_print_format(config.kitchen_print_format.shipping_address)

    def set_58_checkout_default_config(self, config):
        self.set_printer_config_default_config(config)
        self.__set_dish_bill_fee_default_print_format(config.checkout_print_format.dish_bill_fee)
        self.__set_total_paid_fee_default_print_format(config.checkout_print_format.total_paid_fee)
        self.__set_packaging_box_default_print_format(config.checkout_print_format.packaging_box)
        self.__set_shipping_address_default_print_format(config.checkout_print_format.shipping_address)
        config.checkout_print_format.title.item = "结账单"
        config.checkout_print_format.dishes_info.body.enable_bold = False
        config.checkout_print_format.dishes_info.body.disable_price = False


class FeiePrintFormat(DefaultPrintFormt):
    __chinese_re_pattern = re.compile("[\u4e00-\u9fa5（）【】《》]")
    KITCHEN = 0x01
    CHECKOUT = 0x02

    DISH_FONT_SIZE = 0x01
    ATTR_FONT_SIZE = 0x02
    ADDON_FONT_SIZE = 0x03

    _PACKAGE_BLACK_LIST = [
        "b3c07627898b47fbb8e1c8a1e00ed01e",
        "3162121b5dd047549adc8258201cdbbb",
        "76a5d6d43ac64edeaba8c8e3e178cc6e",
        "a155f668fedb426f9a4844a17c45701a",
        "ff3ae5c893f0400590711d5334270dab",
        "a8c16a1aefff4cd9bc6f1cbf7246cf82",
        "a155f668fedb426f9a4844a17c45701a",
        "c764e7c4a9744991ac36e2a3fd8fd251",
        "e752235cd48a42f096c2a1f7a5c82070",
        "935f6da2e62841d3ad3e47ac140a87dc",
        "d5709f104e40415d890162bb8acd7cfc",
        "a2130dff677c4f40b7e868960cc49c75",
        "f844b5078abf47d6af545edc5576f492",
        "19185aaed57f4226824cbd1e29918ad8",
        "08995a6585c64329bb8873768952c0d9",
        "6d414be0ebf64fc6bf8c24c54c45aeb7",
        "1ab0957edbd94c8e8835804cc9b02b73",
        "11f6615070314a7a8c9df085ffbfdad6",
        "f5689d02120c411abc1ddff7b6f902d9",
        "b073bebd9af14ae6b154876ac1210ced",
        "a0bb4e1b4ffc403191cddcb51354a9cd",
    ]

    def __init__(self, *args, **kargs):
        self._combo_meal_prefix = "[套]"
        self._package_prefix = "(打包)"
        # 套餐子菜前缀
        self._sub_child_prefix = "-"
        # 加料前缀
        self._addon_prefix = "  ["
        # 加料后缀
        self._addon_suffix = "]"
        self._attr_prefix = "  ["
        self._attr_suffix = "]"
        self._new_line = "<BR>"
        self.contents = {}
        self.printer_config_papers = {}
        self.merchant = kargs.get("merchant")
        self.ordering_config = None
        if self.merchant:
            # 菜品分类-打印机映射 {"category_id": sns}
            self.dish_category_id_map_sns = self.__map_dish_category_id_to_sns()
            # 打印机-配置映射 {"sn": config}
            self.printer_configs, self.printer_config_papers = self.__map_sn_to_printer_config()
            self.dish_id_map_sns = self.__map_dish_id_to_sns(merchant_id=self.merchant.id)
            self.ordering_config = ConfigDataAccessHelper().get_ordering_config(merchant_id=self.merchant.id)
        # 菜品价格最多占用6个字符
        self._price_max_len = 6
        # 退菜单时菜品前缀
        self._is_refund = False
        self._refund_prefix = "(退)"
        self.__init_table_area()
        self._product_handler = ProductHandler(self)

        from .feie_printer_manager import logger

        self.logger = logger

    def __init_table_area(self):
        self.table_printer_map = {}
        if self.merchant is None:
            return
        table_area_da = TableAreaDaHelper()
        areas = table_area_da.list_table_areas(self.merchant.id, status=dish_pb.TableArea.Status.ACTIVE)
        for area in areas:
            self.table_printer_map.update({"exists": True})
            for table_id in area.table_ids:
                for printer_sn in area.printer_sns:
                    key = f"{table_id}_{printer_sn}"
                    self.table_printer_map.update({key: True})

    def __map_sn_to_printer_config(self):
        """把打印机sn与打印机配置做映射"""
        printer_config_da = PrinterConfigDataAccessHelper()
        printer_configs = printer_config_da.get_printer_configs(merchant_id=self.merchant.id)
        printer_config_papers = {p.printer_sn: PaperInfo(p) for p in printer_configs}
        printer_configs = {p.printer_sn: p for p in printer_configs}
        return printer_configs, printer_config_papers

    @PrinterDishCache.cache
    def __map_dish_id_to_sns(self, merchant_id):
        """把菜品ID映射到打印机sn"""
        dish_map_sns, dishes = defaultdict(lambda: []), None
        for sn, config in self.printer_configs.items():
            bind_dish = config.bind_dish
            if not bind_dish.disable_auto_bind:
                if dishes is None:
                    ordering_da = OrderingServiceDataAccessHelper()
                    dishes = list(ordering_da.get_dishes(merchant_id=merchant_id, return_proto=False))
                for dish in dishes:
                    dish_map_sns[dish["id"]].append(sn)
            else:
                for dish_id in bind_dish.dish_ids:
                    dish_map_sns[dish_id].append(sn)
        return dish_map_sns

    def __map_dish_category_id_to_sns(self):
        """把菜品分类ID映射到打印机sn"""
        ordering_da = OrderingServiceDataAccessHelper()
        dish_category_printers = ordering_da.get_dish_category_printer_configs(merchant_id=self.merchant.id)
        dish_category_id_map_sns = defaultdict(lambda: [])
        for _printer in dish_category_printers:
            dish_category_id_map_sns[_printer.dish_category_id].extend(_printer.printer_sns)
        return dish_category_id_map_sns

    @property
    def is_refund(self):
        return self._is_refund

    @is_refund.setter
    def is_refund(self, p):
        self._is_refund = p

    def __build_product_content(self, product_info: ProductInfo, config, cmd_obj=None):
        """构建单个菜品的打印格式"""
        if cmd_obj is None:
            cmd_obj = self.get_cmd_obj(config)
        formater = self.get_formater(config)
        body_formater = formater.dishes_info.body
        if formater.dish_size != registration_pb.BOLD:
            body_formater.enable_bold = False
        else:
            body_formater.enable_bold = True
        product, price_max_len = product_info.product, product_info.price_max_len

        # --------- 第1行 ---------
        contents, other_contents = [], []
        # 1、处理菜品名
        name = product.name
        if product.type == dish_pb.Dish.COMBO_MEAL:
            name = f"{self._combo_meal_prefix}{name}"
        if product_info.meal_type == dish_pb.DishOrder.TAKE_AWAY and not body_formater.disable_package:
            try:
                if self.merchant.id not in self._PACKAGE_BLACK_LIST:
                    name = f"{self._package_prefix}{name}"
            except:
                name = f"{self._package_prefix}{name}"
        if product_info.is_sub_product:
            name = f"-{name}"
        spec_name = self.__get_dish_specification_name(product)
        if spec_name:
            name = f"{name}({spec_name})"
        if self.is_refund:
            name = f"{self._refund_prefix}{name}"
        if formater.enable_dish_name_index and product_info.index is not None:
            name = f"{product_info.index}.{name}"
        # 计算换行
        paper = self.printer_config_papers.get(config.printer_sn)
        if body_formater.disable_price:
            price_max_len = 0
        dish_names = self.__multi_line(name, config=body_formater, paper=paper, price_max_len=price_max_len)
        # 格式对齐（空格填补）
        if body_formater.enable_quantity_new_line:
            contents.append("".join(dish_names))
        else:
            first_dish_name = dish_names[0]
            dish_name_first_count = self.__calculate_total_char_count(body_formater, first_dish_name)
            body_format_count = paper.get_first_part_occupy(body_formater, price_max_len=price_max_len)
            if dish_name_first_count < body_format_count:
                first_dish_name = self.__after_spaces(
                    first_dish_name,
                    body_formater,
                    body_format_count if body_formater.enable_bold else body_format_count - 1,
                    dish_name_first_count,
                )
            contents.append(first_dish_name)

        # 2、处理菜品数量
        if not body_formater.disable_number:
            quantity = self.__format_quantity_to_str(formater, product.quantity)
            quantity_bytes_count = self.__calculate_total_char_count(body_formater, quantity)
            quantity_occupy_bytes_count = paper.get_quantity_occupy_bytes_count(body_formater)
            quantity = self.__after_spaces(quantity, body_formater, quantity_occupy_bytes_count, quantity_bytes_count)
            if body_formater.enable_quantity_new_line:
                other_contents.append(quantity)
            else:
                contents.append(quantity)

        # 3、处理菜品总价
        if not body_formater.disable_price and not product_info.is_sub_product:
            total_fee = product.total_fee
            total_fee += self.get_supply_condiments_total_fee(product)
            if formater.enable_item_split:
                total_fee /= product.quantity
            price = self.__format_price_to_str(total_fee)
            if body_formater.enable_quantity_new_line:
                other_contents.append(price)
            else:
                contents.append(price)

        # 4、组装第1-2行
        space = " " if body_formater.enable_bold else "   "
        out_content = contents[0]
        if not body_formater.enable_quantity_new_line:
            if len(contents) > 1:
                out_content += space + space.join(contents[1:])
            if len(dish_names) > 1:
                out_content += ''.join([self._new_line, *dish_names[1:]])

        # --------- 第3行 ---------
        # 5、处理菜品属性、加料
        out_content += self._new_line
        out_content = self.__set_font_size(out_content, config, body_formater, formater, self.DISH_FONT_SIZE)
        out_content += self.__build_attrs_content(product, config, body_formater, formater)
        out_content += self.__build_addon_content(product, config, body_formater, formater)
        if body_formater.enable_quantity_new_line and other_contents:
            other_content = cmd_obj.align_right(" ".join(other_contents))
            out_content += self.__set_font_size(other_content, config, body_formater, formater, self.DISH_FONT_SIZE)
            out_content += self._new_line

        # 6、处理菜品显示样式
        if body_formater.enable_bold:
            out_content = f"<B>{out_content}</B>"
        if body_formater.disable_seperate_line:
            out_content += self.add_seperate_line(config, prefix_new_line=False)
        out_content = [out_content]
        if formater.enable_item_split:
            out_content *= int(product.quantity)
        return out_content

    def get_formater(self, config):
        if self._type == self.CHECKOUT:
            return config.checkout_print_format
        elif self._type == self.KITCHEN:
            return config.kitchen_print_format

    def get_cmd_obj(self, config):
        cls = CMD_MAP.get(config.type)
        if cls:
            return cls()
        self.logger.error(f"未获取到打印机类型，如：飞鹅/芯烨, type={config.type}")

    def __get_line_print_format(self, config, paper, contents=[]):
        """单行打印格式"""
        content = ""
        if config.disable_display:
            return content
        # 如果是居中,contents必须为空,只需要把config.item居中显示就行了
        if config.item_position == registration_pb.LinePrintFormat.CENTER:
            content = self.__center_content(config.item)
        elif config.item_position == registration_pb.LinePrintFormat.LEFT_ALIGN:
            if config.typography == registration_pb.LinePrintFormat.ONE_BY_ONE:
                content = config.item
                for c in contents:
                    if content == "":
                        content = c
                    else:
                        content = content + " " + str(c)
            elif config.typography == registration_pb.LinePrintFormat.BEGIN_AND_END:
                content = self.__begin_and_end_contents(config.item, contents[0], config, paper)
            elif config.typography == registration_pb.LinePrintFormat.UNIFORM:
                c = [config.item]
                c.extend(contents)
                content = self.__uniform_contents(c, config, paper)
        elif config.item_position == registration_pb.LinePrintFormat.RIGHT_ALIGN:
            pass
        if config.enable_bold:
            content = f"<B>{content}</B>"
        content += self._new_line
        if config.enable_seperate_line:
            content += paper.seperate_line
            content += self._new_line
        return content

    def add_seperate_line(self, config, prefix_new_line=True, suffix_new_line=True):
        """添加分隔符
        :params: config: 打印机设置
        :params: prefix_new_line: 是否增加前缀换行符
        :params: suffix_new_line: 是否增加后缀换行符
        """
        paper = self.printer_config_papers.get(config.printer_sn)
        seperate_line = paper.seperate_line
        if prefix_new_line and suffix_new_line:
            seperate_line = self._new_line + seperate_line + self._new_line
        elif suffix_new_line:
            seperate_line = seperate_line + self._new_line
        elif prefix_new_line:
            seperate_line = self._new_line + seperate_line
        return seperate_line

    def build_title_content(self, config):
        """一张单的标题"""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        if self.is_refund:
            formater.title.item = f"{self._refund_prefix}{formater.title.item}"
        content = self.__get_line_print_format(formater.title, paper)
        return content

    def build_meal_type_name(self, config, order):
        """设置取餐方式"""
        meal_type_map = {
            dish_pb.DishOrder.EAT_IN: "堂食",
            dish_pb.DishOrder.TAKE_AWAY: "打包",
            dish_pb.DishOrder.SELF_PICK_UP: "自提",
            dish_pb.DishOrder.TAKE_OUT: "外卖",
        }
        if order.meal_type not in meal_type_map.keys():
            return ""
        formater = self.get_formater(config)
        if not formater.HasField("meal_type"):
            return ""

        meal_type = order.meal_type
        if order.meal_type == dish_pb.DishOrder.SELF_PICK_UP:
            ordering_da = OrderingServiceDataAccessHelper()
            registration_info = ordering_da.get_registration_info(merchant_id=order.merchant_id)
            if registration_info.is_pre_order:
                meal_type = dish_pb.DishOrder.TAKE_AWAY
        meal_type = f"【{meal_type_map[meal_type]}】"
        paper = self.printer_config_papers.get(config.printer_sn)
        formater.meal_type.item = meal_type
        content = self.__get_line_print_format(formater.meal_type, paper)
        return content

    def __get_dish_specification_name(self, product):
        """菜品规格名"""
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.SPECIFICATION:
                return attr.name
        return None

    def __build_attrs_content(self, product, config, body_formater, full_config):
        """菜品属性格式"""
        contents = []
        if len(product.attrs) == 0:
            return ""
        for attr in product.attrs:
            if attr.type in (dish_pb.Attr.TAKE_AWAY, dish_pb.Attr.SPECIFICATION):
                continue
            content = f"{attr.name}"
            if full_config.enable_attrs_line_feed:
                if full_config.attr_prefix != "":
                    content = f"{full_config.attr_prefix}{content}"
                else:
                    content = f"  {content}"
            contents.append(content)
        if len(contents) == 0:
            return ""
        if full_config.enable_attrs_line_feed:
            separator = f"{self._new_line}"
            content = separator.join(contents)
        else:
            content = ",".join(contents)
            content = f"{self._attr_prefix}{content}{self._attr_suffix}"
        content = self.__set_font_size(content, config, body_formater, full_config, self.ATTR_FONT_SIZE)
        content += self._new_line
        return content

    def __build_addon_content(self, product, config, body_formater, full_config):
        """菜品加料格式"""
        if len(product.supply_condiments) == 0:
            return ""
        contents = []
        for sc in product.supply_condiments:
            sc_quantity = sc.quantity
            if full_config.enable_item_split:
                sc_quantity = int(sc.quantity) / int(product.quantity)
            content = f"{sc.name}x{sc_quantity}"
            if full_config.enable_addons_line_feed:
                if full_config.addon_prefix != "":
                    content = f"{full_config.addon_prefix}{content}"
                else:
                    content = f"  {content}"
            contents.append(content)
        if full_config.enable_addons_line_feed:
            separator = f"{self._new_line}"
            content = separator.join(contents)
        else:
            content = ",".join(contents)
            content = f"{self._addon_prefix}{content}{self._addon_suffix}"
        content = self.__set_font_size(content, config, body_formater, full_config, self.ADDON_FONT_SIZE)
        content += self._new_line
        return content

    def __set_font_size(self, content, config, body_formater, full_config, type=None):
        """
        设置字体大小，支持动态调整字体大小。

        @param content: 需要设置字体大小的内容
        @param config: 打印机配置
        @param body_formater: 打印格式配置
        @param full_config: 完整的打印配置
        @param type: 字体类型（DISH_FONT_SIZE, ATTR_FONT_SIZE, ADDON_FONT_SIZE）
        @return: 设置后的内容

        目前一共有三种字体需要设置字体大小：
        1. DISH_FONT_SIZE: 菜品字体大小
        2. ATTR_FONT_SIZE: 属性字体大小
        3. ADDON_FONT_SIZE: 加料字体大小
        """
        if type is None:
            type = self.DISH_FONT_SIZE
        if type == self.DISH_FONT_SIZE:
            return self.__set_child_font_size(content, config, body_formater, full_config.dish_size)
        elif type == self.ATTR_FONT_SIZE:
            return self.__set_child_font_size(content, config, body_formater, full_config.attr_size)
        elif type == self.ADDON_FONT_SIZE:
            return self.__set_child_font_size(content, config, body_formater, full_config.addon_size)
        return content

    def __set_child_font_size(self, content, config, body_formater, size_type):
        """设置子项的字体大小
        size_type: 字体的大小配置类型
        """
        if size_type == registration_pb.SAME_AS_DISH:
            body_formater.enable_bold = False
        elif size_type == registration_pb.TWO_TIMES_HEIGHT:
            if config.type == registration_pb.PrinterConfigByType.Type.FEIE:
                content = f"<L>{content}</L>"
            else:
                content = f"<HB>{content}</HB>"
        elif size_type == registration_pb.TWO_TIMES_WIDTH:
            if config.type == registration_pb.PrinterConfigByType.Type.FEIE:
                content = f"<W>{content}</W>"
            else:
                content = f"<WB>{content}</WB>"
        elif size_type == registration_pb.BOLD:
            content = f"<B>{content}</B>"
        return content

    def _exists_packaging_box_by_order(self, order) -> bool:
        for product in order.products:
            if self._exists_packaging_box_by_product(product):
                return True
        return False

    def _exists_packaging_box_by_product(self, product):
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.AttrType.TAKE_AWAY:
                return True
        return False

    def build_order_meal_code_content(self, config, order):
        """取餐码的打印格式"""
        if order.meal_code == "":
            return ""
        meal_code = str(order.meal_code)
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        content = self.__get_line_print_format(formater.meal_code, paper, contents=[meal_code])
        return content

    def build_pay_method_content(self, config, order):
        """支付方式"""
        if self._type not in [self.CHECKOUT, self.KITCHEN]:
            return ""
        formater = self.get_formater(config)
        if not formater.HasField('pay_method'):
            return ""
        if formater.pay_method.disable_display:
            return ""
        allow_pay_method = {
            wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.FANPIAO_PAY): "饭票",
            # wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.WALLET): "零钱",
            # wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.WECHAT_PAY): "微信",
            # wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.ALIPAY): "支付宝",
            # wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.SHILAI_MEMBER_CARD_PAY): "储值",
            # wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.PayMethod.KERUYUN_MEMBER_CARD_PAY): "储值",
            # wallet_pb.Transaction.PayChannel.Name(wallet_pb.Transaction.PayChannel.WECHAT_CHANNEL): "微信",
            # wallet_pb.Transaction.PayChannel.Name(wallet_pb.Transaction.PayChannel.ALIPAY_CHANNEL): "支付宝",
        }
        transaction = TransactionDataAccessHelper().get_transaction_by_id(order.transaction_id)
        if transaction is None:
            return ""
        pay_method = wallet_pb.Transaction.PayMethod.Name(transaction.pay_method)
        if transaction.pay_method == wallet_pb.Transaction.PayMethod.TIAN_QUE_PAY:
            pay_method = wallet_pb.Transaction.PayChannel.Name(transaction.pay_channel)
        pay_method_name = allow_pay_method.get(pay_method)
        if pay_method_name is None:
            return ""
        return self.__get_line_print_format(
            formater.pay_method, self.printer_config_papers.get(config.printer_sn), contents=[pay_method_name]
        )

    def build_order_comment_content(self, config, order):
        """整单备注的打印格式"""
        comment = order.remark
        if comment == "":
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        content = self.__get_line_print_format(formater.order_comment, paper, contents=[order.remark])
        return content

    def build_self_pick_up_order_content(self, config, order):
        if order.meal_type not in [
            dish_pb.DishOrder.SELF_PICK_UP,
            dish_pb.DishOrder.TAKE_AWAY,
            dish_pb.DishOrder.TAKE_OUT,
            dish_pb.DishOrder.EAT_IN,
        ]:
            return ""
        if self._type not in [self.CHECKOUT, self.KITCHEN]:
            return ""
        if not order.phone and order.meal_type != dish_pb.DishOrder.EAT_IN:
            return ""
        if order.meal_type == dish_pb.DishOrder.EAT_IN:
            ordering_da = OrderingServiceDataAccessHelper()
            registration_info = ordering_da.get_registration_info(merchant_id=order.merchant_id)
            if not registration_info.is_pre_order or not registration_info.is_pre_order_show_meal_code:
                return ""
        formater = self.get_formater(config)
        if not formater.HasField('self_pick_up_info'):
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        appointment_time = order.appointment_time
        appointment_time = datetime.fromtimestamp(appointment_time)
        appointment_time = appointment_time.strftime("%Y-%m-%d %H:%M:%S")
        if order.meal_type in [dish_pb.DishOrder.SELF_PICK_UP, dish_pb.DishOrder.TAKE_OUT, dish_pb.DishOrder.EAT_IN]:
            self_pick_up_order_info = [f"- 预约时间: {appointment_time}", f"- 取餐码: {order.meal_code}", f"- 手机号: {order.phone}"]
        else:
            self_pick_up_order_info = [f"- 取餐码: {order.meal_code}", f"- 手机号: {order.phone}"]
        info = "\n".join(self_pick_up_order_info)
        content = self.__get_line_print_format(formater.self_pick_up_info, paper, contents=[info])
        return content

    def build_shipping_address_content(self, config, order, table):
        if table.meal_type != dish_pb.DishOrder.TAKE_OUT:
            return ""
        if self._type not in [self.CHECKOUT, self.KITCHEN]:
            return ""
        formater = self.get_formater(config)
        if not formater.HasField("shipping_address"):
            return ""
        shipping_address = order.shipping_address
        mobile_phone = shipping_address.mobile_phone
        if not mobile_phone:
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        gender = shipping_address.gender
        ordering_da = OrderingServiceDataAccessHelper()
        registration_info = ordering_da.get_registration_info(merchant_id=self.merchant.id)
        mobile_phone = shipping_address.mobile_phone
        username = shipping_address.username
        if not registration_info.show_full_mobile_phone:
            mobile_phone = re.sub(r"(\d{3})(\d{4})(\d{4})", r"\1****\3", mobile_phone)
        street = shipping_address.street
        house_number = shipping_address.house_number
        address = ["- 配送地址: ", f"{username}({gender})", f"{mobile_phone}", f"{street} {house_number}"]
        address = "\n".join(address)
        content = self.__get_line_print_format(formater.shipping_address, paper, contents=[address])
        return content

    def build_pay_time_content(self, config, order):
        """下单时间打印格式

        根据配置中的字体大小调整支付时间的打印格式。
        """
        pay_time = order.paid_time
        if not pay_time:
            pay_time = datetime.now()
        else:
            pay_time = datetime.utcfromtimestamp(pay_time) + timedelta(hours=8)

        pay_time = pay_time.strftime("%Y-%m-%d %H:%M:%S")
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)

        # 根据配置中的字体大小调整打印格式
        font_size = formater.pay_time.font_size
        if font_size == registration_pb.FeiePrintFontSize.SAME_AS_DISH:
            # 使用与菜品相同的字体大小
            pass
        elif font_size == registration_pb.FeiePrintFontSize.TWO_TIMES_HEIGHT:
            pay_time = f"<L>{pay_time}</L>"
            formater.pay_time.item = f"<L>{formater.pay_time.item}</L>"
        elif font_size == registration_pb.FeiePrintFontSize.TWO_TIMES_WIDTH:
            pay_time = f"<W>{pay_time}</W>"
            formater.pay_time.item = f"<W>{formater.pay_time.item}</W>"
        elif font_size == registration_pb.FeiePrintFontSize.BOLD:
            pay_time = f"<B>{pay_time}</B>"
            formater.pay_time.item = f"<B>{formater.pay_time.item}</B>"
        content = self.__get_line_print_format(formater.pay_time, paper, contents=[pay_time])
        return content

    def build_take_meal_time_content(self, config, order):
        """取餐时间打印格式"""
        if order.meal_type not in [dish_pb.DishOrder.SELF_PICK_UP, dish_pb.DishOrder.EAT_IN]:
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        if order.meal_type == dish_pb.DishOrder.EAT_IN and not order.appointment_time:
            return ""
        appointment_time = order.appointment_time
        appointment_time = datetime.fromtimestamp(appointment_time)
        appointment_time = appointment_time.strftime("%Y-%m-%d %H:%M:%S")
        formater = self.get_formater(config)
        content = self.__get_line_print_format(formater.take_meal_time, paper, contents=[appointment_time])
        return content

    def build_table_name_content(self, config, table=None):
        """桌台名打印格式"""
        if table is None:
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        content = self.__get_line_print_format(formater.table_name, paper, contents=[table.name])
        return content

    def build_serial_number_content(self, config, order):
        """流水号打印"""
        if order.serial_number == "":
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        content = self.__get_line_print_format(formater.serial_number, paper, contents=[order.serial_number])
        return content

    def build_pos_info_content(self, config):
        """pos机信息打印格式"""
        return ""

    def _get_packaging_box_total_price_by_product(self, product):
        total_price = 0
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.AttrType.TAKE_AWAY:
                total_price += float(self.__format_price_to_str(attr.reprice)) * product.quantity
        return total_price

    def _get_packaging_box_total_price_by_order(self, order):
        return sum([self._get_packaging_box_total_price_by_product(product) for product in order.products])

    def build_packaging_box_content(self, config, order):
        """打包盒总价"""
        if not self._exists_packaging_box_by_order(order):
            return ""
        formater = self.get_formater(config)
        if not formater.HasField('packaging_box'):
            return ""
        formater = formater.packaging_box
        if not formater.item:
            formater.item = "打包费:"
        fee = self._get_packaging_box_total_price_by_order(order) * 100
        fee = self.__format_price_to_str(fee)
        paper = self.printer_config_papers.get(config.printer_sn)
        content = self.__get_line_print_format(formater, paper, contents=[fee])
        return content

    def build_dish_bill_fee_content(self, config, order):
        """菜品总价"""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        fee = order.bill_fee
        fee -= self._get_packaging_box_total_price_by_order(order) * 100
        fee = self.__format_price_to_str(fee)
        content = self.__get_line_print_format(formater.dish_bill_fee, paper, contents=[fee])
        return content

    def build_dish_bill_fee_content_v2(self, config, order):
        """菜品总价"""
        schema = self.get_product_schema(order)
        count = sum([row.product.product.quantity for row in schema])
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        fee = order.bill_fee
        fee = self.__format_price_to_str(fee)
        contents = [f"本单共:{int(count)}份", f"总价:{fee}元"]
        content = self.__get_line_print_format(formater.dish_bill_fee, paper, contents=contents)
        return content

    def build_total_paid_fee_content(self, config, transaction=None, order=None):
        """支付总金额"""
        if transaction is not None:
            paid_fee = transaction.paid_fee
        elif order is not None:
            paid_fee = order.paid_fee
        else:
            return ""
        paper = self.printer_config_papers.get(config.printer_sn)
        formater = self.get_formater(config)
        fee = self.__format_price_to_str(paid_fee)
        content = self.__get_line_print_format(formater.total_paid_fee, paper, contents=[fee])
        return content

    def build_dishes_info_header(self, config, price_max_len=None):
        """菜品表格的表头"""
        paper = self.printer_config_papers.get(config.printer_sn)
        content = ""
        formater = self.get_formater(config)
        header_formater = formater.dishes_info.header
        body_formater = formater.dishes_info.body
        headers = ["菜品"]
        if not body_formater.disable_number:
            headers.append("数量")
        if not body_formater.disable_price:
            headers.append("总价")
        # 菜品头部
        content += self.__get_dishes_header_content(headers, header_formater, body_formater, paper, price_max_len=price_max_len)
        content += self.add_seperate_line(config)
        return content

    def format_price_to_str(self, value):
        return self.__format_price_to_str(value)

    def __format_price_to_str(self, value):
        """把价格转换成字符串的形式"""
        cent = value % 10
        jiao = value % 100 - cent
        if cent == 0 and jiao == 0:
            ret = round(value / 100)
        elif cent == 0 and jiao > 0:
            ret = round(value / 100, 1)
        else:
            ret = round(value / 100, 2)
        return str(ret)

    def __format_quantity_to_str(self, config, value):
        """把菜品数量转换成字符串的形式"""
        if config.enable_item_split:
            value = 1
        if value == int(value):
            value = str(int(value))
        elif round(value, 1) == value:
            value = str(round(value, 1))
        elif round(value, 2) == value:
            value = str(round(value, 2))
        if self.ordering_config and self.ordering_config.enable_x_sign_before_amount:
            return f"x{value}"
        return value

    def __after_spaces(self, string, config, space_number, bytes_count):
        """增加后缀空格
        :params: string 原字符串
        :params: config 打印配置
        :params: space_number 要添加的空格数
        :params: bytes_count 字符串占用的字节数
        """
        space_count = space_number - bytes_count
        if config.enable_bold:
            space_count = int(space_count / 2) - 1
        spaces = " " * space_count
        string = f"{string}{spaces}"
        return string

    def word_count(self, string):
        """计算字符的总个数"""
        string = str(string)
        cn_count = self.chinese_count(string)
        total_count = len(string)
        en_count = total_count - cn_count
        return cn_count, en_count

    def chinese_count(self, string):
        """计算中文字符的个数"""
        string = str(string)
        chinese_array = self.__chinese_re_pattern.findall(string)
        return len(chinese_array)

    def __calculate_total_char_count(self, config, content):
        """返回content在formater格式下应该占用多少个ASCII字符数"""
        width_ratio = 1
        if config.enable_bold:
            width_ratio = 2
        cn_count, en_count = self.word_count(content)
        cn_char_number = cn_count * 2 * width_ratio
        en_char_number = en_count * width_ratio
        total_char_count = cn_char_number + en_char_number
        return total_char_count

    def __center_content(self, content):
        content = f"<C>{content}</C>"
        return content

    def __get_dishes_header_content(self, headers, config, body_formater, paper, price_max_len=None):
        """菜品列表的头部"""
        ascii_count = paper.get_first_part_occupy(config)
        name_ascii_count = self.__calculate_total_char_count(config, headers[0])
        name = self.__after_spaces(headers[0], config, ascii_count, name_ascii_count)
        price_spaces = ""

        if price_max_len is not None and price_max_len < self._price_max_len:
            price_spaces = " " * int((self._price_max_len - price_max_len) * 0.8)
            if body_formater.enable_bold:
                price_spaces = " " * int((self._price_max_len - price_max_len) * 1.1)
        if len(headers) == 3:
            content = f"{name}{price_spaces}{headers[1]}  {headers[2]}"
        elif len(headers) == 2:
            content = self.__begin_and_end_contents(headers[0], headers[1], config, paper)
        return content

    def __begin_and_end_contents(self, content1, content2, config, paper):
        """把文字放在一行的两边,中间用空格格开
        content1 为从起始位置开放放置
        content2 需要从一行的结束位置开始放置
        举例: |content1     content2 |
        """
        one_char_mm = paper.one_char_mm()

        total_char_count_1 = self.__calculate_total_char_count(config, content1)
        total_char_count_2 = self.__calculate_total_char_count(config, content2)

        total_mm = int((total_char_count_1 + total_char_count_2) * one_char_mm)
        remain_mm = paper.width - total_mm
        space_count = remain_mm / one_char_mm
        if config.enable_bold:
            space_count = int(space_count / 2)
        spaces = " " * int(space_count - 4)  # 计算有多少个空格,4作为容错
        return content1 + spaces + content2

    def __uniform_contents(self, contents, config, paper):
        """把contents等分"""
        one_char_mm = paper.one_char_mm()
        total_char_count = 0
        for content in contents:
            total_char_count += self.__calculate_total_char_count(config, content)
        total_mm = int(total_char_count * one_char_mm)
        remain_mm = paper.width - total_mm
        space_count = int(remain_mm / one_char_mm / len(contents))
        spaces = " " * space_count
        content = spaces.join(contents)
        return content

    def __multi_line(self, content, config, paper, line_count=2, price_max_len=None):
        """把字符串用换行符分开"""
        contents = []
        while True:
            line_count -= 1
            split_char_count = self.__get_newline_index(content, config, paper, price_max_len=price_max_len)
            content1 = content[:split_char_count]
            content2 = content[split_char_count:]
            contents.append(content1)
            if not content2:
                break
            if line_count == 0:
                contents.append(content2)
                break
            content = content2
        return contents

    def __get_newline_index(self, content, config, paper, price_max_len=None):
        """找到换行的位置"""
        total_char_count = self.__calculate_total_char_count(config, content)
        # 换行前部分占多少个ASCII字符数
        first_half_count = total_char_count
        first_part_occupy_ascii_count = paper.get_first_part_occupy(config, price_max_len=price_max_len)
        count = 0
        index = len(content)
        while first_half_count > first_part_occupy_ascii_count:
            # 从最后一个字符开始减去占用的ASCII字符数
            first_half_count -= self.__calculate_total_char_count(config, content[-1 - count])
            index -= 1
            count += 1
        return index

    def add_cut(self):
        return f"{self._new_line}{self._new_line}<CUT>"

    def set_product_price_max_len(self, order):
        for product in order.products:
            price = self.format_price_to_str(product.price)
            self._product_price_max_len = max(self._product_price_max_len, len(price))

    def get_supply_condiments_total_fee(self, product):
        total_fee = 0
        for m in product.supply_condiments:
            total_fee += m.market_price * m.quantity
        return total_fee

    def get_product_schema(self, order):
        schema = self._product_handler.schema
        if not schema:
            self.set_product_price_max_len(order)
            schema = self._product_handler._build_product_info(order, self._product_price_max_len)
        return schema

    def build_product_content(self, *args, **kwargs):
        return self.__build_product_content(*args, **kwargs)

    def build_dish_contents(self, order: dish_pb.DishOrder, price_max_len: int = None, format_type: str = None):
        """构建菜品打印格式"""
        try:
            return self._product_handler.run(order, price_max_len, format_type)
        except:
            self.logger.exception(f"order_id={order.id}, merchant_id={order.merchant_id}, user_id={order.user_id}, 打印格式组装错误")
            return {}


class PaperInfo:
    def __init__(self, config):
        self.__set_paper_width(config)
        self.__set_ascii_count()
        self._price_max_len = 6

    def __set_paper_width(self, config):
        if config.specification == "58mm":
            self.width = 58
        elif config.specification == "80mm":
            self.width = 80

    def __set_ascii_count(self):
        if self.width == 80:
            self.ascii_count = 48
        elif self.width == 58:
            self.ascii_count = 32
        else:
            self.ascii_count = 48

    def get_real_ascii_count(self, c):
        if c.enable_bold:
            return self.ascii_count / 2
        return self.ascii_count

    def one_char_mm(self):
        return self.width / self.ascii_count

    def get_first_part_occupy(self, config, price_max_len=None):
        """菜品名的第一部分占用一行的 百分比 的字节数"""
        real_ascii_count = self.get_real_ascii_count(config)
        percentage = self.__get_first_part_occupy_percentage(config, price_max_len=price_max_len)
        occupy = round(real_ascii_count * percentage)
        if price_max_len is not None and price_max_len > 0:
            occupy = int(real_ascii_count * percentage + self._price_max_len - price_max_len)
        if config.enable_bold:
            occupy *= 2
        return occupy

    def get_price_occupy_bytes_count(self, config):
        """菜品价格应占用的字节数"""
        bytes_count = 8
        if config.enable_bold:
            bytes_count = 10
        return bytes_count

    def get_quantity_occupy_bytes_count(self, config):
        """菜品数量应占用的字节数"""
        bytes_count = 1
        if config.enable_bold:
            bytes_count = 2
        return bytes_count

    def __get_first_part_occupy_percentage(self, config, price_max_len=None):
        _58_percentage = float(13 / 30)  # 1/3 + 1/10
        _80_percentage = float(3 / 5)
        try:
            if config.disable_price:
                _58_percentage = float(3 / 4)
                _80_percentage = float(4 / 5)
        except:
            pass
        if self.width == 58:
            if config.enable_bold:
                return _58_percentage
            return _58_percentage
        if self.width == 80:
            if config.enable_bold:
                return _80_percentage
            return _80_percentage

    @property
    def seperate_line(self):
        return "-" * self.ascii_count


class ProductHandler(object):
    """菜品数据处理"""

    def __init__(self, printformat_handler: FeiePrintFormat):
        self.printformat_handler = printformat_handler
        if self.printformat_handler.merchant:
            self.merchant_id = self.printformat_handler.merchant.id
            ordering_da = OrderingServiceDataAccessHelper()
            registration_info = ordering_da.get_registration_info(merchant_id=self.merchant_id)
            if not registration_info:
                raise errors.Error(err=error_codes.PRINTER_CONFIG_NOT_FOUND)
            self.printer_registration_info = registration_info.printer_config
        self.schema = []

    def _is_combo_name(self, product):
        return product.type == dish_pb.Dish.COMBO_MEAL

    def _is_combo(self, product):
        return self._is_combo_name(product) or self._is_sub_product(product)

    def _is_sub_product(self, product):
        return len(product.parent_uuid) > 1

    def _join_lines(self, lines: list):
        return self.printformat_handler._new_line.join(lines)

    def _build_product_info(self, order, price_max_len) -> List[ProductNode]:
        """
        构造菜品结构
        [
            {
                "name": "xx",
                "product": ProductInfo,
                "sub_product": [ProductInfo]
            }
        ]
        """
        schema, product_cache = [], {}
        for idx, product in enumerate(order.products):
            product_info = ProductInfo(product, order.meal_type, price_max_len=price_max_len)
            if self._is_sub_product(product):
                for node in schema:
                    if node.product.product.uuid == product.parent_uuid:
                        product_info.is_sub_product = True
                        _product, _idx = product_cache[product.parent_uuid]
                        quantity = int(_product.quantity)
                        schema[_idx].product.printer_combo_header_nums = quantity
                        product_info.printer_combo_header_nums = quantity
                        node.sub_product.append(product_info)
            else:
                schema.append(ProductNode(product.name, product_info))
                product_cache[product.uuid] = product, len(schema) - 1
        self.schema = schema
        return schema

    def _prepare_product_info(self, products: List[ProductNode], format_type) -> List[ProductNode]:
        """菜品信息预处理"""

        def get_printer_config(product):
            category_sns = self.printformat_handler.dish_category_id_map_sns.get(product.category_id, [])
            dish_sns = self.printformat_handler.dish_id_map_sns.get(product.id, [])
            enable_bind_dish = self.printer_registration_info.enable_bind_dish
            sns = dish_sns if enable_bind_dish else category_sns
            if not sns:
                self.printformat_handler.logger.warning(
                    f"菜品={product.name}，未绑定任何打印机，" f"当前绑定配置enable_bind_dish={enable_bind_dish}"
                )
            return {
                sn: self.printformat_handler.printer_configs.get(sn)
                for sn in sns
                if self.printformat_handler.printer_configs.get(sn)
            }

        def get_packaging_box_price(product):
            if not self.printformat_handler._exists_packaging_box_by_product(product):
                return 0
            return int(self.printformat_handler._get_packaging_box_total_price_by_product(product)) * 100

        index = 1
        for item in products:
            printer_configs = get_printer_config(item.product.product)
            item.product.printer_configs = printer_configs
            item.product.printer_format = {
                sn: self._get_printer_format_from_config(sn, printer_configs, format_type) for sn in printer_configs.keys()
            }
            item.product.index = index
            item.product.product.total_fee -= get_packaging_box_price(item.product.product)

            if item.sub_product:
                total_fee = 0
                for sub_item in item.sub_product:
                    sub_printer_configs = get_printer_config(sub_item.product)
                    sub_item.printer_configs = sub_printer_configs
                    sub_item.printer_format = {
                        sn: self._get_printer_format_from_config(sn, sub_printer_configs, format_type)
                        for sn in sub_printer_configs.keys()
                    }
                    sub_item.product.quantity /= max(1, item.product.product.quantity)
                    total_fee += sub_item.product.total_fee
                    total_fee += self.printformat_handler.get_supply_condiments_total_fee(sub_item.product)
                    total_fee -= get_packaging_box_price(sub_item.product)

                item.product.product.total_fee += total_fee

            index += 1
        return products

    def _get_printer_format(self, config, format_type: str):
        print_format = getattr(config, format_type)
        if print_format is None:
            raise ValueError(f'暂时不支持该打印机出票方式，只支持[结账单、后厨单], 当前打印类型：{format_type}')
        return print_format

    def _get_printer_format_from_config(self, sn, printer_configs: dict, format_type):
        printer_format = printer_configs.get(sn)
        if printer_format is None:
            return
        return self._get_printer_format(printer_format, format_type)

    def _sort_product_category(self, products: List[ProductNode]) -> List[ProductNode]:
        """菜品分类排序"""
        ordering_da = OrderingServiceDataAccessHelper()
        if ordering_da.get_registration_info(self.merchant_id).printer_config.feie_printer.enable_dish_sort:
            tmp = []
            all_category = ordering_da.get_categories(self.merchant_id, orderby=[('sort', 1)])
            for cate in all_category:
                for item in products:
                    if cate.id == item.product.product.category_id:
                        tmp.append(item)
            products = tmp
        return products

    def _build_printer_content(self, product: ProductInfo):
        """生成菜品打印内容"""
        for sn, printer_config in product.printer_configs.items():
            content = self.printformat_handler.build_product_content(
                product,
                printer_config,
            )
            product.printer_content[sn].extend(content)

    def _append_one_category_cut_to_printer(self, one_category_cut_items: dict, contents: dict):
        for sn, category_item in one_category_cut_items.items():
            for _, items in category_item.items():
                printer_content, tmp = [], defaultdict(lambda: [])
                for item in items:
                    if item.is_sub_product:
                        tmp[item.product.parent_uuid].append(item)
                    else:
                        printer_content.extend(item.printer_content[sn])
                if tmp:
                    for _, _items in tmp.items():
                        combo_header = _items[0].printer_combo_header
                        combo_content = [combo_header, *reduce(lambda x, y: x + y, [row.printer_content[sn] for row in _items])]
                        combo_content = [self._join_lines(combo_content)]
                        if _items[0].printer_format[sn].enable_item_split:
                            combo_content *= _items[0].printer_combo_header_nums
                        for cb_cn in combo_content:
                            printer_content.insert(_items[0].index - 1, cb_cn)
                contents[sn].append(self._join_lines(printer_content))

    def _append_content_to_printer(self, products: List[ProductNode]):
        """
        组装菜品打印内容到对应的打印机

        # one_category_cut_items
        sn:
            category_id:
                [productInfo]
        """
        contents = defaultdict(lambda: [])
        one_category_cut_items = defaultdict(lambda: defaultdict(lambda: []))
        for item in products:
            # 单品菜
            if not item.sub_product:
                for sn, content in item.product.printer_content.items():
                    printer_format = item.product.printer_format.get(sn)
                    if not printer_format:
                        continue
                    if printer_format.one_category_cut:
                        printer_format.one_dish_cut = True
                        one_category_cut_items[sn][item.product.product.category_id].append(item.product.copy())
                    else:
                        contents[sn].extend(content)
                continue
            # 套餐
            tmp_sub_item = defaultdict(lambda: defaultdict(lambda: []))
            for sub_item in item.sub_product:
                for sn, content in sub_item.printer_content.items():
                    if not content:
                        continue
                    header = item.product.printer_content.get(sn)
                    if not header:
                        continue
                    header = header[0]
                    if not header:
                        continue
                    sub_item.printer_combo_header = header
                    printer_format = item.product.printer_format.get(sn)
                    if not printer_format:
                        continue
                    if printer_format.one_category_cut:
                        printer_format.one_dish_cut = True
                        sub_item.index = item.product.index
                        one_category_cut_items[sn][sub_item.product.category_id].append(sub_item.copy())
                    elif printer_format.combo_one_dish_cut:
                        printer_format.one_dish_cut = True
                        content_item = [self._join_lines([header, *content])]
                        if printer_format.enable_item_split:
                            content_item *= sub_item.printer_combo_header_nums
                        contents[sn].extend(content_item)
                    else:
                        tmp_sub_item[sn][header].extend(content)
            if tmp_sub_item:
                for sn, header_content in tmp_sub_item.items():
                    for header, cn in header_content.items():
                        content_item = [self._join_lines([header, *cn])]
                        printer_format = item.product.printer_format.get(sn)
                        if not printer_format:
                            continue
                        if printer_format.enable_item_split:
                            content_item *= item.product.printer_combo_header_nums
                        contents[sn].extend(content_item)

        if one_category_cut_items:
            self._append_one_category_cut_to_printer(one_category_cut_items, contents)
        return contents

    def run(self, order: dish_pb.DishOrder, price_max_len=None, format_type: str = None) -> Dict[str, List[str]]:
        copy_order = copy_obj(order, dish_pb.DishOrder)
        # 组装菜品结构
        products = self._build_product_info(copy_order, price_max_len)
        # 分类排序
        products = self._sort_product_category(products)
        # 菜品信息预处理
        products = self._prepare_product_info(products, format_type)
        # 生成菜品打印内容
        for item in products:
            self._build_printer_content(item.product)
            for sub_item in item.sub_product:
                self._build_printer_content(sub_item)
        # 组装菜品打印内容到对应的打印机
        return self._append_content_to_printer(products)

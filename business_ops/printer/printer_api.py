# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/27 17:18:46
'''
import hashlib
import time

import proto.ordering.registration_pb2 as registration_pb
from common import http


class PrinterApi(object):
    NAME: registration_pb.PrinterConfigByType.Type

    def __init__(self):
        from .feie_printer_manager import logger
        self.logger = logger
    
    def set_voice(self, *args, **kwargs):
        raise NotImplementedError


class FeiePrinterApi(PrinterApi):
    """
    开发文档：https://help.feieyun.com/home/<USER>/zh;nav=1-1
    """
    NAME = registration_pb.PrinterConfigByType.Type.FEIE

    def set_voice(self, printer_sn, voice):
        data = {
            'action': 'Printer_setPrinterConfig',
            'uid': '2019793',
            'sn': printer_sn,
            'beeperalarm': voice
        }
        headers = {
            'Connection': 'keep-alive',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="96", "Google Chrome";v="96"',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua-mobile': '?0',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36',
            'sec-ch-ua-platform': '"macOS"',
            'Origin': 'https://admin.feieyun.com',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://admin.feieyun.com/setprint.php?sn=960201768',
            'Accept-Language': 'zh-CN,zh;q=0.9',
        }
        cookies = {
            'PHPSESSID': 'scejnnl2aegfo7vptqjgesksmm',
            'feie_selected_cookiedate': '2592000',
            'feie_rememberMe': '1637747542',
            'feie_uid': '2019793',
            'feie_mail': 'jieli%40zhiyi.ai',
            'feie_ukey': 'CPLf5uYyNMInmInX',
            'feie_name': 'Jie+Li',
            'feie_lastlogintime': '2021-11-17+11%3A10%3A10',
            'feie_logintimes': '34',
            'feie_token': 'dd39abaafb81d4258f69cce118a45bfe',
            'feie_lastloginip': '************',
            'feie_lastAddr': '%E5%B9%BF%E4%B8%9C%E7%9C%81%E6%B7%B1%E5%9C%B3%E5%B8%82',
            'feie_time': '1637751141',
            'feie_sig': '18d642418a5f0912a377d4f4af11b653749cd801',
            'tgw_l7_route': 'd9d1f4f050e5506acb8753616e8d69d7',
            'feie_ipcode': 'd081WZSMe5SVXt8%2BGcREhp3uxLMQUaBnd%2Bd9%2FoTyNcjYBNbIZU9pR4fg',
        }
        resp = http.post(
            "https://admin.feieyun.com/api.php",
            data=data,
            headers=headers,
            cookies=cookies,
            timeout=30,
        ).json()
        self.logger.info(f"printer_sn={printer_sn} set voice response={resp}")
        return resp.get('data', False)


class XPrinterApi(PrinterApi):
    """
    开发文档：https://www.xpyun.net/open/index.html
    """
    NAME = registration_pb.PrinterConfigByType.Type.XPRINTER
    _VOICE_MAP = {
        "W": 0,
        "V": 1,
        "U": 2,
        "Y": 3,
        "N": 4
    }

    def set_voice(self, printer_sn, voice):
        timestamp = str(int(time.time()))
        user = "<EMAIL>"
        ukey = "3d25861c299c4c2b94a15709f656a13a"
        resp = http.post(
            "https://open.xpyun.net/api/openapi/xprinter/setVoiceType",
            json={
                "user": "<EMAIL>",
                "timestamp": timestamp,
                "sn": printer_sn,
                "voiceType": self._VOICE_MAP[voice],
                "sign": hashlib.sha1(str(user+ ukey + timestamp).encode('utf-8')).hexdigest()
            },
            timeout=30,
        ).json()
        self.logger.info(f"printer_sn={printer_sn} set voice response={resp}")
        return resp.get('data', False)


PRINTER_API_MAP = {
    registration_pb.PrinterConfigByType.Type.Name(FeiePrinterApi.NAME): FeiePrinterApi(),
    registration_pb.PrinterConfigByType.Type.Name(XPrinterApi.NAME): XPrinterApi()
}

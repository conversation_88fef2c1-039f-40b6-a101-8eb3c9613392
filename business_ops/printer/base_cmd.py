# -*- encoding: utf-8 -*-

'''
@Time        :   2025/02/26 13:49:19
'''

import proto.ordering.registration_pb2 as registration_pb


class BaseCMD:

    def align_left(self, *args, **kwargs):
        """左对齐"""

    def align_right(self, *args, **kwargs):
        """右对齐"""

    def align_center(self, *args, **kwargs):
        """居中"""


class FeieCMD(BaseCMD):
    def align_left(self, text: str):
        """左对齐"""
        return f"<L>{text}<BR></L>"

    def align_right(self, text: str):
        """右对齐"""
        return f"<RIGHT>{text} </RIGHT>"

    def align_center(self, text: str):
        """居中"""
        return f"<C>{text}</C>"


class XPrinterCMD(BaseCMD):
    def align_left(self, text: str):
        """左对齐"""
        return f"<L>{text}<BR></L>"

    def align_right(self, text: str):
        """右对齐"""
        return f"<R>{text} <BR></R>"

    def align_center(self, text: str):
        """居中"""
        return f"<C>{text}<BR></C>"


CMD_MAP = {
    registration_pb.PrinterConfigByType.Type.FEIE: FeieCMD,
    registration_pb.PrinterConfigByType.Type.XPRINTER: XPrinterCMD,
}
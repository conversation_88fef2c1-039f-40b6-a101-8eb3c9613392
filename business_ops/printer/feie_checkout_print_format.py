# -*- coding: utf-8 -*-

import logging

from business_ops.printer.feie_print_format import FeiePrintFormat
from common.base_app import record_cpu_cost, record_cpu_cost_from_name

logger = logging.getLogger(__name__)


class FeieCheckoutPrintFormat(FeiePrintFormat):

    @record_cpu_cost_from_name(name='FeieCheckoutPrintFormat.__init__')
    def __init__(self, *args, **kargs):
        super(FeieCheckoutPrintFormat, self).__init__(*args, **kargs)
        self._type = self.CHECKOUT

    @record_cpu_cost
    def reception_print_create_order(self, transaction, order, table):
        price_max_len = 1
        for product in order.products:
            price = self.format_price_to_str(product.price)
            price_max_len = max(price_max_len, len(price))
        dish_contents = self.build_dish_contents(
            order,
            price_max_len=price_max_len,
            format_type="checkout_print_format"
        )
        contents = []
        for sn, cs in dish_contents.items():
            key = f"{table.id}_{sn}"
            logger.info(f"TableArea: {self.merchant.basic_info.display_name} {self.table_printer_map} {key}")
            if self.table_printer_map and key not in self.table_printer_map:
                # 如果有设置映射,且当前组件不在映射里,则跳过
                continue
            config = self.printer_configs.get(sn)
            if not config.enable_checkout_print:
                continue
            header = self.__build_header_contents(config, order, table)
            tail = self.__build_tail_contents(config, transaction, order, table)
            dish_header = self.build_dishes_info_header(config, price_max_len=price_max_len)
            contents.append({
                "sn": sn,
                "contents": cs,
                "header": header,
                "tail": tail,
                "dish_header": dish_header,
                "seperate_line": self.add_seperate_line(config, prefix_new_line=False),
                "cut": self.add_cut(),
                "config": config,
                "one_dish_cut": config.checkout_print_format.one_dish_cut
            })
        return contents

    def __build_header_contents(self, config, order, table):
        content = ""
        content += self.build_title_content(config)
        content += self.build_meal_type_name(config, order)
        content += self.build_table_name_content(config, table)
        content += self.build_serial_number_content(config, order)
        content += self.build_order_meal_code_content(config, order)
        content += self.build_pay_method_content(config, order)
        content += self.build_take_meal_time_content(config, order)
        content += self.build_pay_time_content(config, order)
        content += self.add_seperate_line(config, prefix_new_line=False)
        content += self.build_order_comment_content(config, order)
        return content

    def __build_tail_contents(self, config, transaction, order, table):
        content = ""
        content += self.build_packaging_box_content(config, order)
        content += self.build_dish_bill_fee_content(config, order)
        content += self.build_total_paid_fee_content(config, transaction)
        content += self.build_shipping_address_content(config, order, table)
        content += self.build_self_pick_up_order_content(config, order)
        content += self.build_pos_info_content(config)
        content += self._new_line
        return content
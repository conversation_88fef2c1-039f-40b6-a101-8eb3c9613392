# -*- encoding: utf-8 -*-

'''
@Time        :   2024/06/20 14:43:47
'''
import logging
from typing import List

import proto.ordering.registration_pb2 as registration_pb
from dao.dao_helper import DaoOR<PERSON>Helper
from dao import constants
from cache.printer_cache import PrinterDishCache

logger = logging.getLogger(__name__)


class BasePrinterManager(DaoORMHelper):

    def __init__(self):
        super().__init__(
            db=constants.MONGODB_CONFIG_DATABASE_NAME,
            collection=constants.MONGODB_PRINTER_CONFIG_COLLECTION_NAME,
            pb=registration_pb.PrinterConfigByType
        )
        self._registration_dao = DaoORMHelper(
            db = constants.MONGODB_ORDERING_SERVICE_INFO_DATABASE_NAME,
            collection = constants.MONGODB_ORDERING_SERVICE_INFO_COLLECTION_NAME,
            pb=registration_pb.OrderingServiceRegistrationInfo
        )

    def _default_line_print_format(
        self,
        formater,
        item="",
        typography=registration_pb.LinePrintFormat.ONE_BY_ONE,
        item_position=registration_pb.LinePrintFormat.LEFT_ALIGN,
        enable_seperate_line=True,
        enable_bold=True,
        disable_display=True
    ):
        formater.CopyFrom(registration_pb.LinePrintFormat())
        formater.item = item
        formater.item_position = item_position
        formater.enable_seperate_line = enable_seperate_line
        formater.typography = typography
        formater.enable_bold = enable_bold
        formater.disable_display = disable_display

    def _set_default_value(self, pb):
        if not pb.HasField('bind_dish'):
            pb.bind_dish.CopyFrom(registration_pb.BindDish())

        if pb.HasField('checkout_print_format'):
            checkout_print_format = pb.checkout_print_format
            if not checkout_print_format.HasField('shipping_address'):
                self._default_line_print_format(
                    checkout_print_format.shipping_address,
                )
            if not checkout_print_format.HasField('packaging_box'):
                self._default_line_print_format(
                    checkout_print_format.packaging_box,
                    item="打包费:",
                    typography=registration_pb.LinePrintFormat.BEGIN_AND_END
                )
            if not checkout_print_format.HasField('self_pick_up_info'):
                self._default_line_print_format(
                    checkout_print_format.self_pick_up_info,
                    enable_bold=False
                )
            if not checkout_print_format.HasField('meal_type'):
                self._default_line_print_format(
                    checkout_print_format.meal_type,
                    item_position=registration_pb.LinePrintFormat.CENTER,
                )
            if not checkout_print_format.HasField('pay_method'):
                self._default_line_print_format(
                    checkout_print_format.pay_method,
                    item="支付方式:",
                    enable_seperate_line=False,
                    enable_bold=True,
                    disable_display=True
                )

        if pb.HasField('kitchen_print_format'):
            kitchen_print_format = pb.kitchen_print_format
            if not kitchen_print_format.HasField('shipping_address'):
                self._default_line_print_format(
                    kitchen_print_format.shipping_address,
                )
            if not kitchen_print_format.HasField('self_pick_up_info'):
                self._default_line_print_format(
                    kitchen_print_format.self_pick_up_info,
                    enable_bold=False
                )
            if not kitchen_print_format.HasField('meal_type'):
                self._default_line_print_format(
                    kitchen_print_format.meal_type,
                    item_position=registration_pb.LinePrintFormat.CENTER,
                )
            if not kitchen_print_format.HasField('pay_method'):
                self._default_line_print_format(
                    kitchen_print_format.pay_method,
                    item="支付方式:",
                    enable_seperate_line=False,
                    enable_bold=True,
                    disable_display=True
                )

        return pb


class PrinterConfigManager(BasePrinterManager):

    def get_config(self, merchant_id: str) -> dict:
        pb = self._registration_dao.get(
            matcher={"merchant_id": merchant_id},
            resp_dict=False,
            projection={
                'printerConfig.keruyunPrinter': 0,
                'printerConfig.feiePrinter': 0
            }
        )
        registration_config = self._registration_dao.to_dict(pb.printer_config)
        projection = {
            "oneDishCut": 0,
            "printTimes": 0
        }
        printer_configs = super().query(
            matcher={"merchant_id": merchant_id},
            resp_dict=False,
            projection=projection,
            pb_callback=self._set_default_value
        )
        return {
            "registration_config": registration_config,
            "printer_configs": [
                self.to_dict(p, hidden_names=list(projection.keys()))
                for p in printer_configs
            ]
        }

    @PrinterDishCache.update
    def update_config(
        self,
        merchant_id: str,
        registration_config: dict,
        printer_configs: List[dict]
    ):
        update_set = {
            f"printer_config.{k}": v
            for k, v in registration_config.items()
            if k not in ["feie_printer", "keruyun_printer"]
        }
        self._registration_dao.add_or_update(
            update_set,
            matcher={"merchant_id": merchant_id},
            pb_check=False
        )

        for item in printer_configs:
            printer_sn = item["printerSn"]
            if not self.exists(merchant_id, printer_sn):
                logger.warning(f"打印机：printerSn={printer_sn} 已在其它端删除, merchant_id={merchant_id}")
                continue
            super().add_or_update(
                item,
                matcher={
                    "merchant_id": merchant_id,
                    "printer_sn": printer_sn
                }
            )

    def exists(
        self,
        merchant_id: str,
        printer_sn: str
    ) -> bool:
        return bool(super().get(
            matcher={
                "merchant_id": merchant_id,
                "printer_sn": printer_sn
            }
        ))
    
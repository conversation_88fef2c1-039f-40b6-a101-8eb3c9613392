# 优惠券类别帮助类

from common.utils import date_utils
from common.utils import id_manager
from proto import coupon_category_pb2 as coupon_category_pb
from proto import wechat_common_pb2 as wechat_common_pb

def create_cash_coupon_category(merchant,
                                cash_coupon_spec):
    """创建代金券
    """
    builder = CouponCategoryBuilder(merchant)
    builder.with_issue_scene(cash_coupon_spec.coupon_category_spec.issue_scene)
    builder.with_coupon_type(wechat_common_pb.CASH)
    builder.with_cash_coupon_spec(cash_coupon_spec)
    return builder.build()

def create_verification_code_coupon_category(merchant, verification_code_coupon_spec):
    builder = CouponCategoryBuilder(merchant)
    builder.with_issue_scene(verification_code_coupon_spec.coupon_category_spec.issue_scene)
    builder.with_coupon_type(wechat_common_pb.CASH)
    builder.with_cash_coupon_spec(verification_code_coupon_spec)
    return builder.build()


def create_dish_coupon_category(merchant, dish_coupon_spec):
    builder = CouponCategoryBuilder(merchant)
    builder.with_issue_scene(dish_coupon_spec.coupon_category_spec.issue_scene)
    builder.with_coupon_type(wechat_common_pb.DISH_VERIFICATION_CODE)
    return builder.build()


def create_brand_dish_coupon_category(dish_coupon_spec, brand_id):
    builder = CouponCategoryBuilder(None, brand_id)
    builder.with_issue_scene(dish_coupon_spec.coupon_category_spec.issue_scene)
    builder.with_coupon_type(wechat_common_pb.BRAND_DISH_VERIFICATION_CODE)
    return builder.build()


def get_coupon_category_spec(coupon_category):
    """获取CouponCategory具体的配置信息
    """
    coupon_type = coupon_category.coupon_type
    if coupon_type == wechat_common_pb.GROUPON:
        return coupon_category.groupon_spec
    elif coupon_type == wechat_common_pb.CASH:
        return coupon_category.cash_coupon_spec
    elif coupon_type == wechat_common_pb.DISCOUNT:
        return coupon_category.discount_coupon_spec
    elif coupon_type == wechat_common_pb.GIFT:
        return coupon_category.gift_coupon_spec
    elif coupon_type == wechat_common_pb.GENERAL_COUPON:
        return coupon_category.general_coupon_spec
    elif coupon_type == wechat_common_pb.VERIFICATION_CODE:
        return coupon_category.verification_code_coupon_spec
    elif coupon_type == wechat_common_pb.DISH_VERIFICATION_CODE:
        return coupon_category.dish_verification_code_coupon_spec
    elif coupon_type == wechat_common_pb.BRAND_DISH_VERIFICATION_CODE:
        return coupon_category.brand_dish_verification_code_coupon_spec
    elif coupon_type == wechat_common_pb.INVITE_SHARE:
        return coupon_category.invite_share_coupon_spec
    elif coupon_type == wechat_common_pb.DISH:
        return coupon_category.dish_coupon_spec
    return None

def get_coupon_category_base_info(coupon_category):
    coupon_spec = get_coupon_category_spec(coupon_category)
    if coupon_spec:
        return coupon_spec.base_info
    return None

def get_coupon_category_advanced_info(coupon_category):
    coupon_spec = get_coupon_category_spec(coupon_category)
    if coupon_spec:
        return coupon_spec.advanced_info
    return None

class CouponCategoryBuilder():

    def __init__(self, merchant, brand_id=None):
        self.__merchant = merchant
        self.__store_id_list = None
        self.__issue_scene = None
        self.__coupon_type = None
        self.__cash_coupon_spec = None
        self.__brand_id = brand_id

    def build(self):
        coupon_category = coupon_category_pb.CouponCategory()
        coupon_category.id = id_manager.generate_coupon_category_id()
        if self.__merchant:
            coupon_category.merchant_id = self.__merchant.id
        coupon_category.issue_scene = self.__issue_scene
        coupon_category.create_time = date_utils.timestamp_second()
        coupon_category.coupon_type = self.__coupon_type
        coupon_category.state = coupon_category_pb.CouponCategory.ACTIVE
        if self.__coupon_type == wechat_common_pb.CASH:
            coupon_category.cash_coupon_spec.CopyFrom(self.__cash_coupon_spec)
        if self.__brand_id:
            coupon_category.brand_id = self.__brand_id

        return coupon_category

    def with_issue_scene(self, issue_scene):
        self.__issue_scene = issue_scene

    def with_coupon_type(self, coupon_type):
        self.__coupon_type = coupon_type

    def with_cash_coupon_spec(self, cash_coupon_spec):
        least_cost = cash_coupon_spec.least_cost
        reduce_cost = cash_coupon_spec.reduce_cost

        self.__cash_coupon_spec = coupon_category_pb.CashCouponCategory()

        base_info = self.__with_base_info(cash_coupon_spec.coupon_category_spec)
        base_info.title = "满{}减{}元代金券".format(int(least_cost / 100), int(reduce_cost / 100))
        self.__cash_coupon_spec.base_info.CopyFrom(base_info)

        advanced_info = self.__with_advanced_info(
            coupon_category_spec=cash_coupon_spec.coupon_category_spec, least_cost=least_cost)
        self.__cash_coupon_spec.advanced_info.CopyFrom(advanced_info)

        self.__cash_coupon_spec.least_cost = least_cost
        self.__cash_coupon_spec.reduce_cost = reduce_cost

    def __with_base_info(self, coupon_category_spec):
        base_info = coupon_category_pb.CouponBaseInfo()
        if self.__merchant:
            base_info.logo_url = self.__merchant.basic_info.logo_url
            base_info.brand_name = self.__merchant.basic_info.display_name
            base_info.color = self.__merchant.preferences.design_config.color
        if coupon_category_spec.description:
            base_info.description = coupon_category_spec.description
        else:
            base_info.description = "1、满减优惠券，仅供堂食使用；\n2、本券不与其他优惠同享。\n"
        base_info.sku.quantity = 9999999
        base_info.date_info.CopyFrom(coupon_category_spec.date_info)
        base_info.get_limit = coupon_category_spec.get_limit
        base_info.use_limit = coupon_category_spec.use_limit
        return base_info

    def __with_advanced_info(self, coupon_category_spec, least_cost):
        advanced_info = coupon_category_pb.CouponAdvancedInfo()
        advanced_info.use_condition.reject_category = coupon_category_spec.reject_categories
        advanced_info.use_condition.can_use_with_other_discount = coupon_category_spec.can_use_with_other_discount
        advanced_info.use_condition.least_cost = least_cost
        return advanced_info

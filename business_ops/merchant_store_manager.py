from collections import namedtuple

from business_ops.coupon_manager import CouponManager
from common.utils import id_manager
from common.utils import date_utils
from dao.coupon_category_da_helper import CouponCategoryDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from proto import coupon_category_pb2 as coupon_category_pb
from proto import strategy_pb2 as strategy_pb
from strategy import group_dining_strategy
from wechat_lib import lbs_api_helper

def update_or_create_store(merchant, store):
    """更新或创建商户
    """
    # 更新地址POI
    update_address_poi(store)

    stores = merchant.stores
    existed_store_index = -1
    for index, existed_store in enumerate(stores):
        if existed_store.id == store.id:
            existed_store_index = index
            break

    if existed_store_index == -1:
        # 根据需要生成饭局优惠组合
        if store.enable_group_dining:
            update_group_dining_coupon_policies(merchant, store)

        new_store = stores.add()
        new_store.enable_ordering_coupon_package_union_pay = True
        new_store.enable_ordering_service = True
        new_store.CopyFrom(store)
    else:
        existed_store = stores[existed_store_index]
        # 根据需要生成饭局优惠组合
        if store.enable_group_dining:
            if not (existed_store.enable_group_dining and store.avg_cost_per_person == existed_store.avg_cost_per_person \
                    and store.group_dining_discount == existed_store.group_dining_discount):
                update_group_dining_coupon_policies(merchant, store)
        existed_store.CopyFrom(store)

    MerchantDataAccessHelper().update_or_create_merchant(merchant)

def update_address_poi(store):
    """获取商户门店地址的坐标
    """
    if store.address:
        poi = lbs_api_helper.get_address_location(store.address)
        if poi:
            store.poi.CopyFrom(poi)
    else:
        store.poi.Clear()
    return store

def update_group_dining_coupon_policies(merchant, store):
    """更新饭局的优惠组合
    """
    # 先清除，然后更新门店的group_dining_coupon_policies
    del store.group_dining_coupon_policies[:]
    group_dining_coupon_policies = group_dining_strategy.generate_coupon_policies(cost_per_person=store.avg_cost_per_person,
        total_discount=store.group_dining_discount)
    for group_dining_coupon_policy in group_dining_coupon_policies:
        coupon_policy = store.group_dining_coupon_policies.add()
        coupon_policy.CopyFrom(group_dining_coupon_policy)

    # 生成优惠券类别
    update_group_dining_coupon_categories(merchant, store)
    return store

def get_merchant_store(merchant_id, store_id):
    """获取商户门店信息

    Args:
        merchant_id: (String) 商户ID
        store_id: (String) 门店ID

    Return:
        (merchant_rules_pb.Store) 门店信息
    """
    merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
    if not merchant:
        return None, None

    stores = merchant.stores
    if not stores:
        return None, None

    for store in stores:
        if store.id == store_id:
            return merchant, store

    return None, None

def remove_store_manager(merchant, store, manager_id):
    """从商户门店中移除管理员

    Args:
        merchant: (Merchant) 商户ID
        store: (Store) 门店ID
        manager_id: (String) 门店管理员ID
    """
    merchant_da = MerchantDataAccessHelper()
    for manager in merchant.manager_list:
        if manager.user_id == manager_id:
            while store.id in manager.store_id_list:
                manager.store_id_list.remove(store.id)
            merchant_da.update_or_create_merchant(merchant)
            break

def update_group_dining_coupon_categories(merchant, store):
    """更新/创建饭局类型的优惠券种类
    1. 删除已有的优惠券类型
    2. 拷贝拉新券配置，修改 least_cost, reduce_cost 等配置

    """
    CouponCategoryDataAccessHelper().update_coupon_category_state(merchant_id=merchant.id,
            store_id=store.id, issue_scene=coupon_category_pb.CouponCategory.GROUP_DINING,
            state=coupon_category_pb.CouponCategory.DELETED)

    coupon_manager = CouponManager()
    new_member_coupon_category_spec = None
    if len(merchant.preferences.coupon_config.new_member_coupons) > 0:
        new_member_coupon_category_spec = merchant.preferences.coupon_config.new_member_coupons[0]
    for group_dining_coupon_policy in store.group_dining_coupon_policies:
        coupon_category_spec = strategy_pb.CouponCategorySpec()
        coupon_category_spec.id = id_manager.generate_coupon_category_id()
        coupon_category_spec.name = "饭局优惠券"
        if new_member_coupon_category_spec:
            coupon_category_spec.cash_coupon_spec.CopyFrom(new_member_coupon_category_spec.cash_coupon_spec)
        cash_coupon_category_spec = coupon_category_spec.cash_coupon_spec
        cash_coupon_category_spec.least_cost = group_dining_coupon_policy.least_cost
        cash_coupon_category_spec.reduce_cost = group_dining_coupon_policy.reduce_cost
        cash_coupon_category_spec.coupon_category_spec.issue_scene = coupon_category_pb.CouponCategory.GROUP_DINING
        cash_coupon_category_spec.coupon_category_spec.get_limit = 1000
        cash_coupon_category_spec.coupon_category_spec.use_limit = 1000
        coupon_manager.create_coupon_category(merchant=merchant,
                                              coupon_spec=coupon_category_spec,
                                              store_id=store.id)


def is_no_discount_time_ranges(merchant):
    """ 当前时间是不是非优惠时间
    Args:
        merchant_id: (string)商户id
    Return:
        flag: True/False
        time_range: 时间段的字符串表示
    """
    store = merchant.stores[0]

    now = date_utils.datetime_now_in_timezone()
    weekday = now.weekday()
    now_second = now.hour * date_utils.ONE_HOUR + now.minute * date_utils.ONE_MINUTE + now.second
    Ret = namedtuple("range", ("flag range"))

    for time_range in store.no_discount_time_ranges:
        wday = time_range.day_of_week
        if weekday == wday:
            if time_range.start_second_of_day <= now_second <= time_range.end_second_of_day:
                start = date_utils.hours_text(time_range.start_second_of_day)
                end = date_utils.hours_text(time_range.end_second_of_day)
                return Ret(flag=True, range='%s-%s' % (start, end))
    return Ret(flag=False, range="")

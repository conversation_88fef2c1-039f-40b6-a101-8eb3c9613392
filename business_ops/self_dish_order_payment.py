# -*- coding: utf-8 -*-

import collections
import logging
import math
import time

from flask import request

import proto.finance.wallet_pb2 as wallet_pb
import proto.ordering.dish_pb2 as dish_pb
import proto.group_dining.red_packet_pb2 as red_packet_pb
import proto.coupon_category_pb2 as coupon_category_pb
import proto.ordering.registration_pb2 as registration_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.business_manager import BusinessManager
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from business_ops.transaction_manager import TransactionManager
from business_ops.fanpiao_helper import FanpiaoHelper
from business_ops.payment_manager import PaymentManager
from business_ops.ordering.order_manager import OrderManager
from business_ops.ordering.table_manager import TableManager
from business_ops.merchant_phone_member import MerchantPhoneMemberManager
from business_ops.red_packet_manager import RedPacketManager
from business_ops.shopping_card_manager import ShoppingCardManager
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.wallet_manager import WalletManager
from common.cache_server_keys import CacheServerKeys
from common.utils import date_utils
from cache.redis_client import RedisClient
from common.utils.number_utils import NumberUtils
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.red_packet_da_helper import RedPacketDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from service import errors
from service import error_codes
from service import base_responses
from wechat_lib import subscribe_message_send
from wechat_lib.subscribe_message import SubscribeMessage
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

logger = logging.getLogger(__name__)


class SelfDishOrderPaymentManager(BusinessManager):
    def __init__(
        self,
        transaction_id=None,
        user_id=None,
        merchant_id=None,
        order_id=None,
        no_discount_bill_fee=0,
        bill_fee=None,
        paid_fee=None,
        pay_method=None,
        return_url=None,
        coupon_id=None,
        discount_fee=None,
        is_zero_paid=False,
        fanpiao_id=None,
        is_invoice=None,
        order=None,
        transaction=None,
        merchant=None,
        user=None,
        is_phone_member_pay=None,
        partial_refund=False,
        order_manager_v2=None,
        is_from_group=None,
        is_group_purchase=None,
        coupon_ids=None,
        coin_deduction=None,
        check_coupon=True,
    ):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.return_url = return_url
        self.no_discount_bill_fee = no_discount_bill_fee
        self.shipping_fee = 0
        self.discount_fee = discount_fee
        self.is_invoice = is_invoice
        self.is_from_group = is_from_group
        self.coupon_fee = 0
        self.partial_refund = partial_refund
        self._order_manager_v2 = order_manager_v2
        self.coin_deduction = coin_deduction

        super().__init__(
            transaction_id=transaction_id,
            merchant_id=merchant_id,
            order_id=order_id,
            user_id=user_id,
            coupon_id=coupon_id,
            pay_method=pay_method,
            is_invoice=is_invoice,
            order=order,
            transaction=transaction,
            merchant=merchant,
            user=user,
            is_phone_member_pay=is_phone_member_pay,
            is_group_purchase=is_group_purchase,
            coupon_ids=coupon_ids,
            coin_deduction=coin_deduction,
            check_coupon=check_coupon
        )

        self.paid_fee = NumberUtils.safe_round(self.paid_fee)
        self.bill_fee = NumberUtils.safe_round(self.bill_fee)
        self.coupon_fee = NumberUtils.safe_round(self.coupon_fee)
        self.discount_fee = self.bill_fee - self.paid_fee - self.coupon_fee
        self.no_discount_bill_fee = NumberUtils.safe_round(self.no_discount_bill_fee)
        self.shipping_fee = NumberUtils.safe_round(self.shipping_fee)
        self.redis_client = RedisClient().get_connection()
        config_da = ConfigDataAccessHelper()
        self.platform_global_config = config_da.get_platform_global_config()
        self.merchant_vip_membership_config = config_da.get_vip_membership_config(self.merchant.id)

    def calculate_wechat_group_fee(self):
        Fee = collections.namedtuple("Fee", ["order_bill_fee", "order_paid_fee"])
        if self.is_from_group and wallet_pb.Transaction.WECHAT_PAY == self.pay_method:
            order_bill_fee = self.order.bill_fee
            order_paid_fee = self.round_off(
                self.order.bill_fee * self.merchant_vip_membership_config.vip_membership_discount + 0.5
            )
            logger.info("订单 {} 通过企业微信群下单: {}, {}".format(self.order.id, order_bill_fee, order_paid_fee))
            return Fee(order_bill_fee, order_paid_fee)

    def calculate_phone_member_fee(self):
        if not self.is_phone_member_pay:
            return None
        if self.pay_method not in [
            wallet_pb.Transaction.WECHAT_PAY,
            wallet_pb.Transaction.ALIPAY,
            wallet_pb.Transaction.TIAN_QUE_PAY,
        ]:
            raise errors.ShowError("此支付方式不支持使用手机会员支付")
        Fee = collections.namedtuple("Fee", ["order_bill_fee", "order_paid_fee"])
        manager = MerchantPhoneMemberManager(merchant=self.merchant, user=self.user)
        phone_member_info = manager.get_merchant_phone_member()
        if phone_member_info:
            order_bill_fee = self.order.bill_fee
            order_paid_fee = int(self.order.bill_fee * (100 - phone_member_info.discount) / float(100) + 0.5)
            logger.info("订单 {} 使用手机会员支付: {}, {}".format(self.order.id, order_bill_fee, order_paid_fee))
            return Fee(order_bill_fee, order_paid_fee)
        return None

    def _can_use_vip_membership(self):
        wallet_manager = WalletManager()
        if self.pay_method != wallet_pb.Transaction.WALLET:
            return False
        if not wallet_manager.is_vip_membership(self.user.id):
            return False
        if not wallet_manager.is_merchant_enable_vip_membership_payment(self.merchant, self.merchant_vip_membership_config):
            return False
        return True

    def check_fee_matched(self):
        if len(self.order.coupon_ids) > 0:
            # 新版券的核验逻辑
            self.check_coupon_fee_matched()
            return
        order_bill_fee = self.order.bill_fee
        order_paid_fee = self.order.paid_fee - self.order.ifeedu_fee - self.order.giving_fee - self.coupon_fee
        # 因为用券的情况下导致待支付金额为0，那么设置待支付金额为0
        if order_paid_fee <= 0 and self.coupon_fee > 0:
            order_paid_fee = 0
        if self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            if not self.store.enable_fanpiao:
                raise errors.ShowError("商家已关闭卡包支付,请咨询商家")
            fanpiao_pay_manager = FanpiaoPayManager(merchant=self.merchant)
            order_paid_fee = fanpiao_pay_manager.get_show_order_paid_fee(self.order, self.user.id).fee
        if self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            # 使用会员卡余额支付,必须全额支付.因为会员卡充值的时候已经有赠送金额了.后面也不会生成红包
            order_paid_fee = self.order.bill_fee

        phone_member_fee = self.calculate_phone_member_fee()
        if phone_member_fee:
            order_bill_fee = phone_member_fee.order_bill_fee
            order_paid_fee = phone_member_fee.order_paid_fee

        wechat_group_fee = self.calculate_wechat_group_fee()
        if wechat_group_fee:
            order_bill_fee = wechat_group_fee.order_bill_fee
            order_paid_fee = wechat_group_fee.order_paid_fee

        if self._can_use_vip_membership():
            discount = 100 - self.merchant_vip_membership_config.vip_membership_discount
            discount_config = FanpiaoPayManager(merchant=self.merchant).cal_discount_fee_no_discount_fee(self.order)
            order_paid_fee = math.ceil(discount_config.discount_fee * discount / 100) + discount_config.no_discount_fee

        message = f"""
        扫码点餐prepay(单位: 分): {self.order.id}
        前端账单总金额: {self.bill_fee}
        前端账单总支付: {self.paid_fee}
        后端账单总金额: {order_bill_fee}
        后端账单总支付: {order_paid_fee}
        使用投喂总金额: {self.order.ifeedu_fee}
        """
        logger.info(message)
        if order_bill_fee != self.bill_fee + self.order.ifeedu_fee:
            raise errors.PaidfeeBillfeeNotMatch()
        if (self.pay_method != wallet_pb.Transaction.WALLET and order_paid_fee != self.paid_fee) or (
            self.pay_method == wallet_pb.Transaction.WALLET
            and (order_paid_fee != self.paid_fee and order_paid_fee != int(self.paid_fee * discount / 100 + 0.5))
        ):
            raise errors.PaidfeeBillfeeNotMatch()

    # def set_cannot_add_dish(self):
    #     """设置三分钟内不能加菜"""
    #     # 设置天阙过期时间，一定要小于订锁的时间
    #     # 该锁需要在支付回调里被释放
    #     if self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER:
    #         key = CacheServerKeys.get_order_paying_cache_key(self.order.id)
    #         self.redis_client.set(key, '1', ex=180)

    def prepay(self, check_dish_remain=True):
        '''个人扫码点餐'''
        if not self.order:
            raise errors.OrderNotFound()
        if self.order.status == dish_pb.DishOrder.PAID:
            raise errors.OrderAlreadyPaid()
        if self.order.status == dish_pb.DishOrder.POS_PAID:
            raise errors.OrderAlreadyPaid()
        if self.order.is_cannot_pay_order:
            raise errors.OrderCannotPay()
        self.check_prepay_repeat()
        # 检查支付组合是否合法
        self.check_pay_method_valid()
        self.check_fee_matched()
        # self.set_cannot_add_dish()

        manager = OrderManager(merchant=self.merchant, business_obj=self)
        third_order = manager.get_order_detail_info(self.order)
        pos_type = self.registration_info.pos_type
        if pos_type == registration_pb.OrderingServiceRegistrationInfo.KERUYUN:
            if third_order and third_order.order_info.origin_amount != self.order.bill_fee:
                raise errors.OrderUpdated()
        elif pos_type == registration_pb.OrderingServiceRegistrationInfo.SHILAI:
            if third_order:
                logger.info("时来收银机订单: {} {} {}".format(self.order.id, third_order.get("totalBillFee"), self.order.bill_fee))
                if third_order.get("totalBillFee") != self.order.bill_fee:
                    raise errors.OrderUpdated()
                if third_order.get("status") == "PAID":
                    raise errors.OrderAlreadyPaid()
                if third_order.get("status") not in ["APPROVED", "CONFIRMED"]:
                    raise errors.Error(err=error_codes.ORDER_NOT_CONFIRMED)
        table_manager = TableManager(merchant=self.merchant)
        table_manager.check_table_status(self.order.table_id)
        if check_dish_remain:
            self.check_dish_remain_quantity(self.order, order_manager=manager)
        self.transaction = TransactionManager().handle_self_dining_ordering_payment_prepay(
            self.user.id, self.merchant.id, self.store.id, self.pay_method, self.bill_fee, self.paid_fee, self.coupon_id
        )
        return self.handle_prepay()

    def handle_prepay(self):
        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 采用微信支付
            result = payment_manager.prepay(transaction=self.transaction, order_id=self.order.id, order=self.order)
            logger.info(f'微信支付prepay: {result}')
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = payment_manager.prepay(
                transaction=self.transaction,
                return_url=self.return_url,
                order_id=self.order.id,
                user_id=self.user.id,
                order=self.order,
                registration_info=self.registration_info,
            )
            logger.info(f'支付宝支付prepay: {result}')
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            result = payment_manager.prepay(transaction=self.transaction, order_id=self.order.id, order=self.order)
            logger.info(f'时来钱包支付prepay: {result}')
        elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            # 同一个orderId如果使用饭票支付的话需要限制1分钟只处理一次,避免网络延迟导致的重复支付
            result = payment_manager.prepay(
                transaction=self.transaction, order=self.order, registration_info=self.registration_info
            )
            logger.info(f"时来饭票支付prepay: {result}")
        elif self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            result = payment_manager.prepay(transaction=self.transaction, order=self.order)
            logger.info(f"会员储值支付prepay: {result}")
        elif self.pay_method == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_PAY:
            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        else:
            raise errors.PayMethodNotSupport()
        return self.handle_prepay_success(result)

    def handle_prepay_success(self, result):
        if result.get("errcode") == error_codes.SUCCESS:
            issue_scene = red_packet_pb.RedPacket.SCAN_CODE_ORDER
            status = red_packet_pb.RedPacket.BEFORE_NORMAL
            self.generate_red_packet(issue_scene=issue_scene, status=status)

            logger.info('prepay success: {}, {}'.format(self.order.id, self.transaction.id))
            if self.pay_method == wallet_pb.Transaction.WALLET:
                # 时来钱包
                self.order.transaction_id = self.transaction.id
                self.notification()
                result.update({"transactionId": self.transaction.id})
            elif self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                # 微信
                self.redis_client.set(name=self.transaction.id, value=self.order.id, ex=300)
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                # 支付宝
                self.redis_client.set(name=self.transaction.id, value=self.order.id, ex=300)
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
                FanpiaoHelper().set_user_fanpiao_purchase_risk_control(user=self.user, merchant=self.merchant)
            elif self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
            elif self.pay_method == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()

            if self.red_packet:
                result.update({"redPacketValue": self.red_packet.total_value})
            result['transactionId'] = self.transaction.id
            if "channelId" not in result:
                result.update({"channelId": "", "childNo": ""})
            logger.info("prepay result: {}".format(result))
        return result

    def union_pay_prepay(self):
        """第二版合并支付."""
        table_manager = TableManager(merchant=self.merchant)
        transaction_manager = TransactionManager()
        table_manager.check_table_status(self.order.table_id)
        self.transaction = transaction_manager.handle_self_dining_ordering_payment_prepay(
            self.user.id,
            self.merchant.id,
            self.store.id,
            self.pay_method,
            self.bill_fee,
            self.paid_fee,
            "",
        )
        ordering_da = OrderingServiceDataAccessHelper()
        self.order.transaction_id = self.transaction.id
        # self.update_order_remain_quantity()
        ordering_da.add_or_update_order(self.order)

    def union_pay_notification(self, transaction):
        """第二版合并支付."""
        self.transaction = transaction
        ordering_da_helper = OrderingServiceDataAccessHelper()
        ordering_da_helper.update_order(id=self.order.id, status=dish_pb.DishOrder.PAYING)
        if self._order_manager_v2:
            self._order_manager_v2.transactions = [self.transaction]
            self._order_manager_v2.update_order_operation_record_pay_order(type=wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT)
        registration_info = ordering_da_helper.get_registration_info(self.merchant.id)
        # 处理扫码点餐的逻辑
        self.handle_ordering(registration_info, union_pay=True)

    def notification(self, union_pay=False, ordering_retry=True, reordering=None, failed_order=None, decrease_dish_remain=True):
        if not union_pay:
            self.check_order_repeat_pay()
        self.set_no_pos_paid()
        # 把订单状态改为支付中的状态
        ordering_da_helper = OrderingServiceDataAccessHelper()
        ordering_da_helper.update_order(id=self.order.id, status=dish_pb.DishOrder.PAYING)
        registration_info = ordering_da_helper.get_registration_info(self.merchant.id)
        if self._order_manager_v2:
            self._order_manager_v2.transactions = [self.transaction]
            self._order_manager_v2.update_order_operation_record_pay_order(type=wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT)
        # 处理扫码点餐的逻辑
        self.handle_ordering(
            registration_info,
            union_pay=union_pay,
            ordering_retry=ordering_retry,
            reordering=reordering,
            failed_order=failed_order,
        )
        self.consume_coupon()
        if not union_pay:
            self.clear_order_repeat_pay_key()
        if decrease_dish_remain:
            self.decrease_dish_remain_quantity(self.order)
        self.release_order_lock(self.order)
        MultiPartyDinningManager(order=self.order).payment_event()

    def check_order_repeat_pay(self):
        """对于需要接收回调来处理逻辑的支付方式,根据orderId来做去重判断.
        如果已经有一个用户在支付了,则给后来支付的用户退款
        """
        check_pay_methods = [wallet_pb.Transaction.WECHAT_PAY, wallet_pb.Transaction.ALIPAY, wallet_pb.Transaction.TIAN_QUE_PAY]
        key = CacheServerKeys.get_order_repeat_pay_key(self.order)
        if self.redis_client.set(key, 1, ex=6000, nx=True):
            return
        else:
            if self.transaction.pay_method not in check_pay_methods:
                return
            logger.info("订单处理中: {}".format(self.order.id))
            raise errors.Error(err=error_codes.ORDER_DEALING)

    def clear_order_repeat_pay_key(self):
        key = CacheServerKeys.get_order_repeat_pay_key(self.order)
        self.redis_client.delete(key)

    def clear_transfer_fee(self, manager):
        # 清空补贴金额,后面重新走下单逻辑的时候会重新设置补贴
        payment_manager = PaymentManager(pay_method=self.transaction.WECHAT_PAY, merchant=self.merchant)
        payment_manager.clear_tansfer_fee(manager.transaction)

    def partial_refund_ordering(self, manager):
        """部分退款完成之后调用
        :manager: OrderManagerV2
        """
        order_manager = OrderManager(merchant=self.merchant, business_obj=self)

        self._cal_platform_discount(registration_info=manager.registration_info)
        # 计算新版用券补贴
        self._cal_coupon_subsidy_fee(registration_info=manager.registration_info)
        # 计算时享会员补贴
        self._cal_vip_membership_subsidy_fee(registration_info=manager.registration_info)
        # self._cal_fanpiao_platform_discount(manager.registration_info)
        self._cal_extra_platform_discount()
        # issue_scene = red_packet_pb.RedPacket.SCAN_CODE_ORDER
        # status = red_packet_pb.RedPacket.BEFORE_NORMAL
        # self.generate_red_packet(issue_scene=issue_scene, status=status)
        order_manager.pay_order(partial_refund=self.partial_refund, platform=manager.platform)
        self.confirm_ordering()

    def handle_ordering(self, registration_info, union_pay=False, ordering_retry=True, reordering=None, failed_order=None):
        """扫码点餐支付成功后,处理相应的业务逻辑
        1. 下单,并通知第三方用户已支付
        2. 更新transaction状态
        3. 更新order状态
        4. 生成红包
        """
        manager = OrderManager(merchant=self.merchant, business_obj=self)
        try:
            # 券包补贴
            self._cal_platform_discount(registration_info=registration_info)
            # 饭票补贴
            self._cal_fanpiao_platform_discount(registration_info)
            # 计算新版用券补贴
            self._cal_coupon_subsidy_fee(registration_info=registration_info)
            # 计算时享会员补贴
            self._cal_vip_membership_subsidy_fee(registration_info=registration_info)
            # 分享券补贴
            # self._cal_invite_share_coupon_platform_discount()
            # 部分商户做额外补贴
            self._cal_extra_platform_discount()
            self.order.coupon_fee = self.coupon_fee
            self.order.transaction_id = self.transaction.id
            logger.info("调用manager.pay_order: order_id={}, transaction_id={}".format(self.order.id, self.transaction.id))
            manager.pay_order(reordering=reordering, failed_order=failed_order)
        except errors.KeruyunTableInDining as e:
            self.deal_auto_refund(e)
            raise e
        except Exception as e:
            self.deal_auto_refund(e)
            raise e
        self.confirm_ordering(union_pay=union_pay)
        try:
            manager.send_order_to_data_center(self.order)
        except Exception as ex:
            logger.exception("{}: {}".format(self.order.id, ex))

    def confirm_ordering(self, union_pay=False):
        transaction_manager = TransactionManager()
        ordering_da = OrderingServiceDataAccessHelper()
        payment_manager = PaymentManager(pay_method=self.transaction.WECHAT_PAY, merchant=self.merchant)
        red_packet_da = RedPacketDataAccessHelper()

        # 1. 更新transaction的状态为成功
        # 2. 更新支付时间
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        transaction_manager.update_transaction(self.transaction)

        self.order.transaction_id = self.transaction.id
        self.order.status = dish_pb.DishOrder.PAID
        self.order.paid_time = date_utils.timestamp_second()
        self.order.user_id = self.transaction.payer_id
        if self.red_packet:
            self.order.red_packet_fee = (
                self.red_packet.total_value if self.red_packet.status != red_packet_pb.RedPacket.CANCELLED else 0
            )
        self.order.paid_fee = self.transaction.paid_fee
        ordering_da.add_or_update_order(no_check=True, order=self.order)

        if not union_pay and not self.transaction.ledgered:
            payment_manager.scan_code_order_launch_ledger(self.transaction, order=self.order)
        payment_manager.update_scan_code_order_merchant_transfer_fee(self.order, self.transaction)

        if self.red_packet:
            if self.red_packet.status != red_packet_pb.RedPacket.CANCELLED:
                self.red_packet.status = red_packet_pb.RedPacket.NORMAL
            red_packet_manager = RedPacketManager()
            red_packet_manager.open_red_packet_2(self.transaction, self.red_packet)
            red_packet_da.add_or_update_red_packet(self.red_packet)

        self.consume_coupon_v2()
        self.deal_coupon_as_group_purchase()

        if self.order.meal_code != '':
            subscribe_message_send.take_away_subscribe_message(
                self.user.wechat_profile.openid, self.order.meal_code, self.store.name
            )
        if (
            hasattr(self, "coupon_category")
            and self.coupon_category.issue_scene == coupon_category_pb.CouponCategory.INVITE_SHARE
        ):
            sm = SubscribeMessage()
            sm.invite_share_reward_subscribe_message(self.transaction.use_coupon_id, invitee=self.user, merchant=self.merchant)

        shopping_card_manager = ShoppingCardManager(merchant=self.merchant)
        shopping_card_manager.remove_shopping_card(table_id=self.order.table_id)
        shopping_card_manager.send_order_paid_message(self.order)

    def deal_auto_refund(self, e):
        failed_order = dish_pb.FailedOrder()
        failed_order.id = self.order.id
        failed_order.transaction_id = self.transaction.id
        failed_order.request_path = request.path
        failed_order.user_id = self.transaction.payer_id
        failed_order.merchant_id = self.order.merchant_id
        failed_order.exception_message = str(e)
        failed_order.errcode = e.errcode
        failed_order.ordering_service_order_id = self.order.ordering_service_order_id
        failed_order.ordering_service_trade_id = self.order.ordering_service_trade_id
        now = int(time.time())
        failed_order.timestamp = now
        ordering_da = OrderingServiceDataAccessHelper()
        if e.errcode in [
            error_codes.KERUYUN_SYNC_ORDER_ERROR[0],
            error_codes.KERUYUN_BUSINESS_EXCEPTION[0],
            error_codes.KERUYUN_DUPLICATE_KEY_ERROR[0],
            error_codes.REQUESTS_TIMEOUT[0],
        ]:
            logger.info(f"添加订单为失败订单: {self.order.id} {e.errmsg}")
            ordering_da.add_or_update_failed_order(failed_order)
            return
        if isinstance(e, errors.KeruyunCreateOrderTimeout):
            payment_manager = PaymentManager(self.transaction.pay_method, merchant=self.merchant)
            payment_manager.ordering_refund(self.transaction, self.order)
            logger.info('创建订单超时,发起自动退款: {} {}'.format(self.transaction.id, self.order.id))
            # failed_order.status = dish_pb.FailedOrder.REFUNDED
        elif isinstance(e, errors.KeruyunTableInDining):
            payment_manager = PaymentManager(self.transaction.pay_method, merchant=self.merchant)
            payment_manager.ordering_refund(self.transaction, self.order)
            logger.info('当前桌台就餐中,发起自动退款: {} {}'.format(self.transaction.id, self.order.id))
            # failed_order.status = dish_pb.FailedOrder.REFUNDED
        else:
            logger.info("下单失败,支付方式为: {} {} {}".format(self.transaction.pay_method, self.transaction.id, self.order.id))
            if self.transaction.pay_method in [
                wallet_pb.Transaction.WECHAT_PAY,
                wallet_pb.Transaction.ALIPAY,
                wallet_pb.Transaction.WALLET,
                wallet_pb.Transaction.FANPIAO_PAY,
            ]:
                logger.info('下单失败,发起退款: {} {}'.format(self.transaction.id, self.order.id))
                payment_manager = PaymentManager(self.transaction.pay_method, merchant=self.merchant)
                payment_manager.ordering_refund(self.transaction, self.order)
                # failed_order.status = dish_pb.FailedOrder.REFUNDED
            else:
                logger.info('下单失败,不发起退款: {} {}'.format(self.transaction.id, self.order.id))

# -*- coding: utf-8 -*-

"""
调用数据中心的接口
"""

import os
import logging
import requests

from google.protobuf import json_format

from common import http
import proto.ordering.dish_pb2 as dish_pb
import proto.finance.wallet_pb2 as wallet_pb
from business_ops.ordering.constants import DataCenterConstants
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

logger = logging.getLogger(__name__)


class DataCenterHelper:
    def __init__(self, **kargs):
        self._order = kargs.get("order")
        self._order_id = kargs.get("order_id")
        self._order_operation_record = kargs.get("order_operation_record")

    @property
    def order_operation_record(self):
        if self.order is None:
            return None
        order_da = OrderingServiceDataAccessHelper()
        self._order_operation_record = order_da.get_order_operation_record(id=self.order.id)
        return self._order_operation_record

    @order_operation_record.setter
    def order_operation_record(self, order_operation_record):
        self._order_operation_record = order_operation_record

    @property
    def order(self):
        if self._order:
            return self._order
        if not self._order_id:
            return None
        order_da = OrderingServiceDataAccessHelper()
        self._order = order_da.get_order(id=self.order_id)
        return self._order

    @order.setter
    def order(self, order):
        self._order = order

    def __get_domain(self):
        env = os.environ.get("DEPLOYMENT_ENV")
        if env == "test":
            return DataCenterConstants.TEST_URL
        return DataCenterConstants.BASE_URL

    def generate_url(self, uri):
        domain = self.__get_domain()
        outer_version = os.environ.get("OUTER_VERSION", "v1.6")
        url = "{}/{}{}".format(domain, outer_version, uri)
        # logger.info(f"数据中心 generate url: {url}")
        return url

    def do_post(self, uri, params):
        url = self.generate_url(uri)
        logger.info(f"数据中心请求信息：url={url}, json_data={params}")
        resp = http.post(url, json=params, timeout=30)
        data = resp.json()
        logger.info(f"数据中心响应信息：url={url}, reponse={data}")
        # logger.info("DataCenterHelper: {} \n {} \n {}".format(url, params, ret))
        return data

    def import_order(self, id, order):
        """导入订单到数据中心
        按数据中心需要的结构传输数据
        """
        params = {"id": id, "order": order}
        uri = DataCenterConstants.IMPORT_ORDER
        return self.do_post(uri, params)

    def get_order_list(self, merchant_id, user_id, create_time, status=None):
        params = {
            "userId": user_id,
            "merchantId": merchant_id,
            "createTime": create_time,
            "sortby": [("createTime", -1)],
            "status": status,
        }
        uri = DataCenterConstants.GET_ORDER_LIST
        return self.do_post(uri, params)

    def get_order(self, order_id):
        params = {"id": order_id}
        uri = DataCenterConstants.GET_ORDER
        return self.do_post(uri, params)

    def send_order_to_data_center(self, order=None, source=None, **kargs):
        """订单支付完成后需要把订单信息同步到数据中心"""
        if order is None:
            order = self.order
        status = dish_pb.DishOrder.OrderStatus.Name(order.status)
        paid_fee = 0
        if order.status == dish_pb.DishOrder.PAID:
            paid_fee = order.paid_fee
        data = {
            # 订单号
            "id": order.id,
            # 商户ID
            "merchantId": order.merchant_id,
            # 门店ID
            "storeId": order.store_id,
            # 订单总金额
            "totalBillFee": order.bill_fee,
            # 订单菜品总金额
            "totalDishBillFee": order.bill_fee,
            # 菜品优惠金额
            "totalDishPromotionFee": order.discount_amount,
            # 饭票优惠总金额: 饭票支付的话,用transaction.bill_fee - transaction.paid_fee
            "fanpiaoPromotionFee": 0,
            # 用户支付总金额
            "totalPaidFee": paid_fee,
            # 红包返现
            "redPacketFee": order.red_packet_fee,
            # 补贴总金额
            "totalSubsidyFee": order.platform_discount_fee,
            # 饭票佣金
            "fanpiaoCommissionFee": order.fanpiao_commission_fee,
            # 券包佣金
            "couponPackageCommissionFee": order.coupon_package_commission_fee,
            # 新版券的佣金
            "couponCommissionFee": order.coupon_commission_fee,
            # 设置订单的券列表
            "couponIds": list(order.coupon_ids),
            # 钱包支付佣金
            "walletCommissionFee": order.wallet_commission_fee,
            # 券包优惠金额
            "couponPackagePromotionFee": order.coupon_fee,
            # 商家实收
            "totalReceivableFee": order.paid_in_fee,
            # 商家实际总实收
            "actualTotalReceivableFee": order.actual_total_receivable_fee,
            # 流水号
            "serialNumber": order.ordering_service_serial_number,
            # 取餐号
            "mealCode": order.meal_code,
            # 创建时间
            "createTime": order.create_time,
            # 支付时间
            "payTime": order.paid_time,
            # 第三方平台的订单ID
            "orderingServiceOrderId": order.ordering_service_order_id,
            # 订单来源
            "source": 'NUMBER_PLATE_PAY' if order.meal_type == dish_pb.DishOrder.DIRECT_PAY else "MINI_PROGRAM",
            "numberPlate": "",
            "staffInfo": {"staffName": "openplatform"},
            "status": status,
            "isPartialRefund": order.is_partial_refund,
            "isTotalRefund": order.is_total_refund,
            # 备注信息
            "comment": order.remark,
            "settlementRate": order.settlement_rate,
            "shilaiServiceRate": order.shilai_service_rate,
            "redPacketShilaiDiscount": order.red_packet_shilai_discount,
            "redPacketDiscount": order.red_packet_discount,
        }
        if source is not None:
            data.update({"source": source})
        if order.meal_type == dish_pb.DishOrder.TAKE_OUT:
            shipping_address = json_format.MessageToDict(order.shipping_address, including_default_value_fields=True)
            data.update({"shippingAddress": shipping_address})
        order_operation_record = kargs.get("order_operation_record")
        if order_operation_record is not None:
            self.order_operation_record = order_operation_record
        data.update(
            {
                "transactionInfos": self.__data_center_order_transactions_info(order, data),
                "userInfo": self.__data_center_order_user_info(order),
                "items": self.__data_center_order_items_info(order),
                "partialRefundInfos": self.__data_center_order_partial_refund_record(order),
                "paymentInfos": self.__data_center_order_payment_infos(data, order),
                "dineInOrder": self.__data_center_order_table_info(order),
            }
        )
        if not order.ordering_service_serial_number:
            data.update({"serialNumber": str(order.serial_number)})
        self.import_order(order.id, data)
        return data

    def __deal_transaction(self, transaction):
        pay_method_name = self.__data_center_pay_method_name(transaction)
        transaction = json_format.MessageToDict(transaction, including_default_value_fields=True)
        transaction.update({"payMethodName": pay_method_name})
        return transaction

    def __data_center_order_payment_infos(self, data, order):
        if self.order_operation_record is None:
            data.update({"fanpiaoPromotionFee": order.bill_fee - order.paid_fee})
            return []
        ret = []
        fanpiao_promotion_fee = 0
        for record in self.order_operation_record.pay_records:
            transactions = []
            for transaction in record.transactions:
                if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                    fanpiao_promotion_fee = transaction.bill_fee - transaction.paid_fee
                transactions.append(self.__deal_transaction(transaction))
            ret.append(
                {
                    "transactions": transactions,
                    "payTime": record.pay_time,
                    "staffName": record.staff_name,
                    "type": wallet_pb.Transaction.TransactionType.Name(record.type),
                }
            )
        for record in self.order_operation_record.partial_refund_records:
            transactions = []
            for transaction in record.transactions:
                if transaction.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                    fanpiao_promotion_fee -= transaction.bill_fee - transaction.paid_fee
        for record in self.order_operation_record.refund_records:
            transactions = []
            for transaction in record.transactions:
                if transaction.type == wallet_pb.Transaction.ORDERING_REFUND:
                    fanpiao_promotion_fee = 0
                transactions.append(self.__deal_transaction(transaction))
            ret.append(
                {
                    "transactions": transactions,
                    "refundTime": record.refund_time,
                    "staffName": record.staff_name,
                    "backComment": record.back_comment,
                    "type": wallet_pb.Transaction.TransactionType.Name(record.type),
                }
            )
        data.update({"fanpiaoPromotionFee": fanpiao_promotion_fee})
        return ret

    def __data_center_order_partial_refund_record(self, order):
        """把部分退款的信息同步到数据中心"""
        ret = []
        if self.order_operation_record is None:
            return ret
        for record in self.order_operation_record.partial_refund_records:
            sub_items = self.__data_center_order_sub_items(record.items)
            sub_items_uuid = []
            for back_sub_items in list(sub_items.values()):
                for item in back_sub_items:
                    sub_items_uuid.append(item.uuid)
            items = []
            transactions = []
            for item in record.items:
                if item.uuid in sub_items_uuid:
                    continue
                item_info = self.__data_center_order_item_info(item, sub_items)
                items.append(item_info)
            for transaction in record.transactions:
                transactions.append(self.__deal_transaction(transaction))
            ret.append(
                {
                    "backItems": items,
                    "transactions": transactions,
                    "refundTime": record.refund_time,
                    "staffName": record.staff_name,
                    "backComment": record.back_comment,
                    "type": wallet_pb.Transaction.TransactionType.Name(record.type),
                    "operateTime": record.refund_time,
                }
            )
        return ret

    def __data_center_order_table_info(self, order):
        if order.table_id == "":
            return {}
        ordering_da = OrderingServiceDataAccessHelper()
        table = ordering_da.get_table(ordering_service_table_id=order.table_id)
        if not table:
            return {}
        table_info = {
            "tableId": table.id,
            "orderingServiceTableId": table.ordering_service_table_id,
            "tableName": table.name,
            "mealType": dish_pb.DishOrder.MealType.Name(table.meal_type),
            "peopleCount": order.people_count,
        }
        return table_info

    def __data_center_order_user_info(self, order):
        user_da = UserDataAccessHelper()
        user = user_da.get_user(order.user_id)
        if not user:
            return {}
        nickname = user.member_profile.nickname
        user_info = {"id": user.id, "nickname": nickname, "phone": user.member_profile.mobile_phone}
        return user_info

    def __data_center_pay_method_name(self, transaction, order=None):
        if transaction.pay_method in [wallet_pb.Transaction.WALLET, wallet_pb.Transaction.FANPIAO_PAY]:
            return "智能营销买单"
        if transaction.use_coupon_id != "":
            return "智能营销买单"
        if order is not None and order.coupon_ids:
            return "智能营销买单"
        if transaction.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            return "微信"
        if transaction.pay_method == wallet_pb.Transaction.ALIPAY:
            return "支付宝"
        if transaction.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            return "储值支付"
        if transaction.pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
            if transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                return "微信"
            if transaction.pay_channel == wallet_pb.Transaction.ALIPAY_CHANNEL:
                return "支付宝"
        return "扫码支付"

    def __data_center_order_transactions_info(self, order, data=None):
        """构建数据中心的订单的流水信息"""

        def set_transaction_info(transaction):
            type = wallet_pb.Transaction.TransactionType.Name(transaction.type)
            pay_method = transaction.pay_method
            if pay_method == wallet_pb.Transaction.TIAN_QUE_PAY:
                if transaction.pay_channel == wallet_pb.Transaction.WECHAT_CHANNEL:
                    pay_method = wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.WECHAT_PAY)
                else:
                    pay_method = wallet_pb.Transaction.PayMethod.Name(wallet_pb.Transaction.ALIPAY)
            else:
                pay_method = wallet_pb.Transaction.PayMethod.Name(transaction.pay_method)
            ret = {
                "id": transaction.id,
                "paidFee": transaction.paid_fee,
                "payTime": transaction.paid_time,
                "type": type,
                "payMethod": pay_method,
                "couponId": transaction.use_coupon_id,
                "payMethodName": self.__data_center_pay_method_name(transaction, order=order),
            }
            if transaction.type in [wallet_pb.Transaction.ORDERING_REFUND, wallet_pb.Transaction.PARTIAL_REFUND]:
                data.update({"refundTime": transaction.paid_time})
            return ret

        transaction_da = TransactionDataAccessHelper()
        transaction_infos = []
        dealed_transaction_ids = set()
        if order.transaction_id != "":
            transaction = transaction_da.get_transaction_by_id(order.transaction_id)
            if transaction:
                transaction_infos.append(set_transaction_info(transaction))

                # 部分退款的订单
                transactions = transaction_da.get_transactions(refunded_transaction_id=transaction.id)
                for transaction in transactions:
                    if transaction.id in dealed_transaction_ids:
                        continue
                    dealed_transaction_ids.add(transaction.id)
                    transaction_infos.append(set_transaction_info(transaction))

        if order.refund_transaction_id != "":
            transaction = transaction_da.get_transaction_by_id(order.refund_transaction_id)
            if transaction:
                if transaction.id not in dealed_transaction_ids:
                    dealed_transaction_ids.add(transaction.id)
                    transaction_infos.append(set_transaction_info(transaction))
        return transaction_infos

    def __data_center_order_items_info(self, order):
        """构建数据中心的订单的菜品信息"""
        items = []
        products = []
        for product in order.products:
            products.append(product)
        for add_product in order.add_products:
            for product in add_product.products:
                products.append(product)

        sub_products = self.__data_center_order_sub_items(products)
        for product in products:
            if product.parent_uuid != "":
                continue
            item = self.__data_center_order_item_info(product, sub_products)
            items.append(item)
        return items

    def __data_center_order_item_info(self, product, sub_products, parent_product=None):
        _sub_products = sub_products.get(product.uuid, [])
        sub_items = []
        sub_item_total_dish_bill_fee = 0
        sub_item_total_bill_fee = 0
        item = self.__product_to_item(product, parent_product=parent_product)
        for __sub_product in _sub_products:
            sub_item = self.__data_center_order_item_info(__sub_product, sub_products, parent_product=product)
            sub_items.append(sub_item)
            sub_item_total_dish_bill_fee += sub_item.get("totalDishBillFee")
            sub_item_total_bill_fee += sub_item.get("billFee")

        if len(sub_items):
            item.update({"dishType": "COMBO"})
        spec_option = self.__data_center_order_spec_option(product, item)
        attribute_options, packaging_boxs = self.__data_center_order_attribute_options(product, item)
        supply_condiment_sub_items = self.__data_center_order_supply_condiments_sub_items(product, item)
        sub_items.extend(supply_condiment_sub_items)
        total_dish_bill_fee = item.get("totalDishBillFee")
        bill_fee = item.get("billFee")
        # TODO: 套餐子菜本身就是铺开了的,所有不用再乘以数量
        total_dish_bill_fee += sub_item_total_dish_bill_fee * item.get("quantity")
        bill_fee += sub_item_total_bill_fee * item.get("quantity")
        item.update(
            {
                "specOptions": [spec_option],
                "attributeOptions": attribute_options,
                "packagingBox": packaging_boxs,
                "subItems": sub_items,
                "imageUrl": product.image_url,
                "totalDishBillFee": int(total_dish_bill_fee),
                "billFee": int(bill_fee),
                "batchNumber": product.batch_number,
                "uuid": product.uuid,
            }
        )
        return item

    def __data_center_order_spec_option(self, product, item):
        spec_option = {}
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.SPECIFICATION:
                spec_option = {"name": attr.name, "reprice": attr.reprice, "groupName": attr.group_name}
                break
        if not spec_option:
            return spec_option
        quantity = item.get("quantity")
        item.update(
            {
                "billFee": item.get("billFee", 0) + spec_option.get("reprice") * quantity,
                "totalDishBillFee": item.get("totalDishBillFee", 0) + spec_option.get("reprice") * quantity,
            }
        )
        return spec_option

    def __data_center_order_attribute_options(self, product, item):
        attribute_options = []
        packaging_boxs = {}
        bill_fee = 0
        total_dish_bill_fee = 0
        for attr in product.attrs:
            if attr.type == dish_pb.Attr.SPECIFICATION:
                continue
            if attr.type == dish_pb.Attr.TAKE_AWAY:
                packaging_boxs = {
                    "name": attr.name,
                    "price": attr.reprice,
                    "quantity": 1,
                    "totalPackagingFee": attr.reprice,
                    "isPacked": True,
                }
            else:
                attribute_options.append({"name": attr.name, "reprice": attr.reprice, "groupName": attr.group_name})
                bill_fee += attr.reprice
                total_dish_bill_fee += attr.reprice
        quantity = item.get("quantity")
        item.update(
            {
                "billFee": item.get("billFee", 0) + bill_fee * quantity,
                "totalDishBillFee": item.get("totalDishBillFee", 0) + total_dish_bill_fee * quantity,
            }
        )
        return attribute_options, packaging_boxs

    def __data_center_order_supply_condiments_sub_items(self, product, item):
        """把加料组装成sub_items"""
        sub_items = []
        bill_fee = 0
        total_dish_bill_fee = 0
        for sc in product.supply_condiments:
            quantity = sc.quantity / product.quantity
            sub_items.append(
                {
                    "id": sc.id,
                    "name": sc.name,
                    "price": sc.market_price,
                    "dishType": "ADDON",
                    "billFee": sc.market_price * quantity,
                    "quantity": quantity,
                }
            )
            bill_fee += sc.market_price * quantity
            total_dish_bill_fee += sc.market_price * quantity
        quantity = item.get("quantity")
        item.update(
            {
                "billFee": item.get("billFee", 0) + bill_fee * quantity,
                "totalDishBillFee": item.get("totalDishBillFee", 0) + total_dish_bill_fee * quantity,
            }
        )
        return sub_items

    def __data_center_order_sub_items(self, products):
        products = {p.uuid: p for p in products}
        sub_items = {}
        for uuid, product in products.items():
            parent_uuid = product.parent_uuid
            if parent_uuid == "":
                continue
            products = sub_items.get(parent_uuid, [])
            products.append(product)
            sub_items.update({parent_uuid: products})
        return sub_items

    def __product_to_item(self, product, parent_product=None):
        quantity = product.quantity
        parent_product_quantity = 1
        if parent_product:
            parent_product_quantity = parent_product.quantity
        quantity = quantity / parent_product_quantity
        bill_fee = product.discount_price * quantity
        total_dish_bill_fee = product.price * quantity
        child_dish_price = product.child_dish_price * quantity
        item = {
            "dishId": product.id,
            "name": product.name,
            "unit": product.unit,
            # 原价
            "price": product.price,
            # 减免后的价格
            "discountPrice": product.discount_price,
            "billFee": bill_fee + child_dish_price,
            "quantity": quantity,
            "dishType": dish_pb.Dish.DishType.Name(product.type),
            "imageUrl": product.image_url,
            "totalDishBillFee": total_dish_bill_fee,
        }
        return item

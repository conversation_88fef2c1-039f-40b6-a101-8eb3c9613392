# -*- coding: utf-8 -*-


import proto.user_pb2 as user_pb
from business_ops.base_manager import BaseManager
from dao.merchant_phone_member import MerchantPhoneMemberDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper


class MerchantPhoneMemberManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(MerchantPhoneMemberManager, self).__init__(*args, **kargs)

    def import_merchant_phones(self, file=None, phones=None):
        if file is not None:
            self.import_from_file(file)
        if phones is not None:
            self.import_from_phones(phones)

    def import_from_file(self, file):
        while True:
            line = file.readline()
            if not line:
                break
            line = line.decode("utf8")
            if len(line) == 0:
                continue
            if line[-1] == "\n":
                line = line[:-1]
            self.import_from_phones(line)

    def import_from_phones(self, phones):
        phones = phones.split("|")
        merchant_phone_member_da = MerchantPhoneMemberDataAccessHelper()
        user_da = UserDataAccessHelper()
        for phone in phones:
            data = phone.split(",")
            p = data[0]
            discount = int(data[1])
            red_packet_discount_percentage = 0
            dish_discount_percentage = 100
            if len(data) > 2:
                dish_discount_percentage = int(data[2])
                red_packet_discount_percentage = int(data[3])
            phone_member = user_pb.MerchantPhoneMember()
            phone_member.merchant_id = self.merchant.id
            phone_member.phone = p
            phone_member.discount = 100 - discount
            phone_member.dish_discount_percentage = dish_discount_percentage
            phone_member.red_packet_discount_percentage = red_packet_discount_percentage
            user = user_da.get_users_by_condition(phone=p)
            if user:
                user = user[0]
                phone_member.user_id = user.id
            merchant_phone_member_da.add_or_update_merchant_phone_member(phone_member)

    def bind_phone_member_to_user(self, phone, user_id):
        merchant_phone_member_da = MerchantPhoneMemberDataAccessHelper()
        phone_members = merchant_phone_member_da.get_merchant_phone_members(phone=phone)
        for phone_member in phone_members:
            phone_member.user_id = user_id
            merchant_phone_member_da.add_or_update_merchant_phone_member(phone_member)

    def get_merchant_phone_member(self, user_id=None):
        user = None
        if user_id is None:
            user = self.user
        else:
            user = UserDataAccessHelper().get_user(user_id=user_id)
        if not user:
            return None
        phone = user.member_profile.mobile_phone
        if not phone:
            return None
        merchant_phone_member_da = MerchantPhoneMemberDataAccessHelper()
        phone_member = merchant_phone_member_da.get_merchant_phone_member(
            phone=phone, merchant_id=self.merchant.id)
        if not phone_member:
            return None
        if phone_member.status == user_pb.MerchantPhoneMember.INVALID:
            return None
        return phone_member

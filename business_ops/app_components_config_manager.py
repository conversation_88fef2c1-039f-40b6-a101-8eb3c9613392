# -*- coding: utf-8 -*-

import logging

import proto.merchant_rules_pb2 as merchant_rules_pb
from dao.app_component_config_da_helper import AppComponentConfigDataAccessHelper

logger = logging.getLogger(__name__)


class AppComponentConfigManager:

    @property
    def app_component_da(self):
        if self._app_component_da is None:
            self._app_component_da = AppComponentConfigDataAccessHelper()
        return self._app_component_da

    def __init__(self):
        self._app_component_da = None

    def create_app_component_config(self, promotion_type):
        app_component_config = merchant_rules_pb.AppComponentConfig()
        app_component_config.promotion_type = promotion_type
        return app_component_config

    def add_or_update_app_component_config(self, app_component_config):
        self.app_component_da.add_or_update_app_component_config(app_component_config)

    def get_app_component_config(self, promotion_type):
        app_component_config = self.app_component_da.get_app_component_config(promotion_type)
        return app_component_config

    def default_app_component_config(self):
        app_component_config = merchant_rules_pb.AppComponentConfig()
        app_component_config.promotion_type = merchant_rules_pb.Merchant.DINING
        app_component_config.ticket_package_name = "饭票"
        return app_component_config

    def get_app_component_config_or_default(self, promotion_type):
        return self.get_app_component_config(promotion_type) or self.default_app_component_config()

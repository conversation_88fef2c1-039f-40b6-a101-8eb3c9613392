from common.config import config
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from wechat_lib import template_message_api_helper

import common.utils.date_utils as date_utils

# 模板消息帮助类
class TemplateMessageHelper():

    def __init__(self):
        pass


    def send_member_card_registered_message(self,
                                            merchant_id,
                                            user_id,
                                            form_id):
        """
            发送会员注册完成消息

            :param merchant_id: (String) 商户ID
            :param user_id: (String) 用户ID
            :param form_id: (String) 表单ID
        """
        user = UserDataAccessHelper().get_user(user_id)
        merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        member_cards = MembershipDataAccessHelper().get_member_cards(merchant_id=merchant_id,
                                                                     user_id=user_id)
        member_card = member_cards[0]

        # 会员卡开通成功通知
        # 店铺名称: keyword1
        # 卡号: keyword2
        # 姓名: keyword3
        # 开卡时间: keyword4
        template_id = "ZXI2jj48umlN3Le9I2I2WMOADT26K5kndBWNqhVOzrs"
        json_data = {
            "keyword1": { "value": merchant.basic_info.display_name },
            "keyword2": { "value": member_card.id },
            "keyword3": { "value": user.member_profile.name },
            "keyword4": { "value": date_utils.timestamp_second_to_date(user.joined_time) },
        }
        page = "pages/merchant/index?merchantId={}".format(merchant_id)

        template_message_api_helper.send_message(config.WECHAT_MINIPROGRAM_APPID,
                                                user.wechat_profile.openid,
                                                template_id=template_id,
                                                form_id=form_id,
                                                data=json_data,
                                                page=page)

    def send_payment_finish_message(self, merchant_id, user_id, prepay_id):
        """
            发送付款完成消息

            :param merchant_id: (String) 商户ID
            :param user_id: (String) 用户ID
            :param prepay_id: (String) 预付款ID
        """
        user = UserDataAccessHelper().get_user(user_id)

        template_message_api_helper.send_message(config.WECHAT_MINIPROGRAM_APPID,
                                                user.wechat_profile.openid,
                                                template_id="",
                                                form_id=prepay_id,
                                                data=None,
                                                page=None)

    def send_coupon_accepted_message(self, merchant_id, user_id, form_id):
        """
            发送领取优惠券消息

            :param merchant_id: (String) 商户ID
            :param user_id: (String) 用户ID
            :param form_id: (String) 表单ID
        """
        user = UserDataAccessHelper().get_user(user_id)

        template_message_api_helper.send_message(config.WECHAT_MINIPROGRAM_APPID,
                                                user.wechat_profile.openid,
                                                template_id="",
                                                form_id=form_id,
                                                data=None,
                                                page=None)

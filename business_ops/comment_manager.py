# -*- coding: utf-8 -*-


import proto.page.comment_pb2 as page_comment_pb
import proto.comment_pb2 as comment_pb
from common.utils import id_manager
from common.utils import date_utils
from dao.comment_da_helper import CommentDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper


class CommentManager:

    def create_comment(self, user_id, rating, comment, merchant_id, store_id, order_id):
        user_comment = comment_pb.UserComment()
        user_comment.id = id_manager.generate_common_id()
        user_comment.user_id = user_id
        user_comment.rating = rating
        user_comment.comment = comment
        user_comment.merchant_id = merchant_id
        user_comment.store_id = store_id
        if order_id is not None:
            user_comment.order_id = order_id
        user_comment.create_time = date_utils.timestamp_second()
        CommentDataAccessHelper().add_or_update_comment(user_comment)

    def get_comments(self, merchant_id, latest_create_time):
        comments = CommentDataAccessHelper().get_comments(merchant_id=merchant_id, latest_create_time=latest_create_time)
        comment_list_vo = page_comment_pb.CommentListVo()
        for comment in comments:
            comment_vo = comment_list_vo.comments.add()
            comment_vo.id = comment.id
            comment_vo.user_id = comment.user_id
            user = UserDataAccessHelper().get_user(comment.user_id)
            comment_vo.username = user.member_profile.nickname
            comment_vo.head_img_url = user.member_profile.head_image_url
            comment_vo.comment = comment.comment
            comment_vo.merchant_id = comment.merchant_id
            comment_vo.store_id = "{}_0".format(comment.merchant_id)
            comment_vo.rating = comment.rating
            comment_vo.create_time = comment.create_time
            comment_vo.order_id = comment.order_id
        return comment_list_vo

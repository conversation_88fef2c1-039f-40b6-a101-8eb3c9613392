# -*- coding: utf-8 -*-

from io import BytesIO
import logging

import pandas as pd

import proto.ordering.keruyun.keruyun_member_card_balance_pb2 as keruyun_member_card_balance_pb
from business_ops.base_manager import BaseManager
from dao.membership_da_helper import MembershipDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper

logger = logging.getLogger(__name__)


class MembershipManager(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self._increase = kargs.get("increase", False)
        self._membership_da = None
        self._user_da = None

    @property
    def increase(self):
        return self._increase

    @property
    def membership_da(self):
        if self._membership_da is not None:
            return self._membership_da
        self._membership_da = MembershipDataAccessHelper()
        return self._membership_da

    @property
    def user_da(self):
        if self._user_da is not None:
            return self._user_da
        self._user_da = UserDataAccessHelper()
        return self._user_da

    def import_membership_balance(self, phones=None, file=None):
        error_phones = None
        if file is not None:
            error_phones = self.import_from_file(file)
        elif phones is not None:
            self.import_from_phones(phones)
        return error_phones
    
    def export_membership_balance(self, title='用户储值'):
        vip_user = {}
        output = BytesIO()
        writer = pd.ExcelWriter(output, engine='openpyxl')
        columns = ['电话', '余额(元)', '导入(元)', '状态']
        datas = []
        keruyun_member_cards = self.membership_da.get_keruyun_member_cards(merchant_id=self.merchant.id)
        if keruyun_member_cards:
            member_cards = self.membership_da.get_member_cards(merchant_id=self.merchant.id)
            user_ids = [member_card.user_id for member_card in member_cards]
            users = {}
            for user in self.user_da.get_users_by_ids(user_ids=user_ids):
                users.update({user.id:user})
            for member_card in member_cards:
                user = users.get(member_card.user_id)
                if not user:
                    continue
                phone = user.member_profile.mobile_phone
                vip_user.update({phone:member_card})
            for item in keruyun_member_cards:
                phone = item.phone
                datas.append({
                    columns[0]: phone,
                    columns[1]: (vip_user.get(phone).balance if vip_user.get(phone) else 0) / float(100),
                    columns[2]: item.balance/float(100),
                    columns[3]: '未授权' if item.status=='UNMERGE' else '已授权',
                })
        df = pd.DataFrame(datas, columns=columns)
        df.to_excel(writer, title, header=True, index=False)
        writer.close()
        output.seek(0)
        return output

    def import_from_file(self, file):
        error_phones = []
        for line in file.readlines():
            try:
                line = line.strip().decode()
                if not line:
                    continue
                self.import_membership_balance_with_phone(*line.split(","))
            except Exception as ex:
                logger.exception(f"line={line}, error_msg={ex}")
                error_phones.append(f"{line}")

        return error_phones

    def import_from_phones(self, phones):
        for data in phones:
            phone = data.get("phone")
            balance = data.get("balance")
            self.import_membership_balance_with_phone(phone, balance)

    def import_membership_balance_direct(self, phone, balance):
        """ 如果根据手机号可以查出来惟一的一个微信用户,那么导入到这个微信用户账号下
        """
        user = self.user_da.get_user_by_condition(phone=phone)
        if not user:
            return False
        account = self.membership_da.get_member_card(
            user_id=user.id, merchant_id=self.merchant.id)
        if not account:
            return False
        msg = f""" 修改前
        用户: {user.member_profile.nickname}
        商户: {self.merchant.basic_info.display_name}
        余额: {account.balance}分
        """
        logger.info(msg)
        if self.increase:
            account.balance += self.__get_real_balance(balance)
        else:
            account.balance = self.__get_real_balance(balance)
        self.membership_da.update_or_create_member_card(account)
        msg = f"""
        修改后
        用户: {user.member_profile.nickname}
        商户: {self.merchant.basic_info.display_name}
        余额: {account.balance}分
        """
        logger.info(msg)
        return True

    def import_membership_balance_with_phone(self, phone, balance):
        """ 如果本身手机号没有绑定到任何一个用户,那么需要导入到临时表
        """
        if self.import_membership_balance_direct(phone, balance):
            return True
        brand_id = self.merchant.brand_info.id
        if brand_id == "":
            brand_id = None
        obj = keruyun_member_card_balance_pb.KeruyunMemberCardBalance()
        obj.phone = str(phone).strip()
        obj.balance = self.__get_real_balance(balance)
        obj.merchant_id = self.merchant.id
        obj.brand_id = self.merchant.brand_info.id
        self.membership_da.add_or_update_keruyun_member_card_balance(obj)
        print("导入用户储值金额: {}".format(obj))

    def __get_real_balance(self, balance) -> int:
        return int(float(str(balance).strip()) * 100)

# -*- coding: utf-8 -*-

import logging
import json

from google.protobuf import json_format

import proto.ordering.dish_pb2 as dish_pb
from business_ops.base_manager import BaseManager
from business_ops.message_manager import MessageManager
from common.cache_server_keys import CacheServerKeys
from common.utils import date_utils
from common.utils import id_manager
from cache.redis_client import RedisClient
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class ShoppingCardManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(ShoppingCardManager, self).__init__(*args, **kargs)
        order_manager = kargs.get("order_manager")
        self.order_manager = order_manager
        self.table_id = kargs.get("table_id")
        self._update_shopping_card = False
        self._message_code = MessageManager.SHOPPING_CARD_MODIFIED
        self.shopping_card = None
        # 不给这一批用户发消息
        self._exclude_user_ids = kargs.get("exclude_user_ids", [])

    def create_shopping_card(self, table, dishes_dict, people_count=1):
        """ 创建购物车
        """
        if dishes_dict is None:
            return
        shopping_card = dish_pb.ShoppingCard()
        shopping_card.table_id = table.ordering_service_table_id
        shopping_card.merchant_id = self.merchant.id
        for dish_dict in dishes_dict:
            dish = self.order_manager.dish_manager.get_dish_from_cache(
                dish_id=dish_dict.get("id"))
            if not dish:
                raise errors.DishNotExists()
            self.order_manager.create_shilai_product(
                shopping_card.products, None, dish, dish_dict, shopping_card=shopping_card)
        shopping_card.session_id = id_manager.generate_common_id()
        if people_count is not None:
            shopping_card.people_count = people_count
        self.table_id = shopping_card.table_id
        self._update_shopping_card = True
        self.shopping_card = shopping_card
        return shopping_card

    def add_dishes_to_shopping_card(self, shopping_card, dishes_dict):
        if dishes_dict is None:
            return
        add_dish_to_shopping_card = False
        for dish_dict in dishes_dict:
            dish_id = dish_dict.get("id")
            dish = self.order_manager.dish_manager.get_dish_from_cache(dish_id=dish_id)
            if not dish:
                raise errors.DishNotExists()
            self.order_manager.create_shilai_product(
                shopping_card.products, None, dish, dish_dict, shopping_card=shopping_card)
            add_dish_to_shopping_card = True
        if not add_dish_to_shopping_card:
            return
        self.table_id = shopping_card.table_id
        self._update_shopping_card = True
        self.shopping_card = shopping_card
        return shopping_card

    def remove_dish_from_shopping_card(self, shopping_card, uuids):
        if shopping_card is None:
            raise errors.Error(err=error_codes.SHOPPING_CARD_NOT_EXISTS)
        ps = []
        for product in shopping_card.products:
            if product.uuid in uuids:
                ps.append(product)
        if len(ps) == 0:
            return
        for p in ps:
            shopping_card.products.remove(p)
        self.table_id = shopping_card.table_id
        self._update_shopping_card = True
        self.shopping_card = shopping_card
        return shopping_card

    def update_dish_in_shopping_card(self, shopping_card, dish_dict):
        if shopping_card is None:
            raise errors.Error(err=error_codes.SHOPPING_CARD_NOT_EXISTS)
        if dish_dict is None:
            return
        quantity = dish_dict.get("quantity")
        uuid = dish_dict.get("uuid")
        p = None
        for product in shopping_card.products:
            if product.uuid == uuid:
                p = product
                break
        if not p:
            return
        if quantity > 0:
            self.add_dishes_to_shopping_card(shopping_card, [dish_dict])
            newest = shopping_card.products[-1]
            shopping_card.products.remove(newest)
            p.CopyFrom(newest)
            p.uuid = uuid
        elif quantity == 0:
            shopping_card.products.remove(p)
        self.table_id = shopping_card.table_id
        self._update_shopping_card = True
        self.shopping_card = shopping_card
        return shopping_card

    def get_shopping_card(self, table=None, table_id=None):
        if table is None and table_id is None:
            return None
        if table_id is None:
            table_id = table.ordering_service_table_id
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_shopping_card_key(table_id)
        json_data = redis_client.get(key)
        if not json_data:
            return None
        json_data = json_data.decode()
        json_data = json.loads(json_data)
        shopping_card = json_format.ParseDict(
            json_data, dish_pb.ShoppingCard(), ignore_unknown_fields=True)
        self.shopping_card = shopping_card
        return shopping_card

    def add_or_update_shopping_card_cache(self, shopping_card):
        self.__add_or_update_shopping_card_cache(shopping_card)

    def __add_or_update_shopping_card_cache(self, shopping_card):
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_shopping_card_key(shopping_card.table_id)
        json_data = json_format.MessageToJson(
            shopping_card, including_default_value_fields=True)
        one_day = date_utils.ONE_DAY
        redis_client.set(key, json_data, ex=one_day)

    def __remove_shopping_card_cache(self, shopping_card):
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_shopping_card_key(shopping_card.table_id)
        redis_client.delete(key)

    def clear_shopping_card(self, table=None, table_id=None, message_code=None):
        """ 下单之后清空购物车
        """
        if table is None and table_id is None:
            return None
        if table_id is None:
            table_id = table.id
        shopping_card = self.get_shopping_card(table_id=table_id)
        if shopping_card is None:
            return None
        while shopping_card.products:
            shopping_card.products.pop()
        self.table_id = table_id
        self._update_shopping_card = True
        if message_code is not None:
            self._message_code = message_code
        self.__remove_shopping_card_cache(shopping_card)
        return shopping_card

    def remove_shopping_card(self, table=None, table_id=None):
        redis_client = RedisClient().get_connection()
        key = CacheServerKeys.get_shopping_card_key(table_id)
        redis_client.delete(key)

    def send_order_create_message(self, order):
        message = {
            "type": MessageManager.ORDER_CREATE,
            "exclude_user_ids": self._exclude_user_ids,
            "orderId": order.id
        }
        channel = order.table_id
        message = json.dumps(message)
        redis_client = RedisClient()
        logger.info("redis推送消息: {}".format(redis_client.publish(channel, message)))

    def send_order_update_message(self, order):
        message = {
            "type": MessageManager.ORDER_UPDATE,
            "exclude_user_ids": self._exclude_user_ids,
            "orderId": order.id
        }
        channel = order.table_id
        message = json.dumps(message)
        redis_client = RedisClient()
        logger.info("redis推送消息: {}".format(redis_client.publish(channel, message)))

    def send_order_paid_message(self, order):
        message = {
            "type": MessageManager.ORDER_PAID,
            "exclude_user_ids": self._exclude_user_ids,
            "orderId": order.id
        }
        channel = order.table_id
        message = json.dumps(message)
        redis_client = RedisClient()
        logger.info("redis推送消息: {}".format(redis_client.publish(channel, message)))

    def __send_shopping_card_change_message(self, table_id):
        channel = table_id
        message = {
            "type": self._message_code,
            "exclude_user_ids": self._exclude_user_ids
        }
        if self.shopping_card is not None:
            message.update({
                "shoppingCard": json_format.MessageToDict(
                    self.shopping_card, including_default_value_fields=True)
            })
        message = json.dumps(message)
        redis_client = RedisClient()
        logger.info("redis推送消息: {}".format(redis_client.publish(channel, message)))

    def send_message(self):
        if self._update_shopping_card and self.table_id is not None:
            self.__send_shopping_card_change_message(table_id=self.table_id)

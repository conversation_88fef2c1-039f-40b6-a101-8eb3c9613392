# -*- coding: utf-8 -*-

import logging
import requests
from collections import namedtuple
from base64 import encodebytes

import Crypto.Hash.SHA512
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5

import proto.finance.wallet_pb2 as wallet_pb
from common import constants as common_constants
from common.config import config
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from service import errors
import proto.ordering.registration_pb2 as registration_pb


logger = logging.getLogger(__name__)


class PayManager:
    def __init__(self, *args, **kargs):
        merchant = kargs.get("merchant")
        merchant_id = kargs.get("merchant_id")
        self._registration_info = kargs.get("registration_info")
        self.merchant = None
        if merchant is not None:
            self.merchant = merchant
        if self.merchant is None and merchant_id is not None:
            self.merchant = MerchantDataAccessHelper().get_merchant(merchant_id=merchant_id)

    @property
    def registration_info(self):
        if self._registration_info:
            return self._registration_info
        ordering_da = OrderingServiceDataAccessHelper()
        self._registration_info = ordering_da.get_registration_info(
            merchant_id=self.merchant.id)
        return self._registration_info

    @registration_info.setter
    def registration_info(self, r):
        self._registration_info = r

    def is_pay_later(self):
        return self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER

    def is_multi_party_order(self):
        return self.registration_info.ordering_config.enable_many_people_order

    def sign(self, params):
        keys = params.keys()
        keys = sorted(keys, key=lambda x: x)
        s = []
        for key in keys:
            if params.get(key, "") == "":
                continue
            s.append("{}={}".format(key, params.get(key)))
        s = "&".join(s)
        s = s.replace("'", "\"")
        s = s.replace(" ", "")
        # logger.info("待签名字符串: {}".format(s))
        s = s.encode("utf-8")
        sign = self.rsa_sign(s)
        # logger.info("生成签名: {}".format(sign))
        return sign

    def rsa_sign(self, plaintext):
        """SHA256WithRSA 数字签名"""
        signer = PKCS1_v1_5.new(RSA.importKey(self.prikey_string))
        hash_value = Crypto.Hash.SHA256.new(plaintext)
        signature = signer.sign(hash_value)
        sign = encodebytes(signature).decode("utf8").replace("\n", "")
        return sign

    def rsa_verify(self, sign, plaintext):
        """校验RSA 数字签名"""
        hash_value = Crypto.Hash.SHA256.new(plaintext)
        verifier = PKCS1_v1_5.new(RSA.importKey(self.pubkey_string))
        return verifier.verify(hash_value, sign)

    def generate_notify_url(self, transaction, **kargs):
        domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        order_id = kargs.get("order_id")
        wishlist_id = kargs.get("wishlist_id")
        merchant_id = kargs.get("merchant_id")
        fanpiao_category_id = kargs.get("fanpiao_category_id")
        coupon_package_id = kargs.get("coupon_package_id")
        fanpiao_qrcode_id = kargs.get("fanpiao_qrcode_id")

        prefix = kargs.get("payment_prefix", "payment_notification")
        prefix = "{}/payment/{}".format(domain, prefix)

        if transaction.type == wallet_pb.Transaction.SELF_DINING_PAYMENT:
            notify_url = "{}/self_dining_payment/{}".format(prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT:
            notify_url = "{}/ordering/{}/{}".format(prefix, transaction.id, order_id)
        elif transaction.type == wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT:
            notify_url = "{}/ordering/{}/{}".format(prefix, transaction.id, order_id)
        elif transaction.type == wallet_pb.Transaction.GROUP_DINING_PAYMENT:
            notify_url = "{}/group_dining_payment{}".format(prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_PAYMENT:
            notify_url = "{}/group_dish_order_payment/{}/{}".format(domain, transaction.id, order_id)
        elif transaction.type == wallet_pb.Transaction.GROUP_DINING_TRANSFER:
            notify_url = "{}/group_dining_transfer/{}".format(domain, transaction.id)
        elif transaction.type == wallet_pb.Transaction.GROUP_DISH_ORDER_TRANSFER:
            notify_url = "{}/group_dish_order_transfer/{}".format(domain, transaction.id)
        elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
            notify_url = "{}/coupon_package_purchase/{}".format(prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.FEEDING_PAYMENT:
            notify_url = "{}/feeding/{}/{}".format(prefix, transaction.id, wishlist_id)
        elif transaction.type == wallet_pb.Transaction.FANPIAO_PURCHASE:
            notify_url = "{}/fanpiao_purchase/{}/{}/{}".format(
                prefix, transaction.id, merchant_id, fanpiao_category_id)
            if order_id:
                notify_url = "{}/ordering_with_fanpiao_purchase/{}/{}/{}/{}".format(
                    prefix, transaction.id, merchant_id, order_id, fanpiao_category_id)
            if fanpiao_qrcode_id:
                notify_url = "{}/fanpiao_purchase/pos_pay/{}/{}/{}".format(
                    prefix, transaction.id, fanpiao_qrcode_id, fanpiao_category_id)
        elif transaction.type == wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE:
            notify_url = "{}/shilai_member_card_recharge/{}".format(prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_COUPON_PACKAGE:
            notify_url = "{}/ordering_with_coupon_package/{}/{}/{}".format(
                prefix, transaction.id, order_id, coupon_package_id)
        elif transaction.type == wallet_pb.Transaction.DIRECT_PAY:
            notify_url = "{}/direct-pay/{}".format(prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.DIRECT_PAY_WITH_COUPON_PACKAGE_PURCHASE:
            notify_url = "{}/direct-pay/coupon-package/{}/{}".format(
                prefix, transaction.id, coupon_package_id)
        elif transaction.type == wallet_pb.Transaction.OPEN_GROUP_PURCHASE:
            notify_url = "{}/open_group_purchase/{}".format(
                prefix, transaction.id)
        elif transaction.type == wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT_WITH_OPEN_GROUP_PURCHASE:
            notify_url = "{}/ordering_with_open_group_purchase/{}".format(
                prefix, transaction.id
            )
        elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE:
            notify_url = f"{prefix}/vip_membership_subscribe/{transaction.id}"
        elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_RECHARGE:
            notify_url = f"{prefix}/vip_membership_recharge/{transaction.id}"
        elif transaction.type == wallet_pb.Transaction.VIP_MEMBERSHIP_SUBSCRIBE_RECHARGE_COMBINE:
            notify_url = f"{prefix}/vip_membership_subscribe_recharge_combine/{transaction.id}"
        else:
            logger.info("该支付类型不支持这个业务类型: {}".format(transaction.type))
            raise errors.PayMethodDonnotSupportBusiness()
        return notify_url

    def get_pay_transaction(self, transaction):
        """ 获取唤起支付时的订单ID
        1. 扫码点餐: 查询一次union_pay_transaction,如果有查到则返回 union_pay_transaction.transaction_id指向的transaction
        2. 买券包: 查询一次union_pay_transaction,如果有查到则返回 union_pay_transaction.transaction_id指向的transaction
        3. 买饭票: 直接返回transaction
        """
        transaction_da = TransactionDataAccessHelper()
        if transaction.type in [
                wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
        ]:
            # 拼团和点餐订单合并支付
            union_pay_transaction = transaction_da.get_union_pay_transaction(sub_transaction_id=transaction.id)
            if union_pay_transaction:
                pay_transaction = transaction_da.get_transaction_by_id(union_pay_transaction.transaction_id)
                if pay_transaction:
                    return pay_transaction
        if transaction.type in [
                wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
                wallet_pb.Transaction.DIRECT_PAY
        ]:
            # 扫码点餐退单
            union_pay_transaction = transaction_da.get_ordering_coupon_package_union_pay_transaction(
                ordering_transaction_id=transaction.id)
            if union_pay_transaction:
                pay_transaction = transaction_da.get_transaction_by_id(union_pay_transaction.transaction_id)
                return pay_transaction
        elif transaction.type == wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE:
            union_pay_transaction = transaction_da.get_ordering_coupon_package_union_pay_transaction(
                coupon_package_transaction_id=transaction.id)
            if union_pay_transaction:
                pay_transaction = transaction_da.get_transaction_by_id(union_pay_transaction.transaction_id)
                return pay_transaction
        return transaction

    def generate_refund_notify_url(self, pay_transaction, business_transaction_id, refund_transaction_id, **kargs):
        domain = config.get_config_value(common_constants.SERVICE_DOMAIN_ENV_NAME)
        prefix = kargs.get("prefix", "payment_notification")

        notify_url = "{}/payment/{}/{}/{}/{}".format(
            domain, prefix, pay_transaction.id, business_transaction_id, refund_transaction_id)
        return notify_url

    def try_post(self, url, params, timeout=3, try_times=5):
        Ret = namedtuple("Ret", ["flag", "ret"])
        if try_times == -1:
            logger.info("try post url: {} fail".format(url))
            return Ret(flag=False, ret={})
        try:
            logger.info("api request params: {}".format(params))
            ret = requests.post(url, json=params, timeout=timeout)
            logger.info("api request return text: {}".format(ret.text))
            ret = ret.json()
            return Ret(flag=True, ret=ret)
        except Exception as ex:
            try_times -= 1
            logger.info("try_post: {}".format(ex))
            logger.info("{} 重试".format(url))
            return self.try_post(url=url, params=params, timeout=timeout, try_times=try_times)
        return Ret(flag=False, ret={})

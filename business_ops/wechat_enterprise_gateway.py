import time
import requests
import logging
from cache.redis_client import RedisClient
from dao.user_da_helper import UserDataAccessHelper


logger = logging.getLogger(__name__)


class AccessTokenExpire(Exception):
    pass


class WechatEnterpriseGateway(object):
    CacheKey = 'qyapi_weixin:access_token'
    DOMAIN = "https://qyapi.weixin.qq.com"

    class Endpoint(object):
        OpenGID2ChatID = '/cgi-bin/externalcontact/opengid_to_chatid'
        GetAccessToken = '/cgi-bin/gettoken'
        GroupChatDetail = '/cgi-bin/externalcontact/groupchat/get'
        GroupUserDetail = '/cgi-bin/externalcontact/get'
        ValidateToken = "/cgi-bin/get_api_domain_ip"

    def __init__(self):
        # self.merchant_id = merchant_id
        self.redis_client = RedisClient().get_connection()
        self.access_token = self._get_access_token()
        logger.info(f"access_token: {self.access_token}")

    @classmethod
    def _validate_access_token(cls, access_token):
        url = f"{cls.DOMAIN}{cls.Endpoint.ValidateToken}?access_token={access_token}"
        return cls.get(url)['errcode'] == 0

    def _get_access_token(self):
        url = f"{self.DOMAIN}{self.Endpoint.GetAccessToken}"
        params = {
            'corpid': 'wwae428f6924d66169',
            'corpsecret': 'BVwiOLeUwfQ6AHyoR_0ITTJfsDePdbPQbQFVYjXkdjc',
        }
        access_token = self.redis_client.get(self.CacheKey)
        if access_token:
            access_token = access_token.decode("utf8")
        if access_token and self._validate_access_token(access_token):
            logger.info(f"Using cache access token: {access_token}")
        else:
            access_token = self.get(url, params=params)["access_token"]
            self.redis_client.setex(self.CacheKey, 7200, access_token)
            logger.info(f"Update cache access token: {access_token}")
        return access_token

    @classmethod
    def get(cls, url, params=None):
        start_time = time.time()
        logger.info(f"GET Request: {url}")
        resp = requests.get(url, params=params)
        resp_j = resp.json()
        logger.info(f"Get response: {resp_j}")
        logger.info(f"GET Request Costs: {time.time() - start_time} seconds")
        return resp_j

    @classmethod
    def post(cls, url, data):
        start_time = time.time()
        logger.info(f"POST Request: {url}")
        resp = requests.post(url, json=data)
        resp_j = resp.json()
        logger.info(f"POST Request Costs: {time.time() - start_time} seconds")
        if resp_j['errcode'] == 0:
            return resp_j
        elif resp_j['errcode'] == 42001:
            # access_token expired
            raise AccessTokenExpire(resp_j['errmsg'])
        else:
            raise Exception(resp_j['errmsg'])

    def convert_opengid_to_chatid(self, group_id):
        url = f"{self.DOMAIN}{self.Endpoint.OpenGID2ChatID}?access_token={self.access_token}"
        data = {'opengid': group_id}
        return self.post(url, data)['chat_id']

    def get_chat_group_detail(self, chat_id):
        data = {
            "chat_id": chat_id,
            "need_name": 1
        }
        url = f"{self.DOMAIN}{self.Endpoint.GroupChatDetail}?access_token={self.access_token}"
        return self.post(url, data)['group_chat']


class WechatGroupChatUser(object):

    def __init__(self, open_group_id, user_id=None, user=None):
        self.user_id = user_id
        self.user = user or self.user_dao.get_user(user_id=self.user_id)
        self.open_group_id = open_group_id
        self.we_gateway = WechatEnterpriseGateway()

    @property
    def user_dao(self):
        return UserDataAccessHelper()

    def validate_user_in_group(self, group_name):
        """
        判断 group name 做判断
        """
        res = False
        if not self.user:
            return res
        chat_id = self.we_gateway.convert_opengid_to_chatid(self.open_group_id)
        group_chat_detail = self.we_gateway.get_chat_group_detail(chat_id)
        if group_chat_detail.get('name') and group_name in group_chat_detail['name']:
            res = True
        else:
            logger.info(f"Group Name Not Match: {group_name} vs {group_chat_detail.get('name')}")
        return res


if __name__ == '__main__':
    """
    python -m business_ops.wechat_enterprise_gateway
    """
    from pprint import pprint
    open_group_id = 'Gaoif4kNtrx9HaIv7kd5pJxzaaqM'
    gateway = WechatEnterpriseGateway()
    chat_id = gateway.convert_opengid_to_chatid(open_group_id)
    group_chat_detail = gateway.get_chat_group_detail(chat_id)
    pprint(group_chat_detail)

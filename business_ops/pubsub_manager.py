# -*- coding: utf-8 -*-


"""
Filename: pubsub_manager.py
Date: 2020-07-13 16:21:36
Title: 消息发布
主要用于第三方同步过来的消息,下发到其它进程
"""


import proto.ordering.keruyun.messages_pb2 as message_pb


class PubsubManager:
    def __init__(self, *args, **kargs):
        self.url_pathes = {
            "/keruyun_callback/order/change_table": self.create_change_table_obj,
            "/keruyun_callback/dish/update_category": self.create_update_category_obj,
            "/keruyun_callback/order/payment_notice": None,
            "/keruyun_callback/order/rejected": None,
            "/keruyun_callback/dish/update": self.create_update_dish_obj,
            "/keruyun_callback/order/approved": None,
            "/keruyun_callback/order/pos_return_order": self.create_pos_return_order_obj,
            "/keruyun_callback/order/status": self.create_order_status_obj,
            "/keruyun_callback/food_delivery/cancel_order": None,
            "/keruyun_callback/food_delivery/accept_order": self.create_food_delivery_accept_order_obj,
            "/keruyun_callback/food_delivery/refuse_order": self.create_food_delivery_refuse_order_obj,
            "/keruyun_callback/food_delivery/refund_approved": None,
            "/keruyun_callback/food_delivery/refund_rejected": None,
            "/keruyun_callback/food_delivery/delivery_status": self.create_food_delivery_delivery_status_obj,
            "/keruyun_callback/food_delivery/trash_order": self.create_food_delivery_trash_order_obj,
            "/keruyun_callback/food_delivery/return": None,
            "/keruyun_callback/delivery/order": None,
            "/keruyun_callback/delivery/cancel_order": None
        }

    def create_change_table_obj(self, **kargs):
        order_id = kargs.get("order_id")
        obj = message_pb.ChangeTable()
        obj.order_id = order_id
        return obj

    def create_update_category_obj(self, **kargs):
        dish_type_ids = kargs.get("dish_type_ids")
        operation = kargs.get("operation")
        shop_identy = kargs.get("shop_identy")
        obj = message_pb.UpdateCategory()
        for id in dish_type_ids:
            obj.dish_type_ids.append(str(id))
        obj.operation = operation
        obj.shop_identy = shop_identy
        return obj

    def create_update_dish_obj(self, **kargs):
        dish_shop_ids = kargs.get("dish_shop_ids")
        dish_brand_ids = kargs.get("dish_brand_ids")
        shop_identy = kargs.get("shop_identy")
        operation = kargs.get("operation")
        timestamp = kargs.get("timestamp")
        obj = message_pb.UpdateDish()
        obj.operation = operation
        obj.timestamp = int(timestamp)
        if dish_shop_ids is not None:
            for id in dish_shop_ids:
                obj.dish_shop_ids.append(str(id))
        if dish_brand_ids is not None:
            for id in dish_brand_ids:
                obj.dish_brand_ids.append(str(id))
        obj.shop_identy = shop_identy
        return obj

    def create_pos_return_order_obj(self, **kargs):
        order_id = kargs.get("order_id")
        reason = kargs.get("reason")
        shop_identy = kargs.get("shop_identy")
        obj = message_pb.PosReturnOrder()
        obj.order_id = order_id
        obj.reason = reason
        obj.shop_identy = shop_identy
        return obj

    def create_order_status_obj(self, **kargs):
        """ [{'operation': 26, 'shopId': 810411605, 'brandId': 163930, 'tradeId': 380388207178740736, 'tradePayStatus': 0, 'tradePayForm': 2, 'tradeType': 0}]
        """
        trade_id = kargs.get("trade_id")
        operation = kargs.get("operation")
        obj = message_pb.OrderStatus()
        obj.trade_id = str(trade_id)
        obj.operation = operation
        return obj

    def create_food_delivery_accept_order_obj(self, **kargs):
        order_id = kargs.get("order_id")
        obj = message_pb.FoodDeliveryAcceptOrder()
        obj.order_id = order_id
        return obj

    def create_food_delivery_refuse_order_obj(self, **kargs):
        order_id = kargs.get("order_id")
        reason = kargs.get("reason")
        obj = message_pb.FoodDeliveryRefuseOrder()
        obj.order_id = order_id
        obj.reason = reason
        return obj

    def create_food_delivery_delivery_status_obj(self, **kargs):
        order_id = kargs.get("order_id")
        deliververy_id = kargs.get("deliververy_id")
        deliverer = kargs.get("deliverer")
        rider_name = kargs.get("rider_name")
        rider_phone = kargs.get("rider_phone")
        cancel_code = kargs.get("cancel_code")
        cancel_reason = kargs.get("cancel_reason")
        delivery_status = kargs.get("delivery_status")
        obj = message_pb.FoodDeliveryDeliveryStatus()
        obj.order_id = order_id
        obj.deliververy_id = deliververy_id
        obj.deliverer = deliverer
        obj.rider_name = rider_name
        obj.rider_phone = rider_phone
        obj.cancel_code = cancel_code
        obj.cancel_reason = cancel_reason
        obj.delivery_status = delivery_status
        return obj

    def create_food_delivery_trash_order_obj(self, **kargs):
        order_id = kargs.get("order_id")
        reason = kargs.get("reason")
        obj = message_pb.FoodDeliveryTrashOrder()
        obj.order_id = order_id
        obj.reason = reason
        return obj

    def create_food_delivery_return_obj(self, **kargs):
        order_id = kargs.get("order_id")
        reason = kargs.get("reason")
        obj = message_pb.FoodDeliveryReturn()
        obj.order_id = order_id
        obj.reason = reason
        return obj

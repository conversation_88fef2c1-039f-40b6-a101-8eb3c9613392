# -*- coding: utf-8 -*-

import logging
from datetime import datetime

import proto.message_center.message_pb2 as message_pb

from business_ops.base_manager import BaseManager
from common.utils import id_manager
from common.utils.misc import try_cache
from dao.message_center.message_da_helper import MessageDataAccessHelper

logger = logging.getLogger(__name__)


class MessageManager(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.message_da = MessageDataAccessHelper()

    def __create_message_obj(self, user):
        message = message_pb.Message()
        message.receiver_id = user.id
        message.create_time = int(datetime.now().timestamp())
        message.id = id_manager.generate_common_id()
        return message

    def __add_or_update_message_obj(self, message):
        if not message:
            return
        message.update_time = int(datetime.now().timestamp())
        self.message_da.add_or_update_message(message)

    def get_user_period_messages(self, user, create_time_periods):
        return self.message_da.get_user_period_messages(user.id, create_time_periods)

    def __set_message_info(
            self,
            message,
            title,
            text=None,
            relative_id=None,
            merchant_id=None,
    ):
        message.title = title
        if text is not None:
            message.text = text
        if relative_id is not None:
            message.relative_info.relative_id = relative_id
        if merchant_id is not None:
            message.relative_info.merchant_id = merchant_id

    @try_cache
    def create_group_purchase_opened_message(self, user, group_purchase):
        """开团成功的消息提醒."""
        if self.message_da.get_message_by_relative_id(user.id, group_purchase.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "开团成功",
            "恭喜您获得专属立减券，快去下单使用吧~",
            group_purchase.id,
            group_purchase.merchant_id,
        )
        logger.info(f"{user.member_profile.nickname} 用户开团成功提醒 {message.id}")
        self.__add_or_update_message_obj(message)

    @try_cache
    def create_group_purchase_finished_message(self, user, group_purchase):
        """团购结束提醒"""
        if self.message_da.get_message_by_relative_id(user.id, group_purchase.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "拼团已结束",
            "您的拼团已结束，请前往我的拼团查看详情",
            group_purchase.id,
            group_purchase.merchant_id,
        )
        logger.info(f"{user.member_profile.nickname} 用户拼团结束提醒 {message.id}")
        self.__add_or_update_message_obj(message)

    @try_cache
    def create_fanpiao_expire_soon_message(self, user, fanpiao):
        """饭票即将过期提醒"""
        if self.message_da.get_message_by_relative_id(user.id, fanpiao.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "饭票即将过期",
            "您的饭票即将过期！请尽快使用哟~",
            fanpiao.id,
            fanpiao.merchant_id,
        )
        logger.info(f"{user.member_profile.nickname} 饭票即将过期 {message.id}")
        self.__add_or_update_message_obj(message)

    @try_cache
    def create_coupon_expire_soon_message(self, user, coupon):
        """coupon即将过期提醒"""
        if self.message_da.get_message_by_relative_id(user.id, coupon.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "代金券即将过期",
            "您的代金券即将过期！请尽快使用哟~",
            coupon.id,
            coupon.merchant_id,
        )
        logger.info(f"{user.member_profile.nickname} coupon即将过期提醒 {message.id}")
        self.__add_or_update_message_obj(message)

    @try_cache
    def create_shixiang_member_opened_message(self, user, transaction):
        """成为时享会员提醒"""
        if self.message_da.get_message_by_relative_id(user.id, transaction.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "恭喜成为时享会员",
            "尊贵的时享会员！零钱支付立享跨店打折哟~",
            transaction.id
        )
        logger.info(f"{user.member_profile.nickname} 时享会员开通提醒 {message.id}")
        self.__add_or_update_message_obj(message)

    @try_cache
    def create_group_purhase_coupon_issued_message(self, user, group_purchase):
        """获得拼团券提醒"""
        if self.message_da.get_message_by_relative_id(user.id, group_purchase.id):
            return
        message = self.__create_message_obj(user)
        self.__set_message_info(
            message,
            "恭喜获得拼团券",
            "您领取了好友的拼团券，快去下单使用吧~",
            group_purchase.id,
            group_purchase.merchant_id,
        )
        logger.info(f"{user.member_profile.nickname} 获得拼团券提醒 {message.id}")
        self.__add_or_update_message_obj(message)

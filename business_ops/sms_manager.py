# -*- coding: utf-8 -*-

import logging

from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest

# 在阿里云短信服务平台上注册的发送主体签名名称
SMS_SIGN_NAME = '时来饭票'
ACCESS_KEY_ID = 'LTAIWOyMOaB5Kwnv'
ACCESS_KEY_SECRET = 's4mOCRIUH95Ssu88YwPh6rXSA8Ao6K'

logger = logging.getLogger(__name__)


class SmsManager(object):

    # 获取绑定手机号的验证码
    BIND_PHONE_AUTH_CODE = 0x01

    def send_coupon_notification(self, phone_number, template_code, template_params=None):
        request = self.__get_request_client()
        # 添加发送短信必要参数
        request.set_action_name('SendSms')
        request.add_query_param('PhoneNumbers', phone_number)
        request.add_query_param('SignName', SMS_SIGN_NAME)
        request.add_query_param('TemplateCode', template_code)
        if template_params:
            request.add_query_param('TemplateParam', template_params)

        client = AcsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET, 'default')
        response = client.do_action(request)
        return response

    def send_batch_notifications(self, phone_numbers, template_code, template_params=None):
        request = self.__get_request_client()
        # 添加发送短信必要参数
        request.set_action_name('SendSms')
        request.add_query_param('PhoneNumbers', ','.join(str(phone) for phone in phone_numbers))
        request.add_query_param('SignName', SMS_SIGN_NAME)
        request.add_query_param('TemplateCode', template_code)
        if template_params:
            request.add_query_param('TemplateParam', template_params)

        client = AcsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET, 'default')
        response = client.do_action(request)
        return response

    def __get_request_client(self):
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('dysmsapi.aliyuncs.com')
        request.set_method('POST')
        request.set_protocol_type('https')  # https | http
        request.set_version('2017-05-25')
        return request

    def send_bind_phone_code(self, phone_numbers, code):
        template_code = "SMS_229095624"
        params = {"code": code}
        ret = self.send_batch_notifications(
            phone_numbers, template_code, template_params=params)
        logger.info(f"发送手机验证码: {ret}")

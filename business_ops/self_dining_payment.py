# -*- coding: utf-8 -*-

import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.payment_pb2 as payment_pb
from business_ops import merchant_store_manager
from business_ops.business_manager import BusinessManager
from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from cache.redis_client import RedisClient
from common.utils import id_manager
from common.utils import date_utils
from dao.payment_da_helper import PaymentDataAccessHelper
from service import errors
from service import error_codes
from service import base_responses

logger = logging.getLogger(__name__)


class SelfDiningPaymentManager(BusinessManager):
    def __init__(self, transaction_id=None, user_id=None, merchant_id=None, bill_fee=None,
                 paid_fee=None, pay_method=None, coupon_id=None, no_discount_fee=0, return_url=None):
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.shipping_fee = 0
        self.coupon_fee = 0
        self.payment = None
        self.return_url = return_url

        super(SelfDiningPaymentManager, self).__init__(
            user_id=user_id, transaction_id=transaction_id, merchant_id=merchant_id,
            coupon_id=coupon_id, pay_method=pay_method)

        self.paid_fee = self.round_off(self.paid_fee)
        self.bill_fee = self.round_off(self.bill_fee)
        self.coupon_fee = self.round_off(self.coupon_fee)
        self.no_discount_fee = self.round_off(no_discount_fee)
        self.shipping_fee = self.round_off(self.shipping_fee)
        self.redis_client = RedisClient().get_connection()

    def check_no_discount_time_ranges(self):
        ret = merchant_store_manager.is_no_discount_time_ranges(self.merchant)
        if ret.flag and self.coupon_id != "":
            raise errors.NoDiscountTimeRanges()

    def get_idempotent_key(self):
        return "self_dining_payment:{merchant_id}:{user_id}:{bill_fee}".format(merchant_id=self.merchant.id, user_id=self.user.id, bill_fee=self.bill_fee)

    def check_idempotent(self):
        """ 检查支付的幂等性
        同一个用户在同一个商户下面同样金额的支付做为幂等性的key
        """
        key = self.get_idempotent_key()
        if not self.redis_client.set(name=key, value=1, ex=5 * 60, nx=True):
            raise errors.Error(error_codes.TRADING_IN_PROCESS)

    def delete_idempotent(self):
        key = self.get_idempotent_key()
        self.redis_client.delete(key)

    def prepay(self):
        """ 个人用券付款
        """
        # 前端有做二次确认,就不需要在后端做去重操作了
        # self.check_idempotent()
        self.check_no_discount_time_ranges()

        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        self.transaction = TransactionManager().handle_self_dining_payment_prepay(
            payer_id=self.user.id, payee_id=self.merchant.id, payee_store_id=self.store.id,
            pay_method=self.pay_method, bill_fee=self.bill_fee, paid_fee=self.paid_fee,
            use_coupon_id=self.coupon_id)
        self.create_self_dining_payment_obj()

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            result = payment_manager.prepay(transaction=self.transaction)
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            result = payment_manager.prepay(transaction=self.transaction)
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = payment_manager.prepay(transaction=self.transaction, return_url=self.return_url, user_id=self.user.id)
        elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            result = payment_manager.prepay(transaction=self.transaction, payment=self.payment)
        elif self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
        else:
            raise errors.PayMethodNotSupport()

        if result.get("errcode") == error_codes.SUCCESS:
            logger.info('prepay success: {}'.format(self.transaction.id))
            payment_da = PaymentDataAccessHelper()
            if self.pay_method == wallet_pb.Transaction.WALLET:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
            elif self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
                payment_da.add_or_update_self_dining_payment(self.payment)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.FANPIAO_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
                payment_da.add_or_update_self_dining_payment(self.payment)
            elif self.pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
            result['transactionId'] = self.transaction.id
        return result

    def create_self_dining_payment_obj(self):
        self.payment = payment_pb.SelfDiningPayment()
        self.payment.id = id_manager.generate_common_id()
        self.payment.user_id = self.user.id
        self.payment.merchant_id = self.merchant.id
        self.payment.bill_fee = self.transaction.bill_fee
        self.payment.paid_fee = self.transaction.paid_fee
        self.payment.create_time = date_utils.timestamp_second()
        self.payment.paid_time = date_utils.timestamp_second()
        self.payment.coupon_fee = self.coupon_fee
        self.payment.status = payment_pb.SelfDiningPayment.PREPAY_ORDER
        self.payment.no_discount_fee = self.no_discount_fee
        self.payment.transaction_id = self.transaction.id
        self.payment.paid_in_fee = 0

    def notification(self):
        payment_da = PaymentDataAccessHelper()

        self.generate_red_packet_for_new_member_coupon()
        self.consume_coupon()

        if self.payment is None:
            self.payment = payment_da.get_self_dining_payment(transaction_id=self.transaction.id)
        self.payment.paid_in_fee += self.payment.platform_subsidies + self.transaction.paid_fee
        if self.red_packet:
            self.payment.paid_in_fee -= self.red_packet.total_value
        self.payment.status = payment_pb.SelfDiningPayment.SUCCESS
        payment_da.add_or_update_self_dining_payment(payment=self.payment)

        self.transaction.paid_time = date_utils.timestamp_second()
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        TransactionManager().update_transaction(transaction=self.transaction)

        payment_manager = PaymentManager(pay_method=self.pay_method, merchant=self.merchant)
        flag = payment_manager.self_dining_payment_launch_ledger(self.transaction)
        if flag:
            payment_manager.update_self_dining_payment_transfer_fee(self.transaction)

        logger.info("self_dining_payment notification: transactionId: {}, userId: {}, merchantId: {}".format(
            self.transaction.id, self.user.id, self.merchant.id))

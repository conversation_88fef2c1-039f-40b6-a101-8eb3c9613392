# -*- coding: utf-8 -*-


"""
所以business_ops下的manager应该直接或间接继承此BaseManager
BaseManager的功能主要是做一些初始化的工作
1. initiate self.merchant
2. initiate self.store
3. initiate self.registration_info
4. initiate self.order
5. initiate self.transaction
"""

import os

from google.protobuf import json_format

from common.utils import id_manager
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from common.cache_server_keys import CacheServerKeys
from common.utils.distribute_lock import AtomicDistributedLock
from service import errors, error_codes
import proto.ordering.registration_pb2 as registration_pb


class BaseManager:
    def __init__(self, *args, **kargs):
        self.init()
        self.init_merchant(**kargs)
        self.init_registration_info(**kargs)
        self.init_user(**kargs)
        self.init_transaction(**kargs)
        self.init_order(**kargs)

    def init(self):
        self.merchant = None
        self.store = None
        self.registration_info = None
        self.user = None
        self.transaction = None
        self.order = None

    def init_transaction(self, **kargs):
        transaction = kargs.get("transaction")
        transaction_id = kargs.get("transaction_id")
        if transaction is not None:
            self.transaction = transaction
        elif transaction_id is not None:
            transaction_da = TransactionDataAccessHelper()
            self.transaction = transaction_da.get_transaction_by_id(transaction_id)
        if not self.transaction:
            return
        if not self.merchant:
            self.init_merchant(merchant_id=self.transaction.payee_id)
        if not self.registration_info:
            self.init_registration_info(merchant=self.merchant, merchant_id=self.transaction.payee_id)
        if not self.user:
            self.init_user(user_id=self.transaction.payer_id)

    def init_order(self, **kargs):
        order = kargs.get("order")
        order_id = kargs.get("order_id")
        ordering_da = OrderingServiceDataAccessHelper()
        if order is not None:
            self.order = order
        elif order_id is not None:
            self.order = ordering_da.get_order(id=order_id)
        elif self.transaction:
            self.order = ordering_da.get_order(transaction_id=self.transaction.id)
        if self.order and self.transaction:
            self.order.transaction_id = self.transaction.id
        if not self.order:
            return

        # 千万不在要在此处调用init_user

        if not self.merchant:
            self.init_merchant(merchant_id=self.order.merchant_id)

    def init_merchant(self, **kargs):
        merchant = kargs.get("merchant")
        merchant_id = kargs.get("merchant_id")

        if merchant is not None:
            self.merchant = merchant
        elif merchant_id is not None:
            merchant_da = MerchantDataAccessHelper()
            self.merchant = merchant_da.get_merchant(merchant_id)
        if self.merchant:
            if len(self.merchant.stores) > 0:
                self.store = self.merchant.stores[0]
            else:
                self.store = self.merchant.stores.add()
                self.store.id = id_manager.generate_common_id()

    def init_registration_info(self, **kargs):
        registration_info = kargs.get("registration_info")
        merchant_id = kargs.get("merchant_id")
        ordering_da = OrderingServiceDataAccessHelper()
        if registration_info is not None:
            self.registration_info = registration_info
        elif self.merchant:
            self.registration_info = ordering_da.get_registration_info(merchant_id=self.merchant.id)
        elif merchant_id is not None:
            self.registration_info = ordering_da.get_registration_info(merchant_id=merchant_id)
        if not self.registration_info:
            return
        if not self.merchant:
            self.init_merchant(merchant_id=self.registration_info.merchant_id)

    def init_user(self, **kargs):
        user = kargs.get("user")
        user_id = kargs.get("user_id")
        if user is not None:
            self.user = user
        elif user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

    def get_current_env(self):
        return os.environ.get("DEPLOYMENT_ENV", "test")

    def is_test_env(self):
        return self.get_current_env() == "test"

    @classmethod
    def list_update(cls, field, array):
        while field:
            field.pop()
        for ele in array:
            field.append(ele)

    @classmethod
    def to_json(cls, obj, columns=None):
        """
        将Python 对象转为 RESTFul API 的响应
        """
        if not obj:
            return {}
        origin = json_format.MessageToDict(obj, including_default_value_fields=True)
        if columns and isinstance(columns, list):
            return {key: value for key, value in origin.items() if key in columns}
        return origin

    @property
    def outer_version(self):
        return os.environ.get("OUTER_VERSION")

    @property
    def is_test(self):
        return self.is_test_env()

    def decrease_dish_remain_quantity(self, order, order_manager=None, raise_error=False):
        """支付回调时 扣减菜品余量"""
        if not order:
            return
        if order_manager is None:
            from business_ops.ordering.order_manager import OrderManager
            order_manager = OrderManager(merchant=self.merchant, business_obj=self)
        order_manager.decrease_dish_remain_quantity(order, raise_error=raise_error)

    def check_dish_remain_quantity(self, order, order_manager=None):
        """prepay时 检测菜品余量是否足够"""
        if not order:
            return
        if order_manager is None:
            from business_ops.ordering.order_manager import OrderManager
            order_manager = OrderManager(merchant=self.merchant, business_obj=self)
        order_manager.check_dish_remain_quantity(order)

    def is_pay_later(self):
        return self.registration_info.pay_type == registration_pb.OrderingServiceRegistrationInfo.PAY_LATER

    def is_multi_party_order(self):
        return self.registration_info.ordering_config.enable_many_people_order

    def add_order_lock(self):
        """
        1、同一个用户不能短时间内唤起同一笔订单（支付回调中释放）
        2、多人点餐 锁订单ID，避免多个人同时唤起支付
        """
        if not self.order:
            return
        key = CacheServerKeys.get_check_repeat_prepay_key(self.user.id, self.merchant.id, self.paid_fee)
        if not AtomicDistributedLock(key, ttl=6 * 1000).lock():
            raise errors.Error(err=error_codes.ORDER_PAYING)

        if self.is_multi_party_order():
            if not AtomicDistributedLock(
                CacheServerKeys.get_order_paying_cache_key(self.order.id),
                ttl=60 * 1000
            ).lock():
                raise errors.Error(err=error_codes.ORDER_PAYING_V2)

    def release_order_lock(self, order):
        if not order:
            return
        order_id, user_id, merchant_id = order.id, order.user_id, order.merchant_id
        AtomicDistributedLock(
            CacheServerKeys.get_check_repeat_prepay_key(user_id, merchant_id, order.paid_fee),
            ttl=6 * 1000
        ).release()
        if self.is_multi_party_order():
            AtomicDistributedLock(
                CacheServerKeys.get_order_paying_cache_key(order_id),
                ttl=60 * 1000
            ).release()

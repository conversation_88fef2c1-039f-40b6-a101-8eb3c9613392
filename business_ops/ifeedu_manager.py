# -*- coding: utf-8 -*-

import logging
import random
import time

from business_ops.transaction_manager import TransactionManager
from business_ops.payment_manager import PaymentManager
from business_ops.ifeedu.feed_manager import generate_poster
from business_ops.ifeedu.feed_manager import get_poster_url
from common.utils import id_manager
from common.utils import date_utils
from cache.redis_client import RedisClient
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.ifeedu_da_helper import IFeedUDataAccessHelper
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from proto.ifeedu import common_pb2 as ifeedu_common_pb
from proto.ifeedu import wishlist_pb2 as wishlist_pb
from proto.ifeedu import feeding_pb2 as feeding_pb
from proto.ordering import dish_pb2 as dish_pb
from proto.page import ifeedu_pb2 as page_ifeedu_pb
from proto.finance import wallet_pb2 as wallet_pb
from service import errors
from service import error_codes

logger = logging.getLogger(__name__)


class IFeedUManager:
    def __init__(self, merchant_id=None, wishlist_id=None, pay_method=None, bill_fee=None, paid_fee=None, user_id=None,
                 transaction_id=None, feed_items=None, item_type=None, feed_plan_id=None, check_open_feed=True):
        self.pay_method = pay_method
        self.bill_fee = bill_fee
        self.paid_fee = paid_fee
        self.feed_items = feed_items
        self.item_type = item_type
        self.feed_stats = None

        if feed_plan_id is not None:
            self.feed_plan = IFeedUDataAccessHelper().get_feed_plan(id=feed_plan_id)
            if transaction_id:
                # 在回调中同步feed_plan中的transaction_id
                self.feed_plan.transaction_id = transaction_id
                IFeedUDataAccessHelper().add_or_update_feed_plan(self.feed_plan)
            wishlist_id = self.feed_plan.wish_list_id
            self.feed_items = {feed_item.id: feed_item.feed_amount for feed_item in self.feed_plan.feed_items}
            user_id = self.feed_plan.feeder_id

        if transaction_id:
            self.transaction = TransactionDataAccessHelper().get_transaction_by_id(transaction_id)
            self.paid_fee = self.transaction.paid_fee

        if wishlist_id is not None:
            self.wishlist = IFeedUDataAccessHelper().get_wishlist_by_id(wishlist_id=wishlist_id)
            if not self.wishlist:
                raise errors.NoSuchWishlist()
            if not merchant_id:
                merchant_id = self.wishlist.merchant_id

        if merchant_id:
            self._merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
            self._store = self._merchant.stores[0]
            self._config = IFeedUDataAccessHelper().get_config(merchant_id)
            if check_open_feed and (self._config and not self._config.enable_ifeedu):
                raise errors.NotEnableIFeedU()
        if user_id is not None:
            self.user = UserDataAccessHelper().get_user(user_id)

    def get_config(self):
        config = IFeedUDataAccessHelper().get_config(self._merchant.id)
        ordering_service_da = OrderingServiceDataAccessHelper()
        categories = {}
        categories_dishes = {}
        if config:
            config_vo = page_ifeedu_pb.DishList()
            for dish_id in config.dish_list:
                dish = ordering_service_da.get_dish(dish_id=dish_id, merchant_id=self._merchant.id)

                category_id = dish.categories[0]
                category = categories.get(category_id)
                if not category:
                    category = ordering_service_da.get_category(id=dish.categories[0], merchant_id=self._merchant.id)
                    categories.update({category_id: category})

                category_dishes = categories_dishes.get(category_id, [])
                category_dishes.append(dish)
                categories_dishes.update({category_id: category_dishes})

            categories = list(categories.values())
            categories.sort(key=lambda x: x.sort)
            for category in categories:
                category_id, dishes = category.id, categories_dishes.get(category_id)
                dishes.sort(key=lambda x: x.sort)

                dishes_vo = config_vo.dishes.add()
                dishes_vo.category.id = category.id
                dishes_vo.category.name = category.name

                for dish in dishes:
                    dish_vo = dishes_vo.dish_list.add()
                    dish_vo.id = dish.id
                    dish_vo.name = dish.name
                    if len(dish.images) > 0:
                        dish_vo.image = dish.images[0]
                    dish_vo.price = int(dish.price)
                    dish_vo.category_id = category_id
            config_vo.merchant_id = self._merchant.id
            config_vo.merchant_logo = self._merchant.basic_info.logo_url
            config_vo.optional_dish_number = config.optional_select_dish_number
            config_vo.store_id = self._store.id
            config_vo.store_name = self._store.name
            config_vo.address = self._store.address
            return config_vo
        return None

    def init(self, merchant_id):
        """ 为一个门店初始化投喂功能配置
        """
        config = ifeedu_common_pb.IFeedUConfig()
        config.merchant_id = merchant_id
        config.optional_select_dish_number = 3
        config.random_select_dish_number = 3
        config.enable_ifeedu = True
        dishes = OrderingServiceDataAccessHelper().get_dishes(merchant_id=merchant_id)
        registration_info = OrderingServiceDataAccessHelper().get_registration_info(merchant_id)
        exclude_dish_name = ["打包盒", "配送费", "纸巾", "茶位费", "小打包盒", "大打包盒", "中打包盒"]
        required_order_dishes = {item.name: item.type for item in registration_info.ordering_config.required_order_items}
        for dish in dishes:
            if not dish.status == dish_pb.Dish.NORMAL:
                continue
            if dish.id == registration_info.packaging_box_config.dish_id:
                continue
            if dish.price == 0:
                logger.info("价格为'{}'的商品'{}'不能成为心愿商品".format(dish.price, dish.name))
                continue
            if dish.name in required_order_dishes:
                logger.info("必选商品不能成为心愿商品: {}".format(dish.name))
                continue
            if dish.name in exclude_dish_name:
                logger.info("{} 不能成为心愿商品".format(dish.name))
                continue
            logger.info("设置商品'{}'为心愿商品".format(dish.name))
            config.dish_list.append(dish.id)
        IFeedUDataAccessHelper().set_config(config)

    def update_wishlist_feeding_ranklist(self):
        key = "wishlist_feeding_ranklist_{}".format(self.wishlist.id)
        redis_client = RedisClient().get_connection()
        mapping = {self.feed_stats.feeder_id: self.total_hit_number}
        if redis_client.exists(key):
            redis_client.zadd(key, mapping)

    def get_wishlist_feeding_ranklist(self, times=3):
        if times == 0:
            return []
        key = "wishlist_feeding_ranklist_{}".format(self.wishlist.id)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key):
            ranklist = redis_client.zrange(key, 0, -1, withscores=True, score_cast_func=float)
            ret = []
            ranklist.reverse()
            for index, rank in enumerate(ranklist):
                feeder_id = rank[0].decode('utf8')
                score = float(rank[1])
                feed_state = IFeedUDataAccessHelper().get_feed_stats(wish_list_id=self.wishlist.id, feeder_id=feeder_id)
                feed_state_vo = page_ifeedu_pb.FeedStatsVo()
                user = UserDataAccessHelper().get_user(feeder_id)
                feed_state_vo.nickname = user.member_profile.nickname
                feed_state_vo.matching_score = score
                feed_state_vo.head_img_url = user.member_profile.head_image_url
                feed_state_vo.rank = index + 1
                feed_state_vo.total_feed_amount = feed_state.total_feed_amount
                ret.append(feed_state_vo)
            ret = sorted(ret, key=lambda x: (x.matching_score, x.total_feed_amount), reverse=True)
            for rank, r in enumerate(ret):
                r.rank = rank + 1
            return ret
        else:
            feed_statses = IFeedUDataAccessHelper().get_feed_statses(wish_list_id=self.wishlist.id)
            pipeline = redis_client.pipeline()
            mapping = {feed_stats.feeder_id: feed_stats.matching_score for feed_stats in feed_statses}
            if mapping:
                pipeline.zadd(key, mapping)
                pipeline.expire(key, 24 * 60 * 60)
                pipeline.execute()
                return self.get_wishlist_feeding_ranklist(times - 1)
            return []

    def get_popularity_ranklist(self, user_id, times=3):
        """ 投喂人气总榜
        """
        if times <= 0:
            return []
        ifeedu_da = IFeedUDataAccessHelper()
        user_da = UserDataAccessHelper()
        key = self._get_popularity_ranklist_key(user_id, self._merchant.id)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key):
            user_ids = redis_client.zrange(key, 0, -1)
            ret = []
            for user_id in user_ids:
                user_id = user_id.decode('utf-8')
                total_feed_amount = self.get_user_merchant_feed_stats(user_id)
                if total_feed_amount <= 0:
                    continue
                feed_state_vo = page_ifeedu_pb.FeedStatsVo()
                user = user_da.get_user(user_id)
                feed_state_vo.nickname = user.member_profile.name
                # feed_state_vo.matching_score = self.get_user_merchant_feed_stats(user_id)
                feed_state_vo.total_feed_amount = total_feed_amount
                feed_state_vo.head_img_url = user.member_profile.head_image_url
                ret.append(feed_state_vo)
            ret.sort(key=lambda x: x.total_feed_amount, reverse=True)
            for index, rank in enumerate(ret):
                rank.rank = index + 1
            return ret

        relationships = ifeedu_da.get_user_relationships(user_id=user_id, merchant_id=self._merchant.id)
        root_ids = set()
        for relationship in relationships:
            root_ids.add(relationship.root_id)

        user_ids = set()
        for root_id in root_ids:
            relationships = ifeedu_da.get_user_relationships(root_id=root_id, merchant_id=self._merchant.id)
            user_ids.add(root_id)
            for r in relationships:
                user_ids.add(r.user_id)

        mapping = {user_id: 1 for user_id in user_ids}
        if mapping:
            redis_client.zadd(key, mapping)
            return self.get_popularity_ranklist(user_id, times - 1)
        return []

    def _build_user_relationship(self, user_id, inviter_id):
        ifeedu_da = IFeedUDataAccessHelper()
        if inviter_id is None or inviter_id == "":
            # 没有邀请人,应该是从商户主页点进去自己发现这个功能
            relationship = feeding_pb.UserRelationship()
            relationship.user_id = user_id
            relationship.root_id = user_id
            relationship.inviter_id = user_id
            relationship.merchant_id = self._merchant.id
            ifeedu_da.add_user_relationship(relationship)
        else:
            # 有邀请人应该是从'我也要玩'点进来创建心愿单的
            inviter_relationships = ifeedu_da.get_user_relationships(user_id=inviter_id, merchant_id=self._merchant.id)
            relationships = set()
            for inviter_relationship in inviter_relationships:
                relationships.add(inviter_relationship.root_id)
            for r in relationships:
                relationship = feeding_pb.UserRelationship()
                relationship.user_id = user_id
                relationship.root_id = r
                relationship.inviter_id = inviter_id
                relationship.merchant_id = self._merchant.id
                ifeedu_da.add_user_relationship(relationship)

    def create_wishlist(self, user_id, inviter_id, dish_ids):
        """ 创建心愿清单
        """
        dish_ids = [str(dish_id) for dish_id in dish_ids]
        wishlist = wishlist_pb.WishList()
        wishlist.id = id_manager.generate_common_id()
        wishlist.user_id = user_id
        wishlist.merchant_id = self._merchant.id
        wishlist.store_id = self._store.id
        if inviter_id is not None:
            wishlist.inviter_id = inviter_id
        self._build_user_relationship(user_id, inviter_id)
        wishlist.create_time = int(time.time())
        wishlist.expire_time = wishlist.create_time + date_utils.ONE_DAY * 7
        wishlist.can_feeding = True
        dish_ids = set(dish_ids)

        exclude_dish_ids = set(list(self._config.dish_list)) - dish_ids
        exclude_dish_ids = set(random.sample(exclude_dish_ids, self._config.random_select_dish_number))

        all_dish_ids = exclude_dish_ids | dish_ids

        for dish_id in all_dish_ids:
            dish_id = str(dish_id)
            dish = OrderingServiceDataAccessHelper().get_dish(dish_id=dish_id, merchant_id=self._merchant.id)
            wishitem = wishlist.wish_items.add()
            wishitem.id = dish_id
            wishitem.type = ifeedu_common_pb.ItemType.FOOD_DISH
            wishitem.name = dish.name
            wishitem.price = int(dish.price)
            if len(dish.images) > 0:
                wishitem.image_url = dish.images[0]
            if dish_id in dish_ids:
                wishitem.is_user_like = True

        IFeedUDataAccessHelper().add_or_update_wishlist(wishlist)
        generate_poster(wishlist)

        # 如果是inviterId,则要把创建心愿单的用户的数据存到每个用户的popularity_ranklist里
        redis_client = RedisClient().get_connection()
        pipeline = redis_client.pipeline()
        if inviter_id:
            self.get_popularity_ranklist(inviter_id)
            inviter_id_popularity_ranilkst_key = self._get_popularity_ranklist_key(inviter_id, self._merchant.id)
            user_ids = redis_client.zrange(inviter_id_popularity_ranilkst_key, 0, -1)

            user_ids = {user_id.decode('utf8') for user_id in user_ids}
            for user_id in user_ids:
                _k = self._get_popularity_ranklist_key(user_id, self._merchant.id)
                if redis_client.exists(_k):
                    pipeline.zadd(_k, {wishlist.user_id: 0})

            mapping = {wishlist.user_id: 0}
            for user_id in user_ids:
                mapping.update({user_id: 0})
            key = self._get_popularity_ranklist_key(wishlist.user_id, self._merchant.id)
            pipeline.zadd(key, mapping)

        pipeline.execute()
        return wishlist

    def _get_popularity_ranklist_key(self, user_id, merchant_id):
        return "popularity_ranklist_{}_{}".format(user_id, merchant_id)

    def get_wishlist_detail(self):
        wishlist_vo = page_ifeedu_pb.WishlistVo()
        user_id = self.wishlist.user_id
        user = UserDataAccessHelper().get_user(user_id)
        wishlist_vo.head_img_url = user.member_profile.head_image_url
        wishlist_vo.merchant_id = self._merchant.id
        wishlist_vo.store_id = self._store.id
        wishlist_vo.merchant_logo = self._merchant.basic_info.logo_url
        wishlist_vo.address = self._store.address
        wishlist_vo.poster_url = get_poster_url(self.wishlist)
        wishlist_vo.user_id = self.wishlist.user_id
        wishlist_vo.store_name = self._store.name
        for item in self.wishlist.wish_items:
            if self.user.id == self.wishlist.user_id:
                if not item.is_user_like:
                    continue
            item_vo = wishlist_vo.wish_items.add()
            item_vo.image_url = item.image_url
            item_vo.name = item.name
            item_vo.price = item.price
            item_vo.type = item.type
            item_vo.dish_id = item.id
        return wishlist_vo

    def _update_wishlist(self):
        like_items = set()
        for wish_item in self.wishlist.wish_items:
            item_id = wish_item.id
            if wish_item.is_user_like:
                like_items.add(wish_item.id)
            if item_id not in self.feed_items:
                continue
            item_feed_amount = self.feed_items.get(item_id)
            wish_item.total_feed_amount += item_feed_amount

        self.feed_stats = IFeedUDataAccessHelper().get_feed_stats(wish_list_id=self.wishlist.id, feeder_id=self.user.id)
        if not self.feed_stats:
            self.feed_stats = feeding_pb.FeedStats()
        self.feed_stats.wish_list_id = self.wishlist.id
        self.feed_stats.initiator_id = self.wishlist.user_id
        self.feed_stats.feeder_id = self.user.id
        self.feed_stats.total_feed_amount += self.transaction.paid_fee
        feed_plans = IFeedUDataAccessHelper().get_feed_plans(wishlist_id=self.wishlist.id, feeder_id=self.user.id, status=feeding_pb.FeedPlan.PAID)
        feed_plans.append(self.feed_plan)

        total_feed_items = set()
        for feed_plan in feed_plans:
            feed_items = feed_plan.feed_items
            for feed_item in feed_items:
                total_feed_items.add(feed_item.id)

        current_feed_items = set(self.feed_items.keys())
        hit_number = len(current_feed_items & like_items)
        total_feed_item_number = len(total_feed_items)
        logger.info("wishlist_id: {}, total_feed_items: {}, like_itmes: {}".format(self.wishlist.id, total_feed_items, like_items))
        self.total_hit_number = len(total_feed_items & like_items)

        self.feed_stats.matching_score = hit_number
        self.feed_stats.hit_rate = int(float("{:.2f}".format(float(hit_number) / total_feed_item_number)) * 100)
        self.feed_plan.hit_number = hit_number

    def feeding_prepay(self):
        if not self.wishlist.can_feeding:
            raise errors.WishListCannotBeFeeding()
        self.transaction = TransactionManager().feeding_prepay(
            self.user.id, self.wishlist.user_id, self.bill_fee, self.paid_fee, self.pay_method)
        self._create_feed_plan()

        if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
            # 采用微信支付
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, wishlist_id=self.feed_plan.wish_list_id)
            logger.info('wechat_pay feeding_prepay result: {}, feed_plan_id: {}'.format(result, self.feed_plan.id))
        elif self.pay_method == wallet_pb.Transaction.ALIPAY:
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, return_url=self.return_url, wishlist_id=self.feed_plan.wish_list_id)
            logger.info('alipay feeding_prepay result: {}, feed_plan_id: {}'.format(result, self.feed_plan.wish_list_id))
        elif self.pay_method == wallet_pb.Transaction.WALLET:
            key = "{}_feeding".format(self.feed_plan.wish_list_id)
            result = PaymentManager(self.pay_method).prepay(transaction=self.transaction, key=key)
            logger.info('shilai wallet_pay feeding_prepay result: {}, feed_plan_id: {}'.format(result, self.feed_plan.id))
        else:
            raise errors.PayMethodNotSupport()
        if result.get('errcode') == error_codes.SUCCESS:
            logger.info("feeding_prepay success: {}".format(self.wishlist.id))
            if self.pay_method == wallet_pb.Transaction.WECHAT_PAY:
                self.transaction.wechat_pay.wechat_prepay_id = result.get("prepayId", "")
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.ALIPAY:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                TransactionManager().update_transaction(self.transaction)
            elif self.pay_method == wallet_pb.Transaction.WALLET:
                self.transaction.state = wallet_pb.Transaction.ORDERED
                self.notification()
        result['transactionId'] = self.transaction.id
        return result

    def feeding_payment_notification(self):
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        TransactionManager().update_transaction(self.transaction)

        self._update_wishlist()
        self._update_feed_plan()

        ifeedu_da = IFeedUDataAccessHelper()
        ifeedu_da.add_or_update_wishlist(self.wishlist)
        ifeedu_da.add_or_update_feed_plan(self.feed_plan)
        ifeedu_da.add_or_update_feed_stats(self.feed_stats)

        self.update_wishlist_feeding_ranklist()
        self._update_user_merchant_feed_stats()
        self._update_merchant_wishlist_dish()

    def _update_feed_plan(self):
        self.feed_plan.paid_time = date_utils.timestamp_second()
        self.feed_plan.status = feeding_pb.FeedPlan.PAID

    # 更新用户在这个商户下对应的某个菜品一共有多少份可以使用的投喂
    def _update_merchant_wishlist_dish(self):
        ifeedu_da = IFeedUDataAccessHelper()
        for feed_item in self.feed_plan.feed_items:
            if feed_item.price == 0:
                continue
            copies = feed_item.feed_amount / float(feed_item.price)
            ifeedu_da.add_or_update_merchant_wishlist_dish(
                feed_item.id, self._merchant.id, self.wishlist.user_id, copies=copies)

    def _create_feed_plan(self):
        self.feed_plan = feeding_pb.FeedPlan()
        self.feed_plan.id = id_manager.generate_common_id()
        self.feed_plan.initiator_id = self.wishlist.user_id
        self.feed_plan.wish_list_id = self.wishlist.id
        self.feed_plan.feeder_id = self.user.id
        self.feed_plan.create_time = date_utils.timestamp_second()
        self.feed_plan.transaction_id = self.transaction.id
        self._create_feed_item(self.feed_plan)
        IFeedUDataAccessHelper().add_or_update_feed_plan(self.feed_plan)

    def _create_feed_item(self, feed_plan):
        for item_id, item_feed_amount in self.feed_items.items():
            if self.item_type == ifeedu_common_pb.FOOD_DISH:
                feed_item = feed_plan.feed_items.add()
                feed_item.id = item_id
                feed_item.type = self.item_type
                dish = OrderingServiceDataAccessHelper().get_dish(dish_id=str(item_id), merchant_id=self._merchant.id)
                feed_item.name = dish.name
                feed_item.price = int(dish.price)
                if len(dish.images) > 0:
                    feed_item.image_url = dish.images[0]
                    feed_item.feed_amount = item_feed_amount

    def get_my_wishlist(self):
        ifeedu_da = IFeedUDataAccessHelper()
        wishlists = ifeedu_da.get_wishlist(user_id=self.user.id)
        ret = []
        if not wishlists:
            return []
        for wishlist in wishlists:
            feed_users = {}
            wishlist_vo = page_ifeedu_pb.MyWishlistVo()
            wishlist_vo.id = wishlist.id
            wishlist_vo.merchant_id = wishlist.merchant_id
            merchant = MerchantDataAccessHelper().get_merchant(wishlist.merchant_id)
            wishlist_vo.store_name = merchant.stores[0].name
            wishlist_vo.merchant_logo = merchant.basic_info.logo_url
            feed_plans = ifeedu_da.get_feed_plans(wishlist_id=wishlist.id, status=feeding_pb.FeedPlan.PAID)
            total_feed_amount = 0
            user_dish_money = {}
            for feed_plan in feed_plans:
                for feed_item in feed_plan.feed_items:
                    total_feed_amount += feed_item.feed_amount
                    feed_user = feed_users.get(feed_item.id, set())
                    feed_user.add(feed_plan.feeder_id)
                    feed_users.update({feed_item.id: feed_user})
                    user_dish_money.update({
                        feed_plan.feeder_id: user_dish_money.get(feed_plan.feeder_id, 0) + feed_item.feed_amount
                    })

            wishlist_vo.total_money = total_feed_amount
            for wish_item in wishlist.wish_items:
                if wish_item.price == 0:
                    continue
                merchant_wishlist_dish = ifeedu_da.get_merchant_wishlist_dish(user_id=self.user.id,
                        merchant_id=wishlist.merchant_id, dish_id=str(wish_item.id))

                dish_vo = wishlist_vo.dish_list.add()
                dish_vo.name = wish_item.name
                dish_vo.dish_id = wish_item.id

                dish_vo.total_copies = "0.0"
                dish_vo.used_copies = "0.0"
                dish_vo.available_copies = "0.0"

                if merchant_wishlist_dish:
                    dish_vo.total_copies = str(merchant_wishlist_dish.total_copies)
                    dish_vo.used_copies = str(merchant_wishlist_dish.used_copies)
                    dish_vo.available_copies = str(merchant_wishlist_dish.total_copies - merchant_wishlist_dish.used_copies)

                dish_vo.image_url = wish_item.image_url
                dish_vo.price = wish_item.price

                users = feed_users.get(wish_item.id, [])
                for user_id in users:
                    user = UserDataAccessHelper().get_user(user_id)
                    user_vo = dish_vo.feeders.add()
                    user_vo.nickname = user.member_profile.nickname
                    user_vo.head_image_url = user.member_profile.head_image_url
                    user_vo.feed_money = user_dish_money.get(user_id, 0)
            ret.append(wishlist_vo)
        return ret

    def get_feed_other_stats(self):
        ifeedu_da = IFeedUDataAccessHelper()
        feed_statses = ifeedu_da.get_feed_statses(feeder_id=self.user.id)
        ret = []
        for feed_stats in feed_statses:
            wishlist_id = feed_stats.wish_list_id
            wishlist = ifeedu_da.get_wishlist_by_id(wishlist_id=wishlist_id)
            user = UserDataAccessHelper().get_user(wishlist.user_id)
            other_feed_stats_vo = page_ifeedu_pb.OtherFeedStatsVo()
            other_feed_stats_vo.head_image_url = user.member_profile.head_image_url
            other_feed_stats_vo.nickname = user.member_profile.nickname
            other_feed_stats_vo.matching_score = feed_stats.matching_score

            feed_plans = ifeedu_da.get_feed_plans(wishlist_id=wishlist.id, feeder_id=self.user.id, status=feeding_pb.FeedPlan.PAID)

            for feed_plan in feed_plans:
                for item in feed_plan.feed_items:
                    dish_vo = other_feed_stats_vo.dish_list.add()
                    dish_vo.name = item.name
                    dish_vo.feed_amount = item.feed_amount
            ret.append(other_feed_stats_vo)
        return ret

    # 用户在某个商户下的所有被投喂总和
    def _get_user_merchant_feed_stats_key(self, user_id):
        return "user_merchant_feed_stats_{}_{}".format(user_id, self._merchant.id)

    def get_user_merchant_feed_stats(self, user_id):
        key = self._get_user_merchant_feed_stats_key(user_id)
        redis_client = RedisClient().get_connection()
        amount = 0
        if redis_client.exists(key) and redis_client.ttl(key) > 60 * 5:
            return int(redis_client.get(key))
        else:
            feed_statses = IFeedUDataAccessHelper().get_feed_statses(initiator_id=user_id)
            for feed_state in feed_statses:
                amount += feed_state.total_feed_amount
            redis_client.set(key, amount)
            redis_client.expire(key, date_utils.ONE_DAY)
        return amount

    def _update_user_merchant_feed_stats(self):
        key = self._get_user_merchant_feed_stats_key(self.wishlist.user_id)
        redis_client = RedisClient().get_connection()
        if redis_client.exists(key) and redis_client.ttl(key) > 60 * 5:
            redis_client.incr(key, self.paid_fee)
        else:
            self.get_user_merchant_feed_stats(self.wishlist.user_id)

    def notification(self):
        if self.transaction.type == wallet_pb.Transaction.FEEDING_PAYMENT:
            self.feeding_payment_notification()

    def prepay(self):
        return self.feeding_prepay()

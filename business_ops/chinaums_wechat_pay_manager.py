# -*- coding: utf-8 -*-


"""
Filename: chinaums_pay_manager.py
Date: 2020-08-26 13:45:44
Title: 银联商务小程序综合支付
"""

import logging
from datetime import datetime
from collections import namedtuple

import Crypto.Hash.SHA256
from google.protobuf import json_format

import proto.finance.wallet_pb2 as wallet_pb
import proto.chinaums_wechat_pay_pb2 as chinaums_wechat_pay_pb
from business_ops import constants
from business_ops.pay_manager import PayManager
from business_ops.transaction_manager import TransactionManager
from business_ops.merchant_manager import MerchantManager
from common.utils import id_manager
from dao.user_da_helper import UserDataAccessHelper
from dao.payment_da_helper import PaymentDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from service import base_responses
from service import error_codes
from service import errors


logger = logging.getLogger(__name__)


class ChinaumsWechatPayManager(PayManager):
    """ 银联商务,微信支付
    """

    # 微信下单请求
    UNION_PAY = "wx.unifiedOrder"
    # 查询支付结果
    QUERY = "query"
    # 分账确认
    SUB_ORDERS_CONFIRM = "subOrdersConfirm"
    # 退款
    REFUND = "refund"

    def __init__(self, merchant=None, merchant_id=None, *args, **kargs):
        super(ChinaumsWechatPayManager, self).__init__(merchant=merchant, merchant_id=merchant_id)
        self.msg_src = constants.CHINAUMS_WECHAT_PAY_MSG_SRC
        self.domain = constants.CHINAUMS_WECHAT_PAY_DOMAIN
        self.shilai_mid = constants.CHINAUMS_WECHAT_PAY_SHILAI_MID
        self.shilai_tid = constants.CHINAUMS_WECHAT_PAY_SHILAI_TID
        self.msg_src_id = constants.CHINAUMS_WECHAT_PAY_MSG_SRC_ID
        self.secret_key = constants.CHINAUMS_WECHAT_PAY_SECRET_KEY
        self.payment_prefix = "chinaums_pay"
        self.payment_da = PaymentDataAccessHelper()
        self.division_flag = False

        # 不做分账的业务类型
        self.no_division_transaction_type = [
            wallet_pb.Transaction.COUPON_PACKAGE_PURCHASE,
            wallet_pb.Transaction.FANPIAO_PURCHASE
        ]

        self.chinaums_config = None
        if self.merchant:
            self.chinaums_config = self.payment_da.get_chinaums_wechat_pay_config(merchant_id=self.merchant.id)
            self.merchant_manager = MerchantManager(merchant=self.merchant)

        self.mid = self.chinaums_config.mid
        self.tid = self.chinaums_config.tid

        self.params = {}
        self.build_basic_params()

    def check_chinaums_pay_info(self, raise_error=False):
        flag = True
        if self.chinaums_config is None:
            flag = False
        if self.chinaums_config.status != chinaums_wechat_pay_pb.ChinaumsWechatPayConfig.NORMAL:
            flag = False
        if not flag and raise_error:
            raise errors.Error(err=error_codes.CHINAUMS_WECHAT_PAY_CHECK_INFO_FAIL)
        return flag

    def build_basic_params(self):
        now = datetime.now()
        self.params.update({
            "msgSrc": self.msg_src,
            "msgId": id_manager.generate_common_id(),
            "requestTimestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "tid": self.shilai_tid,
            "mid": self.shilai_mid,
            "instMid": "MINIDEFAULT",
            "tradeType": "MINI",
            "signType": "SHA256"
        })

    def check_op_success(self, ret):
        if not ret.flag:
            return False
        if ret.ret.get("errCode") == "SUCCESS":
            return True
        return False

    def set_division_flag(self, transaction):
        """ 设置此订单是否分账
        银联商务支付同步分账和异步分账.我们统一只做异步分账
        """
        self.params.update({
            "divisionFlag": False,
            "asynDivisionFlag": False
        })
        if not self.chinaums_config.asyn_division_flag:
            # 不分账
            return
        if transaction.type in self.no_division_transaction_type:
            return
        self.params.update({
            "asynDivisionFlag": True
        })
        self.division_flag = True

    def get_scan_code_shilai_amount(self, transaction, order):
        """ 扫码点餐,时来应该分得的金额
        """
        OrderFee = namedtuple("OrderFee", ["merchant_fee", "platform_amount"])
        settlement_rate = self.merchant_manager.get_settlement_rate()
        # 商户应该获得到金额
        merchant_fee = int(order.paid_in_fee * (1 - settlement_rate) + 0.5)
        # 剩下的便是时来应分得的金额.在使用券包的情况下有可能是负数
        platform_amount = abs(transaction.paid_fee) - merchant_fee
        return OrderFee(merchant_fee=merchant_fee, platform_amount=platform_amount)

    def get_shilai_member_card_recharge_shilai_amount(self, transaction):
        """
        """
        OrderFee = namedtuple("OrderFee", ["merchant_fee", "platform_amount"])
        settlement_rate = self.merchant_manager.get_settlement_rate()
        merchant_fee = int(transaction.paid_fee * (1 - settlement_rate) + 0.5)
        platform_amount = abs(transaction.paid_fee) - merchant_fee
        return OrderFee(merchant_fee=merchant_fee, platform_amount=platform_amount)

    def generate_scan_code_sub_orders(self, transaction, order):
        """ 扫码点餐时分账的sub_orders
        """
        order_fee = self.get_scan_code_shilai_amount(transaction, order)
        platform_amount = order_fee.platform_amount
        if platform_amount < 0:
            platform_amount = 0

        self.params.update({
            "platformAmount": platform_amount,
            "subOrders": [{
                "mid": self.mid,
                "merOrderId": id_manager.generate_common_id(),
                "totalAmount": order_fee.merchant_fee
            }]
        })

    def generate_shilai_member_card_recharge_sub_orders(self, transaction):
        """ 会员储值分账
        """
        order_fee = self.get_shilai_member_card_recharge_shilai_amount(transaction)
        platform_amount = order_fee.platform_amount
        self.params.update({
            "platformAmount": platform_amount,
            "subOrders": [{
                "mid": self.mid,
                "merOrderId": id_manager.generate_common_id(),
                "totalAmount": order_fee.merchant_fee
            }]
        })

    def generate_sub_orders(self, transaction, order=None):
        """ 设置此订单的分账sub_orders
        """
        if not self.division_flag:
            return
        if transaction.type in [
                # 扫码点餐
                wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT,
                # 扫码买单
                wallet_pb.Transaction.SELF_DINING_DISCOUNT_PAYMENT
        ]:
            self.generate_scan_code_sub_orders(transaction, order)
        if transaction.type in [
                wallet_pb.Transaction.SHILAI_MEMBER_CARD_RECHARGE
        ]:
            self.generate_shilai_member_card_recharge_sub_orders(transaction)

    def prepay(self, transaction, **kargs):
        order = kargs.get("order", None)
        user = UserDataAccessHelper().get_user(transaction.payer_id)
        notify_url = self.generate_notify_url(transaction, payment_prefix=self.payment_prefix, **kargs)
        mer_order_id = "{}{}".format(self.msg_src_id, transaction.id[4:])
        self.params.update({
            "merOrderId": mer_order_id,
            "totalAmount": transaction.paid_fee,
            "notify_url": notify_url,
            "subOpenId": user.wechat_profile.openid,
            "msgType": self.UNION_PAY
        })
        # 设置是否分账的标识
        self.set_division_flag(transaction)
        sign = self.sha256_sign(self.params, secret_key=self.secret_key)
        self.params.update({"sign": sign})

        ret = self.try_post(url=self.domain, params=self.params, try_times=1)
        if self.check_op_success(ret):
            result = base_responses.create_responses_obj(error_codes.SUCCESS, error_codes.SUCCESS_MSG)
            payment_da = PaymentDataAccessHelper()
            chinaums_pay_order = json_format.ParseDict(ret.ret, chinaums_wechat_pay_pb.ChinaumsWechatPayOrder(), ignore_unknown_fields=True)
            chinaums_pay_order.transaction_id = transaction.id
            payment_da.add_or_update_chinaums_wechat_pay_order(chinaums_pay_order)
            result["signData"] = ret.ret.get("miniPayRequest")
        else:
            result = base_responses.create_responses_obj(error_codes.FAIL, error_codes.FAIL)
        return result

    def notification(self, data):
        params = dict(data)
        logger.info("银联商务支付回调参数: {}".format(params))
        verify = self.sha256_verify(params, self.secret_key)
        if not verify:
            raise errors.Error(err=error_codes.CHINAUMS_WECHAT_PAY_NOT_VERIFIED)
        if data.get("status") not in ["TRADE_SUCCESS", "TRADE_REFUND"]:
            raise errors.Error(err=error_codes.CHINAUMS_WECHAT_PAY_FAIL)
        self.update_chinaums_wechat_pay_order(data)

    def scan_code_order_launch_ledger(self, transaction, order):
        """ 异步分账确认
        """
        self.set_division_flag(transaction)
        if not self.division_flag:
            return
        self.params.update({
            "msgType": self.SUB_ORDERS_CONFIRM,
            "merOrderId": transaction.id
        })
        self.generate_sub_orders(transaction, order)
        sign = self.sha256_sign(self.params, secret_key=self.secret_key)
        self.params.update({"sign": sign})

        ret = self.try_post(url=self.domain, params=self.params, try_times=1)
        return ret.ret

    def update_chinaums_wechat_pay_order(self, data):
        mer_order_id = data.get("merOrderId")
        payment_da = PaymentDataAccessHelper()
        chinaums_wechat_pay_order = payment_da.get_chinaums_wechat_pay_order(mer_order_id=mer_order_id)
        logger.info("data.refund_target_order_id: {}".format(data.get("refundTargetOrderId")))
        if chinaums_wechat_pay_order and len(chinaums_wechat_pay_order) == 1:
            chinaums_wechat_pay_order = chinaums_wechat_pay_order[0]
            if data.get("targetOrderId"):
                chinaums_wechat_pay_order.target_order_id = data.get("targetOrderId")
            if data.get("refundTargetOrderId"):
                chinaums_wechat_pay_order.refund_target_order_id = data.get("refundTargetOrderId")
            payment_da.add_or_update_chinaums_wechat_pay_order(order=chinaums_wechat_pay_order)

    def query_payment(self, transaction_id):
        mer_order_id = "{}{}".format(self.msg_src_id, transaction_id[4:])
        self.params.update({
            "merOrderId": mer_order_id,
            "msgType": self.QUERY
        })
        sign = self.sha256_sign(self.params, secret_key=self.secret_key)
        self.params.update({"sign": sign})
        ret = self.try_post(url=self.domain, params=self.params, try_times=1)
        return ret.ret

    def refund(self, transaction, reason=None):
        payment_da = PaymentDataAccessHelper()
        chinaums_pay_order = payment_da.get_chinaums_wechat_pay_order(transaction_id=transaction.id)
        if chinaums_pay_order and len(chinaums_pay_order) == 1:
            chinaums_pay_order = chinaums_pay_order[0]
        mer_order_id = "{}{}".format(self.msg_src_id, transaction.id[4:])
        refund_order_id = id_manager.generate_common_id()
        refund_order_id = "{}{}".format(self.msg_src_id, refund_order_id[4:])
        self.params.update({
            "msgId": id_manager.generate_common_id(),
            "msgType": self.REFUND,
            "merOrderId": mer_order_id,
            "targetOrderId": chinaums_pay_order.target_order_id,
            "refundAmount": transaction.paid_fee,
            "platformAmount": 0,
            "refundOrderId": refund_order_id
        })
        logger.info("银联商务退款操作参数: {}".format(self.params))
        sign = self.sha256_sign(self.params, secret_key=self.secret_key)
        self.params.update({"sign": sign})
        ret = self.try_post(url=self.domain, params=self.params, try_times=1)
        return ret.ret

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        ret_json = self.refund(transaction, reason=reason)
        if ret_json.get("errCode") != "SUCCESS":
            return None
        _transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.CHINAUMS_WECHAT_PAY,
                refunded_transaction_id=transaction.id,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee,
                use_coupon_id=transaction.use_coupon_id)
        return _transaction

    def fanpiao_refund(self, transaction, reason=None):
        ret_json = self.refund(transaction, reason=reason)
        if ret_json.get("errCode") != "SUCCESS":
            return None
        _transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.CHINAUMS_WECHAT_PAY,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee,
                refunded_transaction_id=transaction.id,
                use_coupon_id=transaction.use_coupon_id)
        return _transaction

    def coupon_package_refund(self, transaction, reason=None):
        ret_json = self.refund(transaction, reason=reason)
        if ret_json.get("errCode") != "SUCCESS":
            return None
        _transaction = None
        if transaction.state == wallet_pb.Transaction.SUCCESS:
            _transaction = TransactionManager().handle_ordering_refund(
                user_id=transaction.payer_id, merchant_id=transaction.payee_id,
                pay_method=wallet_pb.Transaction.CHINAUMS_WECHAT_PAY,
                paid_fee=transaction.paid_fee, bill_fee=transaction.bill_fee, use_coupon_id=transaction.use_coupon_id)
        return _transaction

    def sha256_sign(self, params, secret_key):
        plaintext = self.sort_params(params)
        plaintext = "{}{}".format(plaintext, secret_key)
        plaintext = plaintext.encode("utf8")
        cipher = Crypto.Hash.SHA256.new(plaintext)
        sign = cipher.hexdigest()
        logger.info("chinaums_wechat prepay生成签名: {}".format(sign))
        return sign

    def sha256_verify(self, params, secret_key):
        plaintext = self.sort_params(params)
        plaintext = "{}{}".format(plaintext, secret_key)
        plaintext = plaintext.encode("utf8")
        cipher = Crypto.Hash.SHA256.new(plaintext)
        sign = params.get("sign")
        logger.info("verify sign: {}".format(sign))
        return cipher.hexdigest().upper() == sign

    def sort_params(self, params):
        keys = params.keys()
        keys = sorted(keys, key=lambda x: x)
        kv_pairs = []
        for key in keys:
            value = params.get(key, " ")
            if value == " ":
                continue
            if key == "sign":
                continue
            if isinstance(value, dict):
                value = str(value).replace(" ", "")
            if isinstance(value, list):
                value = str(value).replace(" ", "")
            if isinstance(value, bool):
                kv_pairs.append("{}={}".format(key, str(value).lower()))
            else:
                kv_pairs.append("{}={}".format(key, value))
        plaintext = "&".join(kv_pairs)
        plaintext = plaintext.replace("'", "\"")
        print(plaintext)
        return plaintext

    def add_or_update_chinaums_wechat_pay_config(self, mid, tid, asyn_division_flag, division_flag):
        config = chinaums_wechat_pay_pb.ChinaumsWechatPayConfig()
        payment_da = PaymentDataAccessHelper()

        config.id = id_manager.generate_common_id()
        config.mid = mid
        config.merchant_id = self.merchant.id
        config.asyn_division_flag = asyn_division_flag
        config.division_flag = division_flag
        config.tid = tid
        payment_da.add_or_update_chinaums_wechat_pay_config(config)

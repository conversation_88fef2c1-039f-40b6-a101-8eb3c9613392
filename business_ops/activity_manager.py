# -*- coding: utf-8 -*-

import time
from datetime import datetime
import proto.config_pb2 as config_pb
import proto.activity.activity_pb2 as activity_pb
from business_ops.base_manager import BaseManager
from business_ops.config_manager import ConfigManager
from common.utils import id_manager
from dao.activity_da_helper import ActivityDataAccessHelper
from dao.user_da_helper import UserDataAccessHelper
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.config_da_helper import ConfigDataAccessHelper
from wechat_lib import subscribe_message_send
from service import errors


class ActivityManager(BaseManager):

    def __init__(self, *args, **kargs):
        super(ActivityManager, self).__init__(*args, **kargs)
        self.activity_config = None

    def create_activity(self, start_timestamp, end_timestamp, activity_type, name):
        if isinstance(activity_type, str):
            activity_type = config_pb.ActivityConfig.ActivityType.Value(activity_type)
        try:
            # 处理前商可能传过来NaN
            int(start_timestamp)
            int(end_timestamp)
        except Exception:
            raise errors.ShowError("起始时间错误,必须是数字")
        activity = config_pb.ActivityConfig()
        activity.id = id_manager.generate_common_id()
        activity.start_timestamp = str(start_timestamp)
        activity.end_timestamp = str(end_timestamp)
        activity.activity_type = activity_type
        activity.name = name
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_activity_config(activity)
        return activity

    def update_activity(self, activity_id, **kargs):
        config_da = ConfigDataAccessHelper()
        activity = config_da.get_activity_config(activity_id=activity_id)
        if not activity:
            raise errors.ShowError("活动不存在")
        if kargs.get("start_timestamp") is not None:
            activity.start_timestamp = kargs.get("start_timestamp")
        if kargs.get("end_timestamp") is not None:
            activity.end_timestamp = kargs.get("end_timestamp")
        if kargs.get("activity_type") is not None:
            activity_type = config_pb.ActivityConfig.ActivityType.Value(kargs.get("activity_type"))
            activity.activity_type = activity_type
        if kargs.get("name") is not None:
            activity.name = kargs.get("name")
        if kargs.get("disable_activity") is not None:
            activity.disable_activity = kargs.get("disable_activity")
        config_da.add_or_update_activity_config(activity)

    def list_activities(self, merchant_id=None):
        config_da = ConfigDataAccessHelper()
        if merchant_id is not None:
            merchant_activity_config = config_da.get_merchant_activity_config(merchant_id=merchant_id)
            if merchant_activity_config is None:
                return[]
            activities = config_da.get_activity_configs(activity_ids=list(merchant_activity_config.activity_ids))
        else:
            activities = config_da.get_activity_configs()
        return activities

    def get_activity_config(self, activity_id):
        config_da = ConfigDataAccessHelper()
        activity = config_da.get_activity_config(activity_id=activity_id)
        return activity

    def clear_merchant(self, activity):
        config_da = ConfigDataAccessHelper()
        merchant_activity_configs = config_da.get_merchant_activity_configs(activity_id=activity.id)
        for merchant_activity_config in merchant_activity_configs:
            merchant_activity_config.activity_ids.remove(activity.id)
            config_da.add_or_update_merchant_activity_config(merchant_activity_config)

    def update_merchant_activity_config(self, merchant_activity_config, activity_ids):
        if None in (merchant_activity_config, activity_ids):
            return
        while merchant_activity_config.activity_ids:
            merchant_activity_config.activity_ids.pop()
        for activity_id in activity_ids:
            merchant_activity_config.activity_ids.append(activity_id)

    def get_merchant_activity_config(self, merchant_id, create=True):
        config_da = ConfigDataAccessHelper()
        merchant_activity_config = config_da.get_merchant_activity_config(merchant_id=merchant_id)
        if merchant_activity_config :
            return merchant_activity_config
        if create:
            merchant_activity_config = config_pb.MerchantActivityConfig()
            merchant_activity_config.merchant_id = merchant_id
            return merchant_activity_config
        return None

    def add_or_update_merchant_activity_config(self, merchant_activity_config):
        if merchant_activity_config is None:
            return
        config_da = ConfigDataAccessHelper()
        config_da.add_or_update_merchant_activity_config(merchant_activity_config)

    def get_current_first_time_buy_fanpiao_activity(self, merchant_id):
        """获取当前第一次购买饭票的活动"""
        config_da = ConfigDataAccessHelper()
        merchant_activity_config = config_da.get_merchant_activity_configs(merchant_id=merchant_id)
        if len(merchant_activity_config) == 0:
            return
        merchant_activity_config = merchant_activity_config[0]
        activities = config_da.get_activity_configs(
            activity_ids=list(merchant_activity_config.activity_ids)
        )
        now = int(time.time())
        for activity in activities:
            if activity.disable_activity:
                continue
            if activity.activity_type != config_pb.ActivityConfig.FIRST_TIME_BUY_FANPIAO:
                continue
            if int(activity.start_timestamp) > now or int(activity.end_timestamp) < now:
                continue
            return activity

    def subscribe_wechat_message(self, activity_type):
        config_manager = ConfigManager(merchant=self.merchant)
        merchant_activity_config = config_manager.get_merchant_activity_config()
        if not merchant_activity_config:
            return
        activity_da = ActivityDataAccessHelper()
        activity_configs = config_manager.get_activity_configs(merchant_activity_config.activity_ids)
        activity_type = config_pb.ActivityConfig.ActivityType.Value(activity_type)
        for activity_config in activity_configs:
            if activity_config.activity_type != activity_type:
                continue
            subscribe_info = activity_pb.ActivitySubscribeInfo()
            subscribe_info.user_id = self.user.id
            subscribe_info.merchant_id = self.merchant.id
            subscribe_info.activity_id = activity_config.id
            subscribe_info.id = id_manager.generate_common_id()
            activity_da.add_or_update_activity_subscribe_info(subscribe_info)

    def send_activity_wechat_message(self, activity_type):
        activity_da = ActivityDataAccessHelper()
        user_da = UserDataAccessHelper()
        merchant_da = MerchantDataAccessHelper()
        subscribe_infos = activity_da.get_activity_subscribe_infos(status=activity_pb.ActivitySubscribeInfo.NEED_SEND)
        config_manager = ConfigManager(merchant=self.merchant)

        activity_ids = [s.activity_id for s in subscribe_infos]
        activity_configs = config_manager.get_activity_configs(activity_ids, no_timestamp_check=True)
        activity_configs = {a.id: a for a in activity_configs}
        activity_type = config_pb.ActivityConfig.ActivityType.Value(activity_type)

        for info in subscribe_infos:
            activity_config = activity_configs.get(info.activity_id)
            if not activity_config:
                continue
            if activity_type != activity_config.activity_type:
                continue
            user = user_da.get_user(user_id=info.user_id)
            merchant = merchant_da.get_merchant(merchant_id=info.merchant_id)
            ret = subscribe_message_send.activity_subscribe_message(
                merchant=merchant,
                touser=user.wechat_profile.openid,
                activity_type=activity_type
            )
            if ret:
                info.message_send_times += 1
            if info.message_send_times == activity_config.message_send_times:
                info.status = activity_pb.ActivitySubscribeInfo.NO_NEED_SEND
            if activity_config.message_send_times == 0 and ret:
                info.status = activity_pb.ActivitySubscribeInfo.NO_NEED_SEND
            activity_da.add_or_update_activity_subscribe_info(info)

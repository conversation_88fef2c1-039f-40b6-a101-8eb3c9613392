# -*- coding: utf-8 -*-

import logging
import inspect
from collections import namedtuple

import proto.finance.wallet_pb2 as wallet_pb
import proto.merchant_rules_pb2 as merchant_rules_pb
from business_ops.alipay_miniprogram_manager import AliPayMiniprogramManager
from business_ops.wechat_pay_manager import WechatPayManager
from business_ops.wallet_manager import WalletManager
from business_ops.fanpiao_pay_manager import FanpiaoPayManager
from business_ops.shilai_member_card_pay_manager import ShilaiMemberCardPayManager
from business_ops.keruyun_member_card_pay_manager import KeruyunMemberCardPayManager
from business_ops.tian_que_pay_manager import TianQuePayManager
from business_ops.none_exists_pay_manager import NoneExistsPayManager
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from dao.merchant_da_helper import MerchantDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import error_codes
from service import errors

logger = logging.getLogger(__name__)


class PaymentManager():
    def __init__(self, pay_method=None, is_refund=False, merchant=None, merchant_id=None):
        self.pay_method = pay_method
        if pay_method in [wallet_pb.Transaction.WECHAT_PAY, wallet_pb.Transaction.TIAN_QUE_PAY]:
            self.get_real_wechat_pay_agent(merchant=merchant, merchant_id=merchant_id)
        elif pay_method == wallet_pb.Transaction.ALIPAY:
            self.get_real_alipay_agent(merchant=merchant, merchant_id=merchant_id)
        elif pay_method == wallet_pb.Transaction.WALLET:
            self.agent = WalletManager()
        elif pay_method == wallet_pb.Transaction.FANPIAO_PAY:
            self.agent = FanpiaoPayManager(merchant=merchant, merchant_id=merchant_id)
        elif pay_method == wallet_pb.Transaction.KERUYUN_MEMBER_CARD_PAY:
            self.agent = KeruyunMemberCardPayManager()
        elif pay_method == wallet_pb.Transaction.SHILAI_MEMBER_CARD_PAY:
            self.agent = ShilaiMemberCardPayManager(
                merchant=merchant,
                merchant_id=merchant_id)

    def get_real_alipay_agent(self, merchant=None, merchant_id=None):
        if merchant is None and merchant_id is not None:
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant is not None:
            if merchant.wechat_pay_type == merchant_rules_pb.Merchant.TIAN_QUE:
                self.agent = TianQuePayManager(merchant=merchant, pay_type="ALIPAY", pay_way="02")
                if self.agent.check_tian_que_pay_info(raise_error=False):
                    return
        self.agent = AliPayMiniprogramManager()

    def get_real_wechat_pay_agent(self, merchant=None, merchant_id=None):
        """ 如果商户有配置wechat_pay_type,则获取商户实际使用的微信支付渠道.否则就走财付通渠道
        如果某天 天阙 不能使用了,直接在数据库里把 商户天阙支付的信息状态设置为 DELETED 即可
        """
        if merchant is None and merchant_id is not None:
            merchant = MerchantDataAccessHelper().get_merchant(merchant_id)
        if merchant is not None:
            if merchant.wechat_pay_type == merchant_rules_pb.Merchant.TIAN_QUE:
                self.agent = TianQuePayManager(merchant=merchant)
                if self.agent.check_tian_que_pay_info(raise_error=False):
                    return
        self.agent = WechatPayManager()

    def handle_payment_success(self, trade_no):
        pass

    def prepay(self, transaction, **kargs):
        '''发起支付'''
        return self.agent.prepay(transaction, **kargs)

    def notification(self, data):
        '''支付成功的回调'''
        return self.agent.notification(data)

    def ordering_refund(self, transaction, order, reason=None, **kargs):
        return self.agent.ordering_refund(transaction, order, reason, **kargs)

    def fanpiao_refund(self, transaction, reason=None):
        return self.agent.fanpiao_refund(transaction, reason)

    def coupon_package_refund(self, transaction, reason=None):
        return self.agent.coupon_package_refund(transaction, reason)

    def refund_callback(self, transaction, orig_transaction_id):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.refund_callback(transaction, orig_transaction_id)

    def scan_code_order_launch_ledger(self, transaction, order):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.scan_code_order_launch_ledger(transaction, order)

    def clear_tansfer_fee(self, transaction, reason=None):
        transaction_da = TransactionDataAccessHelper()
        transfers = transaction_da.get_merchant_day_transfers(transaction_id=transaction.id)
        if not transfers:
            return
        transfer = transfers[0]
        transfer.fee = 0
        if reason is not None:
            transfer.reason = reason
        transaction_da.add_or_update_merchant_day_transfer(transfer)

    def update_scan_code_order_merchant_transfer_fee(self, order, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.update_scan_code_order_merchant_transfer_fee(order, transaction)

    def update_pos_order_merchant_transfer_fee(self, transaction, platform_discount_fee, fanpiao_commission_fee):
        if hasattr(self.agent, "update_pos_order_merchant_transfer_fee"):
            self.agent.update_pos_order_merchant_transfer_fee(transaction, platform_discount_fee, fanpiao_commission_fee)

    def update_member_card_recharge_merchant_transfer_fee(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.update_member_card_recharge_merchant_transfer_fee(transaction)

    def coupon_package_launch_ledger(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.coupon_package_launch_ledger(transaction)

    def update_coupon_package_purchase_transfer_fee(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.update_coupon_package_purchase_transfer_fee(transaction)

    def self_dining_payment_launch_ledger(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            return self.agent.self_dining_payment_launch_ledger(transaction)
        return False

    def update_self_dining_payment_transfer_fee(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.update_self_dining_payment_transfer_fee(transaction)

    def do_allocate_to_shilai(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.do_allocate_to_shilai(transaction)

    def fanpiao_launch_ledger(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.fanpiao_launch_ledger(transaction)

    def open_group_purchase_launch_ledger(self, transaction):
        if hasattr(self.agent, 'open_group_purchase_launch_ledger'):
            self.agent.open_group_purchase_launch_ledger(transaction)

    def ordering_with_open_group_purchase_launch_ledger(self, transaction, order):
        if hasattr(self.agent, 'ordering_with_open_group_purchase_launch_ledger'):
            self.agent.ordering_with_open_group_purchase_launch_ledger(transaction, order)

    def update_fanpiao_purchase_transfer_fee(self, transaction):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, fname):
            self.agent.update_fanpiao_purchase_transfer_fee(transaction)

    def recharge_launch_ledger(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.recharge_launch_ledger(transaction)

    def member_card_recharge_refund(self, transaction, reason=None):
        fname = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, fname):
            self.agent.member_card_recharge_refund(transaction, reason)

    def ordering_coupon_package_union_pay_launch_ledger(
            self, transaction, ordering_transaction, order, coupon_package_transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.ordering_coupon_package_union_pay_launch_ledger(
                transaction, ordering_transaction, order, coupon_package_transaction)

    def ordering_coupon_package_union_pay_refund(self, transaction):
        funame = inspect.currentframe().f_code.co_name
        if hasattr(self.agent, funame):
            self.agent.ordering_coupon_package_union_pay_refund(transaction)

    def partial_refund(self, transaction=None, refund_transaction=None, order=None, **kargs):
        if hasattr(self.agent, "partial_refund"):
            return self.agent.partial_refund(
                transaction=transaction,
                refund_transaction=refund_transaction,
                order=order, **kargs)
        raise errors.ShowError("该支付方式暂不支持部分退款")

    def refund(self, transaction):
        if hasattr(self.agent, "refund"):
            self.agent.refund(transaction)

    def refund_notification(self, data):
        if hasattr(self.agent, "refund_notification"):
            return self.agent.refund_notification(data)
        return None

    def fanpiao_balance_withdraw(self, user, bill_fee, paid_fee):
        FanpiaoBalanceWithdrawRet = namedtuple("FanpiaoBalanceWithdrawRet", ["flag", "transaction"])
        if not hasattr(self.agent, "fanpiao_balance_withdraw"):
            return FanpiaoBalanceWithdrawRet(False, None)
        manager = TransactionManager()
        transaction = manager.handle_fanpiao_balance_withdraw(
            user.id, bill_fee, paid_fee, pay_method=self.pay_method)
        ret = self.agent.fanpiao_balance_withdraw(user, transaction)
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.paid_time = date_utils.timestamp_second()
        manager.update_transaction(transaction)
        return FanpiaoBalanceWithdrawRet(ret, transaction)

    def withdraw(self, user, amount):
        if not hasattr(self.agent, "withdraw"):
            return
        transaction = TransactionManager().handle_cash_withdraw(user.id, amount, pay_method=self.pay_method)
        self.agent.withdraw(user, transaction)
        transaction.state = wallet_pb.Transaction.SUCCESS
        transaction.paid_time = date_utils.timestamp_second()
        TransactionManager().update_transaction(transaction)

    @classmethod
    def get_refund_callback_pay_methods(cls):
        refund_callback_pay_methods = [
            wallet_pb.Transaction.WECHAT_PAY
        ]
        return refund_callback_pay_methods

    def trade_query(self, transaction_id=None, transaction=None):
        if transaction_id is None and transaction is None:
            return None
        if transaction_id is None:
            transaction_id = transaction.id
        if hasattr(self.agent, "trade_query"):
            return self.agent.trade_query(transaction_id)
        return None

# -*- coding: utf-8 -*-

"""开团并下单."""

import logging

import proto.finance.wallet_pb2 as wallet_pb
import proto.promotion.group_purchase.group_purchase_pb2 as group_purchase_pb
import proto.promotion.coupon.coupon_pb2 as coupon_pb
from business_ops.self_dish_order_payment import SelfDishOrderPaymentManager
from business_ops.ordering.order_manager_v2 import OrderManager as OrderManagerV2
from business_ops.promotion.group_purchase.group_purchase_manager import GroupPurchaseManager
from business_ops.promotion.group_purchase.group_purchase_template_manager import GroupPurchaseTemplateManager
from business_ops.promotion.coupon.coupon_manager import CouponManager
from business_ops.promotion.coupon.coupon_template_manager import CouponTemplateManager
from business_ops.base_manager import BaseManager
from business_ops.payment_manager import PaymentManager
from business_ops.transaction_manager import TransactionManager
from common.utils import date_utils
from dao.ordering.ordering_service_da_helper import OrderingServiceDataAccessHelper
from dao.transaction_da_helper import TransactionDataAccessHelper
from service import errors
from service import error_codes
from business_ops.multi_party_dinning_manager import MultiPartyDinningManager

logger = logging.getLogger(__name__)


class OrderingWithOpenGroupPurchase(BaseManager):

    def __init__(self, *args, **kargs):
        super().__init__(*args, **kargs)
        self.pay_method = kargs.get('pay_method')
        self.bill_fee = kargs.get('bill_fee')
        self.paid_fee = kargs.get('paid_fee')
        self.group_purchase_template_id = kargs.get('group_purchase_template_id')
        self.group_purchase_template = None
        self.union_transaction = None
        self.transactions = dict()
        self.__init_union_transaction()
        self.group_purchase_manager = GroupPurchaseManager(
            merchant=self.merchant, user=self.user)

    def __init_group_purchase_template(self):
        if self.group_purchase_template is not None:
            return
        if not self.group_purchase_template_id:
            raise errors.ShowError("参数不足: groupPurchaseTemplateId")
        group_purchase_template_manager = GroupPurchaseTemplateManager()
        self.group_purchase_template = group_purchase_template_manager.get_group_purchase_template(
            id=self.group_purchase_template_id)

    def __init_union_transaction(self):
        if self.union_transaction is not None:
            return
        if self.transaction is None:
            return
        transaction_manager = TransactionManager()
        self.union_transaction = transaction_manager.get_union_transaction(
            transaction_id=self.transaction.id)
        self.transactions = transaction_manager.get_transactions_by_union_transaction(
            self.union_transaction)

    def prepay(self):
        pay_method = wallet_pb.Transaction.PayMethod.Value(self.pay_method)
        if pay_method not in [
                wallet_pb.Transaction.WECHAT_PAY,
                wallet_pb.Transaction.ALIPAY
        ]:
            raise errors.ShowError("支付方式不支持")
        self.__check_fee_valid()
        self.group_purchase_manager.check_active_group_purchase_uplimit()
        self.check_dish_remain_quantity(self.order)
        transaction_manager = TransactionManager()
        self.transaction = transaction_manager.create_ordering_with_group_purchase_transaction(
            payee_id=self.merchant.id,
            payer_id=self.user.id,
            bill_fee=self.bill_fee,
            paid_fee=self.paid_fee,
            pay_method=self.pay_method,
        )
        payment_manager = PaymentManager(
            merchant=self.merchant,
            pay_method=wallet_pb.Transaction.PayMethod.Value(self.pay_method)
        )
        result = payment_manager.prepay(
            transaction=self.transaction,
            order=self.order,
            group_purchase_template_id=self.group_purchase_template_id
        )
        if result.get("errcode") == error_codes.SUCCESS:
            self.transaction.state = wallet_pb.Transaction.ORDERED
            union_transaction = transaction_manager.create_union_transaction(self.transaction)
            self.__group_purchase_prepay(union_transaction)
            self.__ordering_prepay(union_transaction)
            transaction_manager.update_union_transaction(union_transaction)
            transaction_manager.update_transaction(self.transaction)
            result['transactionId'] = self.transaction.id
        return result

    def __check_fee_valid(self):
        group_purchase_template_manager = GroupPurchaseTemplateManager()
        group_purchase_template = group_purchase_template_manager.get_group_purchase_template(
            id=self.group_purchase_template_id
        )
        if not group_purchase_template:
            raise errors.ShowError("团购模板不存在")
        coupon_template_manager = CouponTemplateManager()
        min_bill_fee = 0
        for opening_coupon_template_id in group_purchase_template.opening_coupon_template_ids:
            coupon_template = coupon_template_manager.get_coupon_template(id=opening_coupon_template_id)
            min_bill_fee += coupon_template_manager.get_coupon_template_min_bill_fee(coupon_template)
        sell_price = group_purchase_template.sell_price
        logger.info(f"前端总金额: {self.bill_fee} 券售价: {sell_price} 团长立减金额底线: {min_bill_fee}")
        if group_purchase_template.sell_price + min_bill_fee > self.bill_fee:
            raise errors.ShowError("金额校验错误")

    def __group_purchase_prepay(self, union_transaction):
        self.__init_group_purchase_template()
        manager = GroupPurchaseManager(merchant=self.merchant, user=self.user)
        group_purchase = manager.create_group_purchase_union_pay(self.group_purchase_template, self.transaction)
        union_transaction.sub_transaction_ids.append(manager.transaction.id)
        return group_purchase

    def __ordering_prepay(self, union_transaction):
        if len(self.group_purchase_template.opening_coupon_template_ids) == 0:
            return self.__ordering_prepay_without_reduce_coupon(union_transaction)
        bill_fee = self.transaction.bill_fee - self.group_purchase_template.total_value
        coupon_template_manager = CouponTemplateManager(user=self.user, merchant=self.merchant)
        ids = list(self.group_purchase_template.opening_coupon_template_ids)
        coupon_templates = coupon_template_manager.get_coupon_template_list(ids=ids)
        reduce_fee = 0
        _bill_fee = bill_fee
        for coupon_template in coupon_templates:
            reduce_fee += coupon_template_manager.get_coupon_template_reduce_fee(_bill_fee, coupon_template)
            _bill_fee -= coupon_template_manager.get_coupon_template_min_bill_fee(coupon_template)
        paid_fee = _bill_fee - reduce_fee
        if paid_fee < 0:
            paid_fee = 0
        manager = SelfDishOrderPaymentManager(
            merchant=self.merchant,
            user=self.user,
            order=self.order,
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            pay_method=self.transaction.pay_method,
        )
        manager.union_pay_prepay()
        union_transaction.sub_transaction_ids.append(manager.transaction.id)

    def __ordering_prepay_without_reduce_coupon(self, union_transaction):
        bill_fee = self.transaction.bill_fee - self.group_purchase_template.total_value
        coupon_template_manager = CouponTemplateManager(user=self.user, merchant=self.merchant)
        coupon_template = coupon_template_manager.get_coupon_template(
            id=self.group_purchase_template.leader_coupon_template_id
        )
        reduce_fee = coupon_template_manager.get_coupon_template_reduce_fee(bill_fee, coupon_template)
        _bill_fee = bill_fee - coupon_template_manager.get_coupon_template_min_bill_fee(coupon_template)
        paid_fee = _bill_fee - reduce_fee
        if paid_fee < 0:
            paid_fee = 0
        manager = SelfDishOrderPaymentManager(
            merchant=self.merchant,
            user=self.user,
            order=self.order,
            bill_fee=bill_fee,
            paid_fee=paid_fee,
            pay_method=self.transaction.pay_method,
        )
        manager.union_pay_prepay()
        union_transaction.sub_transaction_ids.append(manager.transaction.id)

    def notification(self):
        group_purchase = self.__group_purchase_notify()
        self.__ordering_notify(group_purchase)
        self.__launch_ledger()
        # 1. 更新transaction的状态为成功
        # 2. 更新支付时间
        transaction_manager = TransactionManager()
        self.transaction.state = wallet_pb.Transaction.SUCCESS
        self.transaction.paid_time = date_utils.timestamp_second()
        transaction_manager.update_transaction(self.transaction)
        self.decrease_dish_remain_quantity(self.order)
        self.release_order_lock(self.order)
        MultiPartyDinningManager(order=self.order).payment_event()

    def __group_purchase_notify(self):
        open_group_purchase_transaction = self.__get_transaction_by_type(
            wallet_pb.Transaction.OPEN_GROUP_PURCHASE)
        manager = GroupPurchaseManager(merchant=self.merchant, user=self.user)
        group_purchase = manager.get_group_purchase(
            transaction_id=open_group_purchase_transaction.id)
        manager.group_purchase_union_pay_notification(
            group_purchase, open_group_purchase_transaction)
        return group_purchase

    def __ordering_notify(self, group_purchase):
        if len(group_purchase.opening_coupon_ids) == 0:
            return self.__ordering_notify_without_reduce_coupon(group_purchase)
        else:
            return self.__ordering_notify_with_reduce_coupon(group_purchase)

    def __ordering_notify_with_reduce_coupon(self, group_purchase):
        ordering_da = OrderingServiceDataAccessHelper()
        transaction = self.__get_transaction_by_type(wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT)
        self.order = ordering_da.get_order(transaction_id=transaction.id)
        self.order.coupon_ids.extend(list(group_purchase.opening_coupon_ids))
        order_manager_v2 = OrderManagerV2(order=self.order)
        manager = SelfDishOrderPaymentManager(
            merchant=self.merchant,
            user=self.user,
            transaction=transaction,
            order_manager_v2=order_manager_v2
        )
        manager.order = self.order
        manager.union_pay_notification(transaction)

    def __ordering_notify_without_reduce_coupon(self, group_purchase):
        """没有立减券的团购合并支付."""
        ordering_da = OrderingServiceDataAccessHelper()
        transaction = self.__get_transaction_by_type(
            wallet_pb.Transaction.SELF_DISH_ORDER_PAYMENT)
        self.order = ordering_da.get_order(transaction_id=transaction.id)
        # 生成券
        coupon_template_manager = CouponTemplateManager()
        coupon_template = coupon_template_manager.get_coupon_template(
            id=group_purchase.leader_coupon_template_id)
        coupon_manager = CouponManager(user=self.user, merchant=self.merchant)
        coupon = coupon_manager.create_coupon(coupon_template)
        # 加入团
        group_purchase_manager = GroupPurchaseManager(user=self.user, merchant=self.merchant)
        group_purchase_manager.join_group_purchase(group_purchase, coupon.id)
        coupon_manager.add_or_update_coupon(coupon)
        self.order.coupon_ids.append(coupon.id)
        order_manager_v2 = OrderManagerV2(order=self.order)
        manager = SelfDishOrderPaymentManager(
            merchant=self.merchant,
            user=self.user,
            transaction=transaction,
            order_manager_v2=order_manager_v2
        )
        manager.order = self.order
        manager.union_pay_notification(transaction)
        group_purchase_manager.add_or_update_group_purchase(
            group_purchase)

    def __launch_ledger(self):
        if self.transaction.ledgered:
            return
        payment_manager = PaymentManager(wallet_pb.Transaction.TIAN_QUE_PAY, merchant=self.merchant)
        payment_manager.ordering_with_open_group_purchase_launch_ledger(self.transaction, self.order)

    def __get_transaction_by_type(self, type):
        for _, transaction in self.transactions.items():
            if transaction.type == type:
                return transaction
            if transaction.type == type:
                return transaction
